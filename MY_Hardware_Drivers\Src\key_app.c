/**
 * @file key_app.c
 * @brief ����Ӧ�ô������� (�ع���)
 * @details
 * ���ļ�ʵ���˰���ɨ�����ص�Ӧ���߼���
 * ��ʹ��һ���������������������������ÿ���������������壨GPIO��
 * �����߼����ܣ��ص����������뿪�����Ӷ�����˴���Ŀ���չ�ԺͿ�ά���ԡ�
 *
 * <AUTHOR>
 * @date 2025-07-18
 * @version 2.0 (���ع�����߿���չ��)
 */
#include "key_app.h"
#include "ad_measure.h"
#include "my_fft.h"
#include "da_output.h"
#include "AD9959.h"
#include "my_hmi.h"
// *********************************************************************************
// 1. ���Ͷ���ͳ���
// *********************************************************************************

#define NUM_KEYS 4 // ���尴��������

// ����һ������ָ�����ͣ����ڰ����Ļص�����
typedef void (*key_callback_t)(void);

// ����һ���ṹ�壬���ڱ��浥��������������Ϣ
typedef struct {
    GPIO_TypeDef* port;     // �������ӵ�GPIO�˿�
    uint16_t       pin;      // �������ӵ�GPIO����
    key_callback_t callback; // ��������ʱҪ���õĻص�����
} key_info_t;

// *********************************************************************************
// 2. ��̬�ص�������ԭ������
// *********************************************************************************
static void key1_action_toggle_ask(void);
static void key2_action_toggle_fsk(void);
static void key3_action_cycle_phase(void);
static void key4_action_perform_fft(void);

// *********************************************************************************
// 3. ������������ <--- ������Ƶĺ��ģ�
// *********************************************************************************
static const key_info_t key_config[NUM_KEYS] = {
    {KEY1_GPIO_Port, KEY1_Pin, key1_action_toggle_ask}, // ����1 -> �л�ASK
    {KEY2_GPIO_Port, KEY2_Pin, key2_action_toggle_fsk}, // ����2 -> �л�FSK
    {KEY3_GPIO_Port, KEY3_Pin, key3_action_cycle_phase},// ����3 -> ������λ
    {KEY4_GPIO_Port, KEY4_Pin, key4_action_perform_fft} // ����4 -> ִ��FFT
    // ���Ҫ���ӵ�5��������ֻ���������һ�м��ɣ�
};

// *********************************************************************************
// 4. ȫ�ּ���̬����
// *********************************************************************************
// ʹ��λ����(bitmask)�����水��״̬������Ч���ܴ�����ϼ�
uint8_t key_map_now = 0;
uint8_t key_map_old = 0;
uint8_t key_map_down = 0;
// uint8_t key_map_up = 0; // �����Ҫ�����⣬����ȡ��ע��

static float current_ad_freq = 2000000.0f;

// *********************************************************************************
// 5. ����ʵ��
// *********************************************************************************

/**
 * @brief ��ȡ�������������ĵ�ǰ״̬��������һ��λ����(bitmask)
 * @details ����`key_config`���飬ɨ��ÿ���Ѷ���İ�����������������£��͵�ƽ����
 * ���ڷ���ֵ�Ķ�Ӧλ����1�����磬����0���£����� 0b0001������1���£����� 0b0010��
 * @return uint8_t �������а���״̬��λ���롣
 */
static uint8_t key_read_map(void)
{
    uint8_t map = 0;
    for (int i = 0; i < NUM_KEYS; i++) {
        if (HAL_GPIO_ReadPin(key_config[i].port, key_config[i].pin) == GPIO_PIN_RESET) {
            map |= (1 << i);
        }
    }
    return map;
}

/**
 * @brief �������������� (�ع���)
 * @details
 * 1. ����`key_read_map()`��ȡ���а���״̬��λ���롣
 * 2. ������Щ�����ոձ����¡�
 * 3. �������а����������⵽ĳ�����������£��͵�������`key_config`������ע��Ļص�������
 */
void key_proc(void)
{
    key_map_now = key_read_map();
    key_map_down = key_map_now & (key_map_old ^ key_map_now); // �������½��ؼ��
    // key_map_up = ~key_map_now & (key_map_old ^ key_map_now);
    key_map_old = key_map_now;

    if (key_map_down == 0) {
        return; // ���û���κΰ��������£�ֱ�ӷ���
    }

    // �������п��ܵİ���
    for (int i = 0; i < NUM_KEYS; i++) {
        // ����i�������Ƿ񱻰���
        if (key_map_down & (1 << i)) {
            // ����ǣ��������й����Ļص��������������
            if (key_config[i].callback != NULL) {
                key_config[i].callback();
            }
        }
    }
}

// --- �ⲿ�ɵ��ú��� ---
void set_current_ad_frequency(float freq)
{
    current_ad_freq = freq;
}

float get_current_ad_frequency(void)
{
    return current_ad_freq;
}


// *********************************************************************************
// 6. ��̬�ص������ľ���ʵ��
// *********************************************************************************

double fre_cjy = 100;
uint16_t amp_cjy = 2047;
float disiti_set = 1.0;

#define FREQ_START 100.0    // 起始频率100Hz
#define FREQ_STEP 100.0     // 频率步进100Hz  
#define FREQ_MAX 1000000.0  // 最大频率1000kHz
#define FREQ_STEPS ((uint32_t)((FREQ_MAX - FREQ_START) / FREQ_STEP + 1))

#define AMP_START 10        // 起始幅度10
#define AMP_STEP 10         // 幅度步进10
#define AMP_MAX 3100        // 最大幅度3100
#define AMP_STEPS ((uint32_t)((AMP_MAX - AMP_START) / AMP_STEP + 1))

static uint32_t freq_step = 0;
static uint32_t amp_step = 0;

adjust_mode_t current_mode = MODE_FREQUENCY;

/** @brief ����1�Ĺ��ܣ�����/ֹͣASK�������� */
static void key1_action_toggle_ask(void)
{
    // 切换到下一个模式
    current_mode = (adjust_mode_t)((current_mode + 1) % MODE_COUNT);

//    // 显示当前模式
//    const char* mode_names[] = {"PHASE", "FREQUENCY", "AMPLITUDE", "WAVEFORM"};
	const char* mode_names[] = {"MODE_FREQUENCY", "MODE_AMPLITUDE", "MODE_DISANTI"};
    my_printf(&huart3, "Mode switched to: %s\r\n", mode_names[current_mode]);
		switch(current_mode)
		{
			case MODE_FREQUENCY:
				my_printf(&huart2, "page page2\xff\xff\xff");
			break;
			case MODE_AMPLITUDE:
				my_printf(&huart2, "page page2\xff\xff\xff");
			break;
			case MODE_DISANTI:
				my_printf(&huart2, "page page3\xff\xff\xff");
			break;
			case MODE_DISITI:
				my_printf(&huart2, "page page4\xff\xff\xff");
			break;
			case MODE_COUNT:
			break;
		}
}

/** @brief 按键2的功能：减少当前模式的参数值 */
static void key2_action_toggle_fsk(void)
{
    switch (current_mode) {
        case MODE_FREQUENCY:
            // 频率减少：步进值减1，循环到最大值
            freq_step = (freq_step == 0) ? (FREQ_STEPS - 1) : (freq_step - 1);
            fre_cjy = FREQ_START + freq_step * FREQ_STEP;
            
            DA_SetFREQ(1, fre_cjy);
            DA_Apply_Settings();
            my_printf(&huart3, "DA1 Frequency: %.0f Hz\r\n", fre_cjy);
            HMI_Send_Int(huart2, "n0", (int)fre_cjy);
            break;
            
        case MODE_AMPLITUDE:
            // 幅度减少：步进值减1，循环到最大值
            amp_step = (amp_step == 0) ? (AMP_STEPS - 1) : (amp_step - 1);
            amp_cjy = AMP_START + amp_step * AMP_STEP;
            
            DA_SetAmp(1, amp_cjy);
            DA_Apply_Settings();
            my_printf(&huart3, "DA1 Amplitude: %d\r\n", amp_cjy);
            HMI_Send_Float(huart2, "x0", (float)(amp_cjy*3.04/3072), 1);
            break;
            
        case MODE_DISITI:
            if(disiti_set > DISITI_MIN) {
                disiti_set -= 0.1f;
            }
            HMI_Send_Float(huart2, "x1", disiti_set, 1);
            if(current_mode == MODE_DISITI) {
                uint16_t freq_index = (uint16_t)(fre_cjy / 100);
                if(freq_index >= 1) {
                    float set = disiti_set / Hjw[freq_index - 1];
                    uint16_t set_u16 = set * 3072 / 6.08;
                    amp_cjy = set_u16;
                    DA_SetAmp(1, amp_cjy);
                    DA_Apply_Settings();
                }
            }
            break;
    }
}

/** @brief 按键3的功能：增加当前模式的参数值 */
static void key3_action_cycle_phase(void)
{
    switch (current_mode) {
        case MODE_FREQUENCY:
            // 频率增加：步进值加1，循环到0
            freq_step = (freq_step + 1) % FREQ_STEPS;
            fre_cjy = FREQ_START + freq_step * FREQ_STEP;
            
            DA_SetFREQ(1, fre_cjy);
            DA_Apply_Settings();
            my_printf(&huart3, "DA1 Frequency: %.0f Hz\r\n", fre_cjy);
            HMI_Send_Int(huart2, "n0", (int)fre_cjy);
            break;
            
        case MODE_AMPLITUDE:
            // 幅度增加：步进值加1，循环到0
            amp_step = (amp_step + 1) % AMP_STEPS;
            amp_cjy = AMP_START + amp_step * AMP_STEP;
            
            DA_SetAmp(1, amp_cjy);
            DA_Apply_Settings();
            my_printf(&huart3, "DA1 Amplitude: %d\r\n", amp_cjy);
            HMI_Send_Float(huart2, "x0", (float)(amp_cjy*3.04/3072), 1);
            break;
            
        case MODE_DISITI:
            if(disiti_set < DISITI_MAX) {
                disiti_set += 0.1f;
            }
            HMI_Send_Float(huart2, "x1", disiti_set, 1);
            if(current_mode == MODE_DISITI) {
                uint16_t freq_index = (uint16_t)(fre_cjy / 100);
                if(freq_index >= 1) {
                    float set = disiti_set / Hjw[freq_index - 1];
                    uint16_t set_u16 = set * 3072 / 6.08;
                    amp_cjy = set_u16;
                    DA_SetAmp(1, amp_cjy);
                    DA_Apply_Settings();
                }
            }
            break;
    }
}

/** @brief ����4�Ĺ��ܣ���AD������1�����ݽ���FFT����� */
static void key4_action_perform_fft(void)
{
//    my_printf(&huart3, "���ڼ���FFTƵ��...\r\n");
//    calculate_fft_spectrum(fifo_data1_f, AD_FIFO_SIZE);
//    output_fft_spectrum();
	
}
