TA260 000:003.642   SEGGER J-Link V8.16 Log File
TA260 000:003.745   DLL Compiled: Feb 26 2025 12:07:26
TA260 000:003.752   Logging started @ 2025-08-02 08:48
TA260 000:003.756   Process: G:\keil\keil arm\UV4\UV4.exe
TA260 000:003.766 - 3.759ms 
TA260 000:003.773 JLINK_SetWarnOutHandler(...)
TA260 000:003.776 - 0.004ms 
TA260 000:003.783 JLINK_OpenEx(...)
TA260 000:007.438   Firmware: J-Link V9 compiled May  7 2021 16:26:12
TA260 000:008.803   Firmware: J-Link V9 compiled May  7 2021 16:26:12
TA260 000:008.926   Decompressing FW timestamp took 86 us
TA260 000:016.400   Hardware: V9.60
TA260 000:016.424   S/N: 69655018
TA260 000:016.429   OEM: SEGGER
TA260 000:016.434   Feature(s): RDI, GD<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>B<PERSON>, JFlash
TA260 000:017.752   Bootloader: (FW returned invalid version)
TA260 000:019.181   TELNET listener socket opened on port 19021
TA260 000:019.252   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
TA260 000:019.374   WEBSRV Webserver running on local port 19080
TA260 000:019.439   Looking for J-Link GUI Server exe at: G:\keil\keil arm\ARM\Segger\JLinkGUIServer.exe
TA260 000:019.516   Looking for J-Link GUI Server exe at: C:\Program Files (x86)\SEGGER\JLink_V630d\\JLinkGUIServer.exe
TA260 000:323.730   Failed to connect to J-Link GUI Server.
TA260 000:323.763 - 319.964ms returns "O.K."
TA260 000:323.778 JLINK_GetEmuCaps()
TA260 000:323.783 - 0.004ms returns 0xB9FF7BBF
TA260 000:323.793 JLINK_TIF_GetAvailable(...)
TA260 000:324.178 - 0.385ms 
TA260 000:324.191 JLINK_SetErrorOutHandler(...)
TA260 000:324.195 - 0.003ms 
TA260 000:324.214 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\2025ele_ori\zuolan_stm32\MDK-ARM\JLinkSettings.ini"", ...). 
TA260 000:335.862 - 11.648ms returns 0x00
TA260 000:338.072 JLINK_ExecCommand("Device = STM32F429IGTx", ...). 
TA260 000:339.207   Flash bank @ 0x90000000: SFL: Parsing sectorization info from ELF file
TA260 000:339.221     FlashDevice.SectorInfo[0]: .SectorSize = 0x00010000, .SectorStartAddr = 0x00000000
TA260 000:343.659   Device "STM32F429IG" selected.
TA260 000:343.890 - 5.797ms returns 0x00
TA260 000:343.901 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
TA260 000:343.914   ERROR: Unknown command
TA260 000:343.919 - 0.013ms returns 0x01
TA260 000:343.924 JLINK_GetHardwareVersion()
TA260 000:343.928 - 0.003ms returns 96000
TA260 000:343.932 JLINK_GetDLLVersion()
TA260 000:343.935 - 0.003ms returns 81600
TA260 000:343.939 JLINK_GetOEMString(...)
TA260 000:343.944 JLINK_GetFirmwareString(...)
TA260 000:343.947 - 0.003ms 
TA260 000:348.647 JLINK_GetDLLVersion()
TA260 000:348.670 - 0.023ms returns 81600
TA260 000:348.675 JLINK_GetCompileDateTime()
TA260 000:348.678 - 0.003ms 
TA260 000:351.531 JLINK_GetFirmwareString(...)
TA260 000:351.549 - 0.017ms 
TA260 000:354.196 JLINK_GetHardwareVersion()
TA260 000:354.211 - 0.014ms returns 96000
TA260 000:355.836 JLINK_GetSN()
TA260 000:355.855 - 0.019ms returns 69655018
TA260 000:357.557 JLINK_GetOEMString(...)
TA260 000:360.214 JLINK_TIF_Select(JLINKARM_TIF_SWD)
TA260 000:361.647 - 1.435ms returns 0x00
TA260 000:361.662 JLINK_HasError()
TA260 000:361.673 JLINK_SetSpeed(5000)
TA260 000:361.981 - 0.309ms 
TA260 000:361.987 JLINK_GetId()
TA260 000:363.775   InitTarget() start
TA260 000:363.794    J-Link Script File: Executing InitTarget()
TA260 000:365.952   SWD selected. Executing JTAG -> SWD switching sequence.
TA260 000:370.405   DAP initialized successfully.
TA260 000:383.103   InitTarget() end - Took 17.2ms
TA260 000:386.003   Found SW-DP with ID 0x2BA01477
TA260 000:390.964   DPIDR: 0x2BA01477
TA260 000:392.440   CoreSight SoC-400 or earlier
TA260 000:393.815   Scanning AP map to find all available APs
TA260 000:396.357   AP[1]: Stopped AP scan as end of AP map has been reached
TA260 000:398.399   AP[0]: AHB-AP (IDR: 0x24770011, ADDR: 0x00000000)
TA260 000:399.910   Iterating through AP map to find AHB-AP to use
TA260 000:402.725   AP[0]: Core found
TA260 000:404.099   AP[0]: AHB-AP ROM base: 0xE00FF000
TA260 000:406.577   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
TA260 000:408.203   Found Cortex-M4 r0p1, Little endian.
TA260 000:409.053   -- Max. mem block: 0x00010C40
TA260 000:409.825   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:410.323   CPU_ReadMem(4 bytes @ 0x********)
TA260 000:412.337   FPUnit: 6 code (BP) slots and 2 literal slots
TA260 000:412.353   CPU_ReadMem(4 bytes @ 0xE000EDFC)
TA260 000:412.851   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:413.330   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:413.840   CPU_WriteMem(4 bytes @ 0xE0001000)
TA260 000:414.317   CPU_ReadMem(4 bytes @ 0xE000ED88)
TA260 000:414.807   CPU_WriteMem(4 bytes @ 0xE000ED88)
TA260 000:415.387   CPU_ReadMem(4 bytes @ 0xE000ED88)
TA260 000:415.885   CPU_WriteMem(4 bytes @ 0xE000ED88)
TA260 000:418.237   CoreSight components:
TA260 000:419.897   ROMTbl[0] @ E00FF000
TA260 000:419.915   CPU_ReadMem(64 bytes @ 0xE00FF000)
TA260 000:420.728   CPU_ReadMem(32 bytes @ 0xE000EFE0)
TA260 000:422.752   [0][0]: E000E000 CID B105E00D PID 000BB00C SCS-M7
TA260 000:422.766   CPU_ReadMem(32 bytes @ 0xE0001FE0)
TA260 000:424.946   [0][1]: E0001000 CID B105E00D PID 003BB002 DWT
TA260 000:424.973   CPU_ReadMem(32 bytes @ 0xE0002FE0)
TA260 000:427.187   [0][2]: ******** CID B105E00D PID 002BB003 FPB
TA260 000:427.222   CPU_ReadMem(32 bytes @ 0xE0000FE0)
TA260 000:429.631   [0][3]: ******** CID B105E00D PID 003BB001 ITM
TA260 000:429.651   CPU_ReadMem(32 bytes @ 0xE0040FE0)
TA260 000:431.861   [0][4]: ******** CID B105900D PID 000BB9A1 TPIU
TA260 000:431.878   CPU_ReadMem(32 bytes @ 0xE0041FE0)
TA260 000:433.871   [0][5]: ******** CID B105900D PID 000BB925 ETM
TA260 000:434.350 - 72.362ms returns 0x2BA01477
TA260 000:434.395 JLINK_GetDLLVersion()
TA260 000:434.400 - 0.004ms returns 81600
TA260 000:434.409 JLINK_CORE_GetFound()
TA260 000:434.412 - 0.003ms returns 0xE0000FF
TA260 000:434.417 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
TA260 000:434.422   Value=0xE00FF000
TA260 000:434.427 - 0.010ms returns 0
TA260 000:436.312 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
TA260 000:436.337   Value=0xE00FF000
TA260 000:436.344 - 0.031ms returns 0
TA260 000:436.349 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
TA260 000:436.352   Value=0x********
TA260 000:436.358 - 0.009ms returns 0
TA260 000:436.363 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
TA260 000:436.397   CPU_ReadMem(32 bytes @ 0xE0041FD0)
TA260 000:437.112   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
TA260 000:437.127 - 0.764ms returns 32 (0x20)
TA260 000:437.134 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
TA260 000:437.138   Value=0x00000000
TA260 000:437.143 - 0.009ms returns 0
TA260 000:437.147 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
TA260 000:437.151   Value=0x********
TA260 000:437.156 - 0.008ms returns 0
TA260 000:437.160 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
TA260 000:437.163   Value=0x********
TA260 000:437.168 - 0.008ms returns 0
TA260 000:437.172 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
TA260 000:437.176   Value=0xE0001000
TA260 000:437.180 - 0.008ms returns 0
TA260 000:437.185 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
TA260 000:437.188   Value=0x********
TA260 000:437.193 - 0.008ms returns 0
TA260 000:437.197 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
TA260 000:437.200   Value=0xE000E000
TA260 000:437.205 - 0.008ms returns 0
TA260 000:437.209 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
TA260 000:437.212   Value=0xE000EDF0
TA260 000:437.217 - 0.008ms returns 0
TA260 000:437.221 JLINK_GetDebugInfo(0x01 = Unknown)
TA260 000:437.225   Value=0x00000001
TA260 000:437.229 - 0.008ms returns 0
TA260 000:437.234 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
TA260 000:437.241   CPU_ReadMem(4 bytes @ 0xE000ED00)
TA260 000:437.740   Data:  41 C2 0F 41
TA260 000:437.747   Debug reg: CPUID
TA260 000:437.752 - 0.518ms returns 1 (0x1)
TA260 000:437.757 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
TA260 000:437.805   Value=0x00000000
TA260 000:437.810 - 0.052ms returns 0
TA260 000:437.814 JLINK_HasError()
TA260 000:437.819 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
TA260 000:437.823 - 0.003ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
TA260 000:437.827 JLINK_Reset()
TA260 000:437.833   JLINK_GetResetTypeDesc
TA260 000:437.837   - 0.003ms 
TA260 000:439.479   Reset type: NORMAL (https://wiki.segger.com/J-Link_Reset_Strategies)
TA260 000:439.502   CPU is running
TA260 000:439.509   CPU_WriteMem(4 bytes @ 0xE000EDF0)
TA260 000:440.011   CPU is running
TA260 000:440.019   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:441.944   Reset: Halt core after reset via DEMCR.VC_CORERESET.
TA260 000:444.909   Reset: Reset device via AIRCR.SYSRESETREQ.
TA260 000:444.930   CPU is running
TA260 000:444.941   CPU_WriteMem(4 bytes @ 0xE000ED0C)
TA260 000:500.188   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:500.691   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:503.512   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:509.908   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:512.725   CPU_WriteMem(4 bytes @ 0x********)
TA260 000:513.216   CPU_ReadMem(4 bytes @ 0xE000EDFC)
TA260 000:513.691   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:514.175 - 76.347ms 
TA260 000:514.213 JLINK_Halt()
TA260 000:514.218 - 0.004ms returns 0x00
TA260 000:514.223 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
TA260 000:514.231   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:514.743   Data:  03 00 03 00
TA260 000:514.755   Debug reg: DHCSR
TA260 000:514.761 - 0.537ms returns 1 (0x1)
TA260 000:514.768 JLINK_WriteU32(0xE000EDF0, 0xA05F0003)
TA260 000:514.772   Debug reg: DHCSR
TA260 000:515.044   CPU_WriteMem(4 bytes @ 0xE000EDF0)
TA260 000:515.523 - 0.755ms returns 0 (0x00000000)
TA260 000:515.539 JLINK_WriteU32(0xE000EDFC, 0x01000000)
TA260 000:515.544   Debug reg: DEMCR
TA260 000:515.555   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:516.039 - 0.499ms returns 0 (0x00000000)
TA260 000:524.281 JLINK_GetHWStatus(...)
TA260 000:524.792 - 0.509ms returns 0
TA260 000:529.831 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
TA260 000:529.853 - 0.021ms returns 0x06
TA260 000:529.857 JLINK_GetNumBPUnits(Type = 0xF0)
TA260 000:529.862 - 0.004ms returns 0x2000
TA260 000:529.866 JLINK_GetNumWPUnits()
TA260 000:529.870 - 0.003ms returns 4
TA260 000:533.779 JLINK_GetSpeed()
TA260 000:533.792 - 0.012ms returns 4000
TA260 000:537.505 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
TA260 000:537.540   CPU_ReadMem(4 bytes @ 0xE000E004)
TA260 000:537.975   Data:  02 00 00 00
TA260 000:537.988 - 0.484ms returns 1 (0x1)
TA260 000:537.995 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
TA260 000:538.002   CPU_ReadMem(4 bytes @ 0xE000E004)
TA260 000:538.525   Data:  02 00 00 00
TA260 000:538.532 - 0.537ms returns 1 (0x1)
TA260 000:538.538 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
TA260 000:538.543   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
TA260 000:538.553   CPU_WriteMem(28 bytes @ 0xE0001000)
TA260 000:539.126 - 0.588ms returns 0x1C
TA260 000:539.134 JLINK_Halt()
TA260 000:539.138 - 0.003ms returns 0x00
TA260 000:539.142 JLINK_IsHalted()
TA260 000:539.146 - 0.004ms returns TRUE
TA260 000:541.186 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 000:541.195   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 000:541.428   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 000:543.298 - 2.111ms returns 0x184
TA260 000:543.320 JLINK_HasError()
TA260 000:543.326 JLINK_WriteReg(R0, 0x08000000)
TA260 000:543.330 - 0.004ms returns 0
TA260 000:543.334 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 000:543.338 - 0.003ms returns 0
TA260 000:543.342 JLINK_WriteReg(R2, 0x00000001)
TA260 000:543.345 - 0.003ms returns 0
TA260 000:543.349 JLINK_WriteReg(R3, 0x00000000)
TA260 000:543.353 - 0.003ms returns 0
TA260 000:543.357 JLINK_WriteReg(R4, 0x00000000)
TA260 000:543.360 - 0.003ms returns 0
TA260 000:543.364 JLINK_WriteReg(R5, 0x00000000)
TA260 000:543.370 - 0.005ms returns 0
TA260 000:543.375 JLINK_WriteReg(R6, 0x00000000)
TA260 000:543.378 - 0.003ms returns 0
TA260 000:543.382 JLINK_WriteReg(R7, 0x00000000)
TA260 000:543.386 - 0.003ms returns 0
TA260 000:543.401 JLINK_WriteReg(R8, 0x00000000)
TA260 000:543.404 - 0.014ms returns 0
TA260 000:543.408 JLINK_WriteReg(R9, 0x20000180)
TA260 000:543.412 - 0.003ms returns 0
TA260 000:543.416 JLINK_WriteReg(R10, 0x00000000)
TA260 000:543.419 - 0.003ms returns 0
TA260 000:543.423 JLINK_WriteReg(R11, 0x00000000)
TA260 000:543.427 - 0.003ms returns 0
TA260 000:543.431 JLINK_WriteReg(R12, 0x00000000)
TA260 000:543.434 - 0.003ms returns 0
TA260 000:543.438 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:543.442 - 0.004ms returns 0
TA260 000:543.446 JLINK_WriteReg(R14, 0x20000001)
TA260 000:543.449 - 0.003ms returns 0
TA260 000:543.457 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 000:543.461 - 0.007ms returns 0
TA260 000:543.465 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:543.468 - 0.003ms returns 0
TA260 000:543.472 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:543.476 - 0.003ms returns 0
TA260 000:543.480 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:543.483 - 0.003ms returns 0
TA260 000:543.487 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:543.491 - 0.003ms returns 0
TA260 000:543.495 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:543.501   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:543.979 - 0.484ms returns 0x00000001
TA260 000:543.985 JLINK_Go()
TA260 000:543.989   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 000:544.460   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:544.927   CPU_WriteMem(4 bytes @ 0xE0002008)
TA260 000:544.941   CPU_WriteMem(4 bytes @ 0xE000200C)
TA260 000:544.947   CPU_WriteMem(4 bytes @ 0xE0002010)
TA260 000:544.952   CPU_WriteMem(4 bytes @ 0xE0002014)
TA260 000:544.957   CPU_WriteMem(4 bytes @ 0xE0002018)
TA260 000:544.962   CPU_WriteMem(4 bytes @ 0xE000201C)
TA260 000:546.222   CPU_WriteMem(4 bytes @ 0xE0001004)
TA260 000:550.720   Memory map 'after startup completion point' is active
TA260 000:550.737 - 6.751ms 
TA260 000:550.744 JLINK_IsHalted()
TA260 000:553.060   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:553.572 - 2.828ms returns TRUE
TA260 000:553.582 JLINK_ReadReg(R15 (PC))
TA260 000:553.588 - 0.005ms returns 0x20000000
TA260 000:553.592 JLINK_ClrBPEx(BPHandle = 0x00000001)
TA260 000:553.596 - 0.004ms returns 0x00
TA260 000:553.600 JLINK_ReadReg(R0)
TA260 000:553.604 - 0.003ms returns 0x00000000
TA260 000:553.887 JLINK_HasError()
TA260 000:553.896 JLINK_WriteReg(R0, 0x08000000)
TA260 000:553.901 - 0.005ms returns 0
TA260 000:553.906 JLINK_WriteReg(R1, 0x00004000)
TA260 000:553.909 - 0.003ms returns 0
TA260 000:553.913 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:553.917 - 0.003ms returns 0
TA260 000:553.921 JLINK_WriteReg(R3, 0x00000000)
TA260 000:553.924 - 0.003ms returns 0
TA260 000:553.928 JLINK_WriteReg(R4, 0x00000000)
TA260 000:553.932 - 0.003ms returns 0
TA260 000:553.936 JLINK_WriteReg(R5, 0x00000000)
TA260 000:553.939 - 0.003ms returns 0
TA260 000:553.943 JLINK_WriteReg(R6, 0x00000000)
TA260 000:553.946 - 0.003ms returns 0
TA260 000:553.951 JLINK_WriteReg(R7, 0x00000000)
TA260 000:553.954 - 0.003ms returns 0
TA260 000:553.958 JLINK_WriteReg(R8, 0x00000000)
TA260 000:553.962 - 0.003ms returns 0
TA260 000:553.966 JLINK_WriteReg(R9, 0x20000180)
TA260 000:553.969 - 0.003ms returns 0
TA260 000:553.973 JLINK_WriteReg(R10, 0x00000000)
TA260 000:553.976 - 0.003ms returns 0
TA260 000:553.980 JLINK_WriteReg(R11, 0x00000000)
TA260 000:553.984 - 0.003ms returns 0
TA260 000:553.988 JLINK_WriteReg(R12, 0x00000000)
TA260 000:553.991 - 0.003ms returns 0
TA260 000:553.996 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:553.999 - 0.003ms returns 0
TA260 000:554.003 JLINK_WriteReg(R14, 0x20000001)
TA260 000:554.007 - 0.003ms returns 0
TA260 000:554.011 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 000:554.014 - 0.003ms returns 0
TA260 000:554.018 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:554.022 - 0.003ms returns 0
TA260 000:554.029 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:554.034 - 0.004ms returns 0
TA260 000:554.038 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:554.042 - 0.003ms returns 0
TA260 000:554.046 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:554.049 - 0.003ms returns 0
TA260 000:554.054 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:554.058 - 0.004ms returns 0x00000002
TA260 000:554.062 JLINK_Go()
TA260 000:554.071   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:556.745 - 2.682ms 
TA260 000:556.766 JLINK_IsHalted()
TA260 000:559.150   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:559.648 - 2.882ms returns TRUE
TA260 000:559.655 JLINK_ReadReg(R15 (PC))
TA260 000:559.660 - 0.005ms returns 0x20000000
TA260 000:559.665 JLINK_ClrBPEx(BPHandle = 0x00000002)
TA260 000:559.669 - 0.003ms returns 0x00
TA260 000:559.673 JLINK_ReadReg(R0)
TA260 000:559.677 - 0.003ms returns 0x00000001
TA260 000:559.682 JLINK_HasError()
TA260 000:559.686 JLINK_WriteReg(R0, 0x08000000)
TA260 000:559.690 - 0.003ms returns 0
TA260 000:559.694 JLINK_WriteReg(R1, 0x00004000)
TA260 000:559.698 - 0.003ms returns 0
TA260 000:559.702 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:559.705 - 0.003ms returns 0
TA260 000:559.709 JLINK_WriteReg(R3, 0x00000000)
TA260 000:559.713 - 0.003ms returns 0
TA260 000:559.717 JLINK_WriteReg(R4, 0x00000000)
TA260 000:559.720 - 0.003ms returns 0
TA260 000:559.724 JLINK_WriteReg(R5, 0x00000000)
TA260 000:559.727 - 0.003ms returns 0
TA260 000:559.731 JLINK_WriteReg(R6, 0x00000000)
TA260 000:559.735 - 0.003ms returns 0
TA260 000:559.739 JLINK_WriteReg(R7, 0x00000000)
TA260 000:559.742 - 0.003ms returns 0
TA260 000:559.746 JLINK_WriteReg(R8, 0x00000000)
TA260 000:559.750 - 0.003ms returns 0
TA260 000:559.754 JLINK_WriteReg(R9, 0x20000180)
TA260 000:559.757 - 0.003ms returns 0
TA260 000:559.761 JLINK_WriteReg(R10, 0x00000000)
TA260 000:559.765 - 0.003ms returns 0
TA260 000:559.769 JLINK_WriteReg(R11, 0x00000000)
TA260 000:559.772 - 0.003ms returns 0
TA260 000:559.776 JLINK_WriteReg(R12, 0x00000000)
TA260 000:559.779 - 0.003ms returns 0
TA260 000:559.783 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:559.787 - 0.003ms returns 0
TA260 000:559.791 JLINK_WriteReg(R14, 0x20000001)
TA260 000:559.794 - 0.003ms returns 0
TA260 000:559.798 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 000:559.802 - 0.003ms returns 0
TA260 000:559.806 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:559.810 - 0.003ms returns 0
TA260 000:559.814 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:559.817 - 0.003ms returns 0
TA260 000:559.821 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:559.824 - 0.003ms returns 0
TA260 000:559.828 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:559.832 - 0.003ms returns 0
TA260 000:559.836 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:559.840 - 0.004ms returns 0x00000003
TA260 000:559.844 JLINK_Go()
TA260 000:559.851   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:562.655 - 2.811ms 
TA260 000:562.664 JLINK_IsHalted()
TA260 000:563.167 - 0.503ms returns FALSE
TA260 000:563.173 JLINK_HasError()
TA260 000:573.136 JLINK_IsHalted()
TA260 000:573.651 - 0.515ms returns FALSE
TA260 000:573.658 JLINK_HasError()
TA260 000:575.138 JLINK_IsHalted()
TA260 000:575.659 - 0.519ms returns FALSE
TA260 000:575.674 JLINK_HasError()
TA260 000:577.649 JLINK_IsHalted()
TA260 000:578.115 - 0.465ms returns FALSE
TA260 000:578.125 JLINK_HasError()
TA260 000:579.645 JLINK_IsHalted()
TA260 000:580.170 - 0.524ms returns FALSE
TA260 000:580.177 JLINK_HasError()
TA260 000:581.644 JLINK_IsHalted()
TA260 000:582.134 - 0.489ms returns FALSE
TA260 000:582.149 JLINK_HasError()
TA260 000:583.646 JLINK_IsHalted()
TA260 000:584.073 - 0.426ms returns FALSE
TA260 000:584.080 JLINK_HasError()
TA260 000:586.162 JLINK_IsHalted()
TA260 000:586.668 - 0.506ms returns FALSE
TA260 000:586.684 JLINK_HasError()
TA260 000:588.155 JLINK_IsHalted()
TA260 000:588.652 - 0.496ms returns FALSE
TA260 000:588.665 JLINK_HasError()
TA260 000:590.166 JLINK_IsHalted()
TA260 000:590.685 - 0.518ms returns FALSE
TA260 000:590.708 JLINK_HasError()
TA260 000:592.157 JLINK_IsHalted()
TA260 000:592.580 - 0.423ms returns FALSE
TA260 000:592.590 JLINK_HasError()
TA260 000:594.154 JLINK_IsHalted()
TA260 000:594.757 - 0.601ms returns FALSE
TA260 000:594.770 JLINK_HasError()
TA260 000:596.165 JLINK_IsHalted()
TA260 000:596.664 - 0.499ms returns FALSE
TA260 000:596.671 JLINK_HasError()
TA260 000:598.665 JLINK_IsHalted()
TA260 000:599.140 - 0.474ms returns FALSE
TA260 000:599.153 JLINK_HasError()
TA260 000:600.664 JLINK_IsHalted()
TA260 000:601.085 - 0.421ms returns FALSE
TA260 000:601.094 JLINK_HasError()
TA260 000:603.670 JLINK_IsHalted()
TA260 000:604.138 - 0.467ms returns FALSE
TA260 000:604.144 JLINK_HasError()
TA260 000:606.181 JLINK_IsHalted()
TA260 000:606.662 - 0.479ms returns FALSE
TA260 000:606.677 JLINK_HasError()
TA260 000:608.171 JLINK_IsHalted()
TA260 000:608.664 - 0.491ms returns FALSE
TA260 000:608.679 JLINK_HasError()
TA260 000:610.176 JLINK_IsHalted()
TA260 000:610.696 - 0.521ms returns FALSE
TA260 000:610.703 JLINK_HasError()
TA260 000:612.173 JLINK_IsHalted()
TA260 000:612.653 - 0.479ms returns FALSE
TA260 000:612.664 JLINK_HasError()
TA260 000:614.175 JLINK_IsHalted()
TA260 000:614.750 - 0.575ms returns FALSE
TA260 000:614.761 JLINK_HasError()
TA260 000:616.178 JLINK_IsHalted()
TA260 000:616.707 - 0.527ms returns FALSE
TA260 000:616.727 JLINK_HasError()
TA260 000:618.686 JLINK_IsHalted()
TA260 000:619.154 - 0.468ms returns FALSE
TA260 000:619.163 JLINK_HasError()
TA260 000:620.683 JLINK_IsHalted()
TA260 000:621.159 - 0.475ms returns FALSE
TA260 000:621.166 JLINK_HasError()
TA260 000:622.683 JLINK_IsHalted()
TA260 000:623.143 - 0.459ms returns FALSE
TA260 000:623.152 JLINK_HasError()
TA260 000:624.712 JLINK_IsHalted()
TA260 000:625.242 - 0.529ms returns FALSE
TA260 000:625.274 JLINK_HasError()
TA260 000:627.194 JLINK_IsHalted()
TA260 000:627.673 - 0.478ms returns FALSE
TA260 000:627.691 JLINK_HasError()
TA260 000:629.190 JLINK_IsHalted()
TA260 000:629.662 - 0.471ms returns FALSE
TA260 000:629.672 JLINK_HasError()
TA260 000:631.194 JLINK_IsHalted()
TA260 000:631.658 - 0.463ms returns FALSE
TA260 000:631.670 JLINK_HasError()
TA260 000:633.189 JLINK_IsHalted()
TA260 000:633.651 - 0.461ms returns FALSE
TA260 000:633.659 JLINK_HasError()
TA260 000:635.203 JLINK_IsHalted()
TA260 000:635.747 - 0.543ms returns FALSE
TA260 000:635.767 JLINK_HasError()
TA260 000:637.708 JLINK_IsHalted()
TA260 000:638.246 - 0.538ms returns FALSE
TA260 000:638.261 JLINK_HasError()
TA260 000:639.702 JLINK_IsHalted()
TA260 000:640.198 - 0.495ms returns FALSE
TA260 000:640.210 JLINK_HasError()
TA260 000:641.703 JLINK_IsHalted()
TA260 000:642.217 - 0.514ms returns FALSE
TA260 000:642.226 JLINK_HasError()
TA260 000:643.699 JLINK_IsHalted()
TA260 000:644.218 - 0.518ms returns FALSE
TA260 000:644.226 JLINK_HasError()
TA260 000:646.212 JLINK_IsHalted()
TA260 000:646.691 - 0.477ms returns FALSE
TA260 000:646.703 JLINK_HasError()
TA260 000:648.717 JLINK_IsHalted()
TA260 000:649.231 - 0.514ms returns FALSE
TA260 000:649.245 JLINK_HasError()
TA260 000:650.712 JLINK_IsHalted()
TA260 000:651.152 - 0.439ms returns FALSE
TA260 000:651.160 JLINK_HasError()
TA260 000:654.728 JLINK_IsHalted()
TA260 000:655.356 - 0.627ms returns FALSE
TA260 000:655.376 JLINK_HasError()
TA260 000:659.225 JLINK_IsHalted()
TA260 000:659.795 - 0.570ms returns FALSE
TA260 000:659.813 JLINK_HasError()
TA260 000:663.225 JLINK_IsHalted()
TA260 000:663.696 - 0.471ms returns FALSE
TA260 000:663.705 JLINK_HasError()
TA260 000:666.233 JLINK_IsHalted()
TA260 000:666.761 - 0.527ms returns FALSE
TA260 000:666.774 JLINK_HasError()
TA260 000:671.737 JLINK_IsHalted()
TA260 000:672.267 - 0.529ms returns FALSE
TA260 000:672.275 JLINK_HasError()
TA260 000:682.252 JLINK_IsHalted()
TA260 000:682.801 - 0.548ms returns FALSE
TA260 000:682.809 JLINK_HasError()
TA260 000:693.241 JLINK_IsHalted()
TA260 000:693.804 - 0.561ms returns FALSE
TA260 000:693.811 JLINK_HasError()
TA260 000:696.263 JLINK_IsHalted()
TA260 000:696.804 - 0.540ms returns FALSE
TA260 000:696.818 JLINK_HasError()
TA260 000:706.269 JLINK_IsHalted()
TA260 000:706.806 - 0.536ms returns FALSE
TA260 000:706.823 JLINK_HasError()
TA260 000:712.270 JLINK_IsHalted()
TA260 000:712.796 - 0.525ms returns FALSE
TA260 000:712.811 JLINK_HasError()
TA260 000:720.776 JLINK_IsHalted()
TA260 000:721.269 - 0.493ms returns FALSE
TA260 000:721.287 JLINK_HasError()
TA260 000:726.289 JLINK_IsHalted()
TA260 000:726.793 - 0.503ms returns FALSE
TA260 000:726.810 JLINK_HasError()
TA260 000:731.290 JLINK_IsHalted()
TA260 000:731.805 - 0.514ms returns FALSE
TA260 000:731.812 JLINK_HasError()
TA260 000:735.293 JLINK_IsHalted()
TA260 000:735.901 - 0.607ms returns FALSE
TA260 000:735.920 JLINK_HasError()
TA260 000:739.800 JLINK_IsHalted()
TA260 000:740.311 - 0.510ms returns FALSE
TA260 000:740.318 JLINK_HasError()
TA260 000:742.800 JLINK_IsHalted()
TA260 000:743.289 - 0.489ms returns FALSE
TA260 000:743.302 JLINK_HasError()
TA260 000:745.808 JLINK_IsHalted()
TA260 000:746.311 - 0.502ms returns FALSE
TA260 000:746.327 JLINK_HasError()
TA260 000:748.818 JLINK_IsHalted()
TA260 000:749.291 - 0.472ms returns FALSE
TA260 000:749.309 JLINK_HasError()
TA260 000:751.808 JLINK_IsHalted()
TA260 000:752.311 - 0.503ms returns FALSE
TA260 000:752.318 JLINK_HasError()
TA260 000:756.055 JLINK_IsHalted()
TA260 000:756.543 - 0.487ms returns FALSE
TA260 000:756.562 JLINK_HasError()
TA260 000:758.054 JLINK_IsHalted()
TA260 000:758.572 - 0.516ms returns FALSE
TA260 000:758.578 JLINK_HasError()
TA260 000:761.051 JLINK_IsHalted()
TA260 000:761.519 - 0.467ms returns FALSE
TA260 000:761.535 JLINK_HasError()
TA260 000:764.049 JLINK_IsHalted()
TA260 000:764.551 - 0.500ms returns FALSE
TA260 000:764.558 JLINK_HasError()
TA260 000:766.060 JLINK_IsHalted()
TA260 000:766.647 - 0.587ms returns FALSE
TA260 000:766.655 JLINK_HasError()
TA260 000:768.594 JLINK_IsHalted()
TA260 000:769.037 - 0.442ms returns FALSE
TA260 000:769.045 JLINK_HasError()
TA260 000:771.567 JLINK_IsHalted()
TA260 000:772.085 - 0.518ms returns FALSE
TA260 000:772.093 JLINK_HasError()
TA260 000:773.559 JLINK_IsHalted()
TA260 000:774.039 - 0.480ms returns FALSE
TA260 000:774.046 JLINK_HasError()
TA260 000:776.075 JLINK_IsHalted()
TA260 000:776.574 - 0.497ms returns FALSE
TA260 000:776.583 JLINK_HasError()
TA260 000:778.070 JLINK_IsHalted()
TA260 000:778.534 - 0.463ms returns FALSE
TA260 000:778.540 JLINK_HasError()
TA260 000:780.067 JLINK_IsHalted()
TA260 000:780.572 - 0.505ms returns FALSE
TA260 000:780.579 JLINK_HasError()
TA260 000:782.071 JLINK_IsHalted()
TA260 000:782.549 - 0.477ms returns FALSE
TA260 000:782.558 JLINK_HasError()
TA260 000:784.066 JLINK_IsHalted()
TA260 000:784.550 - 0.483ms returns FALSE
TA260 000:784.558 JLINK_HasError()
TA260 000:786.079 JLINK_IsHalted()
TA260 000:786.662 - 0.583ms returns FALSE
TA260 000:786.668 JLINK_HasError()
TA260 000:788.578 JLINK_IsHalted()
TA260 000:789.060 - 0.481ms returns FALSE
TA260 000:789.074 JLINK_HasError()
TA260 000:790.578 JLINK_IsHalted()
TA260 000:791.133 - 0.554ms returns FALSE
TA260 000:791.148 JLINK_HasError()
TA260 000:792.575 JLINK_IsHalted()
TA260 000:793.084 - 0.507ms returns FALSE
TA260 000:793.095 JLINK_HasError()
TA260 000:794.576 JLINK_IsHalted()
TA260 000:795.085 - 0.508ms returns FALSE
TA260 000:795.108 JLINK_HasError()
TA260 000:797.098 JLINK_IsHalted()
TA260 000:797.652 - 0.554ms returns FALSE
TA260 000:797.659 JLINK_HasError()
TA260 000:799.088 JLINK_IsHalted()
TA260 000:799.545 - 0.456ms returns FALSE
TA260 000:799.552 JLINK_HasError()
TA260 000:801.091 JLINK_IsHalted()
TA260 000:801.557 - 0.467ms returns FALSE
TA260 000:801.564 JLINK_HasError()
TA260 000:803.082 JLINK_IsHalted()
TA260 000:803.561 - 0.479ms returns FALSE
TA260 000:803.569 JLINK_HasError()
TA260 000:805.097 JLINK_IsHalted()
TA260 000:805.666 - 0.569ms returns FALSE
TA260 000:805.681 JLINK_HasError()
TA260 000:807.602 JLINK_IsHalted()
TA260 000:808.098 - 0.495ms returns FALSE
TA260 000:808.118 JLINK_HasError()
TA260 000:809.603 JLINK_IsHalted()
TA260 000:810.093 - 0.489ms returns FALSE
TA260 000:810.103 JLINK_HasError()
TA260 000:811.603 JLINK_IsHalted()
TA260 000:812.128 - 0.525ms returns FALSE
TA260 000:812.137 JLINK_HasError()
TA260 000:813.593 JLINK_IsHalted()
TA260 000:814.090 - 0.497ms returns FALSE
TA260 000:814.100 JLINK_HasError()
TA260 000:816.107 JLINK_IsHalted()
TA260 000:816.655 - 0.547ms returns FALSE
TA260 000:816.662 JLINK_HasError()
TA260 000:818.104 JLINK_IsHalted()
TA260 000:818.660 - 0.555ms returns FALSE
TA260 000:818.670 JLINK_HasError()
TA260 000:820.107 JLINK_IsHalted()
TA260 000:820.652 - 0.544ms returns FALSE
TA260 000:820.659 JLINK_HasError()
TA260 000:822.107 JLINK_IsHalted()
TA260 000:822.649 - 0.541ms returns FALSE
TA260 000:822.658 JLINK_HasError()
TA260 000:824.101 JLINK_IsHalted()
TA260 000:824.561 - 0.459ms returns FALSE
TA260 000:824.568 JLINK_HasError()
TA260 000:826.113 JLINK_IsHalted()
TA260 000:826.654 - 0.540ms returns FALSE
TA260 000:826.661 JLINK_HasError()
TA260 000:828.610 JLINK_IsHalted()
TA260 000:829.102 - 0.490ms returns FALSE
TA260 000:829.119 JLINK_HasError()
TA260 000:830.613 JLINK_IsHalted()
TA260 000:831.120 - 0.505ms returns FALSE
TA260 000:831.128 JLINK_HasError()
TA260 000:832.616 JLINK_IsHalted()
TA260 000:833.118 - 0.501ms returns FALSE
TA260 000:833.128 JLINK_HasError()
TA260 000:834.610 JLINK_IsHalted()
TA260 000:835.112 - 0.498ms returns FALSE
TA260 000:835.137 JLINK_HasError()
TA260 000:837.134 JLINK_IsHalted()
TA260 000:837.654 - 0.520ms returns FALSE
TA260 000:837.664 JLINK_HasError()
TA260 000:839.120 JLINK_IsHalted()
TA260 000:839.650 - 0.529ms returns FALSE
TA260 000:839.658 JLINK_HasError()
TA260 000:841.132 JLINK_IsHalted()
TA260 000:841.650 - 0.517ms returns FALSE
TA260 000:841.657 JLINK_HasError()
TA260 000:843.142 JLINK_IsHalted()
TA260 000:843.649 - 0.507ms returns FALSE
TA260 000:843.663 JLINK_HasError()
TA260 000:845.129 JLINK_IsHalted()
TA260 000:845.593 - 0.463ms returns FALSE
TA260 000:845.609 JLINK_HasError()
TA260 000:847.647 JLINK_IsHalted()
TA260 000:848.164 - 0.516ms returns FALSE
TA260 000:848.172 JLINK_HasError()
TA260 000:850.134 JLINK_IsHalted()
TA260 000:850.575 - 0.440ms returns FALSE
TA260 000:850.592 JLINK_HasError()
TA260 000:852.141 JLINK_IsHalted()
TA260 000:852.653 - 0.512ms returns FALSE
TA260 000:852.659 JLINK_HasError()
TA260 000:854.134 JLINK_IsHalted()
TA260 000:854.688 - 0.552ms returns FALSE
TA260 000:854.708 JLINK_HasError()
TA260 000:856.079 JLINK_IsHalted()
TA260 000:856.654 - 0.574ms returns FALSE
TA260 000:856.668 JLINK_HasError()
TA260 000:858.080 JLINK_IsHalted()
TA260 000:858.566 - 0.485ms returns FALSE
TA260 000:858.582 JLINK_HasError()
TA260 000:860.077 JLINK_IsHalted()
TA260 000:860.660 - 0.582ms returns FALSE
TA260 000:860.671 JLINK_HasError()
TA260 000:862.083 JLINK_IsHalted()
TA260 000:862.584 - 0.500ms returns FALSE
TA260 000:862.591 JLINK_HasError()
TA260 000:864.094 JLINK_IsHalted()
TA260 000:864.799 - 0.704ms returns FALSE
TA260 000:864.813 JLINK_HasError()
TA260 000:866.092 JLINK_IsHalted()
TA260 000:866.655 - 0.562ms returns FALSE
TA260 000:866.672 JLINK_HasError()
TA260 000:868.594 JLINK_IsHalted()
TA260 000:869.103 - 0.508ms returns FALSE
TA260 000:869.110 JLINK_HasError()
TA260 000:870.588 JLINK_IsHalted()
TA260 000:871.080 - 0.491ms returns FALSE
TA260 000:871.090 JLINK_HasError()
TA260 000:872.595 JLINK_IsHalted()
TA260 000:873.008 - 0.412ms returns FALSE
TA260 000:873.015 JLINK_HasError()
TA260 000:874.592 JLINK_IsHalted()
TA260 000:875.148 - 0.555ms returns FALSE
TA260 000:875.168 JLINK_HasError()
TA260 000:877.104 JLINK_IsHalted()
TA260 000:877.663 - 0.558ms returns FALSE
TA260 000:877.671 JLINK_HasError()
TA260 000:879.098 JLINK_IsHalted()
TA260 000:879.651 - 0.553ms returns FALSE
TA260 000:879.659 JLINK_HasError()
TA260 000:881.100 JLINK_IsHalted()
TA260 000:881.683 - 0.583ms returns FALSE
TA260 000:881.690 JLINK_HasError()
TA260 000:884.099 JLINK_IsHalted()
TA260 000:884.798 - 0.698ms returns FALSE
TA260 000:884.877 JLINK_HasError()
TA260 000:886.110 JLINK_IsHalted()
TA260 000:886.667 - 0.556ms returns FALSE
TA260 000:886.685 JLINK_HasError()
TA260 000:888.610 JLINK_IsHalted()
TA260 000:889.055 - 0.445ms returns FALSE
TA260 000:889.062 JLINK_HasError()
TA260 000:890.617 JLINK_IsHalted()
TA260 000:891.063 - 0.445ms returns FALSE
TA260 000:891.072 JLINK_HasError()
TA260 000:892.362 JLINK_IsHalted()
TA260 000:892.884 - 0.522ms returns FALSE
TA260 000:892.892 JLINK_HasError()
TA260 000:894.863 JLINK_IsHalted()
TA260 000:895.360 - 0.496ms returns FALSE
TA260 000:895.390 JLINK_HasError()
TA260 000:897.380 JLINK_IsHalted()
TA260 000:897.857 - 0.477ms returns FALSE
TA260 000:897.873 JLINK_HasError()
TA260 000:899.371 JLINK_IsHalted()
TA260 000:899.787 - 0.416ms returns FALSE
TA260 000:899.794 JLINK_HasError()
TA260 000:901.376 JLINK_IsHalted()
TA260 000:901.894 - 0.517ms returns FALSE
TA260 000:901.905 JLINK_HasError()
TA260 000:903.369 JLINK_IsHalted()
TA260 000:903.876 - 0.506ms returns FALSE
TA260 000:903.882 JLINK_HasError()
TA260 000:905.888 JLINK_IsHalted()
TA260 000:906.355 - 0.467ms returns FALSE
TA260 000:906.377 JLINK_HasError()
TA260 000:907.886 JLINK_IsHalted()
TA260 000:908.381 - 0.494ms returns FALSE
TA260 000:908.389 JLINK_HasError()
TA260 000:909.883 JLINK_IsHalted()
TA260 000:910.387 - 0.504ms returns FALSE
TA260 000:910.395 JLINK_HasError()
TA260 000:911.883 JLINK_IsHalted()
TA260 000:912.309 - 0.426ms returns FALSE
TA260 000:912.317 JLINK_HasError()
TA260 000:915.898 JLINK_IsHalted()
TA260 000:916.370 - 0.471ms returns FALSE
TA260 000:916.400 JLINK_HasError()
TA260 000:918.400 JLINK_IsHalted()
TA260 000:918.887 - 0.486ms returns FALSE
TA260 000:918.905 JLINK_HasError()
TA260 000:920.395 JLINK_IsHalted()
TA260 000:920.890 - 0.494ms returns FALSE
TA260 000:920.939 JLINK_HasError()
TA260 000:922.397 JLINK_IsHalted()
TA260 000:922.892 - 0.494ms returns FALSE
TA260 000:922.900 JLINK_HasError()
TA260 000:924.399 JLINK_IsHalted()
TA260 000:924.946 - 0.546ms returns FALSE
TA260 000:924.973 JLINK_HasError()
TA260 000:926.904 JLINK_IsHalted()
TA260 000:927.407 - 0.502ms returns FALSE
TA260 000:927.424 JLINK_HasError()
TA260 000:928.908 JLINK_IsHalted()
TA260 000:929.456 - 0.547ms returns FALSE
TA260 000:929.465 JLINK_HasError()
TA260 000:930.911 JLINK_IsHalted()
TA260 000:931.426 - 0.515ms returns FALSE
TA260 000:931.434 JLINK_HasError()
TA260 000:932.905 JLINK_IsHalted()
TA260 000:933.357 - 0.452ms returns FALSE
TA260 000:933.366 JLINK_HasError()
TA260 000:934.916 JLINK_IsHalted()
TA260 000:935.536 - 0.619ms returns FALSE
TA260 000:935.560 JLINK_HasError()
TA260 000:937.428 JLINK_IsHalted()
TA260 000:937.892 - 0.463ms returns FALSE
TA260 000:937.899 JLINK_HasError()
TA260 000:939.418 JLINK_IsHalted()
TA260 000:939.877 - 0.459ms returns FALSE
TA260 000:939.884 JLINK_HasError()
TA260 000:941.421 JLINK_IsHalted()
TA260 000:941.922 - 0.501ms returns FALSE
TA260 000:941.930 JLINK_HasError()
TA260 000:943.418 JLINK_IsHalted()
TA260 000:943.924 - 0.506ms returns FALSE
TA260 000:943.931 JLINK_HasError()
TA260 000:945.935 JLINK_IsHalted()
TA260 000:946.461 - 0.525ms returns FALSE
TA260 000:946.474 JLINK_HasError()
TA260 000:947.928 JLINK_IsHalted()
TA260 000:948.425 - 0.497ms returns FALSE
TA260 000:948.439 JLINK_HasError()
TA260 000:950.432 JLINK_IsHalted()
TA260 000:950.936 - 0.504ms returns FALSE
TA260 000:950.943 JLINK_HasError()
TA260 000:952.434 JLINK_IsHalted()
TA260 000:952.935 - 0.501ms returns FALSE
TA260 000:952.944 JLINK_HasError()
TA260 000:954.431 JLINK_IsHalted()
TA260 000:954.959 - 0.526ms returns FALSE
TA260 000:954.985 JLINK_HasError()
TA260 000:956.942 JLINK_IsHalted()
TA260 000:957.439 - 0.496ms returns FALSE
TA260 000:957.448 JLINK_HasError()
TA260 000:958.941 JLINK_IsHalted()
TA260 000:959.469 - 0.527ms returns FALSE
TA260 000:959.476 JLINK_HasError()
TA260 000:960.942 JLINK_IsHalted()
TA260 000:961.380 - 0.438ms returns FALSE
TA260 000:961.389 JLINK_HasError()
TA260 000:962.943 JLINK_IsHalted()
TA260 000:963.368 - 0.424ms returns FALSE
TA260 000:963.377 JLINK_HasError()
TA260 000:964.950 JLINK_IsHalted()
TA260 000:965.513 - 0.562ms returns FALSE
TA260 000:965.531 JLINK_HasError()
TA260 000:967.457 JLINK_IsHalted()
TA260 000:967.939 - 0.482ms returns FALSE
TA260 000:967.956 JLINK_HasError()
TA260 000:969.448 JLINK_IsHalted()
TA260 000:969.943 - 0.495ms returns FALSE
TA260 000:969.951 JLINK_HasError()
TA260 000:971.454 JLINK_IsHalted()
TA260 000:971.925 - 0.471ms returns FALSE
TA260 000:971.939 JLINK_HasError()
TA260 000:973.456 JLINK_IsHalted()
TA260 000:973.974 - 0.517ms returns FALSE
TA260 000:973.986 JLINK_HasError()
TA260 000:975.972 JLINK_IsHalted()
TA260 000:978.389   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:978.936 - 2.964ms returns TRUE
TA260 000:978.944 JLINK_ReadReg(R15 (PC))
TA260 000:978.950 - 0.005ms returns 0x20000000
TA260 000:978.954 JLINK_ClrBPEx(BPHandle = 0x00000003)
TA260 000:978.958 - 0.003ms returns 0x00
TA260 000:978.962 JLINK_ReadReg(R0)
TA260 000:978.966 - 0.003ms returns 0x00000000
TA260 000:979.373 JLINK_HasError()
TA260 000:979.383 JLINK_WriteReg(R0, 0x08004000)
TA260 000:979.388 - 0.004ms returns 0
TA260 000:979.392 JLINK_WriteReg(R1, 0x00004000)
TA260 000:979.396 - 0.003ms returns 0
TA260 000:979.400 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:979.403 - 0.003ms returns 0
TA260 000:979.407 JLINK_WriteReg(R3, 0x00000000)
TA260 000:979.411 - 0.003ms returns 0
TA260 000:979.415 JLINK_WriteReg(R4, 0x00000000)
TA260 000:979.418 - 0.003ms returns 0
TA260 000:979.423 JLINK_WriteReg(R5, 0x00000000)
TA260 000:979.426 - 0.003ms returns 0
TA260 000:979.430 JLINK_WriteReg(R6, 0x00000000)
TA260 000:979.434 - 0.003ms returns 0
TA260 000:979.438 JLINK_WriteReg(R7, 0x00000000)
TA260 000:979.441 - 0.003ms returns 0
TA260 000:979.445 JLINK_WriteReg(R8, 0x00000000)
TA260 000:979.449 - 0.003ms returns 0
TA260 000:979.453 JLINK_WriteReg(R9, 0x20000180)
TA260 000:979.456 - 0.003ms returns 0
TA260 000:979.460 JLINK_WriteReg(R10, 0x00000000)
TA260 000:979.463 - 0.003ms returns 0
TA260 000:979.467 JLINK_WriteReg(R11, 0x00000000)
TA260 000:979.471 - 0.003ms returns 0
TA260 000:979.475 JLINK_WriteReg(R12, 0x00000000)
TA260 000:979.478 - 0.003ms returns 0
TA260 000:979.483 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:979.487 - 0.004ms returns 0
TA260 000:979.491 JLINK_WriteReg(R14, 0x20000001)
TA260 000:979.494 - 0.003ms returns 0
TA260 000:979.498 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 000:979.501 - 0.003ms returns 0
TA260 000:979.506 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:979.509 - 0.003ms returns 0
TA260 000:979.513 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:979.517 - 0.003ms returns 0
TA260 000:979.521 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:979.524 - 0.003ms returns 0
TA260 000:979.528 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:979.532 - 0.003ms returns 0
TA260 000:979.536 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:979.540 - 0.004ms returns 0x00000004
TA260 000:979.545 JLINK_Go()
TA260 000:979.555   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:982.263 - 2.718ms 
TA260 000:982.278 JLINK_IsHalted()
TA260 000:984.818   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:985.334 - 3.054ms returns TRUE
TA260 000:985.352 JLINK_ReadReg(R15 (PC))
TA260 000:985.366 - 0.014ms returns 0x20000000
TA260 000:985.371 JLINK_ClrBPEx(BPHandle = 0x00000004)
TA260 000:985.375 - 0.004ms returns 0x00
TA260 000:985.379 JLINK_ReadReg(R0)
TA260 000:985.383 - 0.003ms returns 0x00000001
TA260 000:985.388 JLINK_HasError()
TA260 000:985.393 JLINK_WriteReg(R0, 0x08004000)
TA260 000:985.397 - 0.003ms returns 0
TA260 000:985.402 JLINK_WriteReg(R1, 0x00004000)
TA260 000:985.405 - 0.003ms returns 0
TA260 000:985.410 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:985.414 - 0.003ms returns 0
TA260 000:985.418 JLINK_WriteReg(R3, 0x00000000)
TA260 000:985.422 - 0.004ms returns 0
TA260 000:985.426 JLINK_WriteReg(R4, 0x00000000)
TA260 000:985.430 - 0.003ms returns 0
TA260 000:985.434 JLINK_WriteReg(R5, 0x00000000)
TA260 000:985.531 - 0.096ms returns 0
TA260 000:985.540 JLINK_WriteReg(R6, 0x00000000)
TA260 000:985.543 - 0.003ms returns 0
TA260 000:985.547 JLINK_WriteReg(R7, 0x00000000)
TA260 000:985.551 - 0.003ms returns 0
TA260 000:985.555 JLINK_WriteReg(R8, 0x00000000)
TA260 000:985.558 - 0.003ms returns 0
TA260 000:985.562 JLINK_WriteReg(R9, 0x20000180)
TA260 000:985.565 - 0.003ms returns 0
TA260 000:985.587 JLINK_WriteReg(R10, 0x00000000)
TA260 000:985.591 - 0.003ms returns 0
TA260 000:985.595 JLINK_WriteReg(R11, 0x00000000)
TA260 000:985.598 - 0.003ms returns 0
TA260 000:985.602 JLINK_WriteReg(R12, 0x00000000)
TA260 000:985.606 - 0.003ms returns 0
TA260 000:985.610 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:985.614 - 0.004ms returns 0
TA260 000:985.618 JLINK_WriteReg(R14, 0x20000001)
TA260 000:985.621 - 0.003ms returns 0
TA260 000:985.625 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 000:985.629 - 0.003ms returns 0
TA260 000:985.633 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:985.636 - 0.003ms returns 0
TA260 000:985.640 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:985.644 - 0.003ms returns 0
TA260 000:985.648 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:985.651 - 0.003ms returns 0
TA260 000:985.656 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:985.659 - 0.003ms returns 0
TA260 000:985.664 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:985.668 - 0.004ms returns 0x00000005
TA260 000:985.672 JLINK_Go()
TA260 000:985.682   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:988.447 - 2.773ms 
TA260 000:988.464 JLINK_IsHalted()
TA260 000:988.958 - 0.494ms returns FALSE
TA260 000:988.965 JLINK_HasError()
TA260 000:990.156 JLINK_IsHalted()
TA260 000:990.649 - 0.493ms returns FALSE
TA260 000:990.657 JLINK_HasError()
TA260 000:993.165 JLINK_IsHalted()
TA260 000:993.652 - 0.487ms returns FALSE
TA260 000:993.660 JLINK_HasError()
TA260 000:995.165 JLINK_IsHalted()
TA260 000:995.657 - 0.491ms returns FALSE
TA260 000:995.672 JLINK_HasError()
TA260 000:997.668 JLINK_IsHalted()
TA260 000:998.121 - 0.452ms returns FALSE
TA260 000:998.128 JLINK_HasError()
TA260 000:999.672 JLINK_IsHalted()
TA260 001:000.230 - 0.557ms returns FALSE
TA260 001:000.237 JLINK_HasError()
TA260 001:001.665 JLINK_IsHalted()
TA260 001:002.171 - 0.505ms returns FALSE
TA260 001:002.182 JLINK_HasError()
TA260 001:003.667 JLINK_IsHalted()
TA260 001:004.097 - 0.429ms returns FALSE
TA260 001:004.104 JLINK_HasError()
TA260 001:005.199 JLINK_IsHalted()
TA260 001:005.718 - 0.518ms returns FALSE
TA260 001:005.734 JLINK_HasError()
TA260 001:007.217 JLINK_IsHalted()
TA260 001:007.688 - 0.470ms returns FALSE
TA260 001:007.704 JLINK_HasError()
TA260 001:009.219 JLINK_IsHalted()
TA260 001:009.696 - 0.476ms returns FALSE
TA260 001:009.703 JLINK_HasError()
TA260 001:011.224 JLINK_IsHalted()
TA260 001:011.700 - 0.475ms returns FALSE
TA260 001:011.711 JLINK_HasError()
TA260 001:013.217 JLINK_IsHalted()
TA260 001:013.696 - 0.478ms returns FALSE
TA260 001:013.706 JLINK_HasError()
TA260 001:015.228 JLINK_IsHalted()
TA260 001:015.810 - 0.580ms returns FALSE
TA260 001:015.828 JLINK_HasError()
TA260 001:017.731 JLINK_IsHalted()
TA260 001:018.259 - 0.527ms returns FALSE
TA260 001:018.274 JLINK_HasError()
TA260 001:019.722 JLINK_IsHalted()
TA260 001:020.199 - 0.477ms returns FALSE
TA260 001:020.208 JLINK_HasError()
TA260 001:021.726 JLINK_IsHalted()
TA260 001:022.197 - 0.470ms returns FALSE
TA260 001:022.212 JLINK_HasError()
TA260 001:024.126 JLINK_IsHalted()
TA260 001:024.719 - 0.592ms returns FALSE
TA260 001:024.738 JLINK_HasError()
TA260 001:026.150 JLINK_IsHalted()
TA260 001:026.664 - 0.514ms returns FALSE
TA260 001:026.679 JLINK_HasError()
TA260 001:028.648 JLINK_IsHalted()
TA260 001:029.096 - 0.447ms returns FALSE
TA260 001:029.104 JLINK_HasError()
TA260 001:030.632 JLINK_IsHalted()
TA260 001:031.091 - 0.459ms returns FALSE
TA260 001:031.098 JLINK_HasError()
TA260 001:032.640 JLINK_IsHalted()
TA260 001:033.118 - 0.477ms returns FALSE
TA260 001:033.126 JLINK_HasError()
TA260 001:034.671 JLINK_IsHalted()
TA260 001:035.237 - 0.564ms returns FALSE
TA260 001:035.266 JLINK_HasError()
TA260 001:037.160 JLINK_IsHalted()
TA260 001:037.704 - 0.543ms returns FALSE
TA260 001:037.718 JLINK_HasError()
TA260 001:039.152 JLINK_IsHalted()
TA260 001:039.649 - 0.497ms returns FALSE
TA260 001:039.659 JLINK_HasError()
TA260 001:041.150 JLINK_IsHalted()
TA260 001:041.657 - 0.506ms returns FALSE
TA260 001:041.664 JLINK_HasError()
TA260 001:043.147 JLINK_IsHalted()
TA260 001:043.649 - 0.502ms returns FALSE
TA260 001:043.657 JLINK_HasError()
TA260 001:045.159 JLINK_IsHalted()
TA260 001:045.706 - 0.546ms returns FALSE
TA260 001:045.719 JLINK_HasError()
TA260 001:047.665 JLINK_IsHalted()
TA260 001:048.140 - 0.474ms returns FALSE
TA260 001:048.152 JLINK_HasError()
TA260 001:050.163 JLINK_IsHalted()
TA260 001:050.698 - 0.534ms returns FALSE
TA260 001:050.708 JLINK_HasError()
TA260 001:052.164 JLINK_IsHalted()
TA260 001:052.754 - 0.589ms returns FALSE
TA260 001:052.776 JLINK_HasError()
TA260 001:054.164 JLINK_IsHalted()
TA260 001:054.659 - 0.494ms returns FALSE
TA260 001:054.677 JLINK_HasError()
TA260 001:056.183 JLINK_IsHalted()
TA260 001:056.814 - 0.630ms returns FALSE
TA260 001:056.835 JLINK_HasError()
TA260 001:058.677 JLINK_IsHalted()
TA260 001:059.164 - 0.486ms returns FALSE
TA260 001:059.173 JLINK_HasError()
TA260 001:060.683 JLINK_IsHalted()
TA260 001:061.167 - 0.483ms returns FALSE
TA260 001:061.182 JLINK_HasError()
TA260 001:062.679 JLINK_IsHalted()
TA260 001:063.201 - 0.521ms returns FALSE
TA260 001:063.211 JLINK_HasError()
TA260 001:064.739 JLINK_IsHalted()
TA260 001:065.249 - 0.508ms returns FALSE
TA260 001:065.292 JLINK_HasError()
TA260 001:067.195 JLINK_IsHalted()
TA260 001:067.688 - 0.492ms returns FALSE
TA260 001:067.703 JLINK_HasError()
TA260 001:068.764 JLINK_IsHalted()
TA260 001:069.255 - 0.490ms returns FALSE
TA260 001:069.267 JLINK_HasError()
TA260 001:070.763 JLINK_IsHalted()
TA260 001:071.219 - 0.454ms returns FALSE
TA260 001:071.231 JLINK_HasError()
TA260 001:072.768 JLINK_IsHalted()
TA260 001:073.256 - 0.487ms returns FALSE
TA260 001:073.272 JLINK_HasError()
TA260 001:074.764 JLINK_IsHalted()
TA260 001:075.300 - 0.535ms returns FALSE
TA260 001:075.317 JLINK_HasError()
TA260 001:077.280 JLINK_IsHalted()
TA260 001:077.807 - 0.526ms returns FALSE
TA260 001:077.824 JLINK_HasError()
TA260 001:079.277 JLINK_IsHalted()
TA260 001:079.801 - 0.523ms returns FALSE
TA260 001:079.810 JLINK_HasError()
TA260 001:081.277 JLINK_IsHalted()
TA260 001:081.789 - 0.511ms returns FALSE
TA260 001:081.798 JLINK_HasError()
TA260 001:083.274 JLINK_IsHalted()
TA260 001:083.804 - 0.529ms returns FALSE
TA260 001:083.816 JLINK_HasError()
TA260 001:085.280 JLINK_IsHalted()
TA260 001:085.827 - 0.545ms returns FALSE
TA260 001:085.843 JLINK_HasError()
TA260 001:087.805 JLINK_IsHalted()
TA260 001:088.337 - 0.531ms returns FALSE
TA260 001:088.352 JLINK_HasError()
TA260 001:089.784 JLINK_IsHalted()
TA260 001:090.294 - 0.509ms returns FALSE
TA260 001:090.302 JLINK_HasError()
TA260 001:091.786 JLINK_IsHalted()
TA260 001:092.255 - 0.468ms returns FALSE
TA260 001:092.262 JLINK_HasError()
TA260 001:093.786 JLINK_IsHalted()
TA260 001:094.286 - 0.500ms returns FALSE
TA260 001:094.294 JLINK_HasError()
TA260 001:096.300 JLINK_IsHalted()
TA260 001:096.802 - 0.501ms returns FALSE
TA260 001:096.809 JLINK_HasError()
TA260 001:098.342 JLINK_IsHalted()
TA260 001:098.884 - 0.541ms returns FALSE
TA260 001:098.926 JLINK_HasError()
TA260 001:101.024 JLINK_IsHalted()
TA260 001:101.527 - 0.503ms returns FALSE
TA260 001:101.536 JLINK_HasError()
TA260 001:103.028 JLINK_IsHalted()
TA260 001:103.517 - 0.489ms returns FALSE
TA260 001:103.526 JLINK_HasError()
TA260 001:105.026 JLINK_IsHalted()
TA260 001:105.518 - 0.491ms returns FALSE
TA260 001:105.532 JLINK_HasError()
TA260 001:107.534 JLINK_IsHalted()
TA260 001:108.019 - 0.484ms returns FALSE
TA260 001:108.035 JLINK_HasError()
TA260 001:109.535 JLINK_IsHalted()
TA260 001:110.000 - 0.465ms returns FALSE
TA260 001:110.006 JLINK_HasError()
TA260 001:111.530 JLINK_IsHalted()
TA260 001:111.979 - 0.449ms returns FALSE
TA260 001:111.987 JLINK_HasError()
TA260 001:113.528 JLINK_IsHalted()
TA260 001:113.985 - 0.456ms returns FALSE
TA260 001:114.011 JLINK_HasError()
TA260 001:116.043 JLINK_IsHalted()
TA260 001:116.519 - 0.476ms returns FALSE
TA260 001:116.529 JLINK_HasError()
TA260 001:118.044 JLINK_IsHalted()
TA260 001:118.543 - 0.498ms returns FALSE
TA260 001:118.561 JLINK_HasError()
TA260 001:120.043 JLINK_IsHalted()
TA260 001:120.514 - 0.470ms returns FALSE
TA260 001:120.520 JLINK_HasError()
TA260 001:122.048 JLINK_IsHalted()
TA260 001:122.576 - 0.527ms returns FALSE
TA260 001:122.588 JLINK_HasError()
TA260 001:124.042 JLINK_IsHalted()
TA260 001:124.558 - 0.515ms returns FALSE
TA260 001:124.572 JLINK_HasError()
TA260 001:126.051 JLINK_IsHalted()
TA260 001:126.555 - 0.504ms returns FALSE
TA260 001:126.572 JLINK_HasError()
TA260 001:128.525 JLINK_IsHalted()
TA260 001:128.995 - 0.469ms returns FALSE
TA260 001:129.004 JLINK_HasError()
TA260 001:130.520 JLINK_IsHalted()
TA260 001:131.025 - 0.505ms returns FALSE
TA260 001:131.033 JLINK_HasError()
TA260 001:132.519 JLINK_IsHalted()
TA260 001:133.037 - 0.517ms returns FALSE
TA260 001:133.047 JLINK_HasError()
TA260 001:134.523 JLINK_IsHalted()
TA260 001:134.990 - 0.466ms returns FALSE
TA260 001:135.014 JLINK_HasError()
TA260 001:136.189 JLINK_IsHalted()
TA260 001:136.684 - 0.495ms returns FALSE
TA260 001:136.691 JLINK_HasError()
TA260 001:138.619 JLINK_IsHalted()
TA260 001:139.073 - 0.452ms returns FALSE
TA260 001:139.079 JLINK_HasError()
TA260 001:140.619 JLINK_IsHalted()
TA260 001:141.096 - 0.461ms returns FALSE
TA260 001:141.108 JLINK_HasError()
TA260 001:142.617 JLINK_IsHalted()
TA260 001:143.071 - 0.453ms returns FALSE
TA260 001:143.078 JLINK_HasError()
TA260 001:144.618 JLINK_IsHalted()
TA260 001:145.263 - 0.644ms returns FALSE
TA260 001:145.278 JLINK_HasError()
TA260 001:147.127 JLINK_IsHalted()
TA260 001:147.651 - 0.524ms returns FALSE
TA260 001:147.658 JLINK_HasError()
TA260 001:149.130 JLINK_IsHalted()
TA260 001:149.658 - 0.528ms returns FALSE
TA260 001:149.671 JLINK_HasError()
TA260 001:151.632 JLINK_IsHalted()
TA260 001:152.130 - 0.498ms returns FALSE
TA260 001:152.139 JLINK_HasError()
TA260 001:153.626 JLINK_IsHalted()
TA260 001:154.125 - 0.499ms returns FALSE
TA260 001:154.131 JLINK_HasError()
TA260 001:156.139 JLINK_IsHalted()
TA260 001:156.655 - 0.515ms returns FALSE
TA260 001:156.676 JLINK_HasError()
TA260 001:158.137 JLINK_IsHalted()
TA260 001:158.662 - 0.524ms returns FALSE
TA260 001:158.672 JLINK_HasError()
TA260 001:159.977 JLINK_IsHalted()
TA260 001:160.414 - 0.436ms returns FALSE
TA260 001:160.422 JLINK_HasError()
TA260 001:161.979 JLINK_IsHalted()
TA260 001:162.472 - 0.492ms returns FALSE
TA260 001:162.480 JLINK_HasError()
TA260 001:163.979 JLINK_IsHalted()
TA260 001:164.455 - 0.476ms returns FALSE
TA260 001:164.464 JLINK_HasError()
TA260 001:165.988 JLINK_IsHalted()
TA260 001:166.461 - 0.473ms returns FALSE
TA260 001:166.468 JLINK_HasError()
TA260 001:168.488 JLINK_IsHalted()
TA260 001:168.987 - 0.498ms returns FALSE
TA260 001:168.994 JLINK_HasError()
TA260 001:170.483 JLINK_IsHalted()
TA260 001:170.970 - 0.487ms returns FALSE
TA260 001:170.978 JLINK_HasError()
TA260 001:172.487 JLINK_IsHalted()
TA260 001:173.034 - 0.547ms returns FALSE
TA260 001:173.042 JLINK_HasError()
TA260 001:174.486 JLINK_IsHalted()
TA260 001:175.029 - 0.541ms returns FALSE
TA260 001:175.055 JLINK_HasError()
TA260 001:176.990 JLINK_IsHalted()
TA260 001:177.510 - 0.519ms returns FALSE
TA260 001:177.527 JLINK_HasError()
TA260 001:178.995 JLINK_IsHalted()
TA260 001:179.515 - 0.520ms returns FALSE
TA260 001:179.524 JLINK_HasError()
TA260 001:180.996 JLINK_IsHalted()
TA260 001:181.494 - 0.498ms returns FALSE
TA260 001:181.504 JLINK_HasError()
TA260 001:182.994 JLINK_IsHalted()
TA260 001:183.458 - 0.462ms returns FALSE
TA260 001:183.467 JLINK_HasError()
TA260 001:184.999 JLINK_IsHalted()
TA260 001:185.479 - 0.479ms returns FALSE
TA260 001:185.586 JLINK_HasError()
TA260 001:187.510 JLINK_IsHalted()
TA260 001:187.994 - 0.483ms returns FALSE
TA260 001:188.001 JLINK_HasError()
TA260 001:189.504 JLINK_IsHalted()
TA260 001:189.968 - 0.463ms returns FALSE
TA260 001:189.977 JLINK_HasError()
TA260 001:191.505 JLINK_IsHalted()
TA260 001:192.028 - 0.523ms returns FALSE
TA260 001:192.035 JLINK_HasError()
TA260 001:193.504 JLINK_IsHalted()
TA260 001:193.969 - 0.464ms returns FALSE
TA260 001:193.976 JLINK_HasError()
TA260 001:196.013 JLINK_IsHalted()
TA260 001:196.532 - 0.518ms returns FALSE
TA260 001:196.547 JLINK_HasError()
TA260 001:198.016 JLINK_IsHalted()
TA260 001:198.506 - 0.489ms returns FALSE
TA260 001:198.514 JLINK_HasError()
TA260 001:200.020 JLINK_IsHalted()
TA260 001:200.548 - 0.528ms returns FALSE
TA260 001:200.555 JLINK_HasError()
TA260 001:202.015 JLINK_IsHalted()
TA260 001:202.504 - 0.488ms returns FALSE
TA260 001:202.513 JLINK_HasError()
TA260 001:204.015 JLINK_IsHalted()
TA260 001:204.482 - 0.466ms returns FALSE
TA260 001:204.491 JLINK_HasError()
TA260 001:206.021 JLINK_IsHalted()
TA260 001:206.525 - 0.503ms returns FALSE
TA260 001:206.540 JLINK_HasError()
TA260 001:209.534 JLINK_IsHalted()
TA260 001:210.039 - 0.505ms returns FALSE
TA260 001:210.046 JLINK_HasError()
TA260 001:211.523 JLINK_IsHalted()
TA260 001:212.026 - 0.502ms returns FALSE
TA260 001:212.032 JLINK_HasError()
TA260 001:213.537 JLINK_IsHalted()
TA260 001:213.988 - 0.451ms returns FALSE
TA260 001:213.996 JLINK_HasError()
TA260 001:216.032 JLINK_IsHalted()
TA260 001:216.574 - 0.541ms returns FALSE
TA260 001:216.583 JLINK_HasError()
TA260 001:218.034 JLINK_IsHalted()
TA260 001:218.550 - 0.515ms returns FALSE
TA260 001:218.564 JLINK_HasError()
TA260 001:220.027 JLINK_IsHalted()
TA260 001:220.501 - 0.474ms returns FALSE
TA260 001:220.507 JLINK_HasError()
TA260 001:222.030 JLINK_IsHalted()
TA260 001:222.568 - 0.537ms returns FALSE
TA260 001:222.574 JLINK_HasError()
TA260 001:224.032 JLINK_IsHalted()
TA260 001:224.523 - 0.490ms returns FALSE
TA260 001:224.529 JLINK_HasError()
TA260 001:226.032 JLINK_IsHalted()
TA260 001:226.549 - 0.516ms returns FALSE
TA260 001:226.560 JLINK_HasError()
TA260 001:228.535 JLINK_IsHalted()
TA260 001:229.036 - 0.501ms returns FALSE
TA260 001:229.049 JLINK_HasError()
TA260 001:230.534 JLINK_IsHalted()
TA260 001:231.025 - 0.490ms returns FALSE
TA260 001:231.031 JLINK_HasError()
TA260 001:232.534 JLINK_IsHalted()
TA260 001:233.042 - 0.508ms returns FALSE
TA260 001:233.048 JLINK_HasError()
TA260 001:234.536 JLINK_IsHalted()
TA260 001:235.017 - 0.480ms returns FALSE
TA260 001:235.027 JLINK_HasError()
TA260 001:237.080 JLINK_IsHalted()
TA260 001:237.661 - 0.580ms returns FALSE
TA260 001:237.674 JLINK_HasError()
TA260 001:239.040 JLINK_IsHalted()
TA260 001:239.501 - 0.460ms returns FALSE
TA260 001:239.507 JLINK_HasError()
TA260 001:241.043 JLINK_IsHalted()
TA260 001:241.545 - 0.502ms returns FALSE
TA260 001:241.552 JLINK_HasError()
TA260 001:243.041 JLINK_IsHalted()
TA260 001:243.543 - 0.501ms returns FALSE
TA260 001:243.548 JLINK_HasError()
TA260 001:245.047 JLINK_IsHalted()
TA260 001:245.521 - 0.473ms returns FALSE
TA260 001:245.528 JLINK_HasError()
TA260 001:247.073 JLINK_IsHalted()
TA260 001:247.577 - 0.504ms returns FALSE
TA260 001:247.584 JLINK_HasError()
TA260 001:249.076 JLINK_IsHalted()
TA260 001:249.556 - 0.480ms returns FALSE
TA260 001:249.562 JLINK_HasError()
TA260 001:251.075 JLINK_IsHalted()
TA260 001:251.568 - 0.493ms returns FALSE
TA260 001:251.574 JLINK_HasError()
TA260 001:253.075 JLINK_IsHalted()
TA260 001:253.528 - 0.452ms returns FALSE
TA260 001:253.537 JLINK_HasError()
TA260 001:255.083 JLINK_IsHalted()
TA260 001:255.565 - 0.481ms returns FALSE
TA260 001:255.600 JLINK_HasError()
TA260 001:257.588 JLINK_IsHalted()
TA260 001:258.040 - 0.452ms returns FALSE
TA260 001:258.055 JLINK_HasError()
TA260 001:259.586 JLINK_IsHalted()
TA260 001:260.074 - 0.488ms returns FALSE
TA260 001:260.080 JLINK_HasError()
TA260 001:261.586 JLINK_IsHalted()
TA260 001:262.069 - 0.482ms returns FALSE
TA260 001:262.074 JLINK_HasError()
TA260 001:263.585 JLINK_IsHalted()
TA260 001:264.044 - 0.459ms returns FALSE
TA260 001:264.049 JLINK_HasError()
TA260 001:266.105 JLINK_IsHalted()
TA260 001:266.654 - 0.548ms returns FALSE
TA260 001:266.668 JLINK_HasError()
TA260 001:268.105 JLINK_IsHalted()
TA260 001:268.667 - 0.561ms returns FALSE
TA260 001:268.678 JLINK_HasError()
TA260 001:270.098 JLINK_IsHalted()
TA260 001:270.656 - 0.557ms returns FALSE
TA260 001:270.661 JLINK_HasError()
TA260 001:272.098 JLINK_IsHalted()
TA260 001:272.658 - 0.560ms returns FALSE
TA260 001:272.665 JLINK_HasError()
TA260 001:274.097 JLINK_IsHalted()
TA260 001:274.725 - 0.628ms returns FALSE
TA260 001:274.731 JLINK_HasError()
TA260 001:276.106 JLINK_IsHalted()
TA260 001:276.651 - 0.544ms returns FALSE
TA260 001:276.665 JLINK_HasError()
TA260 001:278.605 JLINK_IsHalted()
TA260 001:279.083 - 0.477ms returns FALSE
TA260 001:279.088 JLINK_HasError()
TA260 001:280.606 JLINK_IsHalted()
TA260 001:281.090 - 0.484ms returns FALSE
TA260 001:281.096 JLINK_HasError()
TA260 001:282.612 JLINK_IsHalted()
TA260 001:283.127 - 0.515ms returns FALSE
TA260 001:283.134 JLINK_HasError()
TA260 001:284.608 JLINK_IsHalted()
TA260 001:285.156 - 0.547ms returns FALSE
TA260 001:285.168 JLINK_HasError()
TA260 001:287.118 JLINK_IsHalted()
TA260 001:287.657 - 0.539ms returns FALSE
TA260 001:287.664 JLINK_HasError()
TA260 001:289.117 JLINK_IsHalted()
TA260 001:289.583 - 0.466ms returns FALSE
TA260 001:289.590 JLINK_HasError()
TA260 001:291.118 JLINK_IsHalted()
TA260 001:291.657 - 0.539ms returns FALSE
TA260 001:291.665 JLINK_HasError()
TA260 001:293.113 JLINK_IsHalted()
TA260 001:293.657 - 0.543ms returns FALSE
TA260 001:293.662 JLINK_HasError()
TA260 001:295.117 JLINK_IsHalted()
TA260 001:295.558 - 0.441ms returns FALSE
TA260 001:295.564 JLINK_HasError()
TA260 001:296.620 JLINK_IsHalted()
TA260 001:297.126 - 0.505ms returns FALSE
TA260 001:297.134 JLINK_HasError()
TA260 001:298.620 JLINK_IsHalted()
TA260 001:299.092 - 0.471ms returns FALSE
TA260 001:299.097 JLINK_HasError()
TA260 001:300.623 JLINK_IsHalted()
TA260 001:301.094 - 0.471ms returns FALSE
TA260 001:301.102 JLINK_HasError()
TA260 001:302.623 JLINK_IsHalted()
TA260 001:303.116 - 0.493ms returns FALSE
TA260 001:303.126 JLINK_HasError()
TA260 001:304.730 JLINK_IsHalted()
TA260 001:305.276 - 0.545ms returns FALSE
TA260 001:305.291 JLINK_HasError()
TA260 001:307.133 JLINK_IsHalted()
TA260 001:307.651 - 0.518ms returns FALSE
TA260 001:307.658 JLINK_HasError()
TA260 001:309.128 JLINK_IsHalted()
TA260 001:309.648 - 0.519ms returns FALSE
TA260 001:309.654 JLINK_HasError()
TA260 001:311.130 JLINK_IsHalted()
TA260 001:311.647 - 0.516ms returns FALSE
TA260 001:311.653 JLINK_HasError()
TA260 001:313.128 JLINK_IsHalted()
TA260 001:313.656 - 0.528ms returns FALSE
TA260 001:313.662 JLINK_HasError()
TA260 001:315.133 JLINK_IsHalted()
TA260 001:315.653 - 0.519ms returns FALSE
TA260 001:315.665 JLINK_HasError()
TA260 001:317.646 JLINK_IsHalted()
TA260 001:318.151 - 0.505ms returns FALSE
TA260 001:318.159 JLINK_HasError()
TA260 001:319.643 JLINK_IsHalted()
TA260 001:320.126 - 0.482ms returns FALSE
TA260 001:320.132 JLINK_HasError()
TA260 001:321.637 JLINK_IsHalted()
TA260 001:322.114 - 0.476ms returns FALSE
TA260 001:322.119 JLINK_HasError()
TA260 001:323.640 JLINK_IsHalted()
TA260 001:324.100 - 0.460ms returns FALSE
TA260 001:324.106 JLINK_HasError()
TA260 001:326.149 JLINK_IsHalted()
TA260 001:326.655 - 0.505ms returns FALSE
TA260 001:326.669 JLINK_HasError()
TA260 001:328.151 JLINK_IsHalted()
TA260 001:328.661 - 0.510ms returns FALSE
TA260 001:328.679 JLINK_HasError()
TA260 001:330.148 JLINK_IsHalted()
TA260 001:330.647 - 0.499ms returns FALSE
TA260 001:330.652 JLINK_HasError()
TA260 001:332.154 JLINK_IsHalted()
TA260 001:332.658 - 0.504ms returns FALSE
TA260 001:332.664 JLINK_HasError()
TA260 001:334.151 JLINK_IsHalted()
TA260 001:334.752 - 0.600ms returns FALSE
TA260 001:334.765 JLINK_HasError()
TA260 001:336.162 JLINK_IsHalted()
TA260 001:336.689 - 0.526ms returns FALSE
TA260 001:336.701 JLINK_HasError()
TA260 001:338.658 JLINK_IsHalted()
TA260 001:339.194 - 0.535ms returns FALSE
TA260 001:339.200 JLINK_HasError()
TA260 001:340.657 JLINK_IsHalted()
TA260 001:341.124 - 0.467ms returns FALSE
TA260 001:341.130 JLINK_HasError()
TA260 001:342.659 JLINK_IsHalted()
TA260 001:343.161 - 0.502ms returns FALSE
TA260 001:343.169 JLINK_HasError()
TA260 001:344.662 JLINK_IsHalted()
TA260 001:345.137 - 0.474ms returns FALSE
TA260 001:345.159 JLINK_HasError()
TA260 001:347.173 JLINK_IsHalted()
TA260 001:347.661 - 0.488ms returns FALSE
TA260 001:347.676 JLINK_HasError()
TA260 001:349.170 JLINK_IsHalted()
TA260 001:349.661 - 0.490ms returns FALSE
TA260 001:349.674 JLINK_HasError()
TA260 001:351.671 JLINK_IsHalted()
TA260 001:352.127 - 0.455ms returns FALSE
TA260 001:352.133 JLINK_HasError()
TA260 001:353.668 JLINK_IsHalted()
TA260 001:354.148 - 0.479ms returns FALSE
TA260 001:354.154 JLINK_HasError()
TA260 001:356.180 JLINK_IsHalted()
TA260 001:356.653 - 0.472ms returns FALSE
TA260 001:356.667 JLINK_HasError()
TA260 001:358.179 JLINK_IsHalted()
TA260 001:358.695 - 0.516ms returns FALSE
TA260 001:358.701 JLINK_HasError()
TA260 001:360.177 JLINK_IsHalted()
TA260 001:360.647 - 0.470ms returns FALSE
TA260 001:360.653 JLINK_HasError()
TA260 001:362.177 JLINK_IsHalted()
TA260 001:362.656 - 0.478ms returns FALSE
TA260 001:362.662 JLINK_HasError()
TA260 001:364.180 JLINK_IsHalted()
TA260 001:364.720 - 0.539ms returns FALSE
TA260 001:364.734 JLINK_HasError()
TA260 001:366.185 JLINK_IsHalted()
TA260 001:366.695 - 0.510ms returns FALSE
TA260 001:366.704 JLINK_HasError()
TA260 001:368.684 JLINK_IsHalted()
TA260 001:369.194 - 0.510ms returns FALSE
TA260 001:369.201 JLINK_HasError()
TA260 001:370.684 JLINK_IsHalted()
TA260 001:371.172 - 0.488ms returns FALSE
TA260 001:371.180 JLINK_HasError()
TA260 001:372.685 JLINK_IsHalted()
TA260 001:373.157 - 0.472ms returns FALSE
TA260 001:373.162 JLINK_HasError()
TA260 001:374.734 JLINK_IsHalted()
TA260 001:377.069   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:377.666 - 2.931ms returns TRUE
TA260 001:377.680 JLINK_ReadReg(R15 (PC))
TA260 001:377.686 - 0.006ms returns 0x20000000
TA260 001:377.691 JLINK_ClrBPEx(BPHandle = 0x00000005)
TA260 001:377.695 - 0.004ms returns 0x00
TA260 001:377.699 JLINK_ReadReg(R0)
TA260 001:377.703 - 0.003ms returns 0x00000000
TA260 001:378.102 JLINK_HasError()
TA260 001:378.111 JLINK_WriteReg(R0, 0x08008000)
TA260 001:378.116 - 0.005ms returns 0
TA260 001:378.121 JLINK_WriteReg(R1, 0x00004000)
TA260 001:378.124 - 0.003ms returns 0
TA260 001:378.128 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:378.132 - 0.003ms returns 0
TA260 001:378.136 JLINK_WriteReg(R3, 0x00000000)
TA260 001:378.139 - 0.003ms returns 0
TA260 001:378.144 JLINK_WriteReg(R4, 0x00000000)
TA260 001:378.147 - 0.003ms returns 0
TA260 001:378.151 JLINK_WriteReg(R5, 0x00000000)
TA260 001:378.154 - 0.003ms returns 0
TA260 001:378.158 JLINK_WriteReg(R6, 0x00000000)
TA260 001:378.162 - 0.003ms returns 0
TA260 001:378.166 JLINK_WriteReg(R7, 0x00000000)
TA260 001:378.169 - 0.003ms returns 0
TA260 001:378.173 JLINK_WriteReg(R8, 0x00000000)
TA260 001:378.176 - 0.003ms returns 0
TA260 001:378.180 JLINK_WriteReg(R9, 0x20000180)
TA260 001:378.184 - 0.003ms returns 0
TA260 001:378.188 JLINK_WriteReg(R10, 0x00000000)
TA260 001:378.191 - 0.003ms returns 0
TA260 001:378.195 JLINK_WriteReg(R11, 0x00000000)
TA260 001:378.199 - 0.003ms returns 0
TA260 001:378.203 JLINK_WriteReg(R12, 0x00000000)
TA260 001:378.207 - 0.003ms returns 0
TA260 001:378.211 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:378.214 - 0.003ms returns 0
TA260 001:378.218 JLINK_WriteReg(R14, 0x20000001)
TA260 001:378.222 - 0.003ms returns 0
TA260 001:378.226 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 001:378.229 - 0.003ms returns 0
TA260 001:378.234 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:378.237 - 0.003ms returns 0
TA260 001:378.241 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:378.251 - 0.009ms returns 0
TA260 001:378.255 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:378.258 - 0.003ms returns 0
TA260 001:378.262 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:378.265 - 0.003ms returns 0
TA260 001:378.270 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:378.274 - 0.004ms returns 0x00000006
TA260 001:378.279 JLINK_Go()
TA260 001:378.288   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:381.070 - 2.790ms 
TA260 001:381.084 JLINK_IsHalted()
TA260 001:383.408   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:383.887 - 2.803ms returns TRUE
TA260 001:383.897 JLINK_ReadReg(R15 (PC))
TA260 001:383.902 - 0.005ms returns 0x20000000
TA260 001:383.933 JLINK_ClrBPEx(BPHandle = 0x00000006)
TA260 001:383.939 - 0.005ms returns 0x00
TA260 001:383.943 JLINK_ReadReg(R0)
TA260 001:383.947 - 0.003ms returns 0x00000001
TA260 001:383.951 JLINK_HasError()
TA260 001:383.955 JLINK_WriteReg(R0, 0x08008000)
TA260 001:383.959 - 0.003ms returns 0
TA260 001:383.964 JLINK_WriteReg(R1, 0x00004000)
TA260 001:383.967 - 0.003ms returns 0
TA260 001:383.971 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:383.974 - 0.003ms returns 0
TA260 001:383.978 JLINK_WriteReg(R3, 0x00000000)
TA260 001:383.982 - 0.003ms returns 0
TA260 001:383.986 JLINK_WriteReg(R4, 0x00000000)
TA260 001:383.989 - 0.003ms returns 0
TA260 001:383.993 JLINK_WriteReg(R5, 0x00000000)
TA260 001:383.997 - 0.003ms returns 0
TA260 001:384.001 JLINK_WriteReg(R6, 0x00000000)
TA260 001:384.004 - 0.003ms returns 0
TA260 001:384.008 JLINK_WriteReg(R7, 0x00000000)
TA260 001:384.011 - 0.003ms returns 0
TA260 001:384.015 JLINK_WriteReg(R8, 0x00000000)
TA260 001:384.019 - 0.003ms returns 0
TA260 001:384.023 JLINK_WriteReg(R9, 0x20000180)
TA260 001:384.026 - 0.003ms returns 0
TA260 001:384.030 JLINK_WriteReg(R10, 0x00000000)
TA260 001:384.034 - 0.003ms returns 0
TA260 001:384.038 JLINK_WriteReg(R11, 0x00000000)
TA260 001:384.041 - 0.003ms returns 0
TA260 001:384.045 JLINK_WriteReg(R12, 0x00000000)
TA260 001:384.048 - 0.003ms returns 0
TA260 001:384.053 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:384.056 - 0.003ms returns 0
TA260 001:384.060 JLINK_WriteReg(R14, 0x20000001)
TA260 001:384.064 - 0.003ms returns 0
TA260 001:384.068 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 001:384.071 - 0.003ms returns 0
TA260 001:384.075 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:384.079 - 0.003ms returns 0
TA260 001:384.083 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:384.086 - 0.003ms returns 0
TA260 001:384.090 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:384.093 - 0.003ms returns 0
TA260 001:384.097 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:384.101 - 0.003ms returns 0
TA260 001:384.105 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:384.109 - 0.003ms returns 0x00000007
TA260 001:384.113 JLINK_Go()
TA260 001:384.119   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:386.880 - 2.766ms 
TA260 001:386.901 JLINK_IsHalted()
TA260 001:387.340 - 0.439ms returns FALSE
TA260 001:387.347 JLINK_HasError()
TA260 001:389.708 JLINK_IsHalted()
TA260 001:390.159 - 0.451ms returns FALSE
TA260 001:390.166 JLINK_HasError()
TA260 001:391.703 JLINK_IsHalted()
TA260 001:392.212 - 0.508ms returns FALSE
TA260 001:392.218 JLINK_HasError()
TA260 001:393.702 JLINK_IsHalted()
TA260 001:394.170 - 0.468ms returns FALSE
TA260 001:394.176 JLINK_HasError()
TA260 001:396.217 JLINK_IsHalted()
TA260 001:396.802 - 0.584ms returns FALSE
TA260 001:396.817 JLINK_HasError()
TA260 001:398.215 JLINK_IsHalted()
TA260 001:398.695 - 0.479ms returns FALSE
TA260 001:398.701 JLINK_HasError()
TA260 001:400.211 JLINK_IsHalted()
TA260 001:400.679 - 0.468ms returns FALSE
TA260 001:400.684 JLINK_HasError()
TA260 001:402.212 JLINK_IsHalted()
TA260 001:402.692 - 0.480ms returns FALSE
TA260 001:402.698 JLINK_HasError()
TA260 001:404.210 JLINK_IsHalted()
TA260 001:404.699 - 0.488ms returns FALSE
TA260 001:404.713 JLINK_HasError()
TA260 001:406.223 JLINK_IsHalted()
TA260 001:406.799 - 0.576ms returns FALSE
TA260 001:406.813 JLINK_HasError()
TA260 001:408.724 JLINK_IsHalted()
TA260 001:409.218 - 0.493ms returns FALSE
TA260 001:409.236 JLINK_HasError()
TA260 001:410.722 JLINK_IsHalted()
TA260 001:411.208 - 0.485ms returns FALSE
TA260 001:411.221 JLINK_HasError()
TA260 001:414.755 JLINK_IsHalted()
TA260 001:415.237 - 0.482ms returns FALSE
TA260 001:415.251 JLINK_HasError()
TA260 001:417.233 JLINK_IsHalted()
TA260 001:417.830 - 0.596ms returns FALSE
TA260 001:417.846 JLINK_HasError()
TA260 001:419.231 JLINK_IsHalted()
TA260 001:419.784 - 0.552ms returns FALSE
TA260 001:419.790 JLINK_HasError()
TA260 001:421.231 JLINK_IsHalted()
TA260 001:421.727 - 0.496ms returns FALSE
TA260 001:421.733 JLINK_HasError()
TA260 001:423.231 JLINK_IsHalted()
TA260 001:423.739 - 0.507ms returns FALSE
TA260 001:423.744 JLINK_HasError()
TA260 001:425.232 JLINK_IsHalted()
TA260 001:425.787 - 0.554ms returns FALSE
TA260 001:425.801 JLINK_HasError()
TA260 001:427.742 JLINK_IsHalted()
TA260 001:428.209 - 0.467ms returns FALSE
TA260 001:428.224 JLINK_HasError()
TA260 001:430.737 JLINK_IsHalted()
TA260 001:431.221 - 0.483ms returns FALSE
TA260 001:431.233 JLINK_HasError()
TA260 001:432.738 JLINK_IsHalted()
TA260 001:433.238 - 0.499ms returns FALSE
TA260 001:433.243 JLINK_HasError()
TA260 001:434.741 JLINK_IsHalted()
TA260 001:435.221 - 0.479ms returns FALSE
TA260 001:435.229 JLINK_HasError()
TA260 001:437.255 JLINK_IsHalted()
TA260 001:437.699 - 0.443ms returns FALSE
TA260 001:437.718 JLINK_HasError()
TA260 001:439.254 JLINK_IsHalted()
TA260 001:439.789 - 0.535ms returns FALSE
TA260 001:439.796 JLINK_HasError()
TA260 001:441.251 JLINK_IsHalted()
TA260 001:441.728 - 0.477ms returns FALSE
TA260 001:441.736 JLINK_HasError()
TA260 001:443.249 JLINK_IsHalted()
TA260 001:443.727 - 0.477ms returns FALSE
TA260 001:443.732 JLINK_HasError()
TA260 001:445.254 JLINK_IsHalted()
TA260 001:445.743 - 0.488ms returns FALSE
TA260 001:445.749 JLINK_HasError()
TA260 001:447.770 JLINK_IsHalted()
TA260 001:448.239 - 0.469ms returns FALSE
TA260 001:448.245 JLINK_HasError()
TA260 001:450.263 JLINK_IsHalted()
TA260 001:450.797 - 0.533ms returns FALSE
TA260 001:450.803 JLINK_HasError()
TA260 001:452.265 JLINK_IsHalted()
TA260 001:452.782 - 0.517ms returns FALSE
TA260 001:452.788 JLINK_HasError()
TA260 001:454.262 JLINK_IsHalted()
TA260 001:454.744 - 0.481ms returns FALSE
TA260 001:454.757 JLINK_HasError()
TA260 001:456.272 JLINK_IsHalted()
TA260 001:456.791 - 0.519ms returns FALSE
TA260 001:456.806 JLINK_HasError()
TA260 001:458.776 JLINK_IsHalted()
TA260 001:459.261 - 0.484ms returns FALSE
TA260 001:459.271 JLINK_HasError()
TA260 001:460.774 JLINK_IsHalted()
TA260 001:461.241 - 0.467ms returns FALSE
TA260 001:461.249 JLINK_HasError()
TA260 001:462.774 JLINK_IsHalted()
TA260 001:463.237 - 0.462ms returns FALSE
TA260 001:463.243 JLINK_HasError()
TA260 001:464.780 JLINK_IsHalted()
TA260 001:465.245 - 0.464ms returns FALSE
TA260 001:465.259 JLINK_HasError()
TA260 001:467.287 JLINK_IsHalted()
TA260 001:467.798 - 0.510ms returns FALSE
TA260 001:467.805 JLINK_HasError()
TA260 001:469.286 JLINK_IsHalted()
TA260 001:469.798 - 0.511ms returns FALSE
TA260 001:469.804 JLINK_HasError()
TA260 001:471.287 JLINK_IsHalted()
TA260 001:471.786 - 0.498ms returns FALSE
TA260 001:471.793 JLINK_HasError()
TA260 001:473.117 JLINK_IsHalted()
TA260 001:473.647 - 0.530ms returns FALSE
TA260 001:473.653 JLINK_HasError()
TA260 001:475.120 JLINK_IsHalted()
TA260 001:475.565 - 0.444ms returns FALSE
TA260 001:475.578 JLINK_HasError()
TA260 001:477.194 JLINK_IsHalted()
TA260 001:477.701 - 0.506ms returns FALSE
TA260 001:477.715 JLINK_HasError()
TA260 001:479.186 JLINK_IsHalted()
TA260 001:479.656 - 0.470ms returns FALSE
TA260 001:479.662 JLINK_HasError()
TA260 001:481.185 JLINK_IsHalted()
TA260 001:481.678 - 0.492ms returns FALSE
TA260 001:481.684 JLINK_HasError()
TA260 001:483.184 JLINK_IsHalted()
TA260 001:483.693 - 0.508ms returns FALSE
TA260 001:483.699 JLINK_HasError()
TA260 001:485.197 JLINK_IsHalted()
TA260 001:485.805 - 0.606ms returns FALSE
TA260 001:485.822 JLINK_HasError()
TA260 001:487.335 JLINK_IsHalted()
TA260 001:487.801 - 0.465ms returns FALSE
TA260 001:487.808 JLINK_HasError()
TA260 001:489.840 JLINK_IsHalted()
TA260 001:490.366 - 0.525ms returns FALSE
TA260 001:490.373 JLINK_HasError()
TA260 001:491.835 JLINK_IsHalted()
TA260 001:492.274 - 0.439ms returns FALSE
TA260 001:492.281 JLINK_HasError()
TA260 001:493.865 JLINK_IsHalted()
TA260 001:494.366 - 0.500ms returns FALSE
TA260 001:494.373 JLINK_HasError()
TA260 001:496.068 JLINK_IsHalted()
TA260 001:496.546 - 0.477ms returns FALSE
TA260 001:496.565 JLINK_HasError()
TA260 001:498.574 JLINK_IsHalted()
TA260 001:499.038 - 0.463ms returns FALSE
TA260 001:499.044 JLINK_HasError()
TA260 001:500.569 JLINK_IsHalted()
TA260 001:501.070 - 0.501ms returns FALSE
TA260 001:501.078 JLINK_HasError()
TA260 001:502.572 JLINK_IsHalted()
TA260 001:503.039 - 0.466ms returns FALSE
TA260 001:503.050 JLINK_HasError()
TA260 001:504.569 JLINK_IsHalted()
TA260 001:505.047 - 0.476ms returns FALSE
TA260 001:505.069 JLINK_HasError()
TA260 001:507.080 JLINK_IsHalted()
TA260 001:507.538 - 0.457ms returns FALSE
TA260 001:507.546 JLINK_HasError()
TA260 001:509.075 JLINK_IsHalted()
TA260 001:509.658 - 0.582ms returns FALSE
TA260 001:509.665 JLINK_HasError()
TA260 001:511.075 JLINK_IsHalted()
TA260 001:511.568 - 0.493ms returns FALSE
TA260 001:511.573 JLINK_HasError()
TA260 001:513.073 JLINK_IsHalted()
TA260 001:513.532 - 0.459ms returns FALSE
TA260 001:513.538 JLINK_HasError()
TA260 001:515.073 JLINK_IsHalted()
TA260 001:515.528 - 0.453ms returns FALSE
TA260 001:515.539 JLINK_HasError()
TA260 001:516.578 JLINK_IsHalted()
TA260 001:517.091 - 0.513ms returns FALSE
TA260 001:517.098 JLINK_HasError()
TA260 001:518.589 JLINK_IsHalted()
TA260 001:519.041 - 0.451ms returns FALSE
TA260 001:519.057 JLINK_HasError()
TA260 001:520.587 JLINK_IsHalted()
TA260 001:521.069 - 0.482ms returns FALSE
TA260 001:521.075 JLINK_HasError()
TA260 001:522.583 JLINK_IsHalted()
TA260 001:523.047 - 0.463ms returns FALSE
TA260 001:523.054 JLINK_HasError()
TA260 001:524.525 JLINK_IsHalted()
TA260 001:525.073 - 0.547ms returns FALSE
TA260 001:525.086 JLINK_HasError()
TA260 001:527.032 JLINK_IsHalted()
TA260 001:527.475 - 0.443ms returns FALSE
TA260 001:527.481 JLINK_HasError()
TA260 001:529.034 JLINK_IsHalted()
TA260 001:529.547 - 0.512ms returns FALSE
TA260 001:529.553 JLINK_HasError()
TA260 001:531.031 JLINK_IsHalted()
TA260 001:531.511 - 0.480ms returns FALSE
TA260 001:531.518 JLINK_HasError()
TA260 001:533.032 JLINK_IsHalted()
TA260 001:533.523 - 0.491ms returns FALSE
TA260 001:533.529 JLINK_HasError()
TA260 001:535.033 JLINK_IsHalted()
TA260 001:535.529 - 0.495ms returns FALSE
TA260 001:535.546 JLINK_HasError()
TA260 001:537.544 JLINK_IsHalted()
TA260 001:538.025 - 0.481ms returns FALSE
TA260 001:538.032 JLINK_HasError()
TA260 001:539.546 JLINK_IsHalted()
TA260 001:540.047 - 0.500ms returns FALSE
TA260 001:540.053 JLINK_HasError()
TA260 001:541.541 JLINK_IsHalted()
TA260 001:541.977 - 0.435ms returns FALSE
TA260 001:541.983 JLINK_HasError()
TA260 001:543.539 JLINK_IsHalted()
TA260 001:544.046 - 0.507ms returns FALSE
TA260 001:544.052 JLINK_HasError()
TA260 001:546.050 JLINK_IsHalted()
TA260 001:546.515 - 0.464ms returns FALSE
TA260 001:546.530 JLINK_HasError()
TA260 001:548.051 JLINK_IsHalted()
TA260 001:548.569 - 0.518ms returns FALSE
TA260 001:548.575 JLINK_HasError()
TA260 001:550.549 JLINK_IsHalted()
TA260 001:551.047 - 0.497ms returns FALSE
TA260 001:551.052 JLINK_HasError()
TA260 001:552.549 JLINK_IsHalted()
TA260 001:553.032 - 0.482ms returns FALSE
TA260 001:553.038 JLINK_HasError()
TA260 001:554.550 JLINK_IsHalted()
TA260 001:555.074 - 0.523ms returns FALSE
TA260 001:555.091 JLINK_HasError()
TA260 001:557.059 JLINK_IsHalted()
TA260 001:557.516 - 0.457ms returns FALSE
TA260 001:557.530 JLINK_HasError()
TA260 001:559.056 JLINK_IsHalted()
TA260 001:559.545 - 0.489ms returns FALSE
TA260 001:559.551 JLINK_HasError()
TA260 001:561.059 JLINK_IsHalted()
TA260 001:561.571 - 0.511ms returns FALSE
TA260 001:561.584 JLINK_HasError()
TA260 001:563.057 JLINK_IsHalted()
TA260 001:563.556 - 0.499ms returns FALSE
TA260 001:563.562 JLINK_HasError()
TA260 001:565.061 JLINK_IsHalted()
TA260 001:565.533 - 0.471ms returns FALSE
TA260 001:565.548 JLINK_HasError()
TA260 001:567.573 JLINK_IsHalted()
TA260 001:568.037 - 0.463ms returns FALSE
TA260 001:568.051 JLINK_HasError()
TA260 001:570.011 JLINK_IsHalted()
TA260 001:570.480 - 0.469ms returns FALSE
TA260 001:570.487 JLINK_HasError()
TA260 001:572.008 JLINK_IsHalted()
TA260 001:572.492 - 0.484ms returns FALSE
TA260 001:572.501 JLINK_HasError()
TA260 001:574.009 JLINK_IsHalted()
TA260 001:574.510 - 0.500ms returns FALSE
TA260 001:574.516 JLINK_HasError()
TA260 001:576.017 JLINK_IsHalted()
TA260 001:576.535 - 0.517ms returns FALSE
TA260 001:576.543 JLINK_HasError()
TA260 001:578.524 JLINK_IsHalted()
TA260 001:578.979 - 0.454ms returns FALSE
TA260 001:578.985 JLINK_HasError()
TA260 001:580.519 JLINK_IsHalted()
TA260 001:580.988 - 0.468ms returns FALSE
TA260 001:580.996 JLINK_HasError()
TA260 001:582.521 JLINK_IsHalted()
TA260 001:583.023 - 0.501ms returns FALSE
TA260 001:583.028 JLINK_HasError()
TA260 001:584.520 JLINK_IsHalted()
TA260 001:584.979 - 0.458ms returns FALSE
TA260 001:584.990 JLINK_HasError()
TA260 001:586.031 JLINK_IsHalted()
TA260 001:586.551 - 0.519ms returns FALSE
TA260 001:586.565 JLINK_HasError()
TA260 001:588.035 JLINK_IsHalted()
TA260 001:588.525 - 0.490ms returns FALSE
TA260 001:588.531 JLINK_HasError()
TA260 001:590.028 JLINK_IsHalted()
TA260 001:590.544 - 0.516ms returns FALSE
TA260 001:590.550 JLINK_HasError()
TA260 001:592.035 JLINK_IsHalted()
TA260 001:592.547 - 0.511ms returns FALSE
TA260 001:592.555 JLINK_HasError()
TA260 001:594.027 JLINK_IsHalted()
TA260 001:594.500 - 0.473ms returns FALSE
TA260 001:594.506 JLINK_HasError()
TA260 001:596.041 JLINK_IsHalted()
TA260 001:596.558 - 0.517ms returns FALSE
TA260 001:596.573 JLINK_HasError()
TA260 001:598.537 JLINK_IsHalted()
TA260 001:599.035 - 0.498ms returns FALSE
TA260 001:599.041 JLINK_HasError()
TA260 001:600.534 JLINK_IsHalted()
TA260 001:600.989 - 0.454ms returns FALSE
TA260 001:600.994 JLINK_HasError()
TA260 001:602.536 JLINK_IsHalted()
TA260 001:603.073 - 0.536ms returns FALSE
TA260 001:603.079 JLINK_HasError()
TA260 001:604.536 JLINK_IsHalted()
TA260 001:605.037 - 0.501ms returns FALSE
TA260 001:605.051 JLINK_HasError()
TA260 001:607.045 JLINK_IsHalted()
TA260 001:607.525 - 0.479ms returns FALSE
TA260 001:607.531 JLINK_HasError()
TA260 001:609.045 JLINK_IsHalted()
TA260 001:609.560 - 0.514ms returns FALSE
TA260 001:609.566 JLINK_HasError()
TA260 001:611.044 JLINK_IsHalted()
TA260 001:611.526 - 0.482ms returns FALSE
TA260 001:611.535 JLINK_HasError()
TA260 001:613.049 JLINK_IsHalted()
TA260 001:613.514 - 0.464ms returns FALSE
TA260 001:613.524 JLINK_HasError()
TA260 001:615.045 JLINK_IsHalted()
TA260 001:615.512 - 0.467ms returns FALSE
TA260 001:615.522 JLINK_HasError()
TA260 001:617.557 JLINK_IsHalted()
TA260 001:618.018 - 0.461ms returns FALSE
TA260 001:618.025 JLINK_HasError()
TA260 001:619.555 JLINK_IsHalted()
TA260 001:620.034 - 0.479ms returns FALSE
TA260 001:620.040 JLINK_HasError()
TA260 001:621.554 JLINK_IsHalted()
TA260 001:622.032 - 0.478ms returns FALSE
TA260 001:622.037 JLINK_HasError()
TA260 001:623.559 JLINK_IsHalted()
TA260 001:624.079 - 0.520ms returns FALSE
TA260 001:624.086 JLINK_HasError()
TA260 001:626.061 JLINK_IsHalted()
TA260 001:626.516 - 0.454ms returns FALSE
TA260 001:626.530 JLINK_HasError()
TA260 001:628.066 JLINK_IsHalted()
TA260 001:628.529 - 0.462ms returns FALSE
TA260 001:628.538 JLINK_HasError()
TA260 001:630.062 JLINK_IsHalted()
TA260 001:630.568 - 0.506ms returns FALSE
TA260 001:630.577 JLINK_HasError()
TA260 001:632.063 JLINK_IsHalted()
TA260 001:632.569 - 0.505ms returns FALSE
TA260 001:632.578 JLINK_HasError()
TA260 001:634.067 JLINK_IsHalted()
TA260 001:634.549 - 0.481ms returns FALSE
TA260 001:634.559 JLINK_HasError()
TA260 001:636.069 JLINK_IsHalted()
TA260 001:636.555 - 0.485ms returns FALSE
TA260 001:636.570 JLINK_HasError()
TA260 001:638.573 JLINK_IsHalted()
TA260 001:639.081 - 0.507ms returns FALSE
TA260 001:639.087 JLINK_HasError()
TA260 001:640.569 JLINK_IsHalted()
TA260 001:641.070 - 0.501ms returns FALSE
TA260 001:641.075 JLINK_HasError()
TA260 001:642.570 JLINK_IsHalted()
TA260 001:643.069 - 0.498ms returns FALSE
TA260 001:643.075 JLINK_HasError()
TA260 001:644.579 JLINK_IsHalted()
TA260 001:645.187 - 0.607ms returns FALSE
TA260 001:645.197 JLINK_HasError()
TA260 001:647.083 JLINK_IsHalted()
TA260 001:647.561 - 0.477ms returns FALSE
TA260 001:647.574 JLINK_HasError()
TA260 001:649.582 JLINK_IsHalted()
TA260 001:650.068 - 0.485ms returns FALSE
TA260 001:650.073 JLINK_HasError()
TA260 001:651.585 JLINK_IsHalted()
TA260 001:652.093 - 0.508ms returns FALSE
TA260 001:652.100 JLINK_HasError()
TA260 001:653.582 JLINK_IsHalted()
TA260 001:654.075 - 0.493ms returns FALSE
TA260 001:654.081 JLINK_HasError()
TA260 001:656.092 JLINK_IsHalted()
TA260 001:656.661 - 0.568ms returns FALSE
TA260 001:656.672 JLINK_HasError()
TA260 001:658.092 JLINK_IsHalted()
TA260 001:658.649 - 0.557ms returns FALSE
TA260 001:658.655 JLINK_HasError()
TA260 001:660.099 JLINK_IsHalted()
TA260 001:660.567 - 0.467ms returns FALSE
TA260 001:660.579 JLINK_HasError()
TA260 001:662.091 JLINK_IsHalted()
TA260 001:662.656 - 0.565ms returns FALSE
TA260 001:662.662 JLINK_HasError()
TA260 001:664.089 JLINK_IsHalted()
TA260 001:664.568 - 0.478ms returns FALSE
TA260 001:664.573 JLINK_HasError()
TA260 001:666.098 JLINK_IsHalted()
TA260 001:666.672 - 0.573ms returns FALSE
TA260 001:666.684 JLINK_HasError()
TA260 001:668.599 JLINK_IsHalted()
TA260 001:669.096 - 0.495ms returns FALSE
TA260 001:669.109 JLINK_HasError()
TA260 001:670.595 JLINK_IsHalted()
TA260 001:671.093 - 0.497ms returns FALSE
TA260 001:671.098 JLINK_HasError()
TA260 001:672.597 JLINK_IsHalted()
TA260 001:673.080 - 0.483ms returns FALSE
TA260 001:673.087 JLINK_HasError()
TA260 001:674.596 JLINK_IsHalted()
TA260 001:675.060 - 0.463ms returns FALSE
TA260 001:675.073 JLINK_HasError()
TA260 001:677.110 JLINK_IsHalted()
TA260 001:677.662 - 0.552ms returns FALSE
TA260 001:677.678 JLINK_HasError()
TA260 001:679.104 JLINK_IsHalted()
TA260 001:679.556 - 0.452ms returns FALSE
TA260 001:679.562 JLINK_HasError()
TA260 001:681.106 JLINK_IsHalted()
TA260 001:681.650 - 0.543ms returns FALSE
TA260 001:681.656 JLINK_HasError()
TA260 001:683.103 JLINK_IsHalted()
TA260 001:683.566 - 0.462ms returns FALSE
TA260 001:683.572 JLINK_HasError()
TA260 001:685.108 JLINK_IsHalted()
TA260 001:685.553 - 0.444ms returns FALSE
TA260 001:685.574 JLINK_HasError()
TA260 001:687.616 JLINK_IsHalted()
TA260 001:688.128 - 0.512ms returns FALSE
TA260 001:688.135 JLINK_HasError()
TA260 001:689.620 JLINK_IsHalted()
TA260 001:690.156 - 0.535ms returns FALSE
TA260 001:690.167 JLINK_HasError()
TA260 001:691.615 JLINK_IsHalted()
TA260 001:692.116 - 0.501ms returns FALSE
TA260 001:692.122 JLINK_HasError()
TA260 001:694.726 JLINK_IsHalted()
TA260 001:695.208 - 0.481ms returns FALSE
TA260 001:695.220 JLINK_HasError()
TA260 001:697.123 JLINK_IsHalted()
TA260 001:697.649 - 0.525ms returns FALSE
TA260 001:697.660 JLINK_HasError()
TA260 001:699.122 JLINK_IsHalted()
TA260 001:699.650 - 0.527ms returns FALSE
TA260 001:699.656 JLINK_HasError()
TA260 001:701.122 JLINK_IsHalted()
TA260 001:701.656 - 0.534ms returns FALSE
TA260 001:701.662 JLINK_HasError()
TA260 001:703.118 JLINK_IsHalted()
TA260 001:703.657 - 0.538ms returns FALSE
TA260 001:703.663 JLINK_HasError()
TA260 001:705.123 JLINK_IsHalted()
TA260 001:705.660 - 0.536ms returns FALSE
TA260 001:705.668 JLINK_HasError()
TA260 001:707.640 JLINK_IsHalted()
TA260 001:708.105 - 0.464ms returns FALSE
TA260 001:708.119 JLINK_HasError()
TA260 001:709.630 JLINK_IsHalted()
TA260 001:710.120 - 0.489ms returns FALSE
TA260 001:710.134 JLINK_HasError()
TA260 001:711.632 JLINK_IsHalted()
TA260 001:712.137 - 0.505ms returns FALSE
TA260 001:712.144 JLINK_HasError()
TA260 001:713.632 JLINK_IsHalted()
TA260 001:714.116 - 0.483ms returns FALSE
TA260 001:714.125 JLINK_HasError()
TA260 001:716.146 JLINK_IsHalted()
TA260 001:716.653 - 0.507ms returns FALSE
TA260 001:716.667 JLINK_HasError()
TA260 001:718.146 JLINK_IsHalted()
TA260 001:718.659 - 0.512ms returns FALSE
TA260 001:718.672 JLINK_HasError()
TA260 001:720.144 JLINK_IsHalted()
TA260 001:720.648 - 0.503ms returns FALSE
TA260 001:720.653 JLINK_HasError()
TA260 001:722.146 JLINK_IsHalted()
TA260 001:722.657 - 0.510ms returns FALSE
TA260 001:722.663 JLINK_HasError()
TA260 001:724.143 JLINK_IsHalted()
TA260 001:724.709 - 0.566ms returns FALSE
TA260 001:724.715 JLINK_HasError()
TA260 001:726.152 JLINK_IsHalted()
TA260 001:726.658 - 0.506ms returns FALSE
TA260 001:726.673 JLINK_HasError()
TA260 001:728.652 JLINK_IsHalted()
TA260 001:729.171 - 0.518ms returns FALSE
TA260 001:729.178 JLINK_HasError()
TA260 001:730.649 JLINK_IsHalted()
TA260 001:731.152 - 0.502ms returns FALSE
TA260 001:731.160 JLINK_HasError()
TA260 001:732.650 JLINK_IsHalted()
TA260 001:733.168 - 0.517ms returns FALSE
TA260 001:733.173 JLINK_HasError()
TA260 001:734.666 JLINK_IsHalted()
TA260 001:735.258 - 0.591ms returns FALSE
TA260 001:735.273 JLINK_HasError()
TA260 001:737.168 JLINK_IsHalted()
TA260 001:737.683 - 0.515ms returns FALSE
TA260 001:737.690 JLINK_HasError()
TA260 001:739.161 JLINK_IsHalted()
TA260 001:739.659 - 0.498ms returns FALSE
TA260 001:739.669 JLINK_HasError()
TA260 001:741.160 JLINK_IsHalted()
TA260 001:741.656 - 0.496ms returns FALSE
TA260 001:741.662 JLINK_HasError()
TA260 001:743.159 JLINK_IsHalted()
TA260 001:743.646 - 0.487ms returns FALSE
TA260 001:743.652 JLINK_HasError()
TA260 001:745.164 JLINK_IsHalted()
TA260 001:745.661 - 0.496ms returns FALSE
TA260 001:745.674 JLINK_HasError()
TA260 001:747.672 JLINK_IsHalted()
TA260 001:748.160 - 0.487ms returns FALSE
TA260 001:748.173 JLINK_HasError()
TA260 001:749.670 JLINK_IsHalted()
TA260 001:750.218 - 0.547ms returns FALSE
TA260 001:750.225 JLINK_HasError()
TA260 001:752.173 JLINK_IsHalted()
TA260 001:752.651 - 0.477ms returns FALSE
TA260 001:752.657 JLINK_HasError()
TA260 001:755.180 JLINK_IsHalted()
TA260 001:755.672 - 0.491ms returns FALSE
TA260 001:755.685 JLINK_HasError()
TA260 001:757.681 JLINK_IsHalted()
TA260 001:758.172 - 0.490ms returns FALSE
TA260 001:758.178 JLINK_HasError()
TA260 001:759.677 JLINK_IsHalted()
TA260 001:760.157 - 0.480ms returns FALSE
TA260 001:760.162 JLINK_HasError()
TA260 001:761.677 JLINK_IsHalted()
TA260 001:762.148 - 0.470ms returns FALSE
TA260 001:762.154 JLINK_HasError()
TA260 001:763.676 JLINK_IsHalted()
TA260 001:764.149 - 0.472ms returns FALSE
TA260 001:764.155 JLINK_HasError()
TA260 001:766.194 JLINK_IsHalted()
TA260 001:766.669 - 0.476ms returns FALSE
TA260 001:766.683 JLINK_HasError()
TA260 001:768.190 JLINK_IsHalted()
TA260 001:768.662 - 0.471ms returns FALSE
TA260 001:768.677 JLINK_HasError()
TA260 001:770.189 JLINK_IsHalted()
TA260 001:770.696 - 0.507ms returns FALSE
TA260 001:770.702 JLINK_HasError()
TA260 001:772.187 JLINK_IsHalted()
TA260 001:772.693 - 0.506ms returns FALSE
TA260 001:772.699 JLINK_HasError()
TA260 001:774.185 JLINK_IsHalted()
TA260 001:776.577   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:777.076 - 2.890ms returns TRUE
TA260 001:777.091 JLINK_ReadReg(R15 (PC))
TA260 001:777.097 - 0.006ms returns 0x20000000
TA260 001:777.102 JLINK_ClrBPEx(BPHandle = 0x00000007)
TA260 001:777.106 - 0.004ms returns 0x00
TA260 001:777.110 JLINK_ReadReg(R0)
TA260 001:777.114 - 0.003ms returns 0x00000000
TA260 001:777.501 JLINK_HasError()
TA260 001:777.511 JLINK_WriteReg(R0, 0x0800C000)
TA260 001:777.515 - 0.005ms returns 0
TA260 001:777.520 JLINK_WriteReg(R1, 0x00004000)
TA260 001:777.524 - 0.004ms returns 0
TA260 001:777.528 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:777.532 - 0.003ms returns 0
TA260 001:777.536 JLINK_WriteReg(R3, 0x00000000)
TA260 001:777.539 - 0.003ms returns 0
TA260 001:777.549 JLINK_WriteReg(R4, 0x00000000)
TA260 001:777.553 - 0.003ms returns 0
TA260 001:777.557 JLINK_WriteReg(R5, 0x00000000)
TA260 001:777.561 - 0.003ms returns 0
TA260 001:777.565 JLINK_WriteReg(R6, 0x00000000)
TA260 001:777.568 - 0.003ms returns 0
TA260 001:777.572 JLINK_WriteReg(R7, 0x00000000)
TA260 001:777.575 - 0.003ms returns 0
TA260 001:777.580 JLINK_WriteReg(R8, 0x00000000)
TA260 001:777.583 - 0.003ms returns 0
TA260 001:777.587 JLINK_WriteReg(R9, 0x20000180)
TA260 001:777.590 - 0.003ms returns 0
TA260 001:777.594 JLINK_WriteReg(R10, 0x00000000)
TA260 001:777.598 - 0.003ms returns 0
TA260 001:777.602 JLINK_WriteReg(R11, 0x00000000)
TA260 001:777.605 - 0.003ms returns 0
TA260 001:777.609 JLINK_WriteReg(R12, 0x00000000)
TA260 001:777.612 - 0.003ms returns 0
TA260 001:777.617 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:777.620 - 0.003ms returns 0
TA260 001:777.625 JLINK_WriteReg(R14, 0x20000001)
TA260 001:777.628 - 0.003ms returns 0
TA260 001:777.632 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 001:777.636 - 0.003ms returns 0
TA260 001:777.640 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:777.643 - 0.003ms returns 0
TA260 001:777.647 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:777.650 - 0.003ms returns 0
TA260 001:777.654 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:777.658 - 0.003ms returns 0
TA260 001:777.662 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:777.665 - 0.003ms returns 0
TA260 001:777.670 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:777.674 - 0.004ms returns 0x00000008
TA260 001:777.678 JLINK_Go()
TA260 001:777.698   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:780.376 - 2.698ms 
TA260 001:780.390 JLINK_IsHalted()
TA260 001:782.749   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:783.233 - 2.843ms returns TRUE
TA260 001:783.244 JLINK_ReadReg(R15 (PC))
TA260 001:783.249 - 0.004ms returns 0x20000000
TA260 001:783.253 JLINK_ClrBPEx(BPHandle = 0x00000008)
TA260 001:783.257 - 0.003ms returns 0x00
TA260 001:783.262 JLINK_ReadReg(R0)
TA260 001:783.265 - 0.003ms returns 0x00000001
TA260 001:783.270 JLINK_HasError()
TA260 001:783.274 JLINK_WriteReg(R0, 0x0800C000)
TA260 001:783.278 - 0.004ms returns 0
TA260 001:783.282 JLINK_WriteReg(R1, 0x00004000)
TA260 001:783.286 - 0.003ms returns 0
TA260 001:783.290 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:783.293 - 0.003ms returns 0
TA260 001:783.297 JLINK_WriteReg(R3, 0x00000000)
TA260 001:783.300 - 0.003ms returns 0
TA260 001:783.305 JLINK_WriteReg(R4, 0x00000000)
TA260 001:783.308 - 0.003ms returns 0
TA260 001:783.312 JLINK_WriteReg(R5, 0x00000000)
TA260 001:783.315 - 0.003ms returns 0
TA260 001:783.319 JLINK_WriteReg(R6, 0x00000000)
TA260 001:783.323 - 0.003ms returns 0
TA260 001:783.327 JLINK_WriteReg(R7, 0x00000000)
TA260 001:783.330 - 0.003ms returns 0
TA260 001:783.335 JLINK_WriteReg(R8, 0x00000000)
TA260 001:783.338 - 0.003ms returns 0
TA260 001:783.342 JLINK_WriteReg(R9, 0x20000180)
TA260 001:783.345 - 0.003ms returns 0
TA260 001:783.349 JLINK_WriteReg(R10, 0x00000000)
TA260 001:783.353 - 0.003ms returns 0
TA260 001:783.357 JLINK_WriteReg(R11, 0x00000000)
TA260 001:783.360 - 0.003ms returns 0
TA260 001:783.364 JLINK_WriteReg(R12, 0x00000000)
TA260 001:783.368 - 0.003ms returns 0
TA260 001:783.377 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:783.381 - 0.004ms returns 0
TA260 001:783.385 JLINK_WriteReg(R14, 0x20000001)
TA260 001:783.388 - 0.003ms returns 0
TA260 001:783.392 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 001:783.396 - 0.003ms returns 0
TA260 001:783.400 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:783.404 - 0.003ms returns 0
TA260 001:783.408 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:783.411 - 0.003ms returns 0
TA260 001:783.415 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:783.418 - 0.003ms returns 0
TA260 001:783.423 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:783.426 - 0.003ms returns 0
TA260 001:783.430 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:783.434 - 0.004ms returns 0x00000009
TA260 001:783.438 JLINK_Go()
TA260 001:783.446   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:786.239 - 2.800ms 
TA260 001:786.254 JLINK_IsHalted()
TA260 001:786.695 - 0.440ms returns FALSE
TA260 001:786.702 JLINK_HasError()
TA260 001:788.204 JLINK_IsHalted()
TA260 001:788.680 - 0.475ms returns FALSE
TA260 001:788.686 JLINK_HasError()
TA260 001:790.205 JLINK_IsHalted()
TA260 001:790.723 - 0.517ms returns FALSE
TA260 001:790.733 JLINK_HasError()
TA260 001:792.204 JLINK_IsHalted()
TA260 001:792.694 - 0.490ms returns FALSE
TA260 001:792.700 JLINK_HasError()
TA260 001:794.203 JLINK_IsHalted()
TA260 001:794.687 - 0.483ms returns FALSE
TA260 001:794.696 JLINK_HasError()
TA260 001:796.213 JLINK_IsHalted()
TA260 001:796.684 - 0.470ms returns FALSE
TA260 001:796.702 JLINK_HasError()
TA260 001:798.718 JLINK_IsHalted()
TA260 001:799.209 - 0.491ms returns FALSE
TA260 001:799.222 JLINK_HasError()
TA260 001:800.714 JLINK_IsHalted()
TA260 001:801.153 - 0.438ms returns FALSE
TA260 001:801.159 JLINK_HasError()
TA260 001:802.714 JLINK_IsHalted()
TA260 001:803.170 - 0.455ms returns FALSE
TA260 001:803.176 JLINK_HasError()
TA260 001:804.715 JLINK_IsHalted()
TA260 001:805.209 - 0.494ms returns FALSE
TA260 001:805.222 JLINK_HasError()
TA260 001:807.226 JLINK_IsHalted()
TA260 001:807.699 - 0.472ms returns FALSE
TA260 001:807.713 JLINK_HasError()
TA260 001:809.225 JLINK_IsHalted()
TA260 001:809.693 - 0.468ms returns FALSE
TA260 001:809.699 JLINK_HasError()
TA260 001:811.226 JLINK_IsHalted()
TA260 001:811.692 - 0.466ms returns FALSE
TA260 001:811.698 JLINK_HasError()
TA260 001:813.222 JLINK_IsHalted()
TA260 001:813.723 - 0.500ms returns FALSE
TA260 001:813.728 JLINK_HasError()
TA260 001:815.234 JLINK_IsHalted()
TA260 001:815.688 - 0.454ms returns FALSE
TA260 001:815.734 JLINK_HasError()
TA260 001:817.732 JLINK_IsHalted()
TA260 001:818.276 - 0.543ms returns FALSE
TA260 001:818.289 JLINK_HasError()
TA260 001:819.734 JLINK_IsHalted()
TA260 001:820.222 - 0.488ms returns FALSE
TA260 001:820.228 JLINK_HasError()
TA260 001:821.733 JLINK_IsHalted()
TA260 001:822.206 - 0.471ms returns FALSE
TA260 001:822.211 JLINK_HasError()
TA260 001:823.734 JLINK_IsHalted()
TA260 001:824.204 - 0.470ms returns FALSE
TA260 001:824.211 JLINK_HasError()
TA260 001:826.243 JLINK_IsHalted()
TA260 001:826.825 - 0.581ms returns FALSE
TA260 001:826.835 JLINK_HasError()
TA260 001:828.246 JLINK_IsHalted()
TA260 001:828.784 - 0.537ms returns FALSE
TA260 001:828.790 JLINK_HasError()
TA260 001:830.246 JLINK_IsHalted()
TA260 001:830.682 - 0.436ms returns FALSE
TA260 001:830.690 JLINK_HasError()
TA260 001:832.244 JLINK_IsHalted()
TA260 001:832.693 - 0.449ms returns FALSE
TA260 001:832.700 JLINK_HasError()
TA260 001:834.241 JLINK_IsHalted()
TA260 001:834.748 - 0.506ms returns FALSE
TA260 001:834.761 JLINK_HasError()
TA260 001:836.247 JLINK_IsHalted()
TA260 001:836.804 - 0.556ms returns FALSE
TA260 001:836.819 JLINK_HasError()
TA260 001:838.754 JLINK_IsHalted()
TA260 001:839.200 - 0.446ms returns FALSE
TA260 001:839.206 JLINK_HasError()
TA260 001:840.756 JLINK_IsHalted()
TA260 001:841.277 - 0.521ms returns FALSE
TA260 001:841.295 JLINK_HasError()
TA260 001:842.752 JLINK_IsHalted()
TA260 001:843.273 - 0.520ms returns FALSE
TA260 001:843.279 JLINK_HasError()
TA260 001:844.752 JLINK_IsHalted()
TA260 001:845.243 - 0.490ms returns FALSE
TA260 001:845.256 JLINK_HasError()
TA260 001:847.268 JLINK_IsHalted()
TA260 001:847.802 - 0.533ms returns FALSE
TA260 001:847.808 JLINK_HasError()
TA260 001:849.768 JLINK_IsHalted()
TA260 001:850.250 - 0.481ms returns FALSE
TA260 001:850.256 JLINK_HasError()
TA260 001:851.771 JLINK_IsHalted()
TA260 001:852.253 - 0.481ms returns FALSE
TA260 001:852.261 JLINK_HasError()
TA260 001:853.768 JLINK_IsHalted()
TA260 001:854.273 - 0.505ms returns FALSE
TA260 001:854.279 JLINK_HasError()
TA260 001:856.279 JLINK_IsHalted()
TA260 001:856.801 - 0.521ms returns FALSE
TA260 001:856.811 JLINK_HasError()
TA260 001:858.274 JLINK_IsHalted()
TA260 001:858.797 - 0.522ms returns FALSE
TA260 001:858.803 JLINK_HasError()
TA260 001:860.273 JLINK_IsHalted()
TA260 001:860.790 - 0.515ms returns FALSE
TA260 001:860.828 JLINK_HasError()
TA260 001:863.274 JLINK_IsHalted()
TA260 001:863.785 - 0.511ms returns FALSE
TA260 001:863.792 JLINK_HasError()
TA260 001:865.787 JLINK_IsHalted()
TA260 001:866.227 - 0.439ms returns FALSE
TA260 001:866.237 JLINK_HasError()
TA260 001:867.788 JLINK_IsHalted()
TA260 001:868.258 - 0.469ms returns FALSE
TA260 001:868.272 JLINK_HasError()
TA260 001:869.784 JLINK_IsHalted()
TA260 001:870.273 - 0.488ms returns FALSE
TA260 001:870.279 JLINK_HasError()
TA260 001:871.783 JLINK_IsHalted()
TA260 001:872.272 - 0.488ms returns FALSE
TA260 001:872.278 JLINK_HasError()
TA260 001:873.783 JLINK_IsHalted()
TA260 001:874.272 - 0.488ms returns FALSE
TA260 001:874.277 JLINK_HasError()
TA260 001:875.791 JLINK_IsHalted()
TA260 001:876.284 - 0.493ms returns FALSE
TA260 001:876.299 JLINK_HasError()
TA260 001:879.296 JLINK_IsHalted()
TA260 001:879.786 - 0.489ms returns FALSE
TA260 001:879.792 JLINK_HasError()
TA260 001:881.294 JLINK_IsHalted()
TA260 001:881.795 - 0.501ms returns FALSE
TA260 001:881.801 JLINK_HasError()
TA260 001:883.293 JLINK_IsHalted()
TA260 001:883.797 - 0.504ms returns FALSE
TA260 001:883.802 JLINK_HasError()
TA260 001:885.807 JLINK_IsHalted()
TA260 001:886.242 - 0.434ms returns FALSE
TA260 001:886.255 JLINK_HasError()
TA260 001:887.808 JLINK_IsHalted()
TA260 001:888.224 - 0.415ms returns FALSE
TA260 001:888.230 JLINK_HasError()
TA260 001:889.805 JLINK_IsHalted()
TA260 001:890.294 - 0.489ms returns FALSE
TA260 001:890.300 JLINK_HasError()
TA260 001:891.804 JLINK_IsHalted()
TA260 001:892.279 - 0.474ms returns FALSE
TA260 001:892.292 JLINK_HasError()
TA260 001:893.804 JLINK_IsHalted()
TA260 001:894.296 - 0.491ms returns FALSE
TA260 001:894.301 JLINK_HasError()
TA260 001:895.813 JLINK_IsHalted()
TA260 001:896.292 - 0.478ms returns FALSE
TA260 001:896.306 JLINK_HasError()
TA260 001:898.316 JLINK_IsHalted()
TA260 001:898.783 - 0.467ms returns FALSE
TA260 001:898.789 JLINK_HasError()
TA260 001:900.312 JLINK_IsHalted()
TA260 001:900.787 - 0.474ms returns FALSE
TA260 001:900.793 JLINK_HasError()
TA260 001:902.314 JLINK_IsHalted()
TA260 001:902.798 - 0.484ms returns FALSE
TA260 001:902.804 JLINK_HasError()
TA260 001:904.313 JLINK_IsHalted()
TA260 001:904.801 - 0.488ms returns FALSE
TA260 001:904.808 JLINK_HasError()
TA260 001:906.831 JLINK_IsHalted()
TA260 001:907.304 - 0.472ms returns FALSE
TA260 001:907.316 JLINK_HasError()
TA260 001:908.827 JLINK_IsHalted()
TA260 001:909.296 - 0.469ms returns FALSE
TA260 001:909.305 JLINK_HasError()
TA260 001:912.823 JLINK_IsHalted()
TA260 001:913.297 - 0.473ms returns FALSE
TA260 001:913.303 JLINK_HasError()
TA260 001:914.824 JLINK_IsHalted()
TA260 001:915.300 - 0.476ms returns FALSE
TA260 001:915.313 JLINK_HasError()
TA260 001:917.340 JLINK_IsHalted()
TA260 001:917.802 - 0.461ms returns FALSE
TA260 001:917.816 JLINK_HasError()
TA260 001:919.483 JLINK_IsHalted()
TA260 001:919.972 - 0.488ms returns FALSE
TA260 001:919.982 JLINK_HasError()
TA260 001:921.477 JLINK_IsHalted()
TA260 001:921.977 - 0.500ms returns FALSE
TA260 001:921.983 JLINK_HasError()
TA260 001:923.478 JLINK_IsHalted()
TA260 001:923.971 - 0.492ms returns FALSE
TA260 001:923.982 JLINK_HasError()
TA260 001:925.988 JLINK_IsHalted()
TA260 001:926.526 - 0.537ms returns FALSE
TA260 001:926.539 JLINK_HasError()
TA260 001:927.990 JLINK_IsHalted()
TA260 001:928.503 - 0.512ms returns FALSE
TA260 001:928.510 JLINK_HasError()
TA260 001:929.993 JLINK_IsHalted()
TA260 001:930.467 - 0.473ms returns FALSE
TA260 001:930.473 JLINK_HasError()
TA260 001:931.987 JLINK_IsHalted()
TA260 001:932.455 - 0.468ms returns FALSE
TA260 001:932.461 JLINK_HasError()
TA260 001:933.985 JLINK_IsHalted()
TA260 001:934.468 - 0.482ms returns FALSE
TA260 001:934.473 JLINK_HasError()
TA260 001:935.998 JLINK_IsHalted()
TA260 001:936.526 - 0.528ms returns FALSE
TA260 001:936.533 JLINK_HasError()
TA260 001:938.496 JLINK_IsHalted()
TA260 001:938.977 - 0.480ms returns FALSE
TA260 001:938.991 JLINK_HasError()
TA260 001:940.503 JLINK_IsHalted()
TA260 001:941.003 - 0.499ms returns FALSE
TA260 001:941.009 JLINK_HasError()
TA260 001:942.497 JLINK_IsHalted()
TA260 001:942.976 - 0.479ms returns FALSE
TA260 001:942.982 JLINK_HasError()
TA260 001:944.496 JLINK_IsHalted()
TA260 001:945.028 - 0.531ms returns FALSE
TA260 001:945.041 JLINK_HasError()
TA260 001:947.015 JLINK_IsHalted()
TA260 001:947.528 - 0.512ms returns FALSE
TA260 001:947.541 JLINK_HasError()
TA260 001:949.003 JLINK_IsHalted()
TA260 001:949.502 - 0.498ms returns FALSE
TA260 001:949.508 JLINK_HasError()
TA260 001:951.505 JLINK_IsHalted()
TA260 001:951.977 - 0.471ms returns FALSE
TA260 001:951.983 JLINK_HasError()
TA260 001:953.507 JLINK_IsHalted()
TA260 001:953.932 - 0.424ms returns FALSE
TA260 001:953.940 JLINK_HasError()
TA260 001:956.020 JLINK_IsHalted()
TA260 001:956.507 - 0.486ms returns FALSE
TA260 001:956.521 JLINK_HasError()
TA260 001:958.020 JLINK_IsHalted()
TA260 001:958.478 - 0.457ms returns FALSE
TA260 001:958.484 JLINK_HasError()
TA260 001:960.016 JLINK_IsHalted()
TA260 001:960.476 - 0.459ms returns FALSE
TA260 001:960.482 JLINK_HasError()
TA260 001:962.016 JLINK_IsHalted()
TA260 001:962.476 - 0.460ms returns FALSE
TA260 001:962.482 JLINK_HasError()
TA260 001:964.015 JLINK_IsHalted()
TA260 001:964.510 - 0.494ms returns FALSE
TA260 001:964.515 JLINK_HasError()
TA260 001:966.021 JLINK_IsHalted()
TA260 001:966.529 - 0.507ms returns FALSE
TA260 001:966.547 JLINK_HasError()
TA260 001:968.524 JLINK_IsHalted()
TA260 001:968.984 - 0.460ms returns FALSE
TA260 001:968.992 JLINK_HasError()
TA260 001:970.525 JLINK_IsHalted()
TA260 001:970.986 - 0.460ms returns FALSE
TA260 001:970.992 JLINK_HasError()
TA260 001:973.527 JLINK_IsHalted()
TA260 001:974.001 - 0.473ms returns FALSE
TA260 001:974.007 JLINK_HasError()
TA260 001:976.034 JLINK_IsHalted()
TA260 001:976.468 - 0.433ms returns FALSE
TA260 001:976.475 JLINK_HasError()
TA260 001:978.036 JLINK_IsHalted()
TA260 001:978.573 - 0.536ms returns FALSE
TA260 001:978.586 JLINK_HasError()
TA260 001:980.032 JLINK_IsHalted()
TA260 001:980.512 - 0.480ms returns FALSE
TA260 001:980.518 JLINK_HasError()
TA260 001:982.038 JLINK_IsHalted()
TA260 001:982.570 - 0.531ms returns FALSE
TA260 001:982.577 JLINK_HasError()
TA260 001:984.031 JLINK_IsHalted()
TA260 001:984.523 - 0.491ms returns FALSE
TA260 001:984.529 JLINK_HasError()
TA260 001:986.042 JLINK_IsHalted()
TA260 001:986.531 - 0.489ms returns FALSE
TA260 001:986.545 JLINK_HasError()
TA260 001:988.543 JLINK_IsHalted()
TA260 001:988.990 - 0.446ms returns FALSE
TA260 001:988.996 JLINK_HasError()
TA260 001:990.547 JLINK_IsHalted()
TA260 001:991.024 - 0.476ms returns FALSE
TA260 001:991.037 JLINK_HasError()
TA260 001:992.548 JLINK_IsHalted()
TA260 001:993.037 - 0.489ms returns FALSE
TA260 001:993.050 JLINK_HasError()
TA260 001:994.541 JLINK_IsHalted()
TA260 001:995.027 - 0.485ms returns FALSE
TA260 001:995.037 JLINK_HasError()
TA260 001:997.052 JLINK_IsHalted()
TA260 001:997.514 - 0.462ms returns FALSE
TA260 001:997.528 JLINK_HasError()
TA260 001:999.049 JLINK_IsHalted()
TA260 001:999.545 - 0.495ms returns FALSE
TA260 001:999.550 JLINK_HasError()
TA260 002:001.055 JLINK_IsHalted()
TA260 002:001.530 - 0.474ms returns FALSE
TA260 002:001.540 JLINK_HasError()
TA260 002:003.060 JLINK_IsHalted()
TA260 002:003.572 - 0.511ms returns FALSE
TA260 002:003.583 JLINK_HasError()
TA260 002:005.058 JLINK_IsHalted()
TA260 002:005.492 - 0.433ms returns FALSE
TA260 002:005.499 JLINK_HasError()
TA260 002:006.560 JLINK_IsHalted()
TA260 002:006.984 - 0.423ms returns FALSE
TA260 002:006.991 JLINK_HasError()
TA260 002:008.562 JLINK_IsHalted()
TA260 002:009.047 - 0.484ms returns FALSE
TA260 002:009.053 JLINK_HasError()
TA260 002:010.559 JLINK_IsHalted()
TA260 002:011.035 - 0.475ms returns FALSE
TA260 002:011.040 JLINK_HasError()
TA260 002:012.562 JLINK_IsHalted()
TA260 002:013.069 - 0.507ms returns FALSE
TA260 002:013.075 JLINK_HasError()
TA260 002:014.561 JLINK_IsHalted()
TA260 002:015.035 - 0.473ms returns FALSE
TA260 002:015.046 JLINK_HasError()
TA260 002:017.077 JLINK_IsHalted()
TA260 002:017.539 - 0.462ms returns FALSE
TA260 002:017.550 JLINK_HasError()
TA260 002:019.068 JLINK_IsHalted()
TA260 002:019.569 - 0.500ms returns FALSE
TA260 002:019.575 JLINK_HasError()
TA260 002:021.069 JLINK_IsHalted()
TA260 002:021.568 - 0.499ms returns FALSE
TA260 002:021.575 JLINK_HasError()
TA260 002:023.073 JLINK_IsHalted()
TA260 002:023.568 - 0.495ms returns FALSE
TA260 002:023.573 JLINK_HasError()
TA260 002:025.073 JLINK_IsHalted()
TA260 002:025.518 - 0.444ms returns FALSE
TA260 002:025.528 JLINK_HasError()
TA260 002:026.578 JLINK_IsHalted()
TA260 002:027.051 - 0.472ms returns FALSE
TA260 002:027.064 JLINK_HasError()
TA260 002:028.581 JLINK_IsHalted()
TA260 002:029.092 - 0.511ms returns FALSE
TA260 002:029.099 JLINK_HasError()
TA260 002:030.578 JLINK_IsHalted()
TA260 002:031.069 - 0.491ms returns FALSE
TA260 002:031.075 JLINK_HasError()
TA260 002:032.581 JLINK_IsHalted()
TA260 002:033.082 - 0.501ms returns FALSE
TA260 002:033.088 JLINK_HasError()
TA260 002:034.587 JLINK_IsHalted()
TA260 002:035.221 - 0.634ms returns FALSE
TA260 002:035.238 JLINK_HasError()
TA260 002:037.104 JLINK_IsHalted()
TA260 002:037.663 - 0.558ms returns FALSE
TA260 002:037.677 JLINK_HasError()
TA260 002:039.088 JLINK_IsHalted()
TA260 002:039.646 - 0.558ms returns FALSE
TA260 002:039.652 JLINK_HasError()
TA260 002:041.090 JLINK_IsHalted()
TA260 002:041.545 - 0.454ms returns FALSE
TA260 002:041.551 JLINK_HasError()
TA260 002:043.087 JLINK_IsHalted()
TA260 002:043.553 - 0.466ms returns FALSE
TA260 002:043.558 JLINK_HasError()
TA260 002:045.101 JLINK_IsHalted()
TA260 002:045.553 - 0.451ms returns FALSE
TA260 002:045.566 JLINK_HasError()
TA260 002:047.607 JLINK_IsHalted()
TA260 002:049.107 - 1.499ms returns FALSE
TA260 002:049.117 JLINK_HasError()
TA260 002:051.105 JLINK_IsHalted()
TA260 002:051.649 - 0.544ms returns FALSE
TA260 002:051.657 JLINK_HasError()
TA260 002:053.102 JLINK_IsHalted()
TA260 002:053.648 - 0.546ms returns FALSE
TA260 002:053.654 JLINK_HasError()
TA260 002:055.107 JLINK_IsHalted()
TA260 002:055.648 - 0.540ms returns FALSE
TA260 002:055.655 JLINK_HasError()
TA260 002:057.618 JLINK_IsHalted()
TA260 002:058.082 - 0.464ms returns FALSE
TA260 002:058.095 JLINK_HasError()
TA260 002:059.613 JLINK_IsHalted()
TA260 002:060.125 - 0.511ms returns FALSE
TA260 002:060.131 JLINK_HasError()
TA260 002:061.612 JLINK_IsHalted()
TA260 002:062.049 - 0.436ms returns FALSE
TA260 002:062.054 JLINK_HasError()
TA260 002:063.376 JLINK_IsHalted()
TA260 002:063.796 - 0.419ms returns FALSE
TA260 002:063.802 JLINK_HasError()
TA260 002:065.883 JLINK_IsHalted()
TA260 002:066.368 - 0.484ms returns FALSE
TA260 002:066.383 JLINK_HasError()
TA260 002:067.888 JLINK_IsHalted()
TA260 002:068.388 - 0.499ms returns FALSE
TA260 002:068.400 JLINK_HasError()
TA260 002:069.883 JLINK_IsHalted()
TA260 002:070.376 - 0.492ms returns FALSE
TA260 002:070.381 JLINK_HasError()
TA260 002:071.883 JLINK_IsHalted()
TA260 002:072.353 - 0.470ms returns FALSE
TA260 002:072.359 JLINK_HasError()
TA260 002:073.882 JLINK_IsHalted()
TA260 002:074.350 - 0.468ms returns FALSE
TA260 002:074.356 JLINK_HasError()
TA260 002:075.890 JLINK_IsHalted()
TA260 002:076.346 - 0.456ms returns FALSE
TA260 002:076.361 JLINK_HasError()
TA260 002:078.392 JLINK_IsHalted()
TA260 002:078.927 - 0.534ms returns FALSE
TA260 002:078.935 JLINK_HasError()
TA260 002:081.394 JLINK_IsHalted()
TA260 002:081.889 - 0.495ms returns FALSE
TA260 002:081.895 JLINK_HasError()
TA260 002:083.391 JLINK_IsHalted()
TA260 002:083.888 - 0.497ms returns FALSE
TA260 002:083.894 JLINK_HasError()
TA260 002:085.901 JLINK_IsHalted()
TA260 002:086.380 - 0.479ms returns FALSE
TA260 002:086.392 JLINK_HasError()
TA260 002:087.899 JLINK_IsHalted()
TA260 002:088.422 - 0.522ms returns FALSE
TA260 002:088.435 JLINK_HasError()
TA260 002:089.900 JLINK_IsHalted()
TA260 002:090.398 - 0.498ms returns FALSE
TA260 002:090.404 JLINK_HasError()
TA260 002:091.898 JLINK_IsHalted()
TA260 002:092.399 - 0.500ms returns FALSE
TA260 002:092.404 JLINK_HasError()
TA260 002:093.897 JLINK_IsHalted()
TA260 002:094.449 - 0.551ms returns FALSE
TA260 002:094.459 JLINK_HasError()
TA260 002:095.908 JLINK_IsHalted()
TA260 002:096.410 - 0.501ms returns FALSE
TA260 002:096.419 JLINK_HasError()
TA260 002:098.410 JLINK_IsHalted()
TA260 002:098.899 - 0.489ms returns FALSE
TA260 002:098.905 JLINK_HasError()
TA260 002:100.406 JLINK_IsHalted()
TA260 002:100.874 - 0.468ms returns FALSE
TA260 002:100.880 JLINK_HasError()
TA260 002:102.408 JLINK_IsHalted()
TA260 002:102.897 - 0.488ms returns FALSE
TA260 002:102.907 JLINK_HasError()
TA260 002:104.407 JLINK_IsHalted()
TA260 002:104.932 - 0.524ms returns FALSE
TA260 002:104.943 JLINK_HasError()
TA260 002:106.924 JLINK_IsHalted()
TA260 002:107.364 - 0.440ms returns FALSE
TA260 002:107.372 JLINK_HasError()
TA260 002:108.920 JLINK_IsHalted()
TA260 002:109.370 - 0.449ms returns FALSE
TA260 002:109.383 JLINK_HasError()
TA260 002:110.429 JLINK_IsHalted()
TA260 002:110.957 - 0.527ms returns FALSE
TA260 002:110.963 JLINK_HasError()
TA260 002:112.422 JLINK_IsHalted()
TA260 002:112.896 - 0.473ms returns FALSE
TA260 002:112.902 JLINK_HasError()
TA260 002:114.422 JLINK_IsHalted()
TA260 002:114.997 - 0.575ms returns FALSE
TA260 002:115.008 JLINK_HasError()
TA260 002:116.929 JLINK_IsHalted()
TA260 002:117.386 - 0.456ms returns FALSE
TA260 002:117.393 JLINK_HasError()
TA260 002:118.931 JLINK_IsHalted()
TA260 002:119.422 - 0.490ms returns FALSE
TA260 002:119.428 JLINK_HasError()
TA260 002:120.932 JLINK_IsHalted()
TA260 002:121.447 - 0.514ms returns FALSE
TA260 002:121.455 JLINK_HasError()
TA260 002:122.931 JLINK_IsHalted()
TA260 002:123.441 - 0.509ms returns FALSE
TA260 002:123.446 JLINK_HasError()
TA260 002:124.932 JLINK_IsHalted()
TA260 002:125.393 - 0.460ms returns FALSE
TA260 002:125.403 JLINK_HasError()
TA260 002:127.453 JLINK_IsHalted()
TA260 002:127.923 - 0.469ms returns FALSE
TA260 002:127.931 JLINK_HasError()
TA260 002:129.448 JLINK_IsHalted()
TA260 002:129.958 - 0.509ms returns FALSE
TA260 002:129.964 JLINK_HasError()
TA260 002:131.444 JLINK_IsHalted()
TA260 002:131.931 - 0.487ms returns FALSE
TA260 002:131.937 JLINK_HasError()
TA260 002:133.445 JLINK_IsHalted()
TA260 002:133.923 - 0.477ms returns FALSE
TA260 002:133.929 JLINK_HasError()
TA260 002:135.971 JLINK_IsHalted()
TA260 002:136.474 - 0.502ms returns FALSE
TA260 002:136.508 JLINK_HasError()
TA260 002:137.956 JLINK_IsHalted()
TA260 002:138.529 - 0.572ms returns FALSE
TA260 002:138.541 JLINK_HasError()
TA260 002:139.957 JLINK_IsHalted()
TA260 002:140.480 - 0.522ms returns FALSE
TA260 002:140.485 JLINK_HasError()
TA260 002:141.959 JLINK_IsHalted()
TA260 002:142.400 - 0.440ms returns FALSE
TA260 002:142.406 JLINK_HasError()
TA260 002:143.953 JLINK_IsHalted()
TA260 002:144.429 - 0.476ms returns FALSE
TA260 002:144.435 JLINK_HasError()
TA260 002:145.964 JLINK_IsHalted()
TA260 002:146.430 - 0.465ms returns FALSE
TA260 002:146.444 JLINK_HasError()
TA260 002:148.463 JLINK_IsHalted()
TA260 002:148.971 - 0.508ms returns FALSE
TA260 002:148.978 JLINK_HasError()
TA260 002:150.963 JLINK_IsHalted()
TA260 002:151.457 - 0.493ms returns FALSE
TA260 002:151.463 JLINK_HasError()
TA260 002:152.964 JLINK_IsHalted()
TA260 002:153.443 - 0.478ms returns FALSE
TA260 002:153.448 JLINK_HasError()
TA260 002:154.965 JLINK_IsHalted()
TA260 002:155.451 - 0.485ms returns FALSE
TA260 002:155.460 JLINK_HasError()
TA260 002:157.480 JLINK_IsHalted()
TA260 002:157.982 - 0.502ms returns FALSE
TA260 002:157.989 JLINK_HasError()
TA260 002:159.474 JLINK_IsHalted()
TA260 002:159.967 - 0.492ms returns FALSE
TA260 002:159.973 JLINK_HasError()
TA260 002:161.474 JLINK_IsHalted()
TA260 002:161.966 - 0.491ms returns FALSE
TA260 002:161.971 JLINK_HasError()
TA260 002:163.472 JLINK_IsHalted()
TA260 002:163.941 - 0.468ms returns FALSE
TA260 002:163.946 JLINK_HasError()
TA260 002:165.986 JLINK_IsHalted()
TA260 002:166.507 - 0.520ms returns FALSE
TA260 002:166.529 JLINK_HasError()
TA260 002:167.984 JLINK_IsHalted()
TA260 002:168.469 - 0.484ms returns FALSE
TA260 002:168.479 JLINK_HasError()
TA260 002:169.980 JLINK_IsHalted()
TA260 002:170.454 - 0.473ms returns FALSE
TA260 002:170.459 JLINK_HasError()
TA260 002:171.981 JLINK_IsHalted()
TA260 002:172.460 - 0.479ms returns FALSE
TA260 002:172.473 JLINK_HasError()
TA260 002:173.982 JLINK_IsHalted()
TA260 002:174.476 - 0.493ms returns FALSE
TA260 002:174.482 JLINK_HasError()
TA260 002:175.987 JLINK_IsHalted()
TA260 002:176.468 - 0.480ms returns FALSE
TA260 002:176.484 JLINK_HasError()
TA260 002:178.487 JLINK_IsHalted()
TA260 002:178.974 - 0.486ms returns FALSE
TA260 002:178.986 JLINK_HasError()
TA260 002:180.488 JLINK_IsHalted()
TA260 002:180.976 - 0.487ms returns FALSE
TA260 002:180.982 JLINK_HasError()
TA260 002:182.488 JLINK_IsHalted()
TA260 002:182.973 - 0.485ms returns FALSE
TA260 002:182.979 JLINK_HasError()
TA260 002:184.488 JLINK_IsHalted()
TA260 002:184.970 - 0.482ms returns FALSE
TA260 002:184.978 JLINK_HasError()
TA260 002:187.000 JLINK_IsHalted()
TA260 002:187.532 - 0.531ms returns FALSE
TA260 002:187.547 JLINK_HasError()
TA260 002:189.999 JLINK_IsHalted()
TA260 002:190.513 - 0.512ms returns FALSE
TA260 002:190.519 JLINK_HasError()
TA260 002:192.000 JLINK_IsHalted()
TA260 002:192.502 - 0.501ms returns FALSE
TA260 002:192.508 JLINK_HasError()
TA260 002:193.998 JLINK_IsHalted()
TA260 002:194.476 - 0.477ms returns FALSE
TA260 002:194.482 JLINK_HasError()
TA260 002:196.005 JLINK_IsHalted()
TA260 002:196.515 - 0.509ms returns FALSE
TA260 002:196.531 JLINK_HasError()
TA260 002:198.510 JLINK_IsHalted()
TA260 002:198.980 - 0.469ms returns FALSE
TA260 002:198.986 JLINK_HasError()
TA260 002:200.507 JLINK_IsHalted()
TA260 002:202.830   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:203.306 - 2.798ms returns TRUE
TA260 002:203.323 JLINK_ReadReg(R15 (PC))
TA260 002:203.329 - 0.006ms returns 0x20000000
TA260 002:203.334 JLINK_ClrBPEx(BPHandle = 0x00000009)
TA260 002:203.338 - 0.004ms returns 0x00
TA260 002:203.343 JLINK_ReadReg(R0)
TA260 002:203.346 - 0.003ms returns 0x00000000
TA260 002:203.675 JLINK_HasError()
TA260 002:203.684 JLINK_WriteReg(R0, 0x00000001)
TA260 002:203.689 - 0.004ms returns 0
TA260 002:203.693 JLINK_WriteReg(R1, 0x00004000)
TA260 002:203.697 - 0.003ms returns 0
TA260 002:203.701 JLINK_WriteReg(R2, 0x000000FF)
TA260 002:203.704 - 0.003ms returns 0
TA260 002:203.708 JLINK_WriteReg(R3, 0x00000000)
TA260 002:203.712 - 0.003ms returns 0
TA260 002:203.716 JLINK_WriteReg(R4, 0x00000000)
TA260 002:203.719 - 0.003ms returns 0
TA260 002:203.723 JLINK_WriteReg(R5, 0x00000000)
TA260 002:203.727 - 0.003ms returns 0
TA260 002:203.731 JLINK_WriteReg(R6, 0x00000000)
TA260 002:203.734 - 0.003ms returns 0
TA260 002:203.739 JLINK_WriteReg(R7, 0x00000000)
TA260 002:203.742 - 0.003ms returns 0
TA260 002:203.746 JLINK_WriteReg(R8, 0x00000000)
TA260 002:203.750 - 0.003ms returns 0
TA260 002:203.754 JLINK_WriteReg(R9, 0x20000180)
TA260 002:203.757 - 0.003ms returns 0
TA260 002:203.761 JLINK_WriteReg(R10, 0x00000000)
TA260 002:203.764 - 0.003ms returns 0
TA260 002:203.769 JLINK_WriteReg(R11, 0x00000000)
TA260 002:203.772 - 0.003ms returns 0
TA260 002:203.776 JLINK_WriteReg(R12, 0x00000000)
TA260 002:203.780 - 0.003ms returns 0
TA260 002:203.784 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:203.788 - 0.003ms returns 0
TA260 002:203.792 JLINK_WriteReg(R14, 0x20000001)
TA260 002:203.795 - 0.003ms returns 0
TA260 002:203.799 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 002:203.803 - 0.003ms returns 0
TA260 002:203.807 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:203.810 - 0.003ms returns 0
TA260 002:203.815 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:203.818 - 0.003ms returns 0
TA260 002:203.822 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:203.826 - 0.003ms returns 0
TA260 002:203.830 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:203.834 - 0.003ms returns 0
TA260 002:203.838 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:203.846 - 0.008ms returns 0x0000000A
TA260 002:203.852 JLINK_Go()
TA260 002:203.862   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:206.660 - 2.807ms 
TA260 002:206.677 JLINK_IsHalted()
TA260 002:209.033   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:209.543 - 2.865ms returns TRUE
TA260 002:209.560 JLINK_ReadReg(R15 (PC))
TA260 002:209.565 - 0.005ms returns 0x20000000
TA260 002:209.570 JLINK_ClrBPEx(BPHandle = 0x0000000A)
TA260 002:209.574 - 0.004ms returns 0x00
TA260 002:209.578 JLINK_ReadReg(R0)
TA260 002:209.582 - 0.003ms returns 0x00000000
TA260 002:264.674 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 002:264.690   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 002:264.709   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 002:266.680 - 2.005ms returns 0x184
TA260 002:266.747 JLINK_HasError()
TA260 002:266.754 JLINK_WriteReg(R0, 0x08000000)
TA260 002:266.762 - 0.007ms returns 0
TA260 002:266.766 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 002:266.770 - 0.003ms returns 0
TA260 002:266.774 JLINK_WriteReg(R2, 0x00000002)
TA260 002:266.778 - 0.003ms returns 0
TA260 002:266.782 JLINK_WriteReg(R3, 0x00000000)
TA260 002:266.785 - 0.003ms returns 0
TA260 002:266.789 JLINK_WriteReg(R4, 0x00000000)
TA260 002:266.792 - 0.003ms returns 0
TA260 002:266.796 JLINK_WriteReg(R5, 0x00000000)
TA260 002:266.800 - 0.003ms returns 0
TA260 002:266.804 JLINK_WriteReg(R6, 0x00000000)
TA260 002:266.808 - 0.003ms returns 0
TA260 002:266.812 JLINK_WriteReg(R7, 0x00000000)
TA260 002:266.815 - 0.003ms returns 0
TA260 002:266.819 JLINK_WriteReg(R8, 0x00000000)
TA260 002:266.822 - 0.003ms returns 0
TA260 002:266.826 JLINK_WriteReg(R9, 0x20000180)
TA260 002:266.830 - 0.003ms returns 0
TA260 002:266.834 JLINK_WriteReg(R10, 0x00000000)
TA260 002:266.838 - 0.003ms returns 0
TA260 002:266.842 JLINK_WriteReg(R11, 0x00000000)
TA260 002:266.845 - 0.003ms returns 0
TA260 002:266.849 JLINK_WriteReg(R12, 0x00000000)
TA260 002:266.852 - 0.003ms returns 0
TA260 002:266.857 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:266.861 - 0.004ms returns 0
TA260 002:266.865 JLINK_WriteReg(R14, 0x20000001)
TA260 002:266.868 - 0.003ms returns 0
TA260 002:266.872 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 002:266.876 - 0.004ms returns 0
TA260 002:266.880 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:266.884 - 0.003ms returns 0
TA260 002:266.892 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:266.896 - 0.003ms returns 0
TA260 002:266.900 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:266.903 - 0.003ms returns 0
TA260 002:266.907 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:266.911 - 0.003ms returns 0
TA260 002:266.916 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:266.925   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:267.421 - 0.504ms returns 0x0000000B
TA260 002:267.438 JLINK_Go()
TA260 002:267.445   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 002:267.975   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:270.757 - 3.319ms 
TA260 002:270.788 JLINK_IsHalted()
TA260 002:273.171   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:273.698 - 2.909ms returns TRUE
TA260 002:273.714 JLINK_ReadReg(R15 (PC))
TA260 002:273.722 - 0.007ms returns 0x20000000
TA260 002:273.766 JLINK_ClrBPEx(BPHandle = 0x0000000B)
TA260 002:273.772 - 0.006ms returns 0x00
TA260 002:273.777 JLINK_ReadReg(R0)
TA260 002:273.782 - 0.004ms returns 0x00000000
TA260 002:274.105 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:274.115   Data:  18 18 00 20 C1 01 00 08 99 2A 00 08 7D 27 00 08 ...
TA260 002:274.130   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:276.768 - 2.661ms returns 0x27C
TA260 002:276.799 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:276.804   Data:  10 B5 13 46 0A 46 04 46 19 46 FF F7 F0 FF 20 46 ...
TA260 002:276.821   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:278.701 - 1.901ms returns 0x184
TA260 002:278.722 JLINK_HasError()
TA260 002:278.728 JLINK_WriteReg(R0, 0x08000000)
TA260 002:278.734 - 0.005ms returns 0
TA260 002:278.738 JLINK_WriteReg(R1, 0x00000400)
TA260 002:278.742 - 0.004ms returns 0
TA260 002:278.753 JLINK_WriteReg(R2, 0x20000184)
TA260 002:278.756 - 0.003ms returns 0
TA260 002:278.761 JLINK_WriteReg(R3, 0x00000000)
TA260 002:278.764 - 0.003ms returns 0
TA260 002:278.768 JLINK_WriteReg(R4, 0x00000000)
TA260 002:278.772 - 0.004ms returns 0
TA260 002:278.776 JLINK_WriteReg(R5, 0x00000000)
TA260 002:278.779 - 0.003ms returns 0
TA260 002:278.783 JLINK_WriteReg(R6, 0x00000000)
TA260 002:278.787 - 0.003ms returns 0
TA260 002:278.792 JLINK_WriteReg(R7, 0x00000000)
TA260 002:278.795 - 0.003ms returns 0
TA260 002:278.799 JLINK_WriteReg(R8, 0x00000000)
TA260 002:278.802 - 0.003ms returns 0
TA260 002:278.806 JLINK_WriteReg(R9, 0x20000180)
TA260 002:278.810 - 0.003ms returns 0
TA260 002:278.814 JLINK_WriteReg(R10, 0x00000000)
TA260 002:278.817 - 0.003ms returns 0
TA260 002:278.821 JLINK_WriteReg(R11, 0x00000000)
TA260 002:278.824 - 0.003ms returns 0
TA260 002:278.829 JLINK_WriteReg(R12, 0x00000000)
TA260 002:278.832 - 0.003ms returns 0
TA260 002:278.836 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:278.840 - 0.003ms returns 0
TA260 002:278.844 JLINK_WriteReg(R14, 0x20000001)
TA260 002:278.847 - 0.003ms returns 0
TA260 002:278.852 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:278.855 - 0.003ms returns 0
TA260 002:278.859 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:278.862 - 0.003ms returns 0
TA260 002:278.866 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:278.870 - 0.003ms returns 0
TA260 002:278.874 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:278.877 - 0.003ms returns 0
TA260 002:278.881 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:278.885 - 0.003ms returns 0
TA260 002:278.890 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:278.894 - 0.004ms returns 0x0000000C
TA260 002:278.898 JLINK_Go()
TA260 002:278.908   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:281.668 - 2.768ms 
TA260 002:281.695 JLINK_IsHalted()
TA260 002:282.180 - 0.484ms returns FALSE
TA260 002:282.194 JLINK_HasError()
TA260 002:284.989 JLINK_IsHalted()
TA260 002:285.494 - 0.504ms returns FALSE
TA260 002:285.511 JLINK_HasError()
TA260 002:287.501 JLINK_IsHalted()
TA260 002:289.818   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:290.311 - 2.809ms returns TRUE
TA260 002:290.322 JLINK_ReadReg(R15 (PC))
TA260 002:290.327 - 0.005ms returns 0x20000000
TA260 002:290.332 JLINK_ClrBPEx(BPHandle = 0x0000000C)
TA260 002:290.336 - 0.004ms returns 0x00
TA260 002:290.341 JLINK_ReadReg(R0)
TA260 002:290.344 - 0.003ms returns 0x00000000
TA260 002:290.749 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:290.761   Data:  5B D0 C3 F3 0A 54 C1 F3 0A 55 2C 44 A4 F2 F3 34 ...
TA260 002:290.773   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:293.435 - 2.685ms returns 0x27C
TA260 002:293.451 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:293.454   Data:  01 02 06 D0 0A 0D A2 F5 60 72 C1 F3 13 01 00 2A ...
TA260 002:293.465   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:295.365 - 1.912ms returns 0x184
TA260 002:295.398 JLINK_HasError()
TA260 002:295.405 JLINK_WriteReg(R0, 0x08000400)
TA260 002:295.411 - 0.006ms returns 0
TA260 002:295.416 JLINK_WriteReg(R1, 0x00000400)
TA260 002:295.419 - 0.003ms returns 0
TA260 002:295.424 JLINK_WriteReg(R2, 0x20000184)
TA260 002:295.427 - 0.003ms returns 0
TA260 002:295.432 JLINK_WriteReg(R3, 0x00000000)
TA260 002:295.435 - 0.003ms returns 0
TA260 002:295.439 JLINK_WriteReg(R4, 0x00000000)
TA260 002:295.442 - 0.003ms returns 0
TA260 002:295.446 JLINK_WriteReg(R5, 0x00000000)
TA260 002:295.450 - 0.003ms returns 0
TA260 002:295.454 JLINK_WriteReg(R6, 0x00000000)
TA260 002:295.457 - 0.003ms returns 0
TA260 002:295.461 JLINK_WriteReg(R7, 0x00000000)
TA260 002:295.464 - 0.003ms returns 0
TA260 002:295.469 JLINK_WriteReg(R8, 0x00000000)
TA260 002:295.472 - 0.004ms returns 0
TA260 002:295.476 JLINK_WriteReg(R9, 0x20000180)
TA260 002:295.480 - 0.003ms returns 0
TA260 002:295.484 JLINK_WriteReg(R10, 0x00000000)
TA260 002:295.488 - 0.003ms returns 0
TA260 002:295.492 JLINK_WriteReg(R11, 0x00000000)
TA260 002:295.495 - 0.003ms returns 0
TA260 002:295.508 JLINK_WriteReg(R12, 0x00000000)
TA260 002:295.512 - 0.003ms returns 0
TA260 002:295.516 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:295.520 - 0.003ms returns 0
TA260 002:295.524 JLINK_WriteReg(R14, 0x20000001)
TA260 002:295.527 - 0.003ms returns 0
TA260 002:295.532 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:295.535 - 0.003ms returns 0
TA260 002:295.547 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:295.550 - 0.003ms returns 0
TA260 002:295.554 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:295.558 - 0.003ms returns 0
TA260 002:295.562 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:295.565 - 0.003ms returns 0
TA260 002:295.569 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:295.572 - 0.003ms returns 0
TA260 002:295.577 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:295.582 - 0.005ms returns 0x0000000D
TA260 002:295.587 JLINK_Go()
TA260 002:295.598   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:298.320 - 2.731ms 
TA260 002:298.345 JLINK_IsHalted()
TA260 002:298.846 - 0.501ms returns FALSE
TA260 002:298.863 JLINK_HasError()
TA260 002:300.999 JLINK_IsHalted()
TA260 002:301.486 - 0.486ms returns FALSE
TA260 002:301.495 JLINK_HasError()
TA260 002:302.997 JLINK_IsHalted()
TA260 002:305.364   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:305.893 - 2.896ms returns TRUE
TA260 002:305.902 JLINK_ReadReg(R15 (PC))
TA260 002:305.909 - 0.006ms returns 0x20000000
TA260 002:305.914 JLINK_ClrBPEx(BPHandle = 0x0000000D)
TA260 002:305.917 - 0.004ms returns 0x00
TA260 002:305.922 JLINK_ReadReg(R0)
TA260 002:305.926 - 0.003ms returns 0x00000000
TA260 002:306.335 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:306.347   Data:  F0 4D 92 46 9B 46 11 B1 B1 FA 81 F2 02 E0 B0 FA ...
TA260 002:306.360   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:309.030 - 2.695ms returns 0x27C
TA260 002:309.057 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:309.062   Data:  D5 FD 20 46 4F F4 00 51 00 22 00 F0 CF FD 01 20 ...
TA260 002:309.074   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:310.923 - 1.865ms returns 0x184
TA260 002:310.932 JLINK_HasError()
TA260 002:310.938 JLINK_WriteReg(R0, 0x08000800)
TA260 002:310.944 - 0.005ms returns 0
TA260 002:310.948 JLINK_WriteReg(R1, 0x00000400)
TA260 002:310.951 - 0.003ms returns 0
TA260 002:310.955 JLINK_WriteReg(R2, 0x20000184)
TA260 002:310.959 - 0.003ms returns 0
TA260 002:310.963 JLINK_WriteReg(R3, 0x00000000)
TA260 002:310.966 - 0.003ms returns 0
TA260 002:310.970 JLINK_WriteReg(R4, 0x00000000)
TA260 002:310.975 - 0.004ms returns 0
TA260 002:310.979 JLINK_WriteReg(R5, 0x00000000)
TA260 002:310.982 - 0.003ms returns 0
TA260 002:310.986 JLINK_WriteReg(R6, 0x00000000)
TA260 002:310.989 - 0.003ms returns 0
TA260 002:310.994 JLINK_WriteReg(R7, 0x00000000)
TA260 002:310.997 - 0.003ms returns 0
TA260 002:311.001 JLINK_WriteReg(R8, 0x00000000)
TA260 002:311.005 - 0.003ms returns 0
TA260 002:311.009 JLINK_WriteReg(R9, 0x20000180)
TA260 002:311.012 - 0.003ms returns 0
TA260 002:311.017 JLINK_WriteReg(R10, 0x00000000)
TA260 002:311.020 - 0.003ms returns 0
TA260 002:311.024 JLINK_WriteReg(R11, 0x00000000)
TA260 002:311.028 - 0.004ms returns 0
TA260 002:311.033 JLINK_WriteReg(R12, 0x00000000)
TA260 002:311.036 - 0.003ms returns 0
TA260 002:311.040 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:311.044 - 0.004ms returns 0
TA260 002:311.048 JLINK_WriteReg(R14, 0x20000001)
TA260 002:311.052 - 0.003ms returns 0
TA260 002:311.056 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:311.060 - 0.004ms returns 0
TA260 002:311.064 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:311.068 - 0.003ms returns 0
TA260 002:311.072 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:311.075 - 0.003ms returns 0
TA260 002:311.079 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:311.083 - 0.003ms returns 0
TA260 002:311.087 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:311.090 - 0.003ms returns 0
TA260 002:311.095 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:311.099 - 0.004ms returns 0x0000000E
TA260 002:311.103 JLINK_Go()
TA260 002:311.158   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:313.893 - 2.789ms 
TA260 002:313.914 JLINK_IsHalted()
TA260 002:314.446 - 0.532ms returns FALSE
TA260 002:314.454 JLINK_HasError()
TA260 002:316.018 JLINK_IsHalted()
TA260 002:316.512 - 0.493ms returns FALSE
TA260 002:316.542 JLINK_HasError()
TA260 002:318.020 JLINK_IsHalted()
TA260 002:320.341   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:320.797 - 2.776ms returns TRUE
TA260 002:320.805 JLINK_ReadReg(R15 (PC))
TA260 002:320.810 - 0.005ms returns 0x20000000
TA260 002:320.815 JLINK_ClrBPEx(BPHandle = 0x0000000E)
TA260 002:320.819 - 0.004ms returns 0x00
TA260 002:320.824 JLINK_ReadReg(R0)
TA260 002:320.827 - 0.003ms returns 0x00000000
TA260 002:321.220 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:321.231   Data:  BD E8 F0 40 00 F0 10 BD 2D E9 F0 4F 81 B0 41 F6 ...
TA260 002:321.242   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:323.947 - 2.726ms returns 0x27C
TA260 002:323.963 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:323.967   Data:  05 F0 01 02 30 46 4F F4 80 51 00 F0 CF FB 20 46 ...
TA260 002:323.977   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:325.894 - 1.930ms returns 0x184
TA260 002:325.920 JLINK_HasError()
TA260 002:325.957 JLINK_WriteReg(R0, 0x08000C00)
TA260 002:325.973 - 0.015ms returns 0
TA260 002:325.978 JLINK_WriteReg(R1, 0x00000400)
TA260 002:325.981 - 0.003ms returns 0
TA260 002:325.985 JLINK_WriteReg(R2, 0x20000184)
TA260 002:325.989 - 0.003ms returns 0
TA260 002:325.993 JLINK_WriteReg(R3, 0x00000000)
TA260 002:325.996 - 0.003ms returns 0
TA260 002:326.000 JLINK_WriteReg(R4, 0x00000000)
TA260 002:326.004 - 0.003ms returns 0
TA260 002:326.008 JLINK_WriteReg(R5, 0x00000000)
TA260 002:326.012 - 0.004ms returns 0
TA260 002:326.016 JLINK_WriteReg(R6, 0x00000000)
TA260 002:326.020 - 0.003ms returns 0
TA260 002:326.024 JLINK_WriteReg(R7, 0x00000000)
TA260 002:326.029 - 0.005ms returns 0
TA260 002:326.036 JLINK_WriteReg(R8, 0x00000000)
TA260 002:326.039 - 0.003ms returns 0
TA260 002:326.043 JLINK_WriteReg(R9, 0x20000180)
TA260 002:326.047 - 0.003ms returns 0
TA260 002:326.051 JLINK_WriteReg(R10, 0x00000000)
TA260 002:326.054 - 0.003ms returns 0
TA260 002:326.058 JLINK_WriteReg(R11, 0x00000000)
TA260 002:326.062 - 0.003ms returns 0
TA260 002:326.066 JLINK_WriteReg(R12, 0x00000000)
TA260 002:326.069 - 0.003ms returns 0
TA260 002:326.073 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:326.077 - 0.004ms returns 0
TA260 002:326.081 JLINK_WriteReg(R14, 0x20000001)
TA260 002:326.085 - 0.003ms returns 0
TA260 002:326.089 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:326.092 - 0.003ms returns 0
TA260 002:326.096 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:326.100 - 0.003ms returns 0
TA260 002:326.104 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:326.107 - 0.003ms returns 0
TA260 002:326.111 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:326.115 - 0.003ms returns 0
TA260 002:326.119 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:326.123 - 0.003ms returns 0
TA260 002:326.127 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:326.132 - 0.005ms returns 0x0000000F
TA260 002:326.136 JLINK_Go()
TA260 002:326.148   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:328.909 - 2.772ms 
TA260 002:328.934 JLINK_IsHalted()
TA260 002:329.410 - 0.476ms returns FALSE
TA260 002:329.418 JLINK_HasError()
TA260 002:330.527 JLINK_IsHalted()
TA260 002:330.980 - 0.453ms returns FALSE
TA260 002:330.988 JLINK_HasError()
TA260 002:332.526 JLINK_IsHalted()
TA260 002:333.029 - 0.503ms returns FALSE
TA260 002:333.038 JLINK_HasError()
TA260 002:334.525 JLINK_IsHalted()
TA260 002:336.910   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:337.404 - 2.878ms returns TRUE
TA260 002:337.414 JLINK_ReadReg(R15 (PC))
TA260 002:337.502 - 0.088ms returns 0x20000000
TA260 002:337.507 JLINK_ClrBPEx(BPHandle = 0x0000000F)
TA260 002:337.512 - 0.004ms returns 0x00
TA260 002:337.516 JLINK_ReadReg(R0)
TA260 002:337.520 - 0.004ms returns 0x00000000
TA260 002:337.926 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:337.940   Data:  05 12 20 77 FF F7 DA FF 94 ED 02 0A 9F ED 31 8A ...
TA260 002:337.954   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:340.580 - 2.652ms returns 0x27C
TA260 002:340.600 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:340.604   Data:  5A 68 91 E8 00 07 02 F0 40 4E 4E EA 08 05 D1 E9 ...
TA260 002:340.618   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:342.481 - 1.880ms returns 0x184
TA260 002:342.492 JLINK_HasError()
TA260 002:342.497 JLINK_WriteReg(R0, 0x08001000)
TA260 002:342.502 - 0.005ms returns 0
TA260 002:342.507 JLINK_WriteReg(R1, 0x00000400)
TA260 002:342.510 - 0.003ms returns 0
TA260 002:342.514 JLINK_WriteReg(R2, 0x20000184)
TA260 002:342.518 - 0.003ms returns 0
TA260 002:342.522 JLINK_WriteReg(R3, 0x00000000)
TA260 002:342.525 - 0.003ms returns 0
TA260 002:342.530 JLINK_WriteReg(R4, 0x00000000)
TA260 002:342.533 - 0.003ms returns 0
TA260 002:342.537 JLINK_WriteReg(R5, 0x00000000)
TA260 002:342.541 - 0.003ms returns 0
TA260 002:342.545 JLINK_WriteReg(R6, 0x00000000)
TA260 002:342.548 - 0.003ms returns 0
TA260 002:342.552 JLINK_WriteReg(R7, 0x00000000)
TA260 002:342.556 - 0.003ms returns 0
TA260 002:342.560 JLINK_WriteReg(R8, 0x00000000)
TA260 002:342.563 - 0.003ms returns 0
TA260 002:342.567 JLINK_WriteReg(R9, 0x20000180)
TA260 002:342.571 - 0.003ms returns 0
TA260 002:342.575 JLINK_WriteReg(R10, 0x00000000)
TA260 002:342.578 - 0.003ms returns 0
TA260 002:342.583 JLINK_WriteReg(R11, 0x00000000)
TA260 002:342.586 - 0.003ms returns 0
TA260 002:342.590 JLINK_WriteReg(R12, 0x00000000)
TA260 002:342.594 - 0.003ms returns 0
TA260 002:342.598 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:342.602 - 0.004ms returns 0
TA260 002:342.606 JLINK_WriteReg(R14, 0x20000001)
TA260 002:342.609 - 0.003ms returns 0
TA260 002:342.614 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:342.617 - 0.003ms returns 0
TA260 002:342.622 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:342.625 - 0.003ms returns 0
TA260 002:342.629 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:342.633 - 0.003ms returns 0
TA260 002:342.637 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:342.640 - 0.003ms returns 0
TA260 002:342.644 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:342.648 - 0.003ms returns 0
TA260 002:342.652 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:342.657 - 0.004ms returns 0x00000010
TA260 002:342.661 JLINK_Go()
TA260 002:342.670   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:345.389 - 2.727ms 
TA260 002:345.424 JLINK_IsHalted()
TA260 002:345.922 - 0.497ms returns FALSE
TA260 002:345.940 JLINK_HasError()
TA260 002:347.549 JLINK_IsHalted()
TA260 002:347.996 - 0.446ms returns FALSE
TA260 002:348.004 JLINK_HasError()
TA260 002:350.059 JLINK_IsHalted()
TA260 002:352.377   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:352.892 - 2.832ms returns TRUE
TA260 002:352.906 JLINK_ReadReg(R15 (PC))
TA260 002:352.912 - 0.006ms returns 0x20000000
TA260 002:352.950 JLINK_ClrBPEx(BPHandle = 0x00000010)
TA260 002:352.956 - 0.005ms returns 0x00
TA260 002:352.961 JLINK_ReadReg(R0)
TA260 002:352.964 - 0.003ms returns 0x00000000
TA260 002:353.337 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:353.346   Data:  20 68 00 68 C0 07 0E D0 00 F0 14 F9 40 1B 06 28 ...
TA260 002:353.358   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:355.959 - 2.621ms returns 0x27C
TA260 002:355.978 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:355.982   Data:  01 60 03 20 00 F0 8A F8 0F 20 00 F0 05 F8 00 F0 ...
TA260 002:355.995   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:357.834 - 1.856ms returns 0x184
TA260 002:357.850 JLINK_HasError()
TA260 002:357.856 JLINK_WriteReg(R0, 0x08001400)
TA260 002:357.862 - 0.005ms returns 0
TA260 002:357.866 JLINK_WriteReg(R1, 0x00000400)
TA260 002:357.869 - 0.003ms returns 0
TA260 002:357.873 JLINK_WriteReg(R2, 0x20000184)
TA260 002:357.877 - 0.003ms returns 0
TA260 002:357.881 JLINK_WriteReg(R3, 0x00000000)
TA260 002:357.884 - 0.003ms returns 0
TA260 002:357.888 JLINK_WriteReg(R4, 0x00000000)
TA260 002:357.892 - 0.003ms returns 0
TA260 002:357.902 JLINK_WriteReg(R5, 0x00000000)
TA260 002:357.906 - 0.003ms returns 0
TA260 002:357.910 JLINK_WriteReg(R6, 0x00000000)
TA260 002:357.913 - 0.003ms returns 0
TA260 002:357.917 JLINK_WriteReg(R7, 0x00000000)
TA260 002:357.921 - 0.003ms returns 0
TA260 002:357.925 JLINK_WriteReg(R8, 0x00000000)
TA260 002:357.928 - 0.003ms returns 0
TA260 002:357.932 JLINK_WriteReg(R9, 0x20000180)
TA260 002:357.935 - 0.003ms returns 0
TA260 002:357.939 JLINK_WriteReg(R10, 0x00000000)
TA260 002:357.943 - 0.003ms returns 0
TA260 002:357.947 JLINK_WriteReg(R11, 0x00000000)
TA260 002:357.950 - 0.003ms returns 0
TA260 002:357.954 JLINK_WriteReg(R12, 0x00000000)
TA260 002:357.958 - 0.003ms returns 0
TA260 002:357.962 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:357.966 - 0.004ms returns 0
TA260 002:357.970 JLINK_WriteReg(R14, 0x20000001)
TA260 002:357.974 - 0.003ms returns 0
TA260 002:357.978 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:357.981 - 0.003ms returns 0
TA260 002:357.985 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:357.989 - 0.003ms returns 0
TA260 002:357.993 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:357.996 - 0.003ms returns 0
TA260 002:358.000 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:358.004 - 0.003ms returns 0
TA260 002:358.008 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:358.011 - 0.003ms returns 0
TA260 002:358.016 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:358.020 - 0.004ms returns 0x00000011
TA260 002:358.024 JLINK_Go()
TA260 002:358.033   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:360.868 - 2.843ms 
TA260 002:360.885 JLINK_IsHalted()
TA260 002:361.367 - 0.482ms returns FALSE
TA260 002:361.374 JLINK_HasError()
TA260 002:362.568 JLINK_IsHalted()
TA260 002:363.086 - 0.518ms returns FALSE
TA260 002:363.094 JLINK_HasError()
TA260 002:364.563 JLINK_IsHalted()
TA260 002:364.984 - 0.420ms returns FALSE
TA260 002:365.010 JLINK_HasError()
TA260 002:366.069 JLINK_IsHalted()
TA260 002:368.431   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:368.928 - 2.858ms returns TRUE
TA260 002:368.936 JLINK_ReadReg(R15 (PC))
TA260 002:368.942 - 0.005ms returns 0x20000000
TA260 002:368.946 JLINK_ClrBPEx(BPHandle = 0x00000011)
TA260 002:368.950 - 0.003ms returns 0x00
TA260 002:368.956 JLINK_ReadReg(R0)
TA260 002:368.959 - 0.004ms returns 0x00000000
TA260 002:369.338 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:369.348   Data:  B0 F5 7A 7F F6 D9 0E E0 01 20 70 60 FF F7 12 FF ...
TA260 002:369.359   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:372.044 - 2.705ms returns 0x27C
TA260 002:372.058 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:372.062   Data:  00 28 04 BF 01 20 70 47 2D E9 F0 41 82 B0 04 46 ...
TA260 002:372.072   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:374.013 - 1.955ms returns 0x184
TA260 002:374.023 JLINK_HasError()
TA260 002:374.028 JLINK_WriteReg(R0, 0x08001800)
TA260 002:374.034 - 0.005ms returns 0
TA260 002:374.038 JLINK_WriteReg(R1, 0x00000400)
TA260 002:374.042 - 0.003ms returns 0
TA260 002:374.046 JLINK_WriteReg(R2, 0x20000184)
TA260 002:374.049 - 0.003ms returns 0
TA260 002:374.054 JLINK_WriteReg(R3, 0x00000000)
TA260 002:374.057 - 0.003ms returns 0
TA260 002:374.061 JLINK_WriteReg(R4, 0x00000000)
TA260 002:374.065 - 0.003ms returns 0
TA260 002:374.069 JLINK_WriteReg(R5, 0x00000000)
TA260 002:374.072 - 0.003ms returns 0
TA260 002:374.076 JLINK_WriteReg(R6, 0x00000000)
TA260 002:374.080 - 0.003ms returns 0
TA260 002:374.084 JLINK_WriteReg(R7, 0x00000000)
TA260 002:374.087 - 0.003ms returns 0
TA260 002:374.092 JLINK_WriteReg(R8, 0x00000000)
TA260 002:374.095 - 0.003ms returns 0
TA260 002:374.099 JLINK_WriteReg(R9, 0x20000180)
TA260 002:374.102 - 0.003ms returns 0
TA260 002:374.106 JLINK_WriteReg(R10, 0x00000000)
TA260 002:374.110 - 0.003ms returns 0
TA260 002:374.114 JLINK_WriteReg(R11, 0x00000000)
TA260 002:374.117 - 0.003ms returns 0
TA260 002:374.121 JLINK_WriteReg(R12, 0x00000000)
TA260 002:374.126 - 0.004ms returns 0
TA260 002:374.130 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:374.139 - 0.008ms returns 0
TA260 002:374.144 JLINK_WriteReg(R14, 0x20000001)
TA260 002:374.148 - 0.003ms returns 0
TA260 002:374.152 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:374.156 - 0.003ms returns 0
TA260 002:374.160 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:374.163 - 0.003ms returns 0
TA260 002:374.167 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:374.171 - 0.003ms returns 0
TA260 002:374.175 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:374.178 - 0.003ms returns 0
TA260 002:374.183 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:374.186 - 0.003ms returns 0
TA260 002:374.190 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:374.195 - 0.004ms returns 0x00000012
TA260 002:374.199 JLINK_Go()
TA260 002:374.208   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:377.024 - 2.823ms 
TA260 002:377.050 JLINK_IsHalted()
TA260 002:377.510 - 0.459ms returns FALSE
TA260 002:377.526 JLINK_HasError()
TA260 002:379.582 JLINK_IsHalted()
TA260 002:380.105 - 0.523ms returns FALSE
TA260 002:380.113 JLINK_HasError()
TA260 002:381.585 JLINK_IsHalted()
TA260 002:383.975   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:384.468 - 2.883ms returns TRUE
TA260 002:384.476 JLINK_ReadReg(R15 (PC))
TA260 002:384.481 - 0.005ms returns 0x20000000
TA260 002:384.485 JLINK_ClrBPEx(BPHandle = 0x00000012)
TA260 002:384.489 - 0.003ms returns 0x00
TA260 002:384.493 JLINK_ReadReg(R0)
TA260 002:384.497 - 0.003ms returns 0x00000000
TA260 002:385.068 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:385.081   Data:  02 28 F7 D9 B0 E0 30 6C 5F EA C0 08 0A D4 00 20 ...
TA260 002:385.096   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:387.768 - 2.698ms returns 0x27C
TA260 002:387.795 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:387.799   Data:  02 AD 02 90 4F F0 02 08 4F F0 03 09 0C 27 04 F5 ...
TA260 002:387.814   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:389.764 - 1.968ms returns 0x184
TA260 002:389.777 JLINK_HasError()
TA260 002:389.783 JLINK_WriteReg(R0, 0x08001C00)
TA260 002:389.789 - 0.006ms returns 0
TA260 002:389.794 JLINK_WriteReg(R1, 0x00000400)
TA260 002:389.798 - 0.003ms returns 0
TA260 002:389.802 JLINK_WriteReg(R2, 0x20000184)
TA260 002:389.805 - 0.003ms returns 0
TA260 002:389.810 JLINK_WriteReg(R3, 0x00000000)
TA260 002:389.813 - 0.003ms returns 0
TA260 002:389.817 JLINK_WriteReg(R4, 0x00000000)
TA260 002:389.820 - 0.003ms returns 0
TA260 002:389.824 JLINK_WriteReg(R5, 0x00000000)
TA260 002:389.828 - 0.003ms returns 0
TA260 002:389.832 JLINK_WriteReg(R6, 0x00000000)
TA260 002:389.835 - 0.003ms returns 0
TA260 002:389.840 JLINK_WriteReg(R7, 0x00000000)
TA260 002:389.843 - 0.003ms returns 0
TA260 002:389.847 JLINK_WriteReg(R8, 0x00000000)
TA260 002:389.850 - 0.003ms returns 0
TA260 002:389.854 JLINK_WriteReg(R9, 0x20000180)
TA260 002:389.858 - 0.003ms returns 0
TA260 002:389.862 JLINK_WriteReg(R10, 0x00000000)
TA260 002:389.865 - 0.003ms returns 0
TA260 002:389.869 JLINK_WriteReg(R11, 0x00000000)
TA260 002:389.873 - 0.003ms returns 0
TA260 002:389.877 JLINK_WriteReg(R12, 0x00000000)
TA260 002:389.880 - 0.003ms returns 0
TA260 002:389.884 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:389.888 - 0.004ms returns 0
TA260 002:389.892 JLINK_WriteReg(R14, 0x20000001)
TA260 002:389.896 - 0.003ms returns 0
TA260 002:389.900 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:389.903 - 0.003ms returns 0
TA260 002:389.907 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:389.911 - 0.003ms returns 0
TA260 002:389.915 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:389.918 - 0.003ms returns 0
TA260 002:389.922 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:389.926 - 0.003ms returns 0
TA260 002:389.930 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:389.933 - 0.003ms returns 0
TA260 002:389.938 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:389.943 - 0.005ms returns 0x00000013
TA260 002:389.947 JLINK_Go()
TA260 002:389.956   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:392.758 - 2.810ms 
TA260 002:392.773 JLINK_IsHalted()
TA260 002:393.229 - 0.455ms returns FALSE
TA260 002:393.241 JLINK_HasError()
TA260 002:395.093 JLINK_IsHalted()
TA260 002:395.659 - 0.565ms returns FALSE
TA260 002:395.680 JLINK_HasError()
TA260 002:397.611 JLINK_IsHalted()
TA260 002:399.987   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:400.494 - 2.882ms returns TRUE
TA260 002:400.503 JLINK_ReadReg(R15 (PC))
TA260 002:400.508 - 0.005ms returns 0x20000000
TA260 002:400.513 JLINK_ClrBPEx(BPHandle = 0x00000013)
TA260 002:400.517 - 0.003ms returns 0x00
TA260 002:400.521 JLINK_ReadReg(R0)
TA260 002:400.525 - 0.003ms returns 0x00000000
TA260 002:400.909 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:400.918   Data:  BD E8 B0 40 00 F0 5A BB 81 6B 0A 68 52 68 93 B2 ...
TA260 002:400.928   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:403.569 - 2.660ms returns 0x27C
TA260 002:403.591 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:403.596   Data:  00 42 C4 F2 00 02 00 21 90 42 CD E9 04 11 CD E9 ...
TA260 002:403.608   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:405.546 - 1.955ms returns 0x184
TA260 002:405.579 JLINK_HasError()
TA260 002:405.585 JLINK_WriteReg(R0, 0x08002000)
TA260 002:405.592 - 0.007ms returns 0
TA260 002:405.640 JLINK_WriteReg(R1, 0x00000400)
TA260 002:405.645 - 0.005ms returns 0
TA260 002:405.649 JLINK_WriteReg(R2, 0x20000184)
TA260 002:405.653 - 0.004ms returns 0
TA260 002:405.658 JLINK_WriteReg(R3, 0x00000000)
TA260 002:405.661 - 0.003ms returns 0
TA260 002:405.665 JLINK_WriteReg(R4, 0x00000000)
TA260 002:405.669 - 0.003ms returns 0
TA260 002:405.673 JLINK_WriteReg(R5, 0x00000000)
TA260 002:405.676 - 0.003ms returns 0
TA260 002:405.680 JLINK_WriteReg(R6, 0x00000000)
TA260 002:405.684 - 0.003ms returns 0
TA260 002:405.688 JLINK_WriteReg(R7, 0x00000000)
TA260 002:405.691 - 0.003ms returns 0
TA260 002:405.696 JLINK_WriteReg(R8, 0x00000000)
TA260 002:405.699 - 0.003ms returns 0
TA260 002:405.703 JLINK_WriteReg(R9, 0x20000180)
TA260 002:405.706 - 0.003ms returns 0
TA260 002:405.711 JLINK_WriteReg(R10, 0x00000000)
TA260 002:405.714 - 0.003ms returns 0
TA260 002:405.718 JLINK_WriteReg(R11, 0x00000000)
TA260 002:405.722 - 0.003ms returns 0
TA260 002:405.726 JLINK_WriteReg(R12, 0x00000000)
TA260 002:405.729 - 0.003ms returns 0
TA260 002:405.733 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:405.738 - 0.004ms returns 0
TA260 002:405.742 JLINK_WriteReg(R14, 0x20000001)
TA260 002:405.745 - 0.003ms returns 0
TA260 002:405.749 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:405.753 - 0.003ms returns 0
TA260 002:405.757 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:405.760 - 0.003ms returns 0
TA260 002:405.765 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:405.768 - 0.003ms returns 0
TA260 002:405.772 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:405.775 - 0.003ms returns 0
TA260 002:405.779 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:405.783 - 0.003ms returns 0
TA260 002:405.788 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:405.794 - 0.005ms returns 0x00000014
TA260 002:405.798 JLINK_Go()
TA260 002:405.809   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:408.668 - 2.869ms 
TA260 002:408.693 JLINK_IsHalted()
TA260 002:409.136 - 0.442ms returns FALSE
TA260 002:409.150 JLINK_HasError()
TA260 002:412.753 JLINK_IsHalted()
TA260 002:415.108   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:415.664 - 2.909ms returns TRUE
TA260 002:415.685 JLINK_ReadReg(R15 (PC))
TA260 002:415.693 - 0.007ms returns 0x20000000
TA260 002:415.698 JLINK_ClrBPEx(BPHandle = 0x00000014)
TA260 002:415.703 - 0.005ms returns 0x00
TA260 002:415.708 JLINK_ReadReg(R0)
TA260 002:415.712 - 0.004ms returns 0x00000000
TA260 002:416.173 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:416.183   Data:  40 F2 D0 00 C2 F2 00 00 C1 88 7E 29 12 D8 C3 78 ...
TA260 002:416.196   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:418.778 - 2.605ms returns 0x27C
TA260 002:418.800 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:418.804   Data:  08 00 48 45 F2 D9 00 BF 20 68 50 E8 03 0F 21 68 ...
TA260 002:418.818   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:420.761 - 1.960ms returns 0x184
TA260 002:420.771 JLINK_HasError()
TA260 002:420.776 JLINK_WriteReg(R0, 0x08002400)
TA260 002:420.782 - 0.005ms returns 0
TA260 002:420.786 JLINK_WriteReg(R1, 0x00000400)
TA260 002:420.789 - 0.003ms returns 0
TA260 002:420.794 JLINK_WriteReg(R2, 0x20000184)
TA260 002:420.797 - 0.003ms returns 0
TA260 002:420.801 JLINK_WriteReg(R3, 0x00000000)
TA260 002:420.804 - 0.003ms returns 0
TA260 002:420.809 JLINK_WriteReg(R4, 0x00000000)
TA260 002:420.813 - 0.004ms returns 0
TA260 002:420.817 JLINK_WriteReg(R5, 0x00000000)
TA260 002:420.820 - 0.003ms returns 0
TA260 002:420.824 JLINK_WriteReg(R6, 0x00000000)
TA260 002:420.828 - 0.003ms returns 0
TA260 002:420.832 JLINK_WriteReg(R7, 0x00000000)
TA260 002:420.836 - 0.003ms returns 0
TA260 002:420.840 JLINK_WriteReg(R8, 0x00000000)
TA260 002:420.843 - 0.003ms returns 0
TA260 002:420.847 JLINK_WriteReg(R9, 0x20000180)
TA260 002:420.851 - 0.003ms returns 0
TA260 002:420.855 JLINK_WriteReg(R10, 0x00000000)
TA260 002:420.859 - 0.003ms returns 0
TA260 002:420.863 JLINK_WriteReg(R11, 0x00000000)
TA260 002:420.866 - 0.003ms returns 0
TA260 002:420.870 JLINK_WriteReg(R12, 0x00000000)
TA260 002:420.874 - 0.003ms returns 0
TA260 002:420.878 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:420.882 - 0.004ms returns 0
TA260 002:420.886 JLINK_WriteReg(R14, 0x20000001)
TA260 002:420.890 - 0.003ms returns 0
TA260 002:420.894 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:420.897 - 0.003ms returns 0
TA260 002:420.902 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:420.905 - 0.003ms returns 0
TA260 002:420.909 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:420.913 - 0.003ms returns 0
TA260 002:420.917 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:420.920 - 0.003ms returns 0
TA260 002:420.924 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:420.928 - 0.003ms returns 0
TA260 002:420.932 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:420.937 - 0.004ms returns 0x00000015
TA260 002:420.941 JLINK_Go()
TA260 002:420.950   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:423.741 - 2.798ms 
TA260 002:423.759 JLINK_IsHalted()
TA260 002:424.285 - 0.525ms returns FALSE
TA260 002:424.299 JLINK_HasError()
TA260 002:426.270 JLINK_IsHalted()
TA260 002:426.790 - 0.519ms returns FALSE
TA260 002:426.807 JLINK_HasError()
TA260 002:428.770 JLINK_IsHalted()
TA260 002:431.070   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:431.573 - 2.803ms returns TRUE
TA260 002:431.591 JLINK_ReadReg(R15 (PC))
TA260 002:431.597 - 0.006ms returns 0x20000000
TA260 002:431.636 JLINK_ClrBPEx(BPHandle = 0x00000015)
TA260 002:431.642 - 0.005ms returns 0x00
TA260 002:431.648 JLINK_ReadReg(R0)
TA260 002:431.652 - 0.003ms returns 0x00000000
TA260 002:432.057 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:432.068   Data:  06 22 40 F2 04 13 CD E9 03 21 11 22 4F F0 20 4C ...
TA260 002:432.080   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:434.770 - 2.712ms returns 0x27C
TA260 002:434.788 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:434.792   Data:  C0 E9 05 C3 C3 61 FF F7 C7 FB 00 28 08 BF 80 BD ...
TA260 002:434.802   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:436.775 - 1.986ms returns 0x184
TA260 002:436.804 JLINK_HasError()
TA260 002:436.810 JLINK_WriteReg(R0, 0x08002800)
TA260 002:436.817 - 0.007ms returns 0
TA260 002:436.822 JLINK_WriteReg(R1, 0x00000400)
TA260 002:436.825 - 0.003ms returns 0
TA260 002:436.829 JLINK_WriteReg(R2, 0x20000184)
TA260 002:436.832 - 0.003ms returns 0
TA260 002:436.837 JLINK_WriteReg(R3, 0x00000000)
TA260 002:436.840 - 0.003ms returns 0
TA260 002:436.844 JLINK_WriteReg(R4, 0x00000000)
TA260 002:436.848 - 0.003ms returns 0
TA260 002:436.852 JLINK_WriteReg(R5, 0x00000000)
TA260 002:436.855 - 0.003ms returns 0
TA260 002:436.860 JLINK_WriteReg(R6, 0x00000000)
TA260 002:436.863 - 0.003ms returns 0
TA260 002:436.867 JLINK_WriteReg(R7, 0x00000000)
TA260 002:436.871 - 0.003ms returns 0
TA260 002:436.875 JLINK_WriteReg(R8, 0x00000000)
TA260 002:436.878 - 0.003ms returns 0
TA260 002:436.883 JLINK_WriteReg(R9, 0x20000180)
TA260 002:436.894 - 0.011ms returns 0
TA260 002:436.898 JLINK_WriteReg(R10, 0x00000000)
TA260 002:436.902 - 0.003ms returns 0
TA260 002:436.906 JLINK_WriteReg(R11, 0x00000000)
TA260 002:436.909 - 0.003ms returns 0
TA260 002:436.913 JLINK_WriteReg(R12, 0x00000000)
TA260 002:436.916 - 0.003ms returns 0
TA260 002:436.921 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:436.925 - 0.004ms returns 0
TA260 002:436.930 JLINK_WriteReg(R14, 0x20000001)
TA260 002:436.933 - 0.003ms returns 0
TA260 002:436.937 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:436.941 - 0.003ms returns 0
TA260 002:436.945 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:436.948 - 0.003ms returns 0
TA260 002:436.952 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:436.956 - 0.003ms returns 0
TA260 002:436.960 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:436.964 - 0.003ms returns 0
TA260 002:436.968 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:436.971 - 0.003ms returns 0
TA260 002:436.976 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:436.981 - 0.005ms returns 0x00000016
TA260 002:436.985 JLINK_Go()
TA260 002:436.998   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:439.879 - 2.893ms 
TA260 002:439.894 JLINK_IsHalted()
TA260 002:440.391 - 0.497ms returns FALSE
TA260 002:440.397 JLINK_HasError()
TA260 002:442.285 JLINK_IsHalted()
TA260 002:442.799 - 0.513ms returns FALSE
TA260 002:442.809 JLINK_HasError()
TA260 002:444.281 JLINK_IsHalted()
TA260 002:446.690   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:447.261 - 2.979ms returns TRUE
TA260 002:447.278 JLINK_ReadReg(R15 (PC))
TA260 002:447.284 - 0.006ms returns 0x20000000
TA260 002:447.289 JLINK_ClrBPEx(BPHandle = 0x00000016)
TA260 002:447.294 - 0.004ms returns 0x00
TA260 002:447.298 JLINK_ReadReg(R0)
TA260 002:447.302 - 0.003ms returns 0x00000000
TA260 002:447.693 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:447.700   Data:  C8 F8 00 40 08 E0 00 BF E5 1C 9D 42 06 D2 0C EB ...
TA260 002:447.711   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:450.305 - 2.611ms returns 0x27C
TA260 002:450.330 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:450.334   Data:  FD F7 B2 F9 48 F2 1F 51 C5 F2 EB 11 A0 FB 01 23 ...
TA260 002:450.348   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:452.218 - 1.888ms returns 0x184
TA260 002:452.231 JLINK_HasError()
TA260 002:452.237 JLINK_WriteReg(R0, 0x08002C00)
TA260 002:452.243 - 0.006ms returns 0
TA260 002:452.248 JLINK_WriteReg(R1, 0x00000400)
TA260 002:452.251 - 0.003ms returns 0
TA260 002:452.255 JLINK_WriteReg(R2, 0x20000184)
TA260 002:452.259 - 0.003ms returns 0
TA260 002:452.263 JLINK_WriteReg(R3, 0x00000000)
TA260 002:452.266 - 0.003ms returns 0
TA260 002:452.270 JLINK_WriteReg(R4, 0x00000000)
TA260 002:452.274 - 0.003ms returns 0
TA260 002:452.278 JLINK_WriteReg(R5, 0x00000000)
TA260 002:452.281 - 0.003ms returns 0
TA260 002:452.286 JLINK_WriteReg(R6, 0x00000000)
TA260 002:452.289 - 0.003ms returns 0
TA260 002:452.293 JLINK_WriteReg(R7, 0x00000000)
TA260 002:452.297 - 0.003ms returns 0
TA260 002:452.301 JLINK_WriteReg(R8, 0x00000000)
TA260 002:452.304 - 0.003ms returns 0
TA260 002:452.308 JLINK_WriteReg(R9, 0x20000180)
TA260 002:452.312 - 0.003ms returns 0
TA260 002:452.316 JLINK_WriteReg(R10, 0x00000000)
TA260 002:452.319 - 0.003ms returns 0
TA260 002:452.323 JLINK_WriteReg(R11, 0x00000000)
TA260 002:452.327 - 0.003ms returns 0
TA260 002:452.331 JLINK_WriteReg(R12, 0x00000000)
TA260 002:452.334 - 0.003ms returns 0
TA260 002:452.338 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:452.342 - 0.004ms returns 0
TA260 002:452.346 JLINK_WriteReg(R14, 0x20000001)
TA260 002:452.350 - 0.003ms returns 0
TA260 002:452.354 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:452.357 - 0.003ms returns 0
TA260 002:452.362 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:452.366 - 0.004ms returns 0
TA260 002:452.370 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:452.374 - 0.003ms returns 0
TA260 002:452.378 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:452.381 - 0.003ms returns 0
TA260 002:452.391 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:452.396 - 0.005ms returns 0
TA260 002:452.401 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:452.406 - 0.004ms returns 0x00000017
TA260 002:452.410 JLINK_Go()
TA260 002:452.424   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:455.244 - 2.833ms 
TA260 002:455.257 JLINK_IsHalted()
TA260 002:455.819 - 0.562ms returns FALSE
TA260 002:455.836 JLINK_HasError()
TA260 002:457.809 JLINK_IsHalted()
TA260 002:458.278 - 0.468ms returns FALSE
TA260 002:458.285 JLINK_HasError()
TA260 002:459.803 JLINK_IsHalted()
TA260 002:462.203   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:462.687 - 2.883ms returns TRUE
TA260 002:462.696 JLINK_ReadReg(R15 (PC))
TA260 002:462.701 - 0.005ms returns 0x20000000
TA260 002:462.706 JLINK_ClrBPEx(BPHandle = 0x00000017)
TA260 002:462.710 - 0.003ms returns 0x00
TA260 002:462.714 JLINK_ReadReg(R0)
TA260 002:462.718 - 0.003ms returns 0x00000000
TA260 002:463.159 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:463.172   Data:  00 00 00 44 00 00 00 C4 B0 B5 2D ED 08 8B 40 F2 ...
TA260 002:463.186   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:465.784 - 2.624ms returns 0x27C
TA260 002:465.832 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:465.837   Data:  55 F8 04 1B 40 F8 04 1B 94 E8 0F 00 FF F7 5A FA ...
TA260 002:465.849   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:467.762 - 1.929ms returns 0x184
TA260 002:467.776 JLINK_HasError()
TA260 002:467.782 JLINK_WriteReg(R0, 0x08003000)
TA260 002:467.788 - 0.006ms returns 0
TA260 002:467.792 JLINK_WriteReg(R1, 0x00000400)
TA260 002:467.796 - 0.003ms returns 0
TA260 002:467.800 JLINK_WriteReg(R2, 0x20000184)
TA260 002:467.803 - 0.003ms returns 0
TA260 002:467.807 JLINK_WriteReg(R3, 0x00000000)
TA260 002:467.811 - 0.003ms returns 0
TA260 002:467.815 JLINK_WriteReg(R4, 0x00000000)
TA260 002:467.818 - 0.003ms returns 0
TA260 002:467.822 JLINK_WriteReg(R5, 0x00000000)
TA260 002:467.825 - 0.003ms returns 0
TA260 002:467.829 JLINK_WriteReg(R6, 0x00000000)
TA260 002:467.833 - 0.003ms returns 0
TA260 002:467.837 JLINK_WriteReg(R7, 0x00000000)
TA260 002:467.840 - 0.003ms returns 0
TA260 002:467.844 JLINK_WriteReg(R8, 0x00000000)
TA260 002:467.848 - 0.004ms returns 0
TA260 002:467.852 JLINK_WriteReg(R9, 0x20000180)
TA260 002:467.856 - 0.003ms returns 0
TA260 002:467.860 JLINK_WriteReg(R10, 0x00000000)
TA260 002:467.864 - 0.003ms returns 0
TA260 002:467.868 JLINK_WriteReg(R11, 0x00000000)
TA260 002:467.871 - 0.003ms returns 0
TA260 002:467.875 JLINK_WriteReg(R12, 0x00000000)
TA260 002:467.879 - 0.003ms returns 0
TA260 002:467.883 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:467.887 - 0.003ms returns 0
TA260 002:467.891 JLINK_WriteReg(R14, 0x20000001)
TA260 002:467.894 - 0.003ms returns 0
TA260 002:467.899 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:467.902 - 0.003ms returns 0
TA260 002:467.906 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:467.910 - 0.003ms returns 0
TA260 002:467.914 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:467.917 - 0.003ms returns 0
TA260 002:467.921 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:467.924 - 0.003ms returns 0
TA260 002:467.928 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:467.932 - 0.003ms returns 0
TA260 002:467.936 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:467.941 - 0.005ms returns 0x00000018
TA260 002:467.945 JLINK_Go()
TA260 002:467.955   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:470.786 - 2.840ms 
TA260 002:470.806 JLINK_IsHalted()
TA260 002:471.264 - 0.457ms returns FALSE
TA260 002:471.272 JLINK_HasError()
TA260 002:473.112 JLINK_IsHalted()
TA260 002:473.660 - 0.547ms returns FALSE
TA260 002:473.667 JLINK_HasError()
TA260 002:475.121 JLINK_IsHalted()
TA260 002:477.480   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:477.978 - 2.856ms returns TRUE
TA260 002:477.993 JLINK_ReadReg(R15 (PC))
TA260 002:477.999 - 0.006ms returns 0x20000000
TA260 002:478.028 JLINK_ClrBPEx(BPHandle = 0x00000018)
TA260 002:478.033 - 0.004ms returns 0x00
TA260 002:478.052 JLINK_ReadReg(R0)
TA260 002:478.079 - 0.026ms returns 0x00000000
TA260 002:478.464 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:478.472   Data:  CA 71 00 22 00 23 FD F7 7B FE 10 B0 BD E8 B0 40 ...
TA260 002:478.484   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:481.135 - 2.670ms returns 0x27C
TA260 002:481.162 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:481.167   Data:  8C 00 9A 00 68 68 41 F2 59 71 01 30 CD F2 B7 11 ...
TA260 002:481.180   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:483.027 - 1.864ms returns 0x184
TA260 002:483.044 JLINK_HasError()
TA260 002:483.084 JLINK_WriteReg(R0, 0x08003400)
TA260 002:483.091 - 0.006ms returns 0
TA260 002:483.095 JLINK_WriteReg(R1, 0x00000400)
TA260 002:483.098 - 0.003ms returns 0
TA260 002:483.103 JLINK_WriteReg(R2, 0x20000184)
TA260 002:483.106 - 0.003ms returns 0
TA260 002:483.110 JLINK_WriteReg(R3, 0x00000000)
TA260 002:483.114 - 0.003ms returns 0
TA260 002:483.118 JLINK_WriteReg(R4, 0x00000000)
TA260 002:483.122 - 0.004ms returns 0
TA260 002:483.127 JLINK_WriteReg(R5, 0x00000000)
TA260 002:483.131 - 0.003ms returns 0
TA260 002:483.135 JLINK_WriteReg(R6, 0x00000000)
TA260 002:483.138 - 0.003ms returns 0
TA260 002:483.142 JLINK_WriteReg(R7, 0x00000000)
TA260 002:483.146 - 0.003ms returns 0
TA260 002:483.150 JLINK_WriteReg(R8, 0x00000000)
TA260 002:483.153 - 0.003ms returns 0
TA260 002:483.157 JLINK_WriteReg(R9, 0x20000180)
TA260 002:483.161 - 0.003ms returns 0
TA260 002:483.165 JLINK_WriteReg(R10, 0x00000000)
TA260 002:483.168 - 0.003ms returns 0
TA260 002:483.172 JLINK_WriteReg(R11, 0x00000000)
TA260 002:483.176 - 0.003ms returns 0
TA260 002:483.180 JLINK_WriteReg(R12, 0x00000000)
TA260 002:483.183 - 0.003ms returns 0
TA260 002:483.190 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:483.195 - 0.005ms returns 0
TA260 002:483.199 JLINK_WriteReg(R14, 0x20000001)
TA260 002:483.203 - 0.003ms returns 0
TA260 002:483.207 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:483.210 - 0.004ms returns 0
TA260 002:483.215 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:483.218 - 0.003ms returns 0
TA260 002:483.222 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:483.225 - 0.003ms returns 0
TA260 002:483.229 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:483.233 - 0.003ms returns 0
TA260 002:483.237 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:483.240 - 0.003ms returns 0
TA260 002:483.245 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:483.249 - 0.004ms returns 0x00000019
TA260 002:483.254 JLINK_Go()
TA260 002:483.262   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:486.087 - 2.832ms 
TA260 002:486.120 JLINK_IsHalted()
TA260 002:486.654 - 0.533ms returns FALSE
TA260 002:486.663 JLINK_HasError()
TA260 002:488.138 JLINK_IsHalted()
TA260 002:488.580 - 0.441ms returns FALSE
TA260 002:488.586 JLINK_HasError()
TA260 002:490.132 JLINK_IsHalted()
TA260 002:492.480   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:492.971 - 2.838ms returns TRUE
TA260 002:492.980 JLINK_ReadReg(R15 (PC))
TA260 002:492.986 - 0.005ms returns 0x20000000
TA260 002:492.990 JLINK_ClrBPEx(BPHandle = 0x00000019)
TA260 002:492.995 - 0.004ms returns 0x00
TA260 002:492.999 JLINK_ReadReg(R0)
TA260 002:493.003 - 0.003ms returns 0x00000000
TA260 002:493.405 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:493.414   Data:  04 2B 41 F8 04 2B 50 F8 04 2B 41 F8 04 2B 50 F8 ...
TA260 002:493.425   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:496.048 - 2.642ms returns 0x27C
TA260 002:496.076 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:496.081   Data:  FF F7 00 FB A0 78 81 07 F7 D5 FF F7 27 FC A0 78 ...
TA260 002:496.095   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:497.933 - 1.856ms returns 0x184
TA260 002:497.945 JLINK_HasError()
TA260 002:497.951 JLINK_WriteReg(R0, 0x08003800)
TA260 002:497.957 - 0.006ms returns 0
TA260 002:497.962 JLINK_WriteReg(R1, 0x00000400)
TA260 002:497.965 - 0.003ms returns 0
TA260 002:497.969 JLINK_WriteReg(R2, 0x20000184)
TA260 002:497.973 - 0.003ms returns 0
TA260 002:497.977 JLINK_WriteReg(R3, 0x00000000)
TA260 002:497.986 - 0.009ms returns 0
TA260 002:497.993 JLINK_WriteReg(R4, 0x00000000)
TA260 002:497.996 - 0.003ms returns 0
TA260 002:498.000 JLINK_WriteReg(R5, 0x00000000)
TA260 002:498.004 - 0.003ms returns 0
TA260 002:498.008 JLINK_WriteReg(R6, 0x00000000)
TA260 002:498.011 - 0.003ms returns 0
TA260 002:498.016 JLINK_WriteReg(R7, 0x00000000)
TA260 002:498.019 - 0.003ms returns 0
TA260 002:498.024 JLINK_WriteReg(R8, 0x00000000)
TA260 002:498.027 - 0.003ms returns 0
TA260 002:498.031 JLINK_WriteReg(R9, 0x20000180)
TA260 002:498.035 - 0.003ms returns 0
TA260 002:498.039 JLINK_WriteReg(R10, 0x00000000)
TA260 002:498.042 - 0.003ms returns 0
TA260 002:498.046 JLINK_WriteReg(R11, 0x00000000)
TA260 002:498.050 - 0.003ms returns 0
TA260 002:498.054 JLINK_WriteReg(R12, 0x00000000)
TA260 002:498.057 - 0.003ms returns 0
TA260 002:498.062 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:498.066 - 0.004ms returns 0
TA260 002:498.070 JLINK_WriteReg(R14, 0x20000001)
TA260 002:498.073 - 0.003ms returns 0
TA260 002:498.078 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:498.081 - 0.003ms returns 0
TA260 002:498.085 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:498.088 - 0.003ms returns 0
TA260 002:498.093 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:498.096 - 0.003ms returns 0
TA260 002:498.100 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:498.103 - 0.003ms returns 0
TA260 002:498.108 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:498.111 - 0.003ms returns 0
TA260 002:498.116 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:498.120 - 0.004ms returns 0x0000001A
TA260 002:498.124 JLINK_Go()
TA260 002:498.133   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:500.892 - 2.767ms 
TA260 002:500.917 JLINK_IsHalted()
TA260 002:501.420 - 0.503ms returns FALSE
TA260 002:501.426 JLINK_HasError()
TA260 002:502.643 JLINK_IsHalted()
TA260 002:503.176 - 0.532ms returns FALSE
TA260 002:503.186 JLINK_HasError()
TA260 002:504.682 JLINK_IsHalted()
TA260 002:505.214 - 0.530ms returns FALSE
TA260 002:505.232 JLINK_HasError()
TA260 002:507.155 JLINK_IsHalted()
TA260 002:509.475   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:509.996 - 2.840ms returns TRUE
TA260 002:510.022 JLINK_ReadReg(R15 (PC))
TA260 002:510.028 - 0.006ms returns 0x20000000
TA260 002:510.070 JLINK_ClrBPEx(BPHandle = 0x0000001A)
TA260 002:510.084 - 0.013ms returns 0x00
TA260 002:510.090 JLINK_ReadReg(R0)
TA260 002:510.094 - 0.004ms returns 0x00000000
TA260 002:510.484 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:510.492   Data:  50 F8 04 2B 41 F8 04 2B 94 E8 0F 00 FE F7 58 FD ...
TA260 002:510.503   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:513.126 - 2.641ms returns 0x27C
TA260 002:513.141 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:513.146   Data:  94 B0 DD E9 23 50 25 9C DD F8 88 A0 25 F0 00 46 ...
TA260 002:513.156   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:515.042 - 1.900ms returns 0x184
TA260 002:515.063 JLINK_HasError()
TA260 002:515.070 JLINK_WriteReg(R0, 0x08003C00)
TA260 002:515.076 - 0.006ms returns 0
TA260 002:515.081 JLINK_WriteReg(R1, 0x00000400)
TA260 002:515.084 - 0.003ms returns 0
TA260 002:515.089 JLINK_WriteReg(R2, 0x20000184)
TA260 002:515.092 - 0.003ms returns 0
TA260 002:515.096 JLINK_WriteReg(R3, 0x00000000)
TA260 002:515.100 - 0.003ms returns 0
TA260 002:515.104 JLINK_WriteReg(R4, 0x00000000)
TA260 002:515.107 - 0.003ms returns 0
TA260 002:515.111 JLINK_WriteReg(R5, 0x00000000)
TA260 002:515.115 - 0.003ms returns 0
TA260 002:515.119 JLINK_WriteReg(R6, 0x00000000)
TA260 002:515.122 - 0.003ms returns 0
TA260 002:515.126 JLINK_WriteReg(R7, 0x00000000)
TA260 002:515.130 - 0.003ms returns 0
TA260 002:515.134 JLINK_WriteReg(R8, 0x00000000)
TA260 002:515.137 - 0.003ms returns 0
TA260 002:515.142 JLINK_WriteReg(R9, 0x20000180)
TA260 002:515.146 - 0.003ms returns 0
TA260 002:515.150 JLINK_WriteReg(R10, 0x00000000)
TA260 002:515.154 - 0.003ms returns 0
TA260 002:515.158 JLINK_WriteReg(R11, 0x00000000)
TA260 002:515.161 - 0.003ms returns 0
TA260 002:515.165 JLINK_WriteReg(R12, 0x00000000)
TA260 002:515.180 - 0.014ms returns 0
TA260 002:515.185 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:515.190 - 0.005ms returns 0
TA260 002:515.194 JLINK_WriteReg(R14, 0x20000001)
TA260 002:515.198 - 0.003ms returns 0
TA260 002:515.202 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:515.206 - 0.003ms returns 0
TA260 002:515.210 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:515.213 - 0.003ms returns 0
TA260 002:515.218 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:515.221 - 0.003ms returns 0
TA260 002:515.225 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:515.229 - 0.004ms returns 0
TA260 002:515.233 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:515.236 - 0.003ms returns 0
TA260 002:515.241 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:515.246 - 0.005ms returns 0x0000001B
TA260 002:515.251 JLINK_Go()
TA260 002:515.264   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:518.170 - 2.919ms 
TA260 002:518.211 JLINK_IsHalted()
TA260 002:518.740 - 0.529ms returns FALSE
TA260 002:518.747 JLINK_HasError()
TA260 002:520.668 JLINK_IsHalted()
TA260 002:521.141 - 0.472ms returns FALSE
TA260 002:521.155 JLINK_HasError()
TA260 002:522.675 JLINK_IsHalted()
TA260 002:525.058   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:525.546 - 2.870ms returns TRUE
TA260 002:525.561 JLINK_ReadReg(R15 (PC))
TA260 002:525.567 - 0.006ms returns 0x20000000
TA260 002:525.572 JLINK_ClrBPEx(BPHandle = 0x0000001B)
TA260 002:525.576 - 0.004ms returns 0x00
TA260 002:525.580 JLINK_ReadReg(R0)
TA260 002:525.584 - 0.003ms returns 0x00000000
TA260 002:526.146 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:526.158   Data:  00 41 6F E0 AE 48 78 44 90 ED 00 0B CC E7 D8 45 ...
TA260 002:526.172   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:528.805 - 2.659ms returns 0x27C
TA260 002:528.831 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:528.835   Data:  53 EC 11 2B FC F7 AC F8 9F ED 1E 1B 53 EC 11 2B ...
TA260 002:528.849   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:530.683 - 1.851ms returns 0x184
TA260 002:530.690 JLINK_HasError()
TA260 002:530.695 JLINK_WriteReg(R0, 0x08004000)
TA260 002:530.700 - 0.005ms returns 0
TA260 002:530.705 JLINK_WriteReg(R1, 0x00000400)
TA260 002:530.708 - 0.003ms returns 0
TA260 002:530.712 JLINK_WriteReg(R2, 0x20000184)
TA260 002:530.716 - 0.003ms returns 0
TA260 002:530.720 JLINK_WriteReg(R3, 0x00000000)
TA260 002:530.723 - 0.003ms returns 0
TA260 002:530.728 JLINK_WriteReg(R4, 0x00000000)
TA260 002:530.731 - 0.003ms returns 0
TA260 002:530.735 JLINK_WriteReg(R5, 0x00000000)
TA260 002:530.739 - 0.003ms returns 0
TA260 002:530.743 JLINK_WriteReg(R6, 0x00000000)
TA260 002:530.746 - 0.003ms returns 0
TA260 002:530.750 JLINK_WriteReg(R7, 0x00000000)
TA260 002:530.753 - 0.003ms returns 0
TA260 002:530.757 JLINK_WriteReg(R8, 0x00000000)
TA260 002:530.761 - 0.003ms returns 0
TA260 002:530.765 JLINK_WriteReg(R9, 0x20000180)
TA260 002:530.768 - 0.003ms returns 0
TA260 002:530.772 JLINK_WriteReg(R10, 0x00000000)
TA260 002:530.776 - 0.003ms returns 0
TA260 002:530.780 JLINK_WriteReg(R11, 0x00000000)
TA260 002:530.783 - 0.003ms returns 0
TA260 002:530.787 JLINK_WriteReg(R12, 0x00000000)
TA260 002:530.791 - 0.003ms returns 0
TA260 002:530.795 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:530.799 - 0.004ms returns 0
TA260 002:530.803 JLINK_WriteReg(R14, 0x20000001)
TA260 002:530.806 - 0.003ms returns 0
TA260 002:530.811 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:530.814 - 0.003ms returns 0
TA260 002:530.818 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:530.822 - 0.003ms returns 0
TA260 002:530.826 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:530.829 - 0.003ms returns 0
TA260 002:530.833 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:530.837 - 0.003ms returns 0
TA260 002:530.841 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:530.844 - 0.003ms returns 0
TA260 002:530.849 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:530.853 - 0.004ms returns 0x0000001C
TA260 002:530.857 JLINK_Go()
TA260 002:530.865   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:533.661 - 2.802ms 
TA260 002:533.677 JLINK_IsHalted()
TA260 002:534.171 - 0.494ms returns FALSE
TA260 002:534.178 JLINK_HasError()
TA260 002:536.189 JLINK_IsHalted()
TA260 002:536.699 - 0.509ms returns FALSE
TA260 002:536.716 JLINK_HasError()
TA260 002:538.693 JLINK_IsHalted()
TA260 002:541.020   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:541.529 - 2.836ms returns TRUE
TA260 002:541.539 JLINK_ReadReg(R15 (PC))
TA260 002:541.545 - 0.006ms returns 0x20000000
TA260 002:541.549 JLINK_ClrBPEx(BPHandle = 0x0000001C)
TA260 002:541.554 - 0.004ms returns 0x00
TA260 002:541.558 JLINK_ReadReg(R0)
TA260 002:541.562 - 0.003ms returns 0x00000000
TA260 002:541.958 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:541.971   Data:  D5 48 78 44 00 EB C4 08 0B 96 98 ED 00 0B 53 EC ...
TA260 002:541.989   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:544.735 - 2.776ms returns 0x27C
TA260 002:544.760 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:544.764   Data:  FB F7 07 FE 41 EC 19 0B 38 46 FB F7 8A FF CD E9 ...
TA260 002:544.776   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:546.686 - 1.925ms returns 0x184
TA260 002:546.721 JLINK_HasError()
TA260 002:546.728 JLINK_WriteReg(R0, 0x08004400)
TA260 002:546.735 - 0.006ms returns 0
TA260 002:546.739 JLINK_WriteReg(R1, 0x00000400)
TA260 002:546.743 - 0.003ms returns 0
TA260 002:546.747 JLINK_WriteReg(R2, 0x20000184)
TA260 002:546.750 - 0.003ms returns 0
TA260 002:546.754 JLINK_WriteReg(R3, 0x00000000)
TA260 002:546.758 - 0.003ms returns 0
TA260 002:546.762 JLINK_WriteReg(R4, 0x00000000)
TA260 002:546.765 - 0.003ms returns 0
TA260 002:546.769 JLINK_WriteReg(R5, 0x00000000)
TA260 002:546.773 - 0.004ms returns 0
TA260 002:546.777 JLINK_WriteReg(R6, 0x00000000)
TA260 002:546.781 - 0.003ms returns 0
TA260 002:546.785 JLINK_WriteReg(R7, 0x00000000)
TA260 002:546.788 - 0.003ms returns 0
TA260 002:546.792 JLINK_WriteReg(R8, 0x00000000)
TA260 002:546.796 - 0.003ms returns 0
TA260 002:546.800 JLINK_WriteReg(R9, 0x20000180)
TA260 002:546.803 - 0.003ms returns 0
TA260 002:546.813 JLINK_WriteReg(R10, 0x00000000)
TA260 002:546.817 - 0.003ms returns 0
TA260 002:546.821 JLINK_WriteReg(R11, 0x00000000)
TA260 002:546.825 - 0.003ms returns 0
TA260 002:546.829 JLINK_WriteReg(R12, 0x00000000)
TA260 002:546.832 - 0.003ms returns 0
TA260 002:546.836 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:546.840 - 0.004ms returns 0
TA260 002:546.844 JLINK_WriteReg(R14, 0x20000001)
TA260 002:546.848 - 0.003ms returns 0
TA260 002:546.852 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:546.855 - 0.003ms returns 0
TA260 002:546.860 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:546.863 - 0.003ms returns 0
TA260 002:546.867 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:546.870 - 0.003ms returns 0
TA260 002:546.875 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:546.878 - 0.003ms returns 0
TA260 002:546.882 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:546.886 - 0.003ms returns 0
TA260 002:546.890 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:546.895 - 0.004ms returns 0x0000001D
TA260 002:546.899 JLINK_Go()
TA260 002:546.910   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:549.659 - 2.759ms 
TA260 002:549.678 JLINK_IsHalted()
TA260 002:550.203 - 0.525ms returns FALSE
TA260 002:550.215 JLINK_HasError()
TA260 002:551.702 JLINK_IsHalted()
TA260 002:552.209 - 0.506ms returns FALSE
TA260 002:552.219 JLINK_HasError()
TA260 002:553.706 JLINK_IsHalted()
TA260 002:555.988   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:556.455 - 2.749ms returns TRUE
TA260 002:556.466 JLINK_ReadReg(R15 (PC))
TA260 002:556.473 - 0.007ms returns 0x20000000
TA260 002:556.478 JLINK_ClrBPEx(BPHandle = 0x0000001D)
TA260 002:556.489 - 0.010ms returns 0x00
TA260 002:556.495 JLINK_ReadReg(R0)
TA260 002:556.499 - 0.004ms returns 0x00000000
TA260 002:556.954 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:556.965   Data:  19 0B 53 EC 10 2B FB F7 42 FD 53 EC 1B 2B FC F7 ...
TA260 002:556.979   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:559.527 - 2.573ms returns 0x27C
TA260 002:559.565 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:559.570   Data:  FF F7 32 BB 00 00 90 40 00 00 00 00 FE 82 2B 65 ...
TA260 002:559.584   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:561.437 - 1.871ms returns 0x184
TA260 002:561.494 JLINK_HasError()
TA260 002:561.503 JLINK_WriteReg(R0, 0x08004800)
TA260 002:561.510 - 0.006ms returns 0
TA260 002:561.515 JLINK_WriteReg(R1, 0x00000400)
TA260 002:561.520 - 0.005ms returns 0
TA260 002:561.524 JLINK_WriteReg(R2, 0x20000184)
TA260 002:561.527 - 0.003ms returns 0
TA260 002:561.532 JLINK_WriteReg(R3, 0x00000000)
TA260 002:561.535 - 0.003ms returns 0
TA260 002:561.539 JLINK_WriteReg(R4, 0x00000000)
TA260 002:561.543 - 0.003ms returns 0
TA260 002:561.547 JLINK_WriteReg(R5, 0x00000000)
TA260 002:561.550 - 0.003ms returns 0
TA260 002:561.554 JLINK_WriteReg(R6, 0x00000000)
TA260 002:561.558 - 0.003ms returns 0
TA260 002:561.562 JLINK_WriteReg(R7, 0x00000000)
TA260 002:561.565 - 0.003ms returns 0
TA260 002:561.570 JLINK_WriteReg(R8, 0x00000000)
TA260 002:561.573 - 0.003ms returns 0
TA260 002:561.577 JLINK_WriteReg(R9, 0x20000180)
TA260 002:561.580 - 0.003ms returns 0
TA260 002:561.584 JLINK_WriteReg(R10, 0x00000000)
TA260 002:561.588 - 0.003ms returns 0
TA260 002:561.592 JLINK_WriteReg(R11, 0x00000000)
TA260 002:561.595 - 0.003ms returns 0
TA260 002:561.599 JLINK_WriteReg(R12, 0x00000000)
TA260 002:561.603 - 0.003ms returns 0
TA260 002:561.607 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:561.611 - 0.004ms returns 0
TA260 002:561.615 JLINK_WriteReg(R14, 0x20000001)
TA260 002:561.618 - 0.003ms returns 0
TA260 002:561.622 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:561.626 - 0.003ms returns 0
TA260 002:561.630 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:561.633 - 0.003ms returns 0
TA260 002:561.637 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:561.641 - 0.003ms returns 0
TA260 002:561.645 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:561.648 - 0.003ms returns 0
TA260 002:561.652 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:561.656 - 0.003ms returns 0
TA260 002:561.661 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:561.665 - 0.004ms returns 0x0000001E
TA260 002:561.670 JLINK_Go()
TA260 002:561.678   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:564.479 - 2.809ms 
TA260 002:564.493 JLINK_IsHalted()
TA260 002:564.982 - 0.487ms returns FALSE
TA260 002:565.004 JLINK_HasError()
TA260 002:566.218 JLINK_IsHalted()
TA260 002:566.805 - 0.587ms returns FALSE
TA260 002:566.866 JLINK_HasError()
TA260 002:568.724 JLINK_IsHalted()
TA260 002:571.014   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:571.526 - 2.802ms returns TRUE
TA260 002:571.534 JLINK_ReadReg(R15 (PC))
TA260 002:571.540 - 0.006ms returns 0x20000000
TA260 002:571.544 JLINK_ClrBPEx(BPHandle = 0x0000001E)
TA260 002:571.548 - 0.003ms returns 0x00
TA260 002:571.552 JLINK_ReadReg(R0)
TA260 002:571.556 - 0.003ms returns 0x00000000
TA260 002:572.029 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:572.039   Data:  51 EC 10 0B FB F7 EA FB 94 ED 06 1B 53 EC 11 2B ...
TA260 002:572.051   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:574.731 - 2.702ms returns 0x27C
TA260 002:574.764 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:574.768   Data:  6D 1E 0D E0 5B 45 04 DD 4F F0 00 02 05 F1 01 05 ...
TA260 002:574.781   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:576.680 - 1.915ms returns 0x184
TA260 002:576.710 JLINK_HasError()
TA260 002:576.717 JLINK_WriteReg(R0, 0x08004C00)
TA260 002:576.723 - 0.006ms returns 0
TA260 002:576.728 JLINK_WriteReg(R1, 0x00000400)
TA260 002:576.731 - 0.003ms returns 0
TA260 002:576.735 JLINK_WriteReg(R2, 0x20000184)
TA260 002:576.738 - 0.003ms returns 0
TA260 002:576.743 JLINK_WriteReg(R3, 0x00000000)
TA260 002:576.746 - 0.003ms returns 0
TA260 002:576.750 JLINK_WriteReg(R4, 0x00000000)
TA260 002:576.754 - 0.003ms returns 0
TA260 002:576.758 JLINK_WriteReg(R5, 0x00000000)
TA260 002:576.761 - 0.003ms returns 0
TA260 002:576.765 JLINK_WriteReg(R6, 0x00000000)
TA260 002:576.776 - 0.010ms returns 0
TA260 002:576.783 JLINK_WriteReg(R7, 0x00000000)
TA260 002:576.787 - 0.003ms returns 0
TA260 002:576.791 JLINK_WriteReg(R8, 0x00000000)
TA260 002:576.794 - 0.003ms returns 0
TA260 002:576.799 JLINK_WriteReg(R9, 0x20000180)
TA260 002:576.802 - 0.003ms returns 0
TA260 002:576.806 JLINK_WriteReg(R10, 0x00000000)
TA260 002:576.810 - 0.003ms returns 0
TA260 002:576.814 JLINK_WriteReg(R11, 0x00000000)
TA260 002:576.817 - 0.003ms returns 0
TA260 002:576.822 JLINK_WriteReg(R12, 0x00000000)
TA260 002:576.825 - 0.003ms returns 0
TA260 002:576.829 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:576.833 - 0.004ms returns 0
TA260 002:576.837 JLINK_WriteReg(R14, 0x20000001)
TA260 002:576.840 - 0.003ms returns 0
TA260 002:576.845 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:576.848 - 0.003ms returns 0
TA260 002:576.852 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:576.856 - 0.003ms returns 0
TA260 002:576.860 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:576.863 - 0.003ms returns 0
TA260 002:576.867 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:576.871 - 0.003ms returns 0
TA260 002:576.875 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:576.878 - 0.003ms returns 0
TA260 002:576.883 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:576.888 - 0.004ms returns 0x0000001F
TA260 002:576.892 JLINK_Go()
TA260 002:576.902   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:579.664 - 2.772ms 
TA260 002:579.682 JLINK_IsHalted()
TA260 002:580.184 - 0.501ms returns FALSE
TA260 002:580.190 JLINK_HasError()
TA260 002:581.238 JLINK_IsHalted()
TA260 002:581.789 - 0.550ms returns FALSE
TA260 002:581.798 JLINK_HasError()
TA260 002:583.233 JLINK_IsHalted()
TA260 002:583.697 - 0.463ms returns FALSE
TA260 002:583.705 JLINK_HasError()
TA260 002:585.241 JLINK_IsHalted()
TA260 002:587.768   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:588.229 - 2.987ms returns TRUE
TA260 002:588.252 JLINK_ReadReg(R15 (PC))
TA260 002:588.259 - 0.006ms returns 0x20000000
TA260 002:588.265 JLINK_ClrBPEx(BPHandle = 0x0000001F)
TA260 002:588.268 - 0.004ms returns 0x00
TA260 002:588.305 JLINK_ReadReg(R0)
TA260 002:588.319 - 0.014ms returns 0x00000000
TA260 002:588.726 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:588.734   Data:  75 E1 C4 F3 02 50 02 28 09 D0 03 28 0D D0 D9 F8 ...
TA260 002:588.745   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:591.397 - 2.671ms returns 0x27C
TA260 002:591.420 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:591.425   Data:  05 98 00 F0 9F F9 05 44 04 E0 30 20 5A 46 17 99 ...
TA260 002:591.437   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:593.256 - 1.834ms returns 0x184
TA260 002:593.265 JLINK_HasError()
TA260 002:593.270 JLINK_WriteReg(R0, 0x08005000)
TA260 002:593.276 - 0.005ms returns 0
TA260 002:593.280 JLINK_WriteReg(R1, 0x00000400)
TA260 002:593.284 - 0.004ms returns 0
TA260 002:593.289 JLINK_WriteReg(R2, 0x20000184)
TA260 002:593.293 - 0.003ms returns 0
TA260 002:593.297 JLINK_WriteReg(R3, 0x00000000)
TA260 002:593.300 - 0.003ms returns 0
TA260 002:593.304 JLINK_WriteReg(R4, 0x00000000)
TA260 002:593.308 - 0.003ms returns 0
TA260 002:593.312 JLINK_WriteReg(R5, 0x00000000)
TA260 002:593.315 - 0.003ms returns 0
TA260 002:593.319 JLINK_WriteReg(R6, 0x00000000)
TA260 002:593.323 - 0.003ms returns 0
TA260 002:593.327 JLINK_WriteReg(R7, 0x00000000)
TA260 002:593.330 - 0.003ms returns 0
TA260 002:593.334 JLINK_WriteReg(R8, 0x00000000)
TA260 002:593.338 - 0.003ms returns 0
TA260 002:593.342 JLINK_WriteReg(R9, 0x20000180)
TA260 002:593.345 - 0.003ms returns 0
TA260 002:593.349 JLINK_WriteReg(R10, 0x00000000)
TA260 002:593.353 - 0.003ms returns 0
TA260 002:593.357 JLINK_WriteReg(R11, 0x00000000)
TA260 002:593.360 - 0.003ms returns 0
TA260 002:593.364 JLINK_WriteReg(R12, 0x00000000)
TA260 002:593.368 - 0.003ms returns 0
TA260 002:593.372 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:593.376 - 0.004ms returns 0
TA260 002:593.380 JLINK_WriteReg(R14, 0x20000001)
TA260 002:593.383 - 0.003ms returns 0
TA260 002:593.387 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:593.397 - 0.009ms returns 0
TA260 002:593.401 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:593.405 - 0.003ms returns 0
TA260 002:593.409 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:593.412 - 0.003ms returns 0
TA260 002:593.416 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:593.420 - 0.003ms returns 0
TA260 002:593.424 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:593.427 - 0.003ms returns 0
TA260 002:593.432 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:593.437 - 0.005ms returns 0x00000020
TA260 002:593.441 JLINK_Go()
TA260 002:593.449   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:596.273 - 2.831ms 
TA260 002:596.299 JLINK_IsHalted()
TA260 002:596.801 - 0.502ms returns FALSE
TA260 002:596.808 JLINK_HasError()
TA260 002:598.260 JLINK_IsHalted()
TA260 002:598.697 - 0.437ms returns FALSE
TA260 002:598.704 JLINK_HasError()
TA260 002:600.256 JLINK_IsHalted()
TA260 002:602.672   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:603.174 - 2.917ms returns TRUE
TA260 002:603.181 JLINK_ReadReg(R15 (PC))
TA260 002:603.187 - 0.006ms returns 0x20000000
TA260 002:603.191 JLINK_ClrBPEx(BPHandle = 0x00000020)
TA260 002:603.196 - 0.004ms returns 0x00
TA260 002:603.200 JLINK_ReadReg(R0)
TA260 002:603.204 - 0.003ms returns 0x00000000
TA260 002:603.581 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:603.590   Data:  03 91 00 21 04 91 00 92 BA 46 21 07 0C D4 03 99 ...
TA260 002:603.600   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:606.225 - 2.643ms returns 0x27C
TA260 002:606.248 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:606.252   Data:  59 FB 51 EC 18 0B 03 B0 BD EC 04 8B 00 BD 00 00 ...
TA260 002:606.269   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:608.128 - 1.879ms returns 0x184
TA260 002:608.149 JLINK_HasError()
TA260 002:608.156 JLINK_WriteReg(R0, 0x08005400)
TA260 002:608.163 - 0.007ms returns 0
TA260 002:608.167 JLINK_WriteReg(R1, 0x00000400)
TA260 002:608.171 - 0.003ms returns 0
TA260 002:608.175 JLINK_WriteReg(R2, 0x20000184)
TA260 002:608.178 - 0.003ms returns 0
TA260 002:608.182 JLINK_WriteReg(R3, 0x00000000)
TA260 002:608.186 - 0.003ms returns 0
TA260 002:608.190 JLINK_WriteReg(R4, 0x00000000)
TA260 002:608.194 - 0.004ms returns 0
TA260 002:608.198 JLINK_WriteReg(R5, 0x00000000)
TA260 002:608.201 - 0.003ms returns 0
TA260 002:608.205 JLINK_WriteReg(R6, 0x00000000)
TA260 002:608.208 - 0.003ms returns 0
TA260 002:608.212 JLINK_WriteReg(R7, 0x00000000)
TA260 002:608.216 - 0.003ms returns 0
TA260 002:608.220 JLINK_WriteReg(R8, 0x00000000)
TA260 002:608.223 - 0.003ms returns 0
TA260 002:608.227 JLINK_WriteReg(R9, 0x20000180)
TA260 002:608.230 - 0.003ms returns 0
TA260 002:608.234 JLINK_WriteReg(R10, 0x00000000)
TA260 002:608.238 - 0.003ms returns 0
TA260 002:608.242 JLINK_WriteReg(R11, 0x00000000)
TA260 002:608.245 - 0.003ms returns 0
TA260 002:608.249 JLINK_WriteReg(R12, 0x00000000)
TA260 002:608.253 - 0.003ms returns 0
TA260 002:608.257 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:608.261 - 0.004ms returns 0
TA260 002:608.265 JLINK_WriteReg(R14, 0x20000001)
TA260 002:608.268 - 0.003ms returns 0
TA260 002:608.272 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:608.276 - 0.003ms returns 0
TA260 002:608.280 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:608.283 - 0.003ms returns 0
TA260 002:608.288 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:608.291 - 0.004ms returns 0
TA260 002:608.296 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:608.299 - 0.003ms returns 0
TA260 002:608.303 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:608.306 - 0.003ms returns 0
TA260 002:608.311 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:608.316 - 0.004ms returns 0x00000021
TA260 002:608.320 JLINK_Go()
TA260 002:608.329   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:611.168 - 2.847ms 
TA260 002:611.188 JLINK_IsHalted()
TA260 002:611.663 - 0.474ms returns FALSE
TA260 002:611.673 JLINK_HasError()
TA260 002:612.767 JLINK_IsHalted()
TA260 002:613.253 - 0.485ms returns FALSE
TA260 002:613.269 JLINK_HasError()
TA260 002:614.770 JLINK_IsHalted()
TA260 002:615.242 - 0.471ms returns FALSE
TA260 002:615.258 JLINK_HasError()
TA260 002:617.274 JLINK_IsHalted()
TA260 002:619.685   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:620.164 - 2.889ms returns TRUE
TA260 002:620.172 JLINK_ReadReg(R15 (PC))
TA260 002:620.178 - 0.006ms returns 0x20000000
TA260 002:620.183 JLINK_ClrBPEx(BPHandle = 0x00000021)
TA260 002:620.187 - 0.004ms returns 0x00
TA260 002:620.192 JLINK_ReadReg(R0)
TA260 002:620.195 - 0.003ms returns 0x00000000
TA260 002:620.576 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:620.584   Data:  D0 00 D0 04 D0 02 D0 06 D0 01 D0 05 D0 03 D0 07 ...
TA260 002:620.596   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:623.210 - 2.634ms returns 0x27C
TA260 002:623.222 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:623.226   Data:  4C 03 4C 07 CC 00 CC 04 CC 02 CC 06 CC 01 CC 05 ...
TA260 002:623.236   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:625.217 - 1.994ms returns 0x184
TA260 002:625.241 JLINK_HasError()
TA260 002:625.247 JLINK_WriteReg(R0, 0x08005800)
TA260 002:625.254 - 0.007ms returns 0
TA260 002:625.258 JLINK_WriteReg(R1, 0x00000400)
TA260 002:625.262 - 0.003ms returns 0
TA260 002:625.266 JLINK_WriteReg(R2, 0x20000184)
TA260 002:625.271 - 0.003ms returns 0
TA260 002:625.276 JLINK_WriteReg(R3, 0x00000000)
TA260 002:625.280 - 0.003ms returns 0
TA260 002:625.284 JLINK_WriteReg(R4, 0x00000000)
TA260 002:625.288 - 0.003ms returns 0
TA260 002:625.292 JLINK_WriteReg(R5, 0x00000000)
TA260 002:625.295 - 0.003ms returns 0
TA260 002:625.299 JLINK_WriteReg(R6, 0x00000000)
TA260 002:625.302 - 0.003ms returns 0
TA260 002:625.306 JLINK_WriteReg(R7, 0x00000000)
TA260 002:625.310 - 0.003ms returns 0
TA260 002:625.314 JLINK_WriteReg(R8, 0x00000000)
TA260 002:625.317 - 0.003ms returns 0
TA260 002:625.321 JLINK_WriteReg(R9, 0x20000180)
TA260 002:625.325 - 0.003ms returns 0
TA260 002:625.329 JLINK_WriteReg(R10, 0x00000000)
TA260 002:625.332 - 0.003ms returns 0
TA260 002:625.336 JLINK_WriteReg(R11, 0x00000000)
TA260 002:625.340 - 0.004ms returns 0
TA260 002:625.344 JLINK_WriteReg(R12, 0x00000000)
TA260 002:625.348 - 0.003ms returns 0
TA260 002:625.352 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:625.357 - 0.005ms returns 0
TA260 002:625.361 JLINK_WriteReg(R14, 0x20000001)
TA260 002:625.364 - 0.003ms returns 0
TA260 002:625.369 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:625.372 - 0.003ms returns 0
TA260 002:625.376 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:625.380 - 0.003ms returns 0
TA260 002:625.384 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:625.387 - 0.003ms returns 0
TA260 002:625.391 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:625.395 - 0.003ms returns 0
TA260 002:625.399 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:625.402 - 0.003ms returns 0
TA260 002:625.407 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:625.412 - 0.004ms returns 0x00000022
TA260 002:625.416 JLINK_Go()
TA260 002:625.427   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:628.245 - 2.828ms 
TA260 002:628.266 JLINK_IsHalted()
TA260 002:628.799 - 0.532ms returns FALSE
TA260 002:628.805 JLINK_HasError()
TA260 002:630.780 JLINK_IsHalted()
TA260 002:631.256 - 0.475ms returns FALSE
TA260 002:631.269 JLINK_HasError()
TA260 002:632.780 JLINK_IsHalted()
TA260 002:635.190   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:635.695 - 2.914ms returns TRUE
TA260 002:635.711 JLINK_ReadReg(R15 (PC))
TA260 002:635.717 - 0.006ms returns 0x20000000
TA260 002:635.721 JLINK_ClrBPEx(BPHandle = 0x00000022)
TA260 002:635.726 - 0.004ms returns 0x00
TA260 002:635.731 JLINK_ReadReg(R0)
TA260 002:635.734 - 0.003ms returns 0x00000000
TA260 002:636.115 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:636.122   Data:  D2 00 D2 04 D2 02 D2 06 D2 01 D2 05 D2 03 D2 07 ...
TA260 002:636.132   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:638.758 - 2.642ms returns 0x27C
TA260 002:638.783 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:638.787   Data:  4E 03 4E 07 CE 00 CE 04 CE 02 CE 06 CE 01 CE 05 ...
TA260 002:638.846   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:640.757 - 1.974ms returns 0x184
TA260 002:640.771 JLINK_HasError()
TA260 002:640.809 JLINK_WriteReg(R0, 0x08005C00)
TA260 002:640.816 - 0.006ms returns 0
TA260 002:640.820 JLINK_WriteReg(R1, 0x00000400)
TA260 002:640.835 - 0.014ms returns 0
TA260 002:640.840 JLINK_WriteReg(R2, 0x20000184)
TA260 002:640.843 - 0.003ms returns 0
TA260 002:640.848 JLINK_WriteReg(R3, 0x00000000)
TA260 002:640.851 - 0.003ms returns 0
TA260 002:640.855 JLINK_WriteReg(R4, 0x00000000)
TA260 002:640.858 - 0.003ms returns 0
TA260 002:640.862 JLINK_WriteReg(R5, 0x00000000)
TA260 002:640.866 - 0.003ms returns 0
TA260 002:640.870 JLINK_WriteReg(R6, 0x00000000)
TA260 002:640.874 - 0.003ms returns 0
TA260 002:640.878 JLINK_WriteReg(R7, 0x00000000)
TA260 002:640.881 - 0.003ms returns 0
TA260 002:640.885 JLINK_WriteReg(R8, 0x00000000)
TA260 002:640.889 - 0.003ms returns 0
TA260 002:640.893 JLINK_WriteReg(R9, 0x20000180)
TA260 002:640.896 - 0.003ms returns 0
TA260 002:640.900 JLINK_WriteReg(R10, 0x00000000)
TA260 002:640.904 - 0.003ms returns 0
TA260 002:640.908 JLINK_WriteReg(R11, 0x00000000)
TA260 002:640.911 - 0.003ms returns 0
TA260 002:640.915 JLINK_WriteReg(R12, 0x00000000)
TA260 002:640.918 - 0.003ms returns 0
TA260 002:640.923 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:640.927 - 0.004ms returns 0
TA260 002:640.931 JLINK_WriteReg(R14, 0x20000001)
TA260 002:640.935 - 0.003ms returns 0
TA260 002:640.939 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:640.942 - 0.003ms returns 0
TA260 002:640.951 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:640.955 - 0.003ms returns 0
TA260 002:640.959 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:640.962 - 0.003ms returns 0
TA260 002:640.966 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:640.970 - 0.003ms returns 0
TA260 002:640.974 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:640.977 - 0.003ms returns 0
TA260 002:640.982 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:640.986 - 0.004ms returns 0x00000023
TA260 002:640.990 JLINK_Go()
TA260 002:640.999   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:643.822 - 2.830ms 
TA260 002:643.841 JLINK_IsHalted()
TA260 002:644.304 - 0.463ms returns FALSE
TA260 002:644.311 JLINK_HasError()
TA260 002:645.800 JLINK_IsHalted()
TA260 002:646.321 - 0.520ms returns FALSE
TA260 002:646.328 JLINK_HasError()
TA260 002:647.804 JLINK_IsHalted()
TA260 002:650.106   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:650.652 - 2.848ms returns TRUE
TA260 002:650.660 JLINK_ReadReg(R15 (PC))
TA260 002:650.665 - 0.005ms returns 0x20000000
TA260 002:650.670 JLINK_ClrBPEx(BPHandle = 0x00000023)
TA260 002:650.674 - 0.004ms returns 0x00
TA260 002:650.678 JLINK_ReadReg(R0)
TA260 002:650.682 - 0.003ms returns 0x00000000
TA260 002:651.081 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:651.090   Data:  E4 E7 00 3F 3D 9C 03 3F 82 4B 06 3F 9B F5 08 3F ...
TA260 002:651.100   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:653.755 - 2.673ms returns 0x27C
TA260 002:653.776 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:653.780   Data:  D1 7F 1D 3F C6 02 1B 3F C0 7F 18 3F D9 F6 15 3F ...
TA260 002:653.792   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:655.677 - 1.900ms returns 0x184
TA260 002:655.696 JLINK_HasError()
TA260 002:655.703 JLINK_WriteReg(R0, 0x08006000)
TA260 002:655.708 - 0.006ms returns 0
TA260 002:655.713 JLINK_WriteReg(R1, 0x00000400)
TA260 002:655.716 - 0.003ms returns 0
TA260 002:655.720 JLINK_WriteReg(R2, 0x20000184)
TA260 002:655.724 - 0.003ms returns 0
TA260 002:655.728 JLINK_WriteReg(R3, 0x00000000)
TA260 002:655.732 - 0.003ms returns 0
TA260 002:655.736 JLINK_WriteReg(R4, 0x00000000)
TA260 002:655.740 - 0.003ms returns 0
TA260 002:655.744 JLINK_WriteReg(R5, 0x00000000)
TA260 002:655.747 - 0.003ms returns 0
TA260 002:655.751 JLINK_WriteReg(R6, 0x00000000)
TA260 002:655.755 - 0.003ms returns 0
TA260 002:655.759 JLINK_WriteReg(R7, 0x00000000)
TA260 002:655.762 - 0.003ms returns 0
TA260 002:655.766 JLINK_WriteReg(R8, 0x00000000)
TA260 002:655.774 - 0.007ms returns 0
TA260 002:655.779 JLINK_WriteReg(R9, 0x20000180)
TA260 002:655.783 - 0.003ms returns 0
TA260 002:655.787 JLINK_WriteReg(R10, 0x00000000)
TA260 002:655.790 - 0.003ms returns 0
TA260 002:655.794 JLINK_WriteReg(R11, 0x00000000)
TA260 002:655.798 - 0.003ms returns 0
TA260 002:655.802 JLINK_WriteReg(R12, 0x00000000)
TA260 002:655.805 - 0.003ms returns 0
TA260 002:655.809 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:655.813 - 0.004ms returns 0
TA260 002:655.817 JLINK_WriteReg(R14, 0x20000001)
TA260 002:655.821 - 0.003ms returns 0
TA260 002:655.825 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:655.829 - 0.004ms returns 0
TA260 002:655.833 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:655.837 - 0.003ms returns 0
TA260 002:655.841 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:655.844 - 0.003ms returns 0
TA260 002:655.848 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:655.852 - 0.003ms returns 0
TA260 002:655.856 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:655.859 - 0.003ms returns 0
TA260 002:655.864 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:655.869 - 0.004ms returns 0x00000024
TA260 002:655.873 JLINK_Go()
TA260 002:655.884   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:658.587 - 2.713ms 
TA260 002:658.615 JLINK_IsHalted()
TA260 002:659.117 - 0.502ms returns FALSE
TA260 002:659.124 JLINK_HasError()
TA260 002:660.816 JLINK_IsHalted()
TA260 002:661.255 - 0.439ms returns FALSE
TA260 002:661.263 JLINK_HasError()
TA260 002:662.816 JLINK_IsHalted()
TA260 002:665.181   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:665.658 - 2.842ms returns TRUE
TA260 002:665.675 JLINK_ReadReg(R15 (PC))
TA260 002:665.682 - 0.007ms returns 0x20000000
TA260 002:665.687 JLINK_ClrBPEx(BPHandle = 0x00000024)
TA260 002:665.691 - 0.003ms returns 0x00
TA260 002:665.695 JLINK_ReadReg(R0)
TA260 002:665.699 - 0.004ms returns 0x00000000
TA260 002:666.103 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:666.111   Data:  E4 E7 00 BF 3D 9C 03 BF 82 4B 06 BF 9B F5 08 BF ...
TA260 002:666.122   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:668.666 - 2.562ms returns 0x27C
TA260 002:668.693 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:668.697   Data:  D1 7F 1D BF C6 02 1B BF C0 7F 18 BF D9 F6 15 BF ...
TA260 002:668.712   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:670.666 - 1.972ms returns 0x184
TA260 002:670.674 JLINK_HasError()
TA260 002:670.680 JLINK_WriteReg(R0, 0x08006400)
TA260 002:670.686 - 0.006ms returns 0
TA260 002:670.690 JLINK_WriteReg(R1, 0x00000400)
TA260 002:670.694 - 0.003ms returns 0
TA260 002:670.698 JLINK_WriteReg(R2, 0x20000184)
TA260 002:670.702 - 0.003ms returns 0
TA260 002:670.706 JLINK_WriteReg(R3, 0x00000000)
TA260 002:670.709 - 0.003ms returns 0
TA260 002:670.713 JLINK_WriteReg(R4, 0x00000000)
TA260 002:670.717 - 0.003ms returns 0
TA260 002:670.721 JLINK_WriteReg(R5, 0x00000000)
TA260 002:670.725 - 0.003ms returns 0
TA260 002:670.729 JLINK_WriteReg(R6, 0x00000000)
TA260 002:670.733 - 0.004ms returns 0
TA260 002:670.737 JLINK_WriteReg(R7, 0x00000000)
TA260 002:670.740 - 0.003ms returns 0
TA260 002:670.745 JLINK_WriteReg(R8, 0x00000000)
TA260 002:670.748 - 0.003ms returns 0
TA260 002:670.752 JLINK_WriteReg(R9, 0x20000180)
TA260 002:670.756 - 0.003ms returns 0
TA260 002:670.760 JLINK_WriteReg(R10, 0x00000000)
TA260 002:670.764 - 0.003ms returns 0
TA260 002:670.768 JLINK_WriteReg(R11, 0x00000000)
TA260 002:670.771 - 0.003ms returns 0
TA260 002:670.775 JLINK_WriteReg(R12, 0x00000000)
TA260 002:670.778 - 0.003ms returns 0
TA260 002:670.782 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:670.787 - 0.004ms returns 0
TA260 002:670.791 JLINK_WriteReg(R14, 0x20000001)
TA260 002:670.794 - 0.003ms returns 0
TA260 002:670.798 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:670.802 - 0.003ms returns 0
TA260 002:670.806 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:670.810 - 0.003ms returns 0
TA260 002:670.814 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:670.817 - 0.003ms returns 0
TA260 002:670.822 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:670.833 - 0.011ms returns 0
TA260 002:670.838 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:670.842 - 0.003ms returns 0
TA260 002:670.846 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:670.851 - 0.005ms returns 0x00000025
TA260 002:670.855 JLINK_Go()
TA260 002:670.864   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:673.654 - 2.797ms 
TA260 002:673.669 JLINK_IsHalted()
TA260 002:674.131 - 0.460ms returns FALSE
TA260 002:674.143 JLINK_HasError()
TA260 002:675.837 JLINK_IsHalted()
TA260 002:676.381 - 0.543ms returns FALSE
TA260 002:676.390 JLINK_HasError()
TA260 002:677.841 JLINK_IsHalted()
TA260 002:680.188   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:680.660 - 2.819ms returns TRUE
TA260 002:680.668 JLINK_ReadReg(R15 (PC))
TA260 002:680.675 - 0.006ms returns 0x20000000
TA260 002:680.680 JLINK_ClrBPEx(BPHandle = 0x00000025)
TA260 002:680.684 - 0.003ms returns 0x00
TA260 002:680.689 JLINK_ReadReg(R0)
TA260 002:680.692 - 0.004ms returns 0x00000000
TA260 002:681.083 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:681.093   Data:  B1 FF 7F 3F C7 0F 49 3B 4E FF 7F 3F C1 CB 96 3B ...
TA260 002:681.104   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:683.748 - 2.665ms returns 0x27C
TA260 002:683.757 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:683.761   Data:  92 D0 FD 3D 38 FA 7D 3F 45 77 00 3E 88 ED 7D 3F ...
TA260 002:683.769   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:685.672 - 1.914ms returns 0x184
TA260 002:685.692 JLINK_HasError()
TA260 002:685.698 JLINK_WriteReg(R0, 0x08006800)
TA260 002:685.711 - 0.012ms returns 0
TA260 002:685.715 JLINK_WriteReg(R1, 0x00000400)
TA260 002:685.718 - 0.003ms returns 0
TA260 002:685.723 JLINK_WriteReg(R2, 0x20000184)
TA260 002:685.726 - 0.003ms returns 0
TA260 002:685.730 JLINK_WriteReg(R3, 0x00000000)
TA260 002:685.734 - 0.003ms returns 0
TA260 002:685.738 JLINK_WriteReg(R4, 0x00000000)
TA260 002:685.741 - 0.003ms returns 0
TA260 002:685.772 JLINK_WriteReg(R5, 0x00000000)
TA260 002:685.776 - 0.003ms returns 0
TA260 002:685.781 JLINK_WriteReg(R6, 0x00000000)
TA260 002:685.784 - 0.003ms returns 0
TA260 002:685.788 JLINK_WriteReg(R7, 0x00000000)
TA260 002:685.792 - 0.003ms returns 0
TA260 002:685.796 JLINK_WriteReg(R8, 0x00000000)
TA260 002:685.800 - 0.003ms returns 0
TA260 002:685.804 JLINK_WriteReg(R9, 0x20000180)
TA260 002:685.807 - 0.003ms returns 0
TA260 002:685.811 JLINK_WriteReg(R10, 0x00000000)
TA260 002:685.815 - 0.003ms returns 0
TA260 002:685.819 JLINK_WriteReg(R11, 0x00000000)
TA260 002:685.822 - 0.003ms returns 0
TA260 002:685.826 JLINK_WriteReg(R12, 0x00000000)
TA260 002:685.830 - 0.003ms returns 0
TA260 002:685.834 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:685.838 - 0.004ms returns 0
TA260 002:685.842 JLINK_WriteReg(R14, 0x20000001)
TA260 002:685.846 - 0.003ms returns 0
TA260 002:685.850 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:685.853 - 0.003ms returns 0
TA260 002:685.858 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:685.861 - 0.003ms returns 0
TA260 002:685.865 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:685.868 - 0.003ms returns 0
TA260 002:685.872 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:685.876 - 0.003ms returns 0
TA260 002:685.880 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:685.884 - 0.003ms returns 0
TA260 002:685.888 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:685.893 - 0.004ms returns 0x00000026
TA260 002:685.897 JLINK_Go()
TA260 002:685.908   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:688.662 - 2.764ms 
TA260 002:688.678 JLINK_IsHalted()
TA260 002:689.197 - 0.518ms returns FALSE
TA260 002:689.212 JLINK_HasError()
TA260 002:691.352 JLINK_IsHalted()
TA260 002:691.886 - 0.534ms returns FALSE
TA260 002:691.900 JLINK_HasError()
TA260 002:693.350 JLINK_IsHalted()
TA260 002:695.748   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:696.241 - 2.890ms returns TRUE
TA260 002:696.262 JLINK_ReadReg(R15 (PC))
TA260 002:696.269 - 0.006ms returns 0x20000000
TA260 002:696.274 JLINK_ClrBPEx(BPHandle = 0x00000026)
TA260 002:696.285 - 0.011ms returns 0x00
TA260 002:696.293 JLINK_ReadReg(R0)
TA260 002:696.297 - 0.004ms returns 0x00000000
TA260 002:696.724 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:696.735   Data:  37 ED 7A 3F 4F DA 4A 3E 3A D9 7A 3F 67 64 4C 3E ...
TA260 002:696.747   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:699.348 - 2.624ms returns 0x27C
TA260 002:699.372 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:699.377   Data:  C2 59 A1 3E EB D4 72 3F 87 18 A2 3E 04 B5 72 3F ...
TA260 002:699.391   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:701.256 - 1.883ms returns 0x184
TA260 002:701.267 JLINK_HasError()
TA260 002:701.273 JLINK_WriteReg(R0, 0x08006C00)
TA260 002:701.279 - 0.006ms returns 0
TA260 002:701.283 JLINK_WriteReg(R1, 0x00000400)
TA260 002:701.287 - 0.003ms returns 0
TA260 002:701.291 JLINK_WriteReg(R2, 0x20000184)
TA260 002:701.294 - 0.003ms returns 0
TA260 002:701.354 JLINK_WriteReg(R3, 0x00000000)
TA260 002:701.358 - 0.003ms returns 0
TA260 002:701.362 JLINK_WriteReg(R4, 0x00000000)
TA260 002:701.365 - 0.003ms returns 0
TA260 002:701.369 JLINK_WriteReg(R5, 0x00000000)
TA260 002:701.373 - 0.003ms returns 0
TA260 002:701.378 JLINK_WriteReg(R6, 0x00000000)
TA260 002:701.381 - 0.004ms returns 0
TA260 002:701.386 JLINK_WriteReg(R7, 0x00000000)
TA260 002:701.389 - 0.003ms returns 0
TA260 002:701.393 JLINK_WriteReg(R8, 0x00000000)
TA260 002:701.396 - 0.003ms returns 0
TA260 002:701.400 JLINK_WriteReg(R9, 0x20000180)
TA260 002:701.404 - 0.003ms returns 0
TA260 002:701.408 JLINK_WriteReg(R10, 0x00000000)
TA260 002:701.411 - 0.003ms returns 0
TA260 002:701.415 JLINK_WriteReg(R11, 0x00000000)
TA260 002:701.419 - 0.003ms returns 0
TA260 002:701.423 JLINK_WriteReg(R12, 0x00000000)
TA260 002:701.444 - 0.020ms returns 0
TA260 002:701.448 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:701.452 - 0.003ms returns 0
TA260 002:701.456 JLINK_WriteReg(R14, 0x20000001)
TA260 002:701.460 - 0.003ms returns 0
TA260 002:701.468 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:701.472 - 0.003ms returns 0
TA260 002:701.476 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:701.480 - 0.003ms returns 0
TA260 002:701.495 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:701.499 - 0.003ms returns 0
TA260 002:701.503 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:701.507 - 0.003ms returns 0
TA260 002:701.511 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:701.515 - 0.003ms returns 0
TA260 002:701.521 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:701.526 - 0.005ms returns 0x00000027
TA260 002:701.531 JLINK_Go()
TA260 002:701.540   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:704.325 - 2.793ms 
TA260 002:704.350 JLINK_IsHalted()
TA260 002:704.804 - 0.453ms returns FALSE
TA260 002:704.820 JLINK_HasError()
TA260 002:706.186 JLINK_IsHalted()
TA260 002:706.658 - 0.471ms returns FALSE
TA260 002:706.676 JLINK_HasError()
TA260 002:708.694 JLINK_IsHalted()
TA260 002:711.003   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:711.482 - 2.788ms returns TRUE
TA260 002:711.489 JLINK_ReadReg(R15 (PC))
TA260 002:711.494 - 0.005ms returns 0x20000000
TA260 002:711.499 JLINK_ClrBPEx(BPHandle = 0x00000027)
TA260 002:711.502 - 0.003ms returns 0x00
TA260 002:711.508 JLINK_ReadReg(R0)
TA260 002:711.511 - 0.003ms returns 0x00000000
TA260 002:711.937 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:711.946   Data:  24 36 6C 3F 5C 62 C5 3E 50 0F 6C 3F D2 1B C6 3E ...
TA260 002:711.957   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:714.722 - 2.784ms returns 0x27C
TA260 002:714.742 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:714.746   Data:  04 0C FD 3E A6 5A 5E 3F BB BA FD 3E C3 28 5E 3F ...
TA260 002:714.756   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:716.677 - 1.934ms returns 0x184
TA260 002:716.698 JLINK_HasError()
TA260 002:716.704 JLINK_WriteReg(R0, 0x08007000)
TA260 002:716.742 - 0.037ms returns 0
TA260 002:716.747 JLINK_WriteReg(R1, 0x00000400)
TA260 002:716.750 - 0.003ms returns 0
TA260 002:716.754 JLINK_WriteReg(R2, 0x20000184)
TA260 002:716.763 - 0.008ms returns 0
TA260 002:716.770 JLINK_WriteReg(R3, 0x00000000)
TA260 002:716.773 - 0.003ms returns 0
TA260 002:716.777 JLINK_WriteReg(R4, 0x00000000)
TA260 002:716.800 - 0.003ms returns 0
TA260 002:716.805 JLINK_WriteReg(R5, 0x00000000)
TA260 002:716.808 - 0.003ms returns 0
TA260 002:716.813 JLINK_WriteReg(R6, 0x00000000)
TA260 002:716.816 - 0.003ms returns 0
TA260 002:716.821 JLINK_WriteReg(R7, 0x00000000)
TA260 002:716.824 - 0.003ms returns 0
TA260 002:716.829 JLINK_WriteReg(R8, 0x00000000)
TA260 002:716.832 - 0.003ms returns 0
TA260 002:716.836 JLINK_WriteReg(R9, 0x20000180)
TA260 002:716.918 - 0.082ms returns 0
TA260 002:716.922 JLINK_WriteReg(R10, 0x00000000)
TA260 002:716.926 - 0.003ms returns 0
TA260 002:716.930 JLINK_WriteReg(R11, 0x00000000)
TA260 002:716.944 - 0.013ms returns 0
TA260 002:716.948 JLINK_WriteReg(R12, 0x00000000)
TA260 002:716.952 - 0.003ms returns 0
TA260 002:716.956 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:716.960 - 0.004ms returns 0
TA260 002:716.968 JLINK_WriteReg(R14, 0x20000001)
TA260 002:716.972 - 0.003ms returns 0
TA260 002:716.976 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:716.979 - 0.003ms returns 0
TA260 002:716.984 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:716.987 - 0.003ms returns 0
TA260 002:716.991 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:716.995 - 0.003ms returns 0
TA260 002:716.999 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:717.002 - 0.003ms returns 0
TA260 002:717.006 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:717.011 - 0.004ms returns 0
TA260 002:717.023 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:717.028 - 0.005ms returns 0x00000028
TA260 002:717.034 JLINK_Go()
TA260 002:717.045   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:719.755 - 2.721ms 
TA260 002:719.775 JLINK_IsHalted()
TA260 002:720.282 - 0.506ms returns FALSE
TA260 002:720.305 JLINK_HasError()
TA260 002:722.203 JLINK_IsHalted()
TA260 002:722.699 - 0.495ms returns FALSE
TA260 002:722.708 JLINK_HasError()
TA260 002:724.203 JLINK_IsHalted()
TA260 002:726.586   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:727.126 - 2.921ms returns TRUE
TA260 002:727.233 JLINK_ReadReg(R15 (PC))
TA260 002:727.240 - 0.013ms returns 0x20000000
TA260 002:727.245 JLINK_ClrBPEx(BPHandle = 0x00000028)
TA260 002:727.249 - 0.004ms returns 0x00
TA260 002:727.254 JLINK_ReadReg(R0)
TA260 002:727.257 - 0.003ms returns 0x00000000
TA260 002:727.679 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:727.687   Data:  3B 6B 54 3F DB E0 0E 3F 0F 33 54 3F 3B 34 0F 3F ...
TA260 002:727.698   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:730.301 - 2.621ms returns 0x27C
TA260 002:730.315 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:730.319   Data:  68 82 27 3F E1 54 41 3F 61 CE 27 3F EC 12 41 3F ...
TA260 002:730.330   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:732.207 - 1.891ms returns 0x184
TA260 002:732.217 JLINK_HasError()
TA260 002:732.223 JLINK_WriteReg(R0, 0x08007400)
TA260 002:732.228 - 0.005ms returns 0
TA260 002:732.232 JLINK_WriteReg(R1, 0x00000400)
TA260 002:732.236 - 0.003ms returns 0
TA260 002:732.240 JLINK_WriteReg(R2, 0x20000184)
TA260 002:732.244 - 0.004ms returns 0
TA260 002:732.248 JLINK_WriteReg(R3, 0x00000000)
TA260 002:732.251 - 0.003ms returns 0
TA260 002:732.255 JLINK_WriteReg(R4, 0x00000000)
TA260 002:732.259 - 0.003ms returns 0
TA260 002:732.263 JLINK_WriteReg(R5, 0x00000000)
TA260 002:732.266 - 0.003ms returns 0
TA260 002:732.270 JLINK_WriteReg(R6, 0x00000000)
TA260 002:732.274 - 0.003ms returns 0
TA260 002:732.279 JLINK_WriteReg(R7, 0x00000000)
TA260 002:732.283 - 0.004ms returns 0
TA260 002:732.287 JLINK_WriteReg(R8, 0x00000000)
TA260 002:732.291 - 0.003ms returns 0
TA260 002:732.295 JLINK_WriteReg(R9, 0x20000180)
TA260 002:732.298 - 0.003ms returns 0
TA260 002:732.302 JLINK_WriteReg(R10, 0x00000000)
TA260 002:732.306 - 0.003ms returns 0
TA260 002:732.310 JLINK_WriteReg(R11, 0x00000000)
TA260 002:732.313 - 0.003ms returns 0
TA260 002:732.317 JLINK_WriteReg(R12, 0x00000000)
TA260 002:732.320 - 0.003ms returns 0
TA260 002:732.331 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:732.335 - 0.004ms returns 0
TA260 002:732.339 JLINK_WriteReg(R14, 0x20000001)
TA260 002:732.343 - 0.003ms returns 0
TA260 002:732.347 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:732.350 - 0.003ms returns 0
TA260 002:732.354 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:732.358 - 0.003ms returns 0
TA260 002:732.362 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:732.365 - 0.003ms returns 0
TA260 002:732.369 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:732.373 - 0.003ms returns 0
TA260 002:732.377 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:732.380 - 0.003ms returns 0
TA260 002:732.385 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:732.389 - 0.004ms returns 0x00000029
TA260 002:732.394 JLINK_Go()
TA260 002:732.402   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:735.090 - 2.695ms 
TA260 002:735.112 JLINK_IsHalted()
TA260 002:735.666 - 0.554ms returns FALSE
TA260 002:735.685 JLINK_HasError()
TA260 002:737.684 JLINK_IsHalted()
TA260 002:738.258 - 0.573ms returns FALSE
TA260 002:738.274 JLINK_HasError()
TA260 002:740.670 JLINK_IsHalted()
TA260 002:743.011   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:743.528 - 2.858ms returns TRUE
TA260 002:743.536 JLINK_ReadReg(R15 (PC))
TA260 002:743.542 - 0.005ms returns 0x20000000
TA260 002:743.547 JLINK_ClrBPEx(BPHandle = 0x00000029)
TA260 002:743.551 - 0.004ms returns 0x00
TA260 002:743.555 JLINK_ReadReg(R0)
TA260 002:743.559 - 0.003ms returns 0x00000000
TA260 002:743.927 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:743.936   Data:  8F 76 34 3F E7 92 35 3F 34 2F 34 3F B8 D9 35 3F ...
TA260 002:743.947   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:746.586 - 2.657ms returns 0x27C
TA260 002:746.664 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:746.669   Data:  DC 0E 4A 3F 1F E1 1C 3F 87 4C 4A 3F A2 91 1C 3F ...
TA260 002:746.685   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:748.584 - 1.920ms returns 0x184
TA260 002:748.603 JLINK_HasError()
TA260 002:748.608 JLINK_WriteReg(R0, 0x08007800)
TA260 002:748.614 - 0.006ms returns 0
TA260 002:748.619 JLINK_WriteReg(R1, 0x00000400)
TA260 002:748.622 - 0.003ms returns 0
TA260 002:748.626 JLINK_WriteReg(R2, 0x20000184)
TA260 002:748.630 - 0.003ms returns 0
TA260 002:748.634 JLINK_WriteReg(R3, 0x00000000)
TA260 002:748.637 - 0.003ms returns 0
TA260 002:748.641 JLINK_WriteReg(R4, 0x00000000)
TA260 002:748.644 - 0.003ms returns 0
TA260 002:748.648 JLINK_WriteReg(R5, 0x00000000)
TA260 002:748.652 - 0.003ms returns 0
TA260 002:748.656 JLINK_WriteReg(R6, 0x00000000)
TA260 002:748.659 - 0.003ms returns 0
TA260 002:748.663 JLINK_WriteReg(R7, 0x00000000)
TA260 002:748.667 - 0.003ms returns 0
TA260 002:748.671 JLINK_WriteReg(R8, 0x00000000)
TA260 002:748.674 - 0.003ms returns 0
TA260 002:748.678 JLINK_WriteReg(R9, 0x20000180)
TA260 002:748.682 - 0.003ms returns 0
TA260 002:748.686 JLINK_WriteReg(R10, 0x00000000)
TA260 002:748.689 - 0.003ms returns 0
TA260 002:748.693 JLINK_WriteReg(R11, 0x00000000)
TA260 002:748.697 - 0.003ms returns 0
TA260 002:748.701 JLINK_WriteReg(R12, 0x00000000)
TA260 002:748.704 - 0.003ms returns 0
TA260 002:748.708 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:748.712 - 0.004ms returns 0
TA260 002:748.716 JLINK_WriteReg(R14, 0x20000001)
TA260 002:748.720 - 0.003ms returns 0
TA260 002:748.724 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:748.727 - 0.003ms returns 0
TA260 002:748.731 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:748.735 - 0.003ms returns 0
TA260 002:748.739 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:748.742 - 0.003ms returns 0
TA260 002:748.746 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:748.750 - 0.003ms returns 0
TA260 002:748.754 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:748.757 - 0.003ms returns 0
TA260 002:748.762 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:748.766 - 0.004ms returns 0x0000002A
TA260 002:748.770 JLINK_Go()
TA260 002:748.780   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:751.589 - 2.818ms 
TA260 002:751.614 JLINK_IsHalted()
TA260 002:752.043 - 0.428ms returns FALSE
TA260 002:752.060 JLINK_HasError()
TA260 002:753.625 JLINK_IsHalted()
TA260 002:754.106 - 0.481ms returns FALSE
TA260 002:754.118 JLINK_HasError()
TA260 002:756.133 JLINK_IsHalted()
TA260 002:758.433   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:758.904 - 2.771ms returns TRUE
TA260 002:758.913 JLINK_ReadReg(R15 (PC))
TA260 002:758.918 - 0.005ms returns 0x20000000
TA260 002:758.923 JLINK_ClrBPEx(BPHandle = 0x0000002A)
TA260 002:758.927 - 0.004ms returns 0x00
TA260 002:758.932 JLINK_ReadReg(R0)
TA260 002:758.935 - 0.003ms returns 0x00000000
TA260 002:759.350 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:759.360   Data:  81 92 0D 3F A4 4A 55 3F B3 3E 0D 3F 2C 82 55 3F ...
TA260 002:759.372   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:761.989 - 2.638ms returns 0x27C
TA260 002:762.001 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:762.005   Data:  7B D7 64 3F FE CB E4 3E 79 04 65 3F 0E 18 E4 3E ...
TA260 002:762.017   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:763.892 - 1.890ms returns 0x184
TA260 002:763.904 JLINK_HasError()
TA260 002:763.909 JLINK_WriteReg(R0, 0x08007C00)
TA260 002:763.914 - 0.005ms returns 0
TA260 002:763.919 JLINK_WriteReg(R1, 0x00000400)
TA260 002:763.922 - 0.003ms returns 0
TA260 002:763.926 JLINK_WriteReg(R2, 0x20000184)
TA260 002:763.930 - 0.003ms returns 0
TA260 002:763.934 JLINK_WriteReg(R3, 0x00000000)
TA260 002:763.937 - 0.003ms returns 0
TA260 002:763.942 JLINK_WriteReg(R4, 0x00000000)
TA260 002:763.946 - 0.004ms returns 0
TA260 002:763.950 JLINK_WriteReg(R5, 0x00000000)
TA260 002:763.953 - 0.003ms returns 0
TA260 002:763.957 JLINK_WriteReg(R6, 0x00000000)
TA260 002:763.961 - 0.003ms returns 0
TA260 002:763.966 JLINK_WriteReg(R7, 0x00000000)
TA260 002:763.969 - 0.003ms returns 0
TA260 002:763.973 JLINK_WriteReg(R8, 0x00000000)
TA260 002:763.977 - 0.003ms returns 0
TA260 002:763.981 JLINK_WriteReg(R9, 0x20000180)
TA260 002:763.984 - 0.003ms returns 0
TA260 002:763.989 JLINK_WriteReg(R10, 0x00000000)
TA260 002:763.992 - 0.003ms returns 0
TA260 002:763.997 JLINK_WriteReg(R11, 0x00000000)
TA260 002:764.000 - 0.003ms returns 0
TA260 002:764.004 JLINK_WriteReg(R12, 0x00000000)
TA260 002:764.008 - 0.003ms returns 0
TA260 002:764.012 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:764.016 - 0.004ms returns 0
TA260 002:764.020 JLINK_WriteReg(R14, 0x20000001)
TA260 002:764.024 - 0.003ms returns 0
TA260 002:764.028 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:764.031 - 0.003ms returns 0
TA260 002:764.035 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:764.039 - 0.004ms returns 0
TA260 002:764.044 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:764.047 - 0.003ms returns 0
TA260 002:764.051 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:764.054 - 0.003ms returns 0
TA260 002:764.058 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:764.062 - 0.003ms returns 0
TA260 002:764.066 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:764.071 - 0.005ms returns 0x0000002B
TA260 002:764.075 JLINK_Go()
TA260 002:764.099   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:767.065 - 2.988ms 
TA260 002:767.119 JLINK_IsHalted()
TA260 002:767.571 - 0.451ms returns FALSE
TA260 002:767.580 JLINK_HasError()
TA260 002:768.639 JLINK_IsHalted()
TA260 002:769.084 - 0.444ms returns FALSE
TA260 002:769.096 JLINK_HasError()
TA260 002:770.639 JLINK_IsHalted()
TA260 002:771.161 - 0.522ms returns FALSE
TA260 002:771.168 JLINK_HasError()
TA260 002:772.648 JLINK_IsHalted()
TA260 002:775.029   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:775.506 - 2.857ms returns TRUE
TA260 002:775.516 JLINK_ReadReg(R15 (PC))
TA260 002:775.522 - 0.006ms returns 0x20000000
TA260 002:775.527 JLINK_ClrBPEx(BPHandle = 0x0000002B)
TA260 002:775.530 - 0.004ms returns 0x00
TA260 002:775.535 JLINK_ReadReg(R0)
TA260 002:775.539 - 0.003ms returns 0x00000000
TA260 002:775.949 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:775.958   Data:  55 7B C2 3E 07 D0 6C 3F 48 C1 C1 3E 24 F6 6C 3F ...
TA260 002:775.976   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:778.528 - 2.578ms returns 0x27C
TA260 002:778.552 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:778.556   Data:  C4 D4 76 3F DA 0A 87 3E 5B EF 76 3F DF 48 86 3E ...
TA260 002:778.568   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:780.474 - 1.921ms returns 0x184
TA260 002:780.485 JLINK_HasError()
TA260 002:780.491 JLINK_WriteReg(R0, 0x08008000)
TA260 002:780.496 - 0.005ms returns 0
TA260 002:780.501 JLINK_WriteReg(R1, 0x00000400)
TA260 002:780.505 - 0.004ms returns 0
TA260 002:780.510 JLINK_WriteReg(R2, 0x20000184)
TA260 002:780.513 - 0.003ms returns 0
TA260 002:780.517 JLINK_WriteReg(R3, 0x00000000)
TA260 002:780.521 - 0.003ms returns 0
TA260 002:780.526 JLINK_WriteReg(R4, 0x00000000)
TA260 002:780.529 - 0.003ms returns 0
TA260 002:780.533 JLINK_WriteReg(R5, 0x00000000)
TA260 002:780.536 - 0.003ms returns 0
TA260 002:780.540 JLINK_WriteReg(R6, 0x00000000)
TA260 002:780.544 - 0.003ms returns 0
TA260 002:780.548 JLINK_WriteReg(R7, 0x00000000)
TA260 002:780.551 - 0.003ms returns 0
TA260 002:780.556 JLINK_WriteReg(R8, 0x00000000)
TA260 002:780.560 - 0.004ms returns 0
TA260 002:780.564 JLINK_WriteReg(R9, 0x20000180)
TA260 002:780.568 - 0.003ms returns 0
TA260 002:780.572 JLINK_WriteReg(R10, 0x00000000)
TA260 002:780.575 - 0.003ms returns 0
TA260 002:780.579 JLINK_WriteReg(R11, 0x00000000)
TA260 002:780.583 - 0.003ms returns 0
TA260 002:780.587 JLINK_WriteReg(R12, 0x00000000)
TA260 002:780.590 - 0.003ms returns 0
TA260 002:780.594 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:780.598 - 0.004ms returns 0
TA260 002:780.602 JLINK_WriteReg(R14, 0x20000001)
TA260 002:780.606 - 0.003ms returns 0
TA260 002:780.610 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:780.613 - 0.003ms returns 0
TA260 002:780.618 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:780.621 - 0.003ms returns 0
TA260 002:780.625 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:780.629 - 0.003ms returns 0
TA260 002:780.633 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:780.636 - 0.003ms returns 0
TA260 002:780.640 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:780.644 - 0.003ms returns 0
TA260 002:780.648 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:780.653 - 0.004ms returns 0x0000002C
TA260 002:780.657 JLINK_Go()
TA260 002:780.666   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:783.492 - 2.834ms 
TA260 002:783.512 JLINK_IsHalted()
TA260 002:784.014 - 0.502ms returns FALSE
TA260 002:784.023 JLINK_HasError()
TA260 002:785.157 JLINK_IsHalted()
TA260 002:785.664 - 0.506ms returns FALSE
TA260 002:785.681 JLINK_HasError()
TA260 002:787.662 JLINK_IsHalted()
TA260 002:789.978   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:790.495 - 2.832ms returns TRUE
TA260 002:790.502 JLINK_ReadReg(R15 (PC))
TA260 002:790.508 - 0.005ms returns 0x20000000
TA260 002:790.512 JLINK_ClrBPEx(BPHandle = 0x0000002C)
TA260 002:790.516 - 0.004ms returns 0x00
TA260 002:790.520 JLINK_ReadReg(R0)
TA260 002:790.524 - 0.003ms returns 0x00000000
TA260 002:790.948 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:790.956   Data:  B9 B0 44 3E AB 3B 7B 3F 07 26 43 3E E7 4E 7B 3F ...
TA260 002:790.968   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:793.522 - 2.573ms returns 0x27C
TA260 002:793.534 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:793.538   Data:  BF 55 7F 3F B4 64 90 3D E9 5C 7F 3F 6A 42 8D 3D ...
TA260 002:793.547   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:795.449 - 1.915ms returns 0x184
TA260 002:795.476 JLINK_HasError()
TA260 002:795.483 JLINK_WriteReg(R0, 0x08008400)
TA260 002:795.489 - 0.006ms returns 0
TA260 002:795.494 JLINK_WriteReg(R1, 0x00000400)
TA260 002:795.498 - 0.003ms returns 0
TA260 002:795.502 JLINK_WriteReg(R2, 0x20000184)
TA260 002:795.505 - 0.003ms returns 0
TA260 002:795.509 JLINK_WriteReg(R3, 0x00000000)
TA260 002:795.513 - 0.003ms returns 0
TA260 002:795.517 JLINK_WriteReg(R4, 0x00000000)
TA260 002:795.520 - 0.003ms returns 0
TA260 002:795.524 JLINK_WriteReg(R5, 0x00000000)
TA260 002:795.527 - 0.003ms returns 0
TA260 002:795.543 JLINK_WriteReg(R6, 0x00000000)
TA260 002:795.547 - 0.003ms returns 0
TA260 002:795.551 JLINK_WriteReg(R7, 0x00000000)
TA260 002:795.554 - 0.003ms returns 0
TA260 002:795.558 JLINK_WriteReg(R8, 0x00000000)
TA260 002:795.562 - 0.003ms returns 0
TA260 002:795.582 JLINK_WriteReg(R9, 0x20000180)
TA260 002:795.587 - 0.004ms returns 0
TA260 002:795.592 JLINK_WriteReg(R10, 0x00000000)
TA260 002:795.595 - 0.003ms returns 0
TA260 002:795.599 JLINK_WriteReg(R11, 0x00000000)
TA260 002:795.602 - 0.003ms returns 0
TA260 002:795.607 JLINK_WriteReg(R12, 0x00000000)
TA260 002:795.610 - 0.003ms returns 0
TA260 002:795.615 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:795.619 - 0.004ms returns 0
TA260 002:795.623 JLINK_WriteReg(R14, 0x20000001)
TA260 002:795.626 - 0.003ms returns 0
TA260 002:795.631 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:795.634 - 0.003ms returns 0
TA260 002:795.638 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:795.642 - 0.003ms returns 0
TA260 002:795.646 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:795.649 - 0.003ms returns 0
TA260 002:795.653 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:795.656 - 0.003ms returns 0
TA260 002:795.660 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:795.664 - 0.003ms returns 0
TA260 002:795.669 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:795.673 - 0.004ms returns 0x0000002D
TA260 002:795.677 JLINK_Go()
TA260 002:795.688   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:798.400 - 2.722ms 
TA260 002:798.426 JLINK_IsHalted()
TA260 002:798.920 - 0.493ms returns FALSE
TA260 002:798.929 JLINK_HasError()
TA260 002:800.104 JLINK_IsHalted()
TA260 002:800.661 - 0.556ms returns FALSE
TA260 002:800.668 JLINK_HasError()
TA260 002:802.111 JLINK_IsHalted()
TA260 002:802.584 - 0.473ms returns FALSE
TA260 002:802.592 JLINK_HasError()
TA260 002:804.103 JLINK_IsHalted()
TA260 002:806.564   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:807.038 - 2.934ms returns TRUE
TA260 002:807.047 JLINK_ReadReg(R15 (PC))
TA260 002:807.052 - 0.005ms returns 0x20000000
TA260 002:807.057 JLINK_ClrBPEx(BPHandle = 0x0000002D)
TA260 002:807.060 - 0.003ms returns 0x00
TA260 002:807.065 JLINK_ReadReg(R0)
TA260 002:807.069 - 0.004ms returns 0x00000000
TA260 002:807.459 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:807.471   Data:  C7 0F 49 BB B1 FF 7F 3F C1 CB 96 BB 4E FF 7F 3F ...
TA260 002:807.483   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:810.086 - 2.626ms returns 0x27C
TA260 002:810.111 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:810.115   Data:  C2 06 7E 3F 45 77 00 BE 38 FA 7D 3F 2E 06 02 BE ...
TA260 002:810.126   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:812.033 - 1.922ms returns 0x184
TA260 002:812.045 JLINK_HasError()
TA260 002:812.063 JLINK_WriteReg(R0, 0x08008800)
TA260 002:812.070 - 0.007ms returns 0
TA260 002:812.074 JLINK_WriteReg(R1, 0x00000400)
TA260 002:812.078 - 0.004ms returns 0
TA260 002:812.082 JLINK_WriteReg(R2, 0x20000184)
TA260 002:812.086 - 0.003ms returns 0
TA260 002:812.090 JLINK_WriteReg(R3, 0x00000000)
TA260 002:812.093 - 0.003ms returns 0
TA260 002:812.097 JLINK_WriteReg(R4, 0x00000000)
TA260 002:812.101 - 0.003ms returns 0
TA260 002:812.105 JLINK_WriteReg(R5, 0x00000000)
TA260 002:812.110 - 0.004ms returns 0
TA260 002:812.115 JLINK_WriteReg(R6, 0x00000000)
TA260 002:812.118 - 0.003ms returns 0
TA260 002:812.123 JLINK_WriteReg(R7, 0x00000000)
TA260 002:812.126 - 0.003ms returns 0
TA260 002:812.130 JLINK_WriteReg(R8, 0x00000000)
TA260 002:812.134 - 0.003ms returns 0
TA260 002:812.138 JLINK_WriteReg(R9, 0x20000180)
TA260 002:812.141 - 0.003ms returns 0
TA260 002:812.145 JLINK_WriteReg(R10, 0x00000000)
TA260 002:812.149 - 0.003ms returns 0
TA260 002:812.153 JLINK_WriteReg(R11, 0x00000000)
TA260 002:812.156 - 0.003ms returns 0
TA260 002:812.160 JLINK_WriteReg(R12, 0x00000000)
TA260 002:812.164 - 0.003ms returns 0
TA260 002:812.168 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:812.172 - 0.004ms returns 0
TA260 002:812.177 JLINK_WriteReg(R14, 0x20000001)
TA260 002:812.187 - 0.009ms returns 0
TA260 002:812.194 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:812.198 - 0.003ms returns 0
TA260 002:812.202 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:812.205 - 0.003ms returns 0
TA260 002:812.210 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:812.213 - 0.003ms returns 0
TA260 002:812.217 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:812.221 - 0.003ms returns 0
TA260 002:812.225 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:812.228 - 0.003ms returns 0
TA260 002:812.233 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:812.240 - 0.007ms returns 0x0000002E
TA260 002:812.244 JLINK_Go()
TA260 002:812.254   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:815.072 - 2.826ms 
TA260 002:815.102 JLINK_IsHalted()
TA260 002:815.655 - 0.553ms returns FALSE
TA260 002:815.672 JLINK_HasError()
TA260 002:817.133 JLINK_IsHalted()
TA260 002:817.663 - 0.529ms returns FALSE
TA260 002:817.676 JLINK_HasError()
TA260 002:819.127 JLINK_IsHalted()
TA260 002:821.486   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:821.970 - 2.843ms returns TRUE
TA260 002:821.980 JLINK_ReadReg(R15 (PC))
TA260 002:821.985 - 0.005ms returns 0x20000000
TA260 002:821.990 JLINK_ClrBPEx(BPHandle = 0x0000002E)
TA260 002:821.994 - 0.004ms returns 0x00
TA260 002:821.999 JLINK_ReadReg(R0)
TA260 002:822.002 - 0.003ms returns 0x00000000
TA260 002:822.445 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:822.456   Data:  4F DA 4A BE 37 ED 7A 3F 67 64 4C BE 3A D9 7A 3F ...
TA260 002:822.470   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:825.077 - 2.631ms returns 0x27C
TA260 002:825.150 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:825.155   Data:  AC F4 72 3F 87 18 A2 BE EB D4 72 3F 33 D7 A2 BE ...
TA260 002:825.167   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:827.049 - 1.898ms returns 0x184
TA260 002:827.069 JLINK_HasError()
TA260 002:827.075 JLINK_WriteReg(R0, 0x08008C00)
TA260 002:827.083 - 0.007ms returns 0
TA260 002:827.087 JLINK_WriteReg(R1, 0x00000400)
TA260 002:827.091 - 0.003ms returns 0
TA260 002:827.095 JLINK_WriteReg(R2, 0x20000184)
TA260 002:827.098 - 0.003ms returns 0
TA260 002:827.102 JLINK_WriteReg(R3, 0x00000000)
TA260 002:827.106 - 0.003ms returns 0
TA260 002:827.110 JLINK_WriteReg(R4, 0x00000000)
TA260 002:827.113 - 0.003ms returns 0
TA260 002:827.117 JLINK_WriteReg(R5, 0x00000000)
TA260 002:827.120 - 0.003ms returns 0
TA260 002:827.124 JLINK_WriteReg(R6, 0x00000000)
TA260 002:827.128 - 0.003ms returns 0
TA260 002:827.132 JLINK_WriteReg(R7, 0x00000000)
TA260 002:827.135 - 0.003ms returns 0
TA260 002:827.139 JLINK_WriteReg(R8, 0x00000000)
TA260 002:827.143 - 0.003ms returns 0
TA260 002:827.147 JLINK_WriteReg(R9, 0x20000180)
TA260 002:827.150 - 0.003ms returns 0
TA260 002:827.154 JLINK_WriteReg(R10, 0x00000000)
TA260 002:827.158 - 0.003ms returns 0
TA260 002:827.162 JLINK_WriteReg(R11, 0x00000000)
TA260 002:827.165 - 0.003ms returns 0
TA260 002:827.169 JLINK_WriteReg(R12, 0x00000000)
TA260 002:827.173 - 0.003ms returns 0
TA260 002:827.177 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:827.181 - 0.004ms returns 0
TA260 002:827.185 JLINK_WriteReg(R14, 0x20000001)
TA260 002:827.188 - 0.003ms returns 0
TA260 002:827.193 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:827.196 - 0.003ms returns 0
TA260 002:827.200 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:827.204 - 0.003ms returns 0
TA260 002:827.208 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:827.211 - 0.003ms returns 0
TA260 002:827.215 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:827.218 - 0.003ms returns 0
TA260 002:827.223 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:827.226 - 0.003ms returns 0
TA260 002:827.230 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:827.235 - 0.004ms returns 0x0000002F
TA260 002:827.239 JLINK_Go()
TA260 002:827.250   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:830.072 - 2.831ms 
TA260 002:830.097 JLINK_IsHalted()
TA260 002:830.716 - 0.618ms returns FALSE
TA260 002:830.724 JLINK_HasError()
TA260 002:832.644 JLINK_IsHalted()
TA260 002:833.156 - 0.511ms returns FALSE
TA260 002:833.168 JLINK_HasError()
TA260 002:834.658 JLINK_IsHalted()
TA260 002:837.158   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:837.727 - 3.068ms returns TRUE
TA260 002:837.736 JLINK_ReadReg(R15 (PC))
TA260 002:837.742 - 0.005ms returns 0x20000000
TA260 002:837.746 JLINK_ClrBPEx(BPHandle = 0x0000002F)
TA260 002:837.750 - 0.003ms returns 0x00
TA260 002:837.754 JLINK_ReadReg(R0)
TA260 002:837.758 - 0.003ms returns 0x00000000
TA260 002:838.204 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:838.213   Data:  5C 62 C5 BE 24 36 6C 3F D2 1B C6 BE 50 0F 6C 3F ...
TA260 002:838.224   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:840.895 - 2.690ms returns 0x27C
TA260 002:840.911 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:840.915   Data:  67 8C 5E 3F BB BA FD BE A6 5A 5E 3F 4A 69 FE BE ...
TA260 002:840.930   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:842.848 - 1.936ms returns 0x184
TA260 002:842.863 JLINK_HasError()
TA260 002:842.869 JLINK_WriteReg(R0, 0x08009000)
TA260 002:842.874 - 0.006ms returns 0
TA260 002:842.879 JLINK_WriteReg(R1, 0x00000400)
TA260 002:842.882 - 0.003ms returns 0
TA260 002:842.887 JLINK_WriteReg(R2, 0x20000184)
TA260 002:842.890 - 0.003ms returns 0
TA260 002:842.894 JLINK_WriteReg(R3, 0x00000000)
TA260 002:842.898 - 0.003ms returns 0
TA260 002:842.902 JLINK_WriteReg(R4, 0x00000000)
TA260 002:842.905 - 0.003ms returns 0
TA260 002:842.910 JLINK_WriteReg(R5, 0x00000000)
TA260 002:842.913 - 0.003ms returns 0
TA260 002:842.917 JLINK_WriteReg(R6, 0x00000000)
TA260 002:842.921 - 0.003ms returns 0
TA260 002:842.925 JLINK_WriteReg(R7, 0x00000000)
TA260 002:842.928 - 0.003ms returns 0
TA260 002:842.932 JLINK_WriteReg(R8, 0x00000000)
TA260 002:842.936 - 0.003ms returns 0
TA260 002:842.940 JLINK_WriteReg(R9, 0x20000180)
TA260 002:842.943 - 0.003ms returns 0
TA260 002:842.947 JLINK_WriteReg(R10, 0x00000000)
TA260 002:842.951 - 0.003ms returns 0
TA260 002:842.955 JLINK_WriteReg(R11, 0x00000000)
TA260 002:842.958 - 0.003ms returns 0
TA260 002:842.962 JLINK_WriteReg(R12, 0x00000000)
TA260 002:842.965 - 0.003ms returns 0
TA260 002:842.970 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:842.974 - 0.004ms returns 0
TA260 002:842.978 JLINK_WriteReg(R14, 0x20000001)
TA260 002:842.981 - 0.003ms returns 0
TA260 002:842.986 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:842.989 - 0.004ms returns 0
TA260 002:842.993 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:842.997 - 0.003ms returns 0
TA260 002:843.001 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:843.004 - 0.003ms returns 0
TA260 002:843.008 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:843.012 - 0.003ms returns 0
TA260 002:843.016 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:843.019 - 0.003ms returns 0
TA260 002:843.024 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:843.028 - 0.004ms returns 0x00000030
TA260 002:843.032 JLINK_Go()
TA260 002:843.041   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:845.885 - 2.852ms 
TA260 002:845.913 JLINK_IsHalted()
TA260 002:846.416 - 0.502ms returns FALSE
TA260 002:846.435 JLINK_HasError()
TA260 002:848.670 JLINK_IsHalted()
TA260 002:849.168 - 0.497ms returns FALSE
TA260 002:849.186 JLINK_HasError()
TA260 002:851.160 JLINK_IsHalted()
TA260 002:853.528   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:854.027 - 2.866ms returns TRUE
TA260 002:854.036 JLINK_ReadReg(R15 (PC))
TA260 002:854.049 - 0.013ms returns 0x20000000
TA260 002:854.055 JLINK_ClrBPEx(BPHandle = 0x00000030)
TA260 002:854.059 - 0.004ms returns 0x00
TA260 002:854.063 JLINK_ReadReg(R0)
TA260 002:854.067 - 0.003ms returns 0x00000000
TA260 002:854.449 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:854.458   Data:  DB E0 0E BF 3B 6B 54 3F 3B 34 0F BF 0F 33 54 3F ...
TA260 002:854.469   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:857.079 - 2.630ms returns 0x27C
TA260 002:857.111 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:857.116   Data:  B7 96 41 3F 61 CE 27 BF E1 54 41 3F 40 1A 28 BF ...
TA260 002:857.140   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:859.033 - 1.921ms returns 0x184
TA260 002:859.052 JLINK_HasError()
TA260 002:859.058 JLINK_WriteReg(R0, 0x08009400)
TA260 002:859.064 - 0.006ms returns 0
TA260 002:859.069 JLINK_WriteReg(R1, 0x00000400)
TA260 002:859.072 - 0.003ms returns 0
TA260 002:859.076 JLINK_WriteReg(R2, 0x20000184)
TA260 002:859.080 - 0.003ms returns 0
TA260 002:859.084 JLINK_WriteReg(R3, 0x00000000)
TA260 002:859.087 - 0.003ms returns 0
TA260 002:859.091 JLINK_WriteReg(R4, 0x00000000)
TA260 002:859.094 - 0.003ms returns 0
TA260 002:859.100 JLINK_WriteReg(R5, 0x00000000)
TA260 002:859.103 - 0.003ms returns 0
TA260 002:859.107 JLINK_WriteReg(R6, 0x00000000)
TA260 002:859.110 - 0.003ms returns 0
TA260 002:859.114 JLINK_WriteReg(R7, 0x00000000)
TA260 002:859.118 - 0.003ms returns 0
TA260 002:859.122 JLINK_WriteReg(R8, 0x00000000)
TA260 002:859.125 - 0.003ms returns 0
TA260 002:859.129 JLINK_WriteReg(R9, 0x20000180)
TA260 002:859.132 - 0.003ms returns 0
TA260 002:859.136 JLINK_WriteReg(R10, 0x00000000)
TA260 002:859.140 - 0.003ms returns 0
TA260 002:859.144 JLINK_WriteReg(R11, 0x00000000)
TA260 002:859.147 - 0.003ms returns 0
TA260 002:859.152 JLINK_WriteReg(R12, 0x00000000)
TA260 002:859.155 - 0.003ms returns 0
TA260 002:859.159 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:859.163 - 0.004ms returns 0
TA260 002:859.167 JLINK_WriteReg(R14, 0x20000001)
TA260 002:859.170 - 0.003ms returns 0
TA260 002:859.174 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:859.178 - 0.003ms returns 0
TA260 002:859.182 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:859.185 - 0.003ms returns 0
TA260 002:859.189 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:859.193 - 0.003ms returns 0
TA260 002:859.197 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:859.200 - 0.003ms returns 0
TA260 002:859.204 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:859.208 - 0.003ms returns 0
TA260 002:859.212 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:859.217 - 0.004ms returns 0x00000031
TA260 002:859.221 JLINK_Go()
TA260 002:859.230   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:862.087 - 2.865ms 
TA260 002:862.104 JLINK_IsHalted()
TA260 002:862.653 - 0.548ms returns FALSE
TA260 002:862.664 JLINK_HasError()
TA260 002:864.464 JLINK_IsHalted()
TA260 002:864.941 - 0.476ms returns FALSE
TA260 002:864.962 JLINK_HasError()
TA260 002:866.972 JLINK_IsHalted()
TA260 002:869.300   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:869.802 - 2.829ms returns TRUE
TA260 002:869.811 JLINK_ReadReg(R15 (PC))
TA260 002:869.816 - 0.005ms returns 0x20000000
TA260 002:869.821 JLINK_ClrBPEx(BPHandle = 0x00000031)
TA260 002:869.825 - 0.003ms returns 0x00
TA260 002:869.830 JLINK_ReadReg(R0)
TA260 002:869.833 - 0.003ms returns 0x00000000
TA260 002:870.209 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:870.217   Data:  E7 92 35 BF 8F 76 34 3F B8 D9 35 BF 34 2F 34 3F ...
TA260 002:870.229   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:872.745 - 2.536ms returns 0x27C
TA260 002:872.756 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:872.760   Data:  84 30 1D 3F 87 4C 4A BF 1F E1 1C 3F 13 8A 4A BF ...
TA260 002:872.770   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:874.676 - 1.919ms returns 0x184
TA260 002:874.710 JLINK_HasError()
TA260 002:874.717 JLINK_WriteReg(R0, 0x08009800)
TA260 002:874.722 - 0.005ms returns 0
TA260 002:874.726 JLINK_WriteReg(R1, 0x00000400)
TA260 002:874.730 - 0.003ms returns 0
TA260 002:874.734 JLINK_WriteReg(R2, 0x20000184)
TA260 002:874.737 - 0.003ms returns 0
TA260 002:874.741 JLINK_WriteReg(R3, 0x00000000)
TA260 002:874.745 - 0.003ms returns 0
TA260 002:874.749 JLINK_WriteReg(R4, 0x00000000)
TA260 002:874.752 - 0.003ms returns 0
TA260 002:874.756 JLINK_WriteReg(R5, 0x00000000)
TA260 002:874.760 - 0.003ms returns 0
TA260 002:874.764 JLINK_WriteReg(R6, 0x00000000)
TA260 002:874.767 - 0.003ms returns 0
TA260 002:874.771 JLINK_WriteReg(R7, 0x00000000)
TA260 002:874.775 - 0.003ms returns 0
TA260 002:874.815 JLINK_WriteReg(R8, 0x00000000)
TA260 002:874.819 - 0.040ms returns 0
TA260 002:874.830 JLINK_WriteReg(R9, 0x20000180)
TA260 002:874.834 - 0.003ms returns 0
TA260 002:874.838 JLINK_WriteReg(R10, 0x00000000)
TA260 002:874.842 - 0.003ms returns 0
TA260 002:874.846 JLINK_WriteReg(R11, 0x00000000)
TA260 002:874.849 - 0.003ms returns 0
TA260 002:874.853 JLINK_WriteReg(R12, 0x00000000)
TA260 002:874.857 - 0.003ms returns 0
TA260 002:874.861 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:874.865 - 0.004ms returns 0
TA260 002:874.869 JLINK_WriteReg(R14, 0x20000001)
TA260 002:874.872 - 0.003ms returns 0
TA260 002:874.876 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:874.880 - 0.003ms returns 0
TA260 002:874.884 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:874.887 - 0.003ms returns 0
TA260 002:874.891 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:874.894 - 0.003ms returns 0
TA260 002:874.898 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:874.902 - 0.003ms returns 0
TA260 002:874.906 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:874.910 - 0.003ms returns 0
TA260 002:874.914 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:874.919 - 0.004ms returns 0x00000032
TA260 002:874.923 JLINK_Go()
TA260 002:874.932   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:877.662 - 2.739ms 
TA260 002:877.689 JLINK_IsHalted()
TA260 002:878.194 - 0.505ms returns FALSE
TA260 002:878.208 JLINK_HasError()
TA260 002:879.980 JLINK_IsHalted()
TA260 002:880.474 - 0.494ms returns FALSE
TA260 002:880.480 JLINK_HasError()
TA260 002:881.978 JLINK_IsHalted()
TA260 002:884.272   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:884.805 - 2.826ms returns TRUE
TA260 002:884.822 JLINK_ReadReg(R15 (PC))
TA260 002:884.828 - 0.005ms returns 0x20000000
TA260 002:884.832 JLINK_ClrBPEx(BPHandle = 0x00000032)
TA260 002:884.836 - 0.004ms returns 0x00
TA260 002:884.841 JLINK_ReadReg(R0)
TA260 002:884.844 - 0.003ms returns 0x00000000
TA260 002:885.277 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:885.287   Data:  A4 4A 55 BF 81 92 0D 3F 2C 82 55 BF B3 3E 0D 3F ...
TA260 002:885.298   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:887.933 - 2.655ms returns 0x27C
TA260 002:887.948 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:887.953   Data:  CB 7F E5 3E 79 04 65 BF FE CB E4 3E 54 31 65 BF ...
TA260 002:887.964   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:889.890 - 1.940ms returns 0x184
TA260 002:889.903 JLINK_HasError()
TA260 002:889.909 JLINK_WriteReg(R0, 0x08009C00)
TA260 002:889.914 - 0.005ms returns 0
TA260 002:889.918 JLINK_WriteReg(R1, 0x00000400)
TA260 002:889.923 - 0.005ms returns 0
TA260 002:889.928 JLINK_WriteReg(R2, 0x20000184)
TA260 002:889.932 - 0.003ms returns 0
TA260 002:889.936 JLINK_WriteReg(R3, 0x00000000)
TA260 002:889.939 - 0.003ms returns 0
TA260 002:889.943 JLINK_WriteReg(R4, 0x00000000)
TA260 002:889.946 - 0.003ms returns 0
TA260 002:889.951 JLINK_WriteReg(R5, 0x00000000)
TA260 002:889.954 - 0.003ms returns 0
TA260 002:889.958 JLINK_WriteReg(R6, 0x00000000)
TA260 002:889.961 - 0.003ms returns 0
TA260 002:889.965 JLINK_WriteReg(R7, 0x00000000)
TA260 002:889.969 - 0.003ms returns 0
TA260 002:889.973 JLINK_WriteReg(R8, 0x00000000)
TA260 002:889.977 - 0.003ms returns 0
TA260 002:889.981 JLINK_WriteReg(R9, 0x20000180)
TA260 002:889.984 - 0.003ms returns 0
TA260 002:889.988 JLINK_WriteReg(R10, 0x00000000)
TA260 002:889.991 - 0.003ms returns 0
TA260 002:889.995 JLINK_WriteReg(R11, 0x00000000)
TA260 002:889.999 - 0.003ms returns 0
TA260 002:890.003 JLINK_WriteReg(R12, 0x00000000)
TA260 002:890.006 - 0.003ms returns 0
TA260 002:890.010 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:890.014 - 0.004ms returns 0
TA260 002:890.018 JLINK_WriteReg(R14, 0x20000001)
TA260 002:890.022 - 0.003ms returns 0
TA260 002:890.026 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:890.029 - 0.003ms returns 0
TA260 002:890.034 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:890.037 - 0.003ms returns 0
TA260 002:890.041 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:890.045 - 0.003ms returns 0
TA260 002:890.049 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:890.056 - 0.007ms returns 0
TA260 002:890.061 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:890.064 - 0.003ms returns 0
TA260 002:890.069 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:890.074 - 0.005ms returns 0x00000033
TA260 002:890.078 JLINK_Go()
TA260 002:890.086   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:892.883 - 2.805ms 
TA260 002:892.893 JLINK_IsHalted()
TA260 002:893.346 - 0.452ms returns FALSE
TA260 002:893.356 JLINK_HasError()
TA260 002:894.583 JLINK_IsHalted()
TA260 002:895.072 - 0.488ms returns FALSE
TA260 002:895.080 JLINK_HasError()
TA260 002:897.094 JLINK_IsHalted()
TA260 002:899.412   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:899.877 - 2.783ms returns TRUE
TA260 002:899.884 JLINK_ReadReg(R15 (PC))
TA260 002:899.889 - 0.005ms returns 0x20000000
TA260 002:899.893 JLINK_ClrBPEx(BPHandle = 0x00000033)
TA260 002:899.897 - 0.003ms returns 0x00
TA260 002:899.901 JLINK_ReadReg(R0)
TA260 002:899.905 - 0.003ms returns 0x00000000
TA260 002:900.262 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:900.270   Data:  07 D0 6C BF 55 7B C2 3E 24 F6 6C BF 48 C1 C1 3E ...
TA260 002:900.280   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:902.923 - 2.661ms returns 0x27C
TA260 002:902.932 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:902.936   Data:  C1 CC 87 3E 5B EF 76 BF DA 0A 87 3E CC 09 77 BF ...
TA260 002:902.943   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:904.802 - 1.870ms returns 0x184
TA260 002:904.820 JLINK_HasError()
TA260 002:904.857 JLINK_WriteReg(R0, 0x0800A000)
TA260 002:904.871 - 0.013ms returns 0
TA260 002:904.876 JLINK_WriteReg(R1, 0x00000400)
TA260 002:904.880 - 0.003ms returns 0
TA260 002:904.884 JLINK_WriteReg(R2, 0x20000184)
TA260 002:904.887 - 0.003ms returns 0
TA260 002:904.891 JLINK_WriteReg(R3, 0x00000000)
TA260 002:904.894 - 0.003ms returns 0
TA260 002:904.898 JLINK_WriteReg(R4, 0x00000000)
TA260 002:904.902 - 0.003ms returns 0
TA260 002:904.906 JLINK_WriteReg(R5, 0x00000000)
TA260 002:904.910 - 0.003ms returns 0
TA260 002:904.914 JLINK_WriteReg(R6, 0x00000000)
TA260 002:904.917 - 0.003ms returns 0
TA260 002:904.921 JLINK_WriteReg(R7, 0x00000000)
TA260 002:904.925 - 0.003ms returns 0
TA260 002:904.929 JLINK_WriteReg(R8, 0x00000000)
TA260 002:904.932 - 0.003ms returns 0
TA260 002:904.937 JLINK_WriteReg(R9, 0x20000180)
TA260 002:904.940 - 0.003ms returns 0
TA260 002:904.944 JLINK_WriteReg(R10, 0x00000000)
TA260 002:904.948 - 0.003ms returns 0
TA260 002:904.951 JLINK_WriteReg(R11, 0x00000000)
TA260 002:904.955 - 0.003ms returns 0
TA260 002:904.959 JLINK_WriteReg(R12, 0x00000000)
TA260 002:904.963 - 0.003ms returns 0
TA260 002:904.967 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:904.970 - 0.003ms returns 0
TA260 002:904.975 JLINK_WriteReg(R14, 0x20000001)
TA260 002:904.978 - 0.003ms returns 0
TA260 002:904.982 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:904.985 - 0.003ms returns 0
TA260 002:904.990 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:904.993 - 0.003ms returns 0
TA260 002:904.997 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:905.000 - 0.003ms returns 0
TA260 002:905.004 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:905.008 - 0.003ms returns 0
TA260 002:905.012 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:905.015 - 0.003ms returns 0
TA260 002:905.020 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:905.025 - 0.004ms returns 0x00000034
TA260 002:905.029 JLINK_Go()
TA260 002:905.037   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:907.882 - 2.851ms 
TA260 002:907.900 JLINK_IsHalted()
TA260 002:908.419 - 0.518ms returns FALSE
TA260 002:908.425 JLINK_HasError()
TA260 002:909.600 JLINK_IsHalted()
TA260 002:910.092 - 0.492ms returns FALSE
TA260 002:910.098 JLINK_HasError()
TA260 002:913.598 JLINK_IsHalted()
TA260 002:915.979   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:916.532 - 2.934ms returns TRUE
TA260 002:916.547 JLINK_ReadReg(R15 (PC))
TA260 002:916.553 - 0.005ms returns 0x20000000
TA260 002:916.557 JLINK_ClrBPEx(BPHandle = 0x00000034)
TA260 002:916.561 - 0.003ms returns 0x00
TA260 002:916.570 JLINK_ReadReg(R0)
TA260 002:916.576 - 0.005ms returns 0x00000000
TA260 002:917.149 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:917.159   Data:  AB 3B 7B BF B9 B0 44 3E E7 4E 7B BF 07 26 43 3E ...
TA260 002:917.171   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:919.743 - 2.594ms returns 0x27C
TA260 002:919.751 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:919.755   Data:  E7 86 93 3D E9 5C 7F BF B4 64 90 3D EC 63 7F BF ...
TA260 002:919.763   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:921.656 - 1.905ms returns 0x184
TA260 002:921.663 JLINK_HasError()
TA260 002:921.667 JLINK_WriteReg(R0, 0x0800A400)
TA260 002:921.672 - 0.004ms returns 0
TA260 002:921.677 JLINK_WriteReg(R1, 0x00000400)
TA260 002:921.681 - 0.003ms returns 0
TA260 002:921.685 JLINK_WriteReg(R2, 0x20000184)
TA260 002:921.688 - 0.003ms returns 0
TA260 002:921.692 JLINK_WriteReg(R3, 0x00000000)
TA260 002:921.695 - 0.003ms returns 0
TA260 002:921.699 JLINK_WriteReg(R4, 0x00000000)
TA260 002:921.703 - 0.003ms returns 0
TA260 002:921.707 JLINK_WriteReg(R5, 0x00000000)
TA260 002:921.710 - 0.003ms returns 0
TA260 002:921.714 JLINK_WriteReg(R6, 0x00000000)
TA260 002:921.717 - 0.003ms returns 0
TA260 002:921.721 JLINK_WriteReg(R7, 0x00000000)
TA260 002:921.725 - 0.003ms returns 0
TA260 002:921.729 JLINK_WriteReg(R8, 0x00000000)
TA260 002:921.732 - 0.003ms returns 0
TA260 002:921.736 JLINK_WriteReg(R9, 0x20000180)
TA260 002:921.740 - 0.003ms returns 0
TA260 002:921.744 JLINK_WriteReg(R10, 0x00000000)
TA260 002:921.747 - 0.003ms returns 0
TA260 002:921.751 JLINK_WriteReg(R11, 0x00000000)
TA260 002:921.754 - 0.003ms returns 0
TA260 002:921.758 JLINK_WriteReg(R12, 0x00000000)
TA260 002:921.762 - 0.003ms returns 0
TA260 002:921.766 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:921.770 - 0.003ms returns 0
TA260 002:921.774 JLINK_WriteReg(R14, 0x20000001)
TA260 002:921.777 - 0.003ms returns 0
TA260 002:921.781 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:921.785 - 0.003ms returns 0
TA260 002:921.789 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:921.792 - 0.003ms returns 0
TA260 002:921.796 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:921.800 - 0.003ms returns 0
TA260 002:921.804 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:921.807 - 0.003ms returns 0
TA260 002:921.811 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:921.814 - 0.003ms returns 0
TA260 002:921.819 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:921.823 - 0.004ms returns 0x00000035
TA260 002:921.827 JLINK_Go()
TA260 002:921.835   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:924.585 - 2.757ms 
TA260 002:924.598 JLINK_IsHalted()
TA260 002:925.036 - 0.438ms returns FALSE
TA260 002:925.045 JLINK_HasError()
TA260 002:926.113 JLINK_IsHalted()
TA260 002:926.657 - 0.543ms returns FALSE
TA260 002:926.667 JLINK_HasError()
TA260 002:928.612 JLINK_IsHalted()
TA260 002:930.966   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:931.446 - 2.833ms returns TRUE
TA260 002:931.456 JLINK_ReadReg(R15 (PC))
TA260 002:931.462 - 0.005ms returns 0x20000000
TA260 002:931.494 JLINK_ClrBPEx(BPHandle = 0x00000035)
TA260 002:931.499 - 0.004ms returns 0x00
TA260 002:931.504 JLINK_ReadReg(R0)
TA260 002:931.508 - 0.003ms returns 0x00000000
TA260 002:931.892 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:931.899   Data:  B1 FF 7F BF C7 0F 49 BB 4E FF 7F BF C1 CB 96 BB ...
TA260 002:931.910   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:934.526 - 2.635ms returns 0x27C
TA260 002:934.532 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:934.536   Data:  92 D0 FD BD 38 FA 7D BF 45 77 00 BE 88 ED 7D BF ...
TA260 002:934.543   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:936.356 - 1.823ms returns 0x184
TA260 002:936.379 JLINK_HasError()
TA260 002:936.385 JLINK_WriteReg(R0, 0x0800A800)
TA260 002:936.392 - 0.006ms returns 0
TA260 002:936.396 JLINK_WriteReg(R1, 0x00000400)
TA260 002:936.399 - 0.003ms returns 0
TA260 002:936.404 JLINK_WriteReg(R2, 0x20000184)
TA260 002:936.407 - 0.003ms returns 0
TA260 002:936.416 JLINK_WriteReg(R3, 0x00000000)
TA260 002:936.422 - 0.005ms returns 0
TA260 002:936.426 JLINK_WriteReg(R4, 0x00000000)
TA260 002:936.429 - 0.003ms returns 0
TA260 002:936.433 JLINK_WriteReg(R5, 0x00000000)
TA260 002:936.436 - 0.003ms returns 0
TA260 002:936.440 JLINK_WriteReg(R6, 0x00000000)
TA260 002:936.444 - 0.003ms returns 0
TA260 002:936.448 JLINK_WriteReg(R7, 0x00000000)
TA260 002:936.451 - 0.003ms returns 0
TA260 002:936.455 JLINK_WriteReg(R8, 0x00000000)
TA260 002:936.459 - 0.003ms returns 0
TA260 002:936.462 JLINK_WriteReg(R9, 0x20000180)
TA260 002:936.472 - 0.009ms returns 0
TA260 002:936.477 JLINK_WriteReg(R10, 0x00000000)
TA260 002:936.480 - 0.003ms returns 0
TA260 002:936.484 JLINK_WriteReg(R11, 0x00000000)
TA260 002:936.488 - 0.003ms returns 0
TA260 002:936.492 JLINK_WriteReg(R12, 0x00000000)
TA260 002:936.495 - 0.003ms returns 0
TA260 002:936.499 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:936.503 - 0.003ms returns 0
TA260 002:936.507 JLINK_WriteReg(R14, 0x20000001)
TA260 002:936.510 - 0.003ms returns 0
TA260 002:936.515 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:936.518 - 0.003ms returns 0
TA260 002:936.523 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:936.526 - 0.003ms returns 0
TA260 002:936.530 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:936.534 - 0.003ms returns 0
TA260 002:936.538 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:936.541 - 0.003ms returns 0
TA260 002:936.545 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:936.548 - 0.003ms returns 0
TA260 002:936.553 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:936.557 - 0.004ms returns 0x00000036
TA260 002:936.561 JLINK_Go()
TA260 002:936.572   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:939.326 - 2.764ms 
TA260 002:939.341 JLINK_IsHalted()
TA260 002:939.837 - 0.495ms returns FALSE
TA260 002:939.850 JLINK_HasError()
TA260 002:941.120 JLINK_IsHalted()
TA260 002:941.647 - 0.527ms returns FALSE
TA260 002:941.653 JLINK_HasError()
TA260 002:943.119 JLINK_IsHalted()
TA260 002:943.658 - 0.538ms returns FALSE
TA260 002:943.663 JLINK_HasError()
TA260 002:945.122 JLINK_IsHalted()
TA260 002:947.482   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:947.976 - 2.853ms returns TRUE
TA260 002:947.992 JLINK_ReadReg(R15 (PC))
TA260 002:947.998 - 0.006ms returns 0x20000000
TA260 002:948.002 JLINK_ClrBPEx(BPHandle = 0x00000036)
TA260 002:948.006 - 0.004ms returns 0x00
TA260 002:948.011 JLINK_ReadReg(R0)
TA260 002:948.014 - 0.003ms returns 0x00000000
TA260 002:948.372 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:948.379   Data:  37 ED 7A BF 4F DA 4A BE 3A D9 7A BF 67 64 4C BE ...
TA260 002:948.390   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:950.968 - 2.596ms returns 0x27C
TA260 002:950.975 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:950.979   Data:  C2 59 A1 BE EB D4 72 BF 87 18 A2 BE 04 B5 72 BF ...
TA260 002:950.987   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:952.830 - 1.854ms returns 0x184
TA260 002:952.840 JLINK_HasError()
TA260 002:952.845 JLINK_WriteReg(R0, 0x0800AC00)
TA260 002:952.850 - 0.005ms returns 0
TA260 002:952.854 JLINK_WriteReg(R1, 0x00000400)
TA260 002:952.858 - 0.003ms returns 0
TA260 002:952.862 JLINK_WriteReg(R2, 0x20000184)
TA260 002:952.866 - 0.003ms returns 0
TA260 002:952.870 JLINK_WriteReg(R3, 0x00000000)
TA260 002:952.873 - 0.003ms returns 0
TA260 002:952.877 JLINK_WriteReg(R4, 0x00000000)
TA260 002:952.880 - 0.003ms returns 0
TA260 002:952.884 JLINK_WriteReg(R5, 0x00000000)
TA260 002:952.888 - 0.003ms returns 0
TA260 002:952.892 JLINK_WriteReg(R6, 0x00000000)
TA260 002:952.895 - 0.003ms returns 0
TA260 002:952.899 JLINK_WriteReg(R7, 0x00000000)
TA260 002:952.902 - 0.003ms returns 0
TA260 002:952.906 JLINK_WriteReg(R8, 0x00000000)
TA260 002:952.910 - 0.003ms returns 0
TA260 002:952.914 JLINK_WriteReg(R9, 0x20000180)
TA260 002:952.917 - 0.003ms returns 0
TA260 002:952.921 JLINK_WriteReg(R10, 0x00000000)
TA260 002:952.925 - 0.003ms returns 0
TA260 002:952.929 JLINK_WriteReg(R11, 0x00000000)
TA260 002:952.932 - 0.003ms returns 0
TA260 002:953.000 JLINK_WriteReg(R12, 0x00000000)
TA260 002:953.004 - 0.003ms returns 0
TA260 002:953.008 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:953.012 - 0.003ms returns 0
TA260 002:953.016 JLINK_WriteReg(R14, 0x20000001)
TA260 002:953.019 - 0.003ms returns 0
TA260 002:953.033 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:953.037 - 0.004ms returns 0
TA260 002:953.041 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:953.045 - 0.003ms returns 0
TA260 002:953.049 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:953.052 - 0.003ms returns 0
TA260 002:953.056 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:953.060 - 0.003ms returns 0
TA260 002:953.064 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:953.067 - 0.003ms returns 0
TA260 002:953.072 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:953.076 - 0.004ms returns 0x00000037
TA260 002:953.080 JLINK_Go()
TA260 002:953.088   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:955.933 - 2.852ms 
TA260 002:955.949 JLINK_IsHalted()
TA260 002:956.446 - 0.496ms returns FALSE
TA260 002:956.477 JLINK_HasError()
TA260 002:958.640 JLINK_IsHalted()
TA260 002:959.129 - 0.489ms returns FALSE
TA260 002:959.142 JLINK_HasError()
TA260 002:960.639 JLINK_IsHalted()
TA260 002:962.955   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:963.478 - 2.839ms returns TRUE
TA260 002:963.485 JLINK_ReadReg(R15 (PC))
TA260 002:963.490 - 0.005ms returns 0x20000000
TA260 002:963.494 JLINK_ClrBPEx(BPHandle = 0x00000037)
TA260 002:963.498 - 0.003ms returns 0x00
TA260 002:963.502 JLINK_ReadReg(R0)
TA260 002:963.506 - 0.003ms returns 0x00000000
TA260 002:963.826 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:963.833   Data:  24 36 6C BF 5C 62 C5 BE 50 0F 6C BF D2 1B C6 BE ...
TA260 002:963.843   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:966.480 - 2.653ms returns 0x27C
TA260 002:966.499 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:966.536   Data:  04 0C FD BE A6 5A 5E BF BB BA FD BE C3 28 5E BF ...
TA260 002:966.547   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:968.389 - 1.889ms returns 0x184
TA260 002:968.399 JLINK_HasError()
TA260 002:968.405 JLINK_WriteReg(R0, 0x0800B000)
TA260 002:968.410 - 0.005ms returns 0
TA260 002:968.415 JLINK_WriteReg(R1, 0x00000400)
TA260 002:968.418 - 0.003ms returns 0
TA260 002:968.423 JLINK_WriteReg(R2, 0x20000184)
TA260 002:968.426 - 0.003ms returns 0
TA260 002:968.430 JLINK_WriteReg(R3, 0x00000000)
TA260 002:968.434 - 0.003ms returns 0
TA260 002:968.438 JLINK_WriteReg(R4, 0x00000000)
TA260 002:968.441 - 0.003ms returns 0
TA260 002:968.445 JLINK_WriteReg(R5, 0x00000000)
TA260 002:968.448 - 0.003ms returns 0
TA260 002:968.452 JLINK_WriteReg(R6, 0x00000000)
TA260 002:968.456 - 0.003ms returns 0
TA260 002:968.460 JLINK_WriteReg(R7, 0x00000000)
TA260 002:968.463 - 0.003ms returns 0
TA260 002:968.467 JLINK_WriteReg(R8, 0x00000000)
TA260 002:968.471 - 0.003ms returns 0
TA260 002:968.475 JLINK_WriteReg(R9, 0x20000180)
TA260 002:968.478 - 0.003ms returns 0
TA260 002:968.482 JLINK_WriteReg(R10, 0x00000000)
TA260 002:968.485 - 0.003ms returns 0
TA260 002:968.489 JLINK_WriteReg(R11, 0x00000000)
TA260 002:968.493 - 0.003ms returns 0
TA260 002:968.497 JLINK_WriteReg(R12, 0x00000000)
TA260 002:968.500 - 0.003ms returns 0
TA260 002:968.504 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:968.508 - 0.004ms returns 0
TA260 002:968.512 JLINK_WriteReg(R14, 0x20000001)
TA260 002:968.516 - 0.003ms returns 0
TA260 002:968.520 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:968.523 - 0.003ms returns 0
TA260 002:968.527 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:968.530 - 0.003ms returns 0
TA260 002:968.534 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:968.538 - 0.003ms returns 0
TA260 002:968.542 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:968.545 - 0.003ms returns 0
TA260 002:968.549 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:968.553 - 0.003ms returns 0
TA260 002:968.557 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:968.561 - 0.004ms returns 0x00000038
TA260 002:968.565 JLINK_Go()
TA260 002:968.577   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:971.288 - 2.721ms 
TA260 002:971.300 JLINK_IsHalted()
TA260 002:971.801 - 0.500ms returns FALSE
TA260 002:971.813 JLINK_HasError()
TA260 002:973.155 JLINK_IsHalted()
TA260 002:973.650 - 0.494ms returns FALSE
TA260 002:973.658 JLINK_HasError()
TA260 002:975.151 JLINK_IsHalted()
TA260 002:975.652 - 0.500ms returns FALSE
TA260 002:975.664 JLINK_HasError()
TA260 002:977.668 JLINK_IsHalted()
TA260 002:980.003   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:980.479 - 2.810ms returns TRUE
TA260 002:980.486 JLINK_ReadReg(R15 (PC))
TA260 002:980.491 - 0.005ms returns 0x20000000
TA260 002:980.496 JLINK_ClrBPEx(BPHandle = 0x00000038)
TA260 002:980.500 - 0.003ms returns 0x00
TA260 002:980.504 JLINK_ReadReg(R0)
TA260 002:980.508 - 0.003ms returns 0x00000000
TA260 002:980.879 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:980.886   Data:  3B 6B 54 BF DB E0 0E BF 0F 33 54 BF 3B 34 0F BF ...
TA260 002:980.897   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:983.476 - 2.597ms returns 0x27C
TA260 002:983.488 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:983.491   Data:  68 82 27 BF E1 54 41 BF 61 CE 27 BF EC 12 41 BF ...
TA260 002:983.498   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:985.352 - 1.864ms returns 0x184
TA260 002:985.373 JLINK_HasError()
TA260 002:985.406 JLINK_WriteReg(R0, 0x0800B400)
TA260 002:985.413 - 0.007ms returns 0
TA260 002:985.418 JLINK_WriteReg(R1, 0x00000400)
TA260 002:985.421 - 0.003ms returns 0
TA260 002:985.426 JLINK_WriteReg(R2, 0x20000184)
TA260 002:985.429 - 0.003ms returns 0
TA260 002:985.433 JLINK_WriteReg(R3, 0x00000000)
TA260 002:985.437 - 0.003ms returns 0
TA260 002:985.441 JLINK_WriteReg(R4, 0x00000000)
TA260 002:985.444 - 0.003ms returns 0
TA260 002:985.449 JLINK_WriteReg(R5, 0x00000000)
TA260 002:985.453 - 0.004ms returns 0
TA260 002:985.457 JLINK_WriteReg(R6, 0x00000000)
TA260 002:985.461 - 0.003ms returns 0
TA260 002:985.465 JLINK_WriteReg(R7, 0x00000000)
TA260 002:985.469 - 0.003ms returns 0
TA260 002:985.474 JLINK_WriteReg(R8, 0x00000000)
TA260 002:985.477 - 0.003ms returns 0
TA260 002:985.481 JLINK_WriteReg(R9, 0x20000180)
TA260 002:985.485 - 0.003ms returns 0
TA260 002:985.489 JLINK_WriteReg(R10, 0x00000000)
TA260 002:985.493 - 0.003ms returns 0
TA260 002:985.497 JLINK_WriteReg(R11, 0x00000000)
TA260 002:985.501 - 0.003ms returns 0
TA260 002:985.505 JLINK_WriteReg(R12, 0x00000000)
TA260 002:985.508 - 0.003ms returns 0
TA260 002:985.512 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:985.516 - 0.004ms returns 0
TA260 002:985.520 JLINK_WriteReg(R14, 0x20000001)
TA260 002:985.524 - 0.003ms returns 0
TA260 002:985.528 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:985.531 - 0.003ms returns 0
TA260 002:985.536 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:985.539 - 0.003ms returns 0
TA260 002:985.543 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:985.546 - 0.003ms returns 0
TA260 002:985.550 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:985.554 - 0.003ms returns 0
TA260 002:985.558 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:985.561 - 0.003ms returns 0
TA260 002:985.566 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:985.571 - 0.004ms returns 0x00000039
TA260 002:985.575 JLINK_Go()
TA260 002:985.584   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:988.330 - 2.754ms 
TA260 002:988.344 JLINK_IsHalted()
TA260 002:988.842 - 0.498ms returns FALSE
TA260 002:988.848 JLINK_HasError()
TA260 002:990.168 JLINK_IsHalted()
TA260 002:990.645 - 0.477ms returns FALSE
TA260 002:990.651 JLINK_HasError()
TA260 002:992.168 JLINK_IsHalted()
TA260 002:992.645 - 0.477ms returns FALSE
TA260 002:992.651 JLINK_HasError()
TA260 002:994.183 JLINK_IsHalted()
TA260 002:996.542   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:997.076 - 2.892ms returns TRUE
TA260 002:997.085 JLINK_ReadReg(R15 (PC))
TA260 002:997.091 - 0.005ms returns 0x20000000
TA260 002:997.095 JLINK_ClrBPEx(BPHandle = 0x00000039)
TA260 002:997.099 - 0.003ms returns 0x00
TA260 002:997.103 JLINK_ReadReg(R0)
TA260 002:997.110 - 0.006ms returns 0x00000000
TA260 002:997.474 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:997.482   Data:  8F 76 34 BF E7 92 35 BF 34 2F 34 BF B8 D9 35 BF ...
TA260 002:997.493   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:000.117 - 2.643ms returns 0x27C
TA260 003:000.129 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:000.133   Data:  DC 0E 4A BF 1F E1 1C BF 87 4C 4A BF A2 91 1C BF ...
TA260 003:000.142   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:002.027 - 1.898ms returns 0x184
TA260 003:002.041 JLINK_HasError()
TA260 003:002.046 JLINK_WriteReg(R0, 0x0800B800)
TA260 003:002.051 - 0.005ms returns 0
TA260 003:002.056 JLINK_WriteReg(R1, 0x00000400)
TA260 003:002.059 - 0.003ms returns 0
TA260 003:002.063 JLINK_WriteReg(R2, 0x20000184)
TA260 003:002.067 - 0.003ms returns 0
TA260 003:002.071 JLINK_WriteReg(R3, 0x00000000)
TA260 003:002.074 - 0.003ms returns 0
TA260 003:002.078 JLINK_WriteReg(R4, 0x00000000)
TA260 003:002.082 - 0.003ms returns 0
TA260 003:002.085 JLINK_WriteReg(R5, 0x00000000)
TA260 003:002.089 - 0.003ms returns 0
TA260 003:002.093 JLINK_WriteReg(R6, 0x00000000)
TA260 003:002.096 - 0.003ms returns 0
TA260 003:002.100 JLINK_WriteReg(R7, 0x00000000)
TA260 003:002.104 - 0.003ms returns 0
TA260 003:002.108 JLINK_WriteReg(R8, 0x00000000)
TA260 003:002.111 - 0.003ms returns 0
TA260 003:002.116 JLINK_WriteReg(R9, 0x20000180)
TA260 003:002.119 - 0.003ms returns 0
TA260 003:002.123 JLINK_WriteReg(R10, 0x00000000)
TA260 003:002.127 - 0.003ms returns 0
TA260 003:002.131 JLINK_WriteReg(R11, 0x00000000)
TA260 003:002.134 - 0.003ms returns 0
TA260 003:002.138 JLINK_WriteReg(R12, 0x00000000)
TA260 003:002.141 - 0.003ms returns 0
TA260 003:002.146 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:002.150 - 0.004ms returns 0
TA260 003:002.154 JLINK_WriteReg(R14, 0x20000001)
TA260 003:002.157 - 0.003ms returns 0
TA260 003:002.161 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:002.164 - 0.003ms returns 0
TA260 003:002.168 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:002.172 - 0.003ms returns 0
TA260 003:002.176 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:002.179 - 0.003ms returns 0
TA260 003:002.183 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:002.187 - 0.003ms returns 0
TA260 003:002.191 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:002.194 - 0.003ms returns 0
TA260 003:002.199 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:002.203 - 0.004ms returns 0x0000003A
TA260 003:002.208 JLINK_Go()
TA260 003:002.220   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:004.971 - 2.763ms 
TA260 003:004.981 JLINK_IsHalted()
TA260 003:005.528 - 0.546ms returns FALSE
TA260 003:005.541 JLINK_HasError()
TA260 003:007.187 JLINK_IsHalted()
TA260 003:007.682 - 0.494ms returns FALSE
TA260 003:007.688 JLINK_HasError()
TA260 003:009.181 JLINK_IsHalted()
TA260 003:011.548   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:012.047 - 2.866ms returns TRUE
TA260 003:012.058 JLINK_ReadReg(R15 (PC))
TA260 003:012.063 - 0.005ms returns 0x20000000
TA260 003:012.095 JLINK_ClrBPEx(BPHandle = 0x0000003A)
TA260 003:012.100 - 0.005ms returns 0x00
TA260 003:012.104 JLINK_ReadReg(R0)
TA260 003:012.108 - 0.003ms returns 0x00000000
TA260 003:012.471 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:012.478   Data:  81 92 0D BF A4 4A 55 BF B3 3E 0D BF 2C 82 55 BF ...
TA260 003:012.489   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:015.109 - 2.637ms returns 0x27C
TA260 003:015.123 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:015.127   Data:  7B D7 64 BF FE CB E4 BE 79 04 65 BF 0E 18 E4 BE ...
TA260 003:015.136   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:017.078 - 1.954ms returns 0x184
TA260 003:017.097 JLINK_HasError()
TA260 003:017.103 JLINK_WriteReg(R0, 0x0800BC00)
TA260 003:017.109 - 0.006ms returns 0
TA260 003:017.113 JLINK_WriteReg(R1, 0x00000400)
TA260 003:017.117 - 0.003ms returns 0
TA260 003:017.121 JLINK_WriteReg(R2, 0x20000184)
TA260 003:017.124 - 0.003ms returns 0
TA260 003:017.128 JLINK_WriteReg(R3, 0x00000000)
TA260 003:017.136 - 0.007ms returns 0
TA260 003:017.140 JLINK_WriteReg(R4, 0x00000000)
TA260 003:017.144 - 0.003ms returns 0
TA260 003:017.148 JLINK_WriteReg(R5, 0x00000000)
TA260 003:017.151 - 0.003ms returns 0
TA260 003:017.155 JLINK_WriteReg(R6, 0x00000000)
TA260 003:017.159 - 0.003ms returns 0
TA260 003:017.163 JLINK_WriteReg(R7, 0x00000000)
TA260 003:017.166 - 0.003ms returns 0
TA260 003:017.170 JLINK_WriteReg(R8, 0x00000000)
TA260 003:017.174 - 0.003ms returns 0
TA260 003:017.178 JLINK_WriteReg(R9, 0x20000180)
TA260 003:017.181 - 0.003ms returns 0
TA260 003:017.185 JLINK_WriteReg(R10, 0x00000000)
TA260 003:017.188 - 0.003ms returns 0
TA260 003:017.193 JLINK_WriteReg(R11, 0x00000000)
TA260 003:017.196 - 0.003ms returns 0
TA260 003:017.200 JLINK_WriteReg(R12, 0x00000000)
TA260 003:017.203 - 0.003ms returns 0
TA260 003:017.207 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:017.211 - 0.004ms returns 0
TA260 003:017.216 JLINK_WriteReg(R14, 0x20000001)
TA260 003:017.219 - 0.003ms returns 0
TA260 003:017.223 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:017.227 - 0.003ms returns 0
TA260 003:017.231 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:017.234 - 0.003ms returns 0
TA260 003:017.238 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:017.242 - 0.003ms returns 0
TA260 003:017.246 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:017.249 - 0.003ms returns 0
TA260 003:017.253 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:017.257 - 0.003ms returns 0
TA260 003:017.262 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:017.266 - 0.004ms returns 0x0000003B
TA260 003:017.270 JLINK_Go()
TA260 003:017.280   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:020.070 - 2.799ms 
TA260 003:020.083 JLINK_IsHalted()
TA260 003:020.546 - 0.463ms returns FALSE
TA260 003:020.552 JLINK_HasError()
TA260 003:021.700 JLINK_IsHalted()
TA260 003:022.172 - 0.471ms returns FALSE
TA260 003:022.179 JLINK_HasError()
TA260 003:023.692 JLINK_IsHalted()
TA260 003:024.194 - 0.502ms returns FALSE
TA260 003:024.199 JLINK_HasError()
TA260 003:026.206 JLINK_IsHalted()
TA260 003:028.476   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:028.968 - 2.761ms returns TRUE
TA260 003:028.977 JLINK_ReadReg(R15 (PC))
TA260 003:028.984 - 0.006ms returns 0x20000000
TA260 003:028.989 JLINK_ClrBPEx(BPHandle = 0x0000003B)
TA260 003:028.992 - 0.003ms returns 0x00
TA260 003:028.997 JLINK_ReadReg(R0)
TA260 003:029.000 - 0.003ms returns 0x00000000
TA260 003:029.372 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:029.379   Data:  55 7B C2 BE 07 D0 6C BF 48 C1 C1 BE 24 F6 6C BF ...
TA260 003:029.390   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:031.977 - 2.605ms returns 0x27C
TA260 003:031.985 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:031.988   Data:  C4 D4 76 BF DA 0A 87 BE 5B EF 76 BF DF 48 86 BE ...
TA260 003:031.996   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:033.832 - 1.847ms returns 0x184
TA260 003:033.842 JLINK_HasError()
TA260 003:033.848 JLINK_WriteReg(R0, 0x0800C000)
TA260 003:033.852 - 0.004ms returns 0
TA260 003:033.857 JLINK_WriteReg(R1, 0x00000400)
TA260 003:033.860 - 0.003ms returns 0
TA260 003:033.864 JLINK_WriteReg(R2, 0x20000184)
TA260 003:033.868 - 0.003ms returns 0
TA260 003:033.872 JLINK_WriteReg(R3, 0x00000000)
TA260 003:033.875 - 0.003ms returns 0
TA260 003:033.879 JLINK_WriteReg(R4, 0x00000000)
TA260 003:033.883 - 0.003ms returns 0
TA260 003:033.887 JLINK_WriteReg(R5, 0x00000000)
TA260 003:033.890 - 0.003ms returns 0
TA260 003:033.894 JLINK_WriteReg(R6, 0x00000000)
TA260 003:033.898 - 0.003ms returns 0
TA260 003:033.902 JLINK_WriteReg(R7, 0x00000000)
TA260 003:033.905 - 0.003ms returns 0
TA260 003:033.909 JLINK_WriteReg(R8, 0x00000000)
TA260 003:033.913 - 0.003ms returns 0
TA260 003:033.917 JLINK_WriteReg(R9, 0x20000180)
TA260 003:033.920 - 0.003ms returns 0
TA260 003:033.924 JLINK_WriteReg(R10, 0x00000000)
TA260 003:033.928 - 0.003ms returns 0
TA260 003:033.932 JLINK_WriteReg(R11, 0x00000000)
TA260 003:033.935 - 0.003ms returns 0
TA260 003:033.939 JLINK_WriteReg(R12, 0x00000000)
TA260 003:033.945 - 0.006ms returns 0
TA260 003:033.949 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:033.953 - 0.003ms returns 0
TA260 003:033.957 JLINK_WriteReg(R14, 0x20000001)
TA260 003:033.960 - 0.003ms returns 0
TA260 003:033.964 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:033.968 - 0.003ms returns 0
TA260 003:033.972 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:033.976 - 0.003ms returns 0
TA260 003:033.980 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:033.983 - 0.003ms returns 0
TA260 003:033.987 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:033.990 - 0.003ms returns 0
TA260 003:033.994 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:033.998 - 0.003ms returns 0
TA260 003:034.003 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:034.007 - 0.004ms returns 0x0000003C
TA260 003:034.011 JLINK_Go()
TA260 003:034.019   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:036.893 - 2.881ms 
TA260 003:036.960 JLINK_IsHalted()
TA260 003:037.445 - 0.484ms returns FALSE
TA260 003:037.459 JLINK_HasError()
TA260 003:038.708 JLINK_IsHalted()
TA260 003:039.205 - 0.496ms returns FALSE
TA260 003:039.211 JLINK_HasError()
TA260 003:040.712 JLINK_IsHalted()
TA260 003:041.196 - 0.484ms returns FALSE
TA260 003:041.204 JLINK_HasError()
TA260 003:042.709 JLINK_IsHalted()
TA260 003:045.058   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:045.664 - 2.954ms returns TRUE
TA260 003:045.678 JLINK_ReadReg(R15 (PC))
TA260 003:045.683 - 0.005ms returns 0x20000000
TA260 003:045.688 JLINK_ClrBPEx(BPHandle = 0x0000003C)
TA260 003:045.692 - 0.003ms returns 0x00
TA260 003:045.696 JLINK_ReadReg(R0)
TA260 003:045.700 - 0.003ms returns 0x00000000
TA260 003:046.074 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:046.082   Data:  B9 B0 44 BE AB 3B 7B BF 07 26 43 BE E7 4E 7B BF ...
TA260 003:046.093   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:048.663 - 2.588ms returns 0x27C
TA260 003:048.679 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:048.683   Data:  BF 55 7F BF B4 64 90 BD E9 5C 7F BF 6A 42 8D BD ...
TA260 003:048.693   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:050.574 - 1.894ms returns 0x184
TA260 003:050.588 JLINK_HasError()
TA260 003:050.593 JLINK_WriteReg(R0, 0x0800C400)
TA260 003:050.598 - 0.005ms returns 0
TA260 003:050.603 JLINK_WriteReg(R1, 0x00000400)
TA260 003:050.606 - 0.003ms returns 0
TA260 003:050.610 JLINK_WriteReg(R2, 0x20000184)
TA260 003:050.614 - 0.003ms returns 0
TA260 003:050.618 JLINK_WriteReg(R3, 0x00000000)
TA260 003:050.621 - 0.003ms returns 0
TA260 003:050.626 JLINK_WriteReg(R4, 0x00000000)
TA260 003:050.629 - 0.003ms returns 0
TA260 003:050.633 JLINK_WriteReg(R5, 0x00000000)
TA260 003:050.636 - 0.003ms returns 0
TA260 003:050.640 JLINK_WriteReg(R6, 0x00000000)
TA260 003:050.644 - 0.003ms returns 0
TA260 003:050.648 JLINK_WriteReg(R7, 0x00000000)
TA260 003:050.651 - 0.003ms returns 0
TA260 003:050.655 JLINK_WriteReg(R8, 0x00000000)
TA260 003:050.659 - 0.003ms returns 0
TA260 003:050.663 JLINK_WriteReg(R9, 0x20000180)
TA260 003:050.666 - 0.003ms returns 0
TA260 003:050.670 JLINK_WriteReg(R10, 0x00000000)
TA260 003:050.674 - 0.003ms returns 0
TA260 003:050.678 JLINK_WriteReg(R11, 0x00000000)
TA260 003:050.681 - 0.003ms returns 0
TA260 003:050.685 JLINK_WriteReg(R12, 0x00000000)
TA260 003:050.688 - 0.003ms returns 0
TA260 003:050.692 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:050.696 - 0.003ms returns 0
TA260 003:050.700 JLINK_WriteReg(R14, 0x20000001)
TA260 003:050.704 - 0.003ms returns 0
TA260 003:050.708 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:050.712 - 0.004ms returns 0
TA260 003:050.716 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:050.719 - 0.003ms returns 0
TA260 003:050.724 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:050.727 - 0.003ms returns 0
TA260 003:050.731 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:050.734 - 0.003ms returns 0
TA260 003:050.738 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:050.742 - 0.003ms returns 0
TA260 003:050.746 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:050.751 - 0.004ms returns 0x0000003D
TA260 003:050.758 JLINK_Go()
TA260 003:050.767   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:053.511 - 2.752ms 
TA260 003:053.520 JLINK_IsHalted()
TA260 003:054.023 - 0.502ms returns FALSE
TA260 003:054.028 JLINK_HasError()
TA260 003:056.232 JLINK_IsHalted()
TA260 003:056.745 - 0.511ms returns FALSE
TA260 003:056.758 JLINK_HasError()
TA260 003:058.234 JLINK_IsHalted()
TA260 003:060.589   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:061.093 - 2.858ms returns TRUE
TA260 003:061.099 JLINK_ReadReg(R15 (PC))
TA260 003:061.104 - 0.005ms returns 0x20000000
TA260 003:061.108 JLINK_ClrBPEx(BPHandle = 0x0000003D)
TA260 003:061.113 - 0.004ms returns 0x00
TA260 003:061.117 JLINK_ReadReg(R0)
TA260 003:061.120 - 0.003ms returns 0x00000000
TA260 003:061.510 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:061.518   Data:  C7 0F 49 3B B1 FF 7F BF C1 CB 96 3B 4E FF 7F BF ...
TA260 003:061.530   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:064.116 - 2.605ms returns 0x27C
TA260 003:064.126 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:064.130   Data:  C2 06 7E BF 45 77 00 3E 38 FA 7D BF 2E 06 02 3E ...
TA260 003:064.137   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:066.037 - 1.910ms returns 0x184
TA260 003:066.061 JLINK_HasError()
TA260 003:066.095 JLINK_WriteReg(R0, 0x0800C800)
TA260 003:066.102 - 0.007ms returns 0
TA260 003:066.107 JLINK_WriteReg(R1, 0x00000400)
TA260 003:066.110 - 0.003ms returns 0
TA260 003:066.115 JLINK_WriteReg(R2, 0x20000184)
TA260 003:066.118 - 0.003ms returns 0
TA260 003:066.122 JLINK_WriteReg(R3, 0x00000000)
TA260 003:066.126 - 0.003ms returns 0
TA260 003:066.130 JLINK_WriteReg(R4, 0x00000000)
TA260 003:066.133 - 0.003ms returns 0
TA260 003:066.138 JLINK_WriteReg(R5, 0x00000000)
TA260 003:066.142 - 0.003ms returns 0
TA260 003:066.146 JLINK_WriteReg(R6, 0x00000000)
TA260 003:066.149 - 0.003ms returns 0
TA260 003:066.153 JLINK_WriteReg(R7, 0x00000000)
TA260 003:066.156 - 0.003ms returns 0
TA260 003:066.160 JLINK_WriteReg(R8, 0x00000000)
TA260 003:066.165 - 0.004ms returns 0
TA260 003:066.169 JLINK_WriteReg(R9, 0x20000180)
TA260 003:066.172 - 0.003ms returns 0
TA260 003:066.176 JLINK_WriteReg(R10, 0x00000000)
TA260 003:066.180 - 0.003ms returns 0
TA260 003:066.184 JLINK_WriteReg(R11, 0x00000000)
TA260 003:066.187 - 0.003ms returns 0
TA260 003:066.191 JLINK_WriteReg(R12, 0x00000000)
TA260 003:066.194 - 0.003ms returns 0
TA260 003:066.198 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:066.202 - 0.004ms returns 0
TA260 003:066.206 JLINK_WriteReg(R14, 0x20000001)
TA260 003:066.210 - 0.003ms returns 0
TA260 003:066.214 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:066.217 - 0.003ms returns 0
TA260 003:066.221 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:066.225 - 0.003ms returns 0
TA260 003:066.229 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:066.232 - 0.003ms returns 0
TA260 003:066.236 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:066.240 - 0.003ms returns 0
TA260 003:066.244 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:066.247 - 0.003ms returns 0
TA260 003:066.252 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:066.256 - 0.004ms returns 0x0000003E
TA260 003:066.262 JLINK_Go()
TA260 003:066.272   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:069.071 - 2.808ms 
TA260 003:069.084 JLINK_IsHalted()
TA260 003:069.655 - 0.571ms returns FALSE
TA260 003:069.661 JLINK_HasError()
TA260 003:072.416 JLINK_IsHalted()
TA260 003:072.898 - 0.482ms returns FALSE
TA260 003:072.904 JLINK_HasError()
TA260 003:074.415 JLINK_IsHalted()
TA260 003:076.789   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:077.314 - 2.897ms returns TRUE
TA260 003:077.329 JLINK_ReadReg(R15 (PC))
TA260 003:077.335 - 0.005ms returns 0x20000000
TA260 003:077.340 JLINK_ClrBPEx(BPHandle = 0x0000003E)
TA260 003:077.344 - 0.004ms returns 0x00
TA260 003:077.349 JLINK_ReadReg(R0)
TA260 003:077.354 - 0.004ms returns 0x00000000
TA260 003:077.716 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:077.723   Data:  4F DA 4A 3E 37 ED 7A BF 67 64 4C 3E 3A D9 7A BF ...
TA260 003:077.738   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:080.385 - 2.668ms returns 0x27C
TA260 003:080.396 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:080.401   Data:  AC F4 72 BF 87 18 A2 3E EB D4 72 BF 33 D7 A2 3E ...
TA260 003:080.410   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:082.254 - 1.857ms returns 0x184
TA260 003:082.262 JLINK_HasError()
TA260 003:082.267 JLINK_WriteReg(R0, 0x0800CC00)
TA260 003:082.272 - 0.005ms returns 0
TA260 003:082.276 JLINK_WriteReg(R1, 0x00000400)
TA260 003:082.280 - 0.003ms returns 0
TA260 003:082.284 JLINK_WriteReg(R2, 0x20000184)
TA260 003:082.288 - 0.003ms returns 0
TA260 003:082.292 JLINK_WriteReg(R3, 0x00000000)
TA260 003:082.295 - 0.003ms returns 0
TA260 003:082.299 JLINK_WriteReg(R4, 0x00000000)
TA260 003:082.302 - 0.003ms returns 0
TA260 003:082.306 JLINK_WriteReg(R5, 0x00000000)
TA260 003:082.310 - 0.003ms returns 0
TA260 003:082.314 JLINK_WriteReg(R6, 0x00000000)
TA260 003:082.318 - 0.003ms returns 0
TA260 003:082.322 JLINK_WriteReg(R7, 0x00000000)
TA260 003:082.325 - 0.003ms returns 0
TA260 003:082.329 JLINK_WriteReg(R8, 0x00000000)
TA260 003:082.332 - 0.003ms returns 0
TA260 003:082.336 JLINK_WriteReg(R9, 0x20000180)
TA260 003:082.340 - 0.003ms returns 0
TA260 003:082.344 JLINK_WriteReg(R10, 0x00000000)
TA260 003:082.348 - 0.003ms returns 0
TA260 003:082.352 JLINK_WriteReg(R11, 0x00000000)
TA260 003:082.355 - 0.003ms returns 0
TA260 003:082.359 JLINK_WriteReg(R12, 0x00000000)
TA260 003:082.363 - 0.003ms returns 0
TA260 003:082.367 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:082.370 - 0.004ms returns 0
TA260 003:082.375 JLINK_WriteReg(R14, 0x20000001)
TA260 003:082.378 - 0.003ms returns 0
TA260 003:082.382 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:082.386 - 0.003ms returns 0
TA260 003:082.390 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:082.393 - 0.003ms returns 0
TA260 003:082.397 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:082.400 - 0.003ms returns 0
TA260 003:082.404 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:082.408 - 0.003ms returns 0
TA260 003:082.412 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:082.415 - 0.003ms returns 0
TA260 003:082.420 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:082.424 - 0.004ms returns 0x0000003F
TA260 003:082.428 JLINK_Go()
TA260 003:082.436   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:085.177 - 2.748ms 
TA260 003:085.192 JLINK_IsHalted()
TA260 003:085.657 - 0.465ms returns FALSE
TA260 003:085.669 JLINK_HasError()
TA260 003:087.437 JLINK_IsHalted()
TA260 003:087.891 - 0.453ms returns FALSE
TA260 003:087.904 JLINK_HasError()
TA260 003:089.440 JLINK_IsHalted()
TA260 003:091.731   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:092.205 - 2.765ms returns TRUE
TA260 003:092.216 JLINK_ReadReg(R15 (PC))
TA260 003:092.221 - 0.004ms returns 0x20000000
TA260 003:092.251 JLINK_ClrBPEx(BPHandle = 0x0000003F)
TA260 003:092.256 - 0.005ms returns 0x00
TA260 003:092.261 JLINK_ReadReg(R0)
TA260 003:092.264 - 0.003ms returns 0x00000000
TA260 003:092.624 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:092.632   Data:  5C 62 C5 3E 24 36 6C BF D2 1B C6 3E 50 0F 6C BF ...
TA260 003:092.642   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:095.261 - 2.635ms returns 0x27C
TA260 003:095.279 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:095.283   Data:  67 8C 5E BF BB BA FD 3E A6 5A 5E BF 4A 69 FE 3E ...
TA260 003:095.293   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:097.213 - 1.933ms returns 0x184
TA260 003:097.228 JLINK_HasError()
TA260 003:097.234 JLINK_WriteReg(R0, 0x0800D000)
TA260 003:097.240 - 0.006ms returns 0
TA260 003:097.244 JLINK_WriteReg(R1, 0x00000400)
TA260 003:097.247 - 0.003ms returns 0
TA260 003:097.252 JLINK_WriteReg(R2, 0x20000184)
TA260 003:097.255 - 0.003ms returns 0
TA260 003:097.259 JLINK_WriteReg(R3, 0x00000000)
TA260 003:097.263 - 0.003ms returns 0
TA260 003:097.267 JLINK_WriteReg(R4, 0x00000000)
TA260 003:097.270 - 0.003ms returns 0
TA260 003:097.274 JLINK_WriteReg(R5, 0x00000000)
TA260 003:097.281 - 0.007ms returns 0
TA260 003:097.286 JLINK_WriteReg(R6, 0x00000000)
TA260 003:097.289 - 0.003ms returns 0
TA260 003:097.293 JLINK_WriteReg(R7, 0x00000000)
TA260 003:097.297 - 0.003ms returns 0
TA260 003:097.301 JLINK_WriteReg(R8, 0x00000000)
TA260 003:097.304 - 0.003ms returns 0
TA260 003:097.308 JLINK_WriteReg(R9, 0x20000180)
TA260 003:097.312 - 0.003ms returns 0
TA260 003:097.316 JLINK_WriteReg(R10, 0x00000000)
TA260 003:097.319 - 0.003ms returns 0
TA260 003:097.324 JLINK_WriteReg(R11, 0x00000000)
TA260 003:097.327 - 0.003ms returns 0
TA260 003:097.331 JLINK_WriteReg(R12, 0x00000000)
TA260 003:097.334 - 0.003ms returns 0
TA260 003:097.338 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:097.342 - 0.003ms returns 0
TA260 003:097.346 JLINK_WriteReg(R14, 0x20000001)
TA260 003:097.350 - 0.003ms returns 0
TA260 003:097.354 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:097.357 - 0.003ms returns 0
TA260 003:097.361 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:097.365 - 0.003ms returns 0
TA260 003:097.369 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:097.372 - 0.003ms returns 0
TA260 003:097.376 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:097.380 - 0.003ms returns 0
TA260 003:097.384 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:097.387 - 0.003ms returns 0
TA260 003:097.392 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:097.396 - 0.004ms returns 0x00000040
TA260 003:097.400 JLINK_Go()
TA260 003:097.409   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:100.216 - 2.815ms 
TA260 003:100.229 JLINK_IsHalted()
TA260 003:100.723 - 0.494ms returns FALSE
TA260 003:100.729 JLINK_HasError()
TA260 003:101.944 JLINK_IsHalted()
TA260 003:102.400 - 0.456ms returns FALSE
TA260 003:102.406 JLINK_HasError()
TA260 003:103.942 JLINK_IsHalted()
TA260 003:104.408 - 0.465ms returns FALSE
TA260 003:104.413 JLINK_HasError()
TA260 003:105.951 JLINK_IsHalted()
TA260 003:108.238   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:108.788 - 2.836ms returns TRUE
TA260 003:108.795 JLINK_ReadReg(R15 (PC))
TA260 003:108.800 - 0.005ms returns 0x20000000
TA260 003:108.805 JLINK_ClrBPEx(BPHandle = 0x00000040)
TA260 003:108.809 - 0.003ms returns 0x00
TA260 003:108.813 JLINK_ReadReg(R0)
TA260 003:108.817 - 0.004ms returns 0x00000000
TA260 003:109.182 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:109.189   Data:  DB E0 0E 3F 3B 6B 54 BF 3B 34 0F 3F 0F 33 54 BF ...
TA260 003:109.199   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:111.744 - 2.562ms returns 0x27C
TA260 003:111.758 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:111.762   Data:  B7 96 41 BF 61 CE 27 3F E1 54 41 BF 40 1A 28 3F ...
TA260 003:111.772   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:113.652 - 1.893ms returns 0x184
TA260 003:113.664 JLINK_HasError()
TA260 003:113.670 JLINK_WriteReg(R0, 0x0800D400)
TA260 003:113.675 - 0.005ms returns 0
TA260 003:113.680 JLINK_WriteReg(R1, 0x00000400)
TA260 003:113.689 - 0.003ms returns 0
TA260 003:113.693 JLINK_WriteReg(R2, 0x20000184)
TA260 003:113.697 - 0.003ms returns 0
TA260 003:113.701 JLINK_WriteReg(R3, 0x00000000)
TA260 003:113.704 - 0.003ms returns 0
TA260 003:113.708 JLINK_WriteReg(R4, 0x00000000)
TA260 003:113.712 - 0.003ms returns 0
TA260 003:113.716 JLINK_WriteReg(R5, 0x00000000)
TA260 003:113.719 - 0.003ms returns 0
TA260 003:113.724 JLINK_WriteReg(R6, 0x00000000)
TA260 003:113.727 - 0.003ms returns 0
TA260 003:113.731 JLINK_WriteReg(R7, 0x00000000)
TA260 003:113.734 - 0.003ms returns 0
TA260 003:113.739 JLINK_WriteReg(R8, 0x00000000)
TA260 003:113.742 - 0.003ms returns 0
TA260 003:113.746 JLINK_WriteReg(R9, 0x20000180)
TA260 003:113.749 - 0.003ms returns 0
TA260 003:113.754 JLINK_WriteReg(R10, 0x00000000)
TA260 003:113.758 - 0.003ms returns 0
TA260 003:113.762 JLINK_WriteReg(R11, 0x00000000)
TA260 003:113.765 - 0.003ms returns 0
TA260 003:113.769 JLINK_WriteReg(R12, 0x00000000)
TA260 003:113.773 - 0.003ms returns 0
TA260 003:113.777 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:113.781 - 0.004ms returns 0
TA260 003:113.788 JLINK_WriteReg(R14, 0x20000001)
TA260 003:113.792 - 0.003ms returns 0
TA260 003:113.796 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:113.799 - 0.003ms returns 0
TA260 003:113.803 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:113.807 - 0.003ms returns 0
TA260 003:113.811 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:113.824 - 0.013ms returns 0
TA260 003:113.829 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:113.833 - 0.003ms returns 0
TA260 003:113.837 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:113.840 - 0.003ms returns 0
TA260 003:113.845 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:113.849 - 0.004ms returns 0x00000041
TA260 003:113.853 JLINK_Go()
TA260 003:113.862   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:116.679 - 2.825ms 
TA260 003:116.722 JLINK_IsHalted()
TA260 003:117.222 - 0.500ms returns FALSE
TA260 003:117.244 JLINK_HasError()
TA260 003:118.963 JLINK_IsHalted()
TA260 003:119.444 - 0.481ms returns FALSE
TA260 003:119.451 JLINK_HasError()
TA260 003:120.964 JLINK_IsHalted()
TA260 003:123.321   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:123.796 - 2.831ms returns TRUE
TA260 003:123.803 JLINK_ReadReg(R15 (PC))
TA260 003:123.808 - 0.005ms returns 0x20000000
TA260 003:123.813 JLINK_ClrBPEx(BPHandle = 0x00000041)
TA260 003:123.817 - 0.003ms returns 0x00
TA260 003:123.821 JLINK_ReadReg(R0)
TA260 003:123.824 - 0.003ms returns 0x00000000
TA260 003:124.176 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:124.183   Data:  E7 92 35 3F 8F 76 34 BF B8 D9 35 3F 34 2F 34 BF ...
TA260 003:124.194   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:126.750 - 2.573ms returns 0x27C
TA260 003:126.770 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:126.774   Data:  84 30 1D BF 87 4C 4A 3F 1F E1 1C BF 13 8A 4A 3F ...
TA260 003:126.786   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:128.664 - 1.894ms returns 0x184
TA260 003:128.680 JLINK_HasError()
TA260 003:128.686 JLINK_WriteReg(R0, 0x0800D800)
TA260 003:128.692 - 0.006ms returns 0
TA260 003:128.696 JLINK_WriteReg(R1, 0x00000400)
TA260 003:128.699 - 0.003ms returns 0
TA260 003:128.704 JLINK_WriteReg(R2, 0x20000184)
TA260 003:128.707 - 0.003ms returns 0
TA260 003:128.711 JLINK_WriteReg(R3, 0x00000000)
TA260 003:128.714 - 0.003ms returns 0
TA260 003:128.718 JLINK_WriteReg(R4, 0x00000000)
TA260 003:128.722 - 0.003ms returns 0
TA260 003:128.726 JLINK_WriteReg(R5, 0x00000000)
TA260 003:128.730 - 0.003ms returns 0
TA260 003:128.734 JLINK_WriteReg(R6, 0x00000000)
TA260 003:128.737 - 0.003ms returns 0
TA260 003:128.741 JLINK_WriteReg(R7, 0x00000000)
TA260 003:128.744 - 0.003ms returns 0
TA260 003:128.749 JLINK_WriteReg(R8, 0x00000000)
TA260 003:128.752 - 0.003ms returns 0
TA260 003:128.756 JLINK_WriteReg(R9, 0x20000180)
TA260 003:128.759 - 0.003ms returns 0
TA260 003:128.763 JLINK_WriteReg(R10, 0x00000000)
TA260 003:128.767 - 0.003ms returns 0
TA260 003:128.771 JLINK_WriteReg(R11, 0x00000000)
TA260 003:128.774 - 0.003ms returns 0
TA260 003:128.778 JLINK_WriteReg(R12, 0x00000000)
TA260 003:128.782 - 0.003ms returns 0
TA260 003:128.786 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:128.790 - 0.003ms returns 0
TA260 003:128.794 JLINK_WriteReg(R14, 0x20000001)
TA260 003:128.797 - 0.003ms returns 0
TA260 003:128.801 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:128.805 - 0.003ms returns 0
TA260 003:128.809 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:128.812 - 0.003ms returns 0
TA260 003:128.816 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:128.820 - 0.003ms returns 0
TA260 003:128.824 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:128.827 - 0.003ms returns 0
TA260 003:128.831 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:128.834 - 0.003ms returns 0
TA260 003:128.839 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:128.843 - 0.004ms returns 0x00000042
TA260 003:128.848 JLINK_Go()
TA260 003:128.856   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:131.581 - 2.733ms 
TA260 003:131.595 JLINK_IsHalted()
TA260 003:132.080 - 0.484ms returns FALSE
TA260 003:132.086 JLINK_HasError()
TA260 003:133.468 JLINK_IsHalted()
TA260 003:133.966 - 0.497ms returns FALSE
TA260 003:133.971 JLINK_HasError()
TA260 003:135.988 JLINK_IsHalted()
TA260 003:138.283   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:138.800 - 2.811ms returns TRUE
TA260 003:138.807 JLINK_ReadReg(R15 (PC))
TA260 003:138.812 - 0.005ms returns 0x20000000
TA260 003:138.817 JLINK_ClrBPEx(BPHandle = 0x00000042)
TA260 003:138.820 - 0.003ms returns 0x00
TA260 003:138.825 JLINK_ReadReg(R0)
TA260 003:138.828 - 0.003ms returns 0x00000000
TA260 003:139.236 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:139.244   Data:  A4 4A 55 3F 81 92 0D BF 2C 82 55 3F B3 3E 0D BF ...
TA260 003:139.255   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:141.925 - 2.688ms returns 0x27C
TA260 003:141.936 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:141.940   Data:  CB 7F E5 BE 79 04 65 3F FE CB E4 BE 54 31 65 3F ...
TA260 003:141.949   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:143.834 - 1.897ms returns 0x184
TA260 003:143.848 JLINK_HasError()
TA260 003:143.878 JLINK_WriteReg(R0, 0x0800DC00)
TA260 003:143.884 - 0.006ms returns 0
TA260 003:143.888 JLINK_WriteReg(R1, 0x00000400)
TA260 003:143.892 - 0.003ms returns 0
TA260 003:143.896 JLINK_WriteReg(R2, 0x20000184)
TA260 003:143.900 - 0.003ms returns 0
TA260 003:143.904 JLINK_WriteReg(R3, 0x00000000)
TA260 003:143.907 - 0.003ms returns 0
TA260 003:143.912 JLINK_WriteReg(R4, 0x00000000)
TA260 003:143.915 - 0.003ms returns 0
TA260 003:143.919 JLINK_WriteReg(R5, 0x00000000)
TA260 003:143.922 - 0.003ms returns 0
TA260 003:143.926 JLINK_WriteReg(R6, 0x00000000)
TA260 003:143.930 - 0.003ms returns 0
TA260 003:143.934 JLINK_WriteReg(R7, 0x00000000)
TA260 003:143.937 - 0.003ms returns 0
TA260 003:143.941 JLINK_WriteReg(R8, 0x00000000)
TA260 003:143.944 - 0.003ms returns 0
TA260 003:143.948 JLINK_WriteReg(R9, 0x20000180)
TA260 003:143.952 - 0.003ms returns 0
TA260 003:143.956 JLINK_WriteReg(R10, 0x00000000)
TA260 003:143.959 - 0.003ms returns 0
TA260 003:143.963 JLINK_WriteReg(R11, 0x00000000)
TA260 003:143.967 - 0.003ms returns 0
TA260 003:143.971 JLINK_WriteReg(R12, 0x00000000)
TA260 003:143.974 - 0.003ms returns 0
TA260 003:143.978 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:143.982 - 0.003ms returns 0
TA260 003:143.986 JLINK_WriteReg(R14, 0x20000001)
TA260 003:143.990 - 0.003ms returns 0
TA260 003:143.994 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:144.024 - 0.003ms returns 0
TA260 003:144.029 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:144.032 - 0.003ms returns 0
TA260 003:144.037 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:144.040 - 0.003ms returns 0
TA260 003:144.044 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:144.047 - 0.003ms returns 0
TA260 003:144.052 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:144.055 - 0.003ms returns 0
TA260 003:144.060 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:144.064 - 0.004ms returns 0x00000043
TA260 003:144.068 JLINK_Go()
TA260 003:144.076   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:146.897 - 2.828ms 
TA260 003:146.916 JLINK_IsHalted()
TA260 003:147.427 - 0.510ms returns FALSE
TA260 003:147.444 JLINK_HasError()
TA260 003:148.489 JLINK_IsHalted()
TA260 003:148.971 - 0.481ms returns FALSE
TA260 003:148.978 JLINK_HasError()
TA260 003:150.493 JLINK_IsHalted()
TA260 003:150.977 - 0.483ms returns FALSE
TA260 003:150.983 JLINK_HasError()
TA260 003:152.993 JLINK_IsHalted()
TA260 003:155.286   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:155.731 - 2.737ms returns TRUE
TA260 003:155.742 JLINK_ReadReg(R15 (PC))
TA260 003:155.748 - 0.006ms returns 0x20000000
TA260 003:155.753 JLINK_ClrBPEx(BPHandle = 0x00000043)
TA260 003:155.757 - 0.004ms returns 0x00
TA260 003:155.761 JLINK_ReadReg(R0)
TA260 003:155.765 - 0.003ms returns 0x00000000
TA260 003:156.138 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:156.146   Data:  07 D0 6C 3F 55 7B C2 BE 24 F6 6C 3F 48 C1 C1 BE ...
TA260 003:156.158   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:158.760 - 2.621ms returns 0x27C
TA260 003:158.776 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:158.783   Data:  C1 CC 87 BE 5B EF 76 3F DA 0A 87 BE CC 09 77 3F ...
TA260 003:158.792   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:160.696 - 1.920ms returns 0x184
TA260 003:160.710 JLINK_HasError()
TA260 003:160.716 JLINK_WriteReg(R0, 0x0800E000)
TA260 003:160.721 - 0.005ms returns 0
TA260 003:160.725 JLINK_WriteReg(R1, 0x00000400)
TA260 003:160.728 - 0.003ms returns 0
TA260 003:160.732 JLINK_WriteReg(R2, 0x20000184)
TA260 003:160.736 - 0.003ms returns 0
TA260 003:160.740 JLINK_WriteReg(R3, 0x00000000)
TA260 003:160.743 - 0.003ms returns 0
TA260 003:160.747 JLINK_WriteReg(R4, 0x00000000)
TA260 003:160.751 - 0.003ms returns 0
TA260 003:160.755 JLINK_WriteReg(R5, 0x00000000)
TA260 003:160.758 - 0.003ms returns 0
TA260 003:160.762 JLINK_WriteReg(R6, 0x00000000)
TA260 003:160.766 - 0.003ms returns 0
TA260 003:160.770 JLINK_WriteReg(R7, 0x00000000)
TA260 003:160.773 - 0.003ms returns 0
TA260 003:160.778 JLINK_WriteReg(R8, 0x00000000)
TA260 003:160.781 - 0.003ms returns 0
TA260 003:160.785 JLINK_WriteReg(R9, 0x20000180)
TA260 003:160.788 - 0.003ms returns 0
TA260 003:160.792 JLINK_WriteReg(R10, 0x00000000)
TA260 003:160.796 - 0.003ms returns 0
TA260 003:160.800 JLINK_WriteReg(R11, 0x00000000)
TA260 003:160.803 - 0.003ms returns 0
TA260 003:160.807 JLINK_WriteReg(R12, 0x00000000)
TA260 003:160.811 - 0.003ms returns 0
TA260 003:160.815 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:160.819 - 0.003ms returns 0
TA260 003:160.823 JLINK_WriteReg(R14, 0x20000001)
TA260 003:160.826 - 0.003ms returns 0
TA260 003:160.830 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:160.834 - 0.003ms returns 0
TA260 003:160.838 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:160.841 - 0.003ms returns 0
TA260 003:160.845 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:160.849 - 0.003ms returns 0
TA260 003:160.853 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:160.856 - 0.003ms returns 0
TA260 003:160.860 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:160.863 - 0.003ms returns 0
TA260 003:160.868 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:160.872 - 0.004ms returns 0x00000044
TA260 003:160.876 JLINK_Go()
TA260 003:160.884   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:163.649 - 2.772ms 
TA260 003:163.657 JLINK_IsHalted()
TA260 003:164.168 - 0.511ms returns FALSE
TA260 003:164.174 JLINK_HasError()
TA260 003:166.011 JLINK_IsHalted()
TA260 003:166.472 - 0.460ms returns FALSE
TA260 003:166.486 JLINK_HasError()
TA260 003:168.012 JLINK_IsHalted()
TA260 003:170.331   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:170.786 - 2.773ms returns TRUE
TA260 003:170.798 JLINK_ReadReg(R15 (PC))
TA260 003:170.804 - 0.006ms returns 0x20000000
TA260 003:170.836 JLINK_ClrBPEx(BPHandle = 0x00000044)
TA260 003:170.843 - 0.006ms returns 0x00
TA260 003:170.849 JLINK_ReadReg(R0)
TA260 003:170.854 - 0.004ms returns 0x00000000
TA260 003:171.210 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:171.217   Data:  AB 3B 7B 3F B9 B0 44 BE E7 4E 7B 3F 07 26 43 BE ...
TA260 003:171.228   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:173.842 - 2.632ms returns 0x27C
TA260 003:173.849 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:173.853   Data:  E7 86 93 BD E9 5C 7F 3F B4 64 90 BD EC 63 7F 3F ...
TA260 003:173.860   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:175.693 - 1.843ms returns 0x184
TA260 003:175.712 JLINK_HasError()
TA260 003:175.718 JLINK_WriteReg(R0, 0x0800E400)
TA260 003:175.724 - 0.006ms returns 0
TA260 003:175.729 JLINK_WriteReg(R1, 0x00000400)
TA260 003:175.732 - 0.003ms returns 0
TA260 003:175.736 JLINK_WriteReg(R2, 0x20000184)
TA260 003:175.740 - 0.003ms returns 0
TA260 003:175.744 JLINK_WriteReg(R3, 0x00000000)
TA260 003:175.747 - 0.003ms returns 0
TA260 003:175.751 JLINK_WriteReg(R4, 0x00000000)
TA260 003:175.754 - 0.003ms returns 0
TA260 003:175.758 JLINK_WriteReg(R5, 0x00000000)
TA260 003:175.762 - 0.003ms returns 0
TA260 003:175.766 JLINK_WriteReg(R6, 0x00000000)
TA260 003:175.770 - 0.003ms returns 0
TA260 003:175.774 JLINK_WriteReg(R7, 0x00000000)
TA260 003:175.780 - 0.007ms returns 0
TA260 003:175.785 JLINK_WriteReg(R8, 0x00000000)
TA260 003:175.790 - 0.004ms returns 0
TA260 003:175.795 JLINK_WriteReg(R9, 0x20000180)
TA260 003:175.798 - 0.003ms returns 0
TA260 003:175.802 JLINK_WriteReg(R10, 0x00000000)
TA260 003:175.806 - 0.003ms returns 0
TA260 003:175.810 JLINK_WriteReg(R11, 0x00000000)
TA260 003:175.813 - 0.003ms returns 0
TA260 003:175.817 JLINK_WriteReg(R12, 0x00000000)
TA260 003:175.821 - 0.003ms returns 0
TA260 003:175.826 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:175.830 - 0.004ms returns 0
TA260 003:175.834 JLINK_WriteReg(R14, 0x20000001)
TA260 003:175.837 - 0.003ms returns 0
TA260 003:175.842 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:175.845 - 0.003ms returns 0
TA260 003:175.849 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:175.852 - 0.003ms returns 0
TA260 003:175.856 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:175.860 - 0.003ms returns 0
TA260 003:175.864 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:175.868 - 0.003ms returns 0
TA260 003:175.872 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:175.875 - 0.003ms returns 0
TA260 003:175.880 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:175.885 - 0.005ms returns 0x00000045
TA260 003:175.889 JLINK_Go()
TA260 003:175.899   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:178.658 - 2.769ms 
TA260 003:178.673 JLINK_IsHalted()
TA260 003:179.125 - 0.452ms returns FALSE
TA260 003:179.131 JLINK_HasError()
TA260 003:181.519 JLINK_IsHalted()
TA260 003:182.034 - 0.514ms returns FALSE
TA260 003:182.040 JLINK_HasError()
TA260 003:183.516 JLINK_IsHalted()
TA260 003:185.823   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:186.332 - 2.814ms returns TRUE
TA260 003:186.348 JLINK_ReadReg(R15 (PC))
TA260 003:186.353 - 0.005ms returns 0x20000000
TA260 003:186.358 JLINK_ClrBPEx(BPHandle = 0x00000045)
TA260 003:186.362 - 0.004ms returns 0x00
TA260 003:186.366 JLINK_ReadReg(R0)
TA260 003:186.370 - 0.003ms returns 0x00000000
TA260 003:186.869 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:186.881   Data:  C0 E8 00 08 B0 00 00 20 68 17 00 00 20 4D 00 08 ...
TA260 003:186.892   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:189.471 - 2.601ms returns 0x27C
TA260 003:189.484 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:189.488   Data:  FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF ...
TA260 003:189.497   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:191.380 - 1.896ms returns 0x184
TA260 003:191.391 JLINK_HasError()
TA260 003:191.397 JLINK_WriteReg(R0, 0x0800E800)
TA260 003:191.402 - 0.005ms returns 0
TA260 003:191.406 JLINK_WriteReg(R1, 0x000000C0)
TA260 003:191.410 - 0.003ms returns 0
TA260 003:191.414 JLINK_WriteReg(R2, 0x20000184)
TA260 003:191.417 - 0.003ms returns 0
TA260 003:191.421 JLINK_WriteReg(R3, 0x00000000)
TA260 003:191.425 - 0.003ms returns 0
TA260 003:191.429 JLINK_WriteReg(R4, 0x00000000)
TA260 003:191.432 - 0.003ms returns 0
TA260 003:191.436 JLINK_WriteReg(R5, 0x00000000)
TA260 003:191.440 - 0.003ms returns 0
TA260 003:191.444 JLINK_WriteReg(R6, 0x00000000)
TA260 003:191.447 - 0.003ms returns 0
TA260 003:191.452 JLINK_WriteReg(R7, 0x00000000)
TA260 003:191.455 - 0.003ms returns 0
TA260 003:191.460 JLINK_WriteReg(R8, 0x00000000)
TA260 003:191.463 - 0.003ms returns 0
TA260 003:191.467 JLINK_WriteReg(R9, 0x20000180)
TA260 003:191.470 - 0.003ms returns 0
TA260 003:191.474 JLINK_WriteReg(R10, 0x00000000)
TA260 003:191.478 - 0.003ms returns 0
TA260 003:191.482 JLINK_WriteReg(R11, 0x00000000)
TA260 003:191.485 - 0.003ms returns 0
TA260 003:191.489 JLINK_WriteReg(R12, 0x00000000)
TA260 003:191.492 - 0.003ms returns 0
TA260 003:191.497 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:191.500 - 0.003ms returns 0
TA260 003:191.504 JLINK_WriteReg(R14, 0x20000001)
TA260 003:191.508 - 0.003ms returns 0
TA260 003:191.512 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:191.515 - 0.003ms returns 0
TA260 003:191.519 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:191.523 - 0.003ms returns 0
TA260 003:191.530 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:191.533 - 0.003ms returns 0
TA260 003:191.537 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:191.540 - 0.003ms returns 0
TA260 003:191.544 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:191.548 - 0.003ms returns 0
TA260 003:191.552 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:191.558 - 0.006ms returns 0x00000046
TA260 003:191.563 JLINK_Go()
TA260 003:191.571   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:194.328 - 2.765ms 
TA260 003:194.334 JLINK_IsHalted()
TA260 003:194.801 - 0.466ms returns FALSE
TA260 003:194.809 JLINK_HasError()
TA260 003:196.031 JLINK_IsHalted()
TA260 003:198.331   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:198.844 - 2.812ms returns TRUE
TA260 003:198.850 JLINK_ReadReg(R15 (PC))
TA260 003:198.856 - 0.005ms returns 0x20000000
TA260 003:198.861 JLINK_ClrBPEx(BPHandle = 0x00000046)
TA260 003:198.864 - 0.003ms returns 0x00
TA260 003:198.869 JLINK_ReadReg(R0)
TA260 003:198.872 - 0.003ms returns 0x00000000
TA260 003:198.877 JLINK_HasError()
TA260 003:198.882 JLINK_WriteReg(R0, 0x00000002)
TA260 003:198.886 - 0.003ms returns 0
TA260 003:198.890 JLINK_WriteReg(R1, 0x000000C0)
TA260 003:198.894 - 0.003ms returns 0
TA260 003:198.898 JLINK_WriteReg(R2, 0x20000184)
TA260 003:198.901 - 0.003ms returns 0
TA260 003:198.906 JLINK_WriteReg(R3, 0x00000000)
TA260 003:198.909 - 0.003ms returns 0
TA260 003:198.913 JLINK_WriteReg(R4, 0x00000000)
TA260 003:198.916 - 0.003ms returns 0
TA260 003:198.920 JLINK_WriteReg(R5, 0x00000000)
TA260 003:198.924 - 0.003ms returns 0
TA260 003:198.928 JLINK_WriteReg(R6, 0x00000000)
TA260 003:198.931 - 0.003ms returns 0
TA260 003:198.935 JLINK_WriteReg(R7, 0x00000000)
TA260 003:198.938 - 0.003ms returns 0
TA260 003:198.942 JLINK_WriteReg(R8, 0x00000000)
TA260 003:198.946 - 0.003ms returns 0
TA260 003:198.950 JLINK_WriteReg(R9, 0x20000180)
TA260 003:198.953 - 0.003ms returns 0
TA260 003:198.957 JLINK_WriteReg(R10, 0x00000000)
TA260 003:198.961 - 0.003ms returns 0
TA260 003:198.965 JLINK_WriteReg(R11, 0x00000000)
TA260 003:198.968 - 0.003ms returns 0
TA260 003:198.972 JLINK_WriteReg(R12, 0x00000000)
TA260 003:198.976 - 0.003ms returns 0
TA260 003:198.980 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:198.983 - 0.003ms returns 0
TA260 003:198.987 JLINK_WriteReg(R14, 0x20000001)
TA260 003:198.990 - 0.003ms returns 0
TA260 003:198.994 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 003:198.998 - 0.003ms returns 0
TA260 003:199.002 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:199.005 - 0.003ms returns 0
TA260 003:199.009 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:199.013 - 0.003ms returns 0
TA260 003:199.017 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:199.020 - 0.003ms returns 0
TA260 003:199.024 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:199.028 - 0.003ms returns 0
TA260 003:199.032 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:199.036 - 0.004ms returns 0x00000047
TA260 003:199.040 JLINK_Go()
TA260 003:199.047   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:201.777 - 2.736ms 
TA260 003:201.785 JLINK_IsHalted()
TA260 003:204.169   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:204.702 - 2.916ms returns TRUE
TA260 003:204.719 JLINK_ReadReg(R15 (PC))
TA260 003:204.724 - 0.006ms returns 0x20000000
TA260 003:204.728 JLINK_ClrBPEx(BPHandle = 0x00000047)
TA260 003:204.733 - 0.004ms returns 0x00
TA260 003:204.737 JLINK_ReadReg(R0)
TA260 003:204.741 - 0.004ms returns 0x00000000
TA260 003:260.979 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 003:260.992   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 003:261.008   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 003:262.880 - 1.900ms returns 0x184
TA260 003:262.900 JLINK_HasError()
TA260 003:262.906 JLINK_WriteReg(R0, 0x08000000)
TA260 003:262.911 - 0.005ms returns 0
TA260 003:262.915 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 003:262.918 - 0.003ms returns 0
TA260 003:262.923 JLINK_WriteReg(R2, 0x00000003)
TA260 003:262.926 - 0.003ms returns 0
TA260 003:262.930 JLINK_WriteReg(R3, 0x00000000)
TA260 003:262.936 - 0.005ms returns 0
TA260 003:262.940 JLINK_WriteReg(R4, 0x00000000)
TA260 003:262.944 - 0.003ms returns 0
TA260 003:262.948 JLINK_WriteReg(R5, 0x00000000)
TA260 003:262.952 - 0.003ms returns 0
TA260 003:262.956 JLINK_WriteReg(R6, 0x00000000)
TA260 003:262.959 - 0.003ms returns 0
TA260 003:262.963 JLINK_WriteReg(R7, 0x00000000)
TA260 003:262.966 - 0.003ms returns 0
TA260 003:262.970 JLINK_WriteReg(R8, 0x00000000)
TA260 003:262.974 - 0.003ms returns 0
TA260 003:262.978 JLINK_WriteReg(R9, 0x20000180)
TA260 003:262.981 - 0.003ms returns 0
TA260 003:262.985 JLINK_WriteReg(R10, 0x00000000)
TA260 003:262.989 - 0.003ms returns 0
TA260 003:262.993 JLINK_WriteReg(R11, 0x00000000)
TA260 003:262.996 - 0.003ms returns 0
TA260 003:263.000 JLINK_WriteReg(R12, 0x00000000)
TA260 003:263.003 - 0.003ms returns 0
TA260 003:263.007 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:263.011 - 0.003ms returns 0
TA260 003:263.015 JLINK_WriteReg(R14, 0x20000001)
TA260 003:263.019 - 0.003ms returns 0
TA260 003:263.023 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 003:263.026 - 0.003ms returns 0
TA260 003:263.030 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:263.034 - 0.003ms returns 0
TA260 003:263.038 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:263.041 - 0.003ms returns 0
TA260 003:263.045 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:263.049 - 0.003ms returns 0
TA260 003:263.053 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:263.056 - 0.003ms returns 0
TA260 003:263.061 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:263.067   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:263.535 - 0.474ms returns 0x00000048
TA260 003:263.540 JLINK_Go()
TA260 003:263.545   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 003:264.027   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:266.779 - 3.237ms 
TA260 003:266.801 JLINK_IsHalted()
TA260 003:269.182   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:269.693 - 2.891ms returns TRUE
TA260 003:269.700 JLINK_ReadReg(R15 (PC))
TA260 003:269.706 - 0.005ms returns 0x20000000
TA260 003:269.711 JLINK_ClrBPEx(BPHandle = 0x00000048)
TA260 003:269.715 - 0.004ms returns 0x00
TA260 003:269.720 JLINK_ReadReg(R0)
TA260 003:269.723 - 0.003ms returns 0x00000000
TA260 003:269.728 JLINK_HasError()
TA260 003:269.733 JLINK_WriteReg(R0, 0xFFFFFFFF)
TA260 003:269.736 - 0.004ms returns 0
TA260 003:269.741 JLINK_WriteReg(R1, 0x08000000)
TA260 003:269.744 - 0.003ms returns 0
TA260 003:269.748 JLINK_WriteReg(R2, 0x0000E8C0)
TA260 003:269.752 - 0.003ms returns 0
TA260 003:269.756 JLINK_WriteReg(R3, 0x04C11DB7)
TA260 003:269.759 - 0.003ms returns 0
TA260 003:269.763 JLINK_WriteReg(R4, 0x00000000)
TA260 003:269.766 - 0.003ms returns 0
TA260 003:269.770 JLINK_WriteReg(R5, 0x00000000)
TA260 003:269.774 - 0.003ms returns 0
TA260 003:269.778 JLINK_WriteReg(R6, 0x00000000)
TA260 003:269.781 - 0.003ms returns 0
TA260 003:269.785 JLINK_WriteReg(R7, 0x00000000)
TA260 003:269.789 - 0.003ms returns 0
TA260 003:269.793 JLINK_WriteReg(R8, 0x00000000)
TA260 003:269.796 - 0.003ms returns 0
TA260 003:269.800 JLINK_WriteReg(R9, 0x20000180)
TA260 003:269.803 - 0.003ms returns 0
TA260 003:269.808 JLINK_WriteReg(R10, 0x00000000)
TA260 003:269.811 - 0.003ms returns 0
TA260 003:269.815 JLINK_WriteReg(R11, 0x00000000)
TA260 003:269.819 - 0.003ms returns 0
TA260 003:269.823 JLINK_WriteReg(R12, 0x00000000)
TA260 003:269.826 - 0.003ms returns 0
TA260 003:269.830 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:269.840 - 0.009ms returns 0
TA260 003:269.844 JLINK_WriteReg(R14, 0x20000001)
TA260 003:269.848 - 0.003ms returns 0
TA260 003:269.852 JLINK_WriteReg(R15 (PC), 0x20000002)
TA260 003:269.855 - 0.003ms returns 0
TA260 003:269.859 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:269.863 - 0.003ms returns 0
TA260 003:269.867 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:269.870 - 0.003ms returns 0
TA260 003:269.874 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:269.877 - 0.003ms returns 0
TA260 003:269.881 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:269.885 - 0.003ms returns 0
TA260 003:269.889 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:269.896 - 0.006ms returns 0x00000049
TA260 003:269.900 JLINK_Go()
TA260 003:269.907   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:272.671 - 2.770ms 
TA260 003:272.684 JLINK_IsHalted()
TA260 003:273.216 - 0.531ms returns FALSE
TA260 003:273.224 JLINK_HasError()
TA260 003:276.069 JLINK_IsHalted()
TA260 003:276.528 - 0.459ms returns FALSE
TA260 003:276.542 JLINK_HasError()
TA260 003:278.063 JLINK_IsHalted()
TA260 003:278.526 - 0.462ms returns FALSE
TA260 003:278.532 JLINK_HasError()
TA260 003:280.060 JLINK_IsHalted()
TA260 003:280.546 - 0.486ms returns FALSE
TA260 003:280.552 JLINK_HasError()
TA260 003:282.060 JLINK_IsHalted()
TA260 003:282.556 - 0.496ms returns FALSE
TA260 003:282.562 JLINK_HasError()
TA260 003:284.059 JLINK_IsHalted()
TA260 003:284.676 - 0.616ms returns FALSE
TA260 003:284.690 JLINK_HasError()
TA260 003:287.572 JLINK_IsHalted()
TA260 003:288.046 - 0.473ms returns FALSE
TA260 003:288.053 JLINK_HasError()
TA260 003:289.572 JLINK_IsHalted()
TA260 003:290.008 - 0.435ms returns FALSE
TA260 003:290.015 JLINK_HasError()
TA260 003:291.569 JLINK_IsHalted()
TA260 003:292.042 - 0.473ms returns FALSE
TA260 003:292.049 JLINK_HasError()
TA260 003:293.567 JLINK_IsHalted()
TA260 003:294.067 - 0.500ms returns FALSE
TA260 003:294.073 JLINK_HasError()
TA260 003:296.077 JLINK_IsHalted()
TA260 003:296.557 - 0.479ms returns FALSE
TA260 003:296.564 JLINK_HasError()
TA260 003:298.080 JLINK_IsHalted()
TA260 003:298.657 - 0.577ms returns FALSE
TA260 003:298.668 JLINK_HasError()
TA260 003:300.077 JLINK_IsHalted()
TA260 003:300.580 - 0.503ms returns FALSE
TA260 003:300.586 JLINK_HasError()
TA260 003:302.077 JLINK_IsHalted()
TA260 003:302.557 - 0.480ms returns FALSE
TA260 003:302.563 JLINK_HasError()
TA260 003:304.075 JLINK_IsHalted()
TA260 003:304.546 - 0.470ms returns FALSE
TA260 003:304.657 JLINK_HasError()
TA260 003:306.092 JLINK_IsHalted()
TA260 003:306.586 - 0.493ms returns FALSE
TA260 003:306.599 JLINK_HasError()
TA260 003:308.589 JLINK_IsHalted()
TA260 003:309.079 - 0.489ms returns FALSE
TA260 003:309.085 JLINK_HasError()
TA260 003:310.589 JLINK_IsHalted()
TA260 003:311.068 - 0.478ms returns FALSE
TA260 003:311.074 JLINK_HasError()
TA260 003:312.589 JLINK_IsHalted()
TA260 003:313.093 - 0.503ms returns FALSE
TA260 003:313.098 JLINK_HasError()
TA260 003:314.670 JLINK_IsHalted()
TA260 003:315.334 - 0.664ms returns FALSE
TA260 003:315.350 JLINK_HasError()
TA260 003:317.096 JLINK_IsHalted()
TA260 003:317.663 - 0.566ms returns FALSE
TA260 003:317.677 JLINK_HasError()
TA260 003:319.097 JLINK_IsHalted()
TA260 003:319.650 - 0.552ms returns FALSE
TA260 003:319.656 JLINK_HasError()
TA260 003:321.095 JLINK_IsHalted()
TA260 003:321.656 - 0.561ms returns FALSE
TA260 003:321.662 JLINK_HasError()
TA260 003:323.096 JLINK_IsHalted()
TA260 003:323.580 - 0.484ms returns FALSE
TA260 003:323.590 JLINK_HasError()
TA260 003:325.106 JLINK_IsHalted()
TA260 003:325.650 - 0.543ms returns FALSE
TA260 003:325.657 JLINK_HasError()
TA260 003:327.606 JLINK_IsHalted()
TA260 003:328.094 - 0.487ms returns FALSE
TA260 003:328.100 JLINK_HasError()
TA260 003:329.603 JLINK_IsHalted()
TA260 003:330.080 - 0.477ms returns FALSE
TA260 003:330.086 JLINK_HasError()
TA260 003:331.606 JLINK_IsHalted()
TA260 003:332.036 - 0.430ms returns FALSE
TA260 003:332.042 JLINK_HasError()
TA260 003:333.603 JLINK_IsHalted()
TA260 003:334.077 - 0.474ms returns FALSE
TA260 003:334.082 JLINK_HasError()
TA260 003:336.116 JLINK_IsHalted()
TA260 003:336.662 - 0.544ms returns FALSE
TA260 003:336.676 JLINK_HasError()
TA260 003:338.119 JLINK_IsHalted()
TA260 003:338.584 - 0.464ms returns FALSE
TA260 003:338.590 JLINK_HasError()
TA260 003:340.116 JLINK_IsHalted()
TA260 003:340.656 - 0.540ms returns FALSE
TA260 003:340.662 JLINK_HasError()
TA260 003:342.115 JLINK_IsHalted()
TA260 003:342.659 - 0.543ms returns FALSE
TA260 003:342.664 JLINK_HasError()
TA260 003:344.114 JLINK_IsHalted()
TA260 003:344.651 - 0.536ms returns FALSE
TA260 003:344.660 JLINK_HasError()
TA260 003:346.172 JLINK_IsHalted()
TA260 003:346.664 - 0.491ms returns FALSE
TA260 003:346.680 JLINK_HasError()
TA260 003:348.624 JLINK_IsHalted()
TA260 003:349.132 - 0.507ms returns FALSE
TA260 003:349.179 JLINK_HasError()
TA260 003:350.621 JLINK_IsHalted()
TA260 003:351.136 - 0.514ms returns FALSE
TA260 003:351.152 JLINK_HasError()
TA260 003:353.128 JLINK_IsHalted()
TA260 003:353.548 - 0.419ms returns FALSE
TA260 003:353.554 JLINK_HasError()
TA260 003:355.129 JLINK_IsHalted()
TA260 003:355.582 - 0.453ms returns FALSE
TA260 003:355.599 JLINK_HasError()
TA260 003:356.636 JLINK_IsHalted()
TA260 003:357.153 - 0.517ms returns FALSE
TA260 003:357.165 JLINK_HasError()
TA260 003:358.637 JLINK_IsHalted()
TA260 003:359.127 - 0.490ms returns FALSE
TA260 003:359.133 JLINK_HasError()
TA260 003:360.638 JLINK_IsHalted()
TA260 003:361.138 - 0.499ms returns FALSE
TA260 003:361.144 JLINK_HasError()
TA260 003:362.643 JLINK_IsHalted()
TA260 003:363.126 - 0.483ms returns FALSE
TA260 003:363.132 JLINK_HasError()
TA260 003:364.643 JLINK_IsHalted()
TA260 003:365.212 - 0.568ms returns FALSE
TA260 003:365.226 JLINK_HasError()
TA260 003:367.155 JLINK_IsHalted()
TA260 003:367.652 - 0.497ms returns FALSE
TA260 003:367.661 JLINK_HasError()
TA260 003:369.152 JLINK_IsHalted()
TA260 003:369.658 - 0.505ms returns FALSE
TA260 003:369.664 JLINK_HasError()
TA260 003:371.151 JLINK_IsHalted()
TA260 003:371.647 - 0.495ms returns FALSE
TA260 003:371.652 JLINK_HasError()
TA260 003:373.151 JLINK_IsHalted()
TA260 003:373.648 - 0.497ms returns FALSE
TA260 003:373.653 JLINK_HasError()
TA260 003:375.153 JLINK_IsHalted()
TA260 003:375.652 - 0.498ms returns FALSE
TA260 003:375.666 JLINK_HasError()
TA260 003:377.665 JLINK_IsHalted()
TA260 003:378.096 - 0.430ms returns FALSE
TA260 003:378.111 JLINK_HasError()
TA260 003:380.660 JLINK_IsHalted()
TA260 003:381.130 - 0.468ms returns FALSE
TA260 003:381.142 JLINK_HasError()
TA260 003:382.661 JLINK_IsHalted()
TA260 003:383.170 - 0.508ms returns FALSE
TA260 003:383.176 JLINK_HasError()
TA260 003:384.664 JLINK_IsHalted()
TA260 003:385.176 - 0.511ms returns FALSE
TA260 003:385.189 JLINK_HasError()
TA260 003:387.177 JLINK_IsHalted()
TA260 003:387.660 - 0.482ms returns FALSE
TA260 003:387.669 JLINK_HasError()
TA260 003:389.176 JLINK_IsHalted()
TA260 003:389.693 - 0.517ms returns FALSE
TA260 003:389.700 JLINK_HasError()
TA260 003:391.172 JLINK_IsHalted()
TA260 003:391.648 - 0.475ms returns FALSE
TA260 003:391.653 JLINK_HasError()
TA260 003:393.174 JLINK_IsHalted()
TA260 003:393.659 - 0.484ms returns FALSE
TA260 003:393.671 JLINK_HasError()
TA260 003:395.812 JLINK_IsHalted()
TA260 003:396.312 - 0.500ms returns FALSE
TA260 003:396.319 JLINK_HasError()
TA260 003:397.813 JLINK_IsHalted()
TA260 003:398.264 - 0.450ms returns FALSE
TA260 003:398.271 JLINK_HasError()
TA260 003:399.810 JLINK_IsHalted()
TA260 003:400.301 - 0.490ms returns FALSE
TA260 003:400.317 JLINK_HasError()
TA260 003:401.810 JLINK_IsHalted()
TA260 003:402.320 - 0.510ms returns FALSE
TA260 003:402.326 JLINK_HasError()
TA260 003:403.807 JLINK_IsHalted()
TA260 003:404.318 - 0.510ms returns FALSE
TA260 003:404.323 JLINK_HasError()
TA260 003:405.814 JLINK_IsHalted()
TA260 003:406.245 - 0.430ms returns FALSE
TA260 003:406.259 JLINK_HasError()
TA260 003:407.330 JLINK_IsHalted()
TA260 003:407.801 - 0.470ms returns FALSE
TA260 003:407.811 JLINK_HasError()
TA260 003:411.322 JLINK_IsHalted()
TA260 003:411.784 - 0.462ms returns FALSE
TA260 003:411.790 JLINK_HasError()
TA260 003:413.315 JLINK_IsHalted()
TA260 003:413.784 - 0.468ms returns FALSE
TA260 003:413.790 JLINK_HasError()
TA260 003:415.829 JLINK_IsHalted()
TA260 003:416.280 - 0.450ms returns FALSE
TA260 003:416.293 JLINK_HasError()
TA260 003:417.840 JLINK_IsHalted()
TA260 003:418.370 - 0.529ms returns FALSE
TA260 003:418.383 JLINK_HasError()
TA260 003:419.827 JLINK_IsHalted()
TA260 003:420.316 - 0.488ms returns FALSE
TA260 003:420.322 JLINK_HasError()
TA260 003:421.826 JLINK_IsHalted()
TA260 003:422.397 - 0.570ms returns FALSE
TA260 003:422.404 JLINK_HasError()
TA260 003:423.828 JLINK_IsHalted()
TA260 003:424.804 - 0.974ms returns FALSE
TA260 003:424.812 JLINK_HasError()
TA260 003:426.328 JLINK_IsHalted()
TA260 003:426.829 - 0.500ms returns FALSE
TA260 003:426.838 JLINK_HasError()
TA260 003:428.338 JLINK_IsHalted()
TA260 003:428.883 - 0.544ms returns FALSE
TA260 003:428.896 JLINK_HasError()
TA260 003:430.332 JLINK_IsHalted()
TA260 003:430.829 - 0.496ms returns FALSE
TA260 003:430.834 JLINK_HasError()
TA260 003:432.333 JLINK_IsHalted()
TA260 003:432.797 - 0.464ms returns FALSE
TA260 003:432.803 JLINK_HasError()
TA260 003:434.332 JLINK_IsHalted()
TA260 003:434.832 - 0.498ms returns FALSE
TA260 003:434.841 JLINK_HasError()
TA260 003:436.841 JLINK_IsHalted()
TA260 003:437.391 - 0.549ms returns FALSE
TA260 003:437.398 JLINK_HasError()
TA260 003:438.854 JLINK_IsHalted()
TA260 003:439.344 - 0.489ms returns FALSE
TA260 003:439.351 JLINK_HasError()
TA260 003:440.843 JLINK_IsHalted()
TA260 003:441.343 - 0.499ms returns FALSE
TA260 003:441.390 JLINK_HasError()
TA260 003:442.845 JLINK_IsHalted()
TA260 003:443.344 - 0.499ms returns FALSE
TA260 003:443.352 JLINK_HasError()
TA260 003:444.844 JLINK_IsHalted()
TA260 003:445.299 - 0.454ms returns FALSE
TA260 003:445.314 JLINK_HasError()
TA260 003:447.353 JLINK_IsHalted()
TA260 003:447.848 - 0.493ms returns FALSE
TA260 003:447.860 JLINK_HasError()
TA260 003:449.353 JLINK_IsHalted()
TA260 003:449.833 - 0.478ms returns FALSE
TA260 003:449.850 JLINK_HasError()
TA260 003:451.851 JLINK_IsHalted()
TA260 003:452.328 - 0.476ms returns FALSE
TA260 003:452.335 JLINK_HasError()
TA260 003:453.850 JLINK_IsHalted()
TA260 003:454.306 - 0.455ms returns FALSE
TA260 003:454.312 JLINK_HasError()
TA260 003:455.861 JLINK_IsHalted()
TA260 003:456.304 - 0.441ms returns FALSE
TA260 003:456.316 JLINK_HasError()
TA260 003:457.372 JLINK_IsHalted()
TA260 003:457.820 - 0.447ms returns FALSE
TA260 003:457.827 JLINK_HasError()
TA260 003:459.361 JLINK_IsHalted()
TA260 003:459.786 - 0.424ms returns FALSE
TA260 003:459.791 JLINK_HasError()
TA260 003:461.361 JLINK_IsHalted()
TA260 003:461.827 - 0.465ms returns FALSE
TA260 003:461.833 JLINK_HasError()
TA260 003:463.359 JLINK_IsHalted()
TA260 003:463.830 - 0.470ms returns FALSE
TA260 003:463.836 JLINK_HasError()
TA260 003:465.875 JLINK_IsHalted()
TA260 003:466.380 - 0.504ms returns FALSE
TA260 003:466.395 JLINK_HasError()
TA260 003:467.872 JLINK_IsHalted()
TA260 003:468.390 - 0.517ms returns FALSE
TA260 003:468.400 JLINK_HasError()
TA260 003:469.875 JLINK_IsHalted()
TA260 003:470.390 - 0.514ms returns FALSE
TA260 003:470.403 JLINK_HasError()
TA260 003:471.875 JLINK_IsHalted()
TA260 003:472.382 - 0.507ms returns FALSE
TA260 003:472.388 JLINK_HasError()
TA260 003:473.435 JLINK_IsHalted()
TA260 003:473.932 - 0.496ms returns FALSE
TA260 003:473.937 JLINK_HasError()
TA260 003:474.976 JLINK_IsHalted()
TA260 003:475.415 - 0.439ms returns FALSE
TA260 003:475.434 JLINK_HasError()
TA260 003:476.547 JLINK_IsHalted()
TA260 003:477.032 - 0.485ms returns FALSE
TA260 003:477.040 JLINK_HasError()
TA260 003:478.434 JLINK_IsHalted()
TA260 003:478.947 - 0.512ms returns FALSE
TA260 003:478.954 JLINK_HasError()
TA260 003:480.169 JLINK_IsHalted()
TA260 003:480.648 - 0.478ms returns FALSE
TA260 003:480.654 JLINK_HasError()
TA260 003:481.706 JLINK_IsHalted()
TA260 003:482.169 - 0.462ms returns FALSE
TA260 003:482.174 JLINK_HasError()
TA260 003:483.707 JLINK_IsHalted()
TA260 003:484.169 - 0.462ms returns FALSE
TA260 003:484.175 JLINK_HasError()
TA260 003:486.220 JLINK_IsHalted()
TA260 003:486.688 - 0.468ms returns FALSE
TA260 003:486.709 JLINK_HasError()
TA260 003:488.222 JLINK_IsHalted()
TA260 003:488.826 - 0.603ms returns FALSE
TA260 003:488.836 JLINK_HasError()
TA260 003:490.218 JLINK_IsHalted()
TA260 003:490.693 - 0.474ms returns FALSE
TA260 003:490.699 JLINK_HasError()
TA260 003:492.219 JLINK_IsHalted()
TA260 003:492.694 - 0.475ms returns FALSE
TA260 003:492.700 JLINK_HasError()
TA260 003:494.217 JLINK_IsHalted()
TA260 003:494.730 - 0.512ms returns FALSE
TA260 003:494.744 JLINK_HasError()
TA260 003:496.224 JLINK_IsHalted()
TA260 003:496.788 - 0.564ms returns FALSE
TA260 003:496.797 JLINK_HasError()
TA260 003:498.725 JLINK_IsHalted()
TA260 003:499.217 - 0.491ms returns FALSE
TA260 003:499.223 JLINK_HasError()
TA260 003:500.731 JLINK_IsHalted()
TA260 003:501.217 - 0.485ms returns FALSE
TA260 003:501.224 JLINK_HasError()
TA260 003:502.726 JLINK_IsHalted()
TA260 003:503.207 - 0.480ms returns FALSE
TA260 003:503.212 JLINK_HasError()
TA260 003:506.239 JLINK_IsHalted()
TA260 003:506.743 - 0.503ms returns FALSE
TA260 003:506.752 JLINK_HasError()
TA260 003:508.236 JLINK_IsHalted()
TA260 003:508.810 - 0.574ms returns FALSE
TA260 003:508.823 JLINK_HasError()
TA260 003:510.234 JLINK_IsHalted()
TA260 003:510.693 - 0.459ms returns FALSE
TA260 003:510.699 JLINK_HasError()
TA260 003:512.235 JLINK_IsHalted()
TA260 003:512.784 - 0.549ms returns FALSE
TA260 003:512.790 JLINK_HasError()
TA260 003:514.232 JLINK_IsHalted()
TA260 003:514.734 - 0.500ms returns FALSE
TA260 003:514.740 JLINK_HasError()
TA260 003:516.246 JLINK_IsHalted()
TA260 003:516.752 - 0.506ms returns FALSE
TA260 003:516.759 JLINK_HasError()
TA260 003:518.752 JLINK_IsHalted()
TA260 003:519.222 - 0.469ms returns FALSE
TA260 003:519.229 JLINK_HasError()
TA260 003:520.749 JLINK_IsHalted()
TA260 003:521.240 - 0.490ms returns FALSE
TA260 003:521.246 JLINK_HasError()
TA260 003:522.744 JLINK_IsHalted()
TA260 003:523.239 - 0.494ms returns FALSE
TA260 003:523.244 JLINK_HasError()
TA260 003:524.746 JLINK_IsHalted()
TA260 003:525.210 - 0.463ms returns FALSE
TA260 003:525.220 JLINK_HasError()
TA260 003:527.252 JLINK_IsHalted()
TA260 003:527.682 - 0.429ms returns FALSE
TA260 003:527.692 JLINK_HasError()
TA260 003:529.251 JLINK_IsHalted()
TA260 003:529.742 - 0.489ms returns FALSE
TA260 003:529.748 JLINK_HasError()
TA260 003:531.250 JLINK_IsHalted()
TA260 003:531.692 - 0.442ms returns FALSE
TA260 003:531.698 JLINK_HasError()
TA260 003:533.249 JLINK_IsHalted()
TA260 003:533.683 - 0.433ms returns FALSE
TA260 003:533.692 JLINK_HasError()
TA260 003:535.773 JLINK_IsHalted()
TA260 003:536.284 - 0.510ms returns FALSE
TA260 003:536.298 JLINK_HasError()
TA260 003:537.773 JLINK_IsHalted()
TA260 003:538.229 - 0.455ms returns FALSE
TA260 003:538.236 JLINK_HasError()
TA260 003:539.775 JLINK_IsHalted()
TA260 003:540.267 - 0.492ms returns FALSE
TA260 003:540.274 JLINK_HasError()
TA260 003:541.769 JLINK_IsHalted()
TA260 003:542.267 - 0.497ms returns FALSE
TA260 003:542.278 JLINK_HasError()
TA260 003:543.768 JLINK_IsHalted()
TA260 003:544.230 - 0.461ms returns FALSE
TA260 003:544.280 JLINK_HasError()
TA260 003:545.785 JLINK_IsHalted()
TA260 003:546.284 - 0.499ms returns FALSE
TA260 003:546.299 JLINK_HasError()
TA260 003:548.274 JLINK_IsHalted()
TA260 003:548.758 - 0.483ms returns FALSE
TA260 003:548.770 JLINK_HasError()
TA260 003:550.781 JLINK_IsHalted()
TA260 003:551.264 - 0.482ms returns FALSE
TA260 003:551.271 JLINK_HasError()
TA260 003:552.780 JLINK_IsHalted()
TA260 003:553.273 - 0.493ms returns FALSE
TA260 003:553.283 JLINK_HasError()
TA260 003:554.781 JLINK_IsHalted()
TA260 003:555.236 - 0.454ms returns FALSE
TA260 003:555.250 JLINK_HasError()
TA260 003:557.291 JLINK_IsHalted()
TA260 003:557.800 - 0.509ms returns FALSE
TA260 003:557.807 JLINK_HasError()
TA260 003:559.284 JLINK_IsHalted()
TA260 003:559.781 - 0.497ms returns FALSE
TA260 003:559.787 JLINK_HasError()
TA260 003:561.286 JLINK_IsHalted()
TA260 003:561.782 - 0.495ms returns FALSE
TA260 003:561.787 JLINK_HasError()
TA260 003:563.175 JLINK_IsHalted()
TA260 003:563.656 - 0.480ms returns FALSE
TA260 003:563.663 JLINK_HasError()
TA260 003:565.173 JLINK_IsHalted()
TA260 003:565.655 - 0.482ms returns FALSE
TA260 003:565.668 JLINK_HasError()
TA260 003:567.685 JLINK_IsHalted()
TA260 003:568.146 - 0.460ms returns FALSE
TA260 003:568.153 JLINK_HasError()
TA260 003:569.682 JLINK_IsHalted()
TA260 003:570.180 - 0.497ms returns FALSE
TA260 003:570.223 JLINK_HasError()
TA260 003:571.682 JLINK_IsHalted()
TA260 003:572.157 - 0.475ms returns FALSE
TA260 003:572.164 JLINK_HasError()
TA260 003:573.689 JLINK_IsHalted()
TA260 003:574.195 - 0.505ms returns FALSE
TA260 003:574.201 JLINK_HasError()
TA260 003:576.190 JLINK_IsHalted()
TA260 003:576.696 - 0.506ms returns FALSE
TA260 003:576.705 JLINK_HasError()
TA260 003:578.190 JLINK_IsHalted()
TA260 003:578.658 - 0.467ms returns FALSE
TA260 003:578.668 JLINK_HasError()
TA260 003:580.189 JLINK_IsHalted()
TA260 003:580.692 - 0.503ms returns FALSE
TA260 003:580.698 JLINK_HasError()
TA260 003:582.189 JLINK_IsHalted()
TA260 003:582.693 - 0.504ms returns FALSE
TA260 003:582.699 JLINK_HasError()
TA260 003:584.193 JLINK_IsHalted()
TA260 003:584.694 - 0.501ms returns FALSE
TA260 003:584.701 JLINK_HasError()
TA260 003:586.201 JLINK_IsHalted()
TA260 003:586.700 - 0.499ms returns FALSE
TA260 003:586.716 JLINK_HasError()
TA260 003:588.699 JLINK_IsHalted()
TA260 003:589.206 - 0.507ms returns FALSE
TA260 003:589.213 JLINK_HasError()
TA260 003:590.696 JLINK_IsHalted()
TA260 003:591.197 - 0.501ms returns FALSE
TA260 003:591.206 JLINK_HasError()
TA260 003:592.696 JLINK_IsHalted()
TA260 003:593.159 - 0.462ms returns FALSE
TA260 003:593.165 JLINK_HasError()
TA260 003:594.711 JLINK_IsHalted()
TA260 003:595.245 - 0.533ms returns FALSE
TA260 003:595.257 JLINK_HasError()
TA260 003:597.210 JLINK_IsHalted()
TA260 003:597.659 - 0.449ms returns FALSE
TA260 003:597.665 JLINK_HasError()
TA260 003:599.208 JLINK_IsHalted()
TA260 003:601.552   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:602.036 - 2.828ms returns TRUE
TA260 003:602.043 JLINK_ReadReg(R15 (PC))
TA260 003:602.048 - 0.005ms returns 0x20000000
TA260 003:602.053 JLINK_ClrBPEx(BPHandle = 0x00000049)
TA260 003:602.057 - 0.003ms returns 0x00
TA260 003:602.088 JLINK_ReadReg(R0)
TA260 003:602.092 - 0.030ms returns 0x59958467
TA260 003:603.840 JLINK_HasError()
TA260 003:603.850 JLINK_WriteReg(R0, 0x00000003)
TA260 003:603.856 - 0.004ms returns 0
TA260 003:603.860 JLINK_WriteReg(R1, 0x08000000)
TA260 003:603.864 - 0.003ms returns 0
TA260 003:603.868 JLINK_WriteReg(R2, 0x0000E8C0)
TA260 003:603.871 - 0.003ms returns 0
TA260 003:603.875 JLINK_WriteReg(R3, 0x04C11DB7)
TA260 003:603.878 - 0.003ms returns 0
TA260 003:603.882 JLINK_WriteReg(R4, 0x00000000)
TA260 003:603.886 - 0.003ms returns 0
TA260 003:603.890 JLINK_WriteReg(R5, 0x00000000)
TA260 003:603.893 - 0.003ms returns 0
TA260 003:603.897 JLINK_WriteReg(R6, 0x00000000)
TA260 003:603.900 - 0.003ms returns 0
TA260 003:603.904 JLINK_WriteReg(R7, 0x00000000)
TA260 003:603.908 - 0.003ms returns 0
TA260 003:603.912 JLINK_WriteReg(R8, 0x00000000)
TA260 003:603.916 - 0.003ms returns 0
TA260 003:603.920 JLINK_WriteReg(R9, 0x20000180)
TA260 003:603.923 - 0.003ms returns 0
TA260 003:603.927 JLINK_WriteReg(R10, 0x00000000)
TA260 003:603.930 - 0.003ms returns 0
TA260 003:603.934 JLINK_WriteReg(R11, 0x00000000)
TA260 003:603.938 - 0.003ms returns 0
TA260 003:603.942 JLINK_WriteReg(R12, 0x00000000)
TA260 003:603.945 - 0.003ms returns 0
TA260 003:603.950 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:603.953 - 0.003ms returns 0
TA260 003:603.957 JLINK_WriteReg(R14, 0x20000001)
TA260 003:603.961 - 0.003ms returns 0
TA260 003:603.965 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 003:603.968 - 0.003ms returns 0
TA260 003:603.972 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:603.976 - 0.003ms returns 0
TA260 003:603.980 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:603.983 - 0.003ms returns 0
TA260 003:603.987 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:603.990 - 0.003ms returns 0
TA260 003:603.994 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:603.998 - 0.003ms returns 0
TA260 003:604.002 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:604.007 - 0.004ms returns 0x0000004A
TA260 003:604.011 JLINK_Go()
TA260 003:604.020   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:606.820 - 2.809ms 
TA260 003:606.914 JLINK_IsHalted()
TA260 003:609.245   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:609.813 - 2.899ms returns TRUE
TA260 003:609.828 JLINK_ReadReg(R15 (PC))
TA260 003:609.833 - 0.005ms returns 0x20000000
TA260 003:609.838 JLINK_ClrBPEx(BPHandle = 0x0000004A)
TA260 003:609.842 - 0.004ms returns 0x00
TA260 003:609.847 JLINK_ReadReg(R0)
TA260 003:609.850 - 0.003ms returns 0x00000000
TA260 003:663.164 JLINK_WriteMemEx(0x20000000, 0x00000002 Bytes, Flags = 0x02000000)
TA260 003:663.182   Data:  FE E7
TA260 003:663.200   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 003:663.696 - 0.532ms returns 0x2
TA260 003:663.704 JLINK_HasError()
TA260 003:666.378 JLINK_Close()
TA260 003:668.562   OnDisconnectTarget() start
TA260 003:668.580    J-Link Script File: Executing OnDisconnectTarget()
TA260 003:668.592   CPU_WriteMem(4 bytes @ 0xE0042004)
TA260 003:669.095   CPU_WriteMem(4 bytes @ 0xE0042008)
TA260 003:670.935   OnDisconnectTarget() end - Took 946us
TA260 003:670.950   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:690.441 - 24.062ms
TA260 003:690.461   
TA260 003:690.465   Closed
