TA260 000:003.816   SEGGER J-Link V8.16 Log File
TA260 000:003.906   DLL Compiled: Feb 26 2025 12:07:26
TA260 000:003.911   Logging started @ 2025-08-02 08:59
TA260 000:003.914   Process: G:\keil\keil arm\UV4\UV4.exe
TA260 000:003.925 - 3.918ms 
TA260 000:003.963 JLINK_SetWarnOutHandler(...)
TA260 000:003.968 - 0.007ms 
TA260 000:003.978 JLINK_OpenEx(...)
TA260 000:007.847   Firmware: J-Link V9 compiled May  7 2021 16:26:12
TA260 000:009.213   Firmware: J-Link V9 compiled May  7 2021 16:26:12
TA260 000:009.353   Decompressing FW timestamp took 91 us
TA260 000:016.853   Hardware: V9.60
TA260 000:016.869   S/N: 69655018
TA260 000:016.874   OEM: SEGGER
TA260 000:016.880   Feature(s): R<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, JFlash
TA260 000:018.226   Bootloader: (FW returned invalid version)
TA260 000:019.696   TELNET listener socket opened on port 19021
TA260 000:019.760   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
TA260 000:019.875   WEBSRV Webserver running on local port 19080
TA260 000:019.940   Looking for J-Link GUI Server exe at: G:\keil\keil arm\ARM\Segger\JLinkGUIServer.exe
TA260 000:020.019   Looking for J-Link GUI Server exe at: C:\Program Files (x86)\SEGGER\JLink_V630d\\JLinkGUIServer.exe
TA260 000:323.094   Failed to connect to J-Link GUI Server.
TA260 000:323.122 - 319.137ms returns "O.K."
TA260 000:323.138 JLINK_GetEmuCaps()
TA260 000:323.144 - 0.004ms returns 0xB9FF7BBF
TA260 000:323.152 JLINK_TIF_GetAvailable(...)
TA260 000:323.519 - 0.367ms 
TA260 000:323.550 JLINK_SetErrorOutHandler(...)
TA260 000:323.555 - 0.004ms 
TA260 000:323.581 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\2025ele_ori\zuolan_stm32\MDK-ARM\JLinkSettings.ini"", ...). 
TA260 000:334.956 - 11.375ms returns 0x00
TA260 000:348.880 JLINK_ExecCommand("Device = STM32F429IGTx", ...). 
TA260 000:350.171   Flash bank @ 0x90000000: SFL: Parsing sectorization info from ELF file
TA260 000:350.186     FlashDevice.SectorInfo[0]: .SectorSize = 0x00010000, .SectorStartAddr = 0x00000000
TA260 000:359.670   Device "STM32F429IG" selected.
TA260 000:359.881 - 10.982ms returns 0x00
TA260 000:359.893 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
TA260 000:359.911   ERROR: Unknown command
TA260 000:359.917 - 0.018ms returns 0x01
TA260 000:359.922 JLINK_GetHardwareVersion()
TA260 000:359.926 - 0.004ms returns 96000
TA260 000:359.931 JLINK_GetDLLVersion()
TA260 000:359.934 - 0.003ms returns 81600
TA260 000:359.938 JLINK_GetOEMString(...)
TA260 000:359.943 JLINK_GetFirmwareString(...)
TA260 000:359.947 - 0.004ms 
TA260 000:368.141 JLINK_GetDLLVersion()
TA260 000:368.157 - 0.016ms returns 81600
TA260 000:368.162 JLINK_GetCompileDateTime()
TA260 000:368.166 - 0.003ms 
TA260 000:372.347 JLINK_GetFirmwareString(...)
TA260 000:372.369 - 0.021ms 
TA260 000:376.574 JLINK_GetHardwareVersion()
TA260 000:376.596 - 0.020ms returns 96000
TA260 000:380.417 JLINK_GetSN()
TA260 000:380.433 - 0.015ms returns 69655018
TA260 000:383.683 JLINK_GetOEMString(...)
TA260 000:389.911 JLINK_TIF_Select(JLINKARM_TIF_SWD)
TA260 000:391.451 - 1.540ms returns 0x00
TA260 000:391.468 JLINK_HasError()
TA260 000:391.484 JLINK_SetSpeed(5000)
TA260 000:391.801 - 0.318ms 
TA260 000:391.808 JLINK_GetId()
TA260 000:395.614   InitTarget() start
TA260 000:395.645    J-Link Script File: Executing InitTarget()
TA260 000:400.353   SWD selected. Executing JTAG -> SWD switching sequence.
TA260 000:406.574   DAP initialized successfully.
TA260 000:419.823   InitTarget() end - Took 21.4ms
TA260 000:423.577   Found SW-DP with ID 0x2BA01477
TA260 000:429.087   DPIDR: 0x2BA01477
TA260 000:430.649   CoreSight SoC-400 or earlier
TA260 000:432.111   Scanning AP map to find all available APs
TA260 000:434.648   AP[1]: Stopped AP scan as end of AP map has been reached
TA260 000:436.044   AP[0]: AHB-AP (IDR: 0x24770011, ADDR: 0x00000000)
TA260 000:437.651   Iterating through AP map to find AHB-AP to use
TA260 000:440.604   AP[0]: Core found
TA260 000:442.180   AP[0]: AHB-AP ROM base: 0xE00FF000
TA260 000:444.794   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
TA260 000:446.639   Found Cortex-M4 r0p1, Little endian.
TA260 000:447.525   -- Max. mem block: 0x00010C40
TA260 000:448.304   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:448.892   CPU_ReadMem(4 bytes @ 0x********)
TA260 000:451.140   FPUnit: 6 code (BP) slots and 2 literal slots
TA260 000:451.171   CPU_ReadMem(4 bytes @ 0xE000EDFC)
TA260 000:451.616   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:452.115   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:452.616   CPU_WriteMem(4 bytes @ 0xE0001000)
TA260 000:453.089   CPU_ReadMem(4 bytes @ 0xE000ED88)
TA260 000:453.616   CPU_WriteMem(4 bytes @ 0xE000ED88)
TA260 000:454.090   CPU_ReadMem(4 bytes @ 0xE000ED88)
TA260 000:454.614   CPU_WriteMem(4 bytes @ 0xE000ED88)
TA260 000:457.288   CoreSight components:
TA260 000:459.536   ROMTbl[0] @ E00FF000
TA260 000:459.563   CPU_ReadMem(64 bytes @ 0xE00FF000)
TA260 000:460.310   CPU_ReadMem(32 bytes @ 0xE000EFE0)
TA260 000:462.550   [0][0]: E000E000 CID B105E00D PID 000BB00C SCS-M7
TA260 000:462.572   CPU_ReadMem(32 bytes @ 0xE0001FE0)
TA260 000:464.969   [0][1]: E0001000 CID B105E00D PID 003BB002 DWT
TA260 000:464.991   CPU_ReadMem(32 bytes @ 0xE0002FE0)
TA260 000:467.742   [0][2]: ******** CID B105E00D PID 002BB003 FPB
TA260 000:467.771   CPU_ReadMem(32 bytes @ 0xE0000FE0)
TA260 000:470.210   [0][3]: ******** CID B105E00D PID 003BB001 ITM
TA260 000:470.239   CPU_ReadMem(32 bytes @ 0xE0040FE0)
TA260 000:472.393   [0][4]: ******** CID B105900D PID 000BB9A1 TPIU
TA260 000:472.414   CPU_ReadMem(32 bytes @ 0xE0041FE0)
TA260 000:474.614   [0][5]: ******** CID B105900D PID 000BB925 ETM
TA260 000:475.125 - 83.315ms returns 0x2BA01477
TA260 000:475.186 JLINK_GetDLLVersion()
TA260 000:475.196 - 0.009ms returns 81600
TA260 000:475.209 JLINK_CORE_GetFound()
TA260 000:475.213 - 0.003ms returns 0xE0000FF
TA260 000:475.218 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
TA260 000:475.224   Value=0xE00FF000
TA260 000:475.230 - 0.012ms returns 0
TA260 000:478.072 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
TA260 000:478.100   Value=0xE00FF000
TA260 000:478.106 - 0.034ms returns 0
TA260 000:478.111 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
TA260 000:478.115   Value=0x********
TA260 000:478.120 - 0.008ms returns 0
TA260 000:478.125 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
TA260 000:478.175   CPU_ReadMem(32 bytes @ 0xE0041FD0)
TA260 000:478.863   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
TA260 000:478.870 - 0.745ms returns 32 (0x20)
TA260 000:478.875 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
TA260 000:478.879   Value=0x00000000
TA260 000:478.884 - 0.008ms returns 0
TA260 000:478.888 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
TA260 000:478.891   Value=0x********
TA260 000:478.896 - 0.008ms returns 0
TA260 000:478.900 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
TA260 000:478.904   Value=0x********
TA260 000:478.909 - 0.008ms returns 0
TA260 000:478.913 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
TA260 000:478.916   Value=0xE0001000
TA260 000:478.921 - 0.008ms returns 0
TA260 000:478.926 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
TA260 000:478.929   Value=0x********
TA260 000:478.934 - 0.008ms returns 0
TA260 000:478.938 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
TA260 000:478.941   Value=0xE000E000
TA260 000:478.946 - 0.008ms returns 0
TA260 000:478.950 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
TA260 000:478.953   Value=0xE000EDF0
TA260 000:479.071 - 0.120ms returns 0
TA260 000:479.075 JLINK_GetDebugInfo(0x01 = Unknown)
TA260 000:479.079   Value=0x00000001
TA260 000:479.083 - 0.008ms returns 0
TA260 000:479.088 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
TA260 000:479.094   CPU_ReadMem(4 bytes @ 0xE000ED00)
TA260 000:479.615   Data:  41 C2 0F 41
TA260 000:479.623   Debug reg: CPUID
TA260 000:479.628 - 0.540ms returns 1 (0x1)
TA260 000:479.634 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
TA260 000:479.681   Value=0x00000000
TA260 000:479.686 - 0.052ms returns 0
TA260 000:479.691 JLINK_HasError()
TA260 000:479.696 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
TA260 000:479.700 - 0.003ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
TA260 000:479.704 JLINK_Reset()
TA260 000:479.712   JLINK_GetResetTypeDesc
TA260 000:479.715   - 0.003ms 
TA260 000:481.585   Reset type: NORMAL (https://wiki.segger.com/J-Link_Reset_Strategies)
TA260 000:481.623   CPU is running
TA260 000:481.631   CPU_WriteMem(4 bytes @ 0xE000EDF0)
TA260 000:482.138   CPU is running
TA260 000:482.145   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:484.268   Reset: Halt core after reset via DEMCR.VC_CORERESET.
TA260 000:486.569   Reset: Reset device via AIRCR.SYSRESETREQ.
TA260 000:486.590   CPU is running
TA260 000:486.599   CPU_WriteMem(4 bytes @ 0xE000ED0C)
TA260 000:540.471   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:540.935   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:543.836   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:550.274   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:553.099   CPU_WriteMem(4 bytes @ 0x********)
TA260 000:553.543   CPU_ReadMem(4 bytes @ 0xE000EDFC)
TA260 000:554.023   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:554.503 - 74.798ms 
TA260 000:554.524 JLINK_Halt()
TA260 000:554.528 - 0.004ms returns 0x00
TA260 000:554.572 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
TA260 000:554.582   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:555.039   Data:  03 00 03 00
TA260 000:555.046   Debug reg: DHCSR
TA260 000:555.051 - 0.480ms returns 1 (0x1)
TA260 000:555.057 JLINK_WriteU32(0xE000EDF0, 0xA05F0003)
TA260 000:555.060   Debug reg: DHCSR
TA260 000:555.317   CPU_WriteMem(4 bytes @ 0xE000EDF0)
TA260 000:555.870 - 0.812ms returns 0 (0x00000000)
TA260 000:555.880 JLINK_WriteU32(0xE000EDFC, 0x01000000)
TA260 000:555.885   Debug reg: DEMCR
TA260 000:555.893   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:556.375 - 0.494ms returns 0 (0x00000000)
TA260 000:562.761 JLINK_GetHWStatus(...)
TA260 000:563.159 - 0.397ms returns 0
TA260 000:567.506 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
TA260 000:567.539 - 0.033ms returns 0x06
TA260 000:567.544 JLINK_GetNumBPUnits(Type = 0xF0)
TA260 000:567.548 - 0.004ms returns 0x2000
TA260 000:567.552 JLINK_GetNumWPUnits()
TA260 000:567.556 - 0.003ms returns 4
TA260 000:572.636 JLINK_GetSpeed()
TA260 000:572.662 - 0.025ms returns 4000
TA260 000:575.936 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
TA260 000:575.973   CPU_ReadMem(4 bytes @ 0xE000E004)
TA260 000:576.522   Data:  02 00 00 00
TA260 000:576.536 - 0.600ms returns 1 (0x1)
TA260 000:576.543 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
TA260 000:576.551   CPU_ReadMem(4 bytes @ 0xE000E004)
TA260 000:577.035   Data:  02 00 00 00
TA260 000:577.042 - 0.498ms returns 1 (0x1)
TA260 000:577.047 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
TA260 000:577.051   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
TA260 000:577.061   CPU_WriteMem(28 bytes @ 0xE0001000)
TA260 000:577.751 - 0.703ms returns 0x1C
TA260 000:577.772 JLINK_Halt()
TA260 000:577.776 - 0.003ms returns 0x00
TA260 000:577.780 JLINK_IsHalted()
TA260 000:577.785 - 0.004ms returns TRUE
TA260 000:580.383 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 000:580.396   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 000:580.630   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 000:582.485 - 2.102ms returns 0x184
TA260 000:582.526 JLINK_HasError()
TA260 000:582.532 JLINK_WriteReg(R0, 0x08000000)
TA260 000:582.537 - 0.005ms returns 0
TA260 000:582.541 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 000:582.545 - 0.003ms returns 0
TA260 000:582.549 JLINK_WriteReg(R2, 0x00000001)
TA260 000:582.553 - 0.003ms returns 0
TA260 000:582.557 JLINK_WriteReg(R3, 0x00000000)
TA260 000:582.561 - 0.003ms returns 0
TA260 000:582.565 JLINK_WriteReg(R4, 0x00000000)
TA260 000:582.568 - 0.003ms returns 0
TA260 000:582.573 JLINK_WriteReg(R5, 0x00000000)
TA260 000:582.615 - 0.042ms returns 0
TA260 000:582.622 JLINK_WriteReg(R6, 0x00000000)
TA260 000:582.625 - 0.003ms returns 0
TA260 000:582.629 JLINK_WriteReg(R7, 0x00000000)
TA260 000:582.633 - 0.003ms returns 0
TA260 000:582.651 JLINK_WriteReg(R8, 0x00000000)
TA260 000:582.655 - 0.017ms returns 0
TA260 000:582.659 JLINK_WriteReg(R9, 0x20000180)
TA260 000:582.662 - 0.003ms returns 0
TA260 000:582.666 JLINK_WriteReg(R10, 0x00000000)
TA260 000:582.669 - 0.003ms returns 0
TA260 000:582.673 JLINK_WriteReg(R11, 0x00000000)
TA260 000:582.677 - 0.003ms returns 0
TA260 000:582.681 JLINK_WriteReg(R12, 0x00000000)
TA260 000:582.684 - 0.003ms returns 0
TA260 000:582.689 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:582.693 - 0.004ms returns 0
TA260 000:582.697 JLINK_WriteReg(R14, 0x20000001)
TA260 000:582.700 - 0.003ms returns 0
TA260 000:582.708 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 000:582.712 - 0.007ms returns 0
TA260 000:582.716 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:582.719 - 0.003ms returns 0
TA260 000:582.723 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:582.727 - 0.003ms returns 0
TA260 000:582.731 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:582.735 - 0.003ms returns 0
TA260 000:582.739 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:582.742 - 0.003ms returns 0
TA260 000:582.747 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:582.754   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:583.276 - 0.529ms returns 0x00000001
TA260 000:583.283 JLINK_Go()
TA260 000:583.288   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 000:583.808   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:584.325   CPU_WriteMem(4 bytes @ 0xE0002008)
TA260 000:584.339   CPU_WriteMem(4 bytes @ 0xE000200C)
TA260 000:584.344   CPU_WriteMem(4 bytes @ 0xE0002010)
TA260 000:584.349   CPU_WriteMem(4 bytes @ 0xE0002014)
TA260 000:584.354   CPU_WriteMem(4 bytes @ 0xE0002018)
TA260 000:584.359   CPU_WriteMem(4 bytes @ 0xE000201C)
TA260 000:585.586   CPU_WriteMem(4 bytes @ 0xE0001004)
TA260 000:590.325   Memory map 'after startup completion point' is active
TA260 000:590.353 - 7.068ms 
TA260 000:590.361 JLINK_IsHalted()
TA260 000:592.693   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:593.193 - 2.831ms returns TRUE
TA260 000:593.206 JLINK_ReadReg(R15 (PC))
TA260 000:593.212 - 0.006ms returns 0x20000000
TA260 000:593.217 JLINK_ClrBPEx(BPHandle = 0x00000001)
TA260 000:593.221 - 0.004ms returns 0x00
TA260 000:593.225 JLINK_ReadReg(R0)
TA260 000:593.229 - 0.003ms returns 0x00000000
TA260 000:593.498 JLINK_HasError()
TA260 000:593.506 JLINK_WriteReg(R0, 0x08000000)
TA260 000:593.510 - 0.004ms returns 0
TA260 000:593.515 JLINK_WriteReg(R1, 0x00004000)
TA260 000:593.518 - 0.003ms returns 0
TA260 000:593.522 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:593.526 - 0.003ms returns 0
TA260 000:593.530 JLINK_WriteReg(R3, 0x00000000)
TA260 000:593.533 - 0.003ms returns 0
TA260 000:593.537 JLINK_WriteReg(R4, 0x00000000)
TA260 000:593.540 - 0.003ms returns 0
TA260 000:593.544 JLINK_WriteReg(R5, 0x00000000)
TA260 000:593.548 - 0.003ms returns 0
TA260 000:593.553 JLINK_WriteReg(R6, 0x00000000)
TA260 000:593.556 - 0.003ms returns 0
TA260 000:593.560 JLINK_WriteReg(R7, 0x00000000)
TA260 000:593.564 - 0.003ms returns 0
TA260 000:593.568 JLINK_WriteReg(R8, 0x00000000)
TA260 000:593.571 - 0.003ms returns 0
TA260 000:593.575 JLINK_WriteReg(R9, 0x20000180)
TA260 000:593.579 - 0.003ms returns 0
TA260 000:593.583 JLINK_WriteReg(R10, 0x00000000)
TA260 000:593.586 - 0.003ms returns 0
TA260 000:593.590 JLINK_WriteReg(R11, 0x00000000)
TA260 000:593.594 - 0.003ms returns 0
TA260 000:593.598 JLINK_WriteReg(R12, 0x00000000)
TA260 000:593.601 - 0.003ms returns 0
TA260 000:593.605 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:593.609 - 0.003ms returns 0
TA260 000:593.613 JLINK_WriteReg(R14, 0x20000001)
TA260 000:593.616 - 0.003ms returns 0
TA260 000:593.620 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 000:593.624 - 0.003ms returns 0
TA260 000:593.628 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:593.631 - 0.003ms returns 0
TA260 000:593.644 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:593.647 - 0.003ms returns 0
TA260 000:593.651 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:593.655 - 0.003ms returns 0
TA260 000:593.659 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:593.663 - 0.003ms returns 0
TA260 000:593.667 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:593.671 - 0.004ms returns 0x00000002
TA260 000:593.675 JLINK_Go()
TA260 000:593.685   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:596.405 - 2.728ms 
TA260 000:596.421 JLINK_IsHalted()
TA260 000:598.837   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:599.342 - 2.921ms returns TRUE
TA260 000:599.354 JLINK_ReadReg(R15 (PC))
TA260 000:599.360 - 0.006ms returns 0x20000000
TA260 000:599.365 JLINK_ClrBPEx(BPHandle = 0x00000002)
TA260 000:599.369 - 0.004ms returns 0x00
TA260 000:599.373 JLINK_ReadReg(R0)
TA260 000:599.377 - 0.003ms returns 0x00000001
TA260 000:599.382 JLINK_HasError()
TA260 000:599.387 JLINK_WriteReg(R0, 0x08000000)
TA260 000:599.391 - 0.004ms returns 0
TA260 000:599.395 JLINK_WriteReg(R1, 0x00004000)
TA260 000:599.399 - 0.003ms returns 0
TA260 000:599.403 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:599.406 - 0.003ms returns 0
TA260 000:599.411 JLINK_WriteReg(R3, 0x00000000)
TA260 000:599.414 - 0.003ms returns 0
TA260 000:599.418 JLINK_WriteReg(R4, 0x00000000)
TA260 000:599.422 - 0.003ms returns 0
TA260 000:599.426 JLINK_WriteReg(R5, 0x00000000)
TA260 000:599.429 - 0.003ms returns 0
TA260 000:599.433 JLINK_WriteReg(R6, 0x00000000)
TA260 000:599.437 - 0.003ms returns 0
TA260 000:599.441 JLINK_WriteReg(R7, 0x00000000)
TA260 000:599.444 - 0.003ms returns 0
TA260 000:599.448 JLINK_WriteReg(R8, 0x00000000)
TA260 000:599.452 - 0.003ms returns 0
TA260 000:599.456 JLINK_WriteReg(R9, 0x20000180)
TA260 000:599.459 - 0.003ms returns 0
TA260 000:599.463 JLINK_WriteReg(R10, 0x00000000)
TA260 000:599.467 - 0.003ms returns 0
TA260 000:599.471 JLINK_WriteReg(R11, 0x00000000)
TA260 000:599.474 - 0.003ms returns 0
TA260 000:599.478 JLINK_WriteReg(R12, 0x00000000)
TA260 000:599.481 - 0.003ms returns 0
TA260 000:599.486 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:599.489 - 0.003ms returns 0
TA260 000:599.493 JLINK_WriteReg(R14, 0x20000001)
TA260 000:599.496 - 0.003ms returns 0
TA260 000:599.501 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 000:599.505 - 0.003ms returns 0
TA260 000:599.509 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:599.512 - 0.003ms returns 0
TA260 000:599.516 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:599.520 - 0.003ms returns 0
TA260 000:599.524 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:599.527 - 0.003ms returns 0
TA260 000:599.531 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:599.534 - 0.003ms returns 0
TA260 000:599.539 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:599.543 - 0.004ms returns 0x00000003
TA260 000:599.548 JLINK_Go()
TA260 000:599.558   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:602.312 - 2.763ms 
TA260 000:602.330 JLINK_IsHalted()
TA260 000:602.787 - 0.457ms returns FALSE
TA260 000:602.796 JLINK_HasError()
TA260 000:613.206 JLINK_IsHalted()
TA260 000:613.700 - 0.493ms returns FALSE
TA260 000:613.706 JLINK_HasError()
TA260 000:615.214 JLINK_IsHalted()
TA260 000:615.762 - 0.548ms returns FALSE
TA260 000:615.773 JLINK_HasError()
TA260 000:617.207 JLINK_IsHalted()
TA260 000:617.721 - 0.513ms returns FALSE
TA260 000:617.734 JLINK_HasError()
TA260 000:619.205 JLINK_IsHalted()
TA260 000:619.695 - 0.489ms returns FALSE
TA260 000:619.705 JLINK_HasError()
TA260 000:621.203 JLINK_IsHalted()
TA260 000:621.696 - 0.492ms returns FALSE
TA260 000:621.706 JLINK_HasError()
TA260 000:623.211 JLINK_IsHalted()
TA260 000:623.700 - 0.488ms returns FALSE
TA260 000:623.708 JLINK_HasError()
TA260 000:625.713 JLINK_IsHalted()
TA260 000:626.220 - 0.506ms returns FALSE
TA260 000:626.229 JLINK_HasError()
TA260 000:627.710 JLINK_IsHalted()
TA260 000:628.163 - 0.453ms returns FALSE
TA260 000:628.173 JLINK_HasError()
TA260 000:629.711 JLINK_IsHalted()
TA260 000:630.204 - 0.493ms returns FALSE
TA260 000:630.222 JLINK_HasError()
TA260 000:631.703 JLINK_IsHalted()
TA260 000:632.150 - 0.447ms returns FALSE
TA260 000:632.160 JLINK_HasError()
TA260 000:633.212 JLINK_IsHalted()
TA260 000:633.698 - 0.485ms returns FALSE
TA260 000:633.705 JLINK_HasError()
TA260 000:635.213 JLINK_IsHalted()
TA260 000:635.713 - 0.500ms returns FALSE
TA260 000:635.719 JLINK_HasError()
TA260 000:637.220 JLINK_IsHalted()
TA260 000:637.694 - 0.473ms returns FALSE
TA260 000:637.701 JLINK_HasError()
TA260 000:639.212 JLINK_IsHalted()
TA260 000:639.696 - 0.483ms returns FALSE
TA260 000:639.706 JLINK_HasError()
TA260 000:641.216 JLINK_IsHalted()
TA260 000:641.715 - 0.498ms returns FALSE
TA260 000:641.723 JLINK_HasError()
TA260 000:643.212 JLINK_IsHalted()
TA260 000:643.693 - 0.480ms returns FALSE
TA260 000:643.699 JLINK_HasError()
TA260 000:645.725 JLINK_IsHalted()
TA260 000:646.160 - 0.435ms returns FALSE
TA260 000:646.168 JLINK_HasError()
TA260 000:647.725 JLINK_IsHalted()
TA260 000:648.210 - 0.485ms returns FALSE
TA260 000:648.229 JLINK_HasError()
TA260 000:650.731 JLINK_IsHalted()
TA260 000:651.254 - 0.523ms returns FALSE
TA260 000:651.261 JLINK_HasError()
TA260 000:653.229 JLINK_IsHalted()
TA260 000:653.691 - 0.462ms returns FALSE
TA260 000:653.697 JLINK_HasError()
TA260 000:654.745 JLINK_IsHalted()
TA260 000:655.261 - 0.516ms returns FALSE
TA260 000:655.274 JLINK_HasError()
TA260 000:656.751 JLINK_IsHalted()
TA260 000:657.187 - 0.435ms returns FALSE
TA260 000:657.197 JLINK_HasError()
TA260 000:658.749 JLINK_IsHalted()
TA260 000:659.216 - 0.466ms returns FALSE
TA260 000:659.222 JLINK_HasError()
TA260 000:660.743 JLINK_IsHalted()
TA260 000:661.227 - 0.483ms returns FALSE
TA260 000:661.233 JLINK_HasError()
TA260 000:663.250 JLINK_IsHalted()
TA260 000:663.785 - 0.534ms returns FALSE
TA260 000:663.793 JLINK_HasError()
TA260 000:665.253 JLINK_IsHalted()
TA260 000:665.695 - 0.441ms returns FALSE
TA260 000:665.704 JLINK_HasError()
TA260 000:667.258 JLINK_IsHalted()
TA260 000:667.749 - 0.491ms returns FALSE
TA260 000:667.757 JLINK_HasError()
TA260 000:669.252 JLINK_IsHalted()
TA260 000:669.741 - 0.488ms returns FALSE
TA260 000:669.747 JLINK_HasError()
TA260 000:671.253 JLINK_IsHalted()
TA260 000:671.740 - 0.487ms returns FALSE
TA260 000:671.748 JLINK_HasError()
TA260 000:673.259 JLINK_IsHalted()
TA260 000:673.761 - 0.502ms returns FALSE
TA260 000:673.767 JLINK_HasError()
TA260 000:675.761 JLINK_IsHalted()
TA260 000:676.273 - 0.513ms returns FALSE
TA260 000:676.281 JLINK_HasError()
TA260 000:677.764 JLINK_IsHalted()
TA260 000:678.264 - 0.499ms returns FALSE
TA260 000:678.274 JLINK_HasError()
TA260 000:679.764 JLINK_IsHalted()
TA260 000:680.242 - 0.477ms returns FALSE
TA260 000:680.258 JLINK_HasError()
TA260 000:681.764 JLINK_IsHalted()
TA260 000:682.217 - 0.453ms returns FALSE
TA260 000:682.224 JLINK_HasError()
TA260 000:683.267 JLINK_IsHalted()
TA260 000:683.673 - 0.406ms returns FALSE
TA260 000:683.681 JLINK_HasError()
TA260 000:685.273 JLINK_IsHalted()
TA260 000:685.702 - 0.429ms returns FALSE
TA260 000:685.709 JLINK_HasError()
TA260 000:687.271 JLINK_IsHalted()
TA260 000:687.736 - 0.464ms returns FALSE
TA260 000:687.744 JLINK_HasError()
TA260 000:689.278 JLINK_IsHalted()
TA260 000:689.770 - 0.491ms returns FALSE
TA260 000:689.777 JLINK_HasError()
TA260 000:691.270 JLINK_IsHalted()
TA260 000:691.785 - 0.514ms returns FALSE
TA260 000:691.793 JLINK_HasError()
TA260 000:693.272 JLINK_IsHalted()
TA260 000:693.851 - 0.578ms returns FALSE
TA260 000:693.870 JLINK_HasError()
TA260 000:695.786 JLINK_IsHalted()
TA260 000:696.247 - 0.460ms returns FALSE
TA260 000:696.259 JLINK_HasError()
TA260 000:697.776 JLINK_IsHalted()
TA260 000:698.262 - 0.486ms returns FALSE
TA260 000:698.269 JLINK_HasError()
TA260 000:699.785 JLINK_IsHalted()
TA260 000:700.324 - 0.539ms returns FALSE
TA260 000:700.335 JLINK_HasError()
TA260 000:701.787 JLINK_IsHalted()
TA260 000:702.243 - 0.456ms returns FALSE
TA260 000:702.250 JLINK_HasError()
TA260 000:704.283 JLINK_IsHalted()
TA260 000:704.786 - 0.502ms returns FALSE
TA260 000:704.796 JLINK_HasError()
TA260 000:706.288 JLINK_IsHalted()
TA260 000:706.801 - 0.512ms returns FALSE
TA260 000:706.808 JLINK_HasError()
TA260 000:708.287 JLINK_IsHalted()
TA260 000:708.751 - 0.463ms returns FALSE
TA260 000:708.761 JLINK_HasError()
TA260 000:710.291 JLINK_IsHalted()
TA260 000:710.742 - 0.450ms returns FALSE
TA260 000:710.750 JLINK_HasError()
TA260 000:712.287 JLINK_IsHalted()
TA260 000:712.754 - 0.466ms returns FALSE
TA260 000:712.762 JLINK_HasError()
TA260 000:714.797 JLINK_IsHalted()
TA260 000:715.343 - 0.546ms returns FALSE
TA260 000:715.354 JLINK_HasError()
TA260 000:716.799 JLINK_IsHalted()
TA260 000:717.297 - 0.497ms returns FALSE
TA260 000:717.309 JLINK_HasError()
TA260 000:718.797 JLINK_IsHalted()
TA260 000:719.287 - 0.489ms returns FALSE
TA260 000:719.296 JLINK_HasError()
TA260 000:720.802 JLINK_IsHalted()
TA260 000:721.285 - 0.483ms returns FALSE
TA260 000:721.292 JLINK_HasError()
TA260 000:723.307 JLINK_IsHalted()
TA260 000:723.843 - 0.536ms returns FALSE
TA260 000:723.851 JLINK_HasError()
TA260 000:725.320 JLINK_IsHalted()
TA260 000:725.843 - 0.523ms returns FALSE
TA260 000:725.853 JLINK_HasError()
TA260 000:729.307 JLINK_IsHalted()
TA260 000:729.812 - 0.504ms returns FALSE
TA260 000:729.820 JLINK_HasError()
TA260 000:731.310 JLINK_IsHalted()
TA260 000:731.787 - 0.477ms returns FALSE
TA260 000:731.794 JLINK_HasError()
TA260 000:733.305 JLINK_IsHalted()
TA260 000:733.791 - 0.486ms returns FALSE
TA260 000:733.805 JLINK_HasError()
TA260 000:735.820 JLINK_IsHalted()
TA260 000:736.329 - 0.509ms returns FALSE
TA260 000:736.337 JLINK_HasError()
TA260 000:737.810 JLINK_IsHalted()
TA260 000:738.242 - 0.431ms returns FALSE
TA260 000:738.261 JLINK_HasError()
TA260 000:739.816 JLINK_IsHalted()
TA260 000:740.323 - 0.506ms returns FALSE
TA260 000:740.335 JLINK_HasError()
TA260 000:741.820 JLINK_IsHalted()
TA260 000:742.283 - 0.462ms returns FALSE
TA260 000:742.294 JLINK_HasError()
TA260 000:744.316 JLINK_IsHalted()
TA260 000:744.766 - 0.449ms returns FALSE
TA260 000:744.775 JLINK_HasError()
TA260 000:746.324 JLINK_IsHalted()
TA260 000:746.799 - 0.475ms returns FALSE
TA260 000:746.807 JLINK_HasError()
TA260 000:748.320 JLINK_IsHalted()
TA260 000:748.756 - 0.434ms returns FALSE
TA260 000:748.767 JLINK_HasError()
TA260 000:750.316 JLINK_IsHalted()
TA260 000:750.832 - 0.516ms returns FALSE
TA260 000:750.842 JLINK_HasError()
TA260 000:752.331 JLINK_IsHalted()
TA260 000:752.833 - 0.501ms returns FALSE
TA260 000:752.841 JLINK_HasError()
TA260 000:754.831 JLINK_IsHalted()
TA260 000:755.364 - 0.532ms returns FALSE
TA260 000:755.373 JLINK_HasError()
TA260 000:756.838 JLINK_IsHalted()
TA260 000:757.334 - 0.495ms returns FALSE
TA260 000:757.347 JLINK_HasError()
TA260 000:758.835 JLINK_IsHalted()
TA260 000:759.289 - 0.453ms returns FALSE
TA260 000:759.297 JLINK_HasError()
TA260 000:760.828 JLINK_IsHalted()
TA260 000:761.284 - 0.455ms returns FALSE
TA260 000:761.291 JLINK_HasError()
TA260 000:763.341 JLINK_IsHalted()
TA260 000:763.804 - 0.461ms returns FALSE
TA260 000:763.814 JLINK_HasError()
TA260 000:765.339 JLINK_IsHalted()
TA260 000:765.799 - 0.459ms returns FALSE
TA260 000:765.808 JLINK_HasError()
TA260 000:767.339 JLINK_IsHalted()
TA260 000:767.805 - 0.466ms returns FALSE
TA260 000:767.816 JLINK_HasError()
TA260 000:769.339 JLINK_IsHalted()
TA260 000:769.841 - 0.502ms returns FALSE
TA260 000:769.855 JLINK_HasError()
TA260 000:771.336 JLINK_IsHalted()
TA260 000:771.847 - 0.511ms returns FALSE
TA260 000:771.864 JLINK_HasError()
TA260 000:773.339 JLINK_IsHalted()
TA260 000:773.809 - 0.469ms returns FALSE
TA260 000:773.818 JLINK_HasError()
TA260 000:775.846 JLINK_IsHalted()
TA260 000:776.346 - 0.500ms returns FALSE
TA260 000:776.354 JLINK_HasError()
TA260 000:777.845 JLINK_IsHalted()
TA260 000:778.366 - 0.521ms returns FALSE
TA260 000:778.376 JLINK_HasError()
TA260 000:779.844 JLINK_IsHalted()
TA260 000:780.297 - 0.452ms returns FALSE
TA260 000:780.308 JLINK_HasError()
TA260 000:781.846 JLINK_IsHalted()
TA260 000:782.282 - 0.435ms returns FALSE
TA260 000:782.291 JLINK_HasError()
TA260 000:783.346 JLINK_IsHalted()
TA260 000:783.850 - 0.503ms returns FALSE
TA260 000:783.857 JLINK_HasError()
TA260 000:785.354 JLINK_IsHalted()
TA260 000:785.858 - 0.503ms returns FALSE
TA260 000:785.868 JLINK_HasError()
TA260 000:787.358 JLINK_IsHalted()
TA260 000:787.844 - 0.486ms returns FALSE
TA260 000:787.857 JLINK_HasError()
TA260 000:789.351 JLINK_IsHalted()
TA260 000:789.805 - 0.453ms returns FALSE
TA260 000:789.815 JLINK_HasError()
TA260 000:791.350 JLINK_IsHalted()
TA260 000:791.847 - 0.497ms returns FALSE
TA260 000:791.855 JLINK_HasError()
TA260 000:793.354 JLINK_IsHalted()
TA260 000:793.750 - 0.396ms returns FALSE
TA260 000:793.756 JLINK_HasError()
TA260 000:794.853 JLINK_IsHalted()
TA260 000:795.330 - 0.476ms returns FALSE
TA260 000:795.336 JLINK_HasError()
TA260 000:796.860 JLINK_IsHalted()
TA260 000:797.291 - 0.431ms returns FALSE
TA260 000:797.301 JLINK_HasError()
TA260 000:798.859 JLINK_IsHalted()
TA260 000:799.330 - 0.471ms returns FALSE
TA260 000:799.337 JLINK_HasError()
TA260 000:800.856 JLINK_IsHalted()
TA260 000:801.365 - 0.508ms returns FALSE
TA260 000:801.371 JLINK_HasError()
TA260 000:803.378 JLINK_IsHalted()
TA260 000:803.855 - 0.477ms returns FALSE
TA260 000:803.863 JLINK_HasError()
TA260 000:805.368 JLINK_IsHalted()
TA260 000:805.887 - 0.518ms returns FALSE
TA260 000:805.895 JLINK_HasError()
TA260 000:807.368 JLINK_IsHalted()
TA260 000:807.809 - 0.440ms returns FALSE
TA260 000:807.820 JLINK_HasError()
TA260 000:809.366 JLINK_IsHalted()
TA260 000:809.841 - 0.474ms returns FALSE
TA260 000:809.848 JLINK_HasError()
TA260 000:811.369 JLINK_IsHalted()
TA260 000:811.852 - 0.482ms returns FALSE
TA260 000:811.858 JLINK_HasError()
TA260 000:813.365 JLINK_IsHalted()
TA260 000:813.851 - 0.486ms returns FALSE
TA260 000:813.858 JLINK_HasError()
TA260 000:815.879 JLINK_IsHalted()
TA260 000:816.371 - 0.491ms returns FALSE
TA260 000:816.378 JLINK_HasError()
TA260 000:817.873 JLINK_IsHalted()
TA260 000:818.310 - 0.437ms returns FALSE
TA260 000:818.316 JLINK_HasError()
TA260 000:819.880 JLINK_IsHalted()
TA260 000:820.346 - 0.465ms returns FALSE
TA260 000:820.355 JLINK_HasError()
TA260 000:825.398 JLINK_IsHalted()
TA260 000:825.907 - 0.508ms returns FALSE
TA260 000:825.917 JLINK_HasError()
TA260 000:827.100 JLINK_IsHalted()
TA260 000:827.606 - 0.505ms returns FALSE
TA260 000:827.618 JLINK_HasError()
TA260 000:829.373 JLINK_IsHalted()
TA260 000:829.855 - 0.482ms returns FALSE
TA260 000:829.866 JLINK_HasError()
TA260 000:831.348 JLINK_IsHalted()
TA260 000:831.789 - 0.440ms returns FALSE
TA260 000:831.799 JLINK_HasError()
TA260 000:833.348 JLINK_IsHalted()
TA260 000:833.805 - 0.456ms returns FALSE
TA260 000:833.812 JLINK_HasError()
TA260 000:834.854 JLINK_IsHalted()
TA260 000:835.477 - 0.623ms returns FALSE
TA260 000:835.488 JLINK_HasError()
TA260 000:836.865 JLINK_IsHalted()
TA260 000:837.333 - 0.467ms returns FALSE
TA260 000:837.341 JLINK_HasError()
TA260 000:838.857 JLINK_IsHalted()
TA260 000:839.343 - 0.486ms returns FALSE
TA260 000:839.349 JLINK_HasError()
TA260 000:840.854 JLINK_IsHalted()
TA260 000:841.320 - 0.465ms returns FALSE
TA260 000:841.327 JLINK_HasError()
TA260 000:843.362 JLINK_IsHalted()
TA260 000:843.895 - 0.533ms returns FALSE
TA260 000:843.902 JLINK_HasError()
TA260 000:845.364 JLINK_IsHalted()
TA260 000:845.832 - 0.467ms returns FALSE
TA260 000:845.840 JLINK_HasError()
TA260 000:847.378 JLINK_IsHalted()
TA260 000:847.842 - 0.463ms returns FALSE
TA260 000:847.852 JLINK_HasError()
TA260 000:849.364 JLINK_IsHalted()
TA260 000:849.852 - 0.487ms returns FALSE
TA260 000:849.858 JLINK_HasError()
TA260 000:851.365 JLINK_IsHalted()
TA260 000:851.888 - 0.523ms returns FALSE
TA260 000:851.896 JLINK_HasError()
TA260 000:853.366 JLINK_IsHalted()
TA260 000:853.806 - 0.440ms returns FALSE
TA260 000:853.814 JLINK_HasError()
TA260 000:854.872 JLINK_IsHalted()
TA260 000:855.374 - 0.502ms returns FALSE
TA260 000:855.387 JLINK_HasError()
TA260 000:856.876 JLINK_IsHalted()
TA260 000:857.344 - 0.468ms returns FALSE
TA260 000:857.352 JLINK_HasError()
TA260 000:858.875 JLINK_IsHalted()
TA260 000:859.376 - 0.501ms returns FALSE
TA260 000:859.385 JLINK_HasError()
TA260 000:860.871 JLINK_IsHalted()
TA260 000:861.365 - 0.493ms returns FALSE
TA260 000:861.371 JLINK_HasError()
TA260 000:863.376 JLINK_IsHalted()
TA260 000:863.852 - 0.475ms returns FALSE
TA260 000:863.858 JLINK_HasError()
TA260 000:865.386 JLINK_IsHalted()
TA260 000:865.899 - 0.512ms returns FALSE
TA260 000:865.906 JLINK_HasError()
TA260 000:867.385 JLINK_IsHalted()
TA260 000:867.855 - 0.469ms returns FALSE
TA260 000:867.867 JLINK_HasError()
TA260 000:869.373 JLINK_IsHalted()
TA260 000:869.891 - 0.517ms returns FALSE
TA260 000:869.899 JLINK_HasError()
TA260 000:871.878 JLINK_IsHalted()
TA260 000:872.352 - 0.473ms returns FALSE
TA260 000:872.358 JLINK_HasError()
TA260 000:874.383 JLINK_IsHalted()
TA260 000:874.843 - 0.460ms returns FALSE
TA260 000:874.849 JLINK_HasError()
TA260 000:876.384 JLINK_IsHalted()
TA260 000:876.854 - 0.469ms returns FALSE
TA260 000:876.863 JLINK_HasError()
TA260 000:878.383 JLINK_IsHalted()
TA260 000:878.807 - 0.424ms returns FALSE
TA260 000:878.815 JLINK_HasError()
TA260 000:880.396 JLINK_IsHalted()
TA260 000:880.866 - 0.469ms returns FALSE
TA260 000:880.872 JLINK_HasError()
TA260 000:882.388 JLINK_IsHalted()
TA260 000:882.899 - 0.510ms returns FALSE
TA260 000:882.910 JLINK_HasError()
TA260 000:884.891 JLINK_IsHalted()
TA260 000:885.338 - 0.445ms returns FALSE
TA260 000:885.348 JLINK_HasError()
TA260 000:886.898 JLINK_IsHalted()
TA260 000:887.476 - 0.578ms returns FALSE
TA260 000:887.484 JLINK_HasError()
TA260 000:888.893 JLINK_IsHalted()
TA260 000:889.398 - 0.505ms returns FALSE
TA260 000:889.405 JLINK_HasError()
TA260 000:890.894 JLINK_IsHalted()
TA260 000:891.331 - 0.437ms returns FALSE
TA260 000:891.337 JLINK_HasError()
TA260 000:893.398 JLINK_IsHalted()
TA260 000:893.883 - 0.484ms returns FALSE
TA260 000:893.888 JLINK_HasError()
TA260 000:895.403 JLINK_IsHalted()
TA260 000:895.863 - 0.460ms returns FALSE
TA260 000:895.872 JLINK_HasError()
TA260 000:897.407 JLINK_IsHalted()
TA260 000:897.889 - 0.481ms returns FALSE
TA260 000:897.899 JLINK_HasError()
TA260 000:899.401 JLINK_IsHalted()
TA260 000:899.887 - 0.485ms returns FALSE
TA260 000:899.893 JLINK_HasError()
TA260 000:901.400 JLINK_IsHalted()
TA260 000:901.868 - 0.468ms returns FALSE
TA260 000:901.877 JLINK_HasError()
TA260 000:903.408 JLINK_IsHalted()
TA260 000:903.944 - 0.536ms returns FALSE
TA260 000:903.952 JLINK_HasError()
TA260 000:905.910 JLINK_IsHalted()
TA260 000:906.378 - 0.467ms returns FALSE
TA260 000:906.389 JLINK_HasError()
TA260 000:907.912 JLINK_IsHalted()
TA260 000:908.328 - 0.416ms returns FALSE
TA260 000:908.335 JLINK_HasError()
TA260 000:909.908 JLINK_IsHalted()
TA260 000:910.386 - 0.477ms returns FALSE
TA260 000:910.393 JLINK_HasError()
TA260 000:911.916 JLINK_IsHalted()
TA260 000:912.376 - 0.460ms returns FALSE
TA260 000:912.383 JLINK_HasError()
TA260 000:914.422 JLINK_IsHalted()
TA260 000:914.882 - 0.460ms returns FALSE
TA260 000:914.892 JLINK_HasError()
TA260 000:916.416 JLINK_IsHalted()
TA260 000:916.832 - 0.416ms returns FALSE
TA260 000:916.847 JLINK_HasError()
TA260 000:918.418 JLINK_IsHalted()
TA260 000:918.939 - 0.520ms returns FALSE
TA260 000:918.950 JLINK_HasError()
TA260 000:920.422 JLINK_IsHalted()
TA260 000:920.853 - 0.431ms returns FALSE
TA260 000:920.861 JLINK_HasError()
TA260 000:922.418 JLINK_IsHalted()
TA260 000:922.903 - 0.484ms returns FALSE
TA260 000:922.916 JLINK_HasError()
TA260 000:924.932 JLINK_IsHalted()
TA260 000:925.478 - 0.546ms returns FALSE
TA260 000:925.486 JLINK_HasError()
TA260 000:926.931 JLINK_IsHalted()
TA260 000:927.399 - 0.468ms returns FALSE
TA260 000:927.407 JLINK_HasError()
TA260 000:928.930 JLINK_IsHalted()
TA260 000:929.379 - 0.449ms returns FALSE
TA260 000:929.390 JLINK_HasError()
TA260 000:930.929 JLINK_IsHalted()
TA260 000:931.470 - 0.540ms returns FALSE
TA260 000:931.482 JLINK_HasError()
TA260 000:933.428 JLINK_IsHalted()
TA260 000:933.936 - 0.508ms returns FALSE
TA260 000:933.947 JLINK_HasError()
TA260 000:935.437 JLINK_IsHalted()
TA260 000:935.968 - 0.530ms returns FALSE
TA260 000:935.977 JLINK_HasError()
TA260 000:937.436 JLINK_IsHalted()
TA260 000:937.876 - 0.440ms returns FALSE
TA260 000:937.886 JLINK_HasError()
TA260 000:939.430 JLINK_IsHalted()
TA260 000:939.911 - 0.480ms returns FALSE
TA260 000:939.921 JLINK_HasError()
TA260 000:941.435 JLINK_IsHalted()
TA260 000:941.898 - 0.463ms returns FALSE
TA260 000:941.906 JLINK_HasError()
TA260 000:943.937 JLINK_IsHalted()
TA260 000:944.387 - 0.449ms returns FALSE
TA260 000:944.396 JLINK_HasError()
TA260 000:945.941 JLINK_IsHalted()
TA260 000:946.386 - 0.444ms returns FALSE
TA260 000:946.394 JLINK_HasError()
TA260 000:947.940 JLINK_IsHalted()
TA260 000:948.475 - 0.534ms returns FALSE
TA260 000:948.482 JLINK_HasError()
TA260 000:949.937 JLINK_IsHalted()
TA260 000:950.473 - 0.536ms returns FALSE
TA260 000:950.482 JLINK_HasError()
TA260 000:951.947 JLINK_IsHalted()
TA260 000:952.474 - 0.527ms returns FALSE
TA260 000:952.482 JLINK_HasError()
TA260 000:954.953 JLINK_IsHalted()
TA260 000:955.468 - 0.514ms returns FALSE
TA260 000:955.480 JLINK_HasError()
TA260 000:956.958 JLINK_IsHalted()
TA260 000:957.465 - 0.507ms returns FALSE
TA260 000:957.474 JLINK_HasError()
TA260 000:958.957 JLINK_IsHalted()
TA260 000:959.477 - 0.520ms returns FALSE
TA260 000:959.489 JLINK_HasError()
TA260 000:960.950 JLINK_IsHalted()
TA260 000:961.467 - 0.517ms returns FALSE
TA260 000:961.478 JLINK_HasError()
TA260 000:962.956 JLINK_IsHalted()
TA260 000:963.474 - 0.518ms returns FALSE
TA260 000:963.484 JLINK_HasError()
TA260 000:965.461 JLINK_IsHalted()
TA260 000:965.965 - 0.503ms returns FALSE
TA260 000:965.975 JLINK_HasError()
TA260 000:967.461 JLINK_IsHalted()
TA260 000:967.941 - 0.480ms returns FALSE
TA260 000:967.950 JLINK_HasError()
TA260 000:969.462 JLINK_IsHalted()
TA260 000:969.965 - 0.502ms returns FALSE
TA260 000:969.973 JLINK_HasError()
TA260 000:971.458 JLINK_IsHalted()
TA260 000:971.944 - 0.485ms returns FALSE
TA260 000:971.950 JLINK_HasError()
TA260 000:973.964 JLINK_IsHalted()
TA260 000:974.474 - 0.509ms returns FALSE
TA260 000:974.482 JLINK_HasError()
TA260 000:975.966 JLINK_IsHalted()
TA260 000:976.473 - 0.507ms returns FALSE
TA260 000:976.480 JLINK_HasError()
TA260 000:977.963 JLINK_IsHalted()
TA260 000:978.475 - 0.512ms returns FALSE
TA260 000:978.483 JLINK_HasError()
TA260 000:979.970 JLINK_IsHalted()
TA260 000:980.478 - 0.507ms returns FALSE
TA260 000:980.493 JLINK_HasError()
TA260 000:981.968 JLINK_IsHalted()
TA260 000:982.477 - 0.508ms returns FALSE
TA260 000:982.488 JLINK_HasError()
TA260 000:984.471 JLINK_IsHalted()
TA260 000:984.967 - 0.495ms returns FALSE
TA260 000:984.978 JLINK_HasError()
TA260 000:986.474 JLINK_IsHalted()
TA260 000:986.971 - 0.497ms returns FALSE
TA260 000:986.981 JLINK_HasError()
TA260 000:988.474 JLINK_IsHalted()
TA260 000:988.987 - 0.512ms returns FALSE
TA260 000:988.994 JLINK_HasError()
TA260 000:990.477 JLINK_IsHalted()
TA260 000:990.979 - 0.502ms returns FALSE
TA260 000:990.988 JLINK_HasError()
TA260 000:992.981 JLINK_IsHalted()
TA260 000:993.503 - 0.521ms returns FALSE
TA260 000:993.511 JLINK_HasError()
TA260 000:994.974 JLINK_IsHalted()
TA260 000:995.475 - 0.500ms returns FALSE
TA260 000:995.481 JLINK_HasError()
TA260 000:996.981 JLINK_IsHalted()
TA260 000:997.478 - 0.496ms returns FALSE
TA260 000:997.489 JLINK_HasError()
TA260 000:998.974 JLINK_IsHalted()
TA260 000:999.475 - 0.501ms returns FALSE
TA260 000:999.481 JLINK_HasError()
TA260 001:000.981 JLINK_IsHalted()
TA260 001:001.521 - 0.540ms returns FALSE
TA260 001:001.532 JLINK_HasError()
TA260 001:003.027 JLINK_IsHalted()
TA260 001:003.497 - 0.469ms returns FALSE
TA260 001:003.503 JLINK_HasError()
TA260 001:005.026 JLINK_IsHalted()
TA260 001:005.509 - 0.482ms returns FALSE
TA260 001:005.517 JLINK_HasError()
TA260 001:007.039 JLINK_IsHalted()
TA260 001:007.470 - 0.430ms returns FALSE
TA260 001:007.481 JLINK_HasError()
TA260 001:009.031 JLINK_IsHalted()
TA260 001:009.513 - 0.482ms returns FALSE
TA260 001:009.526 JLINK_HasError()
TA260 001:011.061 JLINK_IsHalted()
TA260 001:011.614 - 0.552ms returns FALSE
TA260 001:011.620 JLINK_HasError()
TA260 001:013.029 JLINK_IsHalted()
TA260 001:013.511 - 0.481ms returns FALSE
TA260 001:013.516 JLINK_HasError()
TA260 001:015.537 JLINK_IsHalted()
TA260 001:017.909   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:018.468 - 2.931ms returns TRUE
TA260 001:018.475 JLINK_ReadReg(R15 (PC))
TA260 001:018.481 - 0.005ms returns 0x20000000
TA260 001:018.485 JLINK_ClrBPEx(BPHandle = 0x00000003)
TA260 001:018.489 - 0.004ms returns 0x00
TA260 001:018.494 JLINK_ReadReg(R0)
TA260 001:018.498 - 0.004ms returns 0x00000000
TA260 001:018.866 JLINK_HasError()
TA260 001:018.875 JLINK_WriteReg(R0, 0x08004000)
TA260 001:018.880 - 0.004ms returns 0
TA260 001:018.885 JLINK_WriteReg(R1, 0x00004000)
TA260 001:018.888 - 0.003ms returns 0
TA260 001:018.892 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:018.896 - 0.003ms returns 0
TA260 001:018.900 JLINK_WriteReg(R3, 0x00000000)
TA260 001:018.903 - 0.003ms returns 0
TA260 001:018.907 JLINK_WriteReg(R4, 0x00000000)
TA260 001:018.911 - 0.003ms returns 0
TA260 001:018.915 JLINK_WriteReg(R5, 0x00000000)
TA260 001:018.918 - 0.003ms returns 0
TA260 001:018.922 JLINK_WriteReg(R6, 0x00000000)
TA260 001:018.925 - 0.003ms returns 0
TA260 001:018.929 JLINK_WriteReg(R7, 0x00000000)
TA260 001:018.933 - 0.003ms returns 0
TA260 001:018.937 JLINK_WriteReg(R8, 0x00000000)
TA260 001:018.941 - 0.003ms returns 0
TA260 001:018.945 JLINK_WriteReg(R9, 0x20000180)
TA260 001:018.948 - 0.003ms returns 0
TA260 001:018.952 JLINK_WriteReg(R10, 0x00000000)
TA260 001:018.956 - 0.003ms returns 0
TA260 001:018.960 JLINK_WriteReg(R11, 0x00000000)
TA260 001:018.963 - 0.003ms returns 0
TA260 001:018.967 JLINK_WriteReg(R12, 0x00000000)
TA260 001:018.971 - 0.003ms returns 0
TA260 001:018.975 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:018.979 - 0.004ms returns 0
TA260 001:018.983 JLINK_WriteReg(R14, 0x20000001)
TA260 001:018.986 - 0.003ms returns 0
TA260 001:018.990 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 001:018.993 - 0.003ms returns 0
TA260 001:018.998 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:019.001 - 0.003ms returns 0
TA260 001:019.005 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:019.009 - 0.003ms returns 0
TA260 001:019.013 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:019.016 - 0.003ms returns 0
TA260 001:019.020 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:019.024 - 0.003ms returns 0
TA260 001:019.028 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:019.032 - 0.004ms returns 0x00000004
TA260 001:019.037 JLINK_Go()
TA260 001:019.046   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:021.895 - 2.858ms 
TA260 001:021.901 JLINK_IsHalted()
TA260 001:024.184   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:024.643 - 2.741ms returns TRUE
TA260 001:024.650 JLINK_ReadReg(R15 (PC))
TA260 001:024.655 - 0.005ms returns 0x20000000
TA260 001:024.659 JLINK_ClrBPEx(BPHandle = 0x00000004)
TA260 001:024.663 - 0.003ms returns 0x00
TA260 001:024.667 JLINK_ReadReg(R0)
TA260 001:024.671 - 0.003ms returns 0x00000001
TA260 001:024.675 JLINK_HasError()
TA260 001:024.680 JLINK_WriteReg(R0, 0x08004000)
TA260 001:024.684 - 0.003ms returns 0
TA260 001:024.688 JLINK_WriteReg(R1, 0x00004000)
TA260 001:024.691 - 0.003ms returns 0
TA260 001:024.695 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:024.699 - 0.003ms returns 0
TA260 001:024.703 JLINK_WriteReg(R3, 0x00000000)
TA260 001:024.706 - 0.003ms returns 0
TA260 001:024.710 JLINK_WriteReg(R4, 0x00000000)
TA260 001:024.713 - 0.003ms returns 0
TA260 001:024.717 JLINK_WriteReg(R5, 0x00000000)
TA260 001:024.721 - 0.003ms returns 0
TA260 001:024.725 JLINK_WriteReg(R6, 0x00000000)
TA260 001:024.728 - 0.003ms returns 0
TA260 001:024.732 JLINK_WriteReg(R7, 0x00000000)
TA260 001:024.738 - 0.005ms returns 0
TA260 001:024.744 JLINK_WriteReg(R8, 0x00000000)
TA260 001:024.747 - 0.003ms returns 0
TA260 001:024.751 JLINK_WriteReg(R9, 0x20000180)
TA260 001:024.755 - 0.003ms returns 0
TA260 001:024.759 JLINK_WriteReg(R10, 0x00000000)
TA260 001:024.762 - 0.003ms returns 0
TA260 001:024.766 JLINK_WriteReg(R11, 0x00000000)
TA260 001:024.770 - 0.003ms returns 0
TA260 001:024.774 JLINK_WriteReg(R12, 0x00000000)
TA260 001:024.777 - 0.003ms returns 0
TA260 001:024.781 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:024.785 - 0.003ms returns 0
TA260 001:024.789 JLINK_WriteReg(R14, 0x20000001)
TA260 001:024.792 - 0.003ms returns 0
TA260 001:024.796 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 001:024.800 - 0.003ms returns 0
TA260 001:024.804 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:024.807 - 0.003ms returns 0
TA260 001:024.811 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:024.814 - 0.003ms returns 0
TA260 001:024.819 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:024.822 - 0.003ms returns 0
TA260 001:024.826 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:024.829 - 0.003ms returns 0
TA260 001:024.834 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:024.837 - 0.003ms returns 0x00000005
TA260 001:024.841 JLINK_Go()
TA260 001:024.849   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:027.703 - 2.861ms 
TA260 001:027.716 JLINK_IsHalted()
TA260 001:028.235 - 0.518ms returns FALSE
TA260 001:028.241 JLINK_HasError()
TA260 001:030.040 JLINK_IsHalted()
TA260 001:030.542 - 0.501ms returns FALSE
TA260 001:030.548 JLINK_HasError()
TA260 001:032.048 JLINK_IsHalted()
TA260 001:032.611 - 0.563ms returns FALSE
TA260 001:032.618 JLINK_HasError()
TA260 001:034.547 JLINK_IsHalted()
TA260 001:035.037 - 0.490ms returns FALSE
TA260 001:035.044 JLINK_HasError()
TA260 001:036.549 JLINK_IsHalted()
TA260 001:037.186 - 0.636ms returns FALSE
TA260 001:037.204 JLINK_HasError()
TA260 001:038.549 JLINK_IsHalted()
TA260 001:038.977 - 0.428ms returns FALSE
TA260 001:038.985 JLINK_HasError()
TA260 001:040.549 JLINK_IsHalted()
TA260 001:041.042 - 0.491ms returns FALSE
TA260 001:041.058 JLINK_HasError()
TA260 001:043.198 JLINK_IsHalted()
TA260 001:043.706 - 0.507ms returns FALSE
TA260 001:043.714 JLINK_HasError()
TA260 001:045.196 JLINK_IsHalted()
TA260 001:045.691 - 0.494ms returns FALSE
TA260 001:045.697 JLINK_HasError()
TA260 001:047.199 JLINK_IsHalted()
TA260 001:047.708 - 0.509ms returns FALSE
TA260 001:047.718 JLINK_HasError()
TA260 001:049.198 JLINK_IsHalted()
TA260 001:049.726 - 0.527ms returns FALSE
TA260 001:049.732 JLINK_HasError()
TA260 001:051.196 JLINK_IsHalted()
TA260 001:051.656 - 0.460ms returns FALSE
TA260 001:051.662 JLINK_HasError()
TA260 001:053.197 JLINK_IsHalted()
TA260 001:053.713 - 0.515ms returns FALSE
TA260 001:053.730 JLINK_HasError()
TA260 001:056.207 JLINK_IsHalted()
TA260 001:056.698 - 0.490ms returns FALSE
TA260 001:056.712 JLINK_HasError()
TA260 001:058.203 JLINK_IsHalted()
TA260 001:058.692 - 0.488ms returns FALSE
TA260 001:058.698 JLINK_HasError()
TA260 001:060.200 JLINK_IsHalted()
TA260 001:060.690 - 0.489ms returns FALSE
TA260 001:060.695 JLINK_HasError()
TA260 001:062.204 JLINK_IsHalted()
TA260 001:062.704 - 0.499ms returns FALSE
TA260 001:062.714 JLINK_HasError()
TA260 001:064.709 JLINK_IsHalted()
TA260 001:065.236 - 0.527ms returns FALSE
TA260 001:065.243 JLINK_HasError()
TA260 001:066.709 JLINK_IsHalted()
TA260 001:067.261 - 0.551ms returns FALSE
TA260 001:067.270 JLINK_HasError()
TA260 001:068.708 JLINK_IsHalted()
TA260 001:069.244 - 0.535ms returns FALSE
TA260 001:069.257 JLINK_HasError()
TA260 001:070.710 JLINK_IsHalted()
TA260 001:071.146 - 0.435ms returns FALSE
TA260 001:071.152 JLINK_HasError()
TA260 001:073.211 JLINK_IsHalted()
TA260 001:073.702 - 0.491ms returns FALSE
TA260 001:073.709 JLINK_HasError()
TA260 001:075.213 JLINK_IsHalted()
TA260 001:075.703 - 0.489ms returns FALSE
TA260 001:075.709 JLINK_HasError()
TA260 001:077.214 JLINK_IsHalted()
TA260 001:077.699 - 0.483ms returns FALSE
TA260 001:077.713 JLINK_HasError()
TA260 001:079.455 JLINK_IsHalted()
TA260 001:079.931 - 0.475ms returns FALSE
TA260 001:079.937 JLINK_HasError()
TA260 001:081.456 JLINK_IsHalted()
TA260 001:081.913 - 0.456ms returns FALSE
TA260 001:081.919 JLINK_HasError()
TA260 001:082.958 JLINK_IsHalted()
TA260 001:083.462 - 0.503ms returns FALSE
TA260 001:083.467 JLINK_HasError()
TA260 001:084.961 JLINK_IsHalted()
TA260 001:085.473 - 0.511ms returns FALSE
TA260 001:085.479 JLINK_HasError()
TA260 001:086.966 JLINK_IsHalted()
TA260 001:087.476 - 0.510ms returns FALSE
TA260 001:087.489 JLINK_HasError()
TA260 001:088.962 JLINK_IsHalted()
TA260 001:089.464 - 0.501ms returns FALSE
TA260 001:089.470 JLINK_HasError()
TA260 001:090.962 JLINK_IsHalted()
TA260 001:091.462 - 0.500ms returns FALSE
TA260 001:091.468 JLINK_HasError()
TA260 001:092.962 JLINK_IsHalted()
TA260 001:093.471 - 0.508ms returns FALSE
TA260 001:093.477 JLINK_HasError()
TA260 001:095.467 JLINK_IsHalted()
TA260 001:095.985 - 0.518ms returns FALSE
TA260 001:095.992 JLINK_HasError()
TA260 001:097.113 JLINK_IsHalted()
TA260 001:097.600 - 0.486ms returns FALSE
TA260 001:097.611 JLINK_HasError()
TA260 001:099.108 JLINK_IsHalted()
TA260 001:099.620 - 0.511ms returns FALSE
TA260 001:099.631 JLINK_HasError()
TA260 001:101.108 JLINK_IsHalted()
TA260 001:101.612 - 0.503ms returns FALSE
TA260 001:101.618 JLINK_HasError()
TA260 001:103.108 JLINK_IsHalted()
TA260 001:103.613 - 0.505ms returns FALSE
TA260 001:103.620 JLINK_HasError()
TA260 001:105.611 JLINK_IsHalted()
TA260 001:106.112 - 0.501ms returns FALSE
TA260 001:106.119 JLINK_HasError()
TA260 001:107.616 JLINK_IsHalted()
TA260 001:108.091 - 0.474ms returns FALSE
TA260 001:108.104 JLINK_HasError()
TA260 001:109.611 JLINK_IsHalted()
TA260 001:110.111 - 0.499ms returns FALSE
TA260 001:110.117 JLINK_HasError()
TA260 001:111.612 JLINK_IsHalted()
TA260 001:112.110 - 0.497ms returns FALSE
TA260 001:112.115 JLINK_HasError()
TA260 001:114.117 JLINK_IsHalted()
TA260 001:114.594 - 0.476ms returns FALSE
TA260 001:114.601 JLINK_HasError()
TA260 001:116.120 JLINK_IsHalted()
TA260 001:116.648 - 0.527ms returns FALSE
TA260 001:116.656 JLINK_HasError()
TA260 001:118.117 JLINK_IsHalted()
TA260 001:118.599 - 0.481ms returns FALSE
TA260 001:118.604 JLINK_HasError()
TA260 001:120.119 JLINK_IsHalted()
TA260 001:120.614 - 0.494ms returns FALSE
TA260 001:120.622 JLINK_HasError()
TA260 001:122.120 JLINK_IsHalted()
TA260 001:122.611 - 0.490ms returns FALSE
TA260 001:122.617 JLINK_HasError()
TA260 001:124.623 JLINK_IsHalted()
TA260 001:125.103 - 0.479ms returns FALSE
TA260 001:125.111 JLINK_HasError()
TA260 001:126.624 JLINK_IsHalted()
TA260 001:127.073 - 0.448ms returns FALSE
TA260 001:127.209 JLINK_HasError()
TA260 001:128.626 JLINK_IsHalted()
TA260 001:129.134 - 0.508ms returns FALSE
TA260 001:129.141 JLINK_HasError()
TA260 001:130.623 JLINK_IsHalted()
TA260 001:131.208 - 0.585ms returns FALSE
TA260 001:131.221 JLINK_HasError()
TA260 001:133.129 JLINK_IsHalted()
TA260 001:133.613 - 0.483ms returns FALSE
TA260 001:133.618 JLINK_HasError()
TA260 001:135.129 JLINK_IsHalted()
TA260 001:135.598 - 0.469ms returns FALSE
TA260 001:135.604 JLINK_HasError()
TA260 001:137.133 JLINK_IsHalted()
TA260 001:137.621 - 0.488ms returns FALSE
TA260 001:137.631 JLINK_HasError()
TA260 001:139.172 JLINK_IsHalted()
TA260 001:139.692 - 0.519ms returns FALSE
TA260 001:139.698 JLINK_HasError()
TA260 001:141.174 JLINK_IsHalted()
TA260 001:141.614 - 0.439ms returns FALSE
TA260 001:141.620 JLINK_HasError()
TA260 001:143.173 JLINK_IsHalted()
TA260 001:143.656 - 0.482ms returns FALSE
TA260 001:143.662 JLINK_HasError()
TA260 001:145.682 JLINK_IsHalted()
TA260 001:146.158 - 0.475ms returns FALSE
TA260 001:146.166 JLINK_HasError()
TA260 001:147.681 JLINK_IsHalted()
TA260 001:148.117 - 0.435ms returns FALSE
TA260 001:148.130 JLINK_HasError()
TA260 001:150.678 JLINK_IsHalted()
TA260 001:151.215 - 0.536ms returns FALSE
TA260 001:151.221 JLINK_HasError()
TA260 001:153.183 JLINK_IsHalted()
TA260 001:153.648 - 0.464ms returns FALSE
TA260 001:153.656 JLINK_HasError()
TA260 001:155.184 JLINK_IsHalted()
TA260 001:155.644 - 0.459ms returns FALSE
TA260 001:155.650 JLINK_HasError()
TA260 001:156.689 JLINK_IsHalted()
TA260 001:157.150 - 0.460ms returns FALSE
TA260 001:157.162 JLINK_HasError()
TA260 001:158.688 JLINK_IsHalted()
TA260 001:159.203 - 0.514ms returns FALSE
TA260 001:159.209 JLINK_HasError()
TA260 001:160.689 JLINK_IsHalted()
TA260 001:161.166 - 0.476ms returns FALSE
TA260 001:161.172 JLINK_HasError()
TA260 001:163.200 JLINK_IsHalted()
TA260 001:163.700 - 0.499ms returns FALSE
TA260 001:163.707 JLINK_HasError()
TA260 001:166.351 JLINK_IsHalted()
TA260 001:167.392 - 1.040ms returns FALSE
TA260 001:167.406 JLINK_HasError()
TA260 001:169.196 JLINK_IsHalted()
TA260 001:169.707 - 0.511ms returns FALSE
TA260 001:169.714 JLINK_HasError()
TA260 001:171.193 JLINK_IsHalted()
TA260 001:171.705 - 0.511ms returns FALSE
TA260 001:171.718 JLINK_HasError()
TA260 001:173.198 JLINK_IsHalted()
TA260 001:173.695 - 0.496ms returns FALSE
TA260 001:173.703 JLINK_HasError()
TA260 001:175.701 JLINK_IsHalted()
TA260 001:176.207 - 0.506ms returns FALSE
TA260 001:176.217 JLINK_HasError()
TA260 001:177.706 JLINK_IsHalted()
TA260 001:178.287 - 0.580ms returns FALSE
TA260 001:178.299 JLINK_HasError()
TA260 001:179.704 JLINK_IsHalted()
TA260 001:180.162 - 0.457ms returns FALSE
TA260 001:180.170 JLINK_HasError()
TA260 001:181.704 JLINK_IsHalted()
TA260 001:182.217 - 0.512ms returns FALSE
TA260 001:182.225 JLINK_HasError()
TA260 001:184.204 JLINK_IsHalted()
TA260 001:184.706 - 0.501ms returns FALSE
TA260 001:184.718 JLINK_HasError()
TA260 001:186.205 JLINK_IsHalted()
TA260 001:186.707 - 0.501ms returns FALSE
TA260 001:186.715 JLINK_HasError()
TA260 001:188.212 JLINK_IsHalted()
TA260 001:188.707 - 0.494ms returns FALSE
TA260 001:188.715 JLINK_HasError()
TA260 001:190.207 JLINK_IsHalted()
TA260 001:190.659 - 0.451ms returns FALSE
TA260 001:190.665 JLINK_HasError()
TA260 001:192.206 JLINK_IsHalted()
TA260 001:192.656 - 0.449ms returns FALSE
TA260 001:192.662 JLINK_HasError()
TA260 001:193.713 JLINK_IsHalted()
TA260 001:194.240 - 0.527ms returns FALSE
TA260 001:194.255 JLINK_HasError()
TA260 001:195.719 JLINK_IsHalted()
TA260 001:196.206 - 0.486ms returns FALSE
TA260 001:196.214 JLINK_HasError()
TA260 001:197.718 JLINK_IsHalted()
TA260 001:198.267 - 0.548ms returns FALSE
TA260 001:198.277 JLINK_HasError()
TA260 001:199.717 JLINK_IsHalted()
TA260 001:200.214 - 0.497ms returns FALSE
TA260 001:200.221 JLINK_HasError()
TA260 001:201.717 JLINK_IsHalted()
TA260 001:202.217 - 0.500ms returns FALSE
TA260 001:202.224 JLINK_HasError()
TA260 001:204.224 JLINK_IsHalted()
TA260 001:204.704 - 0.479ms returns FALSE
TA260 001:204.711 JLINK_HasError()
TA260 001:206.226 JLINK_IsHalted()
TA260 001:206.708 - 0.481ms returns FALSE
TA260 001:206.717 JLINK_HasError()
TA260 001:208.227 JLINK_IsHalted()
TA260 001:208.729 - 0.501ms returns FALSE
TA260 001:208.735 JLINK_HasError()
TA260 001:210.226 JLINK_IsHalted()
TA260 001:210.746 - 0.520ms returns FALSE
TA260 001:210.752 JLINK_HasError()
TA260 001:212.228 JLINK_IsHalted()
TA260 001:212.708 - 0.479ms returns FALSE
TA260 001:212.714 JLINK_HasError()
TA260 001:214.741 JLINK_IsHalted()
TA260 001:215.183 - 0.442ms returns FALSE
TA260 001:215.197 JLINK_HasError()
TA260 001:216.742 JLINK_IsHalted()
TA260 001:217.201 - 0.458ms returns FALSE
TA260 001:217.219 JLINK_HasError()
TA260 001:218.737 JLINK_IsHalted()
TA260 001:219.262 - 0.524ms returns FALSE
TA260 001:219.268 JLINK_HasError()
TA260 001:220.733 JLINK_IsHalted()
TA260 001:221.235 - 0.502ms returns FALSE
TA260 001:221.241 JLINK_HasError()
TA260 001:223.245 JLINK_IsHalted()
TA260 001:223.709 - 0.463ms returns FALSE
TA260 001:223.715 JLINK_HasError()
TA260 001:225.292 JLINK_IsHalted()
TA260 001:225.787 - 0.494ms returns FALSE
TA260 001:225.793 JLINK_HasError()
TA260 001:227.147 JLINK_IsHalted()
TA260 001:227.652 - 0.505ms returns FALSE
TA260 001:227.666 JLINK_HasError()
TA260 001:229.166 JLINK_IsHalted()
TA260 001:229.646 - 0.479ms returns FALSE
TA260 001:229.652 JLINK_HasError()
TA260 001:231.163 JLINK_IsHalted()
TA260 001:231.634 - 0.471ms returns FALSE
TA260 001:231.639 JLINK_HasError()
TA260 001:233.167 JLINK_IsHalted()
TA260 001:233.646 - 0.479ms returns FALSE
TA260 001:233.653 JLINK_HasError()
TA260 001:235.672 JLINK_IsHalted()
TA260 001:236.227 - 0.554ms returns FALSE
TA260 001:236.236 JLINK_HasError()
TA260 001:237.673 JLINK_IsHalted()
TA260 001:238.118 - 0.444ms returns FALSE
TA260 001:238.129 JLINK_HasError()
TA260 001:239.668 JLINK_IsHalted()
TA260 001:240.197 - 0.528ms returns FALSE
TA260 001:242.085 JLINK_HasError()
TA260 001:243.175 JLINK_IsHalted()
TA260 001:243.643 - 0.468ms returns FALSE
TA260 001:243.649 JLINK_HasError()
TA260 001:245.176 JLINK_IsHalted()
TA260 001:245.643 - 0.467ms returns FALSE
TA260 001:245.650 JLINK_HasError()
TA260 001:247.179 JLINK_IsHalted()
TA260 001:247.708 - 0.528ms returns FALSE
TA260 001:247.716 JLINK_HasError()
TA260 001:249.179 JLINK_IsHalted()
TA260 001:249.656 - 0.477ms returns FALSE
TA260 001:249.662 JLINK_HasError()
TA260 001:251.182 JLINK_IsHalted()
TA260 001:251.692 - 0.510ms returns FALSE
TA260 001:251.698 JLINK_HasError()
TA260 001:253.177 JLINK_IsHalted()
TA260 001:253.692 - 0.514ms returns FALSE
TA260 001:253.698 JLINK_HasError()
TA260 001:255.181 JLINK_IsHalted()
TA260 001:255.651 - 0.469ms returns FALSE
TA260 001:255.659 JLINK_HasError()
TA260 001:257.186 JLINK_IsHalted()
TA260 001:257.651 - 0.465ms returns FALSE
TA260 001:257.664 JLINK_HasError()
TA260 001:259.186 JLINK_IsHalted()
TA260 001:259.694 - 0.508ms returns FALSE
TA260 001:259.700 JLINK_HasError()
TA260 001:261.182 JLINK_IsHalted()
TA260 001:261.690 - 0.508ms returns FALSE
TA260 001:261.696 JLINK_HasError()
TA260 001:263.183 JLINK_IsHalted()
TA260 001:263.657 - 0.474ms returns FALSE
TA260 001:263.663 JLINK_HasError()
TA260 001:265.688 JLINK_IsHalted()
TA260 001:266.158 - 0.469ms returns FALSE
TA260 001:266.165 JLINK_HasError()
TA260 001:267.688 JLINK_IsHalted()
TA260 001:268.192 - 0.504ms returns FALSE
TA260 001:268.202 JLINK_HasError()
TA260 001:269.686 JLINK_IsHalted()
TA260 001:270.166 - 0.480ms returns FALSE
TA260 001:270.172 JLINK_HasError()
TA260 001:271.691 JLINK_IsHalted()
TA260 001:272.192 - 0.500ms returns FALSE
TA260 001:272.200 JLINK_HasError()
TA260 001:274.192 JLINK_IsHalted()
TA260 001:274.692 - 0.500ms returns FALSE
TA260 001:274.698 JLINK_HasError()
TA260 001:276.193 JLINK_IsHalted()
TA260 001:276.664 - 0.470ms returns FALSE
TA260 001:276.676 JLINK_HasError()
TA260 001:278.196 JLINK_IsHalted()
TA260 001:278.635 - 0.439ms returns FALSE
TA260 001:278.642 JLINK_HasError()
TA260 001:280.195 JLINK_IsHalted()
TA260 001:280.709 - 0.513ms returns FALSE
TA260 001:280.715 JLINK_HasError()
TA260 001:282.195 JLINK_IsHalted()
TA260 001:282.658 - 0.463ms returns FALSE
TA260 001:282.667 JLINK_HasError()
TA260 001:284.698 JLINK_IsHalted()
TA260 001:285.180 - 0.481ms returns FALSE
TA260 001:285.187 JLINK_HasError()
TA260 001:286.700 JLINK_IsHalted()
TA260 001:287.290 - 0.589ms returns FALSE
TA260 001:287.300 JLINK_HasError()
TA260 001:288.699 JLINK_IsHalted()
TA260 001:289.234 - 0.534ms returns FALSE
TA260 001:289.240 JLINK_HasError()
TA260 001:290.699 JLINK_IsHalted()
TA260 001:291.193 - 0.493ms returns FALSE
TA260 001:291.199 JLINK_HasError()
TA260 001:293.205 JLINK_IsHalted()
TA260 001:293.656 - 0.450ms returns FALSE
TA260 001:293.662 JLINK_HasError()
TA260 001:295.205 JLINK_IsHalted()
TA260 001:295.655 - 0.450ms returns FALSE
TA260 001:295.661 JLINK_HasError()
TA260 001:297.207 JLINK_IsHalted()
TA260 001:297.706 - 0.498ms returns FALSE
TA260 001:297.714 JLINK_HasError()
TA260 001:299.205 JLINK_IsHalted()
TA260 001:299.704 - 0.498ms returns FALSE
TA260 001:299.710 JLINK_HasError()
TA260 001:301.204 JLINK_IsHalted()
TA260 001:301.702 - 0.497ms returns FALSE
TA260 001:301.707 JLINK_HasError()
TA260 001:303.214 JLINK_IsHalted()
TA260 001:303.691 - 0.477ms returns FALSE
TA260 001:303.700 JLINK_HasError()
TA260 001:305.708 JLINK_IsHalted()
TA260 001:306.203 - 0.494ms returns FALSE
TA260 001:306.211 JLINK_HasError()
TA260 001:307.714 JLINK_IsHalted()
TA260 001:308.199 - 0.484ms returns FALSE
TA260 001:308.208 JLINK_HasError()
TA260 001:309.710 JLINK_IsHalted()
TA260 001:310.160 - 0.450ms returns FALSE
TA260 001:310.167 JLINK_HasError()
TA260 001:311.709 JLINK_IsHalted()
TA260 001:312.162 - 0.453ms returns FALSE
TA260 001:312.168 JLINK_HasError()
TA260 001:313.213 JLINK_IsHalted()
TA260 001:313.735 - 0.521ms returns FALSE
TA260 001:313.741 JLINK_HasError()
TA260 001:315.215 JLINK_IsHalted()
TA260 001:315.702 - 0.487ms returns FALSE
TA260 001:315.708 JLINK_HasError()
TA260 001:317.217 JLINK_IsHalted()
TA260 001:317.705 - 0.488ms returns FALSE
TA260 001:317.718 JLINK_HasError()
TA260 001:319.216 JLINK_IsHalted()
TA260 001:319.692 - 0.475ms returns FALSE
TA260 001:319.698 JLINK_HasError()
TA260 001:323.219 JLINK_IsHalted()
TA260 001:323.708 - 0.489ms returns FALSE
TA260 001:323.721 JLINK_HasError()
TA260 001:325.722 JLINK_IsHalted()
TA260 001:326.162 - 0.440ms returns FALSE
TA260 001:326.168 JLINK_HasError()
TA260 001:327.723 JLINK_IsHalted()
TA260 001:328.284 - 0.561ms returns FALSE
TA260 001:328.292 JLINK_HasError()
TA260 001:329.720 JLINK_IsHalted()
TA260 001:330.270 - 0.549ms returns FALSE
TA260 001:330.276 JLINK_HasError()
TA260 001:331.720 JLINK_IsHalted()
TA260 001:332.192 - 0.471ms returns FALSE
TA260 001:332.197 JLINK_HasError()
TA260 001:334.225 JLINK_IsHalted()
TA260 001:334.852 - 0.627ms returns FALSE
TA260 001:334.862 JLINK_HasError()
TA260 001:336.341 JLINK_IsHalted()
TA260 001:337.474 - 1.132ms returns FALSE
TA260 001:337.487 JLINK_HasError()
TA260 001:339.226 JLINK_IsHalted()
TA260 001:339.704 - 0.477ms returns FALSE
TA260 001:339.710 JLINK_HasError()
TA260 001:341.227 JLINK_IsHalted()
TA260 001:341.737 - 0.510ms returns FALSE
TA260 001:341.750 JLINK_HasError()
TA260 001:343.233 JLINK_IsHalted()
TA260 001:343.704 - 0.470ms returns FALSE
TA260 001:343.709 JLINK_HasError()
TA260 001:345.731 JLINK_IsHalted()
TA260 001:346.256 - 0.524ms returns FALSE
TA260 001:346.268 JLINK_HasError()
TA260 001:347.732 JLINK_IsHalted()
TA260 001:348.235 - 0.503ms returns FALSE
TA260 001:348.241 JLINK_HasError()
TA260 001:349.730 JLINK_IsHalted()
TA260 001:350.243 - 0.513ms returns FALSE
TA260 001:350.253 JLINK_HasError()
TA260 001:351.733 JLINK_IsHalted()
TA260 001:352.238 - 0.504ms returns FALSE
TA260 001:352.244 JLINK_HasError()
TA260 001:354.233 JLINK_IsHalted()
TA260 001:354.745 - 0.511ms returns FALSE
TA260 001:354.751 JLINK_HasError()
TA260 001:356.746 JLINK_IsHalted()
TA260 001:357.261 - 0.514ms returns FALSE
TA260 001:357.267 JLINK_HasError()
TA260 001:358.738 JLINK_IsHalted()
TA260 001:359.236 - 0.497ms returns FALSE
TA260 001:359.242 JLINK_HasError()
TA260 001:360.737 JLINK_IsHalted()
TA260 001:361.236 - 0.498ms returns FALSE
TA260 001:361.242 JLINK_HasError()
TA260 001:363.249 JLINK_IsHalted()
TA260 001:363.721 - 0.472ms returns FALSE
TA260 001:363.727 JLINK_HasError()
TA260 001:365.243 JLINK_IsHalted()
TA260 001:365.822 - 0.577ms returns FALSE
TA260 001:365.834 JLINK_HasError()
TA260 001:367.277 JLINK_IsHalted()
TA260 001:367.783 - 0.505ms returns FALSE
TA260 001:367.789 JLINK_HasError()
TA260 001:369.243 JLINK_IsHalted()
TA260 001:369.720 - 0.476ms returns FALSE
TA260 001:369.725 JLINK_HasError()
TA260 001:371.243 JLINK_IsHalted()
TA260 001:371.713 - 0.469ms returns FALSE
TA260 001:371.718 JLINK_HasError()
TA260 001:373.252 JLINK_IsHalted()
TA260 001:374.086 - 0.833ms returns FALSE
TA260 001:374.107 JLINK_HasError()
TA260 001:375.750 JLINK_IsHalted()
TA260 001:376.286 - 0.535ms returns FALSE
TA260 001:376.297 JLINK_HasError()
TA260 001:377.753 JLINK_IsHalted()
TA260 001:378.261 - 0.507ms returns FALSE
TA260 001:378.273 JLINK_HasError()
TA260 001:380.751 JLINK_IsHalted()
TA260 001:381.366 - 0.614ms returns FALSE
TA260 001:381.377 JLINK_HasError()
TA260 001:383.257 JLINK_IsHalted()
TA260 001:383.774 - 0.517ms returns FALSE
TA260 001:383.783 JLINK_HasError()
TA260 001:385.256 JLINK_IsHalted()
TA260 001:385.748 - 0.492ms returns FALSE
TA260 001:385.754 JLINK_HasError()
TA260 001:387.257 JLINK_IsHalted()
TA260 001:387.840 - 0.583ms returns FALSE
TA260 001:387.848 JLINK_HasError()
TA260 001:389.355 JLINK_IsHalted()
TA260 001:389.848 - 0.492ms returns FALSE
TA260 001:389.856 JLINK_HasError()
TA260 001:391.256 JLINK_IsHalted()
TA260 001:391.738 - 0.482ms returns FALSE
TA260 001:391.743 JLINK_HasError()
TA260 001:393.261 JLINK_IsHalted()
TA260 001:393.761 - 0.500ms returns FALSE
TA260 001:393.768 JLINK_HasError()
TA260 001:395.760 JLINK_IsHalted()
TA260 001:396.320 - 0.559ms returns FALSE
TA260 001:396.329 JLINK_HasError()
TA260 001:397.761 JLINK_IsHalted()
TA260 001:398.248 - 0.486ms returns FALSE
TA260 001:398.254 JLINK_HasError()
TA260 001:399.759 JLINK_IsHalted()
TA260 001:400.260 - 0.501ms returns FALSE
TA260 001:400.266 JLINK_HasError()
TA260 001:401.762 JLINK_IsHalted()
TA260 001:402.262 - 0.499ms returns FALSE
TA260 001:402.268 JLINK_HasError()
TA260 001:404.264 JLINK_IsHalted()
TA260 001:404.783 - 0.519ms returns FALSE
TA260 001:404.789 JLINK_HasError()
TA260 001:406.319 JLINK_IsHalted()
TA260 001:407.035 - 0.715ms returns FALSE
TA260 001:407.046 JLINK_HasError()
TA260 001:408.268 JLINK_IsHalted()
TA260 001:408.784 - 0.515ms returns FALSE
TA260 001:408.791 JLINK_HasError()
TA260 001:410.264 JLINK_IsHalted()
TA260 001:410.758 - 0.493ms returns FALSE
TA260 001:410.763 JLINK_HasError()
TA260 001:411.799 JLINK_IsHalted()
TA260 001:412.310 - 0.510ms returns FALSE
TA260 001:412.320 JLINK_HasError()
TA260 001:414.308 JLINK_IsHalted()
TA260 001:414.840 - 0.532ms returns FALSE
TA260 001:414.846 JLINK_HasError()
TA260 001:416.308 JLINK_IsHalted()
TA260 001:416.829 - 0.520ms returns FALSE
TA260 001:416.838 JLINK_HasError()
TA260 001:418.307 JLINK_IsHalted()
TA260 001:418.784 - 0.476ms returns FALSE
TA260 001:418.790 JLINK_HasError()
TA260 001:420.306 JLINK_IsHalted()
TA260 001:420.816 - 0.509ms returns FALSE
TA260 001:420.822 JLINK_HasError()
TA260 001:422.308 JLINK_IsHalted()
TA260 001:422.785 - 0.477ms returns FALSE
TA260 001:422.793 JLINK_HasError()
TA260 001:424.814 JLINK_IsHalted()
TA260 001:425.342 - 0.527ms returns FALSE
TA260 001:425.349 JLINK_HasError()
TA260 001:426.818 JLINK_IsHalted()
TA260 001:427.477 - 0.658ms returns FALSE
TA260 001:427.487 JLINK_HasError()
TA260 001:428.815 JLINK_IsHalted()
TA260 001:429.341 - 0.525ms returns FALSE
TA260 001:429.347 JLINK_HasError()
TA260 001:430.813 JLINK_IsHalted()
TA260 001:431.290 - 0.476ms returns FALSE
TA260 001:431.295 JLINK_HasError()
TA260 001:433.327 JLINK_IsHalted()
TA260 001:433.840 - 0.512ms returns FALSE
TA260 001:433.847 JLINK_HasError()
TA260 001:435.320 JLINK_IsHalted()
TA260 001:435.817 - 0.497ms returns FALSE
TA260 001:435.824 JLINK_HasError()
TA260 001:437.320 JLINK_IsHalted()
TA260 001:437.849 - 0.528ms returns FALSE
TA260 001:437.855 JLINK_HasError()
TA260 001:439.319 JLINK_IsHalted()
TA260 001:439.841 - 0.521ms returns FALSE
TA260 001:439.848 JLINK_HasError()
TA260 001:441.319 JLINK_IsHalted()
TA260 001:443.592   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:444.081 - 2.761ms returns TRUE
TA260 001:444.104 JLINK_ReadReg(R15 (PC))
TA260 001:444.110 - 0.006ms returns 0x20000000
TA260 001:444.114 JLINK_ClrBPEx(BPHandle = 0x00000005)
TA260 001:444.118 - 0.004ms returns 0x00
TA260 001:444.123 JLINK_ReadReg(R0)
TA260 001:444.141 - 0.017ms returns 0x00000000
TA260 001:444.503 JLINK_HasError()
TA260 001:444.513 JLINK_WriteReg(R0, 0x08008000)
TA260 001:444.518 - 0.004ms returns 0
TA260 001:444.522 JLINK_WriteReg(R1, 0x00004000)
TA260 001:444.525 - 0.003ms returns 0
TA260 001:444.529 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:444.533 - 0.003ms returns 0
TA260 001:444.537 JLINK_WriteReg(R3, 0x00000000)
TA260 001:444.540 - 0.003ms returns 0
TA260 001:444.545 JLINK_WriteReg(R4, 0x00000000)
TA260 001:444.548 - 0.003ms returns 0
TA260 001:444.552 JLINK_WriteReg(R5, 0x00000000)
TA260 001:444.560 - 0.007ms returns 0
TA260 001:444.564 JLINK_WriteReg(R6, 0x00000000)
TA260 001:444.567 - 0.003ms returns 0
TA260 001:444.571 JLINK_WriteReg(R7, 0x00000000)
TA260 001:444.575 - 0.003ms returns 0
TA260 001:444.579 JLINK_WriteReg(R8, 0x00000000)
TA260 001:444.582 - 0.003ms returns 0
TA260 001:444.586 JLINK_WriteReg(R9, 0x20000180)
TA260 001:444.590 - 0.003ms returns 0
TA260 001:444.594 JLINK_WriteReg(R10, 0x00000000)
TA260 001:444.597 - 0.003ms returns 0
TA260 001:444.601 JLINK_WriteReg(R11, 0x00000000)
TA260 001:444.604 - 0.003ms returns 0
TA260 001:444.609 JLINK_WriteReg(R12, 0x00000000)
TA260 001:444.612 - 0.003ms returns 0
TA260 001:444.616 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:444.620 - 0.003ms returns 0
TA260 001:444.624 JLINK_WriteReg(R14, 0x20000001)
TA260 001:444.628 - 0.003ms returns 0
TA260 001:444.632 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 001:444.635 - 0.003ms returns 0
TA260 001:444.639 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:444.643 - 0.003ms returns 0
TA260 001:444.647 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:444.650 - 0.003ms returns 0
TA260 001:444.654 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:444.658 - 0.003ms returns 0
TA260 001:444.662 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:444.665 - 0.003ms returns 0
TA260 001:444.670 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:444.675 - 0.005ms returns 0x00000006
TA260 001:444.680 JLINK_Go()
TA260 001:444.690   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:447.933 - 3.251ms 
TA260 001:447.946 JLINK_IsHalted()
TA260 001:450.362   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:450.840 - 2.893ms returns TRUE
TA260 001:450.847 JLINK_ReadReg(R15 (PC))
TA260 001:450.851 - 0.004ms returns 0x20000000
TA260 001:450.856 JLINK_ClrBPEx(BPHandle = 0x00000006)
TA260 001:450.860 - 0.003ms returns 0x00
TA260 001:450.864 JLINK_ReadReg(R0)
TA260 001:450.867 - 0.003ms returns 0x00000001
TA260 001:450.872 JLINK_HasError()
TA260 001:450.876 JLINK_WriteReg(R0, 0x08008000)
TA260 001:450.880 - 0.003ms returns 0
TA260 001:450.884 JLINK_WriteReg(R1, 0x00004000)
TA260 001:450.888 - 0.003ms returns 0
TA260 001:450.892 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:450.895 - 0.003ms returns 0
TA260 001:450.899 JLINK_WriteReg(R3, 0x00000000)
TA260 001:450.903 - 0.003ms returns 0
TA260 001:450.907 JLINK_WriteReg(R4, 0x00000000)
TA260 001:450.910 - 0.003ms returns 0
TA260 001:450.914 JLINK_WriteReg(R5, 0x00000000)
TA260 001:450.917 - 0.003ms returns 0
TA260 001:450.922 JLINK_WriteReg(R6, 0x00000000)
TA260 001:450.925 - 0.003ms returns 0
TA260 001:450.929 JLINK_WriteReg(R7, 0x00000000)
TA260 001:450.932 - 0.003ms returns 0
TA260 001:450.936 JLINK_WriteReg(R8, 0x00000000)
TA260 001:450.940 - 0.003ms returns 0
TA260 001:450.944 JLINK_WriteReg(R9, 0x20000180)
TA260 001:450.947 - 0.003ms returns 0
TA260 001:450.951 JLINK_WriteReg(R10, 0x00000000)
TA260 001:450.955 - 0.003ms returns 0
TA260 001:450.959 JLINK_WriteReg(R11, 0x00000000)
TA260 001:450.962 - 0.003ms returns 0
TA260 001:450.966 JLINK_WriteReg(R12, 0x00000000)
TA260 001:450.970 - 0.003ms returns 0
TA260 001:450.974 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:450.977 - 0.004ms returns 0
TA260 001:450.982 JLINK_WriteReg(R14, 0x20000001)
TA260 001:450.985 - 0.003ms returns 0
TA260 001:450.989 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 001:450.992 - 0.003ms returns 0
TA260 001:450.996 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:451.000 - 0.003ms returns 0
TA260 001:451.004 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:451.007 - 0.003ms returns 0
TA260 001:451.011 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:451.015 - 0.003ms returns 0
TA260 001:451.019 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:451.022 - 0.003ms returns 0
TA260 001:451.026 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:451.030 - 0.003ms returns 0x00000007
TA260 001:451.034 JLINK_Go()
TA260 001:451.041   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:453.748 - 2.713ms 
TA260 001:453.754 JLINK_IsHalted()
TA260 001:454.237 - 0.482ms returns FALSE
TA260 001:454.246 JLINK_HasError()
TA260 001:455.335 JLINK_IsHalted()
TA260 001:455.847 - 0.511ms returns FALSE
TA260 001:455.856 JLINK_HasError()
TA260 001:457.835 JLINK_IsHalted()
TA260 001:458.476 - 0.641ms returns FALSE
TA260 001:458.488 JLINK_HasError()
TA260 001:459.831 JLINK_IsHalted()
TA260 001:460.339 - 0.507ms returns FALSE
TA260 001:460.344 JLINK_HasError()
TA260 001:461.835 JLINK_IsHalted()
TA260 001:462.362 - 0.526ms returns FALSE
TA260 001:462.369 JLINK_HasError()
TA260 001:464.337 JLINK_IsHalted()
TA260 001:464.840 - 0.502ms returns FALSE
TA260 001:464.845 JLINK_HasError()
TA260 001:466.338 JLINK_IsHalted()
TA260 001:466.806 - 0.467ms returns FALSE
TA260 001:466.814 JLINK_HasError()
TA260 001:468.339 JLINK_IsHalted()
TA260 001:468.841 - 0.501ms returns FALSE
TA260 001:468.847 JLINK_HasError()
TA260 001:470.337 JLINK_IsHalted()
TA260 001:470.839 - 0.502ms returns FALSE
TA260 001:470.844 JLINK_HasError()
TA260 001:472.338 JLINK_IsHalted()
TA260 001:472.839 - 0.501ms returns FALSE
TA260 001:472.846 JLINK_HasError()
TA260 001:474.845 JLINK_IsHalted()
TA260 001:475.340 - 0.494ms returns FALSE
TA260 001:475.346 JLINK_HasError()
TA260 001:476.848 JLINK_IsHalted()
TA260 001:478.035 - 1.185ms returns FALSE
TA260 001:478.046 JLINK_HasError()
TA260 001:479.843 JLINK_IsHalted()
TA260 001:480.318 - 0.475ms returns FALSE
TA260 001:480.324 JLINK_HasError()
TA260 001:481.844 JLINK_IsHalted()
TA260 001:482.339 - 0.494ms returns FALSE
TA260 001:482.345 JLINK_HasError()
TA260 001:484.350 JLINK_IsHalted()
TA260 001:484.851 - 0.500ms returns FALSE
TA260 001:484.858 JLINK_HasError()
TA260 001:486.351 JLINK_IsHalted()
TA260 001:487.033 - 0.682ms returns FALSE
TA260 001:487.046 JLINK_HasError()
TA260 001:488.351 JLINK_IsHalted()
TA260 001:489.159 - 0.807ms returns FALSE
TA260 001:489.170 JLINK_HasError()
TA260 001:491.353 JLINK_IsHalted()
TA260 001:491.841 - 0.488ms returns FALSE
TA260 001:491.848 JLINK_HasError()
TA260 001:493.354 JLINK_IsHalted()
TA260 001:493.861 - 0.506ms returns FALSE
TA260 001:493.866 JLINK_HasError()
TA260 001:495.855 JLINK_IsHalted()
TA260 001:496.362 - 0.506ms returns FALSE
TA260 001:496.369 JLINK_HasError()
TA260 001:497.858 JLINK_IsHalted()
TA260 001:498.342 - 0.483ms returns FALSE
TA260 001:498.349 JLINK_HasError()
TA260 001:499.854 JLINK_IsHalted()
TA260 001:500.327 - 0.472ms returns FALSE
TA260 001:500.333 JLINK_HasError()
TA260 001:501.856 JLINK_IsHalted()
TA260 001:502.318 - 0.461ms returns FALSE
TA260 001:502.324 JLINK_HasError()
TA260 001:503.364 JLINK_IsHalted()
TA260 001:503.850 - 0.486ms returns FALSE
TA260 001:503.856 JLINK_HasError()
TA260 001:505.365 JLINK_IsHalted()
TA260 001:506.321 - 0.955ms returns FALSE
TA260 001:506.333 JLINK_HasError()
TA260 001:508.363 JLINK_IsHalted()
TA260 001:508.853 - 0.489ms returns FALSE
TA260 001:508.859 JLINK_HasError()
TA260 001:510.359 JLINK_IsHalted()
TA260 001:510.839 - 0.479ms returns FALSE
TA260 001:510.845 JLINK_HasError()
TA260 001:512.360 JLINK_IsHalted()
TA260 001:512.862 - 0.502ms returns FALSE
TA260 001:512.869 JLINK_HasError()
TA260 001:514.864 JLINK_IsHalted()
TA260 001:515.340 - 0.475ms returns FALSE
TA260 001:515.346 JLINK_HasError()
TA260 001:516.868 JLINK_IsHalted()
TA260 001:517.364 - 0.495ms returns FALSE
TA260 001:517.370 JLINK_HasError()
TA260 001:518.865 JLINK_IsHalted()
TA260 001:519.350 - 0.485ms returns FALSE
TA260 001:519.356 JLINK_HasError()
TA260 001:520.867 JLINK_IsHalted()
TA260 001:521.364 - 0.496ms returns FALSE
TA260 001:521.370 JLINK_HasError()
TA260 001:523.377 JLINK_IsHalted()
TA260 001:524.158 - 0.780ms returns FALSE
TA260 001:524.169 JLINK_HasError()
TA260 001:525.371 JLINK_IsHalted()
TA260 001:525.861 - 0.489ms returns FALSE
TA260 001:525.867 JLINK_HasError()
TA260 001:527.376 JLINK_IsHalted()
TA260 001:528.159 - 0.782ms returns FALSE
TA260 001:528.169 JLINK_HasError()
TA260 001:529.382 JLINK_IsHalted()
TA260 001:529.896 - 0.513ms returns FALSE
TA260 001:529.902 JLINK_HasError()
TA260 001:531.370 JLINK_IsHalted()
TA260 001:531.884 - 0.513ms returns FALSE
TA260 001:531.890 JLINK_HasError()
TA260 001:533.376 JLINK_IsHalted()
TA260 001:533.900 - 0.523ms returns FALSE
TA260 001:533.906 JLINK_HasError()
TA260 001:535.878 JLINK_IsHalted()
TA260 001:536.604 - 0.725ms returns FALSE
TA260 001:536.615 JLINK_HasError()
TA260 001:537.878 JLINK_IsHalted()
TA260 001:538.385 - 0.506ms returns FALSE
TA260 001:538.391 JLINK_HasError()
TA260 001:539.877 JLINK_IsHalted()
TA260 001:540.349 - 0.471ms returns FALSE
TA260 001:540.356 JLINK_HasError()
TA260 001:541.880 JLINK_IsHalted()
TA260 001:542.386 - 0.505ms returns FALSE
TA260 001:542.393 JLINK_HasError()
TA260 001:544.380 JLINK_IsHalted()
TA260 001:544.862 - 0.481ms returns FALSE
TA260 001:544.868 JLINK_HasError()
TA260 001:546.383 JLINK_IsHalted()
TA260 001:546.943 - 0.559ms returns FALSE
TA260 001:546.955 JLINK_HasError()
TA260 001:548.386 JLINK_IsHalted()
TA260 001:548.895 - 0.508ms returns FALSE
TA260 001:548.901 JLINK_HasError()
TA260 001:550.382 JLINK_IsHalted()
TA260 001:550.886 - 0.504ms returns FALSE
TA260 001:550.891 JLINK_HasError()
TA260 001:552.385 JLINK_IsHalted()
TA260 001:552.886 - 0.500ms returns FALSE
TA260 001:552.892 JLINK_HasError()
TA260 001:555.388 JLINK_IsHalted()
TA260 001:556.045 - 0.656ms returns FALSE
TA260 001:556.057 JLINK_HasError()
TA260 001:557.298 JLINK_IsHalted()
TA260 001:557.793 - 0.501ms returns FALSE
TA260 001:557.799 JLINK_HasError()
TA260 001:559.291 JLINK_IsHalted()
TA260 001:559.803 - 0.512ms returns FALSE
TA260 001:559.809 JLINK_HasError()
TA260 001:561.287 JLINK_IsHalted()
TA260 001:561.784 - 0.496ms returns FALSE
TA260 001:561.789 JLINK_HasError()
TA260 001:563.291 JLINK_IsHalted()
TA260 001:563.783 - 0.491ms returns FALSE
TA260 001:563.789 JLINK_HasError()
TA260 001:565.793 JLINK_IsHalted()
TA260 001:566.331 - 0.537ms returns FALSE
TA260 001:566.339 JLINK_HasError()
TA260 001:567.793 JLINK_IsHalted()
TA260 001:568.283 - 0.489ms returns FALSE
TA260 001:568.295 JLINK_HasError()
TA260 001:569.795 JLINK_IsHalted()
TA260 001:570.283 - 0.487ms returns FALSE
TA260 001:570.290 JLINK_HasError()
TA260 001:571.793 JLINK_IsHalted()
TA260 001:572.283 - 0.490ms returns FALSE
TA260 001:572.290 JLINK_HasError()
TA260 001:574.298 JLINK_IsHalted()
TA260 001:574.794 - 0.496ms returns FALSE
TA260 001:574.800 JLINK_HasError()
TA260 001:576.316 JLINK_IsHalted()
TA260 001:576.889 - 0.571ms returns FALSE
TA260 001:576.896 JLINK_HasError()
TA260 001:578.301 JLINK_IsHalted()
TA260 001:578.794 - 0.492ms returns FALSE
TA260 001:578.800 JLINK_HasError()
TA260 001:580.298 JLINK_IsHalted()
TA260 001:580.773 - 0.475ms returns FALSE
TA260 001:580.779 JLINK_HasError()
TA260 001:582.299 JLINK_IsHalted()
TA260 001:582.862 - 0.563ms returns FALSE
TA260 001:582.869 JLINK_HasError()
TA260 001:584.805 JLINK_IsHalted()
TA260 001:585.282 - 0.477ms returns FALSE
TA260 001:585.288 JLINK_HasError()
TA260 001:586.807 JLINK_IsHalted()
TA260 001:587.275 - 0.467ms returns FALSE
TA260 001:587.282 JLINK_HasError()
TA260 001:588.807 JLINK_IsHalted()
TA260 001:589.293 - 0.485ms returns FALSE
TA260 001:589.299 JLINK_HasError()
TA260 001:590.806 JLINK_IsHalted()
TA260 001:591.307 - 0.500ms returns FALSE
TA260 001:591.313 JLINK_HasError()
TA260 001:593.312 JLINK_IsHalted()
TA260 001:593.786 - 0.473ms returns FALSE
TA260 001:593.792 JLINK_HasError()
TA260 001:595.312 JLINK_IsHalted()
TA260 001:595.783 - 0.470ms returns FALSE
TA260 001:595.789 JLINK_HasError()
TA260 001:597.313 JLINK_IsHalted()
TA260 001:597.992 - 0.678ms returns FALSE
TA260 001:598.004 JLINK_HasError()
TA260 001:600.311 JLINK_IsHalted()
TA260 001:600.852 - 0.540ms returns FALSE
TA260 001:600.859 JLINK_HasError()
TA260 001:602.312 JLINK_IsHalted()
TA260 001:602.783 - 0.470ms returns FALSE
TA260 001:602.789 JLINK_HasError()
TA260 001:604.819 JLINK_IsHalted()
TA260 001:605.310 - 0.491ms returns FALSE
TA260 001:605.317 JLINK_HasError()
TA260 001:606.822 JLINK_IsHalted()
TA260 001:607.345 - 0.522ms returns FALSE
TA260 001:607.361 JLINK_HasError()
TA260 001:608.818 JLINK_IsHalted()
TA260 001:609.288 - 0.469ms returns FALSE
TA260 001:609.298 JLINK_HasError()
TA260 001:610.817 JLINK_IsHalted()
TA260 001:611.317 - 0.499ms returns FALSE
TA260 001:611.323 JLINK_HasError()
TA260 001:612.389 JLINK_IsHalted()
TA260 001:612.861 - 0.471ms returns FALSE
TA260 001:612.867 JLINK_HasError()
TA260 001:614.894 JLINK_IsHalted()
TA260 001:615.384 - 0.489ms returns FALSE
TA260 001:615.390 JLINK_HasError()
TA260 001:618.513 JLINK_IsHalted()
TA260 001:618.979 - 0.465ms returns FALSE
TA260 001:618.992 JLINK_HasError()
TA260 001:620.510 JLINK_IsHalted()
TA260 001:621.013 - 0.502ms returns FALSE
TA260 001:621.020 JLINK_HasError()
TA260 001:623.013 JLINK_IsHalted()
TA260 001:623.476 - 0.462ms returns FALSE
TA260 001:623.489 JLINK_HasError()
TA260 001:624.907 JLINK_IsHalted()
TA260 001:625.384 - 0.476ms returns FALSE
TA260 001:625.389 JLINK_HasError()
TA260 001:626.910 JLINK_IsHalted()
TA260 001:627.397 - 0.487ms returns FALSE
TA260 001:627.404 JLINK_HasError()
TA260 001:628.908 JLINK_IsHalted()
TA260 001:629.366 - 0.457ms returns FALSE
TA260 001:629.376 JLINK_HasError()
TA260 001:630.610 JLINK_IsHalted()
TA260 001:631.078 - 0.467ms returns FALSE
TA260 001:631.087 JLINK_HasError()
TA260 001:632.145 JLINK_IsHalted()
TA260 001:632.612 - 0.466ms returns FALSE
TA260 001:632.619 JLINK_HasError()
TA260 001:633.674 JLINK_IsHalted()
TA260 001:634.157 - 0.482ms returns FALSE
TA260 001:634.169 JLINK_HasError()
TA260 001:635.202 JLINK_IsHalted()
TA260 001:635.691 - 0.489ms returns FALSE
TA260 001:635.697 JLINK_HasError()
TA260 001:636.741 JLINK_IsHalted()
TA260 001:637.163 - 0.422ms returns FALSE
TA260 001:637.172 JLINK_HasError()
TA260 001:638.297 JLINK_IsHalted()
TA260 001:638.740 - 0.442ms returns FALSE
TA260 001:638.746 JLINK_HasError()
TA260 001:639.825 JLINK_IsHalted()
TA260 001:640.240 - 0.414ms returns FALSE
TA260 001:640.250 JLINK_HasError()
TA260 001:641.829 JLINK_IsHalted()
TA260 001:642.328 - 0.498ms returns FALSE
TA260 001:642.334 JLINK_HasError()
TA260 001:644.330 JLINK_IsHalted()
TA260 001:644.869 - 0.539ms returns FALSE
TA260 001:644.876 JLINK_HasError()
TA260 001:648.335 JLINK_IsHalted()
TA260 001:648.802 - 0.466ms returns FALSE
TA260 001:648.808 JLINK_HasError()
TA260 001:650.003 JLINK_IsHalted()
TA260 001:650.462 - 0.459ms returns FALSE
TA260 001:650.468 JLINK_HasError()
TA260 001:652.006 JLINK_IsHalted()
TA260 001:652.510 - 0.504ms returns FALSE
TA260 001:652.517 JLINK_HasError()
TA260 001:654.509 JLINK_IsHalted()
TA260 001:654.986 - 0.477ms returns FALSE
TA260 001:654.993 JLINK_HasError()
TA260 001:657.012 JLINK_IsHalted()
TA260 001:657.496 - 0.483ms returns FALSE
TA260 001:657.503 JLINK_HasError()
TA260 001:659.010 JLINK_IsHalted()
TA260 001:659.507 - 0.496ms returns FALSE
TA260 001:659.513 JLINK_HasError()
TA260 001:661.012 JLINK_IsHalted()
TA260 001:661.509 - 0.497ms returns FALSE
TA260 001:661.515 JLINK_HasError()
TA260 001:663.011 JLINK_IsHalted()
TA260 001:663.471 - 0.459ms returns FALSE
TA260 001:663.477 JLINK_HasError()
TA260 001:664.517 JLINK_IsHalted()
TA260 001:664.986 - 0.469ms returns FALSE
TA260 001:664.992 JLINK_HasError()
TA260 001:666.517 JLINK_IsHalted()
TA260 001:666.988 - 0.470ms returns FALSE
TA260 001:666.994 JLINK_HasError()
TA260 001:668.516 JLINK_IsHalted()
TA260 001:669.070 - 0.553ms returns FALSE
TA260 001:669.081 JLINK_HasError()
TA260 001:670.515 JLINK_IsHalted()
TA260 001:671.010 - 0.494ms returns FALSE
TA260 001:671.016 JLINK_HasError()
TA260 001:673.025 JLINK_IsHalted()
TA260 001:673.539 - 0.513ms returns FALSE
TA260 001:673.554 JLINK_HasError()
TA260 001:675.022 JLINK_IsHalted()
TA260 001:675.604 - 0.581ms returns FALSE
TA260 001:675.614 JLINK_HasError()
TA260 001:677.026 JLINK_IsHalted()
TA260 001:677.510 - 0.484ms returns FALSE
TA260 001:677.522 JLINK_HasError()
TA260 001:679.024 JLINK_IsHalted()
TA260 001:679.511 - 0.486ms returns FALSE
TA260 001:679.518 JLINK_HasError()
TA260 001:681.022 JLINK_IsHalted()
TA260 001:681.511 - 0.488ms returns FALSE
TA260 001:681.518 JLINK_HasError()
TA260 001:683.025 JLINK_IsHalted()
TA260 001:683.496 - 0.470ms returns FALSE
TA260 001:683.502 JLINK_HasError()
TA260 001:685.528 JLINK_IsHalted()
TA260 001:686.008 - 0.479ms returns FALSE
TA260 001:686.014 JLINK_HasError()
TA260 001:687.530 JLINK_IsHalted()
TA260 001:688.010 - 0.480ms returns FALSE
TA260 001:688.016 JLINK_HasError()
TA260 001:689.526 JLINK_IsHalted()
TA260 001:689.976 - 0.449ms returns FALSE
TA260 001:689.983 JLINK_HasError()
TA260 001:691.530 JLINK_IsHalted()
TA260 001:692.034 - 0.503ms returns FALSE
TA260 001:692.039 JLINK_HasError()
TA260 001:694.030 JLINK_IsHalted()
TA260 001:694.510 - 0.479ms returns FALSE
TA260 001:694.516 JLINK_HasError()
TA260 001:696.033 JLINK_IsHalted()
TA260 001:696.615 - 0.581ms returns FALSE
TA260 001:696.628 JLINK_HasError()
TA260 001:698.034 JLINK_IsHalted()
TA260 001:698.514 - 0.480ms returns FALSE
TA260 001:698.532 JLINK_HasError()
TA260 001:700.032 JLINK_IsHalted()
TA260 001:700.539 - 0.507ms returns FALSE
TA260 001:700.545 JLINK_HasError()
TA260 001:702.034 JLINK_IsHalted()
TA260 001:702.512 - 0.478ms returns FALSE
TA260 001:702.519 JLINK_HasError()
TA260 001:704.537 JLINK_IsHalted()
TA260 001:705.016 - 0.479ms returns FALSE
TA260 001:705.024 JLINK_HasError()
TA260 001:706.542 JLINK_IsHalted()
TA260 001:707.034 - 0.491ms returns FALSE
TA260 001:707.041 JLINK_HasError()
TA260 001:709.538 JLINK_IsHalted()
TA260 001:710.069 - 0.530ms returns FALSE
TA260 001:710.075 JLINK_HasError()
TA260 001:711.538 JLINK_IsHalted()
TA260 001:712.010 - 0.472ms returns FALSE
TA260 001:712.016 JLINK_HasError()
TA260 001:714.042 JLINK_IsHalted()
TA260 001:714.511 - 0.468ms returns FALSE
TA260 001:714.518 JLINK_HasError()
TA260 001:716.044 JLINK_IsHalted()
TA260 001:716.514 - 0.469ms returns FALSE
TA260 001:716.524 JLINK_HasError()
TA260 001:718.046 JLINK_IsHalted()
TA260 001:718.567 - 0.520ms returns FALSE
TA260 001:718.574 JLINK_HasError()
TA260 001:720.046 JLINK_IsHalted()
TA260 001:720.538 - 0.492ms returns FALSE
TA260 001:720.544 JLINK_HasError()
TA260 001:722.046 JLINK_IsHalted()
TA260 001:722.617 - 0.571ms returns FALSE
TA260 001:722.627 JLINK_HasError()
TA260 001:724.554 JLINK_IsHalted()
TA260 001:725.095 - 0.540ms returns FALSE
TA260 001:725.102 JLINK_HasError()
TA260 001:726.552 JLINK_IsHalted()
TA260 001:727.090 - 0.537ms returns FALSE
TA260 001:727.097 JLINK_HasError()
TA260 001:728.554 JLINK_IsHalted()
TA260 001:729.004 - 0.449ms returns FALSE
TA260 001:729.016 JLINK_HasError()
TA260 001:730.568 JLINK_IsHalted()
TA260 001:731.082 - 0.514ms returns FALSE
TA260 001:731.092 JLINK_HasError()
TA260 001:733.055 JLINK_IsHalted()
TA260 001:733.554 - 0.499ms returns FALSE
TA260 001:733.560 JLINK_HasError()
TA260 001:735.055 JLINK_IsHalted()
TA260 001:735.612 - 0.556ms returns FALSE
TA260 001:735.618 JLINK_HasError()
TA260 001:737.060 JLINK_IsHalted()
TA260 001:737.563 - 0.503ms returns FALSE
TA260 001:737.575 JLINK_HasError()
TA260 001:739.060 JLINK_IsHalted()
TA260 001:739.570 - 0.510ms returns FALSE
TA260 001:739.583 JLINK_HasError()
TA260 001:741.071 JLINK_IsHalted()
TA260 001:741.509 - 0.437ms returns FALSE
TA260 001:741.515 JLINK_HasError()
TA260 001:743.059 JLINK_IsHalted()
TA260 001:743.566 - 0.506ms returns FALSE
TA260 001:743.580 JLINK_HasError()
TA260 001:745.566 JLINK_IsHalted()
TA260 001:746.060 - 0.493ms returns FALSE
TA260 001:746.069 JLINK_HasError()
TA260 001:747.566 JLINK_IsHalted()
TA260 001:748.034 - 0.468ms returns FALSE
TA260 001:748.040 JLINK_HasError()
TA260 001:749.561 JLINK_IsHalted()
TA260 001:750.065 - 0.504ms returns FALSE
TA260 001:750.071 JLINK_HasError()
TA260 001:751.569 JLINK_IsHalted()
TA260 001:752.070 - 0.501ms returns FALSE
TA260 001:752.078 JLINK_HasError()
TA260 001:754.069 JLINK_IsHalted()
TA260 001:754.567 - 0.498ms returns FALSE
TA260 001:754.574 JLINK_HasError()
TA260 001:756.575 JLINK_IsHalted()
TA260 001:757.020 - 0.445ms returns FALSE
TA260 001:757.026 JLINK_HasError()
TA260 001:758.610 JLINK_IsHalted()
TA260 001:759.133 - 0.523ms returns FALSE
TA260 001:759.140 JLINK_HasError()
TA260 001:760.569 JLINK_IsHalted()
TA260 001:761.030 - 0.460ms returns FALSE
TA260 001:761.036 JLINK_HasError()
TA260 001:763.076 JLINK_IsHalted()
TA260 001:763.614 - 0.537ms returns FALSE
TA260 001:763.620 JLINK_HasError()
TA260 001:765.076 JLINK_IsHalted()
TA260 001:765.611 - 0.535ms returns FALSE
TA260 001:765.617 JLINK_HasError()
TA260 001:767.082 JLINK_IsHalted()
TA260 001:767.615 - 0.533ms returns FALSE
TA260 001:767.623 JLINK_HasError()
TA260 001:769.082 JLINK_IsHalted()
TA260 001:769.619 - 0.536ms returns FALSE
TA260 001:769.629 JLINK_HasError()
TA260 001:771.079 JLINK_IsHalted()
TA260 001:771.599 - 0.519ms returns FALSE
TA260 001:771.605 JLINK_HasError()
TA260 001:773.081 JLINK_IsHalted()
TA260 001:773.619 - 0.537ms returns FALSE
TA260 001:773.634 JLINK_HasError()
TA260 001:775.586 JLINK_IsHalted()
TA260 001:776.032 - 0.446ms returns FALSE
TA260 001:776.040 JLINK_HasError()
TA260 001:777.585 JLINK_IsHalted()
TA260 001:778.111 - 0.526ms returns FALSE
TA260 001:778.117 JLINK_HasError()
TA260 001:779.582 JLINK_IsHalted()
TA260 001:780.053 - 0.471ms returns FALSE
TA260 001:780.058 JLINK_HasError()
TA260 001:781.586 JLINK_IsHalted()
TA260 001:782.067 - 0.481ms returns FALSE
TA260 001:782.073 JLINK_HasError()
TA260 001:784.086 JLINK_IsHalted()
TA260 001:784.610 - 0.524ms returns FALSE
TA260 001:784.617 JLINK_HasError()
TA260 001:786.094 JLINK_IsHalted()
TA260 001:786.615 - 0.521ms returns FALSE
TA260 001:786.623 JLINK_HasError()
TA260 001:788.092 JLINK_IsHalted()
TA260 001:788.600 - 0.508ms returns FALSE
TA260 001:788.606 JLINK_HasError()
TA260 001:790.093 JLINK_IsHalted()
TA260 001:790.572 - 0.479ms returns FALSE
TA260 001:790.587 JLINK_HasError()
TA260 001:792.093 JLINK_IsHalted()
TA260 001:792.603 - 0.509ms returns FALSE
TA260 001:792.609 JLINK_HasError()
TA260 001:794.595 JLINK_IsHalted()
TA260 001:795.078 - 0.482ms returns FALSE
TA260 001:795.085 JLINK_HasError()
TA260 001:796.597 JLINK_IsHalted()
TA260 001:797.114 - 0.516ms returns FALSE
TA260 001:797.120 JLINK_HasError()
TA260 001:798.601 JLINK_IsHalted()
TA260 001:799.058 - 0.456ms returns FALSE
TA260 001:799.071 JLINK_HasError()
TA260 001:800.596 JLINK_IsHalted()
TA260 001:801.102 - 0.505ms returns FALSE
TA260 001:801.110 JLINK_HasError()
TA260 001:803.102 JLINK_IsHalted()
TA260 001:803.612 - 0.510ms returns FALSE
TA260 001:803.618 JLINK_HasError()
TA260 001:805.103 JLINK_IsHalted()
TA260 001:805.599 - 0.495ms returns FALSE
TA260 001:805.605 JLINK_HasError()
TA260 001:807.111 JLINK_IsHalted()
TA260 001:807.601 - 0.490ms returns FALSE
TA260 001:807.611 JLINK_HasError()
TA260 001:809.104 JLINK_IsHalted()
TA260 001:809.601 - 0.496ms returns FALSE
TA260 001:809.606 JLINK_HasError()
TA260 001:811.104 JLINK_IsHalted()
TA260 001:811.613 - 0.509ms returns FALSE
TA260 001:811.619 JLINK_HasError()
TA260 001:813.169 JLINK_IsHalted()
TA260 001:813.657 - 0.487ms returns FALSE
TA260 001:813.663 JLINK_HasError()
TA260 001:815.676 JLINK_IsHalted()
TA260 001:816.159 - 0.483ms returns FALSE
TA260 001:816.166 JLINK_HasError()
TA260 001:817.681 JLINK_IsHalted()
TA260 001:818.162 - 0.481ms returns FALSE
TA260 001:818.175 JLINK_HasError()
TA260 001:823.189 JLINK_IsHalted()
TA260 001:823.669 - 0.480ms returns FALSE
TA260 001:823.683 JLINK_HasError()
TA260 001:825.187 JLINK_IsHalted()
TA260 001:825.697 - 0.510ms returns FALSE
TA260 001:825.704 JLINK_HasError()
TA260 001:827.188 JLINK_IsHalted()
TA260 001:827.700 - 0.511ms returns FALSE
TA260 001:827.717 JLINK_HasError()
TA260 001:829.183 JLINK_IsHalted()
TA260 001:829.700 - 0.516ms returns FALSE
TA260 001:829.706 JLINK_HasError()
TA260 001:831.490 JLINK_IsHalted()
TA260 001:831.983 - 0.492ms returns FALSE
TA260 001:831.989 JLINK_HasError()
TA260 001:833.995 JLINK_IsHalted()
TA260 001:834.512 - 0.516ms returns FALSE
TA260 001:834.519 JLINK_HasError()
TA260 001:835.998 JLINK_IsHalted()
TA260 001:836.513 - 0.515ms returns FALSE
TA260 001:836.526 JLINK_HasError()
TA260 001:837.995 JLINK_IsHalted()
TA260 001:838.463 - 0.467ms returns FALSE
TA260 001:838.469 JLINK_HasError()
TA260 001:839.999 JLINK_IsHalted()
TA260 001:840.480 - 0.481ms returns FALSE
TA260 001:840.490 JLINK_HasError()
TA260 001:841.998 JLINK_IsHalted()
TA260 001:844.367   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:844.850 - 2.852ms returns TRUE
TA260 001:844.857 JLINK_ReadReg(R15 (PC))
TA260 001:844.863 - 0.005ms returns 0x20000000
TA260 001:844.867 JLINK_ClrBPEx(BPHandle = 0x00000007)
TA260 001:844.871 - 0.003ms returns 0x00
TA260 001:844.875 JLINK_ReadReg(R0)
TA260 001:844.879 - 0.003ms returns 0x00000000
TA260 001:845.239 JLINK_HasError()
TA260 001:845.249 JLINK_WriteReg(R0, 0x0800C000)
TA260 001:845.254 - 0.005ms returns 0
TA260 001:845.259 JLINK_WriteReg(R1, 0x00004000)
TA260 001:845.262 - 0.003ms returns 0
TA260 001:845.266 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:845.270 - 0.003ms returns 0
TA260 001:845.274 JLINK_WriteReg(R3, 0x00000000)
TA260 001:845.277 - 0.003ms returns 0
TA260 001:845.281 JLINK_WriteReg(R4, 0x00000000)
TA260 001:845.285 - 0.003ms returns 0
TA260 001:845.289 JLINK_WriteReg(R5, 0x00000000)
TA260 001:845.292 - 0.003ms returns 0
TA260 001:845.296 JLINK_WriteReg(R6, 0x00000000)
TA260 001:845.300 - 0.003ms returns 0
TA260 001:845.304 JLINK_WriteReg(R7, 0x00000000)
TA260 001:845.307 - 0.003ms returns 0
TA260 001:845.312 JLINK_WriteReg(R8, 0x00000000)
TA260 001:845.315 - 0.003ms returns 0
TA260 001:845.319 JLINK_WriteReg(R9, 0x20000180)
TA260 001:845.322 - 0.003ms returns 0
TA260 001:845.326 JLINK_WriteReg(R10, 0x00000000)
TA260 001:845.330 - 0.003ms returns 0
TA260 001:845.334 JLINK_WriteReg(R11, 0x00000000)
TA260 001:845.337 - 0.003ms returns 0
TA260 001:845.341 JLINK_WriteReg(R12, 0x00000000)
TA260 001:845.345 - 0.003ms returns 0
TA260 001:845.349 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:845.352 - 0.003ms returns 0
TA260 001:845.357 JLINK_WriteReg(R14, 0x20000001)
TA260 001:845.360 - 0.003ms returns 0
TA260 001:845.364 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 001:845.367 - 0.003ms returns 0
TA260 001:845.372 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:845.375 - 0.003ms returns 0
TA260 001:845.379 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:845.382 - 0.003ms returns 0
TA260 001:845.386 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:845.390 - 0.003ms returns 0
TA260 001:845.394 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:845.397 - 0.003ms returns 0
TA260 001:845.402 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:845.406 - 0.005ms returns 0x00000008
TA260 001:845.411 JLINK_Go()
TA260 001:845.419   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:848.179 - 2.767ms 
TA260 001:848.191 JLINK_IsHalted()
TA260 001:850.540   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:851.056 - 2.865ms returns TRUE
TA260 001:851.067 JLINK_ReadReg(R15 (PC))
TA260 001:851.072 - 0.005ms returns 0x20000000
TA260 001:851.105 JLINK_ClrBPEx(BPHandle = 0x00000008)
TA260 001:851.118 - 0.012ms returns 0x00
TA260 001:851.124 JLINK_ReadReg(R0)
TA260 001:851.128 - 0.004ms returns 0x00000001
TA260 001:851.133 JLINK_HasError()
TA260 001:851.138 JLINK_WriteReg(R0, 0x0800C000)
TA260 001:851.142 - 0.004ms returns 0
TA260 001:851.146 JLINK_WriteReg(R1, 0x00004000)
TA260 001:851.149 - 0.003ms returns 0
TA260 001:851.154 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:851.157 - 0.003ms returns 0
TA260 001:851.161 JLINK_WriteReg(R3, 0x00000000)
TA260 001:851.164 - 0.003ms returns 0
TA260 001:851.168 JLINK_WriteReg(R4, 0x00000000)
TA260 001:851.172 - 0.003ms returns 0
TA260 001:851.176 JLINK_WriteReg(R5, 0x00000000)
TA260 001:851.179 - 0.003ms returns 0
TA260 001:851.183 JLINK_WriteReg(R6, 0x00000000)
TA260 001:851.187 - 0.003ms returns 0
TA260 001:851.191 JLINK_WriteReg(R7, 0x00000000)
TA260 001:851.194 - 0.003ms returns 0
TA260 001:851.198 JLINK_WriteReg(R8, 0x00000000)
TA260 001:851.202 - 0.003ms returns 0
TA260 001:851.206 JLINK_WriteReg(R9, 0x20000180)
TA260 001:851.209 - 0.003ms returns 0
TA260 001:851.216 JLINK_WriteReg(R10, 0x00000000)
TA260 001:851.220 - 0.004ms returns 0
TA260 001:851.224 JLINK_WriteReg(R11, 0x00000000)
TA260 001:851.228 - 0.003ms returns 0
TA260 001:851.232 JLINK_WriteReg(R12, 0x00000000)
TA260 001:851.235 - 0.003ms returns 0
TA260 001:851.239 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:851.243 - 0.003ms returns 0
TA260 001:851.247 JLINK_WriteReg(R14, 0x20000001)
TA260 001:851.250 - 0.003ms returns 0
TA260 001:851.254 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 001:851.258 - 0.003ms returns 0
TA260 001:851.262 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:851.265 - 0.003ms returns 0
TA260 001:851.269 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:851.273 - 0.003ms returns 0
TA260 001:851.277 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:851.280 - 0.003ms returns 0
TA260 001:851.284 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:851.288 - 0.003ms returns 0
TA260 001:851.292 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:851.296 - 0.004ms returns 0x00000009
TA260 001:851.300 JLINK_Go()
TA260 001:851.309   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:854.042 - 2.741ms 
TA260 001:854.048 JLINK_IsHalted()
TA260 001:854.553 - 0.504ms returns FALSE
TA260 001:854.559 JLINK_HasError()
TA260 001:856.510 JLINK_IsHalted()
TA260 001:856.976 - 0.465ms returns FALSE
TA260 001:856.983 JLINK_HasError()
TA260 001:858.511 JLINK_IsHalted()
TA260 001:858.953 - 0.442ms returns FALSE
TA260 001:858.960 JLINK_HasError()
TA260 001:860.513 JLINK_IsHalted()
TA260 001:861.006 - 0.493ms returns FALSE
TA260 001:861.012 JLINK_HasError()
TA260 001:863.013 JLINK_IsHalted()
TA260 001:863.496 - 0.482ms returns FALSE
TA260 001:863.503 JLINK_HasError()
TA260 001:865.017 JLINK_IsHalted()
TA260 001:865.512 - 0.494ms returns FALSE
TA260 001:865.518 JLINK_HasError()
TA260 001:867.025 JLINK_IsHalted()
TA260 001:867.512 - 0.487ms returns FALSE
TA260 001:867.521 JLINK_HasError()
TA260 001:868.709 JLINK_IsHalted()
TA260 001:869.203 - 0.493ms returns FALSE
TA260 001:869.209 JLINK_HasError()
TA260 001:870.709 JLINK_IsHalted()
TA260 001:871.180 - 0.471ms returns FALSE
TA260 001:871.186 JLINK_HasError()
TA260 001:873.213 JLINK_IsHalted()
TA260 001:874.057 - 0.844ms returns FALSE
TA260 001:874.064 JLINK_HasError()
TA260 001:875.215 JLINK_IsHalted()
TA260 001:875.658 - 0.443ms returns FALSE
TA260 001:875.664 JLINK_HasError()
TA260 001:877.223 JLINK_IsHalted()
TA260 001:877.740 - 0.516ms returns FALSE
TA260 001:877.746 JLINK_HasError()
TA260 001:879.214 JLINK_IsHalted()
TA260 001:879.887 - 0.672ms returns FALSE
TA260 001:879.898 JLINK_HasError()
TA260 001:881.214 JLINK_IsHalted()
TA260 001:881.743 - 0.528ms returns FALSE
TA260 001:881.753 JLINK_HasError()
TA260 001:883.218 JLINK_IsHalted()
TA260 001:883.693 - 0.474ms returns FALSE
TA260 001:883.699 JLINK_HasError()
TA260 001:885.720 JLINK_IsHalted()
TA260 001:886.243 - 0.522ms returns FALSE
TA260 001:886.250 JLINK_HasError()
TA260 001:887.724 JLINK_IsHalted()
TA260 001:888.235 - 0.511ms returns FALSE
TA260 001:888.242 JLINK_HasError()
TA260 001:889.720 JLINK_IsHalted()
TA260 001:890.156 - 0.436ms returns FALSE
TA260 001:890.163 JLINK_HasError()
TA260 001:891.719 JLINK_IsHalted()
TA260 001:892.192 - 0.472ms returns FALSE
TA260 001:892.198 JLINK_HasError()
TA260 001:894.224 JLINK_IsHalted()
TA260 001:894.841 - 0.617ms returns FALSE
TA260 001:894.850 JLINK_HasError()
TA260 001:896.362 JLINK_IsHalted()
TA260 001:896.888 - 0.525ms returns FALSE
TA260 001:896.898 JLINK_HasError()
TA260 001:898.227 JLINK_IsHalted()
TA260 001:898.760 - 0.532ms returns FALSE
TA260 001:898.767 JLINK_HasError()
TA260 001:900.224 JLINK_IsHalted()
TA260 001:900.704 - 0.480ms returns FALSE
TA260 001:900.710 JLINK_HasError()
TA260 001:902.225 JLINK_IsHalted()
TA260 001:902.736 - 0.511ms returns FALSE
TA260 001:902.742 JLINK_HasError()
TA260 001:904.730 JLINK_IsHalted()
TA260 001:905.237 - 0.507ms returns FALSE
TA260 001:905.244 JLINK_HasError()
TA260 001:906.733 JLINK_IsHalted()
TA260 001:907.263 - 0.529ms returns FALSE
TA260 001:907.277 JLINK_HasError()
TA260 001:908.731 JLINK_IsHalted()
TA260 001:909.260 - 0.529ms returns FALSE
TA260 001:909.266 JLINK_HasError()
TA260 001:910.733 JLINK_IsHalted()
TA260 001:911.264 - 0.530ms returns FALSE
TA260 001:911.271 JLINK_HasError()
TA260 001:913.240 JLINK_IsHalted()
TA260 001:913.740 - 0.500ms returns FALSE
TA260 001:913.746 JLINK_HasError()
TA260 001:915.237 JLINK_IsHalted()
TA260 001:915.697 - 0.459ms returns FALSE
TA260 001:915.704 JLINK_HasError()
TA260 001:917.239 JLINK_IsHalted()
TA260 001:917.742 - 0.503ms returns FALSE
TA260 001:917.756 JLINK_HasError()
TA260 001:919.236 JLINK_IsHalted()
TA260 001:919.713 - 0.476ms returns FALSE
TA260 001:919.718 JLINK_HasError()
TA260 001:921.236 JLINK_IsHalted()
TA260 001:921.713 - 0.476ms returns FALSE
TA260 001:921.718 JLINK_HasError()
TA260 001:923.241 JLINK_IsHalted()
TA260 001:923.713 - 0.471ms returns FALSE
TA260 001:923.720 JLINK_HasError()
TA260 001:925.745 JLINK_IsHalted()
TA260 001:926.206 - 0.461ms returns FALSE
TA260 001:926.216 JLINK_HasError()
TA260 001:928.744 JLINK_IsHalted()
TA260 001:929.215 - 0.471ms returns FALSE
TA260 001:929.221 JLINK_HasError()
TA260 001:930.742 JLINK_IsHalted()
TA260 001:931.224 - 0.481ms returns FALSE
TA260 001:931.229 JLINK_HasError()
TA260 001:933.250 JLINK_IsHalted()
TA260 001:933.746 - 0.496ms returns FALSE
TA260 001:933.753 JLINK_HasError()
TA260 001:935.248 JLINK_IsHalted()
TA260 001:935.737 - 0.488ms returns FALSE
TA260 001:935.742 JLINK_HasError()
TA260 001:937.250 JLINK_IsHalted()
TA260 001:937.746 - 0.496ms returns FALSE
TA260 001:937.756 JLINK_HasError()
TA260 001:939.252 JLINK_IsHalted()
TA260 001:939.737 - 0.485ms returns FALSE
TA260 001:939.744 JLINK_HasError()
TA260 001:941.249 JLINK_IsHalted()
TA260 001:941.701 - 0.451ms returns FALSE
TA260 001:941.707 JLINK_HasError()
TA260 001:943.248 JLINK_IsHalted()
TA260 001:943.736 - 0.487ms returns FALSE
TA260 001:943.742 JLINK_HasError()
TA260 001:945.766 JLINK_IsHalted()
TA260 001:946.268 - 0.501ms returns FALSE
TA260 001:946.275 JLINK_HasError()
TA260 001:947.752 JLINK_IsHalted()
TA260 001:948.260 - 0.507ms returns FALSE
TA260 001:948.266 JLINK_HasError()
TA260 001:949.533 JLINK_IsHalted()
TA260 001:950.018 - 0.484ms returns FALSE
TA260 001:950.025 JLINK_HasError()
TA260 001:952.052 JLINK_IsHalted()
TA260 001:952.599 - 0.547ms returns FALSE
TA260 001:952.605 JLINK_HasError()
TA260 001:954.529 JLINK_IsHalted()
TA260 001:954.986 - 0.456ms returns FALSE
TA260 001:954.992 JLINK_HasError()
TA260 001:956.032 JLINK_IsHalted()
TA260 001:956.474 - 0.441ms returns FALSE
TA260 001:956.480 JLINK_HasError()
TA260 001:958.034 JLINK_IsHalted()
TA260 001:958.539 - 0.505ms returns FALSE
TA260 001:958.546 JLINK_HasError()
TA260 001:960.038 JLINK_IsHalted()
TA260 001:960.496 - 0.458ms returns FALSE
TA260 001:960.502 JLINK_HasError()
TA260 001:962.034 JLINK_IsHalted()
TA260 001:962.510 - 0.476ms returns FALSE
TA260 001:962.516 JLINK_HasError()
TA260 001:964.540 JLINK_IsHalted()
TA260 001:965.034 - 0.494ms returns FALSE
TA260 001:965.041 JLINK_HasError()
TA260 001:966.540 JLINK_IsHalted()
TA260 001:967.019 - 0.479ms returns FALSE
TA260 001:967.026 JLINK_HasError()
TA260 001:968.539 JLINK_IsHalted()
TA260 001:969.006 - 0.467ms returns FALSE
TA260 001:969.012 JLINK_HasError()
TA260 001:970.542 JLINK_IsHalted()
TA260 001:971.058 - 0.515ms returns FALSE
TA260 001:971.067 JLINK_HasError()
TA260 001:973.042 JLINK_IsHalted()
TA260 001:973.520 - 0.477ms returns FALSE
TA260 001:973.533 JLINK_HasError()
TA260 001:975.044 JLINK_IsHalted()
TA260 001:975.495 - 0.451ms returns FALSE
TA260 001:975.501 JLINK_HasError()
TA260 001:977.049 JLINK_IsHalted()
TA260 001:977.601 - 0.551ms returns FALSE
TA260 001:977.611 JLINK_HasError()
TA260 001:978.954 JLINK_IsHalted()
TA260 001:979.472 - 0.517ms returns FALSE
TA260 001:979.478 JLINK_HasError()
TA260 001:980.957 JLINK_IsHalted()
TA260 001:981.474 - 0.517ms returns FALSE
TA260 001:981.480 JLINK_HasError()
TA260 001:982.957 JLINK_IsHalted()
TA260 001:983.471 - 0.514ms returns FALSE
TA260 001:983.477 JLINK_HasError()
TA260 001:985.465 JLINK_IsHalted()
TA260 001:985.931 - 0.465ms returns FALSE
TA260 001:985.940 JLINK_HasError()
TA260 001:987.466 JLINK_IsHalted()
TA260 001:987.914 - 0.447ms returns FALSE
TA260 001:987.922 JLINK_HasError()
TA260 001:989.464 JLINK_IsHalted()
TA260 001:989.946 - 0.482ms returns FALSE
TA260 001:989.955 JLINK_HasError()
TA260 001:991.470 JLINK_IsHalted()
TA260 001:991.942 - 0.471ms returns FALSE
TA260 001:991.949 JLINK_HasError()
TA260 001:993.965 JLINK_IsHalted()
TA260 001:994.461 - 0.496ms returns FALSE
TA260 001:994.468 JLINK_HasError()
TA260 001:995.968 JLINK_IsHalted()
TA260 001:996.463 - 0.494ms returns FALSE
TA260 001:996.469 JLINK_HasError()
TA260 001:997.968 JLINK_IsHalted()
TA260 001:998.464 - 0.495ms returns FALSE
TA260 001:998.471 JLINK_HasError()
TA260 001:999.550 JLINK_IsHalted()
TA260 002:000.032 - 0.481ms returns FALSE
TA260 002:000.038 JLINK_HasError()
TA260 002:002.053 JLINK_IsHalted()
TA260 002:002.620 - 0.566ms returns FALSE
TA260 002:002.637 JLINK_HasError()
TA260 002:004.567 JLINK_IsHalted()
TA260 002:005.069 - 0.502ms returns FALSE
TA260 002:005.076 JLINK_HasError()
TA260 002:006.567 JLINK_IsHalted()
TA260 002:007.037 - 0.470ms returns FALSE
TA260 002:007.045 JLINK_HasError()
TA260 002:008.564 JLINK_IsHalted()
TA260 002:009.117 - 0.552ms returns FALSE
TA260 002:009.134 JLINK_HasError()
TA260 002:010.567 JLINK_IsHalted()
TA260 002:010.999 - 0.431ms returns FALSE
TA260 002:011.009 JLINK_HasError()
TA260 002:013.069 JLINK_IsHalted()
TA260 002:013.610 - 0.541ms returns FALSE
TA260 002:013.618 JLINK_HasError()
TA260 002:015.070 JLINK_IsHalted()
TA260 002:015.511 - 0.441ms returns FALSE
TA260 002:015.517 JLINK_HasError()
TA260 002:017.079 JLINK_IsHalted()
TA260 002:017.606 - 0.526ms returns FALSE
TA260 002:017.616 JLINK_HasError()
TA260 002:019.070 JLINK_IsHalted()
TA260 002:019.511 - 0.440ms returns FALSE
TA260 002:019.519 JLINK_HasError()
TA260 002:021.071 JLINK_IsHalted()
TA260 002:021.643 - 0.572ms returns FALSE
TA260 002:021.652 JLINK_HasError()
TA260 002:023.081 JLINK_IsHalted()
TA260 002:023.615 - 0.533ms returns FALSE
TA260 002:023.622 JLINK_HasError()
TA260 002:025.584 JLINK_IsHalted()
TA260 002:026.070 - 0.486ms returns FALSE
TA260 002:026.078 JLINK_HasError()
TA260 002:027.585 JLINK_IsHalted()
TA260 002:028.045 - 0.459ms returns FALSE
TA260 002:028.060 JLINK_HasError()
TA260 002:029.576 JLINK_IsHalted()
TA260 002:030.092 - 0.516ms returns FALSE
TA260 002:030.100 JLINK_HasError()
TA260 002:031.581 JLINK_IsHalted()
TA260 002:032.073 - 0.492ms returns FALSE
TA260 002:032.079 JLINK_HasError()
TA260 002:034.087 JLINK_IsHalted()
TA260 002:034.602 - 0.515ms returns FALSE
TA260 002:034.608 JLINK_HasError()
TA260 002:037.096 JLINK_IsHalted()
TA260 002:037.609 - 0.512ms returns FALSE
TA260 002:037.624 JLINK_HasError()
TA260 002:039.090 JLINK_IsHalted()
TA260 002:039.619 - 0.528ms returns FALSE
TA260 002:039.630 JLINK_HasError()
TA260 002:041.092 JLINK_IsHalted()
TA260 002:041.623 - 0.530ms returns FALSE
TA260 002:041.630 JLINK_HasError()
TA260 002:043.100 JLINK_IsHalted()
TA260 002:043.616 - 0.516ms returns FALSE
TA260 002:043.623 JLINK_HasError()
TA260 002:045.601 JLINK_IsHalted()
TA260 002:046.151 - 0.549ms returns FALSE
TA260 002:046.159 JLINK_HasError()
TA260 002:047.602 JLINK_IsHalted()
TA260 002:048.098 - 0.495ms returns FALSE
TA260 002:048.109 JLINK_HasError()
TA260 002:049.602 JLINK_IsHalted()
TA260 002:050.102 - 0.500ms returns FALSE
TA260 002:050.113 JLINK_HasError()
TA260 002:051.598 JLINK_IsHalted()
TA260 002:052.095 - 0.497ms returns FALSE
TA260 002:052.104 JLINK_HasError()
TA260 002:054.104 JLINK_IsHalted()
TA260 002:054.617 - 0.513ms returns FALSE
TA260 002:054.628 JLINK_HasError()
TA260 002:056.607 JLINK_IsHalted()
TA260 002:057.097 - 0.490ms returns FALSE
TA260 002:057.107 JLINK_HasError()
TA260 002:058.630 JLINK_IsHalted()
TA260 002:059.120 - 0.490ms returns FALSE
TA260 002:059.128 JLINK_HasError()
TA260 002:060.605 JLINK_IsHalted()
TA260 002:061.117 - 0.511ms returns FALSE
TA260 002:061.125 JLINK_HasError()
TA260 002:063.119 JLINK_IsHalted()
TA260 002:063.605 - 0.485ms returns FALSE
TA260 002:063.612 JLINK_HasError()
TA260 002:065.144 JLINK_IsHalted()
TA260 002:065.605 - 0.460ms returns FALSE
TA260 002:065.617 JLINK_HasError()
TA260 002:067.126 JLINK_IsHalted()
TA260 002:067.619 - 0.493ms returns FALSE
TA260 002:067.630 JLINK_HasError()
TA260 002:069.130 JLINK_IsHalted()
TA260 002:069.650 - 0.519ms returns FALSE
TA260 002:069.659 JLINK_HasError()
TA260 002:071.117 JLINK_IsHalted()
TA260 002:071.649 - 0.532ms returns FALSE
TA260 002:071.657 JLINK_HasError()
TA260 002:073.126 JLINK_IsHalted()
TA260 002:073.603 - 0.477ms returns FALSE
TA260 002:073.637 JLINK_HasError()
TA260 002:075.631 JLINK_IsHalted()
TA260 002:076.140 - 0.509ms returns FALSE
TA260 002:076.148 JLINK_HasError()
TA260 002:077.626 JLINK_IsHalted()
TA260 002:078.097 - 0.470ms returns FALSE
TA260 002:078.108 JLINK_HasError()
TA260 002:079.623 JLINK_IsHalted()
TA260 002:080.138 - 0.515ms returns FALSE
TA260 002:080.145 JLINK_HasError()
TA260 002:081.627 JLINK_IsHalted()
TA260 002:082.106 - 0.479ms returns FALSE
TA260 002:082.113 JLINK_HasError()
TA260 002:084.136 JLINK_IsHalted()
TA260 002:084.663 - 0.527ms returns FALSE
TA260 002:084.673 JLINK_HasError()
TA260 002:086.133 JLINK_IsHalted()
TA260 002:086.618 - 0.485ms returns FALSE
TA260 002:086.629 JLINK_HasError()
TA260 002:088.136 JLINK_IsHalted()
TA260 002:088.661 - 0.524ms returns FALSE
TA260 002:088.668 JLINK_HasError()
TA260 002:090.138 JLINK_IsHalted()
TA260 002:090.622 - 0.484ms returns FALSE
TA260 002:090.629 JLINK_HasError()
TA260 002:092.134 JLINK_IsHalted()
TA260 002:092.604 - 0.469ms returns FALSE
TA260 002:092.613 JLINK_HasError()
TA260 002:094.642 JLINK_IsHalted()
TA260 002:095.073 - 0.431ms returns FALSE
TA260 002:095.084 JLINK_HasError()
TA260 002:096.649 JLINK_IsHalted()
TA260 002:097.135 - 0.486ms returns FALSE
TA260 002:097.147 JLINK_HasError()
TA260 002:098.642 JLINK_IsHalted()
TA260 002:099.072 - 0.429ms returns FALSE
TA260 002:099.079 JLINK_HasError()
TA260 002:100.644 JLINK_IsHalted()
TA260 002:101.104 - 0.460ms returns FALSE
TA260 002:101.111 JLINK_HasError()
TA260 002:103.156 JLINK_IsHalted()
TA260 002:103.651 - 0.494ms returns FALSE
TA260 002:103.657 JLINK_HasError()
TA260 002:105.148 JLINK_IsHalted()
TA260 002:105.702 - 0.553ms returns FALSE
TA260 002:105.709 JLINK_HasError()
TA260 002:107.155 JLINK_IsHalted()
TA260 002:107.619 - 0.463ms returns FALSE
TA260 002:107.628 JLINK_HasError()
TA260 002:109.153 JLINK_IsHalted()
TA260 002:109.616 - 0.462ms returns FALSE
TA260 002:109.624 JLINK_HasError()
TA260 002:111.150 JLINK_IsHalted()
TA260 002:111.647 - 0.496ms returns FALSE
TA260 002:111.653 JLINK_HasError()
TA260 002:113.150 JLINK_IsHalted()
TA260 002:113.617 - 0.467ms returns FALSE
TA260 002:113.623 JLINK_HasError()
TA260 002:115.659 JLINK_IsHalted()
TA260 002:116.104 - 0.445ms returns FALSE
TA260 002:116.114 JLINK_HasError()
TA260 002:117.660 JLINK_IsHalted()
TA260 002:118.151 - 0.490ms returns FALSE
TA260 002:118.162 JLINK_HasError()
TA260 002:119.655 JLINK_IsHalted()
TA260 002:120.137 - 0.482ms returns FALSE
TA260 002:120.144 JLINK_HasError()
TA260 002:121.657 JLINK_IsHalted()
TA260 002:122.136 - 0.478ms returns FALSE
TA260 002:122.143 JLINK_HasError()
TA260 002:124.159 JLINK_IsHalted()
TA260 002:124.618 - 0.458ms returns FALSE
TA260 002:124.625 JLINK_HasError()
TA260 002:126.166 JLINK_IsHalted()
TA260 002:126.602 - 0.435ms returns FALSE
TA260 002:126.608 JLINK_HasError()
TA260 002:128.165 JLINK_IsHalted()
TA260 002:128.620 - 0.454ms returns FALSE
TA260 002:128.634 JLINK_HasError()
TA260 002:130.162 JLINK_IsHalted()
TA260 002:130.604 - 0.441ms returns FALSE
TA260 002:130.613 JLINK_HasError()
TA260 002:132.169 JLINK_IsHalted()
TA260 002:132.706 - 0.536ms returns FALSE
TA260 002:132.715 JLINK_HasError()
TA260 002:134.668 JLINK_IsHalted()
TA260 002:135.159 - 0.491ms returns FALSE
TA260 002:135.173 JLINK_HasError()
TA260 002:136.672 JLINK_IsHalted()
TA260 002:137.186 - 0.513ms returns FALSE
TA260 002:137.195 JLINK_HasError()
TA260 002:138.674 JLINK_IsHalted()
TA260 002:139.183 - 0.508ms returns FALSE
TA260 002:139.191 JLINK_HasError()
TA260 002:140.671 JLINK_IsHalted()
TA260 002:141.113 - 0.441ms returns FALSE
TA260 002:141.120 JLINK_HasError()
TA260 002:143.179 JLINK_IsHalted()
TA260 002:143.713 - 0.533ms returns FALSE
TA260 002:143.727 JLINK_HasError()
TA260 002:146.182 JLINK_IsHalted()
TA260 002:146.709 - 0.526ms returns FALSE
TA260 002:146.719 JLINK_HasError()
TA260 002:148.184 JLINK_IsHalted()
TA260 002:148.709 - 0.524ms returns FALSE
TA260 002:148.720 JLINK_HasError()
TA260 002:150.176 JLINK_IsHalted()
TA260 002:150.704 - 0.527ms returns FALSE
TA260 002:150.712 JLINK_HasError()
TA260 002:152.183 JLINK_IsHalted()
TA260 002:152.695 - 0.511ms returns FALSE
TA260 002:152.704 JLINK_HasError()
TA260 002:154.687 JLINK_IsHalted()
TA260 002:155.204 - 0.516ms returns FALSE
TA260 002:155.218 JLINK_HasError()
TA260 002:157.189 JLINK_IsHalted()
TA260 002:157.709 - 0.520ms returns FALSE
TA260 002:157.718 JLINK_HasError()
TA260 002:159.189 JLINK_IsHalted()
TA260 002:159.708 - 0.519ms returns FALSE
TA260 002:159.718 JLINK_HasError()
TA260 002:161.188 JLINK_IsHalted()
TA260 002:161.646 - 0.458ms returns FALSE
TA260 002:161.653 JLINK_HasError()
TA260 002:163.189 JLINK_IsHalted()
TA260 002:163.698 - 0.508ms returns FALSE
TA260 002:163.716 JLINK_HasError()
TA260 002:165.701 JLINK_IsHalted()
TA260 002:166.208 - 0.507ms returns FALSE
TA260 002:166.219 JLINK_HasError()
TA260 002:167.699 JLINK_IsHalted()
TA260 002:168.199 - 0.500ms returns FALSE
TA260 002:168.210 JLINK_HasError()
TA260 002:169.699 JLINK_IsHalted()
TA260 002:170.159 - 0.460ms returns FALSE
TA260 002:170.167 JLINK_HasError()
TA260 002:171.696 JLINK_IsHalted()
TA260 002:172.149 - 0.452ms returns FALSE
TA260 002:172.155 JLINK_HasError()
TA260 002:173.205 JLINK_IsHalted()
TA260 002:173.718 - 0.513ms returns FALSE
TA260 002:173.725 JLINK_HasError()
TA260 002:175.205 JLINK_IsHalted()
TA260 002:175.705 - 0.500ms returns FALSE
TA260 002:175.714 JLINK_HasError()
TA260 002:177.204 JLINK_IsHalted()
TA260 002:177.696 - 0.491ms returns FALSE
TA260 002:177.706 JLINK_HasError()
TA260 002:179.203 JLINK_IsHalted()
TA260 002:179.739 - 0.535ms returns FALSE
TA260 002:179.747 JLINK_HasError()
TA260 002:181.207 JLINK_IsHalted()
TA260 002:181.724 - 0.517ms returns FALSE
TA260 002:181.731 JLINK_HasError()
TA260 002:183.205 JLINK_IsHalted()
TA260 002:183.693 - 0.487ms returns FALSE
TA260 002:183.702 JLINK_HasError()
TA260 002:185.713 JLINK_IsHalted()
TA260 002:186.167 - 0.454ms returns FALSE
TA260 002:186.177 JLINK_HasError()
TA260 002:187.712 JLINK_IsHalted()
TA260 002:188.208 - 0.495ms returns FALSE
TA260 002:188.219 JLINK_HasError()
TA260 002:189.712 JLINK_IsHalted()
TA260 002:190.298 - 0.586ms returns FALSE
TA260 002:190.317 JLINK_HasError()
TA260 002:191.709 JLINK_IsHalted()
TA260 002:192.237 - 0.527ms returns FALSE
TA260 002:192.245 JLINK_HasError()
TA260 002:194.214 JLINK_IsHalted()
TA260 002:194.693 - 0.479ms returns FALSE
TA260 002:194.704 JLINK_HasError()
TA260 002:196.220 JLINK_IsHalted()
TA260 002:196.715 - 0.495ms returns FALSE
TA260 002:196.724 JLINK_HasError()
TA260 002:198.215 JLINK_IsHalted()
TA260 002:198.692 - 0.477ms returns FALSE
TA260 002:198.701 JLINK_HasError()
TA260 002:200.216 JLINK_IsHalted()
TA260 002:200.705 - 0.489ms returns FALSE
TA260 002:200.712 JLINK_HasError()
TA260 002:202.215 JLINK_IsHalted()
TA260 002:202.692 - 0.476ms returns FALSE
TA260 002:202.699 JLINK_HasError()
TA260 002:204.721 JLINK_IsHalted()
TA260 002:205.237 - 0.515ms returns FALSE
TA260 002:205.244 JLINK_HasError()
TA260 002:206.726 JLINK_IsHalted()
TA260 002:207.160 - 0.434ms returns FALSE
TA260 002:207.168 JLINK_HasError()
TA260 002:208.723 JLINK_IsHalted()
TA260 002:209.263 - 0.540ms returns FALSE
TA260 002:209.272 JLINK_HasError()
TA260 002:210.724 JLINK_IsHalted()
TA260 002:211.192 - 0.468ms returns FALSE
TA260 002:211.199 JLINK_HasError()
TA260 002:213.225 JLINK_IsHalted()
TA260 002:213.735 - 0.510ms returns FALSE
TA260 002:213.741 JLINK_HasError()
TA260 002:215.228 JLINK_IsHalted()
TA260 002:215.752 - 0.524ms returns FALSE
TA260 002:215.760 JLINK_HasError()
TA260 002:217.232 JLINK_IsHalted()
TA260 002:217.692 - 0.460ms returns FALSE
TA260 002:217.700 JLINK_HasError()
TA260 002:219.229 JLINK_IsHalted()
TA260 002:219.738 - 0.509ms returns FALSE
TA260 002:219.746 JLINK_HasError()
TA260 002:221.231 JLINK_IsHalted()
TA260 002:221.664 - 0.432ms returns FALSE
TA260 002:221.676 JLINK_HasError()
TA260 002:222.923 JLINK_IsHalted()
TA260 002:223.477 - 0.554ms returns FALSE
TA260 002:223.487 JLINK_HasError()
TA260 002:224.926 JLINK_IsHalted()
TA260 002:225.466 - 0.540ms returns FALSE
TA260 002:225.475 JLINK_HasError()
TA260 002:226.934 JLINK_IsHalted()
TA260 002:227.475 - 0.540ms returns FALSE
TA260 002:227.482 JLINK_HasError()
TA260 002:228.928 JLINK_IsHalted()
TA260 002:229.474 - 0.546ms returns FALSE
TA260 002:229.484 JLINK_HasError()
TA260 002:230.932 JLINK_IsHalted()
TA260 002:231.466 - 0.534ms returns FALSE
TA260 002:231.481 JLINK_HasError()
TA260 002:232.930 JLINK_IsHalted()
TA260 002:233.477 - 0.547ms returns FALSE
TA260 002:233.485 JLINK_HasError()
TA260 002:235.435 JLINK_IsHalted()
TA260 002:235.897 - 0.461ms returns FALSE
TA260 002:235.906 JLINK_HasError()
TA260 002:237.439 JLINK_IsHalted()
TA260 002:237.897 - 0.457ms returns FALSE
TA260 002:237.906 JLINK_HasError()
TA260 002:239.438 JLINK_IsHalted()
TA260 002:239.935 - 0.496ms returns FALSE
TA260 002:239.944 JLINK_HasError()
TA260 002:241.437 JLINK_IsHalted()
TA260 002:241.944 - 0.506ms returns FALSE
TA260 002:241.951 JLINK_HasError()
TA260 002:243.943 JLINK_IsHalted()
TA260 002:244.473 - 0.529ms returns FALSE
TA260 002:244.480 JLINK_HasError()
TA260 002:245.940 JLINK_IsHalted()
TA260 002:246.466 - 0.525ms returns FALSE
TA260 002:246.473 JLINK_HasError()
TA260 002:247.946 JLINK_IsHalted()
TA260 002:248.475 - 0.529ms returns FALSE
TA260 002:248.484 JLINK_HasError()
TA260 002:249.941 JLINK_IsHalted()
TA260 002:250.465 - 0.523ms returns FALSE
TA260 002:250.472 JLINK_HasError()
TA260 002:251.945 JLINK_IsHalted()
TA260 002:252.479 - 0.532ms returns FALSE
TA260 002:252.490 JLINK_HasError()
TA260 002:255.956 JLINK_IsHalted()
TA260 002:256.474 - 0.518ms returns FALSE
TA260 002:256.492 JLINK_HasError()
TA260 002:257.954 JLINK_IsHalted()
TA260 002:258.476 - 0.520ms returns FALSE
TA260 002:258.487 JLINK_HasError()
TA260 002:259.958 JLINK_IsHalted()
TA260 002:260.375 - 0.416ms returns FALSE
TA260 002:260.384 JLINK_HasError()
TA260 002:261.958 JLINK_IsHalted()
TA260 002:262.473 - 0.514ms returns FALSE
TA260 002:262.482 JLINK_HasError()
TA260 002:264.460 JLINK_IsHalted()
TA260 002:265.003 - 0.543ms returns FALSE
TA260 002:265.015 JLINK_HasError()
TA260 002:266.463 JLINK_IsHalted()
TA260 002:266.932 - 0.468ms returns FALSE
TA260 002:266.942 JLINK_HasError()
TA260 002:268.460 JLINK_IsHalted()
TA260 002:270.752   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:271.265 - 2.805ms returns TRUE
TA260 002:271.274 JLINK_ReadReg(R15 (PC))
TA260 002:271.281 - 0.006ms returns 0x20000000
TA260 002:271.285 JLINK_ClrBPEx(BPHandle = 0x00000009)
TA260 002:271.289 - 0.004ms returns 0x00
TA260 002:271.294 JLINK_ReadReg(R0)
TA260 002:271.297 - 0.003ms returns 0x00000000
TA260 002:271.650 JLINK_HasError()
TA260 002:271.661 JLINK_WriteReg(R0, 0x00000001)
TA260 002:271.667 - 0.005ms returns 0
TA260 002:271.671 JLINK_WriteReg(R1, 0x00004000)
TA260 002:271.675 - 0.003ms returns 0
TA260 002:271.679 JLINK_WriteReg(R2, 0x000000FF)
TA260 002:271.683 - 0.003ms returns 0
TA260 002:271.687 JLINK_WriteReg(R3, 0x00000000)
TA260 002:271.697 - 0.010ms returns 0
TA260 002:271.702 JLINK_WriteReg(R4, 0x00000000)
TA260 002:271.705 - 0.003ms returns 0
TA260 002:271.709 JLINK_WriteReg(R5, 0x00000000)
TA260 002:271.712 - 0.003ms returns 0
TA260 002:271.716 JLINK_WriteReg(R6, 0x00000000)
TA260 002:271.725 - 0.008ms returns 0
TA260 002:271.729 JLINK_WriteReg(R7, 0x00000000)
TA260 002:271.732 - 0.003ms returns 0
TA260 002:271.737 JLINK_WriteReg(R8, 0x00000000)
TA260 002:271.740 - 0.003ms returns 0
TA260 002:271.744 JLINK_WriteReg(R9, 0x20000180)
TA260 002:271.748 - 0.003ms returns 0
TA260 002:271.752 JLINK_WriteReg(R10, 0x00000000)
TA260 002:271.756 - 0.003ms returns 0
TA260 002:271.760 JLINK_WriteReg(R11, 0x00000000)
TA260 002:271.763 - 0.003ms returns 0
TA260 002:271.767 JLINK_WriteReg(R12, 0x00000000)
TA260 002:271.771 - 0.003ms returns 0
TA260 002:271.775 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:271.778 - 0.003ms returns 0
TA260 002:271.782 JLINK_WriteReg(R14, 0x20000001)
TA260 002:271.786 - 0.003ms returns 0
TA260 002:271.790 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 002:271.793 - 0.003ms returns 0
TA260 002:271.798 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:271.801 - 0.003ms returns 0
TA260 002:271.805 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:271.809 - 0.003ms returns 0
TA260 002:271.813 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:271.816 - 0.003ms returns 0
TA260 002:271.821 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:271.824 - 0.003ms returns 0
TA260 002:271.829 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:271.833 - 0.004ms returns 0x0000000A
TA260 002:271.837 JLINK_Go()
TA260 002:271.846   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:274.573 - 2.735ms 
TA260 002:274.594 JLINK_IsHalted()
TA260 002:276.893   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:277.355 - 2.760ms returns TRUE
TA260 002:277.364 JLINK_ReadReg(R15 (PC))
TA260 002:277.369 - 0.005ms returns 0x20000000
TA260 002:277.374 JLINK_ClrBPEx(BPHandle = 0x0000000A)
TA260 002:277.378 - 0.003ms returns 0x00
TA260 002:277.382 JLINK_ReadReg(R0)
TA260 002:277.386 - 0.004ms returns 0x00000000
TA260 002:333.814 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 002:333.827   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 002:333.844   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 002:335.710 - 1.896ms returns 0x184
TA260 002:335.752 JLINK_HasError()
TA260 002:335.758 JLINK_WriteReg(R0, 0x08000000)
TA260 002:335.764 - 0.006ms returns 0
TA260 002:335.769 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 002:335.772 - 0.003ms returns 0
TA260 002:335.776 JLINK_WriteReg(R2, 0x00000002)
TA260 002:335.780 - 0.003ms returns 0
TA260 002:335.784 JLINK_WriteReg(R3, 0x00000000)
TA260 002:335.787 - 0.003ms returns 0
TA260 002:335.791 JLINK_WriteReg(R4, 0x00000000)
TA260 002:335.795 - 0.003ms returns 0
TA260 002:335.799 JLINK_WriteReg(R5, 0x00000000)
TA260 002:335.802 - 0.003ms returns 0
TA260 002:335.806 JLINK_WriteReg(R6, 0x00000000)
TA260 002:335.810 - 0.003ms returns 0
TA260 002:335.814 JLINK_WriteReg(R7, 0x00000000)
TA260 002:335.817 - 0.003ms returns 0
TA260 002:335.821 JLINK_WriteReg(R8, 0x00000000)
TA260 002:335.824 - 0.003ms returns 0
TA260 002:335.828 JLINK_WriteReg(R9, 0x20000180)
TA260 002:335.832 - 0.003ms returns 0
TA260 002:335.836 JLINK_WriteReg(R10, 0x00000000)
TA260 002:335.840 - 0.003ms returns 0
TA260 002:335.844 JLINK_WriteReg(R11, 0x00000000)
TA260 002:335.847 - 0.003ms returns 0
TA260 002:335.852 JLINK_WriteReg(R12, 0x00000000)
TA260 002:335.855 - 0.003ms returns 0
TA260 002:335.864 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:335.868 - 0.004ms returns 0
TA260 002:335.872 JLINK_WriteReg(R14, 0x20000001)
TA260 002:335.876 - 0.003ms returns 0
TA260 002:335.880 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 002:335.884 - 0.003ms returns 0
TA260 002:335.888 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:335.891 - 0.003ms returns 0
TA260 002:335.896 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:335.899 - 0.003ms returns 0
TA260 002:335.903 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:335.907 - 0.003ms returns 0
TA260 002:335.911 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:335.914 - 0.003ms returns 0
TA260 002:335.919 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:335.927   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:336.494 - 0.575ms returns 0x0000000B
TA260 002:336.514 JLINK_Go()
TA260 002:336.521   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 002:337.046   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:339.844 - 3.329ms 
TA260 002:339.859 JLINK_IsHalted()
TA260 002:342.203   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:342.746 - 2.886ms returns TRUE
TA260 002:342.756 JLINK_ReadReg(R15 (PC))
TA260 002:342.761 - 0.006ms returns 0x20000000
TA260 002:342.766 JLINK_ClrBPEx(BPHandle = 0x0000000B)
TA260 002:342.770 - 0.004ms returns 0x00
TA260 002:342.775 JLINK_ReadReg(R0)
TA260 002:342.778 - 0.003ms returns 0x00000000
TA260 002:343.076 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:343.085   Data:  18 18 00 20 C1 01 00 08 B1 2A 00 08 95 27 00 08 ...
TA260 002:343.099   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:345.762 - 2.684ms returns 0x27C
TA260 002:345.775 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:345.780   Data:  10 B5 13 46 0A 46 04 46 19 46 FF F7 F0 FF 20 46 ...
TA260 002:345.794   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:347.677 - 1.901ms returns 0x184
TA260 002:347.700 JLINK_HasError()
TA260 002:347.706 JLINK_WriteReg(R0, 0x08000000)
TA260 002:347.712 - 0.006ms returns 0
TA260 002:347.717 JLINK_WriteReg(R1, 0x00000400)
TA260 002:347.721 - 0.004ms returns 0
TA260 002:347.725 JLINK_WriteReg(R2, 0x20000184)
TA260 002:347.729 - 0.004ms returns 0
TA260 002:347.733 JLINK_WriteReg(R3, 0x00000000)
TA260 002:347.736 - 0.003ms returns 0
TA260 002:347.740 JLINK_WriteReg(R4, 0x00000000)
TA260 002:347.744 - 0.003ms returns 0
TA260 002:347.748 JLINK_WriteReg(R5, 0x00000000)
TA260 002:347.751 - 0.003ms returns 0
TA260 002:347.755 JLINK_WriteReg(R6, 0x00000000)
TA260 002:347.758 - 0.003ms returns 0
TA260 002:347.762 JLINK_WriteReg(R7, 0x00000000)
TA260 002:347.766 - 0.003ms returns 0
TA260 002:347.770 JLINK_WriteReg(R8, 0x00000000)
TA260 002:347.773 - 0.003ms returns 0
TA260 002:347.777 JLINK_WriteReg(R9, 0x20000180)
TA260 002:347.781 - 0.003ms returns 0
TA260 002:347.790 JLINK_WriteReg(R10, 0x00000000)
TA260 002:347.794 - 0.003ms returns 0
TA260 002:347.798 JLINK_WriteReg(R11, 0x00000000)
TA260 002:347.801 - 0.003ms returns 0
TA260 002:347.805 JLINK_WriteReg(R12, 0x00000000)
TA260 002:347.809 - 0.003ms returns 0
TA260 002:347.813 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:347.817 - 0.004ms returns 0
TA260 002:347.821 JLINK_WriteReg(R14, 0x20000001)
TA260 002:347.825 - 0.003ms returns 0
TA260 002:347.829 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:347.834 - 0.005ms returns 0
TA260 002:347.838 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:347.842 - 0.003ms returns 0
TA260 002:347.846 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:347.849 - 0.003ms returns 0
TA260 002:347.853 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:347.857 - 0.003ms returns 0
TA260 002:347.861 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:347.864 - 0.003ms returns 0
TA260 002:347.869 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:347.874 - 0.005ms returns 0x0000000C
TA260 002:347.878 JLINK_Go()
TA260 002:347.888   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:350.620 - 2.742ms 
TA260 002:350.631 JLINK_IsHalted()
TA260 002:351.143 - 0.511ms returns FALSE
TA260 002:351.156 JLINK_HasError()
TA260 002:356.039 JLINK_IsHalted()
TA260 002:358.506   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:359.003 - 2.964ms returns TRUE
TA260 002:359.019 JLINK_ReadReg(R15 (PC))
TA260 002:359.024 - 0.005ms returns 0x20000000
TA260 002:359.063 JLINK_ClrBPEx(BPHandle = 0x0000000C)
TA260 002:359.069 - 0.006ms returns 0x00
TA260 002:359.074 JLINK_ReadReg(R0)
TA260 002:359.078 - 0.003ms returns 0x00000000
TA260 002:359.445 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:359.454   Data:  5B D0 C3 F3 0A 54 C1 F3 0A 55 2C 44 A4 F2 F3 34 ...
TA260 002:359.464   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:362.110 - 2.664ms returns 0x27C
TA260 002:362.122 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:362.126   Data:  01 02 06 D0 0A 0D A2 F5 60 72 C1 F3 13 01 00 2A ...
TA260 002:362.187   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:364.079 - 1.957ms returns 0x184
TA260 002:364.091 JLINK_HasError()
TA260 002:364.098 JLINK_WriteReg(R0, 0x08000400)
TA260 002:364.104 - 0.006ms returns 0
TA260 002:364.110 JLINK_WriteReg(R1, 0x00000400)
TA260 002:364.127 - 0.018ms returns 0
TA260 002:364.135 JLINK_WriteReg(R2, 0x20000184)
TA260 002:364.139 - 0.004ms returns 0
TA260 002:364.144 JLINK_WriteReg(R3, 0x00000000)
TA260 002:364.149 - 0.004ms returns 0
TA260 002:364.154 JLINK_WriteReg(R4, 0x00000000)
TA260 002:364.158 - 0.004ms returns 0
TA260 002:364.164 JLINK_WriteReg(R5, 0x00000000)
TA260 002:364.168 - 0.004ms returns 0
TA260 002:364.174 JLINK_WriteReg(R6, 0x00000000)
TA260 002:364.177 - 0.003ms returns 0
TA260 002:364.181 JLINK_WriteReg(R7, 0x00000000)
TA260 002:364.185 - 0.003ms returns 0
TA260 002:364.189 JLINK_WriteReg(R8, 0x00000000)
TA260 002:364.192 - 0.003ms returns 0
TA260 002:364.196 JLINK_WriteReg(R9, 0x20000180)
TA260 002:364.200 - 0.003ms returns 0
TA260 002:364.204 JLINK_WriteReg(R10, 0x00000000)
TA260 002:364.207 - 0.003ms returns 0
TA260 002:364.211 JLINK_WriteReg(R11, 0x00000000)
TA260 002:364.214 - 0.003ms returns 0
TA260 002:364.218 JLINK_WriteReg(R12, 0x00000000)
TA260 002:364.222 - 0.003ms returns 0
TA260 002:364.226 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:364.230 - 0.004ms returns 0
TA260 002:364.234 JLINK_WriteReg(R14, 0x20000001)
TA260 002:364.237 - 0.003ms returns 0
TA260 002:364.241 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:364.245 - 0.003ms returns 0
TA260 002:364.249 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:364.252 - 0.003ms returns 0
TA260 002:364.256 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:364.260 - 0.003ms returns 0
TA260 002:364.265 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:364.268 - 0.003ms returns 0
TA260 002:364.272 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:364.276 - 0.003ms returns 0
TA260 002:364.280 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:364.285 - 0.004ms returns 0x0000000D
TA260 002:364.289 JLINK_Go()
TA260 002:364.297   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:367.037 - 2.747ms 
TA260 002:367.047 JLINK_IsHalted()
TA260 002:367.617 - 0.570ms returns FALSE
TA260 002:367.625 JLINK_HasError()
TA260 002:369.042 JLINK_IsHalted()
TA260 002:369.570 - 0.527ms returns FALSE
TA260 002:369.578 JLINK_HasError()
TA260 002:371.050 JLINK_IsHalted()
TA260 002:373.387   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:373.870 - 2.819ms returns TRUE
TA260 002:373.881 JLINK_ReadReg(R15 (PC))
TA260 002:373.886 - 0.005ms returns 0x20000000
TA260 002:373.891 JLINK_ClrBPEx(BPHandle = 0x0000000D)
TA260 002:373.895 - 0.003ms returns 0x00
TA260 002:373.899 JLINK_ReadReg(R0)
TA260 002:373.904 - 0.004ms returns 0x00000000
TA260 002:374.269 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:374.277   Data:  F0 4D 92 46 9B 46 11 B1 B1 FA 81 F2 02 E0 B0 FA ...
TA260 002:374.287   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:376.954 - 2.684ms returns 0x27C
TA260 002:376.969 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:376.973   Data:  DF FD 20 46 4F F4 00 51 00 22 00 F0 D9 FD 01 20 ...
TA260 002:376.984   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:378.854 - 1.884ms returns 0x184
TA260 002:378.872 JLINK_HasError()
TA260 002:378.879 JLINK_WriteReg(R0, 0x08000800)
TA260 002:378.885 - 0.006ms returns 0
TA260 002:378.889 JLINK_WriteReg(R1, 0x00000400)
TA260 002:378.892 - 0.003ms returns 0
TA260 002:378.896 JLINK_WriteReg(R2, 0x20000184)
TA260 002:378.900 - 0.003ms returns 0
TA260 002:378.904 JLINK_WriteReg(R3, 0x00000000)
TA260 002:378.908 - 0.003ms returns 0
TA260 002:378.912 JLINK_WriteReg(R4, 0x00000000)
TA260 002:378.915 - 0.003ms returns 0
TA260 002:378.919 JLINK_WriteReg(R5, 0x00000000)
TA260 002:378.923 - 0.003ms returns 0
TA260 002:378.927 JLINK_WriteReg(R6, 0x00000000)
TA260 002:378.931 - 0.003ms returns 0
TA260 002:378.935 JLINK_WriteReg(R7, 0x00000000)
TA260 002:378.938 - 0.003ms returns 0
TA260 002:378.942 JLINK_WriteReg(R8, 0x00000000)
TA260 002:378.950 - 0.007ms returns 0
TA260 002:378.956 JLINK_WriteReg(R9, 0x20000180)
TA260 002:378.959 - 0.003ms returns 0
TA260 002:378.963 JLINK_WriteReg(R10, 0x00000000)
TA260 002:378.967 - 0.003ms returns 0
TA260 002:378.971 JLINK_WriteReg(R11, 0x00000000)
TA260 002:378.975 - 0.003ms returns 0
TA260 002:378.979 JLINK_WriteReg(R12, 0x00000000)
TA260 002:378.982 - 0.003ms returns 0
TA260 002:378.986 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:378.990 - 0.004ms returns 0
TA260 002:378.994 JLINK_WriteReg(R14, 0x20000001)
TA260 002:378.998 - 0.003ms returns 0
TA260 002:379.002 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:379.006 - 0.003ms returns 0
TA260 002:379.010 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:379.013 - 0.003ms returns 0
TA260 002:379.017 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:379.021 - 0.003ms returns 0
TA260 002:379.025 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:379.028 - 0.003ms returns 0
TA260 002:379.032 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:379.036 - 0.003ms returns 0
TA260 002:379.041 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:379.046 - 0.004ms returns 0x0000000E
TA260 002:379.050 JLINK_Go()
TA260 002:379.061   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:381.807 - 2.756ms 
TA260 002:381.814 JLINK_IsHalted()
TA260 002:382.286 - 0.472ms returns FALSE
TA260 002:382.291 JLINK_HasError()
TA260 002:384.057 JLINK_IsHalted()
TA260 002:384.519 - 0.461ms returns FALSE
TA260 002:384.534 JLINK_HasError()
TA260 002:386.062 JLINK_IsHalted()
TA260 002:388.390   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:388.823 - 2.760ms returns TRUE
TA260 002:388.833 JLINK_ReadReg(R15 (PC))
TA260 002:388.839 - 0.005ms returns 0x20000000
TA260 002:388.843 JLINK_ClrBPEx(BPHandle = 0x0000000E)
TA260 002:388.847 - 0.004ms returns 0x00
TA260 002:388.852 JLINK_ReadReg(R0)
TA260 002:388.855 - 0.003ms returns 0x00000000
TA260 002:389.409 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:389.429   Data:  BD E8 F0 40 00 F0 1A BD 2D E9 F0 4F 81 B0 41 F6 ...
TA260 002:389.443   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:392.072 - 2.663ms returns 0x27C
TA260 002:392.094 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:392.098   Data:  05 F0 01 02 30 46 4F F4 80 51 00 F0 D9 FB 20 46 ...
TA260 002:392.110   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:393.990 - 1.895ms returns 0x184
TA260 002:394.005 JLINK_HasError()
TA260 002:394.011 JLINK_WriteReg(R0, 0x08000C00)
TA260 002:394.016 - 0.005ms returns 0
TA260 002:394.020 JLINK_WriteReg(R1, 0x00000400)
TA260 002:394.024 - 0.003ms returns 0
TA260 002:394.028 JLINK_WriteReg(R2, 0x20000184)
TA260 002:394.031 - 0.003ms returns 0
TA260 002:394.035 JLINK_WriteReg(R3, 0x00000000)
TA260 002:394.039 - 0.003ms returns 0
TA260 002:394.043 JLINK_WriteReg(R4, 0x00000000)
TA260 002:394.046 - 0.003ms returns 0
TA260 002:394.050 JLINK_WriteReg(R5, 0x00000000)
TA260 002:394.054 - 0.003ms returns 0
TA260 002:394.058 JLINK_WriteReg(R6, 0x00000000)
TA260 002:394.061 - 0.003ms returns 0
TA260 002:394.065 JLINK_WriteReg(R7, 0x00000000)
TA260 002:394.068 - 0.003ms returns 0
TA260 002:394.073 JLINK_WriteReg(R8, 0x00000000)
TA260 002:394.077 - 0.003ms returns 0
TA260 002:394.080 JLINK_WriteReg(R9, 0x20000180)
TA260 002:394.084 - 0.003ms returns 0
TA260 002:394.088 JLINK_WriteReg(R10, 0x00000000)
TA260 002:394.092 - 0.003ms returns 0
TA260 002:394.096 JLINK_WriteReg(R11, 0x00000000)
TA260 002:394.099 - 0.003ms returns 0
TA260 002:394.103 JLINK_WriteReg(R12, 0x00000000)
TA260 002:394.106 - 0.003ms returns 0
TA260 002:394.110 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:394.114 - 0.003ms returns 0
TA260 002:394.118 JLINK_WriteReg(R14, 0x20000001)
TA260 002:394.122 - 0.003ms returns 0
TA260 002:394.126 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:394.129 - 0.003ms returns 0
TA260 002:394.134 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:394.137 - 0.003ms returns 0
TA260 002:394.141 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:394.144 - 0.003ms returns 0
TA260 002:394.148 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:394.156 - 0.007ms returns 0
TA260 002:394.163 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:394.166 - 0.003ms returns 0
TA260 002:394.171 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:394.176 - 0.005ms returns 0x0000000F
TA260 002:394.180 JLINK_Go()
TA260 002:394.190   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:396.990 - 2.808ms 
TA260 002:397.004 JLINK_IsHalted()
TA260 002:397.486 - 0.481ms returns FALSE
TA260 002:397.497 JLINK_HasError()
TA260 002:398.570 JLINK_IsHalted()
TA260 002:399.022 - 0.451ms returns FALSE
TA260 002:399.028 JLINK_HasError()
TA260 002:400.569 JLINK_IsHalted()
TA260 002:401.012 - 0.443ms returns FALSE
TA260 002:401.021 JLINK_HasError()
TA260 002:403.071 JLINK_IsHalted()
TA260 002:405.391   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:405.909 - 2.837ms returns TRUE
TA260 002:405.917 JLINK_ReadReg(R15 (PC))
TA260 002:405.924 - 0.006ms returns 0x20000000
TA260 002:405.928 JLINK_ClrBPEx(BPHandle = 0x0000000F)
TA260 002:405.932 - 0.004ms returns 0x00
TA260 002:405.937 JLINK_ReadReg(R0)
TA260 002:405.940 - 0.003ms returns 0x00000000
TA260 002:406.586 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:406.599   Data:  12 00 80 B2 00 EE 10 0A B8 EE 40 0A 20 EE 08 0A ...
TA260 002:406.614   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:409.215 - 2.628ms returns 0x27C
TA260 002:409.236 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:409.240   Data:  41 F4 80 11 01 60 00 20 BD E8 F0 83 2D E9 F0 47 ...
TA260 002:409.254   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:411.104 - 1.868ms returns 0x184
TA260 002:411.123 JLINK_HasError()
TA260 002:411.163 JLINK_WriteReg(R0, 0x08001000)
TA260 002:411.170 - 0.007ms returns 0
TA260 002:411.175 JLINK_WriteReg(R1, 0x00000400)
TA260 002:411.178 - 0.003ms returns 0
TA260 002:411.183 JLINK_WriteReg(R2, 0x20000184)
TA260 002:411.186 - 0.003ms returns 0
TA260 002:411.190 JLINK_WriteReg(R3, 0x00000000)
TA260 002:411.194 - 0.003ms returns 0
TA260 002:411.198 JLINK_WriteReg(R4, 0x00000000)
TA260 002:411.201 - 0.003ms returns 0
TA260 002:411.205 JLINK_WriteReg(R5, 0x00000000)
TA260 002:411.209 - 0.003ms returns 0
TA260 002:411.213 JLINK_WriteReg(R6, 0x00000000)
TA260 002:411.217 - 0.004ms returns 0
TA260 002:411.221 JLINK_WriteReg(R7, 0x00000000)
TA260 002:411.225 - 0.003ms returns 0
TA260 002:411.229 JLINK_WriteReg(R8, 0x00000000)
TA260 002:411.232 - 0.003ms returns 0
TA260 002:411.236 JLINK_WriteReg(R9, 0x20000180)
TA260 002:411.240 - 0.003ms returns 0
TA260 002:411.244 JLINK_WriteReg(R10, 0x00000000)
TA260 002:411.248 - 0.003ms returns 0
TA260 002:411.252 JLINK_WriteReg(R11, 0x00000000)
TA260 002:411.256 - 0.004ms returns 0
TA260 002:411.260 JLINK_WriteReg(R12, 0x00000000)
TA260 002:411.263 - 0.003ms returns 0
TA260 002:411.267 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:411.272 - 0.004ms returns 0
TA260 002:411.276 JLINK_WriteReg(R14, 0x20000001)
TA260 002:411.279 - 0.003ms returns 0
TA260 002:411.283 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:411.287 - 0.003ms returns 0
TA260 002:411.291 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:411.294 - 0.003ms returns 0
TA260 002:411.298 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:411.302 - 0.003ms returns 0
TA260 002:411.306 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:411.310 - 0.003ms returns 0
TA260 002:411.314 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:411.317 - 0.003ms returns 0
TA260 002:411.322 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:411.327 - 0.005ms returns 0x00000010
TA260 002:411.331 JLINK_Go()
TA260 002:411.340   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:414.071 - 2.739ms 
TA260 002:414.086 JLINK_IsHalted()
TA260 002:414.603 - 0.516ms returns FALSE
TA260 002:414.611 JLINK_HasError()
TA260 002:416.583 JLINK_IsHalted()
TA260 002:417.039 - 0.455ms returns FALSE
TA260 002:417.049 JLINK_HasError()
TA260 002:418.580 JLINK_IsHalted()
TA260 002:420.905   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:421.474 - 2.894ms returns TRUE
TA260 002:421.481 JLINK_ReadReg(R15 (PC))
TA260 002:421.491 - 0.009ms returns 0x20000000
TA260 002:421.497 JLINK_ClrBPEx(BPHandle = 0x00000010)
TA260 002:421.501 - 0.003ms returns 0x00
TA260 002:421.505 JLINK_ReadReg(R0)
TA260 002:421.508 - 0.003ms returns 0x00000000
TA260 002:421.903 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:421.913   Data:  19 B1 01 68 21 F0 08 01 01 60 01 68 21 F0 01 01 ...
TA260 002:421.925   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:424.478 - 2.575ms returns 0x27C
TA260 002:424.493 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:424.496   Data:  41 F4 00 71 01 60 01 68 41 F4 80 61 01 60 01 68 ...
TA260 002:424.506   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:426.390 - 1.897ms returns 0x184
TA260 002:426.398 JLINK_HasError()
TA260 002:426.404 JLINK_WriteReg(R0, 0x08001400)
TA260 002:426.410 - 0.006ms returns 0
TA260 002:426.414 JLINK_WriteReg(R1, 0x00000400)
TA260 002:426.418 - 0.003ms returns 0
TA260 002:426.422 JLINK_WriteReg(R2, 0x20000184)
TA260 002:426.425 - 0.003ms returns 0
TA260 002:426.430 JLINK_WriteReg(R3, 0x00000000)
TA260 002:426.433 - 0.003ms returns 0
TA260 002:426.437 JLINK_WriteReg(R4, 0x00000000)
TA260 002:426.440 - 0.003ms returns 0
TA260 002:426.444 JLINK_WriteReg(R5, 0x00000000)
TA260 002:426.448 - 0.003ms returns 0
TA260 002:426.452 JLINK_WriteReg(R6, 0x00000000)
TA260 002:426.455 - 0.003ms returns 0
TA260 002:426.459 JLINK_WriteReg(R7, 0x00000000)
TA260 002:426.463 - 0.003ms returns 0
TA260 002:426.467 JLINK_WriteReg(R8, 0x00000000)
TA260 002:426.471 - 0.003ms returns 0
TA260 002:426.474 JLINK_WriteReg(R9, 0x20000180)
TA260 002:426.478 - 0.003ms returns 0
TA260 002:426.482 JLINK_WriteReg(R10, 0x00000000)
TA260 002:426.486 - 0.003ms returns 0
TA260 002:426.490 JLINK_WriteReg(R11, 0x00000000)
TA260 002:426.493 - 0.003ms returns 0
TA260 002:426.497 JLINK_WriteReg(R12, 0x00000000)
TA260 002:426.500 - 0.003ms returns 0
TA260 002:426.505 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:426.509 - 0.004ms returns 0
TA260 002:426.513 JLINK_WriteReg(R14, 0x20000001)
TA260 002:426.516 - 0.003ms returns 0
TA260 002:426.520 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:426.524 - 0.003ms returns 0
TA260 002:426.528 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:426.531 - 0.003ms returns 0
TA260 002:426.535 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:426.539 - 0.003ms returns 0
TA260 002:426.543 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:426.546 - 0.003ms returns 0
TA260 002:426.550 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:426.553 - 0.003ms returns 0
TA260 002:426.558 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:426.563 - 0.005ms returns 0x00000011
TA260 002:426.567 JLINK_Go()
TA260 002:426.575   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:429.351 - 2.784ms 
TA260 002:429.365 JLINK_IsHalted()
TA260 002:429.850 - 0.484ms returns FALSE
TA260 002:429.857 JLINK_HasError()
TA260 002:431.089 JLINK_IsHalted()
TA260 002:431.509 - 0.420ms returns FALSE
TA260 002:431.516 JLINK_HasError()
TA260 002:433.095 JLINK_IsHalted()
TA260 002:433.597 - 0.502ms returns FALSE
TA260 002:433.611 JLINK_HasError()
TA260 002:435.597 JLINK_IsHalted()
TA260 002:437.977   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:438.497 - 2.899ms returns TRUE
TA260 002:438.518 JLINK_ReadReg(R15 (PC))
TA260 002:438.524 - 0.005ms returns 0x20000000
TA260 002:438.529 JLINK_ClrBPEx(BPHandle = 0x00000011)
TA260 002:438.533 - 0.004ms returns 0x00
TA260 002:438.570 JLINK_ReadReg(R0)
TA260 002:438.575 - 0.005ms returns 0x00000000
TA260 002:439.154 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:439.166   Data:  FF F7 22 FF 04 46 00 BF 28 68 C0 03 06 D4 FF F7 ...
TA260 002:439.178   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:441.780 - 2.625ms returns 0x27C
TA260 002:441.797 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:441.801   Data:  02 04 20 68 00 06 58 BF 10 BD FF F7 DF FE 80 20 ...
TA260 002:441.812   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:443.698 - 1.900ms returns 0x184
TA260 002:443.709 JLINK_HasError()
TA260 002:443.716 JLINK_WriteReg(R0, 0x08001800)
TA260 002:443.729 - 0.014ms returns 0
TA260 002:443.734 JLINK_WriteReg(R1, 0x00000400)
TA260 002:443.737 - 0.003ms returns 0
TA260 002:443.741 JLINK_WriteReg(R2, 0x20000184)
TA260 002:443.745 - 0.003ms returns 0
TA260 002:443.750 JLINK_WriteReg(R3, 0x00000000)
TA260 002:443.753 - 0.003ms returns 0
TA260 002:443.757 JLINK_WriteReg(R4, 0x00000000)
TA260 002:443.761 - 0.003ms returns 0
TA260 002:443.765 JLINK_WriteReg(R5, 0x00000000)
TA260 002:443.768 - 0.003ms returns 0
TA260 002:443.773 JLINK_WriteReg(R6, 0x00000000)
TA260 002:443.776 - 0.003ms returns 0
TA260 002:443.780 JLINK_WriteReg(R7, 0x00000000)
TA260 002:443.784 - 0.003ms returns 0
TA260 002:443.788 JLINK_WriteReg(R8, 0x00000000)
TA260 002:443.792 - 0.003ms returns 0
TA260 002:443.796 JLINK_WriteReg(R9, 0x20000180)
TA260 002:443.799 - 0.003ms returns 0
TA260 002:443.803 JLINK_WriteReg(R10, 0x00000000)
TA260 002:443.807 - 0.003ms returns 0
TA260 002:443.811 JLINK_WriteReg(R11, 0x00000000)
TA260 002:443.815 - 0.003ms returns 0
TA260 002:443.819 JLINK_WriteReg(R12, 0x00000000)
TA260 002:443.822 - 0.003ms returns 0
TA260 002:443.826 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:443.831 - 0.004ms returns 0
TA260 002:443.835 JLINK_WriteReg(R14, 0x20000001)
TA260 002:443.838 - 0.003ms returns 0
TA260 002:443.842 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:443.846 - 0.003ms returns 0
TA260 002:443.850 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:443.853 - 0.003ms returns 0
TA260 002:443.858 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:443.861 - 0.003ms returns 0
TA260 002:443.865 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:443.869 - 0.003ms returns 0
TA260 002:443.873 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:443.877 - 0.003ms returns 0
TA260 002:443.882 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:443.886 - 0.005ms returns 0x00000012
TA260 002:443.890 JLINK_Go()
TA260 002:443.901   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:446.502 - 2.610ms 
TA260 002:446.520 JLINK_IsHalted()
TA260 002:446.981 - 0.461ms returns FALSE
TA260 002:446.993 JLINK_HasError()
TA260 002:450.104 JLINK_IsHalted()
TA260 002:450.663 - 0.559ms returns FALSE
TA260 002:450.670 JLINK_HasError()
TA260 002:452.106 JLINK_IsHalted()
TA260 002:454.375   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:454.898 - 2.791ms returns TRUE
TA260 002:454.908 JLINK_ReadReg(R15 (PC))
TA260 002:454.914 - 0.006ms returns 0x20000000
TA260 002:454.919 JLINK_ClrBPEx(BPHandle = 0x00000012)
TA260 002:454.922 - 0.004ms returns 0x00
TA260 002:454.927 JLINK_ReadReg(R0)
TA260 002:454.931 - 0.003ms returns 0x00000000
TA260 002:455.331 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:455.343   Data:  80 1E FF F7 21 FD 05 46 70 6F 80 07 BB D4 FF F7 ...
TA260 002:455.355   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:458.001 - 2.668ms returns 0x27C
TA260 002:458.026 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:458.031   Data:  01 60 00 68 C4 F2 02 04 00 F0 01 00 01 90 01 98 ...
TA260 002:458.042   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:460.033 - 2.006ms returns 0x184
TA260 002:460.048 JLINK_HasError()
TA260 002:460.054 JLINK_WriteReg(R0, 0x08001C00)
TA260 002:460.059 - 0.005ms returns 0
TA260 002:460.064 JLINK_WriteReg(R1, 0x00000400)
TA260 002:460.067 - 0.003ms returns 0
TA260 002:460.071 JLINK_WriteReg(R2, 0x20000184)
TA260 002:460.075 - 0.003ms returns 0
TA260 002:460.079 JLINK_WriteReg(R3, 0x00000000)
TA260 002:460.082 - 0.003ms returns 0
TA260 002:460.086 JLINK_WriteReg(R4, 0x00000000)
TA260 002:460.090 - 0.003ms returns 0
TA260 002:460.094 JLINK_WriteReg(R5, 0x00000000)
TA260 002:460.097 - 0.003ms returns 0
TA260 002:460.102 JLINK_WriteReg(R6, 0x00000000)
TA260 002:460.111 - 0.008ms returns 0
TA260 002:460.115 JLINK_WriteReg(R7, 0x00000000)
TA260 002:460.119 - 0.004ms returns 0
TA260 002:460.124 JLINK_WriteReg(R8, 0x00000000)
TA260 002:460.127 - 0.003ms returns 0
TA260 002:460.131 JLINK_WriteReg(R9, 0x20000180)
TA260 002:460.134 - 0.003ms returns 0
TA260 002:460.139 JLINK_WriteReg(R10, 0x00000000)
TA260 002:460.149 - 0.010ms returns 0
TA260 002:460.154 JLINK_WriteReg(R11, 0x00000000)
TA260 002:460.157 - 0.003ms returns 0
TA260 002:460.161 JLINK_WriteReg(R12, 0x00000000)
TA260 002:460.165 - 0.003ms returns 0
TA260 002:460.169 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:460.173 - 0.004ms returns 0
TA260 002:460.177 JLINK_WriteReg(R14, 0x20000001)
TA260 002:460.181 - 0.003ms returns 0
TA260 002:460.185 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:460.189 - 0.004ms returns 0
TA260 002:460.194 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:460.197 - 0.003ms returns 0
TA260 002:460.201 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:460.205 - 0.003ms returns 0
TA260 002:460.209 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:460.212 - 0.003ms returns 0
TA260 002:460.216 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:460.220 - 0.003ms returns 0
TA260 002:460.224 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:460.229 - 0.005ms returns 0x00000013
TA260 002:460.233 JLINK_Go()
TA260 002:460.243   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:463.060 - 2.826ms 
TA260 002:463.071 JLINK_IsHalted()
TA260 002:463.502 - 0.430ms returns FALSE
TA260 002:463.516 JLINK_HasError()
TA260 002:464.621 JLINK_IsHalted()
TA260 002:465.139 - 0.518ms returns FALSE
TA260 002:465.148 JLINK_HasError()
TA260 002:466.622 JLINK_IsHalted()
TA260 002:467.139 - 0.517ms returns FALSE
TA260 002:467.149 JLINK_HasError()
TA260 002:468.622 JLINK_IsHalted()
TA260 002:470.978   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:471.497 - 2.874ms returns TRUE
TA260 002:471.512 JLINK_ReadReg(R15 (PC))
TA260 002:471.517 - 0.005ms returns 0x20000000
TA260 002:471.522 JLINK_ClrBPEx(BPHandle = 0x00000013)
TA260 002:471.526 - 0.004ms returns 0x00
TA260 002:471.531 JLINK_ReadReg(R0)
TA260 002:471.534 - 0.003ms returns 0x00000000
TA260 002:471.926 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:471.935   Data:  40 02 CB D0 CA 68 22 F0 40 02 CA 60 20 21 80 F8 ...
TA260 002:471.946   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:474.572 - 2.646ms returns 0x27C
TA260 002:474.597 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:474.602   Data:  84 F8 3D 10 84 F8 3E 10 10 BD 00 00 10 B5 86 B0 ...
TA260 002:474.616   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:476.524 - 1.926ms returns 0x184
TA260 002:476.536 JLINK_HasError()
TA260 002:476.542 JLINK_WriteReg(R0, 0x08002000)
TA260 002:476.549 - 0.007ms returns 0
TA260 002:476.553 JLINK_WriteReg(R1, 0x00000400)
TA260 002:476.557 - 0.003ms returns 0
TA260 002:476.561 JLINK_WriteReg(R2, 0x20000184)
TA260 002:476.564 - 0.003ms returns 0
TA260 002:476.568 JLINK_WriteReg(R3, 0x00000000)
TA260 002:476.572 - 0.003ms returns 0
TA260 002:476.577 JLINK_WriteReg(R4, 0x00000000)
TA260 002:476.580 - 0.003ms returns 0
TA260 002:476.584 JLINK_WriteReg(R5, 0x00000000)
TA260 002:476.588 - 0.003ms returns 0
TA260 002:476.592 JLINK_WriteReg(R6, 0x00000000)
TA260 002:476.595 - 0.003ms returns 0
TA260 002:476.599 JLINK_WriteReg(R7, 0x00000000)
TA260 002:476.603 - 0.003ms returns 0
TA260 002:476.607 JLINK_WriteReg(R8, 0x00000000)
TA260 002:476.610 - 0.003ms returns 0
TA260 002:476.614 JLINK_WriteReg(R9, 0x20000180)
TA260 002:476.618 - 0.003ms returns 0
TA260 002:476.622 JLINK_WriteReg(R10, 0x00000000)
TA260 002:476.625 - 0.003ms returns 0
TA260 002:476.629 JLINK_WriteReg(R11, 0x00000000)
TA260 002:476.633 - 0.003ms returns 0
TA260 002:476.637 JLINK_WriteReg(R12, 0x00000000)
TA260 002:476.640 - 0.003ms returns 0
TA260 002:476.644 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:476.649 - 0.004ms returns 0
TA260 002:476.653 JLINK_WriteReg(R14, 0x20000001)
TA260 002:476.656 - 0.003ms returns 0
TA260 002:476.660 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:476.664 - 0.003ms returns 0
TA260 002:476.668 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:476.672 - 0.003ms returns 0
TA260 002:476.676 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:476.679 - 0.003ms returns 0
TA260 002:476.683 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:476.686 - 0.003ms returns 0
TA260 002:476.698 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:476.701 - 0.003ms returns 0
TA260 002:476.706 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:476.711 - 0.004ms returns 0x00000014
TA260 002:476.715 JLINK_Go()
TA260 002:476.725   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:479.500 - 2.783ms 
TA260 002:479.516 JLINK_IsHalted()
TA260 002:480.016 - 0.500ms returns FALSE
TA260 002:480.029 JLINK_HasError()
TA260 002:487.159 JLINK_IsHalted()
TA260 002:489.606   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:490.082 - 2.922ms returns TRUE
TA260 002:490.095 JLINK_ReadReg(R15 (PC))
TA260 002:490.101 - 0.006ms returns 0x20000000
TA260 002:490.137 JLINK_ClrBPEx(BPHandle = 0x00000014)
TA260 002:490.150 - 0.013ms returns 0x00
TA260 002:490.156 JLINK_ReadReg(R0)
TA260 002:490.160 - 0.004ms returns 0x00000000
TA260 002:491.075 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:491.088   Data:  00 68 05 F5 4C 41 88 42 04 D0 05 F5 80 61 88 42 ...
TA260 002:491.100   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:493.764 - 2.688ms returns 0x27C
TA260 002:493.786 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:493.790   Data:  20 68 00 BF 01 68 49 06 1D D4 00 2D FA D0 FE F7 ...
TA260 002:493.809   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:495.704 - 1.917ms returns 0x184
TA260 002:495.725 JLINK_HasError()
TA260 002:495.731 JLINK_WriteReg(R0, 0x08002400)
TA260 002:495.738 - 0.006ms returns 0
TA260 002:495.743 JLINK_WriteReg(R1, 0x00000400)
TA260 002:495.746 - 0.003ms returns 0
TA260 002:495.750 JLINK_WriteReg(R2, 0x20000184)
TA260 002:495.754 - 0.003ms returns 0
TA260 002:495.758 JLINK_WriteReg(R3, 0x00000000)
TA260 002:495.761 - 0.003ms returns 0
TA260 002:495.765 JLINK_WriteReg(R4, 0x00000000)
TA260 002:495.769 - 0.003ms returns 0
TA260 002:495.773 JLINK_WriteReg(R5, 0x00000000)
TA260 002:495.776 - 0.003ms returns 0
TA260 002:495.780 JLINK_WriteReg(R6, 0x00000000)
TA260 002:495.783 - 0.003ms returns 0
TA260 002:495.788 JLINK_WriteReg(R7, 0x00000000)
TA260 002:495.791 - 0.003ms returns 0
TA260 002:495.795 JLINK_WriteReg(R8, 0x00000000)
TA260 002:495.799 - 0.003ms returns 0
TA260 002:495.803 JLINK_WriteReg(R9, 0x20000180)
TA260 002:495.806 - 0.003ms returns 0
TA260 002:495.810 JLINK_WriteReg(R10, 0x00000000)
TA260 002:495.814 - 0.003ms returns 0
TA260 002:495.818 JLINK_WriteReg(R11, 0x00000000)
TA260 002:495.821 - 0.003ms returns 0
TA260 002:495.825 JLINK_WriteReg(R12, 0x00000000)
TA260 002:495.828 - 0.003ms returns 0
TA260 002:495.833 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:495.837 - 0.004ms returns 0
TA260 002:495.841 JLINK_WriteReg(R14, 0x20000001)
TA260 002:495.844 - 0.003ms returns 0
TA260 002:495.848 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:495.853 - 0.004ms returns 0
TA260 002:495.857 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:495.860 - 0.003ms returns 0
TA260 002:495.864 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:495.868 - 0.003ms returns 0
TA260 002:495.872 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:495.875 - 0.003ms returns 0
TA260 002:495.879 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:495.883 - 0.004ms returns 0
TA260 002:495.888 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:495.893 - 0.005ms returns 0x00000015
TA260 002:495.897 JLINK_Go()
TA260 002:495.906   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:498.701 - 2.803ms 
TA260 002:498.716 JLINK_IsHalted()
TA260 002:499.238 - 0.521ms returns FALSE
TA260 002:499.245 JLINK_HasError()
TA260 002:503.254 JLINK_IsHalted()
TA260 002:505.678   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:506.186 - 2.931ms returns TRUE
TA260 002:506.193 JLINK_ReadReg(R15 (PC))
TA260 002:506.199 - 0.005ms returns 0x20000000
TA260 002:506.203 JLINK_ClrBPEx(BPHandle = 0x00000015)
TA260 002:506.207 - 0.004ms returns 0x00
TA260 002:506.212 JLINK_ReadReg(R0)
TA260 002:506.215 - 0.003ms returns 0x00000000
TA260 002:506.670 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:506.688   Data:  02 22 00 21 C0 E9 02 22 4F F4 80 52 C0 E9 0A 21 ...
TA260 002:506.709   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:509.360 - 2.689ms returns 0x27C
TA260 002:509.384 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:509.388   Data:  00 00 C4 F2 00 01 00 23 4F F0 0C 0C 4F F4 E1 32 ...
TA260 002:509.400   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:511.289 - 1.905ms returns 0x184
TA260 002:511.300 JLINK_HasError()
TA260 002:511.306 JLINK_WriteReg(R0, 0x08002800)
TA260 002:511.313 - 0.007ms returns 0
TA260 002:511.317 JLINK_WriteReg(R1, 0x00000400)
TA260 002:511.321 - 0.003ms returns 0
TA260 002:511.325 JLINK_WriteReg(R2, 0x20000184)
TA260 002:511.328 - 0.003ms returns 0
TA260 002:511.332 JLINK_WriteReg(R3, 0x00000000)
TA260 002:511.336 - 0.003ms returns 0
TA260 002:511.340 JLINK_WriteReg(R4, 0x00000000)
TA260 002:511.343 - 0.003ms returns 0
TA260 002:511.347 JLINK_WriteReg(R5, 0x00000000)
TA260 002:511.351 - 0.003ms returns 0
TA260 002:511.355 JLINK_WriteReg(R6, 0x00000000)
TA260 002:511.358 - 0.003ms returns 0
TA260 002:511.362 JLINK_WriteReg(R7, 0x00000000)
TA260 002:511.366 - 0.003ms returns 0
TA260 002:511.370 JLINK_WriteReg(R8, 0x00000000)
TA260 002:511.373 - 0.003ms returns 0
TA260 002:511.378 JLINK_WriteReg(R9, 0x20000180)
TA260 002:511.381 - 0.003ms returns 0
TA260 002:511.385 JLINK_WriteReg(R10, 0x00000000)
TA260 002:511.388 - 0.003ms returns 0
TA260 002:511.392 JLINK_WriteReg(R11, 0x00000000)
TA260 002:511.396 - 0.003ms returns 0
TA260 002:511.400 JLINK_WriteReg(R12, 0x00000000)
TA260 002:511.403 - 0.003ms returns 0
TA260 002:511.407 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:511.412 - 0.004ms returns 0
TA260 002:511.416 JLINK_WriteReg(R14, 0x20000001)
TA260 002:511.419 - 0.003ms returns 0
TA260 002:511.423 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:511.427 - 0.003ms returns 0
TA260 002:511.431 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:511.434 - 0.003ms returns 0
TA260 002:511.438 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:511.442 - 0.003ms returns 0
TA260 002:511.446 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:511.449 - 0.003ms returns 0
TA260 002:511.453 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:511.457 - 0.003ms returns 0
TA260 002:511.461 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:511.466 - 0.004ms returns 0x00000016
TA260 002:511.470 JLINK_Go()
TA260 002:511.478   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:514.258 - 2.788ms 
TA260 002:514.276 JLINK_IsHalted()
TA260 002:514.703 - 0.426ms returns FALSE
TA260 002:514.715 JLINK_HasError()
TA260 002:517.267 JLINK_IsHalted()
TA260 002:517.789 - 0.521ms returns FALSE
TA260 002:517.796 JLINK_HasError()
TA260 002:519.258 JLINK_IsHalted()
TA260 002:521.590   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:522.039 - 2.780ms returns TRUE
TA260 002:522.047 JLINK_ReadReg(R15 (PC))
TA260 002:522.053 - 0.006ms returns 0x20000000
TA260 002:522.058 JLINK_ClrBPEx(BPHandle = 0x00000016)
TA260 002:522.062 - 0.003ms returns 0x00
TA260 002:522.066 JLINK_ReadReg(R0)
TA260 002:522.070 - 0.003ms returns 0x00000000
TA260 002:522.456 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:522.466   Data:  D6 F8 0C 80 7E 78 04 5D BF 78 36 04 46 EA 04 64 ...
TA260 002:522.477   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:525.071 - 2.614ms returns 0x27C
TA260 002:525.092 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:525.096   Data:  01 01 4F F4 F8 70 00 EA 11 10 00 EB 02 10 C1 F3 ...
TA260 002:525.108   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:527.024 - 1.932ms returns 0x184
TA260 002:527.035 JLINK_HasError()
TA260 002:527.040 JLINK_WriteReg(R0, 0x08002C00)
TA260 002:527.046 - 0.005ms returns 0
TA260 002:527.050 JLINK_WriteReg(R1, 0x00000400)
TA260 002:527.054 - 0.003ms returns 0
TA260 002:527.059 JLINK_WriteReg(R2, 0x20000184)
TA260 002:527.062 - 0.003ms returns 0
TA260 002:527.066 JLINK_WriteReg(R3, 0x00000000)
TA260 002:527.070 - 0.003ms returns 0
TA260 002:527.074 JLINK_WriteReg(R4, 0x00000000)
TA260 002:527.077 - 0.003ms returns 0
TA260 002:527.081 JLINK_WriteReg(R5, 0x00000000)
TA260 002:527.084 - 0.003ms returns 0
TA260 002:527.096 JLINK_WriteReg(R6, 0x00000000)
TA260 002:527.099 - 0.003ms returns 0
TA260 002:527.103 JLINK_WriteReg(R7, 0x00000000)
TA260 002:527.107 - 0.003ms returns 0
TA260 002:527.111 JLINK_WriteReg(R8, 0x00000000)
TA260 002:527.114 - 0.003ms returns 0
TA260 002:527.128 JLINK_WriteReg(R9, 0x20000180)
TA260 002:527.132 - 0.003ms returns 0
TA260 002:527.136 JLINK_WriteReg(R10, 0x00000000)
TA260 002:527.140 - 0.003ms returns 0
TA260 002:527.144 JLINK_WriteReg(R11, 0x00000000)
TA260 002:527.147 - 0.003ms returns 0
TA260 002:527.151 JLINK_WriteReg(R12, 0x00000000)
TA260 002:527.154 - 0.003ms returns 0
TA260 002:527.159 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:527.163 - 0.003ms returns 0
TA260 002:527.167 JLINK_WriteReg(R14, 0x20000001)
TA260 002:527.171 - 0.003ms returns 0
TA260 002:527.175 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:527.178 - 0.003ms returns 0
TA260 002:527.182 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:527.186 - 0.003ms returns 0
TA260 002:527.190 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:527.194 - 0.003ms returns 0
TA260 002:527.198 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:527.201 - 0.003ms returns 0
TA260 002:527.205 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:527.209 - 0.003ms returns 0
TA260 002:527.214 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:527.218 - 0.005ms returns 0x00000017
TA260 002:527.223 JLINK_Go()
TA260 002:527.231   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:530.058 - 2.834ms 
TA260 002:530.075 JLINK_IsHalted()
TA260 002:530.501 - 0.426ms returns FALSE
TA260 002:530.509 JLINK_HasError()
TA260 002:532.926 JLINK_IsHalted()
TA260 002:533.367 - 0.441ms returns FALSE
TA260 002:533.382 JLINK_HasError()
TA260 002:534.423 JLINK_IsHalted()
TA260 002:536.748   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:537.266 - 2.842ms returns TRUE
TA260 002:537.276 JLINK_ReadReg(R15 (PC))
TA260 002:537.283 - 0.005ms returns 0x20000000
TA260 002:537.287 JLINK_ClrBPEx(BPHandle = 0x00000017)
TA260 002:537.291 - 0.004ms returns 0x00
TA260 002:537.296 JLINK_ReadReg(R0)
TA260 002:537.299 - 0.003ms returns 0x00000000
TA260 002:537.689 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:537.701   Data:  00 3A 33 EE 40 3A 23 EE 01 1A 20 EE 02 0A 31 EE ...
TA260 002:537.714   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:540.262 - 2.572ms returns 0x27C
TA260 002:540.284 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:540.288   Data:  55 F8 04 1B 40 F8 04 1B 55 F8 04 1B 40 F8 04 1B ...
TA260 002:540.298   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:542.148 - 1.863ms returns 0x184
TA260 002:542.161 JLINK_HasError()
TA260 002:542.201 JLINK_WriteReg(R0, 0x08003000)
TA260 002:542.207 - 0.006ms returns 0
TA260 002:542.211 JLINK_WriteReg(R1, 0x00000400)
TA260 002:542.215 - 0.003ms returns 0
TA260 002:542.219 JLINK_WriteReg(R2, 0x20000184)
TA260 002:542.223 - 0.003ms returns 0
TA260 002:542.227 JLINK_WriteReg(R3, 0x00000000)
TA260 002:542.230 - 0.003ms returns 0
TA260 002:542.234 JLINK_WriteReg(R4, 0x00000000)
TA260 002:542.238 - 0.003ms returns 0
TA260 002:542.242 JLINK_WriteReg(R5, 0x00000000)
TA260 002:542.245 - 0.003ms returns 0
TA260 002:542.249 JLINK_WriteReg(R6, 0x00000000)
TA260 002:542.253 - 0.003ms returns 0
TA260 002:542.257 JLINK_WriteReg(R7, 0x00000000)
TA260 002:542.260 - 0.003ms returns 0
TA260 002:542.264 JLINK_WriteReg(R8, 0x00000000)
TA260 002:542.268 - 0.004ms returns 0
TA260 002:542.272 JLINK_WriteReg(R9, 0x20000180)
TA260 002:542.276 - 0.003ms returns 0
TA260 002:542.280 JLINK_WriteReg(R10, 0x00000000)
TA260 002:542.284 - 0.004ms returns 0
TA260 002:542.288 JLINK_WriteReg(R11, 0x00000000)
TA260 002:542.291 - 0.003ms returns 0
TA260 002:542.296 JLINK_WriteReg(R12, 0x00000000)
TA260 002:542.299 - 0.003ms returns 0
TA260 002:542.303 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:542.307 - 0.004ms returns 0
TA260 002:542.311 JLINK_WriteReg(R14, 0x20000001)
TA260 002:542.315 - 0.003ms returns 0
TA260 002:542.319 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:542.323 - 0.003ms returns 0
TA260 002:542.334 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:542.337 - 0.003ms returns 0
TA260 002:542.341 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:542.345 - 0.003ms returns 0
TA260 002:542.349 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:542.352 - 0.003ms returns 0
TA260 002:542.356 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:542.360 - 0.003ms returns 0
TA260 002:542.364 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:542.370 - 0.005ms returns 0x00000018
TA260 002:542.374 JLINK_Go()
TA260 002:542.383   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:545.133 - 2.758ms 
TA260 002:545.143 JLINK_IsHalted()
TA260 002:545.661 - 0.517ms returns FALSE
TA260 002:545.668 JLINK_HasError()
TA260 002:546.939 JLINK_IsHalted()
TA260 002:547.480 - 0.540ms returns FALSE
TA260 002:547.488 JLINK_HasError()
TA260 002:548.935 JLINK_IsHalted()
TA260 002:549.370 - 0.434ms returns FALSE
TA260 002:549.378 JLINK_HasError()
TA260 002:550.935 JLINK_IsHalted()
TA260 002:553.202   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:553.707 - 2.771ms returns TRUE
TA260 002:553.713 JLINK_ReadReg(R15 (PC))
TA260 002:553.719 - 0.005ms returns 0x20000000
TA260 002:553.724 JLINK_ClrBPEx(BPHandle = 0x00000018)
TA260 002:553.727 - 0.003ms returns 0x00
TA260 002:553.732 JLINK_ReadReg(R0)
TA260 002:553.735 - 0.003ms returns 0x00000000
TA260 002:554.110 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:554.118   Data:  04 2B 41 F8 04 2B 0F CB FF F7 A6 F9 10 B0 B0 BD ...
TA260 002:554.128   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:556.768 - 2.657ms returns 0x27C
TA260 002:556.782 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:556.786   Data:  40 F2 38 15 C2 F2 00 05 E8 78 03 28 00 F2 2A 81 ...
TA260 002:556.798   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:558.615 - 1.833ms returns 0x184
TA260 002:558.628 JLINK_HasError()
TA260 002:558.635 JLINK_WriteReg(R0, 0x08003400)
TA260 002:558.640 - 0.005ms returns 0
TA260 002:558.644 JLINK_WriteReg(R1, 0x00000400)
TA260 002:558.648 - 0.003ms returns 0
TA260 002:558.653 JLINK_WriteReg(R2, 0x20000184)
TA260 002:558.657 - 0.003ms returns 0
TA260 002:558.661 JLINK_WriteReg(R3, 0x00000000)
TA260 002:558.664 - 0.003ms returns 0
TA260 002:558.668 JLINK_WriteReg(R4, 0x00000000)
TA260 002:558.672 - 0.003ms returns 0
TA260 002:558.676 JLINK_WriteReg(R5, 0x00000000)
TA260 002:558.680 - 0.003ms returns 0
TA260 002:558.684 JLINK_WriteReg(R6, 0x00000000)
TA260 002:558.687 - 0.003ms returns 0
TA260 002:558.691 JLINK_WriteReg(R7, 0x00000000)
TA260 002:558.694 - 0.003ms returns 0
TA260 002:558.699 JLINK_WriteReg(R8, 0x00000000)
TA260 002:558.702 - 0.003ms returns 0
TA260 002:558.707 JLINK_WriteReg(R9, 0x20000180)
TA260 002:558.710 - 0.003ms returns 0
TA260 002:558.714 JLINK_WriteReg(R10, 0x00000000)
TA260 002:558.718 - 0.003ms returns 0
TA260 002:558.722 JLINK_WriteReg(R11, 0x00000000)
TA260 002:558.725 - 0.003ms returns 0
TA260 002:558.730 JLINK_WriteReg(R12, 0x00000000)
TA260 002:558.733 - 0.003ms returns 0
TA260 002:558.737 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:558.741 - 0.004ms returns 0
TA260 002:558.745 JLINK_WriteReg(R14, 0x20000001)
TA260 002:558.750 - 0.004ms returns 0
TA260 002:558.754 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:558.758 - 0.004ms returns 0
TA260 002:558.763 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:558.767 - 0.004ms returns 0
TA260 002:558.771 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:558.774 - 0.003ms returns 0
TA260 002:558.779 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:558.782 - 0.003ms returns 0
TA260 002:558.786 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:558.790 - 0.003ms returns 0
TA260 002:558.795 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:558.799 - 0.005ms returns 0x00000019
TA260 002:558.804 JLINK_Go()
TA260 002:558.813   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:561.465 - 2.660ms 
TA260 002:561.475 JLINK_IsHalted()
TA260 002:561.916 - 0.441ms returns FALSE
TA260 002:561.923 JLINK_HasError()
TA260 002:563.449 JLINK_IsHalted()
TA260 002:563.917 - 0.466ms returns FALSE
TA260 002:563.930 JLINK_HasError()
TA260 002:565.454 JLINK_IsHalted()
TA260 002:567.832   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:568.302 - 2.847ms returns TRUE
TA260 002:568.318 JLINK_ReadReg(R15 (PC))
TA260 002:568.324 - 0.006ms returns 0x20000000
TA260 002:568.363 JLINK_ClrBPEx(BPHandle = 0x00000019)
TA260 002:568.368 - 0.005ms returns 0x00
TA260 002:568.373 JLINK_ReadReg(R0)
TA260 002:568.377 - 0.003ms returns 0x00000000
TA260 002:568.759 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:568.768   Data:  03 F1 10 00 50 F8 04 2B 69 46 41 F8 04 2B 50 F8 ...
TA260 002:568.779   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:571.351 - 2.592ms returns 0x27C
TA260 002:571.363 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:571.367   Data:  00 00 A0 70 61 70 08 BF B0 BD C1 07 04 D1 81 07 ...
TA260 002:571.376   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:573.309 - 1.945ms returns 0x184
TA260 002:573.328 JLINK_HasError()
TA260 002:573.334 JLINK_WriteReg(R0, 0x08003800)
TA260 002:573.341 - 0.007ms returns 0
TA260 002:573.345 JLINK_WriteReg(R1, 0x00000400)
TA260 002:573.348 - 0.003ms returns 0
TA260 002:573.353 JLINK_WriteReg(R2, 0x20000184)
TA260 002:573.356 - 0.003ms returns 0
TA260 002:573.360 JLINK_WriteReg(R3, 0x00000000)
TA260 002:573.364 - 0.003ms returns 0
TA260 002:573.368 JLINK_WriteReg(R4, 0x00000000)
TA260 002:573.371 - 0.003ms returns 0
TA260 002:573.375 JLINK_WriteReg(R5, 0x00000000)
TA260 002:573.379 - 0.003ms returns 0
TA260 002:573.383 JLINK_WriteReg(R6, 0x00000000)
TA260 002:573.386 - 0.003ms returns 0
TA260 002:573.390 JLINK_WriteReg(R7, 0x00000000)
TA260 002:573.393 - 0.003ms returns 0
TA260 002:573.398 JLINK_WriteReg(R8, 0x00000000)
TA260 002:573.401 - 0.003ms returns 0
TA260 002:573.405 JLINK_WriteReg(R9, 0x20000180)
TA260 002:573.408 - 0.003ms returns 0
TA260 002:573.413 JLINK_WriteReg(R10, 0x00000000)
TA260 002:573.416 - 0.003ms returns 0
TA260 002:573.420 JLINK_WriteReg(R11, 0x00000000)
TA260 002:573.424 - 0.003ms returns 0
TA260 002:573.427 JLINK_WriteReg(R12, 0x00000000)
TA260 002:573.431 - 0.003ms returns 0
TA260 002:573.435 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:573.439 - 0.004ms returns 0
TA260 002:573.443 JLINK_WriteReg(R14, 0x20000001)
TA260 002:573.446 - 0.003ms returns 0
TA260 002:573.451 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:573.454 - 0.003ms returns 0
TA260 002:573.458 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:573.462 - 0.003ms returns 0
TA260 002:573.466 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:573.469 - 0.003ms returns 0
TA260 002:573.473 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:573.477 - 0.003ms returns 0
TA260 002:573.481 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:573.484 - 0.003ms returns 0
TA260 002:573.489 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:573.493 - 0.004ms returns 0x0000001A
TA260 002:573.497 JLINK_Go()
TA260 002:573.506   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:576.227 - 2.728ms 
TA260 002:576.252 JLINK_IsHalted()
TA260 002:576.719 - 0.466ms returns FALSE
TA260 002:576.732 JLINK_HasError()
TA260 002:577.962 JLINK_IsHalted()
TA260 002:578.468 - 0.506ms returns FALSE
TA260 002:578.481 JLINK_HasError()
TA260 002:579.960 JLINK_IsHalted()
TA260 002:580.486 - 0.525ms returns FALSE
TA260 002:580.499 JLINK_HasError()
TA260 002:582.968 JLINK_IsHalted()
TA260 002:585.310   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:585.799 - 2.831ms returns TRUE
TA260 002:585.808 JLINK_ReadReg(R15 (PC))
TA260 002:585.814 - 0.006ms returns 0x20000000
TA260 002:585.819 JLINK_ClrBPEx(BPHandle = 0x0000001A)
TA260 002:585.823 - 0.003ms returns 0x00
TA260 002:585.827 JLINK_ReadReg(R0)
TA260 002:585.831 - 0.003ms returns 0x00000000
TA260 002:586.216 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:586.225   Data:  50 F8 04 2B 41 F8 04 2B 50 F8 04 2B 41 F8 04 2B ...
TA260 002:586.236   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:588.815 - 2.599ms returns 0x27C
TA260 002:588.838 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:588.847   Data:  02 00 01 28 08 BF 05 20 02 B0 70 47 2D E9 F0 4D ...
TA260 002:588.862   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:590.716 - 1.877ms returns 0x184
TA260 002:590.734 JLINK_HasError()
TA260 002:590.740 JLINK_WriteReg(R0, 0x08003C00)
TA260 002:590.747 - 0.007ms returns 0
TA260 002:590.751 JLINK_WriteReg(R1, 0x00000400)
TA260 002:590.755 - 0.003ms returns 0
TA260 002:590.759 JLINK_WriteReg(R2, 0x20000184)
TA260 002:590.762 - 0.003ms returns 0
TA260 002:590.766 JLINK_WriteReg(R3, 0x00000000)
TA260 002:590.770 - 0.003ms returns 0
TA260 002:590.774 JLINK_WriteReg(R4, 0x00000000)
TA260 002:590.777 - 0.003ms returns 0
TA260 002:590.782 JLINK_WriteReg(R5, 0x00000000)
TA260 002:590.785 - 0.003ms returns 0
TA260 002:590.789 JLINK_WriteReg(R6, 0x00000000)
TA260 002:590.793 - 0.003ms returns 0
TA260 002:590.797 JLINK_WriteReg(R7, 0x00000000)
TA260 002:590.800 - 0.003ms returns 0
TA260 002:590.804 JLINK_WriteReg(R8, 0x00000000)
TA260 002:590.808 - 0.003ms returns 0
TA260 002:590.812 JLINK_WriteReg(R9, 0x20000180)
TA260 002:590.815 - 0.003ms returns 0
TA260 002:590.819 JLINK_WriteReg(R10, 0x00000000)
TA260 002:590.823 - 0.003ms returns 0
TA260 002:590.827 JLINK_WriteReg(R11, 0x00000000)
TA260 002:590.830 - 0.003ms returns 0
TA260 002:590.834 JLINK_WriteReg(R12, 0x00000000)
TA260 002:590.838 - 0.003ms returns 0
TA260 002:590.842 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:590.846 - 0.004ms returns 0
TA260 002:590.850 JLINK_WriteReg(R14, 0x20000001)
TA260 002:590.854 - 0.003ms returns 0
TA260 002:590.858 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:590.862 - 0.004ms returns 0
TA260 002:590.866 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:590.869 - 0.003ms returns 0
TA260 002:590.874 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:590.877 - 0.003ms returns 0
TA260 002:590.881 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:590.884 - 0.003ms returns 0
TA260 002:590.888 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:590.892 - 0.003ms returns 0
TA260 002:590.896 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:590.901 - 0.004ms returns 0x0000001B
TA260 002:590.905 JLINK_Go()
TA260 002:590.915   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:593.700 - 2.795ms 
TA260 002:593.719 JLINK_IsHalted()
TA260 002:594.217 - 0.497ms returns FALSE
TA260 002:594.229 JLINK_HasError()
TA260 002:595.980 JLINK_IsHalted()
TA260 002:596.481 - 0.501ms returns FALSE
TA260 002:596.491 JLINK_HasError()
TA260 002:597.980 JLINK_IsHalted()
TA260 002:600.332   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:600.798 - 2.818ms returns TRUE
TA260 002:600.808 JLINK_ReadReg(R15 (PC))
TA260 002:600.814 - 0.005ms returns 0x20000000
TA260 002:600.818 JLINK_ClrBPEx(BPHandle = 0x0000001B)
TA260 002:600.822 - 0.004ms returns 0x00
TA260 002:600.827 JLINK_ReadReg(R0)
TA260 002:600.831 - 0.003ms returns 0x00000000
TA260 002:601.240 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:601.253   Data:  B0 EE 4A 0A F0 EE 6A 0A DA E7 00 2C 06 DA 9D ED ...
TA260 002:601.266   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:603.849 - 2.607ms returns 0x27C
TA260 002:603.860 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:603.864   Data:  51 EC 10 0B FC F7 AC F8 9F ED 22 1B 53 EC 11 2B ...
TA260 002:603.872   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:605.842 - 1.981ms returns 0x184
TA260 002:605.855 JLINK_HasError()
TA260 002:605.861 JLINK_WriteReg(R0, 0x08004000)
TA260 002:605.867 - 0.006ms returns 0
TA260 002:605.871 JLINK_WriteReg(R1, 0x00000400)
TA260 002:605.875 - 0.003ms returns 0
TA260 002:605.879 JLINK_WriteReg(R2, 0x20000184)
TA260 002:605.883 - 0.003ms returns 0
TA260 002:605.887 JLINK_WriteReg(R3, 0x00000000)
TA260 002:605.890 - 0.003ms returns 0
TA260 002:605.894 JLINK_WriteReg(R4, 0x00000000)
TA260 002:605.897 - 0.003ms returns 0
TA260 002:605.902 JLINK_WriteReg(R5, 0x00000000)
TA260 002:605.905 - 0.003ms returns 0
TA260 002:605.909 JLINK_WriteReg(R6, 0x00000000)
TA260 002:605.912 - 0.003ms returns 0
TA260 002:605.916 JLINK_WriteReg(R7, 0x00000000)
TA260 002:605.926 - 0.009ms returns 0
TA260 002:605.932 JLINK_WriteReg(R8, 0x00000000)
TA260 002:605.935 - 0.003ms returns 0
TA260 002:605.939 JLINK_WriteReg(R9, 0x20000180)
TA260 002:605.943 - 0.003ms returns 0
TA260 002:605.947 JLINK_WriteReg(R10, 0x00000000)
TA260 002:605.950 - 0.003ms returns 0
TA260 002:605.954 JLINK_WriteReg(R11, 0x00000000)
TA260 002:605.958 - 0.003ms returns 0
TA260 002:605.962 JLINK_WriteReg(R12, 0x00000000)
TA260 002:605.965 - 0.003ms returns 0
TA260 002:605.970 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:605.974 - 0.003ms returns 0
TA260 002:605.978 JLINK_WriteReg(R14, 0x20000001)
TA260 002:605.981 - 0.003ms returns 0
TA260 002:605.985 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:605.989 - 0.003ms returns 0
TA260 002:605.993 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:605.996 - 0.003ms returns 0
TA260 002:606.000 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:606.004 - 0.003ms returns 0
TA260 002:606.008 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:606.011 - 0.003ms returns 0
TA260 002:606.015 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:606.018 - 0.003ms returns 0
TA260 002:606.023 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:606.028 - 0.004ms returns 0x0000001C
TA260 002:606.032 JLINK_Go()
TA260 002:606.041   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:608.702 - 2.669ms 
TA260 002:608.720 JLINK_IsHalted()
TA260 002:609.183 - 0.463ms returns FALSE
TA260 002:609.189 JLINK_HasError()
TA260 002:610.484 JLINK_IsHalted()
TA260 002:610.983 - 0.498ms returns FALSE
TA260 002:610.995 JLINK_HasError()
TA260 002:612.992 JLINK_IsHalted()
TA260 002:615.326   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:615.808 - 2.816ms returns TRUE
TA260 002:615.819 JLINK_ReadReg(R15 (PC))
TA260 002:615.825 - 0.005ms returns 0x20000000
TA260 002:615.830 JLINK_ClrBPEx(BPHandle = 0x0000001C)
TA260 002:615.834 - 0.004ms returns 0x00
TA260 002:615.838 JLINK_ReadReg(R0)
TA260 002:615.842 - 0.003ms returns 0x00000000
TA260 002:616.245 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:616.256   Data:  D8 BF 00 24 08 DD D9 49 81 42 C8 BF 01 24 03 DC ...
TA260 002:616.268   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:618.855 - 2.610ms returns 0x27C
TA260 002:618.876 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:618.880   Data:  41 EC 10 0B 47 48 78 44 00 EB C4 00 90 ED 00 1B ...
TA260 002:618.893   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:620.848 - 1.971ms returns 0x184
TA260 002:620.864 JLINK_HasError()
TA260 002:620.908 JLINK_WriteReg(R0, 0x08004400)
TA260 002:620.917 - 0.009ms returns 0
TA260 002:620.922 JLINK_WriteReg(R1, 0x00000400)
TA260 002:620.925 - 0.003ms returns 0
TA260 002:620.930 JLINK_WriteReg(R2, 0x20000184)
TA260 002:620.933 - 0.003ms returns 0
TA260 002:620.938 JLINK_WriteReg(R3, 0x00000000)
TA260 002:620.941 - 0.003ms returns 0
TA260 002:620.946 JLINK_WriteReg(R4, 0x00000000)
TA260 002:620.949 - 0.003ms returns 0
TA260 002:620.953 JLINK_WriteReg(R5, 0x00000000)
TA260 002:620.956 - 0.003ms returns 0
TA260 002:620.960 JLINK_WriteReg(R6, 0x00000000)
TA260 002:620.964 - 0.003ms returns 0
TA260 002:620.968 JLINK_WriteReg(R7, 0x00000000)
TA260 002:620.971 - 0.003ms returns 0
TA260 002:620.975 JLINK_WriteReg(R8, 0x00000000)
TA260 002:620.979 - 0.003ms returns 0
TA260 002:620.983 JLINK_WriteReg(R9, 0x20000180)
TA260 002:620.986 - 0.003ms returns 0
TA260 002:620.990 JLINK_WriteReg(R10, 0x00000000)
TA260 002:620.994 - 0.003ms returns 0
TA260 002:620.998 JLINK_WriteReg(R11, 0x00000000)
TA260 002:621.001 - 0.003ms returns 0
TA260 002:621.005 JLINK_WriteReg(R12, 0x00000000)
TA260 002:621.009 - 0.003ms returns 0
TA260 002:621.014 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:621.019 - 0.004ms returns 0
TA260 002:621.023 JLINK_WriteReg(R14, 0x20000001)
TA260 002:621.026 - 0.003ms returns 0
TA260 002:621.030 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:621.034 - 0.003ms returns 0
TA260 002:621.038 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:621.041 - 0.003ms returns 0
TA260 002:621.046 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:621.053 - 0.007ms returns 0
TA260 002:621.060 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:621.064 - 0.003ms returns 0
TA260 002:621.068 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:621.071 - 0.003ms returns 0
TA260 002:621.076 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:621.081 - 0.005ms returns 0x0000001D
TA260 002:621.085 JLINK_Go()
TA260 002:621.095   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:623.739 - 2.653ms 
TA260 002:623.754 JLINK_IsHalted()
TA260 002:624.236 - 0.480ms returns FALSE
TA260 002:624.254 JLINK_HasError()
TA260 002:625.502 JLINK_IsHalted()
TA260 002:626.020 - 0.518ms returns FALSE
TA260 002:626.028 JLINK_HasError()
TA260 002:627.165 JLINK_IsHalted()
TA260 002:627.719 - 0.553ms returns FALSE
TA260 002:627.732 JLINK_HasError()
TA260 002:629.664 JLINK_IsHalted()
TA260 002:631.976   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:632.494 - 2.829ms returns TRUE
TA260 002:632.507 JLINK_ReadReg(R15 (PC))
TA260 002:632.514 - 0.006ms returns 0x20000000
TA260 002:632.518 JLINK_ClrBPEx(BPHandle = 0x0000001D)
TA260 002:632.523 - 0.004ms returns 0x00
TA260 002:632.527 JLINK_ReadReg(R0)
TA260 002:632.531 - 0.003ms returns 0x00000000
TA260 002:632.915 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:632.926   Data:  10 2B 9D ED 02 0B 51 EC 10 0B FB F7 E1 FD 41 EC ...
TA260 002:632.938   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:635.579 - 2.663ms returns 0x27C
TA260 002:635.595 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:635.599   Data:  FB F7 0D FF CD E9 02 01 9D ED 02 0B 51 EC 18 0B ...
TA260 002:635.611   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:637.525 - 1.929ms returns 0x184
TA260 002:637.549 JLINK_HasError()
TA260 002:637.556 JLINK_WriteReg(R0, 0x08004800)
TA260 002:637.562 - 0.006ms returns 0
TA260 002:637.567 JLINK_WriteReg(R1, 0x00000400)
TA260 002:637.570 - 0.003ms returns 0
TA260 002:637.574 JLINK_WriteReg(R2, 0x20000184)
TA260 002:637.578 - 0.003ms returns 0
TA260 002:637.582 JLINK_WriteReg(R3, 0x00000000)
TA260 002:637.586 - 0.003ms returns 0
TA260 002:637.590 JLINK_WriteReg(R4, 0x00000000)
TA260 002:637.593 - 0.003ms returns 0
TA260 002:637.597 JLINK_WriteReg(R5, 0x00000000)
TA260 002:637.600 - 0.003ms returns 0
TA260 002:637.604 JLINK_WriteReg(R6, 0x00000000)
TA260 002:637.608 - 0.003ms returns 0
TA260 002:637.612 JLINK_WriteReg(R7, 0x00000000)
TA260 002:637.615 - 0.003ms returns 0
TA260 002:637.619 JLINK_WriteReg(R8, 0x00000000)
TA260 002:637.623 - 0.003ms returns 0
TA260 002:637.626 JLINK_WriteReg(R9, 0x20000180)
TA260 002:637.630 - 0.003ms returns 0
TA260 002:637.634 JLINK_WriteReg(R10, 0x00000000)
TA260 002:637.637 - 0.003ms returns 0
TA260 002:637.641 JLINK_WriteReg(R11, 0x00000000)
TA260 002:637.645 - 0.003ms returns 0
TA260 002:637.649 JLINK_WriteReg(R12, 0x00000000)
TA260 002:637.652 - 0.003ms returns 0
TA260 002:637.656 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:637.661 - 0.004ms returns 0
TA260 002:637.665 JLINK_WriteReg(R14, 0x20000001)
TA260 002:637.668 - 0.003ms returns 0
TA260 002:637.672 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:637.676 - 0.003ms returns 0
TA260 002:637.680 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:637.683 - 0.003ms returns 0
TA260 002:637.687 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:637.691 - 0.003ms returns 0
TA260 002:637.695 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:637.698 - 0.003ms returns 0
TA260 002:637.702 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:637.706 - 0.003ms returns 0
TA260 002:637.710 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:637.715 - 0.005ms returns 0x0000001E
TA260 002:637.720 JLINK_Go()
TA260 002:637.730   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:640.394 - 2.673ms 
TA260 002:640.414 JLINK_IsHalted()
TA260 002:640.892 - 0.477ms returns FALSE
TA260 002:640.905 JLINK_HasError()
TA260 002:643.177 JLINK_IsHalted()
TA260 002:643.704 - 0.526ms returns FALSE
TA260 002:643.716 JLINK_HasError()
TA260 002:646.680 JLINK_IsHalted()
TA260 002:649.004   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:649.513 - 2.832ms returns TRUE
TA260 002:649.519 JLINK_ReadReg(R15 (PC))
TA260 002:649.524 - 0.005ms returns 0x20000000
TA260 002:649.528 JLINK_ClrBPEx(BPHandle = 0x0000001E)
TA260 002:649.532 - 0.004ms returns 0x00
TA260 002:649.537 JLINK_ReadReg(R0)
TA260 002:649.540 - 0.003ms returns 0x00000000
TA260 002:649.897 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:649.906   Data:  FB F7 EC FB 94 ED 08 1B 53 EC 11 2B FB F7 3F FB ...
TA260 002:649.917   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:652.575 - 2.677ms returns 0x27C
TA260 002:652.589 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:652.593   Data:  08 43 0D D1 0A E0 08 43 04 D0 00 20 4F F0 11 0B ...
TA260 002:652.602   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:654.478 - 1.888ms returns 0x184
TA260 002:654.486 JLINK_HasError()
TA260 002:654.492 JLINK_WriteReg(R0, 0x08004C00)
TA260 002:654.497 - 0.004ms returns 0
TA260 002:654.501 JLINK_WriteReg(R1, 0x00000400)
TA260 002:654.505 - 0.003ms returns 0
TA260 002:654.509 JLINK_WriteReg(R2, 0x20000184)
TA260 002:654.512 - 0.003ms returns 0
TA260 002:654.516 JLINK_WriteReg(R3, 0x00000000)
TA260 002:654.520 - 0.003ms returns 0
TA260 002:654.524 JLINK_WriteReg(R4, 0x00000000)
TA260 002:654.527 - 0.003ms returns 0
TA260 002:654.532 JLINK_WriteReg(R5, 0x00000000)
TA260 002:654.535 - 0.003ms returns 0
TA260 002:654.539 JLINK_WriteReg(R6, 0x00000000)
TA260 002:654.542 - 0.003ms returns 0
TA260 002:654.546 JLINK_WriteReg(R7, 0x00000000)
TA260 002:654.550 - 0.003ms returns 0
TA260 002:654.554 JLINK_WriteReg(R8, 0x00000000)
TA260 002:654.557 - 0.003ms returns 0
TA260 002:654.561 JLINK_WriteReg(R9, 0x20000180)
TA260 002:654.564 - 0.003ms returns 0
TA260 002:654.568 JLINK_WriteReg(R10, 0x00000000)
TA260 002:654.572 - 0.003ms returns 0
TA260 002:654.576 JLINK_WriteReg(R11, 0x00000000)
TA260 002:654.579 - 0.003ms returns 0
TA260 002:654.583 JLINK_WriteReg(R12, 0x00000000)
TA260 002:654.587 - 0.003ms returns 0
TA260 002:654.591 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:654.595 - 0.004ms returns 0
TA260 002:654.600 JLINK_WriteReg(R14, 0x20000001)
TA260 002:654.603 - 0.003ms returns 0
TA260 002:654.608 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:654.611 - 0.003ms returns 0
TA260 002:654.615 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:654.618 - 0.003ms returns 0
TA260 002:654.622 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:654.626 - 0.003ms returns 0
TA260 002:654.630 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:654.633 - 0.003ms returns 0
TA260 002:654.637 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:654.641 - 0.003ms returns 0
TA260 002:654.645 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:654.649 - 0.004ms returns 0x0000001F
TA260 002:654.654 JLINK_Go()
TA260 002:654.662   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:657.376 - 2.722ms 
TA260 002:657.388 JLINK_IsHalted()
TA260 002:657.856 - 0.467ms returns FALSE
TA260 002:657.869 JLINK_HasError()
TA260 002:660.692 JLINK_IsHalted()
TA260 002:661.140 - 0.448ms returns FALSE
TA260 002:661.147 JLINK_HasError()
TA260 002:663.196 JLINK_IsHalted()
TA260 002:665.549   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:666.021 - 2.825ms returns TRUE
TA260 002:666.027 JLINK_ReadReg(R15 (PC))
TA260 002:666.032 - 0.005ms returns 0x20000000
TA260 002:666.037 JLINK_ClrBPEx(BPHandle = 0x0000001F)
TA260 002:666.041 - 0.003ms returns 0x00
TA260 002:666.045 JLINK_ReadReg(R0)
TA260 002:666.048 - 0.003ms returns 0x00000000
TA260 002:666.364 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:666.372   Data:  06 D1 B5 E0 73 28 2C D0 75 28 75 D0 78 28 74 D0 ...
TA260 002:666.382   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:668.985 - 2.620ms returns 0x27C
TA260 002:668.998 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:669.002   Data:  C0 5D 17 99 90 47 6D 1C 7F 1C 47 45 F6 DB E0 03 ...
TA260 002:669.012   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:670.843 - 1.845ms returns 0x184
TA260 002:670.850 JLINK_HasError()
TA260 002:670.855 JLINK_WriteReg(R0, 0x08005000)
TA260 002:670.862 - 0.007ms returns 0
TA260 002:670.868 JLINK_WriteReg(R1, 0x00000400)
TA260 002:670.872 - 0.003ms returns 0
TA260 002:670.876 JLINK_WriteReg(R2, 0x20000184)
TA260 002:670.879 - 0.003ms returns 0
TA260 002:670.883 JLINK_WriteReg(R3, 0x00000000)
TA260 002:670.887 - 0.003ms returns 0
TA260 002:670.891 JLINK_WriteReg(R4, 0x00000000)
TA260 002:670.894 - 0.003ms returns 0
TA260 002:670.898 JLINK_WriteReg(R5, 0x00000000)
TA260 002:670.901 - 0.003ms returns 0
TA260 002:670.906 JLINK_WriteReg(R6, 0x00000000)
TA260 002:670.909 - 0.003ms returns 0
TA260 002:670.913 JLINK_WriteReg(R7, 0x00000000)
TA260 002:670.916 - 0.003ms returns 0
TA260 002:670.920 JLINK_WriteReg(R8, 0x00000000)
TA260 002:670.924 - 0.003ms returns 0
TA260 002:670.928 JLINK_WriteReg(R9, 0x20000180)
TA260 002:670.931 - 0.003ms returns 0
TA260 002:670.935 JLINK_WriteReg(R10, 0x00000000)
TA260 002:670.938 - 0.003ms returns 0
TA260 002:670.943 JLINK_WriteReg(R11, 0x00000000)
TA260 002:670.946 - 0.003ms returns 0
TA260 002:670.950 JLINK_WriteReg(R12, 0x00000000)
TA260 002:670.954 - 0.003ms returns 0
TA260 002:670.958 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:670.962 - 0.003ms returns 0
TA260 002:670.966 JLINK_WriteReg(R14, 0x20000001)
TA260 002:670.969 - 0.003ms returns 0
TA260 002:670.973 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:670.976 - 0.003ms returns 0
TA260 002:670.981 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:670.984 - 0.003ms returns 0
TA260 002:670.988 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:670.991 - 0.003ms returns 0
TA260 002:670.995 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:670.999 - 0.003ms returns 0
TA260 002:671.003 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:671.006 - 0.003ms returns 0
TA260 002:671.011 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:671.015 - 0.004ms returns 0x00000020
TA260 002:671.019 JLINK_Go()
TA260 002:671.026   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:673.723 - 2.703ms 
TA260 002:673.739 JLINK_IsHalted()
TA260 002:674.226 - 0.486ms returns FALSE
TA260 002:674.236 JLINK_HasError()
TA260 002:675.705 JLINK_IsHalted()
TA260 002:676.182 - 0.476ms returns FALSE
TA260 002:676.188 JLINK_HasError()
TA260 002:677.705 JLINK_IsHalted()
TA260 002:680.055   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:680.616 - 2.910ms returns TRUE
TA260 002:680.622 JLINK_ReadReg(R15 (PC))
TA260 002:680.627 - 0.004ms returns 0x20000000
TA260 002:680.631 JLINK_ClrBPEx(BPHandle = 0x00000020)
TA260 002:680.635 - 0.003ms returns 0x00
TA260 002:680.639 JLINK_ReadReg(R0)
TA260 002:680.643 - 0.003ms returns 0x00000000
TA260 002:680.981 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:680.988   Data:  11 20 00 E0 38 46 CD E9 00 01 06 A9 0E A8 FF F7 ...
TA260 002:680.998   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:683.571 - 2.590ms returns 0x27C
TA260 002:683.577 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:683.581   Data:  01 21 08 43 20 F0 00 40 C0 F1 7F 60 00 F1 E0 40 ...
TA260 002:683.588   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:685.474 - 1.896ms returns 0x184
TA260 002:685.480 JLINK_HasError()
TA260 002:685.486 JLINK_WriteReg(R0, 0x08005400)
TA260 002:685.490 - 0.004ms returns 0
TA260 002:685.495 JLINK_WriteReg(R1, 0x00000400)
TA260 002:685.498 - 0.003ms returns 0
TA260 002:685.502 JLINK_WriteReg(R2, 0x20000184)
TA260 002:685.506 - 0.003ms returns 0
TA260 002:685.510 JLINK_WriteReg(R3, 0x00000000)
TA260 002:685.513 - 0.003ms returns 0
TA260 002:685.518 JLINK_WriteReg(R4, 0x00000000)
TA260 002:685.521 - 0.003ms returns 0
TA260 002:685.525 JLINK_WriteReg(R5, 0x00000000)
TA260 002:685.528 - 0.003ms returns 0
TA260 002:685.532 JLINK_WriteReg(R6, 0x00000000)
TA260 002:685.536 - 0.003ms returns 0
TA260 002:685.540 JLINK_WriteReg(R7, 0x00000000)
TA260 002:685.543 - 0.003ms returns 0
TA260 002:685.547 JLINK_WriteReg(R8, 0x00000000)
TA260 002:685.550 - 0.003ms returns 0
TA260 002:685.554 JLINK_WriteReg(R9, 0x20000180)
TA260 002:685.558 - 0.003ms returns 0
TA260 002:685.562 JLINK_WriteReg(R10, 0x00000000)
TA260 002:685.571 - 0.009ms returns 0
TA260 002:685.622 JLINK_WriteReg(R11, 0x00000000)
TA260 002:685.626 - 0.003ms returns 0
TA260 002:685.630 JLINK_WriteReg(R12, 0x00000000)
TA260 002:685.633 - 0.003ms returns 0
TA260 002:685.638 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:685.641 - 0.003ms returns 0
TA260 002:685.655 JLINK_WriteReg(R14, 0x20000001)
TA260 002:685.659 - 0.003ms returns 0
TA260 002:685.663 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:685.666 - 0.003ms returns 0
TA260 002:685.671 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:685.674 - 0.003ms returns 0
TA260 002:685.678 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:685.682 - 0.003ms returns 0
TA260 002:685.686 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:685.689 - 0.003ms returns 0
TA260 002:685.693 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:685.697 - 0.003ms returns 0
TA260 002:685.701 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:685.705 - 0.004ms returns 0x00000021
TA260 002:685.709 JLINK_Go()
TA260 002:685.717   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:688.503 - 2.793ms 
TA260 002:688.513 JLINK_IsHalted()
TA260 002:688.976 - 0.463ms returns FALSE
TA260 002:688.982 JLINK_HasError()
TA260 002:690.215 JLINK_IsHalted()
TA260 002:690.706 - 0.491ms returns FALSE
TA260 002:690.714 JLINK_HasError()
TA260 002:693.217 JLINK_IsHalted()
TA260 002:695.530   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:695.997 - 2.779ms returns TRUE
TA260 002:696.004 JLINK_ReadReg(R15 (PC))
TA260 002:696.008 - 0.004ms returns 0x20000000
TA260 002:696.013 JLINK_ClrBPEx(BPHandle = 0x00000021)
TA260 002:696.017 - 0.003ms returns 0x00
TA260 002:696.021 JLINK_ReadReg(R0)
TA260 002:696.025 - 0.003ms returns 0x00000000
TA260 002:696.470 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:696.481   Data:  90 01 90 05 90 03 90 07 50 00 50 04 50 02 50 06 ...
TA260 002:696.492   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:699.147 - 2.677ms returns 0x27C
TA260 002:699.166 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:699.170   Data:  8C 02 8C 06 8C 01 8C 05 8C 03 8C 07 4C 00 4C 04 ...
TA260 002:699.180   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:701.056 - 1.889ms returns 0x184
TA260 002:701.065 JLINK_HasError()
TA260 002:701.095 JLINK_WriteReg(R0, 0x08005800)
TA260 002:701.101 - 0.006ms returns 0
TA260 002:701.105 JLINK_WriteReg(R1, 0x00000400)
TA260 002:701.109 - 0.003ms returns 0
TA260 002:701.113 JLINK_WriteReg(R2, 0x20000184)
TA260 002:701.116 - 0.003ms returns 0
TA260 002:701.120 JLINK_WriteReg(R3, 0x00000000)
TA260 002:701.124 - 0.003ms returns 0
TA260 002:701.128 JLINK_WriteReg(R4, 0x00000000)
TA260 002:701.132 - 0.003ms returns 0
TA260 002:701.136 JLINK_WriteReg(R5, 0x00000000)
TA260 002:701.139 - 0.003ms returns 0
TA260 002:701.143 JLINK_WriteReg(R6, 0x00000000)
TA260 002:701.146 - 0.003ms returns 0
TA260 002:701.150 JLINK_WriteReg(R7, 0x00000000)
TA260 002:701.154 - 0.003ms returns 0
TA260 002:701.158 JLINK_WriteReg(R8, 0x00000000)
TA260 002:701.161 - 0.003ms returns 0
TA260 002:701.165 JLINK_WriteReg(R9, 0x20000180)
TA260 002:701.169 - 0.003ms returns 0
TA260 002:701.173 JLINK_WriteReg(R10, 0x00000000)
TA260 002:701.176 - 0.003ms returns 0
TA260 002:701.180 JLINK_WriteReg(R11, 0x00000000)
TA260 002:701.184 - 0.003ms returns 0
TA260 002:701.188 JLINK_WriteReg(R12, 0x00000000)
TA260 002:701.191 - 0.003ms returns 0
TA260 002:701.195 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:701.199 - 0.003ms returns 0
TA260 002:701.203 JLINK_WriteReg(R14, 0x20000001)
TA260 002:701.206 - 0.003ms returns 0
TA260 002:701.210 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:701.214 - 0.003ms returns 0
TA260 002:701.218 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:701.221 - 0.003ms returns 0
TA260 002:701.225 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:701.229 - 0.003ms returns 0
TA260 002:701.233 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:701.236 - 0.003ms returns 0
TA260 002:701.240 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:701.244 - 0.003ms returns 0
TA260 002:701.248 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:701.256 - 0.007ms returns 0x00000022
TA260 002:701.261 JLINK_Go()
TA260 002:701.268   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:704.032 - 2.770ms 
TA260 002:704.038 JLINK_IsHalted()
TA260 002:704.492 - 0.453ms returns FALSE
TA260 002:704.504 JLINK_HasError()
TA260 002:707.227 JLINK_IsHalted()
TA260 002:712.605   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:713.118 - 5.891ms returns TRUE
TA260 002:713.128 JLINK_ReadReg(R15 (PC))
TA260 002:713.135 - 0.006ms returns 0x20000000
TA260 002:713.139 JLINK_ClrBPEx(BPHandle = 0x00000022)
TA260 002:713.144 - 0.004ms returns 0x00
TA260 002:713.148 JLINK_ReadReg(R0)
TA260 002:713.152 - 0.003ms returns 0x00000000
TA260 002:713.538 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:713.548   Data:  92 01 92 05 92 03 92 07 52 00 52 04 52 02 52 06 ...
TA260 002:713.560   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:716.279 - 2.740ms returns 0x27C
TA260 002:716.291 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:716.295   Data:  8E 02 8E 06 8E 01 8E 05 8E 03 8E 07 4E 00 4E 04 ...
TA260 002:716.305   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:718.199 - 1.907ms returns 0x184
TA260 002:718.213 JLINK_HasError()
TA260 002:718.218 JLINK_WriteReg(R0, 0x08005C00)
TA260 002:718.224 - 0.005ms returns 0
TA260 002:718.228 JLINK_WriteReg(R1, 0x00000400)
TA260 002:718.231 - 0.003ms returns 0
TA260 002:718.235 JLINK_WriteReg(R2, 0x20000184)
TA260 002:718.239 - 0.003ms returns 0
TA260 002:718.243 JLINK_WriteReg(R3, 0x00000000)
TA260 002:718.246 - 0.003ms returns 0
TA260 002:718.250 JLINK_WriteReg(R4, 0x00000000)
TA260 002:718.254 - 0.003ms returns 0
TA260 002:718.258 JLINK_WriteReg(R5, 0x00000000)
TA260 002:718.261 - 0.003ms returns 0
TA260 002:718.265 JLINK_WriteReg(R6, 0x00000000)
TA260 002:718.269 - 0.003ms returns 0
TA260 002:718.273 JLINK_WriteReg(R7, 0x00000000)
TA260 002:718.276 - 0.003ms returns 0
TA260 002:718.280 JLINK_WriteReg(R8, 0x00000000)
TA260 002:718.284 - 0.003ms returns 0
TA260 002:718.288 JLINK_WriteReg(R9, 0x20000180)
TA260 002:718.291 - 0.003ms returns 0
TA260 002:718.295 JLINK_WriteReg(R10, 0x00000000)
TA260 002:718.298 - 0.003ms returns 0
TA260 002:718.303 JLINK_WriteReg(R11, 0x00000000)
TA260 002:718.306 - 0.003ms returns 0
TA260 002:718.310 JLINK_WriteReg(R12, 0x00000000)
TA260 002:718.314 - 0.003ms returns 0
TA260 002:718.318 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:718.322 - 0.003ms returns 0
TA260 002:718.326 JLINK_WriteReg(R14, 0x20000001)
TA260 002:718.329 - 0.003ms returns 0
TA260 002:718.333 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:718.337 - 0.003ms returns 0
TA260 002:718.341 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:718.344 - 0.003ms returns 0
TA260 002:718.348 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:718.352 - 0.003ms returns 0
TA260 002:718.356 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:718.359 - 0.003ms returns 0
TA260 002:718.363 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:718.367 - 0.003ms returns 0
TA260 002:718.371 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:718.376 - 0.004ms returns 0x00000023
TA260 002:718.380 JLINK_Go()
TA260 002:718.388   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:721.140 - 2.759ms 
TA260 002:721.152 JLINK_IsHalted()
TA260 002:721.696 - 0.544ms returns FALSE
TA260 002:721.709 JLINK_HasError()
TA260 002:724.233 JLINK_IsHalted()
TA260 002:724.761 - 0.527ms returns FALSE
TA260 002:724.768 JLINK_HasError()
TA260 002:727.236 JLINK_IsHalted()
TA260 002:729.541   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:730.019 - 2.782ms returns TRUE
TA260 002:730.026 JLINK_ReadReg(R15 (PC))
TA260 002:730.032 - 0.005ms returns 0x20000000
TA260 002:730.037 JLINK_ClrBPEx(BPHandle = 0x00000023)
TA260 002:730.041 - 0.004ms returns 0x00
TA260 002:730.045 JLINK_ReadReg(R0)
TA260 002:730.049 - 0.003ms returns 0x00000000
TA260 002:730.406 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:730.413   Data:  4F 92 E0 3E 75 33 E6 3E BB CB EB 3E EA 5A F1 3E ...
TA260 002:730.424   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:733.056 - 2.650ms returns 0x27C
TA260 002:733.065 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:733.068   Data:  4A EB 2B 3F 15 94 29 3F 56 36 27 3F 25 D2 24 3F ...
TA260 002:733.077   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:734.974 - 1.909ms returns 0x184
TA260 002:734.982 JLINK_HasError()
TA260 002:734.987 JLINK_WriteReg(R0, 0x08006000)
TA260 002:734.992 - 0.004ms returns 0
TA260 002:734.996 JLINK_WriteReg(R1, 0x00000400)
TA260 002:734.999 - 0.003ms returns 0
TA260 002:735.003 JLINK_WriteReg(R2, 0x20000184)
TA260 002:735.007 - 0.003ms returns 0
TA260 002:735.011 JLINK_WriteReg(R3, 0x00000000)
TA260 002:735.014 - 0.003ms returns 0
TA260 002:735.018 JLINK_WriteReg(R4, 0x00000000)
TA260 002:735.021 - 0.003ms returns 0
TA260 002:735.025 JLINK_WriteReg(R5, 0x00000000)
TA260 002:735.029 - 0.003ms returns 0
TA260 002:735.033 JLINK_WriteReg(R6, 0x00000000)
TA260 002:735.036 - 0.003ms returns 0
TA260 002:735.041 JLINK_WriteReg(R7, 0x00000000)
TA260 002:735.044 - 0.003ms returns 0
TA260 002:735.049 JLINK_WriteReg(R8, 0x00000000)
TA260 002:735.052 - 0.003ms returns 0
TA260 002:735.056 JLINK_WriteReg(R9, 0x20000180)
TA260 002:735.059 - 0.003ms returns 0
TA260 002:735.063 JLINK_WriteReg(R10, 0x00000000)
TA260 002:735.067 - 0.003ms returns 0
TA260 002:735.071 JLINK_WriteReg(R11, 0x00000000)
TA260 002:735.074 - 0.003ms returns 0
TA260 002:735.078 JLINK_WriteReg(R12, 0x00000000)
TA260 002:735.081 - 0.003ms returns 0
TA260 002:735.086 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:735.089 - 0.004ms returns 0
TA260 002:735.094 JLINK_WriteReg(R14, 0x20000001)
TA260 002:735.097 - 0.003ms returns 0
TA260 002:735.101 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:735.104 - 0.003ms returns 0
TA260 002:735.108 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:735.112 - 0.003ms returns 0
TA260 002:735.116 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:735.119 - 0.003ms returns 0
TA260 002:735.124 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:735.127 - 0.003ms returns 0
TA260 002:735.131 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:735.134 - 0.003ms returns 0
TA260 002:735.139 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:735.143 - 0.004ms returns 0x00000024
TA260 002:735.147 JLINK_Go()
TA260 002:735.154   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:737.920 - 2.773ms 
TA260 002:737.934 JLINK_IsHalted()
TA260 002:738.384 - 0.450ms returns FALSE
TA260 002:738.390 JLINK_HasError()
TA260 002:740.740 JLINK_IsHalted()
TA260 002:741.214 - 0.473ms returns FALSE
TA260 002:741.220 JLINK_HasError()
TA260 002:745.246 JLINK_IsHalted()
TA260 002:747.589   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:748.085 - 2.838ms returns TRUE
TA260 002:748.098 JLINK_ReadReg(R15 (PC))
TA260 002:748.104 - 0.006ms returns 0x20000000
TA260 002:748.108 JLINK_ClrBPEx(BPHandle = 0x00000024)
TA260 002:748.112 - 0.004ms returns 0x00
TA260 002:748.117 JLINK_ReadReg(R0)
TA260 002:748.121 - 0.004ms returns 0x00000000
TA260 002:748.429 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:748.458   Data:  4F 92 E0 BE 75 33 E6 BE BB CB EB BE EA 5A F1 BE ...
TA260 002:748.469   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:751.019 - 2.590ms returns 0x27C
TA260 002:751.026 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:751.029   Data:  4A EB 2B BF 15 94 29 BF 56 36 27 BF 25 D2 24 BF ...
TA260 002:751.036   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:752.845 - 1.818ms returns 0x184
TA260 002:752.863 JLINK_HasError()
TA260 002:752.894 JLINK_WriteReg(R0, 0x08006400)
TA260 002:752.900 - 0.006ms returns 0
TA260 002:752.905 JLINK_WriteReg(R1, 0x00000400)
TA260 002:752.908 - 0.003ms returns 0
TA260 002:752.913 JLINK_WriteReg(R2, 0x20000184)
TA260 002:752.916 - 0.003ms returns 0
TA260 002:752.920 JLINK_WriteReg(R3, 0x00000000)
TA260 002:752.924 - 0.003ms returns 0
TA260 002:752.928 JLINK_WriteReg(R4, 0x00000000)
TA260 002:752.931 - 0.003ms returns 0
TA260 002:752.935 JLINK_WriteReg(R5, 0x00000000)
TA260 002:752.938 - 0.003ms returns 0
TA260 002:752.942 JLINK_WriteReg(R6, 0x00000000)
TA260 002:752.950 - 0.007ms returns 0
TA260 002:752.954 JLINK_WriteReg(R7, 0x00000000)
TA260 002:752.958 - 0.003ms returns 0
TA260 002:752.962 JLINK_WriteReg(R8, 0x00000000)
TA260 002:752.965 - 0.003ms returns 0
TA260 002:752.969 JLINK_WriteReg(R9, 0x20000180)
TA260 002:752.973 - 0.003ms returns 0
TA260 002:752.977 JLINK_WriteReg(R10, 0x00000000)
TA260 002:752.980 - 0.003ms returns 0
TA260 002:752.984 JLINK_WriteReg(R11, 0x00000000)
TA260 002:752.988 - 0.003ms returns 0
TA260 002:752.992 JLINK_WriteReg(R12, 0x00000000)
TA260 002:752.995 - 0.003ms returns 0
TA260 002:752.999 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:753.003 - 0.003ms returns 0
TA260 002:753.007 JLINK_WriteReg(R14, 0x20000001)
TA260 002:753.010 - 0.003ms returns 0
TA260 002:753.014 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:753.018 - 0.003ms returns 0
TA260 002:753.022 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:753.025 - 0.003ms returns 0
TA260 002:753.029 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:753.033 - 0.003ms returns 0
TA260 002:753.037 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:753.040 - 0.003ms returns 0
TA260 002:753.044 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:753.048 - 0.003ms returns 0
TA260 002:753.052 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:753.057 - 0.004ms returns 0x00000025
TA260 002:753.061 JLINK_Go()
TA260 002:753.069   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:755.758 - 2.696ms 
TA260 002:755.765 JLINK_IsHalted()
TA260 002:756.224 - 0.458ms returns FALSE
TA260 002:756.230 JLINK_HasError()
TA260 002:762.262 JLINK_IsHalted()
TA260 002:764.592   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:765.078 - 2.815ms returns TRUE
TA260 002:765.085 JLINK_ReadReg(R15 (PC))
TA260 002:765.090 - 0.005ms returns 0x20000000
TA260 002:765.095 JLINK_ClrBPEx(BPHandle = 0x00000025)
TA260 002:765.099 - 0.004ms returns 0x00
TA260 002:765.103 JLINK_ReadReg(R0)
TA260 002:765.107 - 0.003ms returns 0x00000000
TA260 002:765.414 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:765.421   Data:  6C 3D 25 64 FF FF FF 00 00 00 80 3F 00 00 00 00 ...
TA260 002:765.447   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:768.078 - 2.663ms returns 0x27C
TA260 002:768.093 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:768.099   Data:  C0 75 F4 3D 5E 1F 7E 3F 2C 94 F7 3D 24 13 7E 3F ...
TA260 002:768.109   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:770.025 - 1.931ms returns 0x184
TA260 002:770.039 JLINK_HasError()
TA260 002:770.045 JLINK_WriteReg(R0, 0x08006800)
TA260 002:770.050 - 0.005ms returns 0
TA260 002:770.055 JLINK_WriteReg(R1, 0x00000400)
TA260 002:770.058 - 0.003ms returns 0
TA260 002:770.062 JLINK_WriteReg(R2, 0x20000184)
TA260 002:770.066 - 0.003ms returns 0
TA260 002:770.070 JLINK_WriteReg(R3, 0x00000000)
TA260 002:770.073 - 0.003ms returns 0
TA260 002:770.077 JLINK_WriteReg(R4, 0x00000000)
TA260 002:770.080 - 0.003ms returns 0
TA260 002:770.084 JLINK_WriteReg(R5, 0x00000000)
TA260 002:770.088 - 0.003ms returns 0
TA260 002:770.092 JLINK_WriteReg(R6, 0x00000000)
TA260 002:770.095 - 0.003ms returns 0
TA260 002:770.099 JLINK_WriteReg(R7, 0x00000000)
TA260 002:770.103 - 0.003ms returns 0
TA260 002:770.107 JLINK_WriteReg(R8, 0x00000000)
TA260 002:770.110 - 0.003ms returns 0
TA260 002:770.114 JLINK_WriteReg(R9, 0x20000180)
TA260 002:770.118 - 0.003ms returns 0
TA260 002:770.122 JLINK_WriteReg(R10, 0x00000000)
TA260 002:770.125 - 0.003ms returns 0
TA260 002:770.129 JLINK_WriteReg(R11, 0x00000000)
TA260 002:770.133 - 0.003ms returns 0
TA260 002:770.137 JLINK_WriteReg(R12, 0x00000000)
TA260 002:770.140 - 0.003ms returns 0
TA260 002:770.145 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:770.149 - 0.004ms returns 0
TA260 002:770.153 JLINK_WriteReg(R14, 0x20000001)
TA260 002:770.156 - 0.003ms returns 0
TA260 002:770.160 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:770.164 - 0.003ms returns 0
TA260 002:770.168 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:770.171 - 0.003ms returns 0
TA260 002:770.175 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:770.181 - 0.006ms returns 0
TA260 002:770.187 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:770.190 - 0.003ms returns 0
TA260 002:770.194 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:770.198 - 0.003ms returns 0
TA260 002:770.202 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:770.206 - 0.004ms returns 0x00000026
TA260 002:770.211 JLINK_Go()
TA260 002:770.219   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:772.942 - 2.731ms 
TA260 002:772.953 JLINK_IsHalted()
TA260 002:773.492 - 0.539ms returns FALSE
TA260 002:773.505 JLINK_HasError()
TA260 002:779.268 JLINK_IsHalted()
TA260 002:781.605   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:782.112 - 2.844ms returns TRUE
TA260 002:782.119 JLINK_ReadReg(R15 (PC))
TA260 002:782.125 - 0.005ms returns 0x20000000
TA260 002:782.130 JLINK_ClrBPEx(BPHandle = 0x00000026)
TA260 002:782.133 - 0.003ms returns 0x00
TA260 002:782.138 JLINK_ReadReg(R0)
TA260 002:782.141 - 0.003ms returns 0x00000000
TA260 002:782.676 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:782.687   Data:  48 28 7B 3F 4D 3B 46 3E BE 14 7B 3F C2 C5 47 3E ...
TA260 002:782.698   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:785.334 - 2.657ms returns 0x27C
TA260 002:785.346 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:785.350   Data:  DF 1C 9F 3E BE 33 73 3F EE DB 9F 3E 47 14 73 3F ...
TA260 002:785.359   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:787.253 - 1.906ms returns 0x184
TA260 002:787.266 JLINK_HasError()
TA260 002:787.272 JLINK_WriteReg(R0, 0x08006C00)
TA260 002:787.277 - 0.005ms returns 0
TA260 002:787.281 JLINK_WriteReg(R1, 0x00000400)
TA260 002:787.285 - 0.003ms returns 0
TA260 002:787.289 JLINK_WriteReg(R2, 0x20000184)
TA260 002:787.292 - 0.003ms returns 0
TA260 002:787.296 JLINK_WriteReg(R3, 0x00000000)
TA260 002:787.300 - 0.003ms returns 0
TA260 002:787.304 JLINK_WriteReg(R4, 0x00000000)
TA260 002:787.307 - 0.003ms returns 0
TA260 002:787.311 JLINK_WriteReg(R5, 0x00000000)
TA260 002:787.315 - 0.003ms returns 0
TA260 002:787.319 JLINK_WriteReg(R6, 0x00000000)
TA260 002:787.322 - 0.003ms returns 0
TA260 002:787.326 JLINK_WriteReg(R7, 0x00000000)
TA260 002:787.330 - 0.003ms returns 0
TA260 002:787.334 JLINK_WriteReg(R8, 0x00000000)
TA260 002:787.337 - 0.003ms returns 0
TA260 002:787.341 JLINK_WriteReg(R9, 0x20000180)
TA260 002:787.345 - 0.003ms returns 0
TA260 002:787.349 JLINK_WriteReg(R10, 0x00000000)
TA260 002:787.352 - 0.003ms returns 0
TA260 002:787.356 JLINK_WriteReg(R11, 0x00000000)
TA260 002:787.359 - 0.003ms returns 0
TA260 002:787.363 JLINK_WriteReg(R12, 0x00000000)
TA260 002:787.367 - 0.003ms returns 0
TA260 002:787.371 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:787.375 - 0.004ms returns 0
TA260 002:787.379 JLINK_WriteReg(R14, 0x20000001)
TA260 002:787.382 - 0.003ms returns 0
TA260 002:787.386 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:787.390 - 0.003ms returns 0
TA260 002:787.394 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:787.397 - 0.003ms returns 0
TA260 002:787.401 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:787.405 - 0.003ms returns 0
TA260 002:787.409 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:787.412 - 0.003ms returns 0
TA260 002:787.416 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:787.420 - 0.003ms returns 0
TA260 002:787.424 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:787.428 - 0.004ms returns 0x00000027
TA260 002:787.433 JLINK_Go()
TA260 002:787.441   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:790.157 - 2.724ms 
TA260 002:790.171 JLINK_IsHalted()
TA260 002:790.646 - 0.474ms returns FALSE
TA260 002:790.652 JLINK_HasError()
TA260 002:796.276 JLINK_IsHalted()
TA260 002:798.553   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:799.026 - 2.750ms returns TRUE
TA260 002:799.034 JLINK_ReadReg(R15 (PC))
TA260 002:799.039 - 0.005ms returns 0x20000000
TA260 002:799.043 JLINK_ClrBPEx(BPHandle = 0x00000027)
TA260 002:799.047 - 0.003ms returns 0x00
TA260 002:799.051 JLINK_ReadReg(R0)
TA260 002:799.055 - 0.003ms returns 0x00000000
TA260 002:799.393 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:799.406   Data:  C5 A9 6C 3F 44 35 C3 3E 5E 83 6C 3F 15 EF C3 3E ...
TA260 002:799.416   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:802.025 - 2.631ms returns 0x27C
TA260 002:802.034 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:802.038   Data:  F7 FE FA 3E 81 EF 5E 3F 22 AE FB 3E 05 BE 5E 3F ...
TA260 002:802.046   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:803.922 - 1.888ms returns 0x184
TA260 002:803.939 JLINK_HasError()
TA260 002:803.969 JLINK_WriteReg(R0, 0x08007000)
TA260 002:803.980 - 0.011ms returns 0
TA260 002:803.986 JLINK_WriteReg(R1, 0x00000400)
TA260 002:803.989 - 0.003ms returns 0
TA260 002:803.993 JLINK_WriteReg(R2, 0x20000184)
TA260 002:803.997 - 0.003ms returns 0
TA260 002:804.001 JLINK_WriteReg(R3, 0x00000000)
TA260 002:804.004 - 0.003ms returns 0
TA260 002:804.008 JLINK_WriteReg(R4, 0x00000000)
TA260 002:804.012 - 0.003ms returns 0
TA260 002:804.016 JLINK_WriteReg(R5, 0x00000000)
TA260 002:804.019 - 0.003ms returns 0
TA260 002:804.023 JLINK_WriteReg(R6, 0x00000000)
TA260 002:804.026 - 0.003ms returns 0
TA260 002:804.031 JLINK_WriteReg(R7, 0x00000000)
TA260 002:804.034 - 0.003ms returns 0
TA260 002:804.038 JLINK_WriteReg(R8, 0x00000000)
TA260 002:804.042 - 0.003ms returns 0
TA260 002:804.046 JLINK_WriteReg(R9, 0x20000180)
TA260 002:804.049 - 0.003ms returns 0
TA260 002:804.053 JLINK_WriteReg(R10, 0x00000000)
TA260 002:804.057 - 0.003ms returns 0
TA260 002:804.061 JLINK_WriteReg(R11, 0x00000000)
TA260 002:804.065 - 0.004ms returns 0
TA260 002:804.069 JLINK_WriteReg(R12, 0x00000000)
TA260 002:804.073 - 0.003ms returns 0
TA260 002:804.077 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:804.080 - 0.003ms returns 0
TA260 002:804.084 JLINK_WriteReg(R14, 0x20000001)
TA260 002:804.088 - 0.003ms returns 0
TA260 002:804.092 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:804.095 - 0.003ms returns 0
TA260 002:804.100 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:804.103 - 0.003ms returns 0
TA260 002:804.107 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:804.110 - 0.003ms returns 0
TA260 002:804.114 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:804.118 - 0.003ms returns 0
TA260 002:804.122 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:804.125 - 0.003ms returns 0
TA260 002:804.130 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:804.135 - 0.004ms returns 0x00000028
TA260 002:804.139 JLINK_Go()
TA260 002:804.147   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:806.946 - 2.807ms 
TA260 002:806.957 JLINK_IsHalted()
TA260 002:807.488 - 0.530ms returns FALSE
TA260 002:807.494 JLINK_HasError()
TA260 002:816.311 JLINK_IsHalted()
TA260 002:818.688   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:819.217 - 2.905ms returns TRUE
TA260 002:819.224 JLINK_ReadReg(R15 (PC))
TA260 002:819.229 - 0.005ms returns 0x20000000
TA260 002:819.234 JLINK_ClrBPEx(BPHandle = 0x00000028)
TA260 002:819.238 - 0.003ms returns 0x00
TA260 002:819.242 JLINK_ReadReg(R0)
TA260 002:819.246 - 0.003ms returns 0x00000000
TA260 002:819.611 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:819.619   Data:  FB 12 55 3F 38 E6 0D 3F 31 DB 54 3F DA 39 0E 3F ...
TA260 002:819.630   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:822.195 - 2.584ms returns 0x27C
TA260 002:822.204 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:822.208   Data:  E3 9D 26 3F 0B 1A 42 3F 2A EA 26 3F 70 D8 41 3F ...
TA260 002:822.215   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:824.021 - 1.817ms returns 0x184
TA260 002:824.032 JLINK_HasError()
TA260 002:824.038 JLINK_WriteReg(R0, 0x08007400)
TA260 002:824.043 - 0.005ms returns 0
TA260 002:824.047 JLINK_WriteReg(R1, 0x00000400)
TA260 002:824.050 - 0.003ms returns 0
TA260 002:824.054 JLINK_WriteReg(R2, 0x20000184)
TA260 002:824.058 - 0.003ms returns 0
TA260 002:824.062 JLINK_WriteReg(R3, 0x00000000)
TA260 002:824.065 - 0.003ms returns 0
TA260 002:824.069 JLINK_WriteReg(R4, 0x00000000)
TA260 002:824.072 - 0.003ms returns 0
TA260 002:824.077 JLINK_WriteReg(R5, 0x00000000)
TA260 002:824.080 - 0.003ms returns 0
TA260 002:824.088 JLINK_WriteReg(R6, 0x00000000)
TA260 002:824.092 - 0.003ms returns 0
TA260 002:824.096 JLINK_WriteReg(R7, 0x00000000)
TA260 002:824.099 - 0.003ms returns 0
TA260 002:824.103 JLINK_WriteReg(R8, 0x00000000)
TA260 002:824.106 - 0.003ms returns 0
TA260 002:824.110 JLINK_WriteReg(R9, 0x20000180)
TA260 002:824.114 - 0.003ms returns 0
TA260 002:824.118 JLINK_WriteReg(R10, 0x00000000)
TA260 002:824.121 - 0.003ms returns 0
TA260 002:824.125 JLINK_WriteReg(R11, 0x00000000)
TA260 002:824.129 - 0.003ms returns 0
TA260 002:824.133 JLINK_WriteReg(R12, 0x00000000)
TA260 002:824.136 - 0.003ms returns 0
TA260 002:824.140 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:824.144 - 0.003ms returns 0
TA260 002:824.148 JLINK_WriteReg(R14, 0x20000001)
TA260 002:824.152 - 0.003ms returns 0
TA260 002:824.156 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:824.159 - 0.003ms returns 0
TA260 002:824.163 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:824.167 - 0.003ms returns 0
TA260 002:824.171 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:824.174 - 0.003ms returns 0
TA260 002:824.178 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:824.182 - 0.003ms returns 0
TA260 002:824.186 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:824.189 - 0.003ms returns 0
TA260 002:824.194 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:824.197 - 0.004ms returns 0x00000029
TA260 002:824.202 JLINK_Go()
TA260 002:824.209   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:826.899 - 2.697ms 
TA260 002:826.912 JLINK_IsHalted()
TA260 002:827.494 - 0.582ms returns FALSE
TA260 002:827.505 JLINK_HasError()
TA260 002:833.296 JLINK_IsHalted()
TA260 002:835.578   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:836.078 - 2.782ms returns TRUE
TA260 002:836.085 JLINK_ReadReg(R15 (PC))
TA260 002:836.090 - 0.004ms returns 0x20000000
TA260 002:836.095 JLINK_ClrBPEx(BPHandle = 0x00000029)
TA260 002:836.098 - 0.003ms returns 0x00
TA260 002:836.103 JLINK_ReadReg(R0)
TA260 002:836.106 - 0.003ms returns 0x00000000
TA260 002:836.416 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:836.424   Data:  FB 4B 35 3F CF BD 34 3F F3 04 35 3F F3 04 35 3F ...
TA260 002:836.455   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:839.074 - 2.658ms returns 0x27C
TA260 002:839.089 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:839.093   Data:  21 55 49 3F 06 CF 1D 3F 29 93 49 3F D1 7F 1D 3F ...
TA260 002:839.103   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:841.021 - 1.931ms returns 0x184
TA260 002:841.032 JLINK_HasError()
TA260 002:841.037 JLINK_WriteReg(R0, 0x08007800)
TA260 002:841.043 - 0.005ms returns 0
TA260 002:841.047 JLINK_WriteReg(R1, 0x00000400)
TA260 002:841.050 - 0.003ms returns 0
TA260 002:841.054 JLINK_WriteReg(R2, 0x20000184)
TA260 002:841.058 - 0.003ms returns 0
TA260 002:841.062 JLINK_WriteReg(R3, 0x00000000)
TA260 002:841.066 - 0.003ms returns 0
TA260 002:841.070 JLINK_WriteReg(R4, 0x00000000)
TA260 002:841.073 - 0.003ms returns 0
TA260 002:841.077 JLINK_WriteReg(R5, 0x00000000)
TA260 002:841.080 - 0.003ms returns 0
TA260 002:841.084 JLINK_WriteReg(R6, 0x00000000)
TA260 002:841.088 - 0.003ms returns 0
TA260 002:841.092 JLINK_WriteReg(R7, 0x00000000)
TA260 002:841.095 - 0.003ms returns 0
TA260 002:841.099 JLINK_WriteReg(R8, 0x00000000)
TA260 002:841.103 - 0.003ms returns 0
TA260 002:841.107 JLINK_WriteReg(R9, 0x20000180)
TA260 002:841.110 - 0.003ms returns 0
TA260 002:841.114 JLINK_WriteReg(R10, 0x00000000)
TA260 002:841.118 - 0.003ms returns 0
TA260 002:841.122 JLINK_WriteReg(R11, 0x00000000)
TA260 002:841.125 - 0.003ms returns 0
TA260 002:841.129 JLINK_WriteReg(R12, 0x00000000)
TA260 002:841.132 - 0.003ms returns 0
TA260 002:841.137 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:841.140 - 0.003ms returns 0
TA260 002:841.144 JLINK_WriteReg(R14, 0x20000001)
TA260 002:841.148 - 0.003ms returns 0
TA260 002:841.152 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:841.156 - 0.003ms returns 0
TA260 002:841.160 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:841.163 - 0.003ms returns 0
TA260 002:841.170 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:841.175 - 0.004ms returns 0
TA260 002:841.179 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:841.182 - 0.003ms returns 0
TA260 002:841.187 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:841.190 - 0.003ms returns 0
TA260 002:841.194 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:841.199 - 0.004ms returns 0x0000002A
TA260 002:841.203 JLINK_Go()
TA260 002:841.211   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:843.950 - 2.747ms 
TA260 002:843.957 JLINK_IsHalted()
TA260 002:844.488 - 0.530ms returns FALSE
TA260 002:844.495 JLINK_HasError()
TA260 002:849.811 JLINK_IsHalted()
TA260 002:852.251   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:852.698 - 2.887ms returns TRUE
TA260 002:852.708 JLINK_ReadReg(R15 (PC))
TA260 002:852.713 - 0.005ms returns 0x20000000
TA260 002:852.718 JLINK_ClrBPEx(BPHandle = 0x0000002A)
TA260 002:852.722 - 0.003ms returns 0x00
TA260 002:852.726 JLINK_ReadReg(R0)
TA260 002:852.730 - 0.003ms returns 0x00000000
TA260 002:853.121 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:853.133   Data:  65 8D 0E 3F 47 A3 54 3F DA 39 0E 3F 31 DB 54 3F ...
TA260 002:853.143   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:855.758 - 2.636ms returns 0x27C
TA260 002:855.782 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:855.786   Data:  AC 4F 64 3F FB E6 E6 3E 14 7D 64 3F 75 33 E6 3E ...
TA260 002:855.800   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:857.712 - 1.929ms returns 0x184
TA260 002:857.733 JLINK_HasError()
TA260 002:857.768 JLINK_WriteReg(R0, 0x08007C00)
TA260 002:857.777 - 0.009ms returns 0
TA260 002:857.782 JLINK_WriteReg(R1, 0x00000400)
TA260 002:857.786 - 0.004ms returns 0
TA260 002:857.790 JLINK_WriteReg(R2, 0x20000184)
TA260 002:857.793 - 0.003ms returns 0
TA260 002:857.798 JLINK_WriteReg(R3, 0x00000000)
TA260 002:857.801 - 0.003ms returns 0
TA260 002:857.806 JLINK_WriteReg(R4, 0x00000000)
TA260 002:857.810 - 0.004ms returns 0
TA260 002:857.814 JLINK_WriteReg(R5, 0x00000000)
TA260 002:857.818 - 0.003ms returns 0
TA260 002:857.822 JLINK_WriteReg(R6, 0x00000000)
TA260 002:857.825 - 0.003ms returns 0
TA260 002:857.829 JLINK_WriteReg(R7, 0x00000000)
TA260 002:857.833 - 0.003ms returns 0
TA260 002:857.837 JLINK_WriteReg(R8, 0x00000000)
TA260 002:857.840 - 0.003ms returns 0
TA260 002:857.845 JLINK_WriteReg(R9, 0x20000180)
TA260 002:857.848 - 0.003ms returns 0
TA260 002:857.852 JLINK_WriteReg(R10, 0x00000000)
TA260 002:857.855 - 0.003ms returns 0
TA260 002:857.860 JLINK_WriteReg(R11, 0x00000000)
TA260 002:857.863 - 0.003ms returns 0
TA260 002:857.867 JLINK_WriteReg(R12, 0x00000000)
TA260 002:857.871 - 0.003ms returns 0
TA260 002:857.875 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:857.879 - 0.004ms returns 0
TA260 002:857.883 JLINK_WriteReg(R14, 0x20000001)
TA260 002:857.886 - 0.003ms returns 0
TA260 002:857.891 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:857.894 - 0.003ms returns 0
TA260 002:857.898 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:857.902 - 0.003ms returns 0
TA260 002:857.906 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:857.909 - 0.003ms returns 0
TA260 002:857.913 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:857.917 - 0.003ms returns 0
TA260 002:857.921 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:857.924 - 0.003ms returns 0
TA260 002:857.929 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:857.934 - 0.005ms returns 0x0000002B
TA260 002:857.938 JLINK_Go()
TA260 002:857.948   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:860.704 - 2.765ms 
TA260 002:860.714 JLINK_IsHalted()
TA260 002:861.240 - 0.525ms returns FALSE
TA260 002:861.249 JLINK_HasError()
TA260 002:866.212 JLINK_IsHalted()
TA260 002:868.554   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:869.094 - 2.880ms returns TRUE
TA260 002:869.102 JLINK_ReadReg(R15 (PC))
TA260 002:869.108 - 0.005ms returns 0x20000000
TA260 002:869.112 JLINK_ClrBPEx(BPHandle = 0x0000002B)
TA260 002:869.116 - 0.003ms returns 0x00
TA260 002:869.120 JLINK_ReadReg(R0)
TA260 002:869.124 - 0.003ms returns 0x00000000
TA260 002:869.514 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:869.527   Data:  C8 A8 C4 3E D4 5C 6C 3F 15 EF C3 3E 5E 83 6C 3F ...
TA260 002:869.539   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:872.057 - 2.543ms returns 0x27C
TA260 002:872.064 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:872.068   Data:  1B 84 76 3F 50 50 89 3E 24 9F 76 3F 93 8E 88 3E ...
TA260 002:872.085   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:874.023 - 1.958ms returns 0x184
TA260 002:874.031 JLINK_HasError()
TA260 002:874.036 JLINK_WriteReg(R0, 0x08008000)
TA260 002:874.040 - 0.004ms returns 0
TA260 002:874.045 JLINK_WriteReg(R1, 0x00000400)
TA260 002:874.048 - 0.003ms returns 0
TA260 002:874.052 JLINK_WriteReg(R2, 0x20000184)
TA260 002:874.056 - 0.003ms returns 0
TA260 002:874.060 JLINK_WriteReg(R3, 0x00000000)
TA260 002:874.064 - 0.003ms returns 0
TA260 002:874.068 JLINK_WriteReg(R4, 0x00000000)
TA260 002:874.071 - 0.003ms returns 0
TA260 002:874.075 JLINK_WriteReg(R5, 0x00000000)
TA260 002:874.078 - 0.003ms returns 0
TA260 002:874.082 JLINK_WriteReg(R6, 0x00000000)
TA260 002:874.086 - 0.003ms returns 0
TA260 002:874.090 JLINK_WriteReg(R7, 0x00000000)
TA260 002:874.093 - 0.003ms returns 0
TA260 002:874.097 JLINK_WriteReg(R8, 0x00000000)
TA260 002:874.100 - 0.003ms returns 0
TA260 002:874.104 JLINK_WriteReg(R9, 0x20000180)
TA260 002:874.108 - 0.003ms returns 0
TA260 002:874.112 JLINK_WriteReg(R10, 0x00000000)
TA260 002:874.115 - 0.003ms returns 0
TA260 002:874.119 JLINK_WriteReg(R11, 0x00000000)
TA260 002:874.122 - 0.003ms returns 0
TA260 002:874.127 JLINK_WriteReg(R12, 0x00000000)
TA260 002:874.130 - 0.003ms returns 0
TA260 002:874.134 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:874.138 - 0.003ms returns 0
TA260 002:874.142 JLINK_WriteReg(R14, 0x20000001)
TA260 002:874.145 - 0.003ms returns 0
TA260 002:874.150 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:874.153 - 0.003ms returns 0
TA260 002:874.157 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:874.161 - 0.003ms returns 0
TA260 002:874.165 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:874.168 - 0.003ms returns 0
TA260 002:874.172 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:874.176 - 0.003ms returns 0
TA260 002:874.180 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:874.183 - 0.003ms returns 0
TA260 002:874.187 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:874.191 - 0.004ms returns 0x0000002C
TA260 002:874.196 JLINK_Go()
TA260 002:874.203   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:876.878 - 2.681ms 
TA260 002:876.891 JLINK_IsHalted()
TA260 002:877.400 - 0.508ms returns FALSE
TA260 002:877.413 JLINK_HasError()
TA260 002:884.219 JLINK_IsHalted()
TA260 002:886.599   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:887.119 - 2.900ms returns TRUE
TA260 002:887.132 JLINK_ReadReg(R15 (PC))
TA260 002:887.138 - 0.006ms returns 0x20000000
TA260 002:887.143 JLINK_ClrBPEx(BPHandle = 0x0000002C)
TA260 002:887.147 - 0.004ms returns 0x00
TA260 002:887.151 JLINK_ReadReg(R0)
TA260 002:887.155 - 0.003ms returns 0x00000000
TA260 002:887.465 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:887.472   Data:  18 50 49 3E 0E 01 7B 3F C2 C5 47 3E BE 14 7B 3F ...
TA260 002:887.483   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:890.023 - 2.558ms returns 0x27C
TA260 002:890.036 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:890.040   Data:  54 3F 7F 3F 0A CB 99 3D F4 46 7F 3F 05 A9 96 3D ...
TA260 002:890.050   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:891.977 - 1.941ms returns 0x184
TA260 002:891.985 JLINK_HasError()
TA260 002:891.990 JLINK_WriteReg(R0, 0x08008400)
TA260 002:891.994 - 0.004ms returns 0
TA260 002:891.998 JLINK_WriteReg(R1, 0x00000400)
TA260 002:892.002 - 0.003ms returns 0
TA260 002:892.006 JLINK_WriteReg(R2, 0x20000184)
TA260 002:892.010 - 0.003ms returns 0
TA260 002:892.014 JLINK_WriteReg(R3, 0x00000000)
TA260 002:892.017 - 0.003ms returns 0
TA260 002:892.021 JLINK_WriteReg(R4, 0x00000000)
TA260 002:892.025 - 0.003ms returns 0
TA260 002:892.029 JLINK_WriteReg(R5, 0x00000000)
TA260 002:892.036 - 0.007ms returns 0
TA260 002:892.041 JLINK_WriteReg(R6, 0x00000000)
TA260 002:892.044 - 0.003ms returns 0
TA260 002:892.048 JLINK_WriteReg(R7, 0x00000000)
TA260 002:892.052 - 0.003ms returns 0
TA260 002:892.056 JLINK_WriteReg(R8, 0x00000000)
TA260 002:892.059 - 0.003ms returns 0
TA260 002:892.063 JLINK_WriteReg(R9, 0x20000180)
TA260 002:892.066 - 0.003ms returns 0
TA260 002:892.070 JLINK_WriteReg(R10, 0x00000000)
TA260 002:892.074 - 0.003ms returns 0
TA260 002:892.078 JLINK_WriteReg(R11, 0x00000000)
TA260 002:892.081 - 0.003ms returns 0
TA260 002:892.085 JLINK_WriteReg(R12, 0x00000000)
TA260 002:892.088 - 0.003ms returns 0
TA260 002:892.093 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:892.096 - 0.003ms returns 0
TA260 002:892.100 JLINK_WriteReg(R14, 0x20000001)
TA260 002:892.104 - 0.003ms returns 0
TA260 002:892.108 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:892.112 - 0.003ms returns 0
TA260 002:892.116 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:892.120 - 0.004ms returns 0
TA260 002:892.124 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:892.127 - 0.003ms returns 0
TA260 002:892.131 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:892.135 - 0.003ms returns 0
TA260 002:892.139 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:892.142 - 0.003ms returns 0
TA260 002:892.146 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:892.150 - 0.004ms returns 0x0000002D
TA260 002:892.154 JLINK_Go()
TA260 002:892.162   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:894.888 - 2.733ms 
TA260 002:894.897 JLINK_IsHalted()
TA260 002:895.364 - 0.467ms returns FALSE
TA260 002:895.370 JLINK_HasError()
TA260 002:903.239 JLINK_IsHalted()
TA260 002:905.579   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:906.045 - 2.805ms returns TRUE
TA260 002:906.051 JLINK_ReadReg(R15 (PC))
TA260 002:906.056 - 0.005ms returns 0x20000000
TA260 002:906.061 JLINK_ClrBPEx(BPHandle = 0x0000002D)
TA260 002:906.065 - 0.003ms returns 0x00
TA260 002:906.069 JLINK_ReadReg(R0)
TA260 002:906.073 - 0.003ms returns 0x00000000
TA260 002:906.377 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:906.385   Data:  D4 0F C9 3A EC FF 7F 3F 00 00 00 00 00 00 80 3F ...
TA260 002:906.396   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:909.029 - 2.651ms returns 0x27C
TA260 002:909.049 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:909.053   Data:  72 2B 7E 3F 2C 94 F7 BD 5E 1F 7E 3F 73 B2 FA BD ...
TA260 002:909.064   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:910.929 - 1.880ms returns 0x184
TA260 002:910.938 JLINK_HasError()
TA260 002:910.968 JLINK_WriteReg(R0, 0x08008800)
TA260 002:910.974 - 0.006ms returns 0
TA260 002:910.978 JLINK_WriteReg(R1, 0x00000400)
TA260 002:910.982 - 0.003ms returns 0
TA260 002:910.986 JLINK_WriteReg(R2, 0x20000184)
TA260 002:910.990 - 0.003ms returns 0
TA260 002:910.994 JLINK_WriteReg(R3, 0x00000000)
TA260 002:910.998 - 0.003ms returns 0
TA260 002:911.002 JLINK_WriteReg(R4, 0x00000000)
TA260 002:911.005 - 0.003ms returns 0
TA260 002:911.009 JLINK_WriteReg(R5, 0x00000000)
TA260 002:911.012 - 0.003ms returns 0
TA260 002:911.016 JLINK_WriteReg(R6, 0x00000000)
TA260 002:911.020 - 0.003ms returns 0
TA260 002:911.024 JLINK_WriteReg(R7, 0x00000000)
TA260 002:911.027 - 0.003ms returns 0
TA260 002:911.031 JLINK_WriteReg(R8, 0x00000000)
TA260 002:911.035 - 0.003ms returns 0
TA260 002:911.039 JLINK_WriteReg(R9, 0x20000180)
TA260 002:911.042 - 0.003ms returns 0
TA260 002:911.046 JLINK_WriteReg(R10, 0x00000000)
TA260 002:911.049 - 0.003ms returns 0
TA260 002:911.053 JLINK_WriteReg(R11, 0x00000000)
TA260 002:911.057 - 0.003ms returns 0
TA260 002:911.061 JLINK_WriteReg(R12, 0x00000000)
TA260 002:911.064 - 0.003ms returns 0
TA260 002:911.068 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:911.072 - 0.003ms returns 0
TA260 002:911.076 JLINK_WriteReg(R14, 0x20000001)
TA260 002:911.079 - 0.003ms returns 0
TA260 002:911.083 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:911.087 - 0.003ms returns 0
TA260 002:911.091 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:911.097 - 0.006ms returns 0
TA260 002:911.102 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:911.106 - 0.003ms returns 0
TA260 002:911.110 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:911.113 - 0.003ms returns 0
TA260 002:911.117 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:911.121 - 0.003ms returns 0
TA260 002:911.125 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:911.130 - 0.004ms returns 0x0000002E
TA260 002:911.134 JLINK_Go()
TA260 002:911.141   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:913.849 - 2.714ms 
TA260 002:913.861 JLINK_IsHalted()
TA260 002:914.365 - 0.504ms returns FALSE
TA260 002:914.372 JLINK_HasError()
TA260 002:918.740 JLINK_IsHalted()
TA260 002:921.187   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:921.694 - 2.953ms returns TRUE
TA260 002:921.700 JLINK_ReadReg(R15 (PC))
TA260 002:921.705 - 0.004ms returns 0x20000000
TA260 002:921.709 JLINK_ClrBPEx(BPHandle = 0x0000002E)
TA260 002:921.713 - 0.003ms returns 0x00
TA260 002:921.718 JLINK_ReadReg(R0)
TA260 002:921.721 - 0.003ms returns 0x00000000
TA260 002:922.048 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:922.056   Data:  4D 3B 46 BE 48 28 7B 3F C2 C5 47 BE BE 14 7B 3F ...
TA260 002:922.065   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:924.742 - 2.693ms returns 0x27C
TA260 002:924.753 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:924.757   Data:  0E 53 73 3F EE DB 9F BE BE 33 73 3F E5 9A A0 BE ...
TA260 002:924.766   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:926.664 - 1.910ms returns 0x184
TA260 002:926.677 JLINK_HasError()
TA260 002:926.683 JLINK_WriteReg(R0, 0x08008C00)
TA260 002:926.688 - 0.005ms returns 0
TA260 002:926.692 JLINK_WriteReg(R1, 0x00000400)
TA260 002:926.695 - 0.003ms returns 0
TA260 002:926.699 JLINK_WriteReg(R2, 0x20000184)
TA260 002:926.703 - 0.003ms returns 0
TA260 002:926.707 JLINK_WriteReg(R3, 0x00000000)
TA260 002:926.710 - 0.003ms returns 0
TA260 002:926.714 JLINK_WriteReg(R4, 0x00000000)
TA260 002:926.718 - 0.003ms returns 0
TA260 002:926.722 JLINK_WriteReg(R5, 0x00000000)
TA260 002:926.725 - 0.003ms returns 0
TA260 002:926.729 JLINK_WriteReg(R6, 0x00000000)
TA260 002:926.733 - 0.003ms returns 0
TA260 002:926.737 JLINK_WriteReg(R7, 0x00000000)
TA260 002:926.740 - 0.003ms returns 0
TA260 002:926.744 JLINK_WriteReg(R8, 0x00000000)
TA260 002:926.747 - 0.003ms returns 0
TA260 002:926.751 JLINK_WriteReg(R9, 0x20000180)
TA260 002:926.755 - 0.003ms returns 0
TA260 002:926.759 JLINK_WriteReg(R10, 0x00000000)
TA260 002:926.763 - 0.004ms returns 0
TA260 002:926.767 JLINK_WriteReg(R11, 0x00000000)
TA260 002:926.770 - 0.003ms returns 0
TA260 002:926.774 JLINK_WriteReg(R12, 0x00000000)
TA260 002:926.778 - 0.003ms returns 0
TA260 002:926.782 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:926.786 - 0.004ms returns 0
TA260 002:926.790 JLINK_WriteReg(R14, 0x20000001)
TA260 002:926.793 - 0.003ms returns 0
TA260 002:926.797 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:926.801 - 0.003ms returns 0
TA260 002:926.805 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:926.808 - 0.003ms returns 0
TA260 002:926.812 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:926.816 - 0.003ms returns 0
TA260 002:926.820 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:926.823 - 0.003ms returns 0
TA260 002:926.827 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:926.830 - 0.003ms returns 0
TA260 002:926.835 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:926.839 - 0.004ms returns 0x0000002F
TA260 002:926.843 JLINK_Go()
TA260 002:926.850   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:929.695 - 2.851ms 
TA260 002:929.707 JLINK_IsHalted()
TA260 002:930.167 - 0.459ms returns FALSE
TA260 002:930.173 JLINK_HasError()
TA260 002:933.249 JLINK_IsHalted()
TA260 002:933.738 - 0.489ms returns FALSE
TA260 002:933.744 JLINK_HasError()
TA260 002:935.749 JLINK_IsHalted()
TA260 002:938.095   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:938.614 - 2.864ms returns TRUE
TA260 002:938.625 JLINK_ReadReg(R15 (PC))
TA260 002:938.630 - 0.005ms returns 0x20000000
TA260 002:938.640 JLINK_ClrBPEx(BPHandle = 0x0000002F)
TA260 002:938.645 - 0.005ms returns 0x00
TA260 002:938.672 JLINK_ReadReg(R0)
TA260 002:938.676 - 0.004ms returns 0x00000000
TA260 002:939.015 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:939.023   Data:  44 35 C3 BE C5 A9 6C 3F 15 EF C3 BE 5E 83 6C 3F ...
TA260 002:939.033   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:941.648 - 2.632ms returns 0x27C
TA260 002:941.660 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:941.664   Data:  DB 20 5F 3F 22 AE FB BE 81 EF 5E 3F 27 5D FC BE ...
TA260 002:941.674   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:943.570 - 1.909ms returns 0x184
TA260 002:943.576 JLINK_HasError()
TA260 002:943.581 JLINK_WriteReg(R0, 0x08009000)
TA260 002:943.586 - 0.004ms returns 0
TA260 002:943.590 JLINK_WriteReg(R1, 0x00000400)
TA260 002:943.593 - 0.003ms returns 0
TA260 002:943.597 JLINK_WriteReg(R2, 0x20000184)
TA260 002:943.601 - 0.003ms returns 0
TA260 002:943.605 JLINK_WriteReg(R3, 0x00000000)
TA260 002:943.608 - 0.003ms returns 0
TA260 002:943.612 JLINK_WriteReg(R4, 0x00000000)
TA260 002:943.616 - 0.003ms returns 0
TA260 002:943.619 JLINK_WriteReg(R5, 0x00000000)
TA260 002:943.623 - 0.003ms returns 0
TA260 002:943.627 JLINK_WriteReg(R6, 0x00000000)
TA260 002:943.631 - 0.003ms returns 0
TA260 002:943.635 JLINK_WriteReg(R7, 0x00000000)
TA260 002:943.638 - 0.003ms returns 0
TA260 002:943.642 JLINK_WriteReg(R8, 0x00000000)
TA260 002:943.645 - 0.003ms returns 0
TA260 002:943.649 JLINK_WriteReg(R9, 0x20000180)
TA260 002:943.653 - 0.003ms returns 0
TA260 002:943.657 JLINK_WriteReg(R10, 0x00000000)
TA260 002:943.660 - 0.003ms returns 0
TA260 002:943.664 JLINK_WriteReg(R11, 0x00000000)
TA260 002:943.668 - 0.003ms returns 0
TA260 002:943.672 JLINK_WriteReg(R12, 0x00000000)
TA260 002:943.675 - 0.003ms returns 0
TA260 002:943.679 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:943.683 - 0.003ms returns 0
TA260 002:943.687 JLINK_WriteReg(R14, 0x20000001)
TA260 002:943.690 - 0.003ms returns 0
TA260 002:943.694 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:943.698 - 0.003ms returns 0
TA260 002:943.702 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:943.705 - 0.003ms returns 0
TA260 002:943.709 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:943.712 - 0.003ms returns 0
TA260 002:943.717 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:943.720 - 0.003ms returns 0
TA260 002:943.724 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:943.728 - 0.003ms returns 0
TA260 002:943.732 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:943.736 - 0.004ms returns 0x00000030
TA260 002:943.740 JLINK_Go()
TA260 002:943.747   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:946.572 - 2.831ms 
TA260 002:946.581 JLINK_IsHalted()
TA260 002:947.036 - 0.455ms returns FALSE
TA260 002:947.042 JLINK_HasError()
TA260 002:948.257 JLINK_IsHalted()
TA260 002:948.760 - 0.503ms returns FALSE
TA260 002:948.766 JLINK_HasError()
TA260 002:950.252 JLINK_IsHalted()
TA260 002:950.748 - 0.496ms returns FALSE
TA260 002:950.754 JLINK_HasError()
TA260 002:952.254 JLINK_IsHalted()
TA260 002:954.576   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:955.058 - 2.804ms returns TRUE
TA260 002:955.066 JLINK_ReadReg(R15 (PC))
TA260 002:955.071 - 0.005ms returns 0x20000000
TA260 002:955.076 JLINK_ClrBPEx(BPHandle = 0x00000030)
TA260 002:955.080 - 0.004ms returns 0x00
TA260 002:955.084 JLINK_ReadReg(R0)
TA260 002:955.088 - 0.003ms returns 0x00000000
TA260 002:955.400 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:955.407   Data:  38 E6 0D BF FB 12 55 3F DA 39 0E BF 31 DB 54 3F ...
TA260 002:955.416   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:958.020 - 2.619ms returns 0x27C
TA260 002:958.037 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:958.042   Data:  89 5B 42 3F 2A EA 26 BF 0B 1A 42 3F 56 36 27 BF ...
TA260 002:958.051   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:959.971 - 1.933ms returns 0x184
TA260 002:959.985 JLINK_HasError()
TA260 002:959.990 JLINK_WriteReg(R0, 0x08009400)
TA260 002:959.995 - 0.005ms returns 0
TA260 002:960.004 JLINK_WriteReg(R1, 0x00000400)
TA260 002:960.008 - 0.003ms returns 0
TA260 002:960.012 JLINK_WriteReg(R2, 0x20000184)
TA260 002:960.015 - 0.003ms returns 0
TA260 002:960.019 JLINK_WriteReg(R3, 0x00000000)
TA260 002:960.022 - 0.003ms returns 0
TA260 002:960.026 JLINK_WriteReg(R4, 0x00000000)
TA260 002:960.030 - 0.003ms returns 0
TA260 002:960.034 JLINK_WriteReg(R5, 0x00000000)
TA260 002:960.037 - 0.003ms returns 0
TA260 002:960.042 JLINK_WriteReg(R6, 0x00000000)
TA260 002:960.045 - 0.003ms returns 0
TA260 002:960.049 JLINK_WriteReg(R7, 0x00000000)
TA260 002:960.052 - 0.003ms returns 0
TA260 002:960.056 JLINK_WriteReg(R8, 0x00000000)
TA260 002:960.060 - 0.003ms returns 0
TA260 002:960.064 JLINK_WriteReg(R9, 0x20000180)
TA260 002:960.067 - 0.003ms returns 0
TA260 002:960.071 JLINK_WriteReg(R10, 0x00000000)
TA260 002:960.075 - 0.003ms returns 0
TA260 002:960.079 JLINK_WriteReg(R11, 0x00000000)
TA260 002:960.082 - 0.003ms returns 0
TA260 002:960.086 JLINK_WriteReg(R12, 0x00000000)
TA260 002:960.090 - 0.003ms returns 0
TA260 002:960.094 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:960.098 - 0.003ms returns 0
TA260 002:960.102 JLINK_WriteReg(R14, 0x20000001)
TA260 002:960.105 - 0.003ms returns 0
TA260 002:960.109 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:960.113 - 0.003ms returns 0
TA260 002:960.117 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:960.121 - 0.003ms returns 0
TA260 002:960.125 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:960.128 - 0.003ms returns 0
TA260 002:960.132 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:960.136 - 0.003ms returns 0
TA260 002:960.140 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:960.143 - 0.003ms returns 0
TA260 002:960.148 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:960.152 - 0.004ms returns 0x00000031
TA260 002:960.156 JLINK_Go()
TA260 002:960.163   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:962.848 - 2.692ms 
TA260 002:962.855 JLINK_IsHalted()
TA260 002:963.341 - 0.486ms returns FALSE
TA260 002:963.352 JLINK_HasError()
TA260 002:964.764 JLINK_IsHalted()
TA260 002:965.262 - 0.497ms returns FALSE
TA260 002:965.268 JLINK_HasError()
TA260 002:966.767 JLINK_IsHalted()
TA260 002:969.133   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:969.649 - 2.881ms returns TRUE
TA260 002:969.655 JLINK_ReadReg(R15 (PC))
TA260 002:969.660 - 0.004ms returns 0x20000000
TA260 002:969.664 JLINK_ClrBPEx(BPHandle = 0x00000031)
TA260 002:969.668 - 0.003ms returns 0x00
TA260 002:969.672 JLINK_ReadReg(R0)
TA260 002:969.675 - 0.003ms returns 0x00000000
TA260 002:969.987 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:969.994   Data:  CF BD 34 BF FB 4B 35 3F F3 04 35 BF F3 04 35 3F ...
TA260 002:970.003   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:972.573 - 2.585ms returns 0x27C
TA260 002:972.584 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:972.588   Data:  22 1E 1E 3F 29 93 49 BF 06 CF 1D 3F 12 D1 49 BF ...
TA260 002:972.597   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:974.500 - 1.916ms returns 0x184
TA260 002:974.512 JLINK_HasError()
TA260 002:974.517 JLINK_WriteReg(R0, 0x08009800)
TA260 002:974.522 - 0.005ms returns 0
TA260 002:974.527 JLINK_WriteReg(R1, 0x00000400)
TA260 002:974.530 - 0.003ms returns 0
TA260 002:974.534 JLINK_WriteReg(R2, 0x20000184)
TA260 002:974.538 - 0.003ms returns 0
TA260 002:974.542 JLINK_WriteReg(R3, 0x00000000)
TA260 002:974.545 - 0.003ms returns 0
TA260 002:974.549 JLINK_WriteReg(R4, 0x00000000)
TA260 002:974.552 - 0.003ms returns 0
TA260 002:974.556 JLINK_WriteReg(R5, 0x00000000)
TA260 002:974.560 - 0.003ms returns 0
TA260 002:974.564 JLINK_WriteReg(R6, 0x00000000)
TA260 002:974.567 - 0.003ms returns 0
TA260 002:974.571 JLINK_WriteReg(R7, 0x00000000)
TA260 002:974.575 - 0.003ms returns 0
TA260 002:974.579 JLINK_WriteReg(R8, 0x00000000)
TA260 002:974.582 - 0.003ms returns 0
TA260 002:974.586 JLINK_WriteReg(R9, 0x20000180)
TA260 002:974.589 - 0.003ms returns 0
TA260 002:974.593 JLINK_WriteReg(R10, 0x00000000)
TA260 002:974.597 - 0.003ms returns 0
TA260 002:974.605 JLINK_WriteReg(R11, 0x00000000)
TA260 002:974.608 - 0.003ms returns 0
TA260 002:974.612 JLINK_WriteReg(R12, 0x00000000)
TA260 002:974.616 - 0.003ms returns 0
TA260 002:974.620 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:974.624 - 0.003ms returns 0
TA260 002:974.628 JLINK_WriteReg(R14, 0x20000001)
TA260 002:974.632 - 0.003ms returns 0
TA260 002:974.636 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:974.639 - 0.003ms returns 0
TA260 002:974.643 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:974.646 - 0.003ms returns 0
TA260 002:974.650 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:974.654 - 0.003ms returns 0
TA260 002:974.658 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:974.662 - 0.003ms returns 0
TA260 002:974.666 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:974.669 - 0.003ms returns 0
TA260 002:974.674 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:974.678 - 0.004ms returns 0x00000032
TA260 002:974.682 JLINK_Go()
TA260 002:974.690   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:977.569 - 2.887ms 
TA260 002:977.582 JLINK_IsHalted()
TA260 002:978.046 - 0.463ms returns FALSE
TA260 002:978.054 JLINK_HasError()
TA260 002:979.275 JLINK_IsHalted()
TA260 002:979.760 - 0.484ms returns FALSE
TA260 002:979.766 JLINK_HasError()
TA260 002:981.281 JLINK_IsHalted()
TA260 002:981.746 - 0.465ms returns FALSE
TA260 002:981.752 JLINK_HasError()
TA260 002:983.275 JLINK_IsHalted()
TA260 002:985.542   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:986.020 - 2.744ms returns TRUE
TA260 002:986.027 JLINK_ReadReg(R15 (PC))
TA260 002:986.032 - 0.004ms returns 0x20000000
TA260 002:986.036 JLINK_ClrBPEx(BPHandle = 0x00000032)
TA260 002:986.040 - 0.004ms returns 0x00
TA260 002:986.044 JLINK_ReadReg(R0)
TA260 002:986.048 - 0.003ms returns 0x00000000
TA260 002:986.541 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:986.550   Data:  47 A3 54 BF 65 8D 0E 3F 31 DB 54 BF DA 39 0E 3F ...
TA260 002:986.560   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:989.287 - 2.745ms returns 0x27C
TA260 002:989.307 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:989.311   Data:  5D 9A E7 3E 14 7D 64 BF FB E6 E6 3E 59 AA 64 BF ...
TA260 002:989.320   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:991.198 - 1.890ms returns 0x184
TA260 002:991.214 JLINK_HasError()
TA260 002:991.278 JLINK_WriteReg(R0, 0x08009C00)
TA260 002:991.284 - 0.006ms returns 0
TA260 002:991.288 JLINK_WriteReg(R1, 0x00000400)
TA260 002:991.292 - 0.003ms returns 0
TA260 002:991.296 JLINK_WriteReg(R2, 0x20000184)
TA260 002:991.300 - 0.003ms returns 0
TA260 002:991.304 JLINK_WriteReg(R3, 0x00000000)
TA260 002:991.307 - 0.003ms returns 0
TA260 002:991.311 JLINK_WriteReg(R4, 0x00000000)
TA260 002:991.314 - 0.003ms returns 0
TA260 002:991.318 JLINK_WriteReg(R5, 0x00000000)
TA260 002:991.322 - 0.003ms returns 0
TA260 002:991.326 JLINK_WriteReg(R6, 0x00000000)
TA260 002:991.330 - 0.003ms returns 0
TA260 002:991.334 JLINK_WriteReg(R7, 0x00000000)
TA260 002:991.337 - 0.003ms returns 0
TA260 002:991.341 JLINK_WriteReg(R8, 0x00000000)
TA260 002:991.344 - 0.003ms returns 0
TA260 002:991.349 JLINK_WriteReg(R9, 0x20000180)
TA260 002:991.352 - 0.003ms returns 0
TA260 002:991.356 JLINK_WriteReg(R10, 0x00000000)
TA260 002:991.359 - 0.003ms returns 0
TA260 002:991.363 JLINK_WriteReg(R11, 0x00000000)
TA260 002:991.367 - 0.003ms returns 0
TA260 002:991.371 JLINK_WriteReg(R12, 0x00000000)
TA260 002:991.374 - 0.003ms returns 0
TA260 002:991.378 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:991.382 - 0.004ms returns 0
TA260 002:991.386 JLINK_WriteReg(R14, 0x20000001)
TA260 002:991.390 - 0.003ms returns 0
TA260 002:991.394 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:991.397 - 0.003ms returns 0
TA260 002:991.401 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:991.405 - 0.003ms returns 0
TA260 002:991.409 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:991.412 - 0.003ms returns 0
TA260 002:991.416 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:991.420 - 0.003ms returns 0
TA260 002:991.424 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:991.455 - 0.031ms returns 0
TA260 002:991.462 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:991.466 - 0.004ms returns 0x00000033
TA260 002:991.471 JLINK_Go()
TA260 002:991.480   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:994.329 - 2.858ms 
TA260 002:994.339 JLINK_IsHalted()
TA260 002:994.840 - 0.500ms returns FALSE
TA260 002:994.846 JLINK_HasError()
TA260 002:996.284 JLINK_IsHalted()
TA260 002:996.786 - 0.501ms returns FALSE
TA260 002:996.796 JLINK_HasError()
TA260 002:998.284 JLINK_IsHalted()
TA260 003:000.587   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:001.069 - 2.784ms returns TRUE
TA260 003:001.075 JLINK_ReadReg(R15 (PC))
TA260 003:001.079 - 0.004ms returns 0x20000000
TA260 003:001.084 JLINK_ClrBPEx(BPHandle = 0x00000033)
TA260 003:001.087 - 0.003ms returns 0x00
TA260 003:001.092 JLINK_ReadReg(R0)
TA260 003:001.095 - 0.003ms returns 0x00000000
TA260 003:001.478 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:001.485   Data:  D4 5C 6C BF C8 A8 C4 3E 5E 83 6C BF 15 EF C3 3E ...
TA260 003:001.495   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:004.083 - 2.604ms returns 0x27C
TA260 003:004.099 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:004.103   Data:  F8 11 8A 3E 24 9F 76 BF 50 50 89 3E 07 BA 76 BF ...
TA260 003:004.114   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:005.987 - 1.887ms returns 0x184
TA260 003:006.001 JLINK_HasError()
TA260 003:006.007 JLINK_WriteReg(R0, 0x0800A000)
TA260 003:006.013 - 0.005ms returns 0
TA260 003:006.017 JLINK_WriteReg(R1, 0x00000400)
TA260 003:006.021 - 0.003ms returns 0
TA260 003:006.025 JLINK_WriteReg(R2, 0x20000184)
TA260 003:006.028 - 0.003ms returns 0
TA260 003:006.032 JLINK_WriteReg(R3, 0x00000000)
TA260 003:006.036 - 0.003ms returns 0
TA260 003:006.040 JLINK_WriteReg(R4, 0x00000000)
TA260 003:006.043 - 0.003ms returns 0
TA260 003:006.047 JLINK_WriteReg(R5, 0x00000000)
TA260 003:006.051 - 0.003ms returns 0
TA260 003:006.055 JLINK_WriteReg(R6, 0x00000000)
TA260 003:006.059 - 0.004ms returns 0
TA260 003:006.063 JLINK_WriteReg(R7, 0x00000000)
TA260 003:006.066 - 0.003ms returns 0
TA260 003:006.071 JLINK_WriteReg(R8, 0x00000000)
TA260 003:006.074 - 0.003ms returns 0
TA260 003:006.078 JLINK_WriteReg(R9, 0x20000180)
TA260 003:006.082 - 0.003ms returns 0
TA260 003:006.086 JLINK_WriteReg(R10, 0x00000000)
TA260 003:006.089 - 0.003ms returns 0
TA260 003:006.093 JLINK_WriteReg(R11, 0x00000000)
TA260 003:006.096 - 0.003ms returns 0
TA260 003:006.100 JLINK_WriteReg(R12, 0x00000000)
TA260 003:006.104 - 0.003ms returns 0
TA260 003:006.108 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:006.112 - 0.004ms returns 0
TA260 003:006.116 JLINK_WriteReg(R14, 0x20000001)
TA260 003:006.119 - 0.003ms returns 0
TA260 003:006.297 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:006.301 - 0.177ms returns 0
TA260 003:006.306 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:006.309 - 0.003ms returns 0
TA260 003:006.313 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:006.317 - 0.003ms returns 0
TA260 003:006.321 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:006.324 - 0.003ms returns 0
TA260 003:006.328 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:006.332 - 0.003ms returns 0
TA260 003:006.337 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:006.341 - 0.004ms returns 0x00000034
TA260 003:006.345 JLINK_Go()
TA260 003:006.355   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:009.102 - 2.756ms 
TA260 003:009.118 JLINK_IsHalted()
TA260 003:009.616 - 0.498ms returns FALSE
TA260 003:009.622 JLINK_HasError()
TA260 003:010.794 JLINK_IsHalted()
TA260 003:011.344 - 0.549ms returns FALSE
TA260 003:011.353 JLINK_HasError()
TA260 003:013.295 JLINK_IsHalted()
TA260 003:015.530   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:015.996 - 2.701ms returns TRUE
TA260 003:016.003 JLINK_ReadReg(R15 (PC))
TA260 003:016.008 - 0.004ms returns 0x20000000
TA260 003:016.012 JLINK_ClrBPEx(BPHandle = 0x00000034)
TA260 003:016.017 - 0.004ms returns 0x00
TA260 003:016.021 JLINK_ReadReg(R0)
TA260 003:016.025 - 0.003ms returns 0x00000000
TA260 003:016.419 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:016.426   Data:  0E 01 7B BF 18 50 49 3E BE 14 7B BF C2 C5 47 3E ...
TA260 003:016.437   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:019.061 - 2.642ms returns 0x27C
TA260 003:019.072 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:019.076   Data:  F9 EC 9C 3D F4 46 7F BF 0A CB 99 3D 6D 4E 7F BF ...
TA260 003:019.084   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:020.978 - 1.905ms returns 0x184
TA260 003:020.990 JLINK_HasError()
TA260 003:020.995 JLINK_WriteReg(R0, 0x0800A400)
TA260 003:021.000 - 0.005ms returns 0
TA260 003:021.004 JLINK_WriteReg(R1, 0x00000400)
TA260 003:021.008 - 0.003ms returns 0
TA260 003:021.012 JLINK_WriteReg(R2, 0x20000184)
TA260 003:021.015 - 0.003ms returns 0
TA260 003:021.020 JLINK_WriteReg(R3, 0x00000000)
TA260 003:021.023 - 0.003ms returns 0
TA260 003:021.027 JLINK_WriteReg(R4, 0x00000000)
TA260 003:021.030 - 0.003ms returns 0
TA260 003:021.034 JLINK_WriteReg(R5, 0x00000000)
TA260 003:021.038 - 0.003ms returns 0
TA260 003:021.042 JLINK_WriteReg(R6, 0x00000000)
TA260 003:021.045 - 0.003ms returns 0
TA260 003:021.049 JLINK_WriteReg(R7, 0x00000000)
TA260 003:021.053 - 0.003ms returns 0
TA260 003:021.057 JLINK_WriteReg(R8, 0x00000000)
TA260 003:021.060 - 0.003ms returns 0
TA260 003:021.064 JLINK_WriteReg(R9, 0x20000180)
TA260 003:021.067 - 0.003ms returns 0
TA260 003:021.072 JLINK_WriteReg(R10, 0x00000000)
TA260 003:021.075 - 0.003ms returns 0
TA260 003:021.079 JLINK_WriteReg(R11, 0x00000000)
TA260 003:021.082 - 0.003ms returns 0
TA260 003:021.086 JLINK_WriteReg(R12, 0x00000000)
TA260 003:021.090 - 0.003ms returns 0
TA260 003:021.094 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:021.098 - 0.004ms returns 0
TA260 003:021.102 JLINK_WriteReg(R14, 0x20000001)
TA260 003:021.105 - 0.003ms returns 0
TA260 003:021.109 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:021.113 - 0.003ms returns 0
TA260 003:021.117 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:021.120 - 0.003ms returns 0
TA260 003:021.124 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:021.128 - 0.003ms returns 0
TA260 003:021.132 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:021.135 - 0.003ms returns 0
TA260 003:021.139 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:021.142 - 0.003ms returns 0
TA260 003:021.147 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:021.151 - 0.004ms returns 0x00000035
TA260 003:021.155 JLINK_Go()
TA260 003:021.163   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:023.884 - 2.729ms 
TA260 003:023.892 JLINK_IsHalted()
TA260 003:024.383 - 0.490ms returns FALSE
TA260 003:024.389 JLINK_HasError()
TA260 003:026.804 JLINK_IsHalted()
TA260 003:027.286 - 0.482ms returns FALSE
TA260 003:027.292 JLINK_HasError()
TA260 003:028.803 JLINK_IsHalted()
TA260 003:031.142   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:031.614 - 2.811ms returns TRUE
TA260 003:031.621 JLINK_ReadReg(R15 (PC))
TA260 003:031.625 - 0.004ms returns 0x20000000
TA260 003:031.630 JLINK_ClrBPEx(BPHandle = 0x00000035)
TA260 003:031.634 - 0.003ms returns 0x00
TA260 003:031.638 JLINK_ReadReg(R0)
TA260 003:031.642 - 0.003ms returns 0x00000000
TA260 003:031.965 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:031.972   Data:  EC FF 7F BF D4 0F C9 3A 00 00 80 BF 00 00 00 00 ...
TA260 003:031.981   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:034.560 - 2.595ms returns 0x27C
TA260 003:034.574 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:034.578   Data:  C0 75 F4 BD 5E 1F 7E BF 2C 94 F7 BD 24 13 7E BF ...
TA260 003:034.587   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:036.502 - 1.928ms returns 0x184
TA260 003:036.512 JLINK_HasError()
TA260 003:036.518 JLINK_WriteReg(R0, 0x0800A800)
TA260 003:036.522 - 0.004ms returns 0
TA260 003:036.526 JLINK_WriteReg(R1, 0x00000400)
TA260 003:036.530 - 0.003ms returns 0
TA260 003:036.534 JLINK_WriteReg(R2, 0x20000184)
TA260 003:036.538 - 0.003ms returns 0
TA260 003:036.542 JLINK_WriteReg(R3, 0x00000000)
TA260 003:036.545 - 0.003ms returns 0
TA260 003:036.610 JLINK_WriteReg(R4, 0x00000000)
TA260 003:036.615 - 0.005ms returns 0
TA260 003:036.619 JLINK_WriteReg(R5, 0x00000000)
TA260 003:036.623 - 0.004ms returns 0
TA260 003:036.627 JLINK_WriteReg(R6, 0x00000000)
TA260 003:036.630 - 0.003ms returns 0
TA260 003:036.635 JLINK_WriteReg(R7, 0x00000000)
TA260 003:036.638 - 0.003ms returns 0
TA260 003:036.642 JLINK_WriteReg(R8, 0x00000000)
TA260 003:036.657 - 0.014ms returns 0
TA260 003:036.661 JLINK_WriteReg(R9, 0x20000180)
TA260 003:036.664 - 0.003ms returns 0
TA260 003:036.668 JLINK_WriteReg(R10, 0x00000000)
TA260 003:036.672 - 0.003ms returns 0
TA260 003:036.676 JLINK_WriteReg(R11, 0x00000000)
TA260 003:036.679 - 0.003ms returns 0
TA260 003:036.683 JLINK_WriteReg(R12, 0x00000000)
TA260 003:036.687 - 0.003ms returns 0
TA260 003:036.691 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:036.695 - 0.003ms returns 0
TA260 003:036.699 JLINK_WriteReg(R14, 0x20000001)
TA260 003:036.702 - 0.003ms returns 0
TA260 003:036.706 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:036.710 - 0.003ms returns 0
TA260 003:036.714 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:036.717 - 0.003ms returns 0
TA260 003:036.721 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:036.725 - 0.003ms returns 0
TA260 003:036.729 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:036.732 - 0.003ms returns 0
TA260 003:036.736 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:036.740 - 0.003ms returns 0
TA260 003:036.744 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:036.748 - 0.004ms returns 0x00000036
TA260 003:036.753 JLINK_Go()
TA260 003:036.761   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:039.477 - 2.724ms 
TA260 003:039.489 JLINK_IsHalted()
TA260 003:040.010 - 0.520ms returns FALSE
TA260 003:040.020 JLINK_HasError()
TA260 003:041.310 JLINK_IsHalted()
TA260 003:041.819 - 0.508ms returns FALSE
TA260 003:041.829 JLINK_HasError()
TA260 003:043.308 JLINK_IsHalted()
TA260 003:043.781 - 0.473ms returns FALSE
TA260 003:043.787 JLINK_HasError()
TA260 003:045.813 JLINK_IsHalted()
TA260 003:048.137   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:048.613 - 2.800ms returns TRUE
TA260 003:048.620 JLINK_ReadReg(R15 (PC))
TA260 003:048.624 - 0.004ms returns 0x20000000
TA260 003:048.629 JLINK_ClrBPEx(BPHandle = 0x00000036)
TA260 003:048.633 - 0.003ms returns 0x00
TA260 003:048.637 JLINK_ReadReg(R0)
TA260 003:048.641 - 0.003ms returns 0x00000000
TA260 003:049.001 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:049.009   Data:  48 28 7B BF 4D 3B 46 BE BE 14 7B BF C2 C5 47 BE ...
TA260 003:049.019   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:051.558 - 2.556ms returns 0x27C
TA260 003:051.567 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:051.571   Data:  DF 1C 9F BE BE 33 73 BF EE DB 9F BE 47 14 73 BF ...
TA260 003:051.578   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:053.472 - 1.905ms returns 0x184
TA260 003:053.478 JLINK_HasError()
TA260 003:053.483 JLINK_WriteReg(R0, 0x0800AC00)
TA260 003:053.488 - 0.004ms returns 0
TA260 003:053.492 JLINK_WriteReg(R1, 0x00000400)
TA260 003:053.495 - 0.003ms returns 0
TA260 003:053.499 JLINK_WriteReg(R2, 0x20000184)
TA260 003:053.503 - 0.003ms returns 0
TA260 003:053.507 JLINK_WriteReg(R3, 0x00000000)
TA260 003:053.510 - 0.003ms returns 0
TA260 003:053.514 JLINK_WriteReg(R4, 0x00000000)
TA260 003:053.518 - 0.003ms returns 0
TA260 003:053.522 JLINK_WriteReg(R5, 0x00000000)
TA260 003:053.525 - 0.003ms returns 0
TA260 003:053.529 JLINK_WriteReg(R6, 0x00000000)
TA260 003:053.532 - 0.003ms returns 0
TA260 003:053.536 JLINK_WriteReg(R7, 0x00000000)
TA260 003:053.540 - 0.003ms returns 0
TA260 003:053.544 JLINK_WriteReg(R8, 0x00000000)
TA260 003:053.547 - 0.003ms returns 0
TA260 003:053.551 JLINK_WriteReg(R9, 0x20000180)
TA260 003:053.555 - 0.003ms returns 0
TA260 003:053.559 JLINK_WriteReg(R10, 0x00000000)
TA260 003:053.562 - 0.003ms returns 0
TA260 003:053.566 JLINK_WriteReg(R11, 0x00000000)
TA260 003:053.569 - 0.003ms returns 0
TA260 003:053.573 JLINK_WriteReg(R12, 0x00000000)
TA260 003:053.579 - 0.006ms returns 0
TA260 003:053.585 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:053.589 - 0.003ms returns 0
TA260 003:053.593 JLINK_WriteReg(R14, 0x20000001)
TA260 003:053.596 - 0.003ms returns 0
TA260 003:053.601 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:053.604 - 0.003ms returns 0
TA260 003:053.608 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:053.612 - 0.003ms returns 0
TA260 003:053.616 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:053.619 - 0.003ms returns 0
TA260 003:053.624 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:053.627 - 0.003ms returns 0
TA260 003:053.631 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:053.634 - 0.003ms returns 0
TA260 003:053.639 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:053.642 - 0.004ms returns 0x00000037
TA260 003:053.646 JLINK_Go()
TA260 003:053.654   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:056.480 - 2.832ms 
TA260 003:056.493 JLINK_IsHalted()
TA260 003:057.014 - 0.520ms returns FALSE
TA260 003:057.028 JLINK_HasError()
TA260 003:058.822 JLINK_IsHalted()
TA260 003:059.327 - 0.505ms returns FALSE
TA260 003:059.333 JLINK_HasError()
TA260 003:060.819 JLINK_IsHalted()
TA260 003:063.142   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:063.615 - 2.795ms returns TRUE
TA260 003:063.622 JLINK_ReadReg(R15 (PC))
TA260 003:063.626 - 0.004ms returns 0x20000000
TA260 003:063.630 JLINK_ClrBPEx(BPHandle = 0x00000037)
TA260 003:063.634 - 0.003ms returns 0x00
TA260 003:063.639 JLINK_ReadReg(R0)
TA260 003:063.642 - 0.003ms returns 0x00000000
TA260 003:063.941 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:063.948   Data:  C5 A9 6C BF 44 35 C3 BE 5E 83 6C BF 15 EF C3 BE ...
TA260 003:063.956   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:066.565 - 2.623ms returns 0x27C
TA260 003:066.578 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:066.582   Data:  F7 FE FA BE 81 EF 5E BF 22 AE FB BE 05 BE 5E BF ...
TA260 003:066.592   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:068.499 - 1.920ms returns 0x184
TA260 003:068.517 JLINK_HasError()
TA260 003:068.552 JLINK_WriteReg(R0, 0x0800B000)
TA260 003:068.565 - 0.013ms returns 0
TA260 003:068.570 JLINK_WriteReg(R1, 0x00000400)
TA260 003:068.574 - 0.003ms returns 0
TA260 003:068.578 JLINK_WriteReg(R2, 0x20000184)
TA260 003:068.581 - 0.003ms returns 0
TA260 003:068.585 JLINK_WriteReg(R3, 0x00000000)
TA260 003:068.589 - 0.003ms returns 0
TA260 003:068.593 JLINK_WriteReg(R4, 0x00000000)
TA260 003:068.596 - 0.003ms returns 0
TA260 003:068.600 JLINK_WriteReg(R5, 0x00000000)
TA260 003:068.604 - 0.003ms returns 0
TA260 003:068.608 JLINK_WriteReg(R6, 0x00000000)
TA260 003:068.612 - 0.003ms returns 0
TA260 003:068.616 JLINK_WriteReg(R7, 0x00000000)
TA260 003:068.619 - 0.003ms returns 0
TA260 003:068.623 JLINK_WriteReg(R8, 0x00000000)
TA260 003:068.626 - 0.003ms returns 0
TA260 003:068.630 JLINK_WriteReg(R9, 0x20000180)
TA260 003:068.634 - 0.003ms returns 0
TA260 003:068.638 JLINK_WriteReg(R10, 0x00000000)
TA260 003:068.641 - 0.003ms returns 0
TA260 003:068.645 JLINK_WriteReg(R11, 0x00000000)
TA260 003:068.649 - 0.003ms returns 0
TA260 003:068.653 JLINK_WriteReg(R12, 0x00000000)
TA260 003:068.656 - 0.003ms returns 0
TA260 003:068.660 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:068.664 - 0.003ms returns 0
TA260 003:068.668 JLINK_WriteReg(R14, 0x20000001)
TA260 003:068.671 - 0.003ms returns 0
TA260 003:068.675 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:068.679 - 0.003ms returns 0
TA260 003:068.683 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:068.686 - 0.003ms returns 0
TA260 003:068.690 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:068.694 - 0.003ms returns 0
TA260 003:068.698 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:068.701 - 0.003ms returns 0
TA260 003:068.705 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:068.709 - 0.003ms returns 0
TA260 003:068.713 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:068.718 - 0.004ms returns 0x00000038
TA260 003:068.722 JLINK_Go()
TA260 003:068.730   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:071.472 - 2.750ms 
TA260 003:071.483 JLINK_IsHalted()
TA260 003:071.952 - 0.468ms returns FALSE
TA260 003:071.957 JLINK_HasError()
TA260 003:073.327 JLINK_IsHalted()
TA260 003:073.763 - 0.436ms returns FALSE
TA260 003:073.775 JLINK_HasError()
TA260 003:074.830 JLINK_IsHalted()
TA260 003:075.318 - 0.488ms returns FALSE
TA260 003:075.324 JLINK_HasError()
TA260 003:076.832 JLINK_IsHalted()
TA260 003:079.198   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:079.700 - 2.867ms returns TRUE
TA260 003:079.707 JLINK_ReadReg(R15 (PC))
TA260 003:079.712 - 0.004ms returns 0x20000000
TA260 003:079.716 JLINK_ClrBPEx(BPHandle = 0x00000038)
TA260 003:079.720 - 0.003ms returns 0x00
TA260 003:079.725 JLINK_ReadReg(R0)
TA260 003:079.729 - 0.004ms returns 0x00000000
TA260 003:080.058 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:080.066   Data:  FB 12 55 BF 38 E6 0D BF 31 DB 54 BF DA 39 0E BF ...
TA260 003:080.075   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:082.744 - 2.685ms returns 0x27C
TA260 003:082.758 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:082.762   Data:  E3 9D 26 BF 0B 1A 42 BF 2A EA 26 BF 70 D8 41 BF ...
TA260 003:082.771   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:084.660 - 1.902ms returns 0x184
TA260 003:084.670 JLINK_HasError()
TA260 003:084.676 JLINK_WriteReg(R0, 0x0800B400)
TA260 003:084.682 - 0.005ms returns 0
TA260 003:084.688 JLINK_WriteReg(R1, 0x00000400)
TA260 003:084.692 - 0.004ms returns 0
TA260 003:084.697 JLINK_WriteReg(R2, 0x20000184)
TA260 003:084.701 - 0.004ms returns 0
TA260 003:084.706 JLINK_WriteReg(R3, 0x00000000)
TA260 003:084.711 - 0.004ms returns 0
TA260 003:084.717 JLINK_WriteReg(R4, 0x00000000)
TA260 003:084.721 - 0.004ms returns 0
TA260 003:084.726 JLINK_WriteReg(R5, 0x00000000)
TA260 003:084.730 - 0.004ms returns 0
TA260 003:084.735 JLINK_WriteReg(R6, 0x00000000)
TA260 003:084.740 - 0.004ms returns 0
TA260 003:084.745 JLINK_WriteReg(R7, 0x00000000)
TA260 003:084.749 - 0.004ms returns 0
TA260 003:084.754 JLINK_WriteReg(R8, 0x00000000)
TA260 003:084.758 - 0.004ms returns 0
TA260 003:084.764 JLINK_WriteReg(R9, 0x20000180)
TA260 003:084.768 - 0.004ms returns 0
TA260 003:084.773 JLINK_WriteReg(R10, 0x00000000)
TA260 003:084.777 - 0.004ms returns 0
TA260 003:084.782 JLINK_WriteReg(R11, 0x00000000)
TA260 003:084.786 - 0.004ms returns 0
TA260 003:084.792 JLINK_WriteReg(R12, 0x00000000)
TA260 003:084.795 - 0.003ms returns 0
TA260 003:084.800 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:084.803 - 0.004ms returns 0
TA260 003:084.807 JLINK_WriteReg(R14, 0x20000001)
TA260 003:084.810 - 0.003ms returns 0
TA260 003:084.815 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:084.818 - 0.003ms returns 0
TA260 003:084.822 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:084.826 - 0.003ms returns 0
TA260 003:084.830 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:084.833 - 0.003ms returns 0
TA260 003:084.837 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:084.840 - 0.003ms returns 0
TA260 003:084.844 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:084.848 - 0.003ms returns 0
TA260 003:084.852 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:084.857 - 0.004ms returns 0x00000039
TA260 003:084.861 JLINK_Go()
TA260 003:084.868   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:087.708 - 2.847ms 
TA260 003:087.723 JLINK_IsHalted()
TA260 003:088.227 - 0.503ms returns FALSE
TA260 003:088.260 JLINK_HasError()
TA260 003:089.339 JLINK_IsHalted()
TA260 003:089.807 - 0.467ms returns FALSE
TA260 003:089.814 JLINK_HasError()
TA260 003:091.336 JLINK_IsHalted()
TA260 003:091.804 - 0.468ms returns FALSE
TA260 003:091.810 JLINK_HasError()
TA260 003:093.339 JLINK_IsHalted()
TA260 003:095.727   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:096.313 - 2.973ms returns TRUE
TA260 003:096.326 JLINK_ReadReg(R15 (PC))
TA260 003:096.343 - 0.017ms returns 0x20000000
TA260 003:096.349 JLINK_ClrBPEx(BPHandle = 0x00000039)
TA260 003:096.353 - 0.004ms returns 0x00
TA260 003:096.357 JLINK_ReadReg(R0)
TA260 003:096.361 - 0.003ms returns 0x00000000
TA260 003:096.785 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:096.797   Data:  FB 4B 35 BF CF BD 34 BF F3 04 35 BF F3 04 35 BF ...
TA260 003:096.808   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:099.479 - 2.694ms returns 0x27C
TA260 003:099.492 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:099.496   Data:  21 55 49 BF 06 CF 1D BF 29 93 49 BF D1 7F 1D BF ...
TA260 003:099.505   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:101.465 - 1.973ms returns 0x184
TA260 003:101.471 JLINK_HasError()
TA260 003:101.476 JLINK_WriteReg(R0, 0x0800B800)
TA260 003:101.481 - 0.004ms returns 0
TA260 003:101.485 JLINK_WriteReg(R1, 0x00000400)
TA260 003:101.489 - 0.003ms returns 0
TA260 003:101.493 JLINK_WriteReg(R2, 0x20000184)
TA260 003:101.496 - 0.003ms returns 0
TA260 003:101.500 JLINK_WriteReg(R3, 0x00000000)
TA260 003:101.503 - 0.003ms returns 0
TA260 003:101.508 JLINK_WriteReg(R4, 0x00000000)
TA260 003:101.511 - 0.003ms returns 0
TA260 003:101.516 JLINK_WriteReg(R5, 0x00000000)
TA260 003:101.519 - 0.003ms returns 0
TA260 003:101.523 JLINK_WriteReg(R6, 0x00000000)
TA260 003:101.527 - 0.003ms returns 0
TA260 003:101.531 JLINK_WriteReg(R7, 0x00000000)
TA260 003:101.534 - 0.003ms returns 0
TA260 003:101.538 JLINK_WriteReg(R8, 0x00000000)
TA260 003:101.542 - 0.003ms returns 0
TA260 003:101.546 JLINK_WriteReg(R9, 0x20000180)
TA260 003:101.549 - 0.003ms returns 0
TA260 003:101.553 JLINK_WriteReg(R10, 0x00000000)
TA260 003:101.556 - 0.003ms returns 0
TA260 003:101.560 JLINK_WriteReg(R11, 0x00000000)
TA260 003:101.564 - 0.003ms returns 0
TA260 003:101.568 JLINK_WriteReg(R12, 0x00000000)
TA260 003:101.571 - 0.003ms returns 0
TA260 003:101.575 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:101.579 - 0.003ms returns 0
TA260 003:101.583 JLINK_WriteReg(R14, 0x20000001)
TA260 003:101.586 - 0.003ms returns 0
TA260 003:101.590 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:101.594 - 0.003ms returns 0
TA260 003:101.598 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:101.602 - 0.003ms returns 0
TA260 003:101.606 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:101.609 - 0.003ms returns 0
TA260 003:101.613 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:101.616 - 0.003ms returns 0
TA260 003:101.620 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:101.624 - 0.003ms returns 0
TA260 003:101.628 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:101.632 - 0.004ms returns 0x0000003A
TA260 003:101.637 JLINK_Go()
TA260 003:101.644   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:104.348 - 2.711ms 
TA260 003:104.354 JLINK_IsHalted()
TA260 003:104.850 - 0.496ms returns FALSE
TA260 003:104.856 JLINK_HasError()
TA260 003:106.804 JLINK_IsHalted()
TA260 003:107.296 - 0.491ms returns FALSE
TA260 003:107.306 JLINK_HasError()
TA260 003:108.804 JLINK_IsHalted()
TA260 003:111.143   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:111.646 - 2.841ms returns TRUE
TA260 003:111.652 JLINK_ReadReg(R15 (PC))
TA260 003:111.656 - 0.004ms returns 0x20000000
TA260 003:111.661 JLINK_ClrBPEx(BPHandle = 0x0000003A)
TA260 003:111.667 - 0.006ms returns 0x00
TA260 003:111.672 JLINK_ReadReg(R0)
TA260 003:111.675 - 0.003ms returns 0x00000000
TA260 003:112.175 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:112.184   Data:  65 8D 0E BF 47 A3 54 BF DA 39 0E BF 31 DB 54 BF ...
TA260 003:112.194   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:114.753 - 2.578ms returns 0x27C
TA260 003:114.766 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:114.770   Data:  AC 4F 64 BF FB E6 E6 BE 14 7D 64 BF 75 33 E6 BE ...
TA260 003:114.778   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:116.651 - 1.885ms returns 0x184
TA260 003:116.664 JLINK_HasError()
TA260 003:116.669 JLINK_WriteReg(R0, 0x0800BC00)
TA260 003:116.674 - 0.005ms returns 0
TA260 003:116.682 JLINK_WriteReg(R1, 0x00000400)
TA260 003:116.686 - 0.003ms returns 0
TA260 003:116.690 JLINK_WriteReg(R2, 0x20000184)
TA260 003:116.694 - 0.003ms returns 0
TA260 003:116.698 JLINK_WriteReg(R3, 0x00000000)
TA260 003:116.701 - 0.003ms returns 0
TA260 003:116.705 JLINK_WriteReg(R4, 0x00000000)
TA260 003:116.712 - 0.006ms returns 0
TA260 003:116.717 JLINK_WriteReg(R5, 0x00000000)
TA260 003:116.721 - 0.003ms returns 0
TA260 003:116.725 JLINK_WriteReg(R6, 0x00000000)
TA260 003:116.728 - 0.003ms returns 0
TA260 003:116.732 JLINK_WriteReg(R7, 0x00000000)
TA260 003:116.736 - 0.003ms returns 0
TA260 003:116.740 JLINK_WriteReg(R8, 0x00000000)
TA260 003:116.743 - 0.003ms returns 0
TA260 003:116.747 JLINK_WriteReg(R9, 0x20000180)
TA260 003:116.750 - 0.003ms returns 0
TA260 003:116.754 JLINK_WriteReg(R10, 0x00000000)
TA260 003:116.758 - 0.003ms returns 0
TA260 003:116.762 JLINK_WriteReg(R11, 0x00000000)
TA260 003:116.765 - 0.003ms returns 0
TA260 003:116.769 JLINK_WriteReg(R12, 0x00000000)
TA260 003:116.773 - 0.003ms returns 0
TA260 003:116.777 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:116.781 - 0.003ms returns 0
TA260 003:116.785 JLINK_WriteReg(R14, 0x20000001)
TA260 003:116.788 - 0.003ms returns 0
TA260 003:116.792 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:116.796 - 0.003ms returns 0
TA260 003:116.800 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:116.803 - 0.003ms returns 0
TA260 003:116.807 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:116.810 - 0.003ms returns 0
TA260 003:116.815 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:116.818 - 0.003ms returns 0
TA260 003:116.822 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:116.826 - 0.003ms returns 0
TA260 003:116.830 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:116.834 - 0.004ms returns 0x0000003B
TA260 003:116.839 JLINK_Go()
TA260 003:116.846   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:119.508 - 2.668ms 
TA260 003:119.520 JLINK_IsHalted()
TA260 003:120.009 - 0.488ms returns FALSE
TA260 003:120.014 JLINK_HasError()
TA260 003:121.310 JLINK_IsHalted()
TA260 003:121.759 - 0.448ms returns FALSE
TA260 003:121.769 JLINK_HasError()
TA260 003:123.309 JLINK_IsHalted()
TA260 003:123.783 - 0.474ms returns FALSE
TA260 003:123.789 JLINK_HasError()
TA260 003:125.814 JLINK_IsHalted()
TA260 003:128.185   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:128.694 - 2.879ms returns TRUE
TA260 003:128.701 JLINK_ReadReg(R15 (PC))
TA260 003:128.706 - 0.004ms returns 0x20000000
TA260 003:128.710 JLINK_ClrBPEx(BPHandle = 0x0000003B)
TA260 003:128.714 - 0.004ms returns 0x00
TA260 003:128.719 JLINK_ReadReg(R0)
TA260 003:128.723 - 0.003ms returns 0x00000000
TA260 003:129.046 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:129.053   Data:  C8 A8 C4 BE D4 5C 6C BF 15 EF C3 BE 5E 83 6C BF ...
TA260 003:129.063   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:131.565 - 2.518ms returns 0x27C
TA260 003:131.578 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:131.583   Data:  1B 84 76 BF 50 50 89 BE 24 9F 76 BF 93 8E 88 BE ...
TA260 003:131.592   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:133.413 - 1.834ms returns 0x184
TA260 003:133.426 JLINK_HasError()
TA260 003:133.431 JLINK_WriteReg(R0, 0x0800C000)
TA260 003:133.436 - 0.004ms returns 0
TA260 003:133.440 JLINK_WriteReg(R1, 0x00000400)
TA260 003:133.443 - 0.003ms returns 0
TA260 003:133.447 JLINK_WriteReg(R2, 0x20000184)
TA260 003:133.451 - 0.003ms returns 0
TA260 003:133.455 JLINK_WriteReg(R3, 0x00000000)
TA260 003:133.458 - 0.003ms returns 0
TA260 003:133.462 JLINK_WriteReg(R4, 0x00000000)
TA260 003:133.466 - 0.003ms returns 0
TA260 003:133.470 JLINK_WriteReg(R5, 0x00000000)
TA260 003:133.474 - 0.003ms returns 0
TA260 003:133.478 JLINK_WriteReg(R6, 0x00000000)
TA260 003:133.481 - 0.003ms returns 0
TA260 003:133.485 JLINK_WriteReg(R7, 0x00000000)
TA260 003:133.489 - 0.003ms returns 0
TA260 003:133.493 JLINK_WriteReg(R8, 0x00000000)
TA260 003:133.496 - 0.003ms returns 0
TA260 003:133.500 JLINK_WriteReg(R9, 0x20000180)
TA260 003:133.504 - 0.003ms returns 0
TA260 003:133.508 JLINK_WriteReg(R10, 0x00000000)
TA260 003:133.511 - 0.003ms returns 0
TA260 003:133.515 JLINK_WriteReg(R11, 0x00000000)
TA260 003:133.518 - 0.003ms returns 0
TA260 003:133.522 JLINK_WriteReg(R12, 0x00000000)
TA260 003:133.526 - 0.003ms returns 0
TA260 003:133.530 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:133.538 - 0.007ms returns 0
TA260 003:133.542 JLINK_WriteReg(R14, 0x20000001)
TA260 003:133.545 - 0.003ms returns 0
TA260 003:133.549 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:133.553 - 0.003ms returns 0
TA260 003:133.557 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:133.560 - 0.003ms returns 0
TA260 003:133.564 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:133.568 - 0.003ms returns 0
TA260 003:133.572 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:133.575 - 0.003ms returns 0
TA260 003:133.579 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:133.583 - 0.003ms returns 0
TA260 003:133.587 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:133.592 - 0.004ms returns 0x0000003C
TA260 003:133.596 JLINK_Go()
TA260 003:133.604   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:136.369 - 2.772ms 
TA260 003:136.379 JLINK_IsHalted()
TA260 003:136.864 - 0.484ms returns FALSE
TA260 003:136.871 JLINK_HasError()
TA260 003:138.893 JLINK_IsHalted()
TA260 003:139.475 - 0.581ms returns FALSE
TA260 003:139.481 JLINK_HasError()
TA260 003:140.891 JLINK_IsHalted()
TA260 003:143.237   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:143.741 - 2.849ms returns TRUE
TA260 003:143.748 JLINK_ReadReg(R15 (PC))
TA260 003:143.753 - 0.005ms returns 0x20000000
TA260 003:143.757 JLINK_ClrBPEx(BPHandle = 0x0000003C)
TA260 003:143.761 - 0.003ms returns 0x00
TA260 003:143.766 JLINK_ReadReg(R0)
TA260 003:143.769 - 0.003ms returns 0x00000000
TA260 003:144.112 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:144.121   Data:  18 50 49 BE 0E 01 7B BF C2 C5 47 BE BE 14 7B BF ...
TA260 003:144.131   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:146.748 - 2.635ms returns 0x27C
TA260 003:146.765 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:146.769   Data:  54 3F 7F BF 0A CB 99 BD F4 46 7F BF 05 A9 96 BD ...
TA260 003:146.779   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:148.746 - 1.981ms returns 0x184
TA260 003:148.760 JLINK_HasError()
TA260 003:148.792 JLINK_WriteReg(R0, 0x0800C400)
TA260 003:148.799 - 0.007ms returns 0
TA260 003:148.803 JLINK_WriteReg(R1, 0x00000400)
TA260 003:148.807 - 0.003ms returns 0
TA260 003:148.811 JLINK_WriteReg(R2, 0x20000184)
TA260 003:148.814 - 0.003ms returns 0
TA260 003:148.818 JLINK_WriteReg(R3, 0x00000000)
TA260 003:148.822 - 0.003ms returns 0
TA260 003:148.826 JLINK_WriteReg(R4, 0x00000000)
TA260 003:148.829 - 0.003ms returns 0
TA260 003:148.833 JLINK_WriteReg(R5, 0x00000000)
TA260 003:148.836 - 0.003ms returns 0
TA260 003:148.841 JLINK_WriteReg(R6, 0x00000000)
TA260 003:148.844 - 0.003ms returns 0
TA260 003:148.849 JLINK_WriteReg(R7, 0x00000000)
TA260 003:148.852 - 0.003ms returns 0
TA260 003:148.856 JLINK_WriteReg(R8, 0x00000000)
TA260 003:148.860 - 0.003ms returns 0
TA260 003:148.864 JLINK_WriteReg(R9, 0x20000180)
TA260 003:148.867 - 0.003ms returns 0
TA260 003:148.871 JLINK_WriteReg(R10, 0x00000000)
TA260 003:148.874 - 0.003ms returns 0
TA260 003:148.878 JLINK_WriteReg(R11, 0x00000000)
TA260 003:148.882 - 0.003ms returns 0
TA260 003:148.886 JLINK_WriteReg(R12, 0x00000000)
TA260 003:148.889 - 0.003ms returns 0
TA260 003:148.893 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:148.897 - 0.004ms returns 0
TA260 003:148.901 JLINK_WriteReg(R14, 0x20000001)
TA260 003:148.905 - 0.003ms returns 0
TA260 003:148.909 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:148.912 - 0.003ms returns 0
TA260 003:148.916 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:148.920 - 0.003ms returns 0
TA260 003:148.924 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:148.927 - 0.003ms returns 0
TA260 003:148.931 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:148.935 - 0.003ms returns 0
TA260 003:148.939 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:148.942 - 0.003ms returns 0
TA260 003:148.947 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:148.952 - 0.004ms returns 0x0000003D
TA260 003:148.956 JLINK_Go()
TA260 003:148.964   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:151.706 - 2.750ms 
TA260 003:151.717 JLINK_IsHalted()
TA260 003:152.182 - 0.464ms returns FALSE
TA260 003:152.194 JLINK_HasError()
TA260 003:154.903 JLINK_IsHalted()
TA260 003:155.364 - 0.461ms returns FALSE
TA260 003:155.371 JLINK_HasError()
TA260 003:157.407 JLINK_IsHalted()
TA260 003:159.778   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:160.271 - 2.864ms returns TRUE
TA260 003:160.278 JLINK_ReadReg(R15 (PC))
TA260 003:160.284 - 0.005ms returns 0x20000000
TA260 003:160.288 JLINK_ClrBPEx(BPHandle = 0x0000003D)
TA260 003:160.292 - 0.004ms returns 0x00
TA260 003:160.297 JLINK_ReadReg(R0)
TA260 003:160.300 - 0.003ms returns 0x00000000
TA260 003:160.636 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:160.644   Data:  D4 0F C9 BA EC FF 7F BF 00 00 00 80 00 00 80 BF ...
TA260 003:160.654   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:163.158 - 2.521ms returns 0x27C
TA260 003:163.165 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:163.168   Data:  72 2B 7E BF 2C 94 F7 3D 5E 1F 7E BF 73 B2 FA 3D ...
TA260 003:163.176   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:165.060 - 1.895ms returns 0x184
TA260 003:165.072 JLINK_HasError()
TA260 003:165.077 JLINK_WriteReg(R0, 0x0800C800)
TA260 003:165.082 - 0.005ms returns 0
TA260 003:165.086 JLINK_WriteReg(R1, 0x00000400)
TA260 003:165.089 - 0.003ms returns 0
TA260 003:165.093 JLINK_WriteReg(R2, 0x20000184)
TA260 003:165.097 - 0.003ms returns 0
TA260 003:165.101 JLINK_WriteReg(R3, 0x00000000)
TA260 003:165.104 - 0.003ms returns 0
TA260 003:165.109 JLINK_WriteReg(R4, 0x00000000)
TA260 003:165.112 - 0.003ms returns 0
TA260 003:165.116 JLINK_WriteReg(R5, 0x00000000)
TA260 003:165.120 - 0.003ms returns 0
TA260 003:165.124 JLINK_WriteReg(R6, 0x00000000)
TA260 003:165.127 - 0.003ms returns 0
TA260 003:165.131 JLINK_WriteReg(R7, 0x00000000)
TA260 003:165.134 - 0.003ms returns 0
TA260 003:165.138 JLINK_WriteReg(R8, 0x00000000)
TA260 003:165.142 - 0.003ms returns 0
TA260 003:165.146 JLINK_WriteReg(R9, 0x20000180)
TA260 003:165.149 - 0.003ms returns 0
TA260 003:165.153 JLINK_WriteReg(R10, 0x00000000)
TA260 003:165.156 - 0.003ms returns 0
TA260 003:165.160 JLINK_WriteReg(R11, 0x00000000)
TA260 003:165.164 - 0.003ms returns 0
TA260 003:165.168 JLINK_WriteReg(R12, 0x00000000)
TA260 003:165.171 - 0.003ms returns 0
TA260 003:165.175 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:165.179 - 0.003ms returns 0
TA260 003:165.183 JLINK_WriteReg(R14, 0x20000001)
TA260 003:165.187 - 0.003ms returns 0
TA260 003:165.191 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:165.194 - 0.003ms returns 0
TA260 003:165.199 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:165.202 - 0.004ms returns 0
TA260 003:165.206 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:165.210 - 0.003ms returns 0
TA260 003:165.214 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:165.217 - 0.003ms returns 0
TA260 003:165.221 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:165.225 - 0.003ms returns 0
TA260 003:165.229 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:165.233 - 0.004ms returns 0x0000003E
TA260 003:165.238 JLINK_Go()
TA260 003:165.245   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:168.032 - 2.794ms 
TA260 003:168.045 JLINK_IsHalted()
TA260 003:168.509 - 0.463ms returns FALSE
TA260 003:168.514 JLINK_HasError()
TA260 003:170.023 JLINK_IsHalted()
TA260 003:170.508 - 0.485ms returns FALSE
TA260 003:170.514 JLINK_HasError()
TA260 003:172.025 JLINK_IsHalted()
TA260 003:174.322   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:174.840 - 2.814ms returns TRUE
TA260 003:174.850 JLINK_ReadReg(R15 (PC))
TA260 003:174.855 - 0.005ms returns 0x20000000
TA260 003:174.888 JLINK_ClrBPEx(BPHandle = 0x0000003E)
TA260 003:174.900 - 0.012ms returns 0x00
TA260 003:174.905 JLINK_ReadReg(R0)
TA260 003:174.909 - 0.003ms returns 0x00000000
TA260 003:175.217 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:175.224   Data:  4D 3B 46 3E 48 28 7B BF C2 C5 47 3E BE 14 7B BF ...
TA260 003:175.234   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:177.835 - 2.618ms returns 0x27C
TA260 003:177.848 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:177.855   Data:  0E 53 73 BF EE DB 9F 3E BE 33 73 BF E5 9A A0 3E ...
TA260 003:177.866   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:179.736 - 1.888ms returns 0x184
TA260 003:179.743 JLINK_HasError()
TA260 003:179.748 JLINK_WriteReg(R0, 0x0800CC00)
TA260 003:179.752 - 0.004ms returns 0
TA260 003:179.756 JLINK_WriteReg(R1, 0x00000400)
TA260 003:179.760 - 0.003ms returns 0
TA260 003:179.764 JLINK_WriteReg(R2, 0x20000184)
TA260 003:179.767 - 0.003ms returns 0
TA260 003:179.771 JLINK_WriteReg(R3, 0x00000000)
TA260 003:179.774 - 0.003ms returns 0
TA260 003:179.778 JLINK_WriteReg(R4, 0x00000000)
TA260 003:179.782 - 0.003ms returns 0
TA260 003:179.786 JLINK_WriteReg(R5, 0x00000000)
TA260 003:179.790 - 0.003ms returns 0
TA260 003:179.794 JLINK_WriteReg(R6, 0x00000000)
TA260 003:179.797 - 0.003ms returns 0
TA260 003:179.801 JLINK_WriteReg(R7, 0x00000000)
TA260 003:179.805 - 0.003ms returns 0
TA260 003:179.809 JLINK_WriteReg(R8, 0x00000000)
TA260 003:179.812 - 0.003ms returns 0
TA260 003:179.816 JLINK_WriteReg(R9, 0x20000180)
TA260 003:179.820 - 0.003ms returns 0
TA260 003:179.824 JLINK_WriteReg(R10, 0x00000000)
TA260 003:179.827 - 0.003ms returns 0
TA260 003:179.831 JLINK_WriteReg(R11, 0x00000000)
TA260 003:179.834 - 0.003ms returns 0
TA260 003:179.838 JLINK_WriteReg(R12, 0x00000000)
TA260 003:179.842 - 0.003ms returns 0
TA260 003:179.846 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:179.850 - 0.003ms returns 0
TA260 003:179.854 JLINK_WriteReg(R14, 0x20000001)
TA260 003:179.857 - 0.003ms returns 0
TA260 003:179.861 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:179.865 - 0.003ms returns 0
TA260 003:179.869 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:179.872 - 0.003ms returns 0
TA260 003:179.876 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:179.879 - 0.003ms returns 0
TA260 003:179.883 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:179.887 - 0.003ms returns 0
TA260 003:179.891 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:179.894 - 0.003ms returns 0
TA260 003:179.899 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:179.903 - 0.004ms returns 0x0000003F
TA260 003:179.907 JLINK_Go()
TA260 003:179.914   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:182.592 - 2.685ms 
TA260 003:182.599 JLINK_IsHalted()
TA260 003:183.066 - 0.466ms returns FALSE
TA260 003:183.072 JLINK_HasError()
TA260 003:185.039 JLINK_IsHalted()
TA260 003:185.514 - 0.474ms returns FALSE
TA260 003:185.526 JLINK_HasError()
TA260 003:187.038 JLINK_IsHalted()
TA260 003:189.476   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:189.989 - 2.951ms returns TRUE
TA260 003:189.996 JLINK_ReadReg(R15 (PC))
TA260 003:190.000 - 0.004ms returns 0x20000000
TA260 003:190.005 JLINK_ClrBPEx(BPHandle = 0x0000003F)
TA260 003:190.009 - 0.003ms returns 0x00
TA260 003:190.013 JLINK_ReadReg(R0)
TA260 003:190.016 - 0.003ms returns 0x00000000
TA260 003:190.343 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:190.350   Data:  44 35 C3 3E C5 A9 6C BF 15 EF C3 3E 5E 83 6C BF ...
TA260 003:190.359   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:192.982 - 2.638ms returns 0x27C
TA260 003:192.992 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:192.996   Data:  DB 20 5F BF 22 AE FB 3E 81 EF 5E BF 27 5D FC 3E ...
TA260 003:193.005   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:194.850 - 1.857ms returns 0x184
TA260 003:194.867 JLINK_HasError()
TA260 003:194.873 JLINK_WriteReg(R0, 0x0800D000)
TA260 003:194.879 - 0.006ms returns 0
TA260 003:194.883 JLINK_WriteReg(R1, 0x00000400)
TA260 003:194.887 - 0.003ms returns 0
TA260 003:194.891 JLINK_WriteReg(R2, 0x20000184)
TA260 003:194.894 - 0.003ms returns 0
TA260 003:194.898 JLINK_WriteReg(R3, 0x00000000)
TA260 003:194.902 - 0.003ms returns 0
TA260 003:194.919 JLINK_WriteReg(R4, 0x00000000)
TA260 003:194.922 - 0.003ms returns 0
TA260 003:194.927 JLINK_WriteReg(R5, 0x00000000)
TA260 003:194.930 - 0.003ms returns 0
TA260 003:194.934 JLINK_WriteReg(R6, 0x00000000)
TA260 003:194.938 - 0.003ms returns 0
TA260 003:194.942 JLINK_WriteReg(R7, 0x00000000)
TA260 003:194.949 - 0.007ms returns 0
TA260 003:194.956 JLINK_WriteReg(R8, 0x00000000)
TA260 003:194.959 - 0.003ms returns 0
TA260 003:194.963 JLINK_WriteReg(R9, 0x20000180)
TA260 003:194.966 - 0.003ms returns 0
TA260 003:194.970 JLINK_WriteReg(R10, 0x00000000)
TA260 003:194.974 - 0.003ms returns 0
TA260 003:194.978 JLINK_WriteReg(R11, 0x00000000)
TA260 003:194.982 - 0.003ms returns 0
TA260 003:194.986 JLINK_WriteReg(R12, 0x00000000)
TA260 003:194.999 - 0.013ms returns 0
TA260 003:195.004 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:195.007 - 0.003ms returns 0
TA260 003:195.011 JLINK_WriteReg(R14, 0x20000001)
TA260 003:195.015 - 0.003ms returns 0
TA260 003:195.019 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:195.022 - 0.003ms returns 0
TA260 003:195.027 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:195.030 - 0.003ms returns 0
TA260 003:195.034 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:195.037 - 0.003ms returns 0
TA260 003:195.042 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:195.045 - 0.003ms returns 0
TA260 003:195.049 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:195.052 - 0.003ms returns 0
TA260 003:195.062 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:195.066 - 0.004ms returns 0x00000040
TA260 003:195.070 JLINK_Go()
TA260 003:195.079   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:198.574 - 3.503ms 
TA260 003:198.588 JLINK_IsHalted()
TA260 003:199.094 - 0.505ms returns FALSE
TA260 003:199.103 JLINK_HasError()
TA260 003:200.661 JLINK_IsHalted()
TA260 003:201.137 - 0.475ms returns FALSE
TA260 003:201.143 JLINK_HasError()
TA260 003:203.167 JLINK_IsHalted()
TA260 003:205.594   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:206.074 - 2.906ms returns TRUE
TA260 003:206.087 JLINK_ReadReg(R15 (PC))
TA260 003:206.093 - 0.005ms returns 0x20000000
TA260 003:206.097 JLINK_ClrBPEx(BPHandle = 0x00000040)
TA260 003:206.101 - 0.004ms returns 0x00
TA260 003:206.106 JLINK_ReadReg(R0)
TA260 003:206.110 - 0.004ms returns 0x00000000
TA260 003:206.638 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:206.646   Data:  38 E6 0D 3F FB 12 55 BF DA 39 0E 3F 31 DB 54 BF ...
TA260 003:206.659   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:209.836 - 3.197ms returns 0x27C
TA260 003:209.850 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:209.854   Data:  89 5B 42 BF 2A EA 26 3F 0B 1A 42 BF 56 36 27 3F ...
TA260 003:209.866   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:211.752 - 1.901ms returns 0x184
TA260 003:211.765 JLINK_HasError()
TA260 003:211.771 JLINK_WriteReg(R0, 0x0800D400)
TA260 003:211.776 - 0.005ms returns 0
TA260 003:211.780 JLINK_WriteReg(R1, 0x00000400)
TA260 003:211.784 - 0.003ms returns 0
TA260 003:211.788 JLINK_WriteReg(R2, 0x20000184)
TA260 003:211.791 - 0.003ms returns 0
TA260 003:211.795 JLINK_WriteReg(R3, 0x00000000)
TA260 003:211.799 - 0.003ms returns 0
TA260 003:211.803 JLINK_WriteReg(R4, 0x00000000)
TA260 003:211.806 - 0.003ms returns 0
TA260 003:211.810 JLINK_WriteReg(R5, 0x00000000)
TA260 003:211.813 - 0.003ms returns 0
TA260 003:211.817 JLINK_WriteReg(R6, 0x00000000)
TA260 003:211.820 - 0.003ms returns 0
TA260 003:211.824 JLINK_WriteReg(R7, 0x00000000)
TA260 003:211.828 - 0.003ms returns 0
TA260 003:211.832 JLINK_WriteReg(R8, 0x00000000)
TA260 003:211.836 - 0.003ms returns 0
TA260 003:211.840 JLINK_WriteReg(R9, 0x20000180)
TA260 003:211.843 - 0.003ms returns 0
TA260 003:211.848 JLINK_WriteReg(R10, 0x00000000)
TA260 003:211.851 - 0.003ms returns 0
TA260 003:211.855 JLINK_WriteReg(R11, 0x00000000)
TA260 003:211.858 - 0.003ms returns 0
TA260 003:211.862 JLINK_WriteReg(R12, 0x00000000)
TA260 003:211.866 - 0.003ms returns 0
TA260 003:211.870 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:211.873 - 0.003ms returns 0
TA260 003:211.877 JLINK_WriteReg(R14, 0x20000001)
TA260 003:211.881 - 0.003ms returns 0
TA260 003:211.885 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:211.888 - 0.003ms returns 0
TA260 003:211.892 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:211.896 - 0.003ms returns 0
TA260 003:211.900 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:211.905 - 0.005ms returns 0
TA260 003:211.911 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:211.914 - 0.003ms returns 0
TA260 003:211.918 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:211.921 - 0.003ms returns 0
TA260 003:211.926 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:211.930 - 0.004ms returns 0x00000041
TA260 003:211.934 JLINK_Go()
TA260 003:211.942   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:214.736 - 2.801ms 
TA260 003:214.743 JLINK_IsHalted()
TA260 003:215.235 - 0.491ms returns FALSE
TA260 003:215.240 JLINK_HasError()
TA260 003:216.675 JLINK_IsHalted()
TA260 003:217.134 - 0.459ms returns FALSE
TA260 003:217.141 JLINK_HasError()
TA260 003:218.675 JLINK_IsHalted()
TA260 003:220.971   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:221.506 - 2.830ms returns TRUE
TA260 003:221.522 JLINK_ReadReg(R15 (PC))
TA260 003:221.528 - 0.006ms returns 0x20000000
TA260 003:221.533 JLINK_ClrBPEx(BPHandle = 0x00000041)
TA260 003:221.537 - 0.004ms returns 0x00
TA260 003:221.542 JLINK_ReadReg(R0)
TA260 003:221.545 - 0.003ms returns 0x00000000
TA260 003:221.916 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:221.923   Data:  CF BD 34 3F FB 4B 35 BF F3 04 35 3F F3 04 35 BF ...
TA260 003:221.934   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:224.471 - 2.554ms returns 0x27C
TA260 003:224.487 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:224.491   Data:  22 1E 1E BF 29 93 49 3F 06 CF 1D BF 12 D1 49 3F ...
TA260 003:224.501   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:226.351 - 1.863ms returns 0x184
TA260 003:226.397 JLINK_HasError()
TA260 003:226.406 JLINK_WriteReg(R0, 0x0800D800)
TA260 003:226.412 - 0.007ms returns 0
TA260 003:226.418 JLINK_WriteReg(R1, 0x00000400)
TA260 003:226.423 - 0.004ms returns 0
TA260 003:226.428 JLINK_WriteReg(R2, 0x20000184)
TA260 003:226.432 - 0.004ms returns 0
TA260 003:226.437 JLINK_WriteReg(R3, 0x00000000)
TA260 003:226.442 - 0.004ms returns 0
TA260 003:226.447 JLINK_WriteReg(R4, 0x00000000)
TA260 003:226.452 - 0.004ms returns 0
TA260 003:226.457 JLINK_WriteReg(R5, 0x00000000)
TA260 003:226.461 - 0.004ms returns 0
TA260 003:226.466 JLINK_WriteReg(R6, 0x00000000)
TA260 003:226.471 - 0.004ms returns 0
TA260 003:226.475 JLINK_WriteReg(R7, 0x00000000)
TA260 003:226.478 - 0.003ms returns 0
TA260 003:226.482 JLINK_WriteReg(R8, 0x00000000)
TA260 003:226.486 - 0.003ms returns 0
TA260 003:226.490 JLINK_WriteReg(R9, 0x20000180)
TA260 003:226.493 - 0.003ms returns 0
TA260 003:226.498 JLINK_WriteReg(R10, 0x00000000)
TA260 003:226.501 - 0.003ms returns 0
TA260 003:226.505 JLINK_WriteReg(R11, 0x00000000)
TA260 003:226.508 - 0.003ms returns 0
TA260 003:226.512 JLINK_WriteReg(R12, 0x00000000)
TA260 003:226.516 - 0.003ms returns 0
TA260 003:226.520 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:226.524 - 0.003ms returns 0
TA260 003:226.528 JLINK_WriteReg(R14, 0x20000001)
TA260 003:226.531 - 0.003ms returns 0
TA260 003:226.535 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:226.539 - 0.003ms returns 0
TA260 003:226.543 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:226.547 - 0.003ms returns 0
TA260 003:226.551 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:226.554 - 0.003ms returns 0
TA260 003:226.558 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:226.562 - 0.003ms returns 0
TA260 003:226.566 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:226.569 - 0.003ms returns 0
TA260 003:226.574 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:226.578 - 0.004ms returns 0x00000042
TA260 003:226.582 JLINK_Go()
TA260 003:226.591   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:229.354 - 2.771ms 
TA260 003:229.376 JLINK_IsHalted()
TA260 003:229.852 - 0.476ms returns FALSE
TA260 003:229.858 JLINK_HasError()
TA260 003:231.186 JLINK_IsHalted()
TA260 003:231.697 - 0.511ms returns FALSE
TA260 003:231.705 JLINK_HasError()
TA260 003:233.187 JLINK_IsHalted()
TA260 003:233.616 - 0.429ms returns FALSE
TA260 003:233.624 JLINK_HasError()
TA260 003:234.685 JLINK_IsHalted()
TA260 003:236.981   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:237.515 - 2.830ms returns TRUE
TA260 003:237.525 JLINK_ReadReg(R15 (PC))
TA260 003:237.530 - 0.005ms returns 0x20000000
TA260 003:237.535 JLINK_ClrBPEx(BPHandle = 0x00000042)
TA260 003:237.539 - 0.003ms returns 0x00
TA260 003:237.544 JLINK_ReadReg(R0)
TA260 003:237.547 - 0.003ms returns 0x00000000
TA260 003:238.321 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:238.332   Data:  47 A3 54 3F 65 8D 0E BF 31 DB 54 3F DA 39 0E BF ...
TA260 003:238.344   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:240.878 - 2.557ms returns 0x27C
TA260 003:240.891 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:240.895   Data:  5D 9A E7 BE 14 7D 64 3F FB E6 E6 BE 59 AA 64 3F ...
TA260 003:240.905   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:242.829 - 1.938ms returns 0x184
TA260 003:242.840 JLINK_HasError()
TA260 003:242.845 JLINK_WriteReg(R0, 0x0800DC00)
TA260 003:242.850 - 0.005ms returns 0
TA260 003:242.854 JLINK_WriteReg(R1, 0x00000400)
TA260 003:242.858 - 0.003ms returns 0
TA260 003:242.862 JLINK_WriteReg(R2, 0x20000184)
TA260 003:242.865 - 0.003ms returns 0
TA260 003:242.869 JLINK_WriteReg(R3, 0x00000000)
TA260 003:242.873 - 0.003ms returns 0
TA260 003:242.877 JLINK_WriteReg(R4, 0x00000000)
TA260 003:242.880 - 0.003ms returns 0
TA260 003:242.884 JLINK_WriteReg(R5, 0x00000000)
TA260 003:242.888 - 0.003ms returns 0
TA260 003:242.892 JLINK_WriteReg(R6, 0x00000000)
TA260 003:242.895 - 0.003ms returns 0
TA260 003:242.899 JLINK_WriteReg(R7, 0x00000000)
TA260 003:242.903 - 0.003ms returns 0
TA260 003:242.907 JLINK_WriteReg(R8, 0x00000000)
TA260 003:242.910 - 0.003ms returns 0
TA260 003:242.914 JLINK_WriteReg(R9, 0x20000180)
TA260 003:242.918 - 0.003ms returns 0
TA260 003:242.922 JLINK_WriteReg(R10, 0x00000000)
TA260 003:242.925 - 0.003ms returns 0
TA260 003:242.929 JLINK_WriteReg(R11, 0x00000000)
TA260 003:242.933 - 0.003ms returns 0
TA260 003:242.937 JLINK_WriteReg(R12, 0x00000000)
TA260 003:242.940 - 0.003ms returns 0
TA260 003:242.944 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:242.948 - 0.004ms returns 0
TA260 003:242.953 JLINK_WriteReg(R14, 0x20000001)
TA260 003:242.956 - 0.003ms returns 0
TA260 003:242.960 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:242.963 - 0.003ms returns 0
TA260 003:242.968 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:242.972 - 0.004ms returns 0
TA260 003:242.976 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:242.979 - 0.003ms returns 0
TA260 003:242.983 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:242.986 - 0.003ms returns 0
TA260 003:242.991 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:242.994 - 0.003ms returns 0
TA260 003:242.998 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:243.003 - 0.004ms returns 0x00000043
TA260 003:243.007 JLINK_Go()
TA260 003:243.015   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:245.738 - 2.731ms 
TA260 003:245.746 JLINK_IsHalted()
TA260 003:246.234 - 0.487ms returns FALSE
TA260 003:246.242 JLINK_HasError()
TA260 003:248.198 JLINK_IsHalted()
TA260 003:248.697 - 0.498ms returns FALSE
TA260 003:248.710 JLINK_HasError()
TA260 003:250.194 JLINK_IsHalted()
TA260 003:252.536   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:253.009 - 2.815ms returns TRUE
TA260 003:253.020 JLINK_ReadReg(R15 (PC))
TA260 003:253.025 - 0.005ms returns 0x20000000
TA260 003:253.056 JLINK_ClrBPEx(BPHandle = 0x00000043)
TA260 003:253.061 - 0.005ms returns 0x00
TA260 003:253.066 JLINK_ReadReg(R0)
TA260 003:253.069 - 0.003ms returns 0x00000000
TA260 003:253.407 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:253.414   Data:  D4 5C 6C 3F C8 A8 C4 BE 5E 83 6C 3F 15 EF C3 BE ...
TA260 003:253.424   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:255.973 - 2.565ms returns 0x27C
TA260 003:255.986 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:255.990   Data:  F8 11 8A BE 24 9F 76 3F 50 50 89 BE 07 BA 76 3F ...
TA260 003:256.000   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:257.838 - 1.852ms returns 0x184
TA260 003:257.852 JLINK_HasError()
TA260 003:257.858 JLINK_WriteReg(R0, 0x0800E000)
TA260 003:257.866 - 0.008ms returns 0
TA260 003:257.872 JLINK_WriteReg(R1, 0x00000400)
TA260 003:257.876 - 0.003ms returns 0
TA260 003:257.880 JLINK_WriteReg(R2, 0x20000184)
TA260 003:257.883 - 0.003ms returns 0
TA260 003:257.887 JLINK_WriteReg(R3, 0x00000000)
TA260 003:257.890 - 0.003ms returns 0
TA260 003:257.895 JLINK_WriteReg(R4, 0x00000000)
TA260 003:257.898 - 0.003ms returns 0
TA260 003:257.902 JLINK_WriteReg(R5, 0x00000000)
TA260 003:257.906 - 0.003ms returns 0
TA260 003:257.910 JLINK_WriteReg(R6, 0x00000000)
TA260 003:257.913 - 0.003ms returns 0
TA260 003:257.917 JLINK_WriteReg(R7, 0x00000000)
TA260 003:257.920 - 0.003ms returns 0
TA260 003:257.925 JLINK_WriteReg(R8, 0x00000000)
TA260 003:257.928 - 0.003ms returns 0
TA260 003:257.932 JLINK_WriteReg(R9, 0x20000180)
TA260 003:257.935 - 0.003ms returns 0
TA260 003:257.939 JLINK_WriteReg(R10, 0x00000000)
TA260 003:257.943 - 0.003ms returns 0
TA260 003:257.947 JLINK_WriteReg(R11, 0x00000000)
TA260 003:257.950 - 0.003ms returns 0
TA260 003:257.954 JLINK_WriteReg(R12, 0x00000000)
TA260 003:257.958 - 0.003ms returns 0
TA260 003:257.962 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:257.966 - 0.003ms returns 0
TA260 003:257.970 JLINK_WriteReg(R14, 0x20000001)
TA260 003:257.973 - 0.003ms returns 0
TA260 003:257.977 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:257.980 - 0.003ms returns 0
TA260 003:257.984 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:257.988 - 0.003ms returns 0
TA260 003:257.992 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:257.995 - 0.003ms returns 0
TA260 003:257.999 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:258.003 - 0.003ms returns 0
TA260 003:258.007 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:258.010 - 0.003ms returns 0
TA260 003:258.015 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:258.019 - 0.004ms returns 0x00000044
TA260 003:258.024 JLINK_Go()
TA260 003:258.032   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:260.838 - 2.813ms 
TA260 003:260.844 JLINK_IsHalted()
TA260 003:261.327 - 0.483ms returns FALSE
TA260 003:261.333 JLINK_HasError()
TA260 003:263.198 JLINK_IsHalted()
TA260 003:263.643 - 0.444ms returns FALSE
TA260 003:263.649 JLINK_HasError()
TA260 003:264.704 JLINK_IsHalted()
TA260 003:265.166 - 0.461ms returns FALSE
TA260 003:265.171 JLINK_HasError()
TA260 003:266.704 JLINK_IsHalted()
TA260 003:268.965   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:269.464 - 2.759ms returns TRUE
TA260 003:269.470 JLINK_ReadReg(R15 (PC))
TA260 003:269.475 - 0.004ms returns 0x20000000
TA260 003:269.480 JLINK_ClrBPEx(BPHandle = 0x00000044)
TA260 003:269.483 - 0.003ms returns 0x00
TA260 003:269.487 JLINK_ReadReg(R0)
TA260 003:269.491 - 0.003ms returns 0x00000000
TA260 003:269.833 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:269.840   Data:  0E 01 7B 3F 18 50 49 BE BE 14 7B 3F C2 C5 47 BE ...
TA260 003:269.850   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:272.465 - 2.632ms returns 0x27C
TA260 003:272.471 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:272.475   Data:  F9 EC 9C BD F4 46 7F 3F 0A CB 99 BD 6D 4E 7F 3F ...
TA260 003:272.482   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:274.335 - 1.863ms returns 0x184
TA260 003:274.349 JLINK_HasError()
TA260 003:274.355 JLINK_WriteReg(R0, 0x0800E400)
TA260 003:274.360 - 0.005ms returns 0
TA260 003:274.364 JLINK_WriteReg(R1, 0x00000400)
TA260 003:274.368 - 0.003ms returns 0
TA260 003:274.372 JLINK_WriteReg(R2, 0x20000184)
TA260 003:274.375 - 0.003ms returns 0
TA260 003:274.379 JLINK_WriteReg(R3, 0x00000000)
TA260 003:274.383 - 0.003ms returns 0
TA260 003:274.387 JLINK_WriteReg(R4, 0x00000000)
TA260 003:274.390 - 0.003ms returns 0
TA260 003:274.394 JLINK_WriteReg(R5, 0x00000000)
TA260 003:274.398 - 0.003ms returns 0
TA260 003:274.402 JLINK_WriteReg(R6, 0x00000000)
TA260 003:274.405 - 0.003ms returns 0
TA260 003:274.409 JLINK_WriteReg(R7, 0x00000000)
TA260 003:274.413 - 0.003ms returns 0
TA260 003:274.417 JLINK_WriteReg(R8, 0x00000000)
TA260 003:274.420 - 0.003ms returns 0
TA260 003:274.424 JLINK_WriteReg(R9, 0x20000180)
TA260 003:274.432 - 0.007ms returns 0
TA260 003:274.436 JLINK_WriteReg(R10, 0x00000000)
TA260 003:274.439 - 0.003ms returns 0
TA260 003:274.443 JLINK_WriteReg(R11, 0x00000000)
TA260 003:274.447 - 0.003ms returns 0
TA260 003:274.451 JLINK_WriteReg(R12, 0x00000000)
TA260 003:274.454 - 0.003ms returns 0
TA260 003:274.458 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:274.462 - 0.003ms returns 0
TA260 003:274.466 JLINK_WriteReg(R14, 0x20000001)
TA260 003:274.469 - 0.003ms returns 0
TA260 003:274.473 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:274.477 - 0.003ms returns 0
TA260 003:274.481 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:274.484 - 0.003ms returns 0
TA260 003:274.488 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:274.492 - 0.003ms returns 0
TA260 003:274.496 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:274.499 - 0.003ms returns 0
TA260 003:274.503 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:274.506 - 0.003ms returns 0
TA260 003:274.511 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:274.515 - 0.004ms returns 0x00000045
TA260 003:274.519 JLINK_Go()
TA260 003:274.526   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:277.283 - 2.764ms 
TA260 003:277.300 JLINK_IsHalted()
TA260 003:277.790 - 0.490ms returns FALSE
TA260 003:277.798 JLINK_HasError()
TA260 003:280.152 JLINK_IsHalted()
TA260 003:280.659 - 0.507ms returns FALSE
TA260 003:280.665 JLINK_HasError()
TA260 003:282.156 JLINK_IsHalted()
TA260 003:284.496   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:284.990 - 2.833ms returns TRUE
TA260 003:284.998 JLINK_ReadReg(R15 (PC))
TA260 003:285.003 - 0.005ms returns 0x20000000
TA260 003:285.008 JLINK_ClrBPEx(BPHandle = 0x00000045)
TA260 003:285.012 - 0.004ms returns 0x00
TA260 003:285.017 JLINK_ReadReg(R0)
TA260 003:285.020 - 0.003ms returns 0x00000000
TA260 003:285.468 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:285.476   Data:  EC FF 7F 3F D4 0F C9 BA 28 E8 00 08 00 00 00 20 ...
TA260 003:285.486   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:288.111 - 2.642ms returns 0x27C
TA260 003:288.127 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:288.131   Data:  FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF ...
TA260 003:288.142   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:290.026 - 1.897ms returns 0x184
TA260 003:290.039 JLINK_HasError()
TA260 003:290.045 JLINK_WriteReg(R0, 0x0800E800)
TA260 003:290.050 - 0.005ms returns 0
TA260 003:290.054 JLINK_WriteReg(R1, 0x000000D8)
TA260 003:290.058 - 0.003ms returns 0
TA260 003:290.062 JLINK_WriteReg(R2, 0x20000184)
TA260 003:290.066 - 0.003ms returns 0
TA260 003:290.070 JLINK_WriteReg(R3, 0x00000000)
TA260 003:290.073 - 0.003ms returns 0
TA260 003:290.077 JLINK_WriteReg(R4, 0x00000000)
TA260 003:290.080 - 0.003ms returns 0
TA260 003:290.084 JLINK_WriteReg(R5, 0x00000000)
TA260 003:290.088 - 0.003ms returns 0
TA260 003:290.092 JLINK_WriteReg(R6, 0x00000000)
TA260 003:290.096 - 0.003ms returns 0
TA260 003:290.100 JLINK_WriteReg(R7, 0x00000000)
TA260 003:290.103 - 0.003ms returns 0
TA260 003:290.107 JLINK_WriteReg(R8, 0x00000000)
TA260 003:290.111 - 0.003ms returns 0
TA260 003:290.115 JLINK_WriteReg(R9, 0x20000180)
TA260 003:290.118 - 0.003ms returns 0
TA260 003:290.122 JLINK_WriteReg(R10, 0x00000000)
TA260 003:290.126 - 0.003ms returns 0
TA260 003:290.130 JLINK_WriteReg(R11, 0x00000000)
TA260 003:290.133 - 0.003ms returns 0
TA260 003:290.137 JLINK_WriteReg(R12, 0x00000000)
TA260 003:290.140 - 0.003ms returns 0
TA260 003:290.145 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:290.148 - 0.004ms returns 0
TA260 003:290.152 JLINK_WriteReg(R14, 0x20000001)
TA260 003:290.156 - 0.003ms returns 0
TA260 003:290.160 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:290.163 - 0.003ms returns 0
TA260 003:290.167 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:290.171 - 0.003ms returns 0
TA260 003:290.175 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:290.178 - 0.003ms returns 0
TA260 003:290.182 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:290.186 - 0.004ms returns 0
TA260 003:290.194 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:290.199 - 0.005ms returns 0
TA260 003:290.204 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:290.208 - 0.004ms returns 0x00000046
TA260 003:290.212 JLINK_Go()
TA260 003:290.220   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:293.055 - 2.842ms 
TA260 003:293.067 JLINK_IsHalted()
TA260 003:293.476 - 0.408ms returns FALSE
TA260 003:293.482 JLINK_HasError()
TA260 003:295.167 JLINK_IsHalted()
TA260 003:297.494   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:298.001 - 2.833ms returns TRUE
TA260 003:298.012 JLINK_ReadReg(R15 (PC))
TA260 003:298.017 - 0.005ms returns 0x20000000
TA260 003:298.022 JLINK_ClrBPEx(BPHandle = 0x00000046)
TA260 003:298.026 - 0.004ms returns 0x00
TA260 003:298.030 JLINK_ReadReg(R0)
TA260 003:298.034 - 0.003ms returns 0x00000000
TA260 003:298.038 JLINK_HasError()
TA260 003:298.043 JLINK_WriteReg(R0, 0x00000002)
TA260 003:298.047 - 0.004ms returns 0
TA260 003:298.052 JLINK_WriteReg(R1, 0x000000D8)
TA260 003:298.055 - 0.003ms returns 0
TA260 003:298.059 JLINK_WriteReg(R2, 0x20000184)
TA260 003:298.062 - 0.003ms returns 0
TA260 003:298.067 JLINK_WriteReg(R3, 0x00000000)
TA260 003:298.070 - 0.003ms returns 0
TA260 003:298.074 JLINK_WriteReg(R4, 0x00000000)
TA260 003:298.078 - 0.003ms returns 0
TA260 003:298.082 JLINK_WriteReg(R5, 0x00000000)
TA260 003:298.085 - 0.003ms returns 0
TA260 003:298.089 JLINK_WriteReg(R6, 0x00000000)
TA260 003:298.092 - 0.003ms returns 0
TA260 003:298.096 JLINK_WriteReg(R7, 0x00000000)
TA260 003:298.100 - 0.003ms returns 0
TA260 003:298.104 JLINK_WriteReg(R8, 0x00000000)
TA260 003:298.107 - 0.003ms returns 0
TA260 003:298.111 JLINK_WriteReg(R9, 0x20000180)
TA260 003:298.114 - 0.003ms returns 0
TA260 003:298.118 JLINK_WriteReg(R10, 0x00000000)
TA260 003:298.122 - 0.003ms returns 0
TA260 003:298.126 JLINK_WriteReg(R11, 0x00000000)
TA260 003:298.129 - 0.003ms returns 0
TA260 003:298.133 JLINK_WriteReg(R12, 0x00000000)
TA260 003:298.136 - 0.003ms returns 0
TA260 003:298.140 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:298.144 - 0.004ms returns 0
TA260 003:298.148 JLINK_WriteReg(R14, 0x20000001)
TA260 003:298.152 - 0.003ms returns 0
TA260 003:298.156 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 003:298.159 - 0.003ms returns 0
TA260 003:298.164 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:298.167 - 0.003ms returns 0
TA260 003:298.171 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:298.174 - 0.003ms returns 0
TA260 003:298.178 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:298.182 - 0.003ms returns 0
TA260 003:298.186 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:298.189 - 0.003ms returns 0
TA260 003:298.193 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:298.197 - 0.004ms returns 0x00000047
TA260 003:298.201 JLINK_Go()
TA260 003:298.209   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:300.892 - 2.690ms 
TA260 003:300.898 JLINK_IsHalted()
TA260 003:303.172   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:303.647 - 2.749ms returns TRUE
TA260 003:303.658 JLINK_ReadReg(R15 (PC))
TA260 003:303.663 - 0.005ms returns 0x20000000
TA260 003:303.708 JLINK_ClrBPEx(BPHandle = 0x00000047)
TA260 003:303.714 - 0.005ms returns 0x00
TA260 003:303.719 JLINK_ReadReg(R0)
TA260 003:303.723 - 0.004ms returns 0x00000000
TA260 003:359.309 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 003:359.324   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 003:359.341   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 003:361.159 - 1.850ms returns 0x184
TA260 003:361.200 JLINK_HasError()
TA260 003:361.206 JLINK_WriteReg(R0, 0x08000000)
TA260 003:361.211 - 0.005ms returns 0
TA260 003:361.216 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 003:361.220 - 0.003ms returns 0
TA260 003:361.224 JLINK_WriteReg(R2, 0x00000003)
TA260 003:361.227 - 0.003ms returns 0
TA260 003:361.231 JLINK_WriteReg(R3, 0x00000000)
TA260 003:361.234 - 0.003ms returns 0
TA260 003:361.239 JLINK_WriteReg(R4, 0x00000000)
TA260 003:361.242 - 0.003ms returns 0
TA260 003:361.246 JLINK_WriteReg(R5, 0x00000000)
TA260 003:361.252 - 0.006ms returns 0
TA260 003:361.258 JLINK_WriteReg(R6, 0x00000000)
TA260 003:361.262 - 0.004ms returns 0
TA260 003:361.266 JLINK_WriteReg(R7, 0x00000000)
TA260 003:361.269 - 0.003ms returns 0
TA260 003:361.274 JLINK_WriteReg(R8, 0x00000000)
TA260 003:361.277 - 0.003ms returns 0
TA260 003:361.281 JLINK_WriteReg(R9, 0x20000180)
TA260 003:361.284 - 0.003ms returns 0
TA260 003:361.288 JLINK_WriteReg(R10, 0x00000000)
TA260 003:361.292 - 0.003ms returns 0
TA260 003:361.296 JLINK_WriteReg(R11, 0x00000000)
TA260 003:361.299 - 0.003ms returns 0
TA260 003:361.303 JLINK_WriteReg(R12, 0x00000000)
TA260 003:361.306 - 0.003ms returns 0
TA260 003:361.310 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:361.314 - 0.004ms returns 0
TA260 003:361.318 JLINK_WriteReg(R14, 0x20000001)
TA260 003:361.322 - 0.003ms returns 0
TA260 003:361.326 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 003:361.329 - 0.003ms returns 0
TA260 003:361.333 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:361.337 - 0.003ms returns 0
TA260 003:361.341 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:361.344 - 0.003ms returns 0
TA260 003:361.348 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:361.352 - 0.003ms returns 0
TA260 003:361.356 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:361.360 - 0.003ms returns 0
TA260 003:361.364 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:361.372   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:361.898 - 0.533ms returns 0x00000048
TA260 003:361.912 JLINK_Go()
TA260 003:361.918   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 003:362.384   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:365.122 - 3.210ms 
TA260 003:365.133 JLINK_IsHalted()
TA260 003:367.478   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:367.987 - 2.853ms returns TRUE
TA260 003:367.994 JLINK_ReadReg(R15 (PC))
TA260 003:367.998 - 0.004ms returns 0x20000000
TA260 003:368.003 JLINK_ClrBPEx(BPHandle = 0x00000048)
TA260 003:368.007 - 0.003ms returns 0x00
TA260 003:368.011 JLINK_ReadReg(R0)
TA260 003:368.014 - 0.003ms returns 0x00000000
TA260 003:368.019 JLINK_HasError()
TA260 003:368.024 JLINK_WriteReg(R0, 0xFFFFFFFF)
TA260 003:368.028 - 0.003ms returns 0
TA260 003:368.032 JLINK_WriteReg(R1, 0x08000000)
TA260 003:368.035 - 0.003ms returns 0
TA260 003:368.039 JLINK_WriteReg(R2, 0x0000E8D8)
TA260 003:368.042 - 0.003ms returns 0
TA260 003:368.046 JLINK_WriteReg(R3, 0x04C11DB7)
TA260 003:368.050 - 0.003ms returns 0
TA260 003:368.054 JLINK_WriteReg(R4, 0x00000000)
TA260 003:368.057 - 0.003ms returns 0
TA260 003:368.061 JLINK_WriteReg(R5, 0x00000000)
TA260 003:368.064 - 0.003ms returns 0
TA260 003:368.068 JLINK_WriteReg(R6, 0x00000000)
TA260 003:368.072 - 0.003ms returns 0
TA260 003:368.076 JLINK_WriteReg(R7, 0x00000000)
TA260 003:368.079 - 0.003ms returns 0
TA260 003:368.083 JLINK_WriteReg(R8, 0x00000000)
TA260 003:368.086 - 0.003ms returns 0
TA260 003:368.091 JLINK_WriteReg(R9, 0x20000180)
TA260 003:368.094 - 0.003ms returns 0
TA260 003:368.098 JLINK_WriteReg(R10, 0x00000000)
TA260 003:368.101 - 0.003ms returns 0
TA260 003:368.105 JLINK_WriteReg(R11, 0x00000000)
TA260 003:368.109 - 0.003ms returns 0
TA260 003:368.113 JLINK_WriteReg(R12, 0x00000000)
TA260 003:368.116 - 0.003ms returns 0
TA260 003:368.120 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:368.124 - 0.003ms returns 0
TA260 003:368.128 JLINK_WriteReg(R14, 0x20000001)
TA260 003:368.131 - 0.003ms returns 0
TA260 003:368.135 JLINK_WriteReg(R15 (PC), 0x20000002)
TA260 003:368.139 - 0.003ms returns 0
TA260 003:368.143 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:368.146 - 0.003ms returns 0
TA260 003:368.150 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:368.153 - 0.003ms returns 0
TA260 003:368.158 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:368.161 - 0.003ms returns 0
TA260 003:368.165 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:368.168 - 0.003ms returns 0
TA260 003:368.172 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:368.176 - 0.003ms returns 0x00000049
TA260 003:368.180 JLINK_Go()
TA260 003:368.187   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:370.870 - 2.690ms 
TA260 003:370.876 JLINK_IsHalted()
TA260 003:371.349 - 0.472ms returns FALSE
TA260 003:371.355 JLINK_HasError()
TA260 003:378.140 JLINK_IsHalted()
TA260 003:378.706 - 0.565ms returns FALSE
TA260 003:378.713 JLINK_HasError()
TA260 003:380.131 JLINK_IsHalted()
TA260 003:380.611 - 0.479ms returns FALSE
TA260 003:380.616 JLINK_HasError()
TA260 003:382.136 JLINK_IsHalted()
TA260 003:382.644 - 0.508ms returns FALSE
TA260 003:382.650 JLINK_HasError()
TA260 003:384.637 JLINK_IsHalted()
TA260 003:385.099 - 0.462ms returns FALSE
TA260 003:385.107 JLINK_HasError()
TA260 003:386.448 JLINK_IsHalted()
TA260 003:386.948 - 0.500ms returns FALSE
TA260 003:386.957 JLINK_HasError()
TA260 003:388.450 JLINK_IsHalted()
TA260 003:388.931 - 0.480ms returns FALSE
TA260 003:388.938 JLINK_HasError()
TA260 003:390.385 JLINK_IsHalted()
TA260 003:390.868 - 0.482ms returns FALSE
TA260 003:390.881 JLINK_HasError()
TA260 003:393.389 JLINK_IsHalted()
TA260 003:393.887 - 0.498ms returns FALSE
TA260 003:393.894 JLINK_HasError()
TA260 003:395.398 JLINK_IsHalted()
TA260 003:395.873 - 0.474ms returns FALSE
TA260 003:395.880 JLINK_HasError()
TA260 003:397.393 JLINK_IsHalted()
TA260 003:397.854 - 0.460ms returns FALSE
TA260 003:397.868 JLINK_HasError()
TA260 003:399.392 JLINK_IsHalted()
TA260 003:399.860 - 0.468ms returns FALSE
TA260 003:399.866 JLINK_HasError()
TA260 003:401.391 JLINK_IsHalted()
TA260 003:401.895 - 0.504ms returns FALSE
TA260 003:401.900 JLINK_HasError()
TA260 003:403.394 JLINK_IsHalted()
TA260 003:403.907 - 0.513ms returns FALSE
TA260 003:403.913 JLINK_HasError()
TA260 003:405.896 JLINK_IsHalted()
TA260 003:406.345 - 0.448ms returns FALSE
TA260 003:406.354 JLINK_HasError()
TA260 003:407.897 JLINK_IsHalted()
TA260 003:408.373 - 0.475ms returns FALSE
TA260 003:408.379 JLINK_HasError()
TA260 003:409.896 JLINK_IsHalted()
TA260 003:410.474 - 0.578ms returns FALSE
TA260 003:410.481 JLINK_HasError()
TA260 003:412.020 JLINK_IsHalted()
TA260 003:412.502 - 0.482ms returns FALSE
TA260 003:412.510 JLINK_HasError()
TA260 003:414.522 JLINK_IsHalted()
TA260 003:414.988 - 0.465ms returns FALSE
TA260 003:414.995 JLINK_HasError()
TA260 003:416.496 JLINK_IsHalted()
TA260 003:416.989 - 0.493ms returns FALSE
TA260 003:416.997 JLINK_HasError()
TA260 003:418.668 JLINK_IsHalted()
TA260 003:419.163 - 0.494ms returns FALSE
TA260 003:419.170 JLINK_HasError()
TA260 003:420.665 JLINK_IsHalted()
TA260 003:421.139 - 0.473ms returns FALSE
TA260 003:421.147 JLINK_HasError()
TA260 003:423.170 JLINK_IsHalted()
TA260 003:423.644 - 0.474ms returns FALSE
TA260 003:423.654 JLINK_HasError()
TA260 003:425.174 JLINK_IsHalted()
TA260 003:425.649 - 0.475ms returns FALSE
TA260 003:425.656 JLINK_HasError()
TA260 003:427.184 JLINK_IsHalted()
TA260 003:427.694 - 0.510ms returns FALSE
TA260 003:427.700 JLINK_HasError()
TA260 003:429.171 JLINK_IsHalted()
TA260 003:429.613 - 0.441ms returns FALSE
TA260 003:429.619 JLINK_HasError()
TA260 003:431.171 JLINK_IsHalted()
TA260 003:431.659 - 0.487ms returns FALSE
TA260 003:431.664 JLINK_HasError()
TA260 003:433.175 JLINK_IsHalted()
TA260 003:433.644 - 0.469ms returns FALSE
TA260 003:433.650 JLINK_HasError()
TA260 003:435.676 JLINK_IsHalted()
TA260 003:436.236 - 0.559ms returns FALSE
TA260 003:436.249 JLINK_HasError()
TA260 003:437.684 JLINK_IsHalted()
TA260 003:438.230 - 0.546ms returns FALSE
TA260 003:438.244 JLINK_HasError()
TA260 003:439.678 JLINK_IsHalted()
TA260 003:440.603 - 0.924ms returns FALSE
TA260 003:440.615 JLINK_HasError()
TA260 003:442.208 JLINK_IsHalted()
TA260 003:442.786 - 0.578ms returns FALSE
TA260 003:442.796 JLINK_HasError()
TA260 003:444.713 JLINK_IsHalted()
TA260 003:445.215 - 0.502ms returns FALSE
TA260 003:445.223 JLINK_HasError()
TA260 003:446.714 JLINK_IsHalted()
TA260 003:447.213 - 0.499ms returns FALSE
TA260 003:447.220 JLINK_HasError()
TA260 003:448.713 JLINK_IsHalted()
TA260 003:449.171 - 0.457ms returns FALSE
TA260 003:449.177 JLINK_HasError()
TA260 003:450.711 JLINK_IsHalted()
TA260 003:451.215 - 0.503ms returns FALSE
TA260 003:451.223 JLINK_HasError()
TA260 003:453.217 JLINK_IsHalted()
TA260 003:453.876 - 0.658ms returns FALSE
TA260 003:453.885 JLINK_HasError()
TA260 003:456.728 JLINK_IsHalted()
TA260 003:457.762 - 1.033ms returns FALSE
TA260 003:457.776 JLINK_HasError()
TA260 003:459.719 JLINK_IsHalted()
TA260 003:460.193 - 0.474ms returns FALSE
TA260 003:460.199 JLINK_HasError()
TA260 003:461.719 JLINK_IsHalted()
TA260 003:462.217 - 0.497ms returns FALSE
TA260 003:462.223 JLINK_HasError()
TA260 003:463.790 JLINK_IsHalted()
TA260 003:464.281 - 0.490ms returns FALSE
TA260 003:464.288 JLINK_HasError()
TA260 003:465.788 JLINK_IsHalted()
TA260 003:466.268 - 0.479ms returns FALSE
TA260 003:466.274 JLINK_HasError()
TA260 003:467.521 JLINK_IsHalted()
TA260 003:468.036 - 0.515ms returns FALSE
TA260 003:468.048 JLINK_HasError()
TA260 003:469.875 JLINK_IsHalted()
TA260 003:470.388 - 0.512ms returns FALSE
TA260 003:470.400 JLINK_HasError()
TA260 003:472.378 JLINK_IsHalted()
TA260 003:472.890 - 0.511ms returns FALSE
TA260 003:472.899 JLINK_HasError()
TA260 003:474.883 JLINK_IsHalted()
TA260 003:475.352 - 0.469ms returns FALSE
TA260 003:475.360 JLINK_HasError()
TA260 003:476.886 JLINK_IsHalted()
TA260 003:477.397 - 0.510ms returns FALSE
TA260 003:477.404 JLINK_HasError()
TA260 003:478.885 JLINK_IsHalted()
TA260 003:479.386 - 0.500ms returns FALSE
TA260 003:479.392 JLINK_HasError()
TA260 003:480.883 JLINK_IsHalted()
TA260 003:481.384 - 0.501ms returns FALSE
TA260 003:481.390 JLINK_HasError()
TA260 003:483.387 JLINK_IsHalted()
TA260 003:483.900 - 0.512ms returns FALSE
TA260 003:483.915 JLINK_HasError()
TA260 003:485.390 JLINK_IsHalted()
TA260 003:485.887 - 0.496ms returns FALSE
TA260 003:485.892 JLINK_HasError()
TA260 003:487.392 JLINK_IsHalted()
TA260 003:487.898 - 0.505ms returns FALSE
TA260 003:487.912 JLINK_HasError()
TA260 003:489.392 JLINK_IsHalted()
TA260 003:490.068 - 0.675ms returns FALSE
TA260 003:490.076 JLINK_HasError()
TA260 003:491.995 JLINK_IsHalted()
TA260 003:492.499 - 0.504ms returns FALSE
TA260 003:492.510 JLINK_HasError()
TA260 003:494.470 JLINK_IsHalted()
TA260 003:494.969 - 0.498ms returns FALSE
TA260 003:494.980 JLINK_HasError()
TA260 003:500.948 JLINK_IsHalted()
TA260 003:501.496 - 0.547ms returns FALSE
TA260 003:501.509 JLINK_HasError()
TA260 003:503.452 JLINK_IsHalted()
TA260 003:503.966 - 0.513ms returns FALSE
TA260 003:503.972 JLINK_HasError()
TA260 003:507.498 JLINK_IsHalted()
TA260 003:508.034 - 0.535ms returns FALSE
TA260 003:508.042 JLINK_HasError()
TA260 003:514.957 JLINK_IsHalted()
TA260 003:515.500 - 0.542ms returns FALSE
TA260 003:515.508 JLINK_HasError()
TA260 003:518.420 JLINK_IsHalted()
TA260 003:518.968 - 0.548ms returns FALSE
TA260 003:518.982 JLINK_HasError()
TA260 003:522.785 JLINK_IsHalted()
TA260 003:523.274 - 0.489ms returns FALSE
TA260 003:523.287 JLINK_HasError()
TA260 003:529.294 JLINK_IsHalted()
TA260 003:529.831 - 0.536ms returns FALSE
TA260 003:529.842 JLINK_HasError()
TA260 003:533.295 JLINK_IsHalted()
TA260 003:533.785 - 0.489ms returns FALSE
TA260 003:533.794 JLINK_HasError()
TA260 003:536.800 JLINK_IsHalted()
TA260 003:537.289 - 0.488ms returns FALSE
TA260 003:537.302 JLINK_HasError()
TA260 003:548.278 JLINK_IsHalted()
TA260 003:548.806 - 0.526ms returns FALSE
TA260 003:548.820 JLINK_HasError()
TA260 003:552.271 JLINK_IsHalted()
TA260 003:552.782 - 0.510ms returns FALSE
TA260 003:552.792 JLINK_HasError()
TA260 003:554.772 JLINK_IsHalted()
TA260 003:555.265 - 0.492ms returns FALSE
TA260 003:555.271 JLINK_HasError()
TA260 003:559.280 JLINK_IsHalted()
TA260 003:559.854 - 0.574ms returns FALSE
TA260 003:559.871 JLINK_HasError()
TA260 003:563.280 JLINK_IsHalted()
TA260 003:563.766 - 0.485ms returns FALSE
TA260 003:563.774 JLINK_HasError()
TA260 003:570.278 JLINK_IsHalted()
TA260 003:570.787 - 0.508ms returns FALSE
TA260 003:570.800 JLINK_HasError()
TA260 003:573.286 JLINK_IsHalted()
TA260 003:573.800 - 0.513ms returns FALSE
TA260 003:573.813 JLINK_HasError()
TA260 003:577.169 JLINK_IsHalted()
TA260 003:577.613 - 0.443ms returns FALSE
TA260 003:577.620 JLINK_HasError()
TA260 003:580.170 JLINK_IsHalted()
TA260 003:580.619 - 0.448ms returns FALSE
TA260 003:580.633 JLINK_HasError()
TA260 003:585.675 JLINK_IsHalted()
TA260 003:586.265 - 0.589ms returns FALSE
TA260 003:586.272 JLINK_HasError()
TA260 003:591.675 JLINK_IsHalted()
TA260 003:592.218 - 0.542ms returns FALSE
TA260 003:592.224 JLINK_HasError()
TA260 003:596.037 JLINK_IsHalted()
TA260 003:596.614 - 0.576ms returns FALSE
TA260 003:596.621 JLINK_HasError()
TA260 003:598.904 JLINK_IsHalted()
TA260 003:599.397 - 0.493ms returns FALSE
TA260 003:599.403 JLINK_HasError()
TA260 003:603.360 JLINK_IsHalted()
TA260 003:603.865 - 0.505ms returns FALSE
TA260 003:603.900 JLINK_HasError()
TA260 003:607.868 JLINK_IsHalted()
TA260 003:608.340 - 0.472ms returns FALSE
TA260 003:608.347 JLINK_HasError()
TA260 003:614.368 JLINK_IsHalted()
TA260 003:614.852 - 0.484ms returns FALSE
TA260 003:614.859 JLINK_HasError()
TA260 003:617.373 JLINK_IsHalted()
TA260 003:617.888 - 0.515ms returns FALSE
TA260 003:617.903 JLINK_HasError()
TA260 003:620.364 JLINK_IsHalted()
TA260 003:620.853 - 0.488ms returns FALSE
TA260 003:620.864 JLINK_HasError()
TA260 003:623.372 JLINK_IsHalted()
TA260 003:623.887 - 0.514ms returns FALSE
TA260 003:623.898 JLINK_HasError()
TA260 003:627.320 JLINK_IsHalted()
TA260 003:627.789 - 0.469ms returns FALSE
TA260 003:627.802 JLINK_HasError()
TA260 003:630.532 JLINK_IsHalted()
TA260 003:631.023 - 0.490ms returns FALSE
TA260 003:631.036 JLINK_HasError()
TA260 003:634.611 JLINK_IsHalted()
TA260 003:635.093 - 0.481ms returns FALSE
TA260 003:635.104 JLINK_HasError()
TA260 003:638.357 JLINK_IsHalted()
TA260 003:638.841 - 0.483ms returns FALSE
TA260 003:638.849 JLINK_HasError()
TA260 003:641.358 JLINK_IsHalted()
TA260 003:641.810 - 0.451ms returns FALSE
TA260 003:641.817 JLINK_HasError()
TA260 003:649.862 JLINK_IsHalted()
TA260 003:650.388 - 0.524ms returns FALSE
TA260 003:650.394 JLINK_HasError()
TA260 003:654.554 JLINK_IsHalted()
TA260 003:655.035 - 0.481ms returns FALSE
TA260 003:655.043 JLINK_HasError()
TA260 003:658.756 JLINK_IsHalted()
TA260 003:659.242 - 0.485ms returns FALSE
TA260 003:659.249 JLINK_HasError()
TA260 003:660.754 JLINK_IsHalted()
TA260 003:661.237 - 0.482ms returns FALSE
TA260 003:661.248 JLINK_HasError()
TA260 003:663.256 JLINK_IsHalted()
TA260 003:663.749 - 0.492ms returns FALSE
TA260 003:663.756 JLINK_HasError()
TA260 003:667.263 JLINK_IsHalted()
TA260 003:667.724 - 0.460ms returns FALSE
TA260 003:667.739 JLINK_HasError()
TA260 003:671.261 JLINK_IsHalted()
TA260 003:671.780 - 0.519ms returns FALSE
TA260 003:671.790 JLINK_HasError()
TA260 003:676.768 JLINK_IsHalted()
TA260 003:677.257 - 0.488ms returns FALSE
TA260 003:677.273 JLINK_HasError()
TA260 003:681.768 JLINK_IsHalted()
TA260 003:682.230 - 0.460ms returns FALSE
TA260 003:682.244 JLINK_HasError()
TA260 003:686.272 JLINK_IsHalted()
TA260 003:686.930 - 0.657ms returns FALSE
TA260 003:686.950 JLINK_HasError()
TA260 003:692.274 JLINK_IsHalted()
TA260 003:692.789 - 0.514ms returns FALSE
TA260 003:692.806 JLINK_HasError()
TA260 003:695.780 JLINK_IsHalted()
TA260 003:696.306 - 0.525ms returns FALSE
TA260 003:696.321 JLINK_HasError()
TA260 003:702.106 JLINK_IsHalted()
TA260 003:704.524   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:705.100 - 2.994ms returns TRUE
TA260 003:705.117 JLINK_ReadReg(R15 (PC))
TA260 003:705.124 - 0.006ms returns 0x20000000
TA260 003:705.128 JLINK_ClrBPEx(BPHandle = 0x00000049)
TA260 003:705.132 - 0.004ms returns 0x00
TA260 003:705.137 JLINK_ReadReg(R0)
TA260 003:705.140 - 0.003ms returns 0x1D8F0A2E
TA260 003:707.023 JLINK_HasError()
TA260 003:707.048 JLINK_WriteReg(R0, 0x00000003)
TA260 003:707.054 - 0.005ms returns 0
TA260 003:707.059 JLINK_WriteReg(R1, 0x08000000)
TA260 003:707.062 - 0.003ms returns 0
TA260 003:707.067 JLINK_WriteReg(R2, 0x0000E8D8)
TA260 003:707.070 - 0.003ms returns 0
TA260 003:707.074 JLINK_WriteReg(R3, 0x04C11DB7)
TA260 003:707.084 - 0.009ms returns 0
TA260 003:707.090 JLINK_WriteReg(R4, 0x00000000)
TA260 003:707.094 - 0.003ms returns 0
TA260 003:707.098 JLINK_WriteReg(R5, 0x00000000)
TA260 003:707.102 - 0.003ms returns 0
TA260 003:707.106 JLINK_WriteReg(R6, 0x00000000)
TA260 003:707.109 - 0.003ms returns 0
TA260 003:707.114 JLINK_WriteReg(R7, 0x00000000)
TA260 003:707.117 - 0.003ms returns 0
TA260 003:707.121 JLINK_WriteReg(R8, 0x00000000)
TA260 003:707.125 - 0.004ms returns 0
TA260 003:707.130 JLINK_WriteReg(R9, 0x20000180)
TA260 003:707.133 - 0.003ms returns 0
TA260 003:707.137 JLINK_WriteReg(R10, 0x00000000)
TA260 003:707.140 - 0.003ms returns 0
TA260 003:707.145 JLINK_WriteReg(R11, 0x00000000)
TA260 003:707.148 - 0.003ms returns 0
TA260 003:707.152 JLINK_WriteReg(R12, 0x00000000)
TA260 003:707.156 - 0.003ms returns 0
TA260 003:707.160 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:707.164 - 0.004ms returns 0
TA260 003:707.168 JLINK_WriteReg(R14, 0x20000001)
TA260 003:707.172 - 0.003ms returns 0
TA260 003:707.176 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 003:707.180 - 0.003ms returns 0
TA260 003:707.184 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:707.188 - 0.003ms returns 0
TA260 003:707.192 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:707.195 - 0.003ms returns 0
TA260 003:707.200 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:707.204 - 0.003ms returns 0
TA260 003:707.208 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:707.211 - 0.003ms returns 0
TA260 003:707.216 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:707.220 - 0.004ms returns 0x0000004A
TA260 003:707.224 JLINK_Go()
TA260 003:707.238   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:710.103 - 2.876ms 
TA260 003:710.127 JLINK_IsHalted()
TA260 003:712.513   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:713.042 - 2.915ms returns TRUE
TA260 003:713.058 JLINK_ReadReg(R15 (PC))
TA260 003:713.064 - 0.006ms returns 0x20000000
TA260 003:713.104 JLINK_ClrBPEx(BPHandle = 0x0000004A)
TA260 003:713.118 - 0.013ms returns 0x00
TA260 003:713.123 JLINK_ReadReg(R0)
TA260 003:713.127 - 0.004ms returns 0x00000000
TA260 003:767.365 JLINK_WriteMemEx(0x20000000, 0x00000002 Bytes, Flags = 0x02000000)
TA260 003:767.392   Data:  FE E7
TA260 003:767.411   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 003:767.976 - 0.610ms returns 0x2
TA260 003:767.993 JLINK_HasError()
TA260 003:771.325 JLINK_Close()
TA260 003:773.590   OnDisconnectTarget() start
TA260 003:773.614    J-Link Script File: Executing OnDisconnectTarget()
TA260 003:773.628   CPU_WriteMem(4 bytes @ 0xE0042004)
TA260 003:774.100   CPU_WriteMem(4 bytes @ 0xE0042008)
TA260 003:776.353   OnDisconnectTarget() end - Took 1.00ms
TA260 003:776.375   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:796.043 - 24.717ms
TA260 003:796.072   
TA260 003:796.076   Closed
