TA260 000:004.347   SEGGER J-Link V8.16 Log File
TA260 000:004.467   DLL Compiled: Feb 26 2025 12:07:26
TA260 000:004.472   Logging started @ 2025-08-02 09:06
TA260 000:004.476   Process: G:\keil\keil arm\UV4\UV4.exe
TA260 000:004.487 - 4.479ms 
TA260 000:004.518 JLINK_SetWarnOutHandler(...)
TA260 000:004.523 - 0.006ms 
TA260 000:004.531 JLINK_OpenEx(...)
TA260 000:008.973   Firmware: J-Link V9 compiled May  7 2021 16:26:12
TA260 000:010.360   Firmware: J-Link V9 compiled May  7 2021 16:26:12
TA260 000:010.555   Decompressing FW timestamp took 142 us
TA260 000:018.291   Hardware: V9.60
TA260 000:018.315   S/N: 69655018
TA260 000:018.320   OEM: SEGGER
TA260 000:018.325   Feature(s): RDI, GDB, FlashD<PERSON>, FlashB<PERSON>, JFlash
TA260 000:019.541   Bootloader: (FW returned invalid version)
TA260 000:021.083   TELNET listener socket opened on port 19021
TA260 000:021.166   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
TA260 000:021.294   WEBSRV Webserver running on local port 19080
TA260 000:021.362   Looking for J-Link GUI Server exe at: G:\keil\keil arm\ARM\Segger\JLinkGUIServer.exe
TA260 000:021.449   Looking for J-Link GUI Server exe at: C:\Program Files (x86)\SEGGER\JLink_V630d\\JLinkGUIServer.exe
TA260 000:325.394   Failed to connect to J-Link GUI Server.
TA260 000:325.420 - 320.884ms returns "O.K."
TA260 000:325.436 JLINK_GetEmuCaps()
TA260 000:325.441 - 0.004ms returns 0xB9FF7BBF
TA260 000:325.452 JLINK_TIF_GetAvailable(...)
TA260 000:325.826 - 0.375ms 
TA260 000:325.849 JLINK_SetErrorOutHandler(...)
TA260 000:325.853 - 0.004ms 
TA260 000:325.881 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\2025ele_ori\zuolan_stm32\MDK-ARM\JLinkSettings.ini"", ...). 
TA260 000:337.279 - 11.396ms returns 0x00
TA260 000:342.857 JLINK_ExecCommand("Device = STM32F429IGTx", ...). 
TA260 000:344.160   Flash bank @ 0x90000000: SFL: Parsing sectorization info from ELF file
TA260 000:344.187     FlashDevice.SectorInfo[0]: .SectorSize = 0x00010000, .SectorStartAddr = 0x00000000
TA260 000:350.284   Device "STM32F429IG" selected.
TA260 000:350.489 - 7.611ms returns 0x00
TA260 000:350.501 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
TA260 000:350.517   ERROR: Unknown command
TA260 000:350.524 - 0.017ms returns 0x01
TA260 000:350.529 JLINK_GetHardwareVersion()
TA260 000:350.533 - 0.004ms returns 96000
TA260 000:350.537 JLINK_GetDLLVersion()
TA260 000:350.540 - 0.003ms returns 81600
TA260 000:350.545 JLINK_GetOEMString(...)
TA260 000:350.549 JLINK_GetFirmwareString(...)
TA260 000:350.553 - 0.003ms 
TA260 000:359.625 JLINK_GetDLLVersion()
TA260 000:359.642 - 0.016ms returns 81600
TA260 000:359.648 JLINK_GetCompileDateTime()
TA260 000:359.651 - 0.003ms 
TA260 000:363.018 JLINK_GetFirmwareString(...)
TA260 000:363.035 - 0.016ms 
TA260 000:366.954 JLINK_GetHardwareVersion()
TA260 000:366.975 - 0.020ms returns 96000
TA260 000:369.544 JLINK_GetSN()
TA260 000:369.560 - 0.015ms returns 69655018
TA260 000:371.993 JLINK_GetOEMString(...)
TA260 000:376.186 JLINK_TIF_Select(JLINKARM_TIF_SWD)
TA260 000:377.650 - 1.465ms returns 0x00
TA260 000:377.672 JLINK_HasError()
TA260 000:377.691 JLINK_SetSpeed(5000)
TA260 000:378.008 - 0.318ms 
TA260 000:378.016 JLINK_GetId()
TA260 000:380.871   InitTarget() start
TA260 000:380.904    J-Link Script File: Executing InitTarget()
TA260 000:389.268   SWD selected. Executing JTAG -> SWD switching sequence.
TA260 000:395.478   DAP initialized successfully.
TA260 000:409.585   InitTarget() end - Took 25.3ms
TA260 000:413.075   Found SW-DP with ID 0x2BA01477
TA260 000:419.337   DPIDR: 0x2BA01477
TA260 000:421.838   CoreSight SoC-400 or earlier
TA260 000:424.880   Scanning AP map to find all available APs
TA260 000:428.625   AP[1]: Stopped AP scan as end of AP map has been reached
TA260 000:431.293   AP[0]: AHB-AP (IDR: 0x24770011, ADDR: 0x00000000)
TA260 000:433.808   Iterating through AP map to find AHB-AP to use
TA260 000:438.475   AP[0]: Core found
TA260 000:441.373   AP[0]: AHB-AP ROM base: 0xE00FF000
TA260 000:443.683   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
TA260 000:445.426   Found Cortex-M4 r0p1, Little endian.
TA260 000:446.318   -- Max. mem block: 0x00010C40
TA260 000:447.127   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:447.645   CPU_ReadMem(4 bytes @ 0x********)
TA260 000:450.312   FPUnit: 6 code (BP) slots and 2 literal slots
TA260 000:450.338   CPU_ReadMem(4 bytes @ 0xE000EDFC)
TA260 000:450.827   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:451.360   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:451.858   CPU_WriteMem(4 bytes @ 0xE0001000)
TA260 000:452.358   CPU_ReadMem(4 bytes @ 0xE000ED88)
TA260 000:452.848   CPU_WriteMem(4 bytes @ 0xE000ED88)
TA260 000:453.382   CPU_ReadMem(4 bytes @ 0xE000ED88)
TA260 000:453.849   CPU_WriteMem(4 bytes @ 0xE000ED88)
TA260 000:457.319   CoreSight components:
TA260 000:459.058   ROMTbl[0] @ E00FF000
TA260 000:459.093   CPU_ReadMem(64 bytes @ 0xE00FF000)
TA260 000:459.912   CPU_ReadMem(32 bytes @ 0xE000EFE0)
TA260 000:462.278   [0][0]: E000E000 CID B105E00D PID 000BB00C SCS-M7
TA260 000:462.317   CPU_ReadMem(32 bytes @ 0xE0001FE0)
TA260 000:465.800   [0][1]: E0001000 CID B105E00D PID 003BB002 DWT
TA260 000:465.831   CPU_ReadMem(32 bytes @ 0xE0002FE0)
TA260 000:469.088   [0][2]: ******** CID B105E00D PID 002BB003 FPB
TA260 000:469.116   CPU_ReadMem(32 bytes @ 0xE0000FE0)
TA260 000:472.143   [0][3]: ******** CID B105E00D PID 003BB001 ITM
TA260 000:472.170   CPU_ReadMem(32 bytes @ 0xE0040FE0)
TA260 000:474.872   [0][4]: ******** CID B105900D PID 000BB9A1 TPIU
TA260 000:474.897   CPU_ReadMem(32 bytes @ 0xE0041FE0)
TA260 000:478.179   [0][5]: ******** CID B105900D PID 000BB925 ETM
TA260 000:478.698 - 100.682ms returns 0x2BA01477
TA260 000:478.759 JLINK_GetDLLVersion()
TA260 000:478.768 - 0.008ms returns 81600
TA260 000:478.780 JLINK_CORE_GetFound()
TA260 000:478.784 - 0.004ms returns 0xE0000FF
TA260 000:478.789 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
TA260 000:478.795   Value=0xE00FF000
TA260 000:478.801 - 0.012ms returns 0
TA260 000:480.967 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
TA260 000:480.988   Value=0xE00FF000
TA260 000:480.994 - 0.027ms returns 0
TA260 000:480.999 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
TA260 000:481.002   Value=0x********
TA260 000:481.007 - 0.008ms returns 0
TA260 000:481.013 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
TA260 000:481.047   CPU_ReadMem(32 bytes @ 0xE0041FD0)
TA260 000:481.676   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
TA260 000:481.684 - 0.671ms returns 32 (0x20)
TA260 000:481.690 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
TA260 000:481.694   Value=0x00000000
TA260 000:481.699 - 0.009ms returns 0
TA260 000:481.703 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
TA260 000:481.707   Value=0x********
TA260 000:481.711 - 0.008ms returns 0
TA260 000:481.716 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
TA260 000:481.719   Value=0x********
TA260 000:481.724 - 0.008ms returns 0
TA260 000:481.728 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
TA260 000:481.732   Value=0xE0001000
TA260 000:481.737 - 0.008ms returns 0
TA260 000:481.741 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
TA260 000:481.744   Value=0x********
TA260 000:481.750 - 0.008ms returns 0
TA260 000:481.754 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
TA260 000:481.757   Value=0xE000E000
TA260 000:481.762 - 0.008ms returns 0
TA260 000:481.766 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
TA260 000:481.769   Value=0xE000EDF0
TA260 000:481.774 - 0.008ms returns 0
TA260 000:481.779 JLINK_GetDebugInfo(0x01 = Unknown)
TA260 000:481.782   Value=0x00000001
TA260 000:481.787 - 0.008ms returns 0
TA260 000:481.791 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
TA260 000:481.797   CPU_ReadMem(4 bytes @ 0xE000ED00)
TA260 000:482.287   Data:  41 C2 0F 41
TA260 000:482.302   Debug reg: CPUID
TA260 000:482.309 - 0.517ms returns 1 (0x1)
TA260 000:482.328 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
TA260 000:482.336   Value=0x00000000
TA260 000:482.341 - 0.013ms returns 0
TA260 000:482.346 JLINK_HasError()
TA260 000:482.352 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
TA260 000:482.356 - 0.004ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
TA260 000:482.360 JLINK_Reset()
TA260 000:482.367   JLINK_GetResetTypeDesc
TA260 000:482.371   - 0.003ms 
TA260 000:484.710   Reset type: NORMAL (https://wiki.segger.com/J-Link_Reset_Strategies)
TA260 000:484.745   CPU is running
TA260 000:484.757   CPU_WriteMem(4 bytes @ 0xE000EDF0)
TA260 000:485.244   CPU is running
TA260 000:485.256   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:489.085   Reset: Halt core after reset via DEMCR.VC_CORERESET.
TA260 000:492.589   Reset: Reset device via AIRCR.SYSRESETREQ.
TA260 000:492.611   CPU is running
TA260 000:492.621   CPU_WriteMem(4 bytes @ 0xE000ED0C)
TA260 000:546.413   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:546.914   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:549.741   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:555.921   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:558.777   CPU_WriteMem(4 bytes @ 0x********)
TA260 000:559.257   CPU_ReadMem(4 bytes @ 0xE000EDFC)
TA260 000:559.744   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:560.228 - 77.868ms 
TA260 000:560.274 JLINK_Halt()
TA260 000:560.279 - 0.004ms returns 0x00
TA260 000:560.284 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
TA260 000:560.292   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:560.789   Data:  03 00 03 00
TA260 000:560.796   Debug reg: DHCSR
TA260 000:560.802 - 0.518ms returns 1 (0x1)
TA260 000:560.808 JLINK_WriteU32(0xE000EDF0, 0xA05F0003)
TA260 000:560.812   Debug reg: DHCSR
TA260 000:561.047   CPU_WriteMem(4 bytes @ 0xE000EDF0)
TA260 000:561.552 - 0.744ms returns 0 (0x00000000)
TA260 000:561.558 JLINK_WriteU32(0xE000EDFC, 0x01000000)
TA260 000:561.562   Debug reg: DEMCR
TA260 000:561.569   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:562.063 - 0.504ms returns 0 (0x00000000)
TA260 000:568.685 JLINK_GetHWStatus(...)
TA260 000:569.069 - 0.383ms returns 0
TA260 000:573.074 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
TA260 000:573.088 - 0.013ms returns 0x06
TA260 000:573.093 JLINK_GetNumBPUnits(Type = 0xF0)
TA260 000:573.097 - 0.003ms returns 0x2000
TA260 000:573.101 JLINK_GetNumWPUnits()
TA260 000:573.104 - 0.003ms returns 4
TA260 000:577.586 JLINK_GetSpeed()
TA260 000:577.603 - 0.017ms returns 4000
TA260 000:580.482 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
TA260 000:580.505   CPU_ReadMem(4 bytes @ 0xE000E004)
TA260 000:580.965   Data:  02 00 00 00
TA260 000:580.975 - 0.492ms returns 1 (0x1)
TA260 000:580.981 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
TA260 000:580.989   CPU_ReadMem(4 bytes @ 0xE000E004)
TA260 000:581.506   Data:  02 00 00 00
TA260 000:581.519 - 0.531ms returns 1 (0x1)
TA260 000:581.525 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
TA260 000:581.528   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
TA260 000:581.538   CPU_WriteMem(28 bytes @ 0xE0001000)
TA260 000:582.107 - 0.582ms returns 0x1C
TA260 000:582.115 JLINK_Halt()
TA260 000:582.119 - 0.003ms returns 0x00
TA260 000:582.123 JLINK_IsHalted()
TA260 000:582.127 - 0.004ms returns TRUE
TA260 000:584.286 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 000:584.294   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 000:584.509   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 000:586.348 - 2.061ms returns 0x184
TA260 000:586.398 JLINK_HasError()
TA260 000:586.404 JLINK_WriteReg(R0, 0x08000000)
TA260 000:586.410 - 0.005ms returns 0
TA260 000:586.414 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 000:586.418 - 0.003ms returns 0
TA260 000:586.422 JLINK_WriteReg(R2, 0x00000001)
TA260 000:586.426 - 0.003ms returns 0
TA260 000:586.430 JLINK_WriteReg(R3, 0x00000000)
TA260 000:586.433 - 0.003ms returns 0
TA260 000:586.438 JLINK_WriteReg(R4, 0x00000000)
TA260 000:586.441 - 0.003ms returns 0
TA260 000:586.445 JLINK_WriteReg(R5, 0x00000000)
TA260 000:586.452 - 0.007ms returns 0
TA260 000:586.457 JLINK_WriteReg(R6, 0x00000000)
TA260 000:586.461 - 0.003ms returns 0
TA260 000:586.465 JLINK_WriteReg(R7, 0x00000000)
TA260 000:586.468 - 0.003ms returns 0
TA260 000:586.515 JLINK_WriteReg(R8, 0x00000000)
TA260 000:586.519 - 0.047ms returns 0
TA260 000:586.523 JLINK_WriteReg(R9, 0x20000180)
TA260 000:586.527 - 0.003ms returns 0
TA260 000:586.531 JLINK_WriteReg(R10, 0x00000000)
TA260 000:586.535 - 0.003ms returns 0
TA260 000:586.539 JLINK_WriteReg(R11, 0x00000000)
TA260 000:586.560 - 0.021ms returns 0
TA260 000:586.565 JLINK_WriteReg(R12, 0x00000000)
TA260 000:586.568 - 0.003ms returns 0
TA260 000:586.572 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:586.577 - 0.004ms returns 0
TA260 000:586.581 JLINK_WriteReg(R14, 0x20000001)
TA260 000:586.584 - 0.003ms returns 0
TA260 000:586.593 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 000:586.597 - 0.008ms returns 0
TA260 000:586.699 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:586.703 - 0.004ms returns 0
TA260 000:586.752 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:586.756 - 0.003ms returns 0
TA260 000:586.760 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:586.763 - 0.003ms returns 0
TA260 000:586.767 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:586.771 - 0.003ms returns 0
TA260 000:586.775 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:586.783   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:587.236 - 0.461ms returns 0x00000001
TA260 000:587.244 JLINK_Go()
TA260 000:587.249   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 000:587.789   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:588.316   CPU_WriteMem(4 bytes @ 0xE0002008)
TA260 000:588.322   CPU_WriteMem(4 bytes @ 0xE000200C)
TA260 000:588.327   CPU_WriteMem(4 bytes @ 0xE0002010)
TA260 000:588.332   CPU_WriteMem(4 bytes @ 0xE0002014)
TA260 000:588.337   CPU_WriteMem(4 bytes @ 0xE0002018)
TA260 000:588.342   CPU_WriteMem(4 bytes @ 0xE000201C)
TA260 000:589.631   CPU_WriteMem(4 bytes @ 0xE0001004)
TA260 000:593.994   Memory map 'after startup completion point' is active
TA260 000:594.008 - 6.763ms 
TA260 000:594.015 JLINK_IsHalted()
TA260 000:596.335   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:596.864 - 2.848ms returns TRUE
TA260 000:596.875 JLINK_ReadReg(R15 (PC))
TA260 000:596.882 - 0.007ms returns 0x20000000
TA260 000:596.887 JLINK_ClrBPEx(BPHandle = 0x00000001)
TA260 000:596.891 - 0.004ms returns 0x00
TA260 000:596.895 JLINK_ReadReg(R0)
TA260 000:596.899 - 0.003ms returns 0x00000000
TA260 000:597.128 JLINK_HasError()
TA260 000:597.136 JLINK_WriteReg(R0, 0x08000000)
TA260 000:597.141 - 0.004ms returns 0
TA260 000:597.145 JLINK_WriteReg(R1, 0x00004000)
TA260 000:597.149 - 0.003ms returns 0
TA260 000:597.153 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:597.156 - 0.003ms returns 0
TA260 000:597.160 JLINK_WriteReg(R3, 0x00000000)
TA260 000:597.163 - 0.003ms returns 0
TA260 000:597.167 JLINK_WriteReg(R4, 0x00000000)
TA260 000:597.171 - 0.003ms returns 0
TA260 000:597.175 JLINK_WriteReg(R5, 0x00000000)
TA260 000:597.179 - 0.003ms returns 0
TA260 000:597.183 JLINK_WriteReg(R6, 0x00000000)
TA260 000:597.186 - 0.003ms returns 0
TA260 000:597.190 JLINK_WriteReg(R7, 0x00000000)
TA260 000:597.193 - 0.003ms returns 0
TA260 000:597.197 JLINK_WriteReg(R8, 0x00000000)
TA260 000:597.201 - 0.003ms returns 0
TA260 000:597.205 JLINK_WriteReg(R9, 0x20000180)
TA260 000:597.208 - 0.003ms returns 0
TA260 000:597.212 JLINK_WriteReg(R10, 0x00000000)
TA260 000:597.216 - 0.003ms returns 0
TA260 000:597.220 JLINK_WriteReg(R11, 0x00000000)
TA260 000:597.223 - 0.003ms returns 0
TA260 000:597.227 JLINK_WriteReg(R12, 0x00000000)
TA260 000:597.231 - 0.003ms returns 0
TA260 000:597.235 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:597.238 - 0.003ms returns 0
TA260 000:597.243 JLINK_WriteReg(R14, 0x20000001)
TA260 000:597.246 - 0.003ms returns 0
TA260 000:597.250 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 000:597.253 - 0.003ms returns 0
TA260 000:597.257 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:597.261 - 0.003ms returns 0
TA260 000:597.304 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:597.307 - 0.003ms returns 0
TA260 000:597.311 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:597.315 - 0.003ms returns 0
TA260 000:597.319 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:597.322 - 0.003ms returns 0
TA260 000:597.327 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:597.331 - 0.004ms returns 0x00000002
TA260 000:597.335 JLINK_Go()
TA260 000:597.344   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:600.124 - 2.788ms 
TA260 000:600.140 JLINK_IsHalted()
TA260 000:602.524   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:602.996 - 2.855ms returns TRUE
TA260 000:603.002 JLINK_ReadReg(R15 (PC))
TA260 000:603.007 - 0.004ms returns 0x20000000
TA260 000:603.011 JLINK_ClrBPEx(BPHandle = 0x00000002)
TA260 000:603.015 - 0.003ms returns 0x00
TA260 000:603.019 JLINK_ReadReg(R0)
TA260 000:603.023 - 0.003ms returns 0x00000001
TA260 000:603.027 JLINK_HasError()
TA260 000:603.032 JLINK_WriteReg(R0, 0x08000000)
TA260 000:603.035 - 0.003ms returns 0
TA260 000:603.039 JLINK_WriteReg(R1, 0x00004000)
TA260 000:603.043 - 0.003ms returns 0
TA260 000:603.047 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:603.051 - 0.003ms returns 0
TA260 000:603.055 JLINK_WriteReg(R3, 0x00000000)
TA260 000:603.058 - 0.003ms returns 0
TA260 000:603.062 JLINK_WriteReg(R4, 0x00000000)
TA260 000:603.066 - 0.003ms returns 0
TA260 000:603.070 JLINK_WriteReg(R5, 0x00000000)
TA260 000:603.073 - 0.003ms returns 0
TA260 000:603.077 JLINK_WriteReg(R6, 0x00000000)
TA260 000:603.080 - 0.003ms returns 0
TA260 000:603.085 JLINK_WriteReg(R7, 0x00000000)
TA260 000:603.088 - 0.003ms returns 0
TA260 000:603.092 JLINK_WriteReg(R8, 0x00000000)
TA260 000:603.096 - 0.003ms returns 0
TA260 000:603.100 JLINK_WriteReg(R9, 0x20000180)
TA260 000:603.103 - 0.003ms returns 0
TA260 000:603.107 JLINK_WriteReg(R10, 0x00000000)
TA260 000:603.110 - 0.003ms returns 0
TA260 000:603.114 JLINK_WriteReg(R11, 0x00000000)
TA260 000:603.118 - 0.003ms returns 0
TA260 000:603.122 JLINK_WriteReg(R12, 0x00000000)
TA260 000:603.125 - 0.003ms returns 0
TA260 000:603.129 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:603.133 - 0.003ms returns 0
TA260 000:603.137 JLINK_WriteReg(R14, 0x20000001)
TA260 000:603.140 - 0.003ms returns 0
TA260 000:603.145 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 000:603.148 - 0.003ms returns 0
TA260 000:603.152 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:603.155 - 0.003ms returns 0
TA260 000:603.159 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:603.163 - 0.003ms returns 0
TA260 000:603.167 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:603.170 - 0.003ms returns 0
TA260 000:603.174 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:603.178 - 0.003ms returns 0
TA260 000:603.182 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:603.186 - 0.003ms returns 0x00000003
TA260 000:603.190 JLINK_Go()
TA260 000:603.196   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:605.896 - 2.706ms 
TA260 000:605.906 JLINK_IsHalted()
TA260 000:606.387 - 0.481ms returns FALSE
TA260 000:606.400 JLINK_HasError()
TA260 000:616.013 JLINK_IsHalted()
TA260 000:616.520 - 0.507ms returns FALSE
TA260 000:616.527 JLINK_HasError()
TA260 000:618.011 JLINK_IsHalted()
TA260 000:618.494 - 0.482ms returns FALSE
TA260 000:618.501 JLINK_HasError()
TA260 000:620.107 JLINK_IsHalted()
TA260 000:620.598 - 0.490ms returns FALSE
TA260 000:620.604 JLINK_HasError()
TA260 000:622.107 JLINK_IsHalted()
TA260 000:622.610 - 0.503ms returns FALSE
TA260 000:622.616 JLINK_HasError()
TA260 000:624.108 JLINK_IsHalted()
TA260 000:624.611 - 0.502ms returns FALSE
TA260 000:624.618 JLINK_HasError()
TA260 000:626.621 JLINK_IsHalted()
TA260 000:627.104 - 0.482ms returns FALSE
TA260 000:627.117 JLINK_HasError()
TA260 000:628.616 JLINK_IsHalted()
TA260 000:629.088 - 0.471ms returns FALSE
TA260 000:629.094 JLINK_HasError()
TA260 000:630.614 JLINK_IsHalted()
TA260 000:631.087 - 0.473ms returns FALSE
TA260 000:631.093 JLINK_HasError()
TA260 000:632.614 JLINK_IsHalted()
TA260 000:633.105 - 0.491ms returns FALSE
TA260 000:633.114 JLINK_HasError()
TA260 000:635.118 JLINK_IsHalted()
TA260 000:635.611 - 0.492ms returns FALSE
TA260 000:635.623 JLINK_HasError()
TA260 000:637.086 JLINK_IsHalted()
TA260 000:637.536 - 0.449ms returns FALSE
TA260 000:637.547 JLINK_HasError()
TA260 000:639.078 JLINK_IsHalted()
TA260 000:639.551 - 0.472ms returns FALSE
TA260 000:639.556 JLINK_HasError()
TA260 000:641.082 JLINK_IsHalted()
TA260 000:641.612 - 0.530ms returns FALSE
TA260 000:641.620 JLINK_HasError()
TA260 000:643.081 JLINK_IsHalted()
TA260 000:643.610 - 0.528ms returns FALSE
TA260 000:643.616 JLINK_HasError()
TA260 000:645.083 JLINK_IsHalted()
TA260 000:645.513 - 0.430ms returns FALSE
TA260 000:645.520 JLINK_HasError()
TA260 000:646.595 JLINK_IsHalted()
TA260 000:647.066 - 0.471ms returns FALSE
TA260 000:647.072 JLINK_HasError()
TA260 000:648.592 JLINK_IsHalted()
TA260 000:649.109 - 0.516ms returns FALSE
TA260 000:649.115 JLINK_HasError()
TA260 000:650.587 JLINK_IsHalted()
TA260 000:651.107 - 0.520ms returns FALSE
TA260 000:651.112 JLINK_HasError()
TA260 000:652.295 JLINK_IsHalted()
TA260 000:652.790 - 0.495ms returns FALSE
TA260 000:652.795 JLINK_HasError()
TA260 000:654.799 JLINK_IsHalted()
TA260 000:655.285 - 0.486ms returns FALSE
TA260 000:655.291 JLINK_HasError()
TA260 000:656.808 JLINK_IsHalted()
TA260 000:657.342 - 0.534ms returns FALSE
TA260 000:657.356 JLINK_HasError()
TA260 000:658.809 JLINK_IsHalted()
TA260 000:659.248 - 0.438ms returns FALSE
TA260 000:659.259 JLINK_HasError()
TA260 000:660.809 JLINK_IsHalted()
TA260 000:661.352 - 0.542ms returns FALSE
TA260 000:661.362 JLINK_HasError()
TA260 000:662.808 JLINK_IsHalted()
TA260 000:663.343 - 0.535ms returns FALSE
TA260 000:663.360 JLINK_HasError()
TA260 000:664.810 JLINK_IsHalted()
TA260 000:665.342 - 0.532ms returns FALSE
TA260 000:665.355 JLINK_HasError()
TA260 000:667.324 JLINK_IsHalted()
TA260 000:667.804 - 0.480ms returns FALSE
TA260 000:667.820 JLINK_HasError()
TA260 000:669.328 JLINK_IsHalted()
TA260 000:669.804 - 0.475ms returns FALSE
TA260 000:669.811 JLINK_HasError()
TA260 000:671.321 JLINK_IsHalted()
TA260 000:671.781 - 0.459ms returns FALSE
TA260 000:671.795 JLINK_HasError()
TA260 000:673.320 JLINK_IsHalted()
TA260 000:673.843 - 0.523ms returns FALSE
TA260 000:673.853 JLINK_HasError()
TA260 000:675.827 JLINK_IsHalted()
TA260 000:676.395 - 0.567ms returns FALSE
TA260 000:676.408 JLINK_HasError()
TA260 000:677.828 JLINK_IsHalted()
TA260 000:678.359 - 0.530ms returns FALSE
TA260 000:678.367 JLINK_HasError()
TA260 000:679.833 JLINK_IsHalted()
TA260 000:680.278 - 0.444ms returns FALSE
TA260 000:680.290 JLINK_HasError()
TA260 000:681.828 JLINK_IsHalted()
TA260 000:682.330 - 0.501ms returns FALSE
TA260 000:682.339 JLINK_HasError()
TA260 000:683.829 JLINK_IsHalted()
TA260 000:684.284 - 0.454ms returns FALSE
TA260 000:684.296 JLINK_HasError()
TA260 000:686.019 JLINK_IsHalted()
TA260 000:686.475 - 0.455ms returns FALSE
TA260 000:686.488 JLINK_HasError()
TA260 000:688.023 JLINK_IsHalted()
TA260 000:688.473 - 0.449ms returns FALSE
TA260 000:688.488 JLINK_HasError()
TA260 000:690.018 JLINK_IsHalted()
TA260 000:690.554 - 0.535ms returns FALSE
TA260 000:690.566 JLINK_HasError()
TA260 000:692.021 JLINK_IsHalted()
TA260 000:692.474 - 0.453ms returns FALSE
TA260 000:692.485 JLINK_HasError()
TA260 000:694.019 JLINK_IsHalted()
TA260 000:694.508 - 0.488ms returns FALSE
TA260 000:694.516 JLINK_HasError()
TA260 000:696.533 JLINK_IsHalted()
TA260 000:697.106 - 0.572ms returns FALSE
TA260 000:697.117 JLINK_HasError()
TA260 000:699.538 JLINK_IsHalted()
TA260 000:700.089 - 0.551ms returns FALSE
TA260 000:700.102 JLINK_HasError()
TA260 000:701.537 JLINK_IsHalted()
TA260 000:701.986 - 0.448ms returns FALSE
TA260 000:701.998 JLINK_HasError()
TA260 000:703.533 JLINK_IsHalted()
TA260 000:703.988 - 0.454ms returns FALSE
TA260 000:703.999 JLINK_HasError()
TA260 000:705.040 JLINK_IsHalted()
TA260 000:705.464 - 0.423ms returns FALSE
TA260 000:705.475 JLINK_HasError()
TA260 000:706.546 JLINK_IsHalted()
TA260 000:706.988 - 0.442ms returns FALSE
TA260 000:706.999 JLINK_HasError()
TA260 000:708.227 JLINK_IsHalted()
TA260 000:708.704 - 0.476ms returns FALSE
TA260 000:708.716 JLINK_HasError()
TA260 000:710.730 JLINK_IsHalted()
TA260 000:711.162 - 0.431ms returns FALSE
TA260 000:711.169 JLINK_HasError()
TA260 000:712.733 JLINK_IsHalted()
TA260 000:713.181 - 0.447ms returns FALSE
TA260 000:713.189 JLINK_HasError()
TA260 000:714.731 JLINK_IsHalted()
TA260 000:715.215 - 0.484ms returns FALSE
TA260 000:715.225 JLINK_HasError()
TA260 000:717.244 JLINK_IsHalted()
TA260 000:717.693 - 0.448ms returns FALSE
TA260 000:717.701 JLINK_HasError()
TA260 000:719.243 JLINK_IsHalted()
TA260 000:719.784 - 0.540ms returns FALSE
TA260 000:719.795 JLINK_HasError()
TA260 000:722.237 JLINK_IsHalted()
TA260 000:722.685 - 0.447ms returns FALSE
TA260 000:722.698 JLINK_HasError()
TA260 000:724.744 JLINK_IsHalted()
TA260 000:725.217 - 0.471ms returns FALSE
TA260 000:725.228 JLINK_HasError()
TA260 000:726.752 JLINK_IsHalted()
TA260 000:727.297 - 0.544ms returns FALSE
TA260 000:727.308 JLINK_HasError()
TA260 000:728.752 JLINK_IsHalted()
TA260 000:729.203 - 0.450ms returns FALSE
TA260 000:729.214 JLINK_HasError()
TA260 000:730.752 JLINK_IsHalted()
TA260 000:731.261 - 0.508ms returns FALSE
TA260 000:731.274 JLINK_HasError()
TA260 000:732.750 JLINK_IsHalted()
TA260 000:733.164 - 0.414ms returns FALSE
TA260 000:733.175 JLINK_HasError()
TA260 000:734.752 JLINK_IsHalted()
TA260 000:735.264 - 0.511ms returns FALSE
TA260 000:735.275 JLINK_HasError()
TA260 000:737.260 JLINK_IsHalted()
TA260 000:737.736 - 0.475ms returns FALSE
TA260 000:737.743 JLINK_HasError()
TA260 000:739.260 JLINK_IsHalted()
TA260 000:739.735 - 0.474ms returns FALSE
TA260 000:739.745 JLINK_HasError()
TA260 000:741.257 JLINK_IsHalted()
TA260 000:741.753 - 0.496ms returns FALSE
TA260 000:741.759 JLINK_HasError()
TA260 000:743.257 JLINK_IsHalted()
TA260 000:743.756 - 0.499ms returns FALSE
TA260 000:743.769 JLINK_HasError()
TA260 000:745.762 JLINK_IsHalted()
TA260 000:746.290 - 0.526ms returns FALSE
TA260 000:746.302 JLINK_HasError()
TA260 000:747.766 JLINK_IsHalted()
TA260 000:748.233 - 0.467ms returns FALSE
TA260 000:748.239 JLINK_HasError()
TA260 000:749.764 JLINK_IsHalted()
TA260 000:750.258 - 0.494ms returns FALSE
TA260 000:750.264 JLINK_HasError()
TA260 000:751.673 JLINK_IsHalted()
TA260 000:752.110 - 0.436ms returns FALSE
TA260 000:752.116 JLINK_HasError()
TA260 000:753.673 JLINK_IsHalted()
TA260 000:754.188 - 0.514ms returns FALSE
TA260 000:754.193 JLINK_HasError()
TA260 000:756.186 JLINK_IsHalted()
TA260 000:756.648 - 0.461ms returns FALSE
TA260 000:756.661 JLINK_HasError()
TA260 000:758.183 JLINK_IsHalted()
TA260 000:758.734 - 0.550ms returns FALSE
TA260 000:758.741 JLINK_HasError()
TA260 000:760.185 JLINK_IsHalted()
TA260 000:760.702 - 0.517ms returns FALSE
TA260 000:760.708 JLINK_HasError()
TA260 000:762.181 JLINK_IsHalted()
TA260 000:762.688 - 0.507ms returns FALSE
TA260 000:762.694 JLINK_HasError()
TA260 000:764.686 JLINK_IsHalted()
TA260 000:765.153 - 0.466ms returns FALSE
TA260 000:765.162 JLINK_HasError()
TA260 000:766.690 JLINK_IsHalted()
TA260 000:767.229 - 0.538ms returns FALSE
TA260 000:767.240 JLINK_HasError()
TA260 000:768.691 JLINK_IsHalted()
TA260 000:769.190 - 0.497ms returns FALSE
TA260 000:769.201 JLINK_HasError()
TA260 000:770.693 JLINK_IsHalted()
TA260 000:771.158 - 0.464ms returns FALSE
TA260 000:771.164 JLINK_HasError()
TA260 000:772.688 JLINK_IsHalted()
TA260 000:773.165 - 0.476ms returns FALSE
TA260 000:773.170 JLINK_HasError()
TA260 000:774.689 JLINK_IsHalted()
TA260 000:775.130 - 0.441ms returns FALSE
TA260 000:775.136 JLINK_HasError()
TA260 000:776.201 JLINK_IsHalted()
TA260 000:776.661 - 0.459ms returns FALSE
TA260 000:776.670 JLINK_HasError()
TA260 000:778.209 JLINK_IsHalted()
TA260 000:778.701 - 0.492ms returns FALSE
TA260 000:778.711 JLINK_HasError()
TA260 000:780.199 JLINK_IsHalted()
TA260 000:780.679 - 0.480ms returns FALSE
TA260 000:780.685 JLINK_HasError()
TA260 000:782.195 JLINK_IsHalted()
TA260 000:782.748 - 0.552ms returns FALSE
TA260 000:782.760 JLINK_HasError()
TA260 000:784.702 JLINK_IsHalted()
TA260 000:785.232 - 0.529ms returns FALSE
TA260 000:785.237 JLINK_HasError()
TA260 000:786.707 JLINK_IsHalted()
TA260 000:787.158 - 0.451ms returns FALSE
TA260 000:787.169 JLINK_HasError()
TA260 000:788.709 JLINK_IsHalted()
TA260 000:789.164 - 0.455ms returns FALSE
TA260 000:789.169 JLINK_HasError()
TA260 000:790.709 JLINK_IsHalted()
TA260 000:791.213 - 0.503ms returns FALSE
TA260 000:791.219 JLINK_HasError()
TA260 000:792.704 JLINK_IsHalted()
TA260 000:793.188 - 0.484ms returns FALSE
TA260 000:793.194 JLINK_HasError()
TA260 000:794.706 JLINK_IsHalted()
TA260 000:795.199 - 0.492ms returns FALSE
TA260 000:795.205 JLINK_HasError()
TA260 000:797.219 JLINK_IsHalted()
TA260 000:797.727 - 0.507ms returns FALSE
TA260 000:797.737 JLINK_HasError()
TA260 000:799.215 JLINK_IsHalted()
TA260 000:799.690 - 0.474ms returns FALSE
TA260 000:799.695 JLINK_HasError()
TA260 000:801.215 JLINK_IsHalted()
TA260 000:801.689 - 0.474ms returns FALSE
TA260 000:801.694 JLINK_HasError()
TA260 000:803.214 JLINK_IsHalted()
TA260 000:803.694 - 0.479ms returns FALSE
TA260 000:803.706 JLINK_HasError()
TA260 000:806.239 JLINK_IsHalted()
TA260 000:806.729 - 0.488ms returns FALSE
TA260 000:806.740 JLINK_HasError()
TA260 000:808.233 JLINK_IsHalted()
TA260 000:808.736 - 0.502ms returns FALSE
TA260 000:808.742 JLINK_HasError()
TA260 000:810.232 JLINK_IsHalted()
TA260 000:810.662 - 0.429ms returns FALSE
TA260 000:810.668 JLINK_HasError()
TA260 000:812.231 JLINK_IsHalted()
TA260 000:812.755 - 0.523ms returns FALSE
TA260 000:812.761 JLINK_HasError()
TA260 000:814.765 JLINK_IsHalted()
TA260 000:815.195 - 0.429ms returns FALSE
TA260 000:815.200 JLINK_HasError()
TA260 000:817.742 JLINK_IsHalted()
TA260 000:818.225 - 0.482ms returns FALSE
TA260 000:818.231 JLINK_HasError()
TA260 000:819.742 JLINK_IsHalted()
TA260 000:820.247 - 0.504ms returns FALSE
TA260 000:820.253 JLINK_HasError()
TA260 000:821.736 JLINK_IsHalted()
TA260 000:822.231 - 0.494ms returns FALSE
TA260 000:822.236 JLINK_HasError()
TA260 000:823.736 JLINK_IsHalted()
TA260 000:824.231 - 0.495ms returns FALSE
TA260 000:824.236 JLINK_HasError()
TA260 000:826.244 JLINK_IsHalted()
TA260 000:826.847 - 0.601ms returns FALSE
TA260 000:826.858 JLINK_HasError()
TA260 000:828.247 JLINK_IsHalted()
TA260 000:828.756 - 0.508ms returns FALSE
TA260 000:828.762 JLINK_HasError()
TA260 000:830.247 JLINK_IsHalted()
TA260 000:830.695 - 0.447ms returns FALSE
TA260 000:830.704 JLINK_HasError()
TA260 000:832.243 JLINK_IsHalted()
TA260 000:832.734 - 0.491ms returns FALSE
TA260 000:832.740 JLINK_HasError()
TA260 000:834.745 JLINK_IsHalted()
TA260 000:835.222 - 0.476ms returns FALSE
TA260 000:835.227 JLINK_HasError()
TA260 000:836.755 JLINK_IsHalted()
TA260 000:837.222 - 0.466ms returns FALSE
TA260 000:837.234 JLINK_HasError()
TA260 000:838.754 JLINK_IsHalted()
TA260 000:839.245 - 0.490ms returns FALSE
TA260 000:839.251 JLINK_HasError()
TA260 000:840.757 JLINK_IsHalted()
TA260 000:841.254 - 0.496ms returns FALSE
TA260 000:841.260 JLINK_HasError()
TA260 000:842.750 JLINK_IsHalted()
TA260 000:843.257 - 0.506ms returns FALSE
TA260 000:843.262 JLINK_HasError()
TA260 000:844.752 JLINK_IsHalted()
TA260 000:845.260 - 0.507ms returns FALSE
TA260 000:845.268 JLINK_HasError()
TA260 000:847.262 JLINK_IsHalted()
TA260 000:847.712 - 0.449ms returns FALSE
TA260 000:847.718 JLINK_HasError()
TA260 000:849.264 JLINK_IsHalted()
TA260 000:849.744 - 0.480ms returns FALSE
TA260 000:849.750 JLINK_HasError()
TA260 000:851.261 JLINK_IsHalted()
TA260 000:851.734 - 0.472ms returns FALSE
TA260 000:851.741 JLINK_HasError()
TA260 000:853.258 JLINK_IsHalted()
TA260 000:853.755 - 0.497ms returns FALSE
TA260 000:853.760 JLINK_HasError()
TA260 000:855.764 JLINK_IsHalted()
TA260 000:856.371 - 0.607ms returns FALSE
TA260 000:856.384 JLINK_HasError()
TA260 000:857.769 JLINK_IsHalted()
TA260 000:858.234 - 0.465ms returns FALSE
TA260 000:858.279 JLINK_HasError()
TA260 000:859.766 JLINK_IsHalted()
TA260 000:860.245 - 0.478ms returns FALSE
TA260 000:860.253 JLINK_HasError()
TA260 000:861.768 JLINK_IsHalted()
TA260 000:862.213 - 0.445ms returns FALSE
TA260 000:862.223 JLINK_HasError()
TA260 000:863.764 JLINK_IsHalted()
TA260 000:864.243 - 0.479ms returns FALSE
TA260 000:864.250 JLINK_HasError()
TA260 000:866.270 JLINK_IsHalted()
TA260 000:866.738 - 0.466ms returns FALSE
TA260 000:866.749 JLINK_HasError()
TA260 000:868.272 JLINK_IsHalted()
TA260 000:868.780 - 0.508ms returns FALSE
TA260 000:868.787 JLINK_HasError()
TA260 000:870.270 JLINK_IsHalted()
TA260 000:870.779 - 0.509ms returns FALSE
TA260 000:870.785 JLINK_HasError()
TA260 000:872.273 JLINK_IsHalted()
TA260 000:872.756 - 0.483ms returns FALSE
TA260 000:872.763 JLINK_HasError()
TA260 000:874.772 JLINK_IsHalted()
TA260 000:875.231 - 0.459ms returns FALSE
TA260 000:875.237 JLINK_HasError()
TA260 000:876.782 JLINK_IsHalted()
TA260 000:877.309 - 0.526ms returns FALSE
TA260 000:877.321 JLINK_HasError()
TA260 000:878.915 JLINK_IsHalted()
TA260 000:879.403 - 0.487ms returns FALSE
TA260 000:879.409 JLINK_HasError()
TA260 000:880.913 JLINK_IsHalted()
TA260 000:881.389 - 0.475ms returns FALSE
TA260 000:881.394 JLINK_HasError()
TA260 000:882.916 JLINK_IsHalted()
TA260 000:883.460 - 0.543ms returns FALSE
TA260 000:883.467 JLINK_HasError()
TA260 000:884.913 JLINK_IsHalted()
TA260 000:885.468 - 0.555ms returns FALSE
TA260 000:885.474 JLINK_HasError()
TA260 000:887.425 JLINK_IsHalted()
TA260 000:887.942 - 0.516ms returns FALSE
TA260 000:887.947 JLINK_HasError()
TA260 000:889.423 JLINK_IsHalted()
TA260 000:889.938 - 0.515ms returns FALSE
TA260 000:889.944 JLINK_HasError()
TA260 000:891.420 JLINK_IsHalted()
TA260 000:891.902 - 0.482ms returns FALSE
TA260 000:891.908 JLINK_HasError()
TA260 000:893.424 JLINK_IsHalted()
TA260 000:893.870 - 0.445ms returns FALSE
TA260 000:893.882 JLINK_HasError()
TA260 000:894.922 JLINK_IsHalted()
TA260 000:895.493 - 0.570ms returns FALSE
TA260 000:895.500 JLINK_HasError()
TA260 000:896.928 JLINK_IsHalted()
TA260 000:897.404 - 0.475ms returns FALSE
TA260 000:897.417 JLINK_HasError()
TA260 000:898.927 JLINK_IsHalted()
TA260 000:899.462 - 0.534ms returns FALSE
TA260 000:899.468 JLINK_HasError()
TA260 000:900.927 JLINK_IsHalted()
TA260 000:901.460 - 0.533ms returns FALSE
TA260 000:901.467 JLINK_HasError()
TA260 000:902.926 JLINK_IsHalted()
TA260 000:903.460 - 0.534ms returns FALSE
TA260 000:903.466 JLINK_HasError()
TA260 000:904.926 JLINK_IsHalted()
TA260 000:905.467 - 0.540ms returns FALSE
TA260 000:905.472 JLINK_HasError()
TA260 000:907.435 JLINK_IsHalted()
TA260 000:907.913 - 0.478ms returns FALSE
TA260 000:907.920 JLINK_HasError()
TA260 000:909.435 JLINK_IsHalted()
TA260 000:910.290 - 0.855ms returns FALSE
TA260 000:910.302 JLINK_HasError()
TA260 000:911.435 JLINK_IsHalted()
TA260 000:911.938 - 0.502ms returns FALSE
TA260 000:911.946 JLINK_HasError()
TA260 000:913.432 JLINK_IsHalted()
TA260 000:913.903 - 0.471ms returns FALSE
TA260 000:913.910 JLINK_HasError()
TA260 000:915.943 JLINK_IsHalted()
TA260 000:916.704 - 0.760ms returns FALSE
TA260 000:916.728 JLINK_HasError()
TA260 000:917.941 JLINK_IsHalted()
TA260 000:918.461 - 0.520ms returns FALSE
TA260 000:918.468 JLINK_HasError()
TA260 000:919.938 JLINK_IsHalted()
TA260 000:920.461 - 0.521ms returns FALSE
TA260 000:920.473 JLINK_HasError()
TA260 000:921.937 JLINK_IsHalted()
TA260 000:922.461 - 0.524ms returns FALSE
TA260 000:922.466 JLINK_HasError()
TA260 000:923.944 JLINK_IsHalted()
TA260 000:924.463 - 0.518ms returns FALSE
TA260 000:924.470 JLINK_HasError()
TA260 000:926.471 JLINK_IsHalted()
TA260 000:926.991 - 0.519ms returns FALSE
TA260 000:927.006 JLINK_HasError()
TA260 000:928.454 JLINK_IsHalted()
TA260 000:928.984 - 0.530ms returns FALSE
TA260 000:928.990 JLINK_HasError()
TA260 000:930.451 JLINK_IsHalted()
TA260 000:930.939 - 0.488ms returns FALSE
TA260 000:930.944 JLINK_HasError()
TA260 000:932.450 JLINK_IsHalted()
TA260 000:932.938 - 0.487ms returns FALSE
TA260 000:932.943 JLINK_HasError()
TA260 000:934.954 JLINK_IsHalted()
TA260 000:935.461 - 0.507ms returns FALSE
TA260 000:935.467 JLINK_HasError()
TA260 000:936.965 JLINK_IsHalted()
TA260 000:937.465 - 0.500ms returns FALSE
TA260 000:937.478 JLINK_HasError()
TA260 000:938.958 JLINK_IsHalted()
TA260 000:939.473 - 0.515ms returns FALSE
TA260 000:939.482 JLINK_HasError()
TA260 000:940.963 JLINK_IsHalted()
TA260 000:941.470 - 0.506ms returns FALSE
TA260 000:941.479 JLINK_HasError()
TA260 000:942.957 JLINK_IsHalted()
TA260 000:943.470 - 0.512ms returns FALSE
TA260 000:943.475 JLINK_HasError()
TA260 000:944.961 JLINK_IsHalted()
TA260 000:945.461 - 0.499ms returns FALSE
TA260 000:945.466 JLINK_HasError()
TA260 000:947.470 JLINK_IsHalted()
TA260 000:947.911 - 0.441ms returns FALSE
TA260 000:947.918 JLINK_HasError()
TA260 000:949.470 JLINK_IsHalted()
TA260 000:949.915 - 0.445ms returns FALSE
TA260 000:949.920 JLINK_HasError()
TA260 000:951.467 JLINK_IsHalted()
TA260 000:951.981 - 0.513ms returns FALSE
TA260 000:951.986 JLINK_HasError()
TA260 000:953.467 JLINK_IsHalted()
TA260 000:953.980 - 0.513ms returns FALSE
TA260 000:953.986 JLINK_HasError()
TA260 000:955.976 JLINK_IsHalted()
TA260 000:956.474 - 0.497ms returns FALSE
TA260 000:956.488 JLINK_HasError()
TA260 000:957.978 JLINK_IsHalted()
TA260 000:958.461 - 0.483ms returns FALSE
TA260 000:958.467 JLINK_HasError()
TA260 000:959.976 JLINK_IsHalted()
TA260 000:960.461 - 0.485ms returns FALSE
TA260 000:960.471 JLINK_HasError()
TA260 000:961.973 JLINK_IsHalted()
TA260 000:962.469 - 0.495ms returns FALSE
TA260 000:962.474 JLINK_HasError()
TA260 000:963.974 JLINK_IsHalted()
TA260 000:964.469 - 0.494ms returns FALSE
TA260 000:964.475 JLINK_HasError()
TA260 000:966.486 JLINK_IsHalted()
TA260 000:966.967 - 0.481ms returns FALSE
TA260 000:966.984 JLINK_HasError()
TA260 000:968.482 JLINK_IsHalted()
TA260 000:968.981 - 0.499ms returns FALSE
TA260 000:968.987 JLINK_HasError()
TA260 000:970.480 JLINK_IsHalted()
TA260 000:970.964 - 0.483ms returns FALSE
TA260 000:970.973 JLINK_HasError()
TA260 000:972.490 JLINK_IsHalted()
TA260 000:972.964 - 0.474ms returns FALSE
TA260 000:972.971 JLINK_HasError()
TA260 000:974.989 JLINK_IsHalted()
TA260 000:975.506 - 0.516ms returns FALSE
TA260 000:975.512 JLINK_HasError()
TA260 000:976.995 JLINK_IsHalted()
TA260 000:977.479 - 0.484ms returns FALSE
TA260 000:977.485 JLINK_HasError()
TA260 000:978.989 JLINK_IsHalted()
TA260 000:979.460 - 0.470ms returns FALSE
TA260 000:979.466 JLINK_HasError()
TA260 000:980.995 JLINK_IsHalted()
TA260 000:981.506 - 0.511ms returns FALSE
TA260 000:981.512 JLINK_HasError()
TA260 000:982.990 JLINK_IsHalted()
TA260 000:983.494 - 0.503ms returns FALSE
TA260 000:983.506 JLINK_HasError()
TA260 000:984.992 JLINK_IsHalted()
TA260 000:985.507 - 0.514ms returns FALSE
TA260 000:985.519 JLINK_HasError()
TA260 000:987.282 JLINK_IsHalted()
TA260 000:987.784 - 0.502ms returns FALSE
TA260 000:987.791 JLINK_HasError()
TA260 000:989.280 JLINK_IsHalted()
TA260 000:989.740 - 0.460ms returns FALSE
TA260 000:989.746 JLINK_HasError()
TA260 000:991.279 JLINK_IsHalted()
TA260 000:991.769 - 0.490ms returns FALSE
TA260 000:991.775 JLINK_HasError()
TA260 000:993.278 JLINK_IsHalted()
TA260 000:993.734 - 0.455ms returns FALSE
TA260 000:993.742 JLINK_HasError()
TA260 000:994.785 JLINK_IsHalted()
TA260 000:995.280 - 0.494ms returns FALSE
TA260 000:995.288 JLINK_HasError()
TA260 000:996.786 JLINK_IsHalted()
TA260 000:997.238 - 0.451ms returns FALSE
TA260 000:997.247 JLINK_HasError()
TA260 000:998.787 JLINK_IsHalted()
TA260 000:999.269 - 0.481ms returns FALSE
TA260 000:999.275 JLINK_HasError()
TA260 001:000.787 JLINK_IsHalted()
TA260 001:001.360 - 0.573ms returns FALSE
TA260 001:001.370 JLINK_HasError()
TA260 001:002.791 JLINK_IsHalted()
TA260 001:003.223 - 0.431ms returns FALSE
TA260 001:003.230 JLINK_HasError()
TA260 001:004.787 JLINK_IsHalted()
TA260 001:005.266 - 0.479ms returns FALSE
TA260 001:005.310 JLINK_HasError()
TA260 001:007.296 JLINK_IsHalted()
TA260 001:007.760 - 0.463ms returns FALSE
TA260 001:007.773 JLINK_HasError()
TA260 001:008.995 JLINK_IsHalted()
TA260 001:009.505 - 0.509ms returns FALSE
TA260 001:009.510 JLINK_HasError()
TA260 001:010.997 JLINK_IsHalted()
TA260 001:011.463 - 0.465ms returns FALSE
TA260 001:011.474 JLINK_HasError()
TA260 001:012.994 JLINK_IsHalted()
TA260 001:013.491 - 0.496ms returns FALSE
TA260 001:013.497 JLINK_HasError()
TA260 001:014.995 JLINK_IsHalted()
TA260 001:015.470 - 0.475ms returns FALSE
TA260 001:015.476 JLINK_HasError()
TA260 001:017.505 JLINK_IsHalted()
TA260 001:017.951 - 0.446ms returns FALSE
TA260 001:017.962 JLINK_HasError()
TA260 001:019.505 JLINK_IsHalted()
TA260 001:021.862   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:022.359 - 2.854ms returns TRUE
TA260 001:022.365 JLINK_ReadReg(R15 (PC))
TA260 001:022.372 - 0.006ms returns 0x20000000
TA260 001:022.376 JLINK_ClrBPEx(BPHandle = 0x00000003)
TA260 001:022.380 - 0.003ms returns 0x00
TA260 001:022.384 JLINK_ReadReg(R0)
TA260 001:022.388 - 0.003ms returns 0x00000000
TA260 001:022.760 JLINK_HasError()
TA260 001:022.769 JLINK_WriteReg(R0, 0x08004000)
TA260 001:022.774 - 0.005ms returns 0
TA260 001:022.778 JLINK_WriteReg(R1, 0x00004000)
TA260 001:022.782 - 0.003ms returns 0
TA260 001:022.786 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:022.789 - 0.003ms returns 0
TA260 001:022.794 JLINK_WriteReg(R3, 0x00000000)
TA260 001:022.797 - 0.003ms returns 0
TA260 001:022.801 JLINK_WriteReg(R4, 0x00000000)
TA260 001:022.804 - 0.003ms returns 0
TA260 001:022.808 JLINK_WriteReg(R5, 0x00000000)
TA260 001:022.812 - 0.003ms returns 0
TA260 001:022.816 JLINK_WriteReg(R6, 0x00000000)
TA260 001:022.819 - 0.003ms returns 0
TA260 001:022.823 JLINK_WriteReg(R7, 0x00000000)
TA260 001:022.827 - 0.003ms returns 0
TA260 001:022.831 JLINK_WriteReg(R8, 0x00000000)
TA260 001:022.834 - 0.003ms returns 0
TA260 001:022.838 JLINK_WriteReg(R9, 0x20000180)
TA260 001:022.842 - 0.003ms returns 0
TA260 001:022.846 JLINK_WriteReg(R10, 0x00000000)
TA260 001:022.850 - 0.003ms returns 0
TA260 001:022.854 JLINK_WriteReg(R11, 0x00000000)
TA260 001:022.857 - 0.003ms returns 0
TA260 001:022.861 JLINK_WriteReg(R12, 0x00000000)
TA260 001:022.865 - 0.003ms returns 0
TA260 001:022.869 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:022.873 - 0.003ms returns 0
TA260 001:022.877 JLINK_WriteReg(R14, 0x20000001)
TA260 001:022.880 - 0.003ms returns 0
TA260 001:022.884 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 001:022.888 - 0.003ms returns 0
TA260 001:022.892 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:022.895 - 0.003ms returns 0
TA260 001:022.899 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:022.903 - 0.003ms returns 0
TA260 001:022.907 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:022.910 - 0.003ms returns 0
TA260 001:022.914 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:022.918 - 0.003ms returns 0
TA260 001:022.923 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:022.927 - 0.004ms returns 0x00000004
TA260 001:022.931 JLINK_Go()
TA260 001:022.939   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:025.707 - 2.775ms 
TA260 001:025.723 JLINK_IsHalted()
TA260 001:028.150   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:028.697 - 2.974ms returns TRUE
TA260 001:028.704 JLINK_ReadReg(R15 (PC))
TA260 001:028.709 - 0.005ms returns 0x20000000
TA260 001:028.713 JLINK_ClrBPEx(BPHandle = 0x00000004)
TA260 001:028.717 - 0.003ms returns 0x00
TA260 001:028.722 JLINK_ReadReg(R0)
TA260 001:028.726 - 0.004ms returns 0x00000001
TA260 001:028.730 JLINK_HasError()
TA260 001:028.735 JLINK_WriteReg(R0, 0x08004000)
TA260 001:028.738 - 0.003ms returns 0
TA260 001:028.743 JLINK_WriteReg(R1, 0x00004000)
TA260 001:028.746 - 0.003ms returns 0
TA260 001:028.750 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:028.754 - 0.003ms returns 0
TA260 001:028.758 JLINK_WriteReg(R3, 0x00000000)
TA260 001:028.761 - 0.003ms returns 0
TA260 001:028.765 JLINK_WriteReg(R4, 0x00000000)
TA260 001:028.769 - 0.003ms returns 0
TA260 001:028.815 JLINK_WriteReg(R5, 0x00000000)
TA260 001:028.820 - 0.005ms returns 0
TA260 001:028.824 JLINK_WriteReg(R6, 0x00000000)
TA260 001:028.828 - 0.003ms returns 0
TA260 001:028.832 JLINK_WriteReg(R7, 0x00000000)
TA260 001:028.835 - 0.003ms returns 0
TA260 001:028.840 JLINK_WriteReg(R8, 0x00000000)
TA260 001:028.843 - 0.003ms returns 0
TA260 001:028.847 JLINK_WriteReg(R9, 0x20000180)
TA260 001:028.850 - 0.003ms returns 0
TA260 001:028.854 JLINK_WriteReg(R10, 0x00000000)
TA260 001:028.858 - 0.003ms returns 0
TA260 001:028.862 JLINK_WriteReg(R11, 0x00000000)
TA260 001:028.865 - 0.003ms returns 0
TA260 001:028.869 JLINK_WriteReg(R12, 0x00000000)
TA260 001:028.873 - 0.003ms returns 0
TA260 001:028.877 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:028.880 - 0.003ms returns 0
TA260 001:028.884 JLINK_WriteReg(R14, 0x20000001)
TA260 001:028.888 - 0.003ms returns 0
TA260 001:028.892 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 001:028.895 - 0.003ms returns 0
TA260 001:028.899 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:028.903 - 0.003ms returns 0
TA260 001:028.907 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:028.910 - 0.003ms returns 0
TA260 001:028.914 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:028.918 - 0.003ms returns 0
TA260 001:028.922 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:028.925 - 0.003ms returns 0
TA260 001:028.929 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:028.933 - 0.003ms returns 0x00000005
TA260 001:028.937 JLINK_Go()
TA260 001:028.944   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:031.699 - 2.761ms 
TA260 001:031.705 JLINK_IsHalted()
TA260 001:032.194 - 0.488ms returns FALSE
TA260 001:032.204 JLINK_HasError()
TA260 001:035.014 JLINK_IsHalted()
TA260 001:035.494 - 0.480ms returns FALSE
TA260 001:035.503 JLINK_HasError()
TA260 001:037.524 JLINK_IsHalted()
TA260 001:037.988 - 0.464ms returns FALSE
TA260 001:037.995 JLINK_HasError()
TA260 001:039.521 JLINK_IsHalted()
TA260 001:040.018 - 0.497ms returns FALSE
TA260 001:040.024 JLINK_HasError()
TA260 001:041.518 JLINK_IsHalted()
TA260 001:041.999 - 0.481ms returns FALSE
TA260 001:042.004 JLINK_HasError()
TA260 001:043.764 JLINK_IsHalted()
TA260 001:044.175 - 0.410ms returns FALSE
TA260 001:044.182 JLINK_HasError()
TA260 001:045.267 JLINK_IsHalted()
TA260 001:045.750 - 0.482ms returns FALSE
TA260 001:045.763 JLINK_HasError()
TA260 001:047.274 JLINK_IsHalted()
TA260 001:047.796 - 0.522ms returns FALSE
TA260 001:047.808 JLINK_HasError()
TA260 001:049.271 JLINK_IsHalted()
TA260 001:049.746 - 0.474ms returns FALSE
TA260 001:049.754 JLINK_HasError()
TA260 001:051.271 JLINK_IsHalted()
TA260 001:051.747 - 0.475ms returns FALSE
TA260 001:051.752 JLINK_HasError()
TA260 001:053.270 JLINK_IsHalted()
TA260 001:053.744 - 0.473ms returns FALSE
TA260 001:053.749 JLINK_HasError()
TA260 001:055.778 JLINK_IsHalted()
TA260 001:056.384 - 0.606ms returns FALSE
TA260 001:056.396 JLINK_HasError()
TA260 001:057.777 JLINK_IsHalted()
TA260 001:058.280 - 0.503ms returns FALSE
TA260 001:058.286 JLINK_HasError()
TA260 001:059.779 JLINK_IsHalted()
TA260 001:060.221 - 0.442ms returns FALSE
TA260 001:060.229 JLINK_HasError()
TA260 001:061.775 JLINK_IsHalted()
TA260 001:062.244 - 0.469ms returns FALSE
TA260 001:062.250 JLINK_HasError()
TA260 001:063.779 JLINK_IsHalted()
TA260 001:064.236 - 0.456ms returns FALSE
TA260 001:064.245 JLINK_HasError()
TA260 001:066.283 JLINK_IsHalted()
TA260 001:066.786 - 0.503ms returns FALSE
TA260 001:066.797 JLINK_HasError()
TA260 001:068.285 JLINK_IsHalted()
TA260 001:068.815 - 0.529ms returns FALSE
TA260 001:068.825 JLINK_HasError()
TA260 001:070.282 JLINK_IsHalted()
TA260 001:070.755 - 0.472ms returns FALSE
TA260 001:070.761 JLINK_HasError()
TA260 001:072.282 JLINK_IsHalted()
TA260 001:072.742 - 0.460ms returns FALSE
TA260 001:072.747 JLINK_HasError()
TA260 001:074.784 JLINK_IsHalted()
TA260 001:075.278 - 0.494ms returns FALSE
TA260 001:075.284 JLINK_HasError()
TA260 001:076.794 JLINK_IsHalted()
TA260 001:077.283 - 0.488ms returns FALSE
TA260 001:077.300 JLINK_HasError()
TA260 001:078.789 JLINK_IsHalted()
TA260 001:079.473 - 0.683ms returns FALSE
TA260 001:079.484 JLINK_HasError()
TA260 001:082.794 JLINK_IsHalted()
TA260 001:083.323 - 0.528ms returns FALSE
TA260 001:083.330 JLINK_HasError()
TA260 001:084.790 JLINK_IsHalted()
TA260 001:085.279 - 0.489ms returns FALSE
TA260 001:085.284 JLINK_HasError()
TA260 001:087.298 JLINK_IsHalted()
TA260 001:087.791 - 0.492ms returns FALSE
TA260 001:087.797 JLINK_HasError()
TA260 001:089.296 JLINK_IsHalted()
TA260 001:089.839 - 0.542ms returns FALSE
TA260 001:089.852 JLINK_HasError()
TA260 001:091.296 JLINK_IsHalted()
TA260 001:091.779 - 0.482ms returns FALSE
TA260 001:091.784 JLINK_HasError()
TA260 001:093.295 JLINK_IsHalted()
TA260 001:093.780 - 0.485ms returns FALSE
TA260 001:093.790 JLINK_HasError()
TA260 001:095.759 JLINK_IsHalted()
TA260 001:096.202 - 0.443ms returns FALSE
TA260 001:096.215 JLINK_HasError()
TA260 001:097.758 JLINK_IsHalted()
TA260 001:098.257 - 0.498ms returns FALSE
TA260 001:098.269 JLINK_HasError()
TA260 001:099.758 JLINK_IsHalted()
TA260 001:100.256 - 0.497ms returns FALSE
TA260 001:100.262 JLINK_HasError()
TA260 001:101.757 JLINK_IsHalted()
TA260 001:102.234 - 0.476ms returns FALSE
TA260 001:102.239 JLINK_HasError()
TA260 001:103.757 JLINK_IsHalted()
TA260 001:104.255 - 0.498ms returns FALSE
TA260 001:104.261 JLINK_HasError()
TA260 001:106.268 JLINK_IsHalted()
TA260 001:106.758 - 0.489ms returns FALSE
TA260 001:106.770 JLINK_HasError()
TA260 001:108.264 JLINK_IsHalted()
TA260 001:108.741 - 0.477ms returns FALSE
TA260 001:108.746 JLINK_HasError()
TA260 001:110.265 JLINK_IsHalted()
TA260 001:110.740 - 0.474ms returns FALSE
TA260 001:110.749 JLINK_HasError()
TA260 001:112.264 JLINK_IsHalted()
TA260 001:112.733 - 0.469ms returns FALSE
TA260 001:112.739 JLINK_HasError()
TA260 001:114.771 JLINK_IsHalted()
TA260 001:115.266 - 0.497ms returns FALSE
TA260 001:115.273 JLINK_HasError()
TA260 001:116.773 JLINK_IsHalted()
TA260 001:117.213 - 0.439ms returns FALSE
TA260 001:117.219 JLINK_HasError()
TA260 001:118.771 JLINK_IsHalted()
TA260 001:119.243 - 0.471ms returns FALSE
TA260 001:119.253 JLINK_HasError()
TA260 001:120.772 JLINK_IsHalted()
TA260 001:121.243 - 0.471ms returns FALSE
TA260 001:121.249 JLINK_HasError()
TA260 001:122.770 JLINK_IsHalted()
TA260 001:123.245 - 0.475ms returns FALSE
TA260 001:123.251 JLINK_HasError()
TA260 001:124.771 JLINK_IsHalted()
TA260 001:125.245 - 0.473ms returns FALSE
TA260 001:125.250 JLINK_HasError()
TA260 001:127.281 JLINK_IsHalted()
TA260 001:127.738 - 0.456ms returns FALSE
TA260 001:127.744 JLINK_HasError()
TA260 001:129.278 JLINK_IsHalted()
TA260 001:129.757 - 0.478ms returns FALSE
TA260 001:129.765 JLINK_HasError()
TA260 001:131.280 JLINK_IsHalted()
TA260 001:131.755 - 0.475ms returns FALSE
TA260 001:131.761 JLINK_HasError()
TA260 001:133.277 JLINK_IsHalted()
TA260 001:133.773 - 0.494ms returns FALSE
TA260 001:133.782 JLINK_HasError()
TA260 001:135.784 JLINK_IsHalted()
TA260 001:136.238 - 0.453ms returns FALSE
TA260 001:136.249 JLINK_HasError()
TA260 001:137.787 JLINK_IsHalted()
TA260 001:138.246 - 0.459ms returns FALSE
TA260 001:138.253 JLINK_HasError()
TA260 001:139.785 JLINK_IsHalted()
TA260 001:140.280 - 0.494ms returns FALSE
TA260 001:140.285 JLINK_HasError()
TA260 001:141.784 JLINK_IsHalted()
TA260 001:142.338 - 0.553ms returns FALSE
TA260 001:142.348 JLINK_HasError()
TA260 001:144.789 JLINK_IsHalted()
TA260 001:145.281 - 0.492ms returns FALSE
TA260 001:145.287 JLINK_HasError()
TA260 001:147.298 JLINK_IsHalted()
TA260 001:147.803 - 0.505ms returns FALSE
TA260 001:147.810 JLINK_HasError()
TA260 001:149.298 JLINK_IsHalted()
TA260 001:149.786 - 0.488ms returns FALSE
TA260 001:149.792 JLINK_HasError()
TA260 001:151.294 JLINK_IsHalted()
TA260 001:151.799 - 0.504ms returns FALSE
TA260 001:151.805 JLINK_HasError()
TA260 001:153.300 JLINK_IsHalted()
TA260 001:153.779 - 0.479ms returns FALSE
TA260 001:153.785 JLINK_HasError()
TA260 001:155.801 JLINK_IsHalted()
TA260 001:156.331 - 0.529ms returns FALSE
TA260 001:156.345 JLINK_HasError()
TA260 001:157.805 JLINK_IsHalted()
TA260 001:158.348 - 0.542ms returns FALSE
TA260 001:158.357 JLINK_HasError()
TA260 001:159.804 JLINK_IsHalted()
TA260 001:160.283 - 0.479ms returns FALSE
TA260 001:160.292 JLINK_HasError()
TA260 001:161.800 JLINK_IsHalted()
TA260 001:162.255 - 0.455ms returns FALSE
TA260 001:162.260 JLINK_HasError()
TA260 001:163.801 JLINK_IsHalted()
TA260 001:164.256 - 0.455ms returns FALSE
TA260 001:164.262 JLINK_HasError()
TA260 001:165.303 JLINK_IsHalted()
TA260 001:165.775 - 0.472ms returns FALSE
TA260 001:165.785 JLINK_HasError()
TA260 001:167.310 JLINK_IsHalted()
TA260 001:167.745 - 0.434ms returns FALSE
TA260 001:167.751 JLINK_HasError()
TA260 001:169.313 JLINK_IsHalted()
TA260 001:169.799 - 0.486ms returns FALSE
TA260 001:169.809 JLINK_HasError()
TA260 001:171.308 JLINK_IsHalted()
TA260 001:171.778 - 0.470ms returns FALSE
TA260 001:171.784 JLINK_HasError()
TA260 001:173.308 JLINK_IsHalted()
TA260 001:173.781 - 0.472ms returns FALSE
TA260 001:173.792 JLINK_HasError()
TA260 001:175.821 JLINK_IsHalted()
TA260 001:176.316 - 0.495ms returns FALSE
TA260 001:176.325 JLINK_HasError()
TA260 001:177.820 JLINK_IsHalted()
TA260 001:178.304 - 0.483ms returns FALSE
TA260 001:178.310 JLINK_HasError()
TA260 001:179.817 JLINK_IsHalted()
TA260 001:180.280 - 0.462ms returns FALSE
TA260 001:180.286 JLINK_HasError()
TA260 001:181.822 JLINK_IsHalted()
TA260 001:182.315 - 0.492ms returns FALSE
TA260 001:182.322 JLINK_HasError()
TA260 001:183.816 JLINK_IsHalted()
TA260 001:184.315 - 0.499ms returns FALSE
TA260 001:184.326 JLINK_HasError()
TA260 001:186.022 JLINK_IsHalted()
TA260 001:186.468 - 0.446ms returns FALSE
TA260 001:186.479 JLINK_HasError()
TA260 001:188.022 JLINK_IsHalted()
TA260 001:188.464 - 0.441ms returns FALSE
TA260 001:188.475 JLINK_HasError()
TA260 001:190.025 JLINK_IsHalted()
TA260 001:190.472 - 0.447ms returns FALSE
TA260 001:190.478 JLINK_HasError()
TA260 001:192.482 JLINK_IsHalted()
TA260 001:192.984 - 0.502ms returns FALSE
TA260 001:192.997 JLINK_HasError()
TA260 001:194.995 JLINK_IsHalted()
TA260 001:195.506 - 0.510ms returns FALSE
TA260 001:195.522 JLINK_HasError()
TA260 001:196.993 JLINK_IsHalted()
TA260 001:197.471 - 0.477ms returns FALSE
TA260 001:197.478 JLINK_HasError()
TA260 001:198.990 JLINK_IsHalted()
TA260 001:199.470 - 0.479ms returns FALSE
TA260 001:199.476 JLINK_HasError()
TA260 001:200.991 JLINK_IsHalted()
TA260 001:201.469 - 0.478ms returns FALSE
TA260 001:201.476 JLINK_HasError()
TA260 001:202.988 JLINK_IsHalted()
TA260 001:203.459 - 0.471ms returns FALSE
TA260 001:203.465 JLINK_HasError()
TA260 001:204.990 JLINK_IsHalted()
TA260 001:205.470 - 0.479ms returns FALSE
TA260 001:205.479 JLINK_HasError()
TA260 001:207.497 JLINK_IsHalted()
TA260 001:208.007 - 0.509ms returns FALSE
TA260 001:208.019 JLINK_HasError()
TA260 001:209.494 JLINK_IsHalted()
TA260 001:209.998 - 0.503ms returns FALSE
TA260 001:210.004 JLINK_HasError()
TA260 001:211.495 JLINK_IsHalted()
TA260 001:211.983 - 0.487ms returns FALSE
TA260 001:211.989 JLINK_HasError()
TA260 001:213.494 JLINK_IsHalted()
TA260 001:214.006 - 0.511ms returns FALSE
TA260 001:214.011 JLINK_HasError()
TA260 001:216.006 JLINK_IsHalted()
TA260 001:216.496 - 0.489ms returns FALSE
TA260 001:216.502 JLINK_HasError()
TA260 001:218.001 JLINK_IsHalted()
TA260 001:218.463 - 0.461ms returns FALSE
TA260 001:218.469 JLINK_HasError()
TA260 001:220.005 JLINK_IsHalted()
TA260 001:220.492 - 0.487ms returns FALSE
TA260 001:220.503 JLINK_HasError()
TA260 001:222.003 JLINK_IsHalted()
TA260 001:222.494 - 0.491ms returns FALSE
TA260 001:222.501 JLINK_HasError()
TA260 001:224.000 JLINK_IsHalted()
TA260 001:224.505 - 0.504ms returns FALSE
TA260 001:224.510 JLINK_HasError()
TA260 001:226.513 JLINK_IsHalted()
TA260 001:226.967 - 0.453ms returns FALSE
TA260 001:226.981 JLINK_HasError()
TA260 001:228.509 JLINK_IsHalted()
TA260 001:228.996 - 0.487ms returns FALSE
TA260 001:229.002 JLINK_HasError()
TA260 001:230.506 JLINK_IsHalted()
TA260 001:230.970 - 0.463ms returns FALSE
TA260 001:230.976 JLINK_HasError()
TA260 001:232.509 JLINK_IsHalted()
TA260 001:232.984 - 0.475ms returns FALSE
TA260 001:232.989 JLINK_HasError()
TA260 001:235.010 JLINK_IsHalted()
TA260 001:235.490 - 0.479ms returns FALSE
TA260 001:235.496 JLINK_HasError()
TA260 001:237.024 JLINK_IsHalted()
TA260 001:237.613 - 0.588ms returns FALSE
TA260 001:237.624 JLINK_HasError()
TA260 001:240.016 JLINK_IsHalted()
TA260 001:240.510 - 0.493ms returns FALSE
TA260 001:240.516 JLINK_HasError()
TA260 001:242.014 JLINK_IsHalted()
TA260 001:242.490 - 0.476ms returns FALSE
TA260 001:242.496 JLINK_HasError()
TA260 001:244.016 JLINK_IsHalted()
TA260 001:244.508 - 0.492ms returns FALSE
TA260 001:244.514 JLINK_HasError()
TA260 001:246.527 JLINK_IsHalted()
TA260 001:246.980 - 0.453ms returns FALSE
TA260 001:246.991 JLINK_HasError()
TA260 001:248.530 JLINK_IsHalted()
TA260 001:248.989 - 0.458ms returns FALSE
TA260 001:249.002 JLINK_HasError()
TA260 001:250.523 JLINK_IsHalted()
TA260 001:251.018 - 0.494ms returns FALSE
TA260 001:251.023 JLINK_HasError()
TA260 001:252.523 JLINK_IsHalted()
TA260 001:253.008 - 0.484ms returns FALSE
TA260 001:253.020 JLINK_HasError()
TA260 001:255.029 JLINK_IsHalted()
TA260 001:255.544 - 0.515ms returns FALSE
TA260 001:255.550 JLINK_HasError()
TA260 001:257.036 JLINK_IsHalted()
TA260 001:257.465 - 0.428ms returns FALSE
TA260 001:257.477 JLINK_HasError()
TA260 001:259.032 JLINK_IsHalted()
TA260 001:259.506 - 0.474ms returns FALSE
TA260 001:259.512 JLINK_HasError()
TA260 001:261.036 JLINK_IsHalted()
TA260 001:261.610 - 0.573ms returns FALSE
TA260 001:261.619 JLINK_HasError()
TA260 001:263.032 JLINK_IsHalted()
TA260 001:263.507 - 0.474ms returns FALSE
TA260 001:263.512 JLINK_HasError()
TA260 001:265.036 JLINK_IsHalted()
TA260 001:265.537 - 0.501ms returns FALSE
TA260 001:265.543 JLINK_HasError()
TA260 001:267.544 JLINK_IsHalted()
TA260 001:268.065 - 0.520ms returns FALSE
TA260 001:268.072 JLINK_HasError()
TA260 001:269.547 JLINK_IsHalted()
TA260 001:270.034 - 0.487ms returns FALSE
TA260 001:270.044 JLINK_HasError()
TA260 001:271.541 JLINK_IsHalted()
TA260 001:272.039 - 0.497ms returns FALSE
TA260 001:272.045 JLINK_HasError()
TA260 001:273.541 JLINK_IsHalted()
TA260 001:274.028 - 0.487ms returns FALSE
TA260 001:274.034 JLINK_HasError()
TA260 001:276.050 JLINK_IsHalted()
TA260 001:276.659 - 0.608ms returns FALSE
TA260 001:276.672 JLINK_HasError()
TA260 001:278.050 JLINK_IsHalted()
TA260 001:278.609 - 0.559ms returns FALSE
TA260 001:278.616 JLINK_HasError()
TA260 001:280.053 JLINK_IsHalted()
TA260 001:280.506 - 0.452ms returns FALSE
TA260 001:280.513 JLINK_HasError()
TA260 001:282.052 JLINK_IsHalted()
TA260 001:282.508 - 0.456ms returns FALSE
TA260 001:282.514 JLINK_HasError()
TA260 001:284.048 JLINK_IsHalted()
TA260 001:284.615 - 0.566ms returns FALSE
TA260 001:284.625 JLINK_HasError()
TA260 001:286.067 JLINK_IsHalted()
TA260 001:286.655 - 0.587ms returns FALSE
TA260 001:286.670 JLINK_HasError()
TA260 001:288.075 JLINK_IsHalted()
TA260 001:288.542 - 0.466ms returns FALSE
TA260 001:288.553 JLINK_HasError()
TA260 001:290.072 JLINK_IsHalted()
TA260 001:290.512 - 0.439ms returns FALSE
TA260 001:290.519 JLINK_HasError()
TA260 001:292.068 JLINK_IsHalted()
TA260 001:292.610 - 0.542ms returns FALSE
TA260 001:292.615 JLINK_HasError()
TA260 001:293.760 JLINK_IsHalted()
TA260 001:294.220 - 0.459ms returns FALSE
TA260 001:294.232 JLINK_HasError()
TA260 001:296.026 JLINK_IsHalted()
TA260 001:297.023 - 0.996ms returns FALSE
TA260 001:297.042 JLINK_HasError()
TA260 001:299.023 JLINK_IsHalted()
TA260 001:299.508 - 0.484ms returns FALSE
TA260 001:299.519 JLINK_HasError()
TA260 001:301.026 JLINK_IsHalted()
TA260 001:301.462 - 0.435ms returns FALSE
TA260 001:301.473 JLINK_HasError()
TA260 001:303.022 JLINK_IsHalted()
TA260 001:303.491 - 0.469ms returns FALSE
TA260 001:303.497 JLINK_HasError()
TA260 001:305.022 JLINK_IsHalted()
TA260 001:305.496 - 0.473ms returns FALSE
TA260 001:305.505 JLINK_HasError()
TA260 001:307.527 JLINK_IsHalted()
TA260 001:307.996 - 0.468ms returns FALSE
TA260 001:308.003 JLINK_HasError()
TA260 001:309.524 JLINK_IsHalted()
TA260 001:310.019 - 0.495ms returns FALSE
TA260 001:310.026 JLINK_HasError()
TA260 001:311.528 JLINK_IsHalted()
TA260 001:311.991 - 0.462ms returns FALSE
TA260 001:311.997 JLINK_HasError()
TA260 001:313.525 JLINK_IsHalted()
TA260 001:313.993 - 0.468ms returns FALSE
TA260 001:313.999 JLINK_HasError()
TA260 001:316.104 JLINK_IsHalted()
TA260 001:316.720 - 0.615ms returns FALSE
TA260 001:316.731 JLINK_HasError()
TA260 001:318.034 JLINK_IsHalted()
TA260 001:318.492 - 0.458ms returns FALSE
TA260 001:318.498 JLINK_HasError()
TA260 001:320.036 JLINK_IsHalted()
TA260 001:320.506 - 0.469ms returns FALSE
TA260 001:320.512 JLINK_HasError()
TA260 001:322.041 JLINK_IsHalted()
TA260 001:322.563 - 0.522ms returns FALSE
TA260 001:322.573 JLINK_HasError()
TA260 001:324.034 JLINK_IsHalted()
TA260 001:324.506 - 0.472ms returns FALSE
TA260 001:324.511 JLINK_HasError()
TA260 001:326.549 JLINK_IsHalted()
TA260 001:327.116 - 0.566ms returns FALSE
TA260 001:327.130 JLINK_HasError()
TA260 001:328.543 JLINK_IsHalted()
TA260 001:329.085 - 0.541ms returns FALSE
TA260 001:329.091 JLINK_HasError()
TA260 001:330.542 JLINK_IsHalted()
TA260 001:331.028 - 0.486ms returns FALSE
TA260 001:331.034 JLINK_HasError()
TA260 001:332.547 JLINK_IsHalted()
TA260 001:332.983 - 0.435ms returns FALSE
TA260 001:332.993 JLINK_HasError()
TA260 001:335.045 JLINK_IsHalted()
TA260 001:335.490 - 0.445ms returns FALSE
TA260 001:335.496 JLINK_HasError()
TA260 001:338.255 JLINK_IsHalted()
TA260 001:338.744 - 0.488ms returns FALSE
TA260 001:338.757 JLINK_HasError()
TA260 001:340.250 JLINK_IsHalted()
TA260 001:340.779 - 0.529ms returns FALSE
TA260 001:340.785 JLINK_HasError()
TA260 001:343.258 JLINK_IsHalted()
TA260 001:343.770 - 0.511ms returns FALSE
TA260 001:343.781 JLINK_HasError()
TA260 001:345.755 JLINK_IsHalted()
TA260 001:346.466 - 0.710ms returns FALSE
TA260 001:346.478 JLINK_HasError()
TA260 001:349.758 JLINK_IsHalted()
TA260 001:350.266 - 0.508ms returns FALSE
TA260 001:350.275 JLINK_HasError()
TA260 001:352.757 JLINK_IsHalted()
TA260 001:353.257 - 0.500ms returns FALSE
TA260 001:353.263 JLINK_HasError()
TA260 001:354.757 JLINK_IsHalted()
TA260 001:355.278 - 0.520ms returns FALSE
TA260 001:355.284 JLINK_HasError()
TA260 001:357.267 JLINK_IsHalted()
TA260 001:357.744 - 0.476ms returns FALSE
TA260 001:357.757 JLINK_HasError()
TA260 001:359.261 JLINK_IsHalted()
TA260 001:359.727 - 0.465ms returns FALSE
TA260 001:359.740 JLINK_HasError()
TA260 001:362.264 JLINK_IsHalted()
TA260 001:362.838 - 0.573ms returns FALSE
TA260 001:362.849 JLINK_HasError()
TA260 001:364.776 JLINK_IsHalted()
TA260 001:365.280 - 0.503ms returns FALSE
TA260 001:365.286 JLINK_HasError()
TA260 001:366.770 JLINK_IsHalted()
TA260 001:367.232 - 0.462ms returns FALSE
TA260 001:367.239 JLINK_HasError()
TA260 001:368.770 JLINK_IsHalted()
TA260 001:369.257 - 0.486ms returns FALSE
TA260 001:369.263 JLINK_HasError()
TA260 001:370.772 JLINK_IsHalted()
TA260 001:371.259 - 0.486ms returns FALSE
TA260 001:371.265 JLINK_HasError()
TA260 001:372.770 JLINK_IsHalted()
TA260 001:373.233 - 0.462ms returns FALSE
TA260 001:373.243 JLINK_HasError()
TA260 001:374.770 JLINK_IsHalted()
TA260 001:375.244 - 0.473ms returns FALSE
TA260 001:375.249 JLINK_HasError()
TA260 001:378.281 JLINK_IsHalted()
TA260 001:378.736 - 0.455ms returns FALSE
TA260 001:378.743 JLINK_HasError()
TA260 001:380.276 JLINK_IsHalted()
TA260 001:380.778 - 0.501ms returns FALSE
TA260 001:380.785 JLINK_HasError()
TA260 001:382.276 JLINK_IsHalted()
TA260 001:382.768 - 0.491ms returns FALSE
TA260 001:382.773 JLINK_HasError()
TA260 001:384.780 JLINK_IsHalted()
TA260 001:385.256 - 0.476ms returns FALSE
TA260 001:385.261 JLINK_HasError()
TA260 001:386.784 JLINK_IsHalted()
TA260 001:387.234 - 0.449ms returns FALSE
TA260 001:387.242 JLINK_HasError()
TA260 001:388.784 JLINK_IsHalted()
TA260 001:389.305 - 0.520ms returns FALSE
TA260 001:389.318 JLINK_HasError()
TA260 001:391.782 JLINK_IsHalted()
TA260 001:392.222 - 0.439ms returns FALSE
TA260 001:392.228 JLINK_HasError()
TA260 001:393.786 JLINK_IsHalted()
TA260 001:394.268 - 0.481ms returns FALSE
TA260 001:394.276 JLINK_HasError()
TA260 001:396.289 JLINK_IsHalted()
TA260 001:396.751 - 0.461ms returns FALSE
TA260 001:396.763 JLINK_HasError()
TA260 001:398.291 JLINK_IsHalted()
TA260 001:398.738 - 0.447ms returns FALSE
TA260 001:398.748 JLINK_HasError()
TA260 001:400.288 JLINK_IsHalted()
TA260 001:400.740 - 0.452ms returns FALSE
TA260 001:400.746 JLINK_HasError()
TA260 001:402.287 JLINK_IsHalted()
TA260 001:402.779 - 0.491ms returns FALSE
TA260 001:402.785 JLINK_HasError()
TA260 001:404.790 JLINK_IsHalted()
TA260 001:405.279 - 0.489ms returns FALSE
TA260 001:405.285 JLINK_HasError()
TA260 001:406.798 JLINK_IsHalted()
TA260 001:407.270 - 0.471ms returns FALSE
TA260 001:407.277 JLINK_HasError()
TA260 001:409.298 JLINK_IsHalted()
TA260 001:409.873 - 0.574ms returns FALSE
TA260 001:409.885 JLINK_HasError()
TA260 001:411.300 JLINK_IsHalted()
TA260 001:411.735 - 0.435ms returns FALSE
TA260 001:411.741 JLINK_HasError()
TA260 001:413.297 JLINK_IsHalted()
TA260 001:413.779 - 0.482ms returns FALSE
TA260 001:413.786 JLINK_HasError()
TA260 001:415.802 JLINK_IsHalted()
TA260 001:416.558 - 0.756ms returns FALSE
TA260 001:416.571 JLINK_HasError()
TA260 001:417.803 JLINK_IsHalted()
TA260 001:418.283 - 0.479ms returns FALSE
TA260 001:418.302 JLINK_HasError()
TA260 001:419.805 JLINK_IsHalted()
TA260 001:422.086   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:422.610 - 2.805ms returns TRUE
TA260 001:422.617 JLINK_ReadReg(R15 (PC))
TA260 001:422.622 - 0.005ms returns 0x20000000
TA260 001:422.626 JLINK_ClrBPEx(BPHandle = 0x00000005)
TA260 001:422.630 - 0.003ms returns 0x00
TA260 001:422.634 JLINK_ReadReg(R0)
TA260 001:422.638 - 0.003ms returns 0x00000000
TA260 001:423.005 JLINK_HasError()
TA260 001:423.015 JLINK_WriteReg(R0, 0x08008000)
TA260 001:423.020 - 0.005ms returns 0
TA260 001:423.024 JLINK_WriteReg(R1, 0x00004000)
TA260 001:423.027 - 0.003ms returns 0
TA260 001:423.031 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:423.035 - 0.003ms returns 0
TA260 001:423.039 JLINK_WriteReg(R3, 0x00000000)
TA260 001:423.042 - 0.003ms returns 0
TA260 001:423.046 JLINK_WriteReg(R4, 0x00000000)
TA260 001:423.050 - 0.003ms returns 0
TA260 001:423.054 JLINK_WriteReg(R5, 0x00000000)
TA260 001:423.057 - 0.003ms returns 0
TA260 001:423.061 JLINK_WriteReg(R6, 0x00000000)
TA260 001:423.065 - 0.003ms returns 0
TA260 001:423.069 JLINK_WriteReg(R7, 0x00000000)
TA260 001:423.072 - 0.003ms returns 0
TA260 001:423.076 JLINK_WriteReg(R8, 0x00000000)
TA260 001:423.080 - 0.003ms returns 0
TA260 001:423.084 JLINK_WriteReg(R9, 0x20000180)
TA260 001:423.087 - 0.003ms returns 0
TA260 001:423.091 JLINK_WriteReg(R10, 0x00000000)
TA260 001:423.094 - 0.003ms returns 0
TA260 001:423.098 JLINK_WriteReg(R11, 0x00000000)
TA260 001:423.102 - 0.003ms returns 0
TA260 001:423.106 JLINK_WriteReg(R12, 0x00000000)
TA260 001:423.109 - 0.003ms returns 0
TA260 001:423.113 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:423.117 - 0.003ms returns 0
TA260 001:423.121 JLINK_WriteReg(R14, 0x20000001)
TA260 001:423.124 - 0.003ms returns 0
TA260 001:423.128 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 001:423.132 - 0.003ms returns 0
TA260 001:423.136 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:423.139 - 0.003ms returns 0
TA260 001:423.143 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:423.147 - 0.003ms returns 0
TA260 001:423.151 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:423.154 - 0.003ms returns 0
TA260 001:423.158 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:423.162 - 0.003ms returns 0
TA260 001:423.167 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:423.170 - 0.004ms returns 0x00000006
TA260 001:423.175 JLINK_Go()
TA260 001:423.183   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:426.210 - 3.034ms 
TA260 001:426.231 JLINK_IsHalted()
TA260 001:428.541   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:428.987 - 2.755ms returns TRUE
TA260 001:429.000 JLINK_ReadReg(R15 (PC))
TA260 001:429.007 - 0.007ms returns 0x20000000
TA260 001:429.042 JLINK_ClrBPEx(BPHandle = 0x00000006)
TA260 001:429.047 - 0.005ms returns 0x00
TA260 001:429.052 JLINK_ReadReg(R0)
TA260 001:429.056 - 0.003ms returns 0x00000001
TA260 001:429.061 JLINK_HasError()
TA260 001:429.066 JLINK_WriteReg(R0, 0x08008000)
TA260 001:429.070 - 0.004ms returns 0
TA260 001:429.074 JLINK_WriteReg(R1, 0x00004000)
TA260 001:429.077 - 0.003ms returns 0
TA260 001:429.081 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:429.085 - 0.003ms returns 0
TA260 001:429.089 JLINK_WriteReg(R3, 0x00000000)
TA260 001:429.092 - 0.003ms returns 0
TA260 001:429.096 JLINK_WriteReg(R4, 0x00000000)
TA260 001:429.100 - 0.003ms returns 0
TA260 001:429.104 JLINK_WriteReg(R5, 0x00000000)
TA260 001:429.107 - 0.003ms returns 0
TA260 001:429.111 JLINK_WriteReg(R6, 0x00000000)
TA260 001:429.115 - 0.003ms returns 0
TA260 001:429.119 JLINK_WriteReg(R7, 0x00000000)
TA260 001:429.122 - 0.003ms returns 0
TA260 001:429.126 JLINK_WriteReg(R8, 0x00000000)
TA260 001:429.130 - 0.003ms returns 0
TA260 001:429.134 JLINK_WriteReg(R9, 0x20000180)
TA260 001:429.137 - 0.003ms returns 0
TA260 001:429.141 JLINK_WriteReg(R10, 0x00000000)
TA260 001:429.144 - 0.003ms returns 0
TA260 001:429.148 JLINK_WriteReg(R11, 0x00000000)
TA260 001:429.152 - 0.003ms returns 0
TA260 001:429.156 JLINK_WriteReg(R12, 0x00000000)
TA260 001:429.159 - 0.003ms returns 0
TA260 001:429.163 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:429.167 - 0.003ms returns 0
TA260 001:429.171 JLINK_WriteReg(R14, 0x20000001)
TA260 001:429.175 - 0.003ms returns 0
TA260 001:429.179 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 001:429.182 - 0.003ms returns 0
TA260 001:429.186 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:429.190 - 0.003ms returns 0
TA260 001:429.194 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:429.198 - 0.003ms returns 0
TA260 001:429.202 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:429.205 - 0.003ms returns 0
TA260 001:429.209 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:429.212 - 0.003ms returns 0
TA260 001:429.217 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:429.221 - 0.004ms returns 0x00000007
TA260 001:429.225 JLINK_Go()
TA260 001:429.233   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:432.025 - 2.799ms 
TA260 001:432.034 JLINK_IsHalted()
TA260 001:432.535 - 0.501ms returns FALSE
TA260 001:432.541 JLINK_HasError()
TA260 001:434.815 JLINK_IsHalted()
TA260 001:435.306 - 0.490ms returns FALSE
TA260 001:435.315 JLINK_HasError()
TA260 001:436.821 JLINK_IsHalted()
TA260 001:437.321 - 0.500ms returns FALSE
TA260 001:437.335 JLINK_HasError()
TA260 001:438.816 JLINK_IsHalted()
TA260 001:439.315 - 0.498ms returns FALSE
TA260 001:439.321 JLINK_HasError()
TA260 001:440.821 JLINK_IsHalted()
TA260 001:441.325 - 0.504ms returns FALSE
TA260 001:441.333 JLINK_HasError()
TA260 001:442.816 JLINK_IsHalted()
TA260 001:443.314 - 0.497ms returns FALSE
TA260 001:443.319 JLINK_HasError()
TA260 001:444.818 JLINK_IsHalted()
TA260 001:445.289 - 0.471ms returns FALSE
TA260 001:445.295 JLINK_HasError()
TA260 001:447.326 JLINK_IsHalted()
TA260 001:447.848 - 0.522ms returns FALSE
TA260 001:447.855 JLINK_HasError()
TA260 001:449.325 JLINK_IsHalted()
TA260 001:449.825 - 0.499ms returns FALSE
TA260 001:449.832 JLINK_HasError()
TA260 001:451.325 JLINK_IsHalted()
TA260 001:451.835 - 0.510ms returns FALSE
TA260 001:451.841 JLINK_HasError()
TA260 001:453.326 JLINK_IsHalted()
TA260 001:453.825 - 0.499ms returns FALSE
TA260 001:453.835 JLINK_HasError()
TA260 001:455.831 JLINK_IsHalted()
TA260 001:456.560 - 0.728ms returns FALSE
TA260 001:456.571 JLINK_HasError()
TA260 001:458.895 JLINK_IsHalted()
TA260 001:459.382 - 0.486ms returns FALSE
TA260 001:459.389 JLINK_HasError()
TA260 001:460.896 JLINK_IsHalted()
TA260 001:461.384 - 0.488ms returns FALSE
TA260 001:461.394 JLINK_HasError()
TA260 001:462.893 JLINK_IsHalted()
TA260 001:463.391 - 0.497ms returns FALSE
TA260 001:463.398 JLINK_HasError()
TA260 001:464.896 JLINK_IsHalted()
TA260 001:465.471 - 0.574ms returns FALSE
TA260 001:465.477 JLINK_HasError()
TA260 001:467.405 JLINK_IsHalted()
TA260 001:467.884 - 0.478ms returns FALSE
TA260 001:467.890 JLINK_HasError()
TA260 001:469.405 JLINK_IsHalted()
TA260 001:469.892 - 0.487ms returns FALSE
TA260 001:469.898 JLINK_HasError()
TA260 001:471.404 JLINK_IsHalted()
TA260 001:471.961 - 0.557ms returns FALSE
TA260 001:471.968 JLINK_HasError()
TA260 001:473.403 JLINK_IsHalted()
TA260 001:473.890 - 0.486ms returns FALSE
TA260 001:473.896 JLINK_HasError()
TA260 001:475.913 JLINK_IsHalted()
TA260 001:476.464 - 0.551ms returns FALSE
TA260 001:476.475 JLINK_HasError()
TA260 001:477.911 JLINK_IsHalted()
TA260 001:478.471 - 0.560ms returns FALSE
TA260 001:478.479 JLINK_HasError()
TA260 001:479.911 JLINK_IsHalted()
TA260 001:480.391 - 0.480ms returns FALSE
TA260 001:480.396 JLINK_HasError()
TA260 001:481.911 JLINK_IsHalted()
TA260 001:482.469 - 0.558ms returns FALSE
TA260 001:482.476 JLINK_HasError()
TA260 001:483.909 JLINK_IsHalted()
TA260 001:484.381 - 0.471ms returns FALSE
TA260 001:484.386 JLINK_HasError()
TA260 001:486.417 JLINK_IsHalted()
TA260 001:487.195 - 0.777ms returns FALSE
TA260 001:487.206 JLINK_HasError()
TA260 001:488.522 JLINK_IsHalted()
TA260 001:489.031 - 0.508ms returns FALSE
TA260 001:489.037 JLINK_HasError()
TA260 001:490.519 JLINK_IsHalted()
TA260 001:490.981 - 0.461ms returns FALSE
TA260 001:490.987 JLINK_HasError()
TA260 001:492.519 JLINK_IsHalted()
TA260 001:493.005 - 0.486ms returns FALSE
TA260 001:493.011 JLINK_HasError()
TA260 001:495.030 JLINK_IsHalted()
TA260 001:495.596 - 0.565ms returns FALSE
TA260 001:495.604 JLINK_HasError()
TA260 001:497.027 JLINK_IsHalted()
TA260 001:497.471 - 0.443ms returns FALSE
TA260 001:497.477 JLINK_HasError()
TA260 001:499.025 JLINK_IsHalted()
TA260 001:499.506 - 0.480ms returns FALSE
TA260 001:499.511 JLINK_HasError()
TA260 001:501.026 JLINK_IsHalted()
TA260 001:501.505 - 0.479ms returns FALSE
TA260 001:501.511 JLINK_HasError()
TA260 001:503.028 JLINK_IsHalted()
TA260 001:503.461 - 0.432ms returns FALSE
TA260 001:503.467 JLINK_HasError()
TA260 001:505.028 JLINK_IsHalted()
TA260 001:505.506 - 0.478ms returns FALSE
TA260 001:505.512 JLINK_HasError()
TA260 001:507.037 JLINK_IsHalted()
TA260 001:507.507 - 0.470ms returns FALSE
TA260 001:507.514 JLINK_HasError()
TA260 001:509.034 JLINK_IsHalted()
TA260 001:509.508 - 0.474ms returns FALSE
TA260 001:509.514 JLINK_HasError()
TA260 001:511.033 JLINK_IsHalted()
TA260 001:511.463 - 0.430ms returns FALSE
TA260 001:511.472 JLINK_HasError()
TA260 001:513.031 JLINK_IsHalted()
TA260 001:513.506 - 0.474ms returns FALSE
TA260 001:513.511 JLINK_HasError()
TA260 001:515.033 JLINK_IsHalted()
TA260 001:515.542 - 0.509ms returns FALSE
TA260 001:515.549 JLINK_HasError()
TA260 001:517.537 JLINK_IsHalted()
TA260 001:517.997 - 0.460ms returns FALSE
TA260 001:518.006 JLINK_HasError()
TA260 001:519.539 JLINK_IsHalted()
TA260 001:520.031 - 0.492ms returns FALSE
TA260 001:520.038 JLINK_HasError()
TA260 001:521.536 JLINK_IsHalted()
TA260 001:522.006 - 0.469ms returns FALSE
TA260 001:522.012 JLINK_HasError()
TA260 001:523.539 JLINK_IsHalted()
TA260 001:524.087 - 0.547ms returns FALSE
TA260 001:524.094 JLINK_HasError()
TA260 001:526.044 JLINK_IsHalted()
TA260 001:526.506 - 0.462ms returns FALSE
TA260 001:526.513 JLINK_HasError()
TA260 001:528.043 JLINK_IsHalted()
TA260 001:528.610 - 0.566ms returns FALSE
TA260 001:528.616 JLINK_HasError()
TA260 001:530.043 JLINK_IsHalted()
TA260 001:530.508 - 0.464ms returns FALSE
TA260 001:530.513 JLINK_HasError()
TA260 001:532.044 JLINK_IsHalted()
TA260 001:532.514 - 0.469ms returns FALSE
TA260 001:532.520 JLINK_HasError()
TA260 001:534.046 JLINK_IsHalted()
TA260 001:534.560 - 0.513ms returns FALSE
TA260 001:534.570 JLINK_HasError()
TA260 001:536.558 JLINK_IsHalted()
TA260 001:537.104 - 0.545ms returns FALSE
TA260 001:537.117 JLINK_HasError()
TA260 001:538.553 JLINK_IsHalted()
TA260 001:539.027 - 0.473ms returns FALSE
TA260 001:539.042 JLINK_HasError()
TA260 001:540.551 JLINK_IsHalted()
TA260 001:541.022 - 0.470ms returns FALSE
TA260 001:541.028 JLINK_HasError()
TA260 001:542.552 JLINK_IsHalted()
TA260 001:543.065 - 0.512ms returns FALSE
TA260 001:543.070 JLINK_HasError()
TA260 001:546.059 JLINK_IsHalted()
TA260 001:546.513 - 0.453ms returns FALSE
TA260 001:546.526 JLINK_HasError()
TA260 001:548.056 JLINK_IsHalted()
TA260 001:548.507 - 0.450ms returns FALSE
TA260 001:548.513 JLINK_HasError()
TA260 001:550.061 JLINK_IsHalted()
TA260 001:550.610 - 0.548ms returns FALSE
TA260 001:550.616 JLINK_HasError()
TA260 001:552.058 JLINK_IsHalted()
TA260 001:552.508 - 0.450ms returns FALSE
TA260 001:552.515 JLINK_HasError()
TA260 001:554.059 JLINK_IsHalted()
TA260 001:554.462 - 0.403ms returns FALSE
TA260 001:554.474 JLINK_HasError()
TA260 001:555.560 JLINK_IsHalted()
TA260 001:556.127 - 0.565ms returns FALSE
TA260 001:556.144 JLINK_HasError()
TA260 001:557.566 JLINK_IsHalted()
TA260 001:558.078 - 0.511ms returns FALSE
TA260 001:558.094 JLINK_HasError()
TA260 001:559.564 JLINK_IsHalted()
TA260 001:560.034 - 0.470ms returns FALSE
TA260 001:560.042 JLINK_HasError()
TA260 001:561.566 JLINK_IsHalted()
TA260 001:562.053 - 0.486ms returns FALSE
TA260 001:562.059 JLINK_HasError()
TA260 001:563.566 JLINK_IsHalted()
TA260 001:564.017 - 0.450ms returns FALSE
TA260 001:564.025 JLINK_HasError()
TA260 001:565.071 JLINK_IsHalted()
TA260 001:565.698 - 0.626ms returns FALSE
TA260 001:565.716 JLINK_HasError()
TA260 001:570.077 JLINK_IsHalted()
TA260 001:570.597 - 0.519ms returns FALSE
TA260 001:570.604 JLINK_HasError()
TA260 001:572.074 JLINK_IsHalted()
TA260 001:572.568 - 0.494ms returns FALSE
TA260 001:572.575 JLINK_HasError()
TA260 001:574.071 JLINK_IsHalted()
TA260 001:574.510 - 0.439ms returns FALSE
TA260 001:574.517 JLINK_HasError()
TA260 001:575.574 JLINK_IsHalted()
TA260 001:576.073 - 0.497ms returns FALSE
TA260 001:576.092 JLINK_HasError()
TA260 001:577.582 JLINK_IsHalted()
TA260 001:578.069 - 0.487ms returns FALSE
TA260 001:578.077 JLINK_HasError()
TA260 001:579.594 JLINK_IsHalted()
TA260 001:580.056 - 0.462ms returns FALSE
TA260 001:580.069 JLINK_HasError()
TA260 001:581.158 JLINK_IsHalted()
TA260 001:581.601 - 0.442ms returns FALSE
TA260 001:581.609 JLINK_HasError()
TA260 001:582.660 JLINK_IsHalted()
TA260 001:583.149 - 0.488ms returns FALSE
TA260 001:583.159 JLINK_HasError()
TA260 001:584.664 JLINK_IsHalted()
TA260 001:585.114 - 0.450ms returns FALSE
TA260 001:585.122 JLINK_HasError()
TA260 001:586.169 JLINK_IsHalted()
TA260 001:586.736 - 0.565ms returns FALSE
TA260 001:586.743 JLINK_HasError()
TA260 001:588.170 JLINK_IsHalted()
TA260 001:588.706 - 0.535ms returns FALSE
TA260 001:588.717 JLINK_HasError()
TA260 001:590.172 JLINK_IsHalted()
TA260 001:590.704 - 0.532ms returns FALSE
TA260 001:590.713 JLINK_HasError()
TA260 001:592.169 JLINK_IsHalted()
TA260 001:592.691 - 0.522ms returns FALSE
TA260 001:592.697 JLINK_HasError()
TA260 001:594.682 JLINK_IsHalted()
TA260 001:595.154 - 0.471ms returns FALSE
TA260 001:595.169 JLINK_HasError()
TA260 001:596.678 JLINK_IsHalted()
TA260 001:597.194 - 0.514ms returns FALSE
TA260 001:597.214 JLINK_HasError()
TA260 001:598.684 JLINK_IsHalted()
TA260 001:599.180 - 0.495ms returns FALSE
TA260 001:599.191 JLINK_HasError()
TA260 001:600.687 JLINK_IsHalted()
TA260 001:601.163 - 0.475ms returns FALSE
TA260 001:601.178 JLINK_HasError()
TA260 001:602.680 JLINK_IsHalted()
TA260 001:603.144 - 0.464ms returns FALSE
TA260 001:603.151 JLINK_HasError()
TA260 001:604.685 JLINK_IsHalted()
TA260 001:605.218 - 0.531ms returns FALSE
TA260 001:605.237 JLINK_HasError()
TA260 001:607.199 JLINK_IsHalted()
TA260 001:607.694 - 0.494ms returns FALSE
TA260 001:607.702 JLINK_HasError()
TA260 001:609.701 JLINK_IsHalted()
TA260 001:610.157 - 0.456ms returns FALSE
TA260 001:610.164 JLINK_HasError()
TA260 001:612.698 JLINK_IsHalted()
TA260 001:613.186 - 0.487ms returns FALSE
TA260 001:613.201 JLINK_HasError()
TA260 001:615.211 JLINK_IsHalted()
TA260 001:615.708 - 0.495ms returns FALSE
TA260 001:615.720 JLINK_HasError()
TA260 001:618.208 JLINK_IsHalted()
TA260 001:618.705 - 0.496ms returns FALSE
TA260 001:618.712 JLINK_HasError()
TA260 001:621.212 JLINK_IsHalted()
TA260 001:621.836 - 0.624ms returns FALSE
TA260 001:621.847 JLINK_HasError()
TA260 001:623.206 JLINK_IsHalted()
TA260 001:623.688 - 0.482ms returns FALSE
TA260 001:623.698 JLINK_HasError()
TA260 001:625.718 JLINK_IsHalted()
TA260 001:626.247 - 0.529ms returns FALSE
TA260 001:626.262 JLINK_HasError()
TA260 001:629.726 JLINK_IsHalted()
TA260 001:630.222 - 0.495ms returns FALSE
TA260 001:630.232 JLINK_HasError()
TA260 001:632.718 JLINK_IsHalted()
TA260 001:633.182 - 0.463ms returns FALSE
TA260 001:633.188 JLINK_HasError()
TA260 001:635.216 JLINK_IsHalted()
TA260 001:635.699 - 0.483ms returns FALSE
TA260 001:635.706 JLINK_HasError()
TA260 001:637.229 JLINK_IsHalted()
TA260 001:637.797 - 0.568ms returns FALSE
TA260 001:637.809 JLINK_HasError()
TA260 001:640.224 JLINK_IsHalted()
TA260 001:640.692 - 0.467ms returns FALSE
TA260 001:640.699 JLINK_HasError()
TA260 001:642.224 JLINK_IsHalted()
TA260 001:642.694 - 0.469ms returns FALSE
TA260 001:642.703 JLINK_HasError()
TA260 001:644.729 JLINK_IsHalted()
TA260 001:645.250 - 0.520ms returns FALSE
TA260 001:645.261 JLINK_HasError()
TA260 001:646.729 JLINK_IsHalted()
TA260 001:647.206 - 0.475ms returns FALSE
TA260 001:647.218 JLINK_HasError()
TA260 001:648.733 JLINK_IsHalted()
TA260 001:649.159 - 0.426ms returns FALSE
TA260 001:649.166 JLINK_HasError()
TA260 001:650.731 JLINK_IsHalted()
TA260 001:651.193 - 0.461ms returns FALSE
TA260 001:651.199 JLINK_HasError()
TA260 001:652.732 JLINK_IsHalted()
TA260 001:653.144 - 0.412ms returns FALSE
TA260 001:653.150 JLINK_HasError()
TA260 001:654.729 JLINK_IsHalted()
TA260 001:655.233 - 0.503ms returns FALSE
TA260 001:655.239 JLINK_HasError()
TA260 001:657.237 JLINK_IsHalted()
TA260 001:657.762 - 0.523ms returns FALSE
TA260 001:657.777 JLINK_HasError()
TA260 001:659.237 JLINK_IsHalted()
TA260 001:659.789 - 0.551ms returns FALSE
TA260 001:659.802 JLINK_HasError()
TA260 001:661.243 JLINK_IsHalted()
TA260 001:661.704 - 0.461ms returns FALSE
TA260 001:661.712 JLINK_HasError()
TA260 001:663.240 JLINK_IsHalted()
TA260 001:663.740 - 0.499ms returns FALSE
TA260 001:663.747 JLINK_HasError()
TA260 001:665.746 JLINK_IsHalted()
TA260 001:666.217 - 0.470ms returns FALSE
TA260 001:666.228 JLINK_HasError()
TA260 001:667.745 JLINK_IsHalted()
TA260 001:668.219 - 0.474ms returns FALSE
TA260 001:668.226 JLINK_HasError()
TA260 001:669.749 JLINK_IsHalted()
TA260 001:670.215 - 0.465ms returns FALSE
TA260 001:670.221 JLINK_HasError()
TA260 001:671.744 JLINK_IsHalted()
TA260 001:672.260 - 0.516ms returns FALSE
TA260 001:672.268 JLINK_HasError()
TA260 001:673.749 JLINK_IsHalted()
TA260 001:674.283 - 0.533ms returns FALSE
TA260 001:674.289 JLINK_HasError()
TA260 001:676.256 JLINK_IsHalted()
TA260 001:676.787 - 0.529ms returns FALSE
TA260 001:676.801 JLINK_HasError()
TA260 001:679.259 JLINK_IsHalted()
TA260 001:679.727 - 0.467ms returns FALSE
TA260 001:679.735 JLINK_HasError()
TA260 001:681.255 JLINK_IsHalted()
TA260 001:681.757 - 0.501ms returns FALSE
TA260 001:681.770 JLINK_HasError()
TA260 001:683.179 JLINK_IsHalted()
TA260 001:683.679 - 0.500ms returns FALSE
TA260 001:683.688 JLINK_HasError()
TA260 001:685.692 JLINK_IsHalted()
TA260 001:686.165 - 0.472ms returns FALSE
TA260 001:686.182 JLINK_HasError()
TA260 001:687.689 JLINK_IsHalted()
TA260 001:688.148 - 0.459ms returns FALSE
TA260 001:688.158 JLINK_HasError()
TA260 001:689.696 JLINK_IsHalted()
TA260 001:690.174 - 0.478ms returns FALSE
TA260 001:690.181 JLINK_HasError()
TA260 001:691.688 JLINK_IsHalted()
TA260 001:692.162 - 0.472ms returns FALSE
TA260 001:692.171 JLINK_HasError()
TA260 001:693.694 JLINK_IsHalted()
TA260 001:694.166 - 0.471ms returns FALSE
TA260 001:694.185 JLINK_HasError()
TA260 001:696.202 JLINK_IsHalted()
TA260 001:696.686 - 0.483ms returns FALSE
TA260 001:696.705 JLINK_HasError()
TA260 001:697.885 JLINK_IsHalted()
TA260 001:698.381 - 0.495ms returns FALSE
TA260 001:698.389 JLINK_HasError()
TA260 001:700.392 JLINK_IsHalted()
TA260 001:700.864 - 0.472ms returns FALSE
TA260 001:700.873 JLINK_HasError()
TA260 001:702.387 JLINK_IsHalted()
TA260 001:702.848 - 0.461ms returns FALSE
TA260 001:702.854 JLINK_HasError()
TA260 001:704.045 JLINK_IsHalted()
TA260 001:704.462 - 0.416ms returns FALSE
TA260 001:704.469 JLINK_HasError()
TA260 001:706.068 JLINK_IsHalted()
TA260 001:706.606 - 0.538ms returns FALSE
TA260 001:706.618 JLINK_HasError()
TA260 001:708.563 JLINK_IsHalted()
TA260 001:709.070 - 0.506ms returns FALSE
TA260 001:709.078 JLINK_HasError()
TA260 001:710.610 JLINK_IsHalted()
TA260 001:711.113 - 0.502ms returns FALSE
TA260 001:711.122 JLINK_HasError()
TA260 001:712.611 JLINK_IsHalted()
TA260 001:713.076 - 0.464ms returns FALSE
TA260 001:713.085 JLINK_HasError()
TA260 001:715.114 JLINK_IsHalted()
TA260 001:715.608 - 0.494ms returns FALSE
TA260 001:715.615 JLINK_HasError()
TA260 001:717.130 JLINK_IsHalted()
TA260 001:717.666 - 0.535ms returns FALSE
TA260 001:717.681 JLINK_HasError()
TA260 001:719.124 JLINK_IsHalted()
TA260 001:719.611 - 0.487ms returns FALSE
TA260 001:719.617 JLINK_HasError()
TA260 001:721.125 JLINK_IsHalted()
TA260 001:721.615 - 0.489ms returns FALSE
TA260 001:721.626 JLINK_HasError()
TA260 001:723.124 JLINK_IsHalted()
TA260 001:723.614 - 0.490ms returns FALSE
TA260 001:723.623 JLINK_HasError()
TA260 001:725.123 JLINK_IsHalted()
TA260 001:725.612 - 0.488ms returns FALSE
TA260 001:725.623 JLINK_HasError()
TA260 001:727.629 JLINK_IsHalted()
TA260 001:728.055 - 0.426ms returns FALSE
TA260 001:728.062 JLINK_HasError()
TA260 001:729.630 JLINK_IsHalted()
TA260 001:730.119 - 0.489ms returns FALSE
TA260 001:730.128 JLINK_HasError()
TA260 001:731.632 JLINK_IsHalted()
TA260 001:732.134 - 0.502ms returns FALSE
TA260 001:732.143 JLINK_HasError()
TA260 001:733.627 JLINK_IsHalted()
TA260 001:734.142 - 0.505ms returns FALSE
TA260 001:734.152 JLINK_HasError()
TA260 001:736.139 JLINK_IsHalted()
TA260 001:736.637 - 0.497ms returns FALSE
TA260 001:736.649 JLINK_HasError()
TA260 001:738.133 JLINK_IsHalted()
TA260 001:738.605 - 0.471ms returns FALSE
TA260 001:738.614 JLINK_HasError()
TA260 001:740.140 JLINK_IsHalted()
TA260 001:740.613 - 0.473ms returns FALSE
TA260 001:740.622 JLINK_HasError()
TA260 001:742.139 JLINK_IsHalted()
TA260 001:742.615 - 0.476ms returns FALSE
TA260 001:742.622 JLINK_HasError()
TA260 001:744.645 JLINK_IsHalted()
TA260 001:745.162 - 0.516ms returns FALSE
TA260 001:745.173 JLINK_HasError()
TA260 001:746.641 JLINK_IsHalted()
TA260 001:747.157 - 0.515ms returns FALSE
TA260 001:747.166 JLINK_HasError()
TA260 001:748.552 JLINK_IsHalted()
TA260 001:749.019 - 0.467ms returns FALSE
TA260 001:749.027 JLINK_HasError()
TA260 001:751.059 JLINK_IsHalted()
TA260 001:751.597 - 0.538ms returns FALSE
TA260 001:751.604 JLINK_HasError()
TA260 001:753.060 JLINK_IsHalted()
TA260 001:753.496 - 0.436ms returns FALSE
TA260 001:753.511 JLINK_HasError()
TA260 001:755.059 JLINK_IsHalted()
TA260 001:755.510 - 0.450ms returns FALSE
TA260 001:755.518 JLINK_HasError()
TA260 001:756.566 JLINK_IsHalted()
TA260 001:757.053 - 0.486ms returns FALSE
TA260 001:757.060 JLINK_HasError()
TA260 001:758.567 JLINK_IsHalted()
TA260 001:759.055 - 0.488ms returns FALSE
TA260 001:759.068 JLINK_HasError()
TA260 001:760.332 JLINK_IsHalted()
TA260 001:760.804 - 0.472ms returns FALSE
TA260 001:760.815 JLINK_HasError()
TA260 001:762.006 JLINK_IsHalted()
TA260 001:762.509 - 0.503ms returns FALSE
TA260 001:762.516 JLINK_HasError()
TA260 001:764.015 JLINK_IsHalted()
TA260 001:764.466 - 0.450ms returns FALSE
TA260 001:764.478 JLINK_HasError()
TA260 001:766.522 JLINK_IsHalted()
TA260 001:766.996 - 0.472ms returns FALSE
TA260 001:767.015 JLINK_HasError()
TA260 001:768.519 JLINK_IsHalted()
TA260 001:768.987 - 0.467ms returns FALSE
TA260 001:768.996 JLINK_HasError()
TA260 001:770.521 JLINK_IsHalted()
TA260 001:770.977 - 0.455ms returns FALSE
TA260 001:770.987 JLINK_HasError()
TA260 001:772.517 JLINK_IsHalted()
TA260 001:772.973 - 0.456ms returns FALSE
TA260 001:772.981 JLINK_HasError()
TA260 001:775.033 JLINK_IsHalted()
TA260 001:775.544 - 0.510ms returns FALSE
TA260 001:775.551 JLINK_HasError()
TA260 001:777.029 JLINK_IsHalted()
TA260 001:777.513 - 0.484ms returns FALSE
TA260 001:777.523 JLINK_HasError()
TA260 001:779.023 JLINK_IsHalted()
TA260 001:779.511 - 0.486ms returns FALSE
TA260 001:779.520 JLINK_HasError()
TA260 001:781.030 JLINK_IsHalted()
TA260 001:781.511 - 0.480ms returns FALSE
TA260 001:781.518 JLINK_HasError()
TA260 001:783.022 JLINK_IsHalted()
TA260 001:783.547 - 0.524ms returns FALSE
TA260 001:783.556 JLINK_HasError()
TA260 001:785.027 JLINK_IsHalted()
TA260 001:785.466 - 0.438ms returns FALSE
TA260 001:785.475 JLINK_HasError()
TA260 001:787.540 JLINK_IsHalted()
TA260 001:788.020 - 0.479ms returns FALSE
TA260 001:788.027 JLINK_HasError()
TA260 001:789.535 JLINK_IsHalted()
TA260 001:790.037 - 0.502ms returns FALSE
TA260 001:790.045 JLINK_HasError()
TA260 001:791.537 JLINK_IsHalted()
TA260 001:791.993 - 0.455ms returns FALSE
TA260 001:792.004 JLINK_HasError()
TA260 001:793.155 JLINK_IsHalted()
TA260 001:793.647 - 0.492ms returns FALSE
TA260 001:793.655 JLINK_HasError()
TA260 001:795.670 JLINK_IsHalted()
TA260 001:796.209 - 0.538ms returns FALSE
TA260 001:796.220 JLINK_HasError()
TA260 001:797.670 JLINK_IsHalted()
TA260 001:798.111 - 0.440ms returns FALSE
TA260 001:798.117 JLINK_HasError()
TA260 001:799.671 JLINK_IsHalted()
TA260 001:800.160 - 0.488ms returns FALSE
TA260 001:800.169 JLINK_HasError()
TA260 001:801.669 JLINK_IsHalted()
TA260 001:802.155 - 0.485ms returns FALSE
TA260 001:802.166 JLINK_HasError()
TA260 001:803.668 JLINK_IsHalted()
TA260 001:804.149 - 0.480ms returns FALSE
TA260 001:804.156 JLINK_HasError()
TA260 001:806.175 JLINK_IsHalted()
TA260 001:806.694 - 0.517ms returns FALSE
TA260 001:806.706 JLINK_HasError()
TA260 001:808.681 JLINK_IsHalted()
TA260 001:809.215 - 0.534ms returns FALSE
TA260 001:809.224 JLINK_HasError()
TA260 001:810.685 JLINK_IsHalted()
TA260 001:811.218 - 0.532ms returns FALSE
TA260 001:811.227 JLINK_HasError()
TA260 001:812.680 JLINK_IsHalted()
TA260 001:813.168 - 0.487ms returns FALSE
TA260 001:813.178 JLINK_HasError()
TA260 001:814.682 JLINK_IsHalted()
TA260 001:815.191 - 0.509ms returns FALSE
TA260 001:815.198 JLINK_HasError()
TA260 001:817.191 JLINK_IsHalted()
TA260 001:817.697 - 0.505ms returns FALSE
TA260 001:817.703 JLINK_HasError()
TA260 001:819.186 JLINK_IsHalted()
TA260 001:819.693 - 0.507ms returns FALSE
TA260 001:819.702 JLINK_HasError()
TA260 001:821.190 JLINK_IsHalted()
TA260 001:823.567   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:824.080 - 2.890ms returns TRUE
TA260 001:824.088 JLINK_ReadReg(R15 (PC))
TA260 001:824.095 - 0.006ms returns 0x20000000
TA260 001:824.099 JLINK_ClrBPEx(BPHandle = 0x00000007)
TA260 001:824.103 - 0.004ms returns 0x00
TA260 001:824.108 JLINK_ReadReg(R0)
TA260 001:824.111 - 0.003ms returns 0x00000000
TA260 001:824.542 JLINK_HasError()
TA260 001:824.551 JLINK_WriteReg(R0, 0x0800C000)
TA260 001:824.556 - 0.004ms returns 0
TA260 001:824.560 JLINK_WriteReg(R1, 0x00004000)
TA260 001:824.564 - 0.003ms returns 0
TA260 001:824.568 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:824.572 - 0.003ms returns 0
TA260 001:824.576 JLINK_WriteReg(R3, 0x00000000)
TA260 001:824.579 - 0.003ms returns 0
TA260 001:824.583 JLINK_WriteReg(R4, 0x00000000)
TA260 001:824.587 - 0.003ms returns 0
TA260 001:824.591 JLINK_WriteReg(R5, 0x00000000)
TA260 001:824.594 - 0.003ms returns 0
TA260 001:824.598 JLINK_WriteReg(R6, 0x00000000)
TA260 001:824.602 - 0.003ms returns 0
TA260 001:824.606 JLINK_WriteReg(R7, 0x00000000)
TA260 001:824.610 - 0.003ms returns 0
TA260 001:824.614 JLINK_WriteReg(R8, 0x00000000)
TA260 001:824.617 - 0.003ms returns 0
TA260 001:824.621 JLINK_WriteReg(R9, 0x20000180)
TA260 001:824.625 - 0.003ms returns 0
TA260 001:824.635 JLINK_WriteReg(R10, 0x00000000)
TA260 001:824.638 - 0.003ms returns 0
TA260 001:824.642 JLINK_WriteReg(R11, 0x00000000)
TA260 001:824.645 - 0.003ms returns 0
TA260 001:824.649 JLINK_WriteReg(R12, 0x00000000)
TA260 001:824.653 - 0.003ms returns 0
TA260 001:824.657 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:824.661 - 0.003ms returns 0
TA260 001:824.665 JLINK_WriteReg(R14, 0x20000001)
TA260 001:824.668 - 0.003ms returns 0
TA260 001:824.673 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 001:824.676 - 0.003ms returns 0
TA260 001:824.680 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:824.684 - 0.003ms returns 0
TA260 001:824.688 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:824.691 - 0.003ms returns 0
TA260 001:824.695 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:824.699 - 0.003ms returns 0
TA260 001:824.703 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:824.706 - 0.003ms returns 0
TA260 001:824.711 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:824.716 - 0.005ms returns 0x00000008
TA260 001:824.720 JLINK_Go()
TA260 001:824.729   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:827.481 - 2.760ms 
TA260 001:827.504 JLINK_IsHalted()
TA260 001:829.881   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:830.354 - 2.849ms returns TRUE
TA260 001:830.364 JLINK_ReadReg(R15 (PC))
TA260 001:830.369 - 0.005ms returns 0x20000000
TA260 001:830.374 JLINK_ClrBPEx(BPHandle = 0x00000008)
TA260 001:830.378 - 0.004ms returns 0x00
TA260 001:830.382 JLINK_ReadReg(R0)
TA260 001:830.386 - 0.003ms returns 0x00000001
TA260 001:830.391 JLINK_HasError()
TA260 001:830.396 JLINK_WriteReg(R0, 0x0800C000)
TA260 001:830.400 - 0.003ms returns 0
TA260 001:830.404 JLINK_WriteReg(R1, 0x00004000)
TA260 001:830.408 - 0.003ms returns 0
TA260 001:830.412 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:830.415 - 0.003ms returns 0
TA260 001:830.419 JLINK_WriteReg(R3, 0x00000000)
TA260 001:830.423 - 0.003ms returns 0
TA260 001:830.427 JLINK_WriteReg(R4, 0x00000000)
TA260 001:830.431 - 0.003ms returns 0
TA260 001:830.435 JLINK_WriteReg(R5, 0x00000000)
TA260 001:830.438 - 0.003ms returns 0
TA260 001:830.442 JLINK_WriteReg(R6, 0x00000000)
TA260 001:830.445 - 0.003ms returns 0
TA260 001:830.449 JLINK_WriteReg(R7, 0x00000000)
TA260 001:830.453 - 0.003ms returns 0
TA260 001:830.457 JLINK_WriteReg(R8, 0x00000000)
TA260 001:830.461 - 0.004ms returns 0
TA260 001:830.465 JLINK_WriteReg(R9, 0x20000180)
TA260 001:830.468 - 0.003ms returns 0
TA260 001:830.472 JLINK_WriteReg(R10, 0x00000000)
TA260 001:830.476 - 0.003ms returns 0
TA260 001:830.480 JLINK_WriteReg(R11, 0x00000000)
TA260 001:830.483 - 0.003ms returns 0
TA260 001:830.487 JLINK_WriteReg(R12, 0x00000000)
TA260 001:830.490 - 0.003ms returns 0
TA260 001:830.495 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:830.498 - 0.003ms returns 0
TA260 001:830.502 JLINK_WriteReg(R14, 0x20000001)
TA260 001:830.506 - 0.003ms returns 0
TA260 001:830.510 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 001:830.513 - 0.003ms returns 0
TA260 001:830.517 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:830.521 - 0.003ms returns 0
TA260 001:830.525 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:830.528 - 0.003ms returns 0
TA260 001:830.532 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:830.536 - 0.003ms returns 0
TA260 001:830.540 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:830.543 - 0.003ms returns 0
TA260 001:830.548 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:830.552 - 0.004ms returns 0x00000009
TA260 001:830.556 JLINK_Go()
TA260 001:830.564   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:833.398 - 2.841ms 
TA260 001:833.413 JLINK_IsHalted()
TA260 001:833.896 - 0.482ms returns FALSE
TA260 001:833.906 JLINK_HasError()
TA260 001:835.196 JLINK_IsHalted()
TA260 001:835.656 - 0.460ms returns FALSE
TA260 001:835.663 JLINK_HasError()
TA260 001:837.210 JLINK_IsHalted()
TA260 001:837.737 - 0.527ms returns FALSE
TA260 001:837.752 JLINK_HasError()
TA260 001:839.201 JLINK_IsHalted()
TA260 001:839.695 - 0.493ms returns FALSE
TA260 001:839.702 JLINK_HasError()
TA260 001:841.205 JLINK_IsHalted()
TA260 001:841.692 - 0.487ms returns FALSE
TA260 001:841.699 JLINK_HasError()
TA260 001:843.204 JLINK_IsHalted()
TA260 001:843.698 - 0.493ms returns FALSE
TA260 001:843.712 JLINK_HasError()
TA260 001:845.714 JLINK_IsHalted()
TA260 001:846.206 - 0.491ms returns FALSE
TA260 001:846.221 JLINK_HasError()
TA260 001:847.715 JLINK_IsHalted()
TA260 001:848.261 - 0.546ms returns FALSE
TA260 001:848.268 JLINK_HasError()
TA260 001:849.712 JLINK_IsHalted()
TA260 001:850.144 - 0.432ms returns FALSE
TA260 001:850.153 JLINK_HasError()
TA260 001:851.709 JLINK_IsHalted()
TA260 001:852.203 - 0.494ms returns FALSE
TA260 001:852.211 JLINK_HasError()
TA260 001:853.710 JLINK_IsHalted()
TA260 001:854.203 - 0.492ms returns FALSE
TA260 001:854.210 JLINK_HasError()
TA260 001:856.228 JLINK_IsHalted()
TA260 001:856.781 - 0.552ms returns FALSE
TA260 001:856.788 JLINK_HasError()
TA260 001:858.222 JLINK_IsHalted()
TA260 001:858.694 - 0.472ms returns FALSE
TA260 001:858.708 JLINK_HasError()
TA260 001:860.224 JLINK_IsHalted()
TA260 001:860.716 - 0.491ms returns FALSE
TA260 001:860.732 JLINK_HasError()
TA260 001:862.220 JLINK_IsHalted()
TA260 001:862.690 - 0.470ms returns FALSE
TA260 001:862.700 JLINK_HasError()
TA260 001:864.725 JLINK_IsHalted()
TA260 001:865.166 - 0.441ms returns FALSE
TA260 001:865.175 JLINK_HasError()
TA260 001:866.725 JLINK_IsHalted()
TA260 001:867.209 - 0.483ms returns FALSE
TA260 001:867.225 JLINK_HasError()
TA260 001:868.730 JLINK_IsHalted()
TA260 001:869.238 - 0.508ms returns FALSE
TA260 001:869.245 JLINK_HasError()
TA260 001:870.730 JLINK_IsHalted()
TA260 001:871.228 - 0.497ms returns FALSE
TA260 001:871.238 JLINK_HasError()
TA260 001:872.727 JLINK_IsHalted()
TA260 001:873.248 - 0.520ms returns FALSE
TA260 001:873.255 JLINK_HasError()
TA260 001:874.726 JLINK_IsHalted()
TA260 001:875.262 - 0.535ms returns FALSE
TA260 001:875.270 JLINK_HasError()
TA260 001:877.237 JLINK_IsHalted()
TA260 001:877.724 - 0.487ms returns FALSE
TA260 001:877.740 JLINK_HasError()
TA260 001:879.239 JLINK_IsHalted()
TA260 001:879.783 - 0.544ms returns FALSE
TA260 001:879.795 JLINK_HasError()
TA260 001:881.789 JLINK_IsHalted()
TA260 001:882.277 - 0.487ms returns FALSE
TA260 001:882.292 JLINK_HasError()
TA260 001:883.781 JLINK_IsHalted()
TA260 001:884.283 - 0.501ms returns FALSE
TA260 001:884.291 JLINK_HasError()
TA260 001:886.292 JLINK_IsHalted()
TA260 001:886.750 - 0.457ms returns FALSE
TA260 001:886.762 JLINK_HasError()
TA260 001:888.294 JLINK_IsHalted()
TA260 001:888.759 - 0.465ms returns FALSE
TA260 001:888.767 JLINK_HasError()
TA260 001:890.287 JLINK_IsHalted()
TA260 001:890.748 - 0.461ms returns FALSE
TA260 001:890.757 JLINK_HasError()
TA260 001:892.293 JLINK_IsHalted()
TA260 001:892.831 - 0.538ms returns FALSE
TA260 001:892.841 JLINK_HasError()
TA260 001:894.797 JLINK_IsHalted()
TA260 001:895.322 - 0.524ms returns FALSE
TA260 001:895.340 JLINK_HasError()
TA260 001:896.796 JLINK_IsHalted()
TA260 001:897.256 - 0.459ms returns FALSE
TA260 001:897.275 JLINK_HasError()
TA260 001:898.795 JLINK_IsHalted()
TA260 001:899.270 - 0.475ms returns FALSE
TA260 001:899.278 JLINK_HasError()
TA260 001:900.803 JLINK_IsHalted()
TA260 001:901.306 - 0.503ms returns FALSE
TA260 001:901.316 JLINK_HasError()
TA260 001:902.798 JLINK_IsHalted()
TA260 001:903.329 - 0.530ms returns FALSE
TA260 001:903.338 JLINK_HasError()
TA260 001:904.799 JLINK_IsHalted()
TA260 001:905.285 - 0.485ms returns FALSE
TA260 001:905.293 JLINK_HasError()
TA260 001:907.309 JLINK_IsHalted()
TA260 001:907.809 - 0.500ms returns FALSE
TA260 001:907.822 JLINK_HasError()
TA260 001:910.387 JLINK_IsHalted()
TA260 001:910.943 - 0.555ms returns FALSE
TA260 001:910.955 JLINK_HasError()
TA260 001:912.382 JLINK_IsHalted()
TA260 001:912.852 - 0.469ms returns FALSE
TA260 001:912.859 JLINK_HasError()
TA260 001:914.892 JLINK_IsHalted()
TA260 001:915.474 - 0.582ms returns FALSE
TA260 001:915.486 JLINK_HasError()
TA260 001:917.399 JLINK_IsHalted()
TA260 001:917.948 - 0.548ms returns FALSE
TA260 001:917.962 JLINK_HasError()
TA260 001:919.401 JLINK_IsHalted()
TA260 001:919.864 - 0.463ms returns FALSE
TA260 001:919.872 JLINK_HasError()
TA260 001:921.396 JLINK_IsHalted()
TA260 001:921.907 - 0.511ms returns FALSE
TA260 001:921.918 JLINK_HasError()
TA260 001:923.399 JLINK_IsHalted()
TA260 001:923.943 - 0.544ms returns FALSE
TA260 001:923.953 JLINK_HasError()
TA260 001:925.908 JLINK_IsHalted()
TA260 001:926.379 - 0.470ms returns FALSE
TA260 001:926.392 JLINK_HasError()
TA260 001:928.911 JLINK_IsHalted()
TA260 001:929.474 - 0.563ms returns FALSE
TA260 001:929.481 JLINK_HasError()
TA260 001:930.919 JLINK_IsHalted()
TA260 001:931.398 - 0.479ms returns FALSE
TA260 001:931.405 JLINK_HasError()
TA260 001:932.908 JLINK_IsHalted()
TA260 001:933.363 - 0.455ms returns FALSE
TA260 001:933.371 JLINK_HasError()
TA260 001:934.909 JLINK_IsHalted()
TA260 001:935.372 - 0.462ms returns FALSE
TA260 001:935.380 JLINK_HasError()
TA260 001:937.414 JLINK_IsHalted()
TA260 001:937.848 - 0.433ms returns FALSE
TA260 001:937.854 JLINK_HasError()
TA260 001:939.450 JLINK_IsHalted()
TA260 001:939.921 - 0.471ms returns FALSE
TA260 001:939.932 JLINK_HasError()
TA260 001:941.425 JLINK_IsHalted()
TA260 001:941.955 - 0.530ms returns FALSE
TA260 001:941.963 JLINK_HasError()
TA260 001:943.414 JLINK_IsHalted()
TA260 001:943.865 - 0.450ms returns FALSE
TA260 001:943.876 JLINK_HasError()
TA260 001:944.924 JLINK_IsHalted()
TA260 001:945.475 - 0.551ms returns FALSE
TA260 001:945.483 JLINK_HasError()
TA260 001:946.924 JLINK_IsHalted()
TA260 001:947.481 - 0.555ms returns FALSE
TA260 001:947.490 JLINK_HasError()
TA260 001:948.918 JLINK_IsHalted()
TA260 001:949.479 - 0.560ms returns FALSE
TA260 001:949.533 JLINK_HasError()
TA260 001:950.925 JLINK_IsHalted()
TA260 001:951.375 - 0.449ms returns FALSE
TA260 001:951.388 JLINK_HasError()
TA260 001:952.920 JLINK_IsHalted()
TA260 001:953.473 - 0.553ms returns FALSE
TA260 001:953.481 JLINK_HasError()
TA260 001:954.927 JLINK_IsHalted()
TA260 001:955.477 - 0.549ms returns FALSE
TA260 001:955.492 JLINK_HasError()
TA260 001:957.450 JLINK_IsHalted()
TA260 001:957.941 - 0.491ms returns FALSE
TA260 001:957.949 JLINK_HasError()
TA260 001:959.428 JLINK_IsHalted()
TA260 001:959.930 - 0.501ms returns FALSE
TA260 001:959.938 JLINK_HasError()
TA260 001:961.433 JLINK_IsHalted()
TA260 001:961.943 - 0.509ms returns FALSE
TA260 001:961.950 JLINK_HasError()
TA260 001:963.433 JLINK_IsHalted()
TA260 001:963.894 - 0.460ms returns FALSE
TA260 001:963.901 JLINK_HasError()
TA260 001:965.938 JLINK_IsHalted()
TA260 001:966.473 - 0.535ms returns FALSE
TA260 001:966.482 JLINK_HasError()
TA260 001:967.937 JLINK_IsHalted()
TA260 001:968.384 - 0.446ms returns FALSE
TA260 001:968.392 JLINK_HasError()
TA260 001:969.939 JLINK_IsHalted()
TA260 001:970.474 - 0.535ms returns FALSE
TA260 001:970.484 JLINK_HasError()
TA260 001:971.939 JLINK_IsHalted()
TA260 001:972.466 - 0.526ms returns FALSE
TA260 001:972.474 JLINK_HasError()
TA260 001:973.939 JLINK_IsHalted()
TA260 001:974.471 - 0.532ms returns FALSE
TA260 001:974.478 JLINK_HasError()
TA260 001:976.144 JLINK_IsHalted()
TA260 001:976.696 - 0.551ms returns FALSE
TA260 001:976.708 JLINK_HasError()
TA260 001:978.642 JLINK_IsHalted()
TA260 001:979.115 - 0.472ms returns FALSE
TA260 001:979.122 JLINK_HasError()
TA260 001:980.635 JLINK_IsHalted()
TA260 001:981.111 - 0.476ms returns FALSE
TA260 001:981.120 JLINK_HasError()
TA260 001:982.640 JLINK_IsHalted()
TA260 001:983.103 - 0.461ms returns FALSE
TA260 001:983.115 JLINK_HasError()
TA260 001:984.290 JLINK_IsHalted()
TA260 001:984.781 - 0.491ms returns FALSE
TA260 001:984.789 JLINK_HasError()
TA260 001:986.383 JLINK_IsHalted()
TA260 001:986.849 - 0.465ms returns FALSE
TA260 001:986.857 JLINK_HasError()
TA260 001:988.410 JLINK_IsHalted()
TA260 001:988.933 - 0.522ms returns FALSE
TA260 001:988.939 JLINK_HasError()
TA260 001:990.471 JLINK_IsHalted()
TA260 001:990.928 - 0.456ms returns FALSE
TA260 001:990.935 JLINK_HasError()
TA260 001:991.977 JLINK_IsHalted()
TA260 001:992.476 - 0.498ms returns FALSE
TA260 001:992.486 JLINK_HasError()
TA260 001:993.983 JLINK_IsHalted()
TA260 001:994.496 - 0.512ms returns FALSE
TA260 001:994.504 JLINK_HasError()
TA260 001:996.006 JLINK_IsHalted()
TA260 001:996.473 - 0.467ms returns FALSE
TA260 001:996.481 JLINK_HasError()
TA260 001:998.497 JLINK_IsHalted()
TA260 001:998.964 - 0.466ms returns FALSE
TA260 001:998.970 JLINK_HasError()
TA260 002:000.997 JLINK_IsHalted()
TA260 002:001.513 - 0.516ms returns FALSE
TA260 002:001.524 JLINK_HasError()
TA260 002:002.999 JLINK_IsHalted()
TA260 002:003.473 - 0.474ms returns FALSE
TA260 002:003.481 JLINK_HasError()
TA260 002:004.997 JLINK_IsHalted()
TA260 002:005.508 - 0.510ms returns FALSE
TA260 002:005.514 JLINK_HasError()
TA260 002:007.504 JLINK_IsHalted()
TA260 002:007.975 - 0.470ms returns FALSE
TA260 002:007.982 JLINK_HasError()
TA260 002:010.008 JLINK_IsHalted()
TA260 002:010.464 - 0.456ms returns FALSE
TA260 002:010.472 JLINK_HasError()
TA260 002:012.006 JLINK_IsHalted()
TA260 002:012.498 - 0.492ms returns FALSE
TA260 002:012.514 JLINK_HasError()
TA260 002:014.009 JLINK_IsHalted()
TA260 002:014.464 - 0.455ms returns FALSE
TA260 002:014.471 JLINK_HasError()
TA260 002:015.511 JLINK_IsHalted()
TA260 002:015.977 - 0.466ms returns FALSE
TA260 002:015.985 JLINK_HasError()
TA260 002:017.525 JLINK_IsHalted()
TA260 002:017.988 - 0.462ms returns FALSE
TA260 002:017.994 JLINK_HasError()
TA260 002:020.524 JLINK_IsHalted()
TA260 002:021.058 - 0.534ms returns FALSE
TA260 002:021.066 JLINK_HasError()
TA260 002:023.065 JLINK_IsHalted()
TA260 002:023.615 - 0.550ms returns FALSE
TA260 002:023.623 JLINK_HasError()
TA260 002:025.071 JLINK_IsHalted()
TA260 002:025.600 - 0.528ms returns FALSE
TA260 002:025.607 JLINK_HasError()
TA260 002:027.580 JLINK_IsHalted()
TA260 002:028.067 - 0.486ms returns FALSE
TA260 002:028.076 JLINK_HasError()
TA260 002:029.581 JLINK_IsHalted()
TA260 002:030.112 - 0.531ms returns FALSE
TA260 002:030.122 JLINK_HasError()
TA260 002:031.579 JLINK_IsHalted()
TA260 002:032.062 - 0.481ms returns FALSE
TA260 002:032.080 JLINK_HasError()
TA260 002:033.577 JLINK_IsHalted()
TA260 002:033.999 - 0.421ms returns FALSE
TA260 002:034.006 JLINK_HasError()
TA260 002:035.092 JLINK_IsHalted()
TA260 002:035.683 - 0.591ms returns FALSE
TA260 002:035.690 JLINK_HasError()
TA260 002:037.087 JLINK_IsHalted()
TA260 002:037.543 - 0.455ms returns FALSE
TA260 002:037.561 JLINK_HasError()
TA260 002:039.081 JLINK_IsHalted()
TA260 002:039.538 - 0.457ms returns FALSE
TA260 002:039.544 JLINK_HasError()
TA260 002:041.091 JLINK_IsHalted()
TA260 002:041.500 - 0.409ms returns FALSE
TA260 002:041.510 JLINK_HasError()
TA260 002:043.086 JLINK_IsHalted()
TA260 002:043.610 - 0.524ms returns FALSE
TA260 002:043.618 JLINK_HasError()
TA260 002:045.092 JLINK_IsHalted()
TA260 002:045.661 - 0.569ms returns FALSE
TA260 002:045.672 JLINK_HasError()
TA260 002:047.600 JLINK_IsHalted()
TA260 002:048.130 - 0.530ms returns FALSE
TA260 002:048.137 JLINK_HasError()
TA260 002:049.597 JLINK_IsHalted()
TA260 002:050.022 - 0.425ms returns FALSE
TA260 002:050.030 JLINK_HasError()
TA260 002:051.599 JLINK_IsHalted()
TA260 002:052.070 - 0.471ms returns FALSE
TA260 002:052.080 JLINK_HasError()
TA260 002:053.594 JLINK_IsHalted()
TA260 002:054.113 - 0.519ms returns FALSE
TA260 002:054.120 JLINK_HasError()
TA260 002:056.103 JLINK_IsHalted()
TA260 002:056.612 - 0.509ms returns FALSE
TA260 002:056.623 JLINK_HasError()
TA260 002:058.105 JLINK_IsHalted()
TA260 002:058.612 - 0.506ms returns FALSE
TA260 002:058.618 JLINK_HasError()
TA260 002:060.011 JLINK_IsHalted()
TA260 002:060.514 - 0.503ms returns FALSE
TA260 002:060.545 JLINK_HasError()
TA260 002:062.519 JLINK_IsHalted()
TA260 002:063.060 - 0.540ms returns FALSE
TA260 002:063.076 JLINK_HasError()
TA260 002:065.022 JLINK_IsHalted()
TA260 002:065.495 - 0.473ms returns FALSE
TA260 002:065.502 JLINK_HasError()
TA260 002:067.026 JLINK_IsHalted()
TA260 002:067.498 - 0.471ms returns FALSE
TA260 002:067.513 JLINK_HasError()
TA260 002:069.025 JLINK_IsHalted()
TA260 002:069.472 - 0.446ms returns FALSE
TA260 002:069.482 JLINK_HasError()
TA260 002:071.030 JLINK_IsHalted()
TA260 002:071.598 - 0.568ms returns FALSE
TA260 002:071.608 JLINK_HasError()
TA260 002:073.027 JLINK_IsHalted()
TA260 002:073.500 - 0.472ms returns FALSE
TA260 002:073.511 JLINK_HasError()
TA260 002:075.030 JLINK_IsHalted()
TA260 002:075.546 - 0.515ms returns FALSE
TA260 002:075.554 JLINK_HasError()
TA260 002:077.534 JLINK_IsHalted()
TA260 002:078.092 - 0.557ms returns FALSE
TA260 002:078.100 JLINK_HasError()
TA260 002:079.533 JLINK_IsHalted()
TA260 002:079.994 - 0.460ms returns FALSE
TA260 002:080.003 JLINK_HasError()
TA260 002:085.044 JLINK_IsHalted()
TA260 002:085.543 - 0.499ms returns FALSE
TA260 002:085.550 JLINK_HasError()
TA260 002:087.082 JLINK_IsHalted()
TA260 002:087.605 - 0.522ms returns FALSE
TA260 002:087.621 JLINK_HasError()
TA260 002:089.045 JLINK_IsHalted()
TA260 002:089.614 - 0.569ms returns FALSE
TA260 002:089.624 JLINK_HasError()
TA260 002:091.042 JLINK_IsHalted()
TA260 002:091.630 - 0.587ms returns FALSE
TA260 002:091.639 JLINK_HasError()
TA260 002:093.043 JLINK_IsHalted()
TA260 002:093.496 - 0.452ms returns FALSE
TA260 002:093.506 JLINK_HasError()
TA260 002:095.047 JLINK_IsHalted()
TA260 002:095.561 - 0.513ms returns FALSE
TA260 002:095.578 JLINK_HasError()
TA260 002:097.556 JLINK_IsHalted()
TA260 002:098.023 - 0.467ms returns FALSE
TA260 002:098.030 JLINK_HasError()
TA260 002:099.556 JLINK_IsHalted()
TA260 002:099.998 - 0.442ms returns FALSE
TA260 002:100.007 JLINK_HasError()
TA260 002:101.551 JLINK_IsHalted()
TA260 002:102.043 - 0.492ms returns FALSE
TA260 002:102.051 JLINK_HasError()
TA260 002:103.551 JLINK_IsHalted()
TA260 002:104.021 - 0.469ms returns FALSE
TA260 002:104.027 JLINK_HasError()
TA260 002:106.060 JLINK_IsHalted()
TA260 002:106.613 - 0.552ms returns FALSE
TA260 002:106.627 JLINK_HasError()
TA260 002:108.565 JLINK_IsHalted()
TA260 002:109.111 - 0.545ms returns FALSE
TA260 002:109.117 JLINK_HasError()
TA260 002:110.358 JLINK_IsHalted()
TA260 002:110.829 - 0.470ms returns FALSE
TA260 002:110.837 JLINK_HasError()
TA260 002:112.862 JLINK_IsHalted()
TA260 002:113.319 - 0.456ms returns FALSE
TA260 002:113.337 JLINK_HasError()
TA260 002:115.018 JLINK_IsHalted()
TA260 002:115.472 - 0.453ms returns FALSE
TA260 002:115.479 JLINK_HasError()
TA260 002:117.019 JLINK_IsHalted()
TA260 002:117.472 - 0.452ms returns FALSE
TA260 002:117.478 JLINK_HasError()
TA260 002:119.018 JLINK_IsHalted()
TA260 002:119.508 - 0.490ms returns FALSE
TA260 002:119.514 JLINK_HasError()
TA260 002:121.021 JLINK_IsHalted()
TA260 002:121.510 - 0.488ms returns FALSE
TA260 002:121.517 JLINK_HasError()
TA260 002:123.554 JLINK_IsHalted()
TA260 002:124.056 - 0.501ms returns FALSE
TA260 002:124.062 JLINK_HasError()
TA260 002:126.069 JLINK_IsHalted()
TA260 002:126.617 - 0.547ms returns FALSE
TA260 002:126.632 JLINK_HasError()
TA260 002:129.066 JLINK_IsHalted()
TA260 002:129.615 - 0.548ms returns FALSE
TA260 002:129.622 JLINK_HasError()
TA260 002:131.064 JLINK_IsHalted()
TA260 002:131.512 - 0.448ms returns FALSE
TA260 002:131.520 JLINK_HasError()
TA260 002:133.064 JLINK_IsHalted()
TA260 002:133.511 - 0.447ms returns FALSE
TA260 002:133.520 JLINK_HasError()
TA260 002:135.063 JLINK_IsHalted()
TA260 002:135.496 - 0.433ms returns FALSE
TA260 002:135.503 JLINK_HasError()
TA260 002:137.516 JLINK_IsHalted()
TA260 002:137.973 - 0.456ms returns FALSE
TA260 002:137.979 JLINK_HasError()
TA260 002:139.517 JLINK_IsHalted()
TA260 002:140.032 - 0.515ms returns FALSE
TA260 002:140.041 JLINK_HasError()
TA260 002:141.517 JLINK_IsHalted()
TA260 002:141.989 - 0.471ms returns FALSE
TA260 002:141.998 JLINK_HasError()
TA260 002:143.522 JLINK_IsHalted()
TA260 002:143.976 - 0.454ms returns FALSE
TA260 002:143.987 JLINK_HasError()
TA260 002:146.026 JLINK_IsHalted()
TA260 002:146.510 - 0.484ms returns FALSE
TA260 002:146.518 JLINK_HasError()
TA260 002:148.025 JLINK_IsHalted()
TA260 002:148.553 - 0.527ms returns FALSE
TA260 002:148.567 JLINK_HasError()
TA260 002:149.714 JLINK_IsHalted()
TA260 002:150.201 - 0.486ms returns FALSE
TA260 002:150.210 JLINK_HasError()
TA260 002:151.716 JLINK_IsHalted()
TA260 002:152.161 - 0.445ms returns FALSE
TA260 002:152.169 JLINK_HasError()
TA260 002:153.718 JLINK_IsHalted()
TA260 002:154.210 - 0.492ms returns FALSE
TA260 002:154.217 JLINK_HasError()
TA260 002:156.224 JLINK_IsHalted()
TA260 002:156.698 - 0.473ms returns FALSE
TA260 002:156.709 JLINK_HasError()
TA260 002:158.222 JLINK_IsHalted()
TA260 002:158.714 - 0.492ms returns FALSE
TA260 002:158.723 JLINK_HasError()
TA260 002:159.766 JLINK_IsHalted()
TA260 002:160.275 - 0.509ms returns FALSE
TA260 002:160.280 JLINK_HasError()
TA260 002:161.768 JLINK_IsHalted()
TA260 002:162.270 - 0.501ms returns FALSE
TA260 002:162.278 JLINK_HasError()
TA260 002:163.774 JLINK_IsHalted()
TA260 002:164.259 - 0.485ms returns FALSE
TA260 002:164.270 JLINK_HasError()
TA260 002:166.277 JLINK_IsHalted()
TA260 002:166.746 - 0.469ms returns FALSE
TA260 002:166.755 JLINK_HasError()
TA260 002:168.279 JLINK_IsHalted()
TA260 002:168.818 - 0.538ms returns FALSE
TA260 002:168.826 JLINK_HasError()
TA260 002:170.274 JLINK_IsHalted()
TA260 002:170.780 - 0.505ms returns FALSE
TA260 002:170.787 JLINK_HasError()
TA260 002:172.276 JLINK_IsHalted()
TA260 002:172.840 - 0.563ms returns FALSE
TA260 002:172.848 JLINK_HasError()
TA260 002:174.786 JLINK_IsHalted()
TA260 002:175.280 - 0.495ms returns FALSE
TA260 002:175.287 JLINK_HasError()
TA260 002:176.791 JLINK_IsHalted()
TA260 002:177.225 - 0.434ms returns FALSE
TA260 002:177.237 JLINK_HasError()
TA260 002:178.442 JLINK_IsHalted()
TA260 002:178.905 - 0.463ms returns FALSE
TA260 002:178.914 JLINK_HasError()
TA260 002:180.443 JLINK_IsHalted()
TA260 002:180.910 - 0.467ms returns FALSE
TA260 002:180.917 JLINK_HasError()
TA260 002:182.440 JLINK_IsHalted()
TA260 002:182.967 - 0.526ms returns FALSE
TA260 002:182.982 JLINK_HasError()
TA260 002:184.941 JLINK_IsHalted()
TA260 002:185.468 - 0.527ms returns FALSE
TA260 002:185.475 JLINK_HasError()
TA260 002:187.409 JLINK_IsHalted()
TA260 002:187.913 - 0.504ms returns FALSE
TA260 002:187.927 JLINK_HasError()
TA260 002:189.437 JLINK_IsHalted()
TA260 002:189.929 - 0.492ms returns FALSE
TA260 002:189.936 JLINK_HasError()
TA260 002:191.404 JLINK_IsHalted()
TA260 002:191.881 - 0.477ms returns FALSE
TA260 002:191.886 JLINK_HasError()
TA260 002:193.404 JLINK_IsHalted()
TA260 002:193.859 - 0.455ms returns FALSE
TA260 002:193.864 JLINK_HasError()
TA260 002:194.907 JLINK_IsHalted()
TA260 002:195.394 - 0.486ms returns FALSE
TA260 002:195.406 JLINK_HasError()
TA260 002:196.914 JLINK_IsHalted()
TA260 002:197.356 - 0.440ms returns FALSE
TA260 002:197.363 JLINK_HasError()
TA260 002:198.913 JLINK_IsHalted()
TA260 002:199.373 - 0.459ms returns FALSE
TA260 002:199.381 JLINK_HasError()
TA260 002:200.916 JLINK_IsHalted()
TA260 002:201.462 - 0.545ms returns FALSE
TA260 002:201.470 JLINK_HasError()
TA260 002:202.910 JLINK_IsHalted()
TA260 002:203.473 - 0.562ms returns FALSE
TA260 002:203.480 JLINK_HasError()
TA260 002:204.915 JLINK_IsHalted()
TA260 002:205.465 - 0.550ms returns FALSE
TA260 002:205.475 JLINK_HasError()
TA260 002:207.418 JLINK_IsHalted()
TA260 002:207.931 - 0.512ms returns FALSE
TA260 002:207.937 JLINK_HasError()
TA260 002:209.920 JLINK_IsHalted()
TA260 002:210.463 - 0.542ms returns FALSE
TA260 002:210.472 JLINK_HasError()
TA260 002:211.851 JLINK_IsHalted()
TA260 002:212.361 - 0.510ms returns FALSE
TA260 002:212.368 JLINK_HasError()
TA260 002:214.854 JLINK_IsHalted()
TA260 002:215.359 - 0.504ms returns FALSE
TA260 002:215.369 JLINK_HasError()
TA260 002:216.858 JLINK_IsHalted()
TA260 002:217.308 - 0.450ms returns FALSE
TA260 002:217.322 JLINK_HasError()
TA260 002:218.857 JLINK_IsHalted()
TA260 002:219.384 - 0.526ms returns FALSE
TA260 002:219.392 JLINK_HasError()
TA260 002:220.858 JLINK_IsHalted()
TA260 002:221.381 - 0.523ms returns FALSE
TA260 002:221.387 JLINK_HasError()
TA260 002:222.857 JLINK_IsHalted()
TA260 002:223.359 - 0.501ms returns FALSE
TA260 002:223.364 JLINK_HasError()
TA260 002:224.761 JLINK_IsHalted()
TA260 002:225.258 - 0.496ms returns FALSE
TA260 002:225.263 JLINK_HasError()
TA260 002:226.766 JLINK_IsHalted()
TA260 002:227.230 - 0.463ms returns FALSE
TA260 002:227.244 JLINK_HasError()
TA260 002:228.765 JLINK_IsHalted()
TA260 002:229.255 - 0.490ms returns FALSE
TA260 002:229.261 JLINK_HasError()
TA260 002:230.766 JLINK_IsHalted()
TA260 002:231.235 - 0.469ms returns FALSE
TA260 002:231.241 JLINK_HasError()
TA260 002:232.768 JLINK_IsHalted()
TA260 002:233.279 - 0.510ms returns FALSE
TA260 002:233.285 JLINK_HasError()
TA260 002:234.765 JLINK_IsHalted()
TA260 002:235.246 - 0.480ms returns FALSE
TA260 002:235.254 JLINK_HasError()
TA260 002:237.275 JLINK_IsHalted()
TA260 002:237.747 - 0.471ms returns FALSE
TA260 002:237.753 JLINK_HasError()
TA260 002:239.274 JLINK_IsHalted()
TA260 002:239.782 - 0.507ms returns FALSE
TA260 002:239.790 JLINK_HasError()
TA260 002:241.178 JLINK_IsHalted()
TA260 002:241.656 - 0.477ms returns FALSE
TA260 002:241.661 JLINK_HasError()
TA260 002:243.405 JLINK_IsHalted()
TA260 002:243.930 - 0.524ms returns FALSE
TA260 002:243.939 JLINK_HasError()
TA260 002:245.914 JLINK_IsHalted()
TA260 002:246.465 - 0.550ms returns FALSE
TA260 002:246.472 JLINK_HasError()
TA260 002:248.167 JLINK_IsHalted()
TA260 002:250.478   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:250.976 - 2.808ms returns TRUE
TA260 002:250.990 JLINK_ReadReg(R15 (PC))
TA260 002:250.997 - 0.006ms returns 0x20000000
TA260 002:251.001 JLINK_ClrBPEx(BPHandle = 0x00000009)
TA260 002:251.005 - 0.004ms returns 0x00
TA260 002:251.010 JLINK_ReadReg(R0)
TA260 002:251.013 - 0.003ms returns 0x00000000
TA260 002:251.278 JLINK_HasError()
TA260 002:251.286 JLINK_WriteReg(R0, 0x00000001)
TA260 002:251.290 - 0.004ms returns 0
TA260 002:251.295 JLINK_WriteReg(R1, 0x00004000)
TA260 002:251.298 - 0.003ms returns 0
TA260 002:251.302 JLINK_WriteReg(R2, 0x000000FF)
TA260 002:251.306 - 0.003ms returns 0
TA260 002:251.310 JLINK_WriteReg(R3, 0x00000000)
TA260 002:251.313 - 0.003ms returns 0
TA260 002:251.317 JLINK_WriteReg(R4, 0x00000000)
TA260 002:251.320 - 0.003ms returns 0
TA260 002:251.324 JLINK_WriteReg(R5, 0x00000000)
TA260 002:251.328 - 0.003ms returns 0
TA260 002:251.332 JLINK_WriteReg(R6, 0x00000000)
TA260 002:251.335 - 0.003ms returns 0
TA260 002:251.339 JLINK_WriteReg(R7, 0x00000000)
TA260 002:251.343 - 0.003ms returns 0
TA260 002:251.347 JLINK_WriteReg(R8, 0x00000000)
TA260 002:251.350 - 0.003ms returns 0
TA260 002:251.354 JLINK_WriteReg(R9, 0x20000180)
TA260 002:251.357 - 0.003ms returns 0
TA260 002:251.361 JLINK_WriteReg(R10, 0x00000000)
TA260 002:251.365 - 0.003ms returns 0
TA260 002:251.369 JLINK_WriteReg(R11, 0x00000000)
TA260 002:251.372 - 0.003ms returns 0
TA260 002:251.376 JLINK_WriteReg(R12, 0x00000000)
TA260 002:251.380 - 0.003ms returns 0
TA260 002:251.384 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:251.388 - 0.003ms returns 0
TA260 002:251.392 JLINK_WriteReg(R14, 0x20000001)
TA260 002:251.395 - 0.003ms returns 0
TA260 002:251.399 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 002:251.402 - 0.003ms returns 0
TA260 002:251.406 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:251.410 - 0.003ms returns 0
TA260 002:251.414 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:251.417 - 0.003ms returns 0
TA260 002:251.421 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:251.424 - 0.003ms returns 0
TA260 002:251.428 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:251.432 - 0.003ms returns 0
TA260 002:251.437 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:251.441 - 0.005ms returns 0x0000000A
TA260 002:251.445 JLINK_Go()
TA260 002:251.454   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:254.218 - 2.772ms 
TA260 002:254.224 JLINK_IsHalted()
TA260 002:256.534   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:256.994 - 2.769ms returns TRUE
TA260 002:257.002 JLINK_ReadReg(R15 (PC))
TA260 002:257.007 - 0.005ms returns 0x20000000
TA260 002:257.012 JLINK_ClrBPEx(BPHandle = 0x0000000A)
TA260 002:257.022 - 0.010ms returns 0x00
TA260 002:257.026 JLINK_ReadReg(R0)
TA260 002:257.030 - 0.003ms returns 0x00000000
TA260 002:311.665 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 002:311.678   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 002:311.694   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 002:313.573 - 1.907ms returns 0x184
TA260 002:313.610 JLINK_HasError()
TA260 002:313.617 JLINK_WriteReg(R0, 0x08000000)
TA260 002:313.622 - 0.005ms returns 0
TA260 002:313.626 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 002:313.630 - 0.003ms returns 0
TA260 002:313.634 JLINK_WriteReg(R2, 0x00000002)
TA260 002:313.638 - 0.003ms returns 0
TA260 002:313.642 JLINK_WriteReg(R3, 0x00000000)
TA260 002:313.645 - 0.003ms returns 0
TA260 002:313.649 JLINK_WriteReg(R4, 0x00000000)
TA260 002:313.653 - 0.003ms returns 0
TA260 002:313.657 JLINK_WriteReg(R5, 0x00000000)
TA260 002:313.660 - 0.003ms returns 0
TA260 002:313.664 JLINK_WriteReg(R6, 0x00000000)
TA260 002:313.668 - 0.003ms returns 0
TA260 002:313.672 JLINK_WriteReg(R7, 0x00000000)
TA260 002:313.675 - 0.003ms returns 0
TA260 002:313.681 JLINK_WriteReg(R8, 0x00000000)
TA260 002:313.684 - 0.003ms returns 0
TA260 002:313.688 JLINK_WriteReg(R9, 0x20000180)
TA260 002:313.692 - 0.003ms returns 0
TA260 002:313.696 JLINK_WriteReg(R10, 0x00000000)
TA260 002:313.699 - 0.003ms returns 0
TA260 002:313.703 JLINK_WriteReg(R11, 0x00000000)
TA260 002:313.707 - 0.003ms returns 0
TA260 002:313.711 JLINK_WriteReg(R12, 0x00000000)
TA260 002:313.714 - 0.003ms returns 0
TA260 002:313.718 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:313.722 - 0.004ms returns 0
TA260 002:313.726 JLINK_WriteReg(R14, 0x20000001)
TA260 002:313.730 - 0.003ms returns 0
TA260 002:313.734 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 002:313.737 - 0.004ms returns 0
TA260 002:313.742 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:313.745 - 0.003ms returns 0
TA260 002:313.749 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:313.753 - 0.003ms returns 0
TA260 002:313.757 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:313.760 - 0.003ms returns 0
TA260 002:313.765 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:313.768 - 0.003ms returns 0
TA260 002:313.772 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:313.780   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:314.217 - 0.444ms returns 0x0000000B
TA260 002:314.230 JLINK_Go()
TA260 002:314.236   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 002:314.737   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:317.461 - 3.230ms 
TA260 002:317.475 JLINK_IsHalted()
TA260 002:319.857   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:320.348 - 2.872ms returns TRUE
TA260 002:320.359 JLINK_ReadReg(R15 (PC))
TA260 002:320.364 - 0.005ms returns 0x20000000
TA260 002:320.392 JLINK_ClrBPEx(BPHandle = 0x0000000B)
TA260 002:320.397 - 0.005ms returns 0x00
TA260 002:320.401 JLINK_ReadReg(R0)
TA260 002:320.405 - 0.004ms returns 0x00000000
TA260 002:320.610 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:320.616   Data:  18 18 00 20 C1 01 00 08 D1 2A 00 08 B5 27 00 08 ...
TA260 002:320.633   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:323.234 - 2.624ms returns 0x27C
TA260 002:323.240 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:323.244   Data:  10 B5 13 46 0A 46 04 46 19 46 FF F7 F0 FF 20 46 ...
TA260 002:323.251   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:325.100 - 1.859ms returns 0x184
TA260 002:325.106 JLINK_HasError()
TA260 002:325.110 JLINK_WriteReg(R0, 0x08000000)
TA260 002:325.115 - 0.004ms returns 0
TA260 002:325.119 JLINK_WriteReg(R1, 0x00000400)
TA260 002:325.122 - 0.003ms returns 0
TA260 002:325.126 JLINK_WriteReg(R2, 0x20000184)
TA260 002:325.130 - 0.003ms returns 0
TA260 002:325.134 JLINK_WriteReg(R3, 0x00000000)
TA260 002:325.137 - 0.003ms returns 0
TA260 002:325.141 JLINK_WriteReg(R4, 0x00000000)
TA260 002:325.144 - 0.003ms returns 0
TA260 002:325.148 JLINK_WriteReg(R5, 0x00000000)
TA260 002:325.152 - 0.003ms returns 0
TA260 002:325.156 JLINK_WriteReg(R6, 0x00000000)
TA260 002:325.162 - 0.006ms returns 0
TA260 002:325.168 JLINK_WriteReg(R7, 0x00000000)
TA260 002:325.172 - 0.003ms returns 0
TA260 002:325.176 JLINK_WriteReg(R8, 0x00000000)
TA260 002:325.179 - 0.003ms returns 0
TA260 002:325.183 JLINK_WriteReg(R9, 0x20000180)
TA260 002:325.186 - 0.003ms returns 0
TA260 002:325.190 JLINK_WriteReg(R10, 0x00000000)
TA260 002:325.194 - 0.003ms returns 0
TA260 002:325.198 JLINK_WriteReg(R11, 0x00000000)
TA260 002:325.201 - 0.003ms returns 0
TA260 002:325.205 JLINK_WriteReg(R12, 0x00000000)
TA260 002:325.209 - 0.003ms returns 0
TA260 002:325.213 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:325.216 - 0.003ms returns 0
TA260 002:325.220 JLINK_WriteReg(R14, 0x20000001)
TA260 002:325.224 - 0.003ms returns 0
TA260 002:325.228 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:325.231 - 0.003ms returns 0
TA260 002:325.235 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:325.239 - 0.003ms returns 0
TA260 002:325.243 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:325.246 - 0.003ms returns 0
TA260 002:325.250 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:325.254 - 0.003ms returns 0
TA260 002:325.258 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:325.261 - 0.003ms returns 0
TA260 002:325.266 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:325.270 - 0.004ms returns 0x0000000C
TA260 002:325.274 JLINK_Go()
TA260 002:325.281   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:328.238 - 2.963ms 
TA260 002:328.255 JLINK_IsHalted()
TA260 002:328.756 - 0.501ms returns FALSE
TA260 002:328.763 JLINK_HasError()
TA260 002:331.588 JLINK_IsHalted()
TA260 002:332.029 - 0.441ms returns FALSE
TA260 002:332.040 JLINK_HasError()
TA260 002:333.584 JLINK_IsHalted()
TA260 002:335.924   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:336.468 - 2.883ms returns TRUE
TA260 002:336.480 JLINK_ReadReg(R15 (PC))
TA260 002:336.486 - 0.006ms returns 0x20000000
TA260 002:336.491 JLINK_ClrBPEx(BPHandle = 0x0000000C)
TA260 002:336.494 - 0.004ms returns 0x00
TA260 002:336.499 JLINK_ReadReg(R0)
TA260 002:336.502 - 0.003ms returns 0x00000000
TA260 002:336.814 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:336.821   Data:  5B D0 C3 F3 0A 54 C1 F3 0A 55 2C 44 A4 F2 F3 34 ...
TA260 002:336.832   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:339.466 - 2.652ms returns 0x27C
TA260 002:339.478 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:339.482   Data:  01 02 06 D0 0A 0D A2 F5 60 72 C1 F3 13 01 00 2A ...
TA260 002:339.491   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:341.369 - 1.891ms returns 0x184
TA260 002:341.376 JLINK_HasError()
TA260 002:341.380 JLINK_WriteReg(R0, 0x08000400)
TA260 002:341.385 - 0.004ms returns 0
TA260 002:341.389 JLINK_WriteReg(R1, 0x00000400)
TA260 002:341.392 - 0.003ms returns 0
TA260 002:341.396 JLINK_WriteReg(R2, 0x20000184)
TA260 002:341.400 - 0.003ms returns 0
TA260 002:341.404 JLINK_WriteReg(R3, 0x00000000)
TA260 002:341.407 - 0.003ms returns 0
TA260 002:341.411 JLINK_WriteReg(R4, 0x00000000)
TA260 002:341.414 - 0.003ms returns 0
TA260 002:341.418 JLINK_WriteReg(R5, 0x00000000)
TA260 002:341.422 - 0.003ms returns 0
TA260 002:341.426 JLINK_WriteReg(R6, 0x00000000)
TA260 002:341.429 - 0.003ms returns 0
TA260 002:341.433 JLINK_WriteReg(R7, 0x00000000)
TA260 002:341.436 - 0.003ms returns 0
TA260 002:341.441 JLINK_WriteReg(R8, 0x00000000)
TA260 002:341.444 - 0.003ms returns 0
TA260 002:341.448 JLINK_WriteReg(R9, 0x20000180)
TA260 002:341.452 - 0.003ms returns 0
TA260 002:341.456 JLINK_WriteReg(R10, 0x00000000)
TA260 002:341.459 - 0.003ms returns 0
TA260 002:341.463 JLINK_WriteReg(R11, 0x00000000)
TA260 002:341.467 - 0.003ms returns 0
TA260 002:341.471 JLINK_WriteReg(R12, 0x00000000)
TA260 002:341.474 - 0.003ms returns 0
TA260 002:341.478 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:341.482 - 0.003ms returns 0
TA260 002:341.486 JLINK_WriteReg(R14, 0x20000001)
TA260 002:341.489 - 0.003ms returns 0
TA260 002:341.493 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:341.497 - 0.003ms returns 0
TA260 002:341.501 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:341.542 - 0.041ms returns 0
TA260 002:341.546 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:341.550 - 0.003ms returns 0
TA260 002:341.554 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:341.557 - 0.003ms returns 0
TA260 002:341.561 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:341.576 - 0.015ms returns 0
TA260 002:341.581 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:341.585 - 0.004ms returns 0x0000000D
TA260 002:341.590 JLINK_Go()
TA260 002:341.597   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:344.361 - 2.770ms 
TA260 002:344.368 JLINK_IsHalted()
TA260 002:344.845 - 0.476ms returns FALSE
TA260 002:344.851 JLINK_HasError()
TA260 002:346.372 JLINK_IsHalted()
TA260 002:347.161 - 0.788ms returns FALSE
TA260 002:347.173 JLINK_HasError()
TA260 002:349.372 JLINK_IsHalted()
TA260 002:351.678   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:352.156 - 2.783ms returns TRUE
TA260 002:352.162 JLINK_ReadReg(R15 (PC))
TA260 002:352.168 - 0.005ms returns 0x20000000
TA260 002:352.173 JLINK_ClrBPEx(BPHandle = 0x0000000D)
TA260 002:352.177 - 0.003ms returns 0x00
TA260 002:352.181 JLINK_ReadReg(R0)
TA260 002:352.185 - 0.003ms returns 0x00000000
TA260 002:352.536 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:352.544   Data:  F0 4D 92 46 9B 46 11 B1 B1 FA 81 F2 02 E0 B0 FA ...
TA260 002:352.555   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:355.197 - 2.660ms returns 0x27C
TA260 002:355.214 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:355.218   Data:  F1 FD 20 46 4F F4 00 51 00 22 00 F0 EB FD 01 20 ...
TA260 002:355.227   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:357.109 - 1.895ms returns 0x184
TA260 002:357.124 JLINK_HasError()
TA260 002:357.130 JLINK_WriteReg(R0, 0x08000800)
TA260 002:357.135 - 0.005ms returns 0
TA260 002:357.139 JLINK_WriteReg(R1, 0x00000400)
TA260 002:357.143 - 0.003ms returns 0
TA260 002:357.147 JLINK_WriteReg(R2, 0x20000184)
TA260 002:357.150 - 0.003ms returns 0
TA260 002:357.154 JLINK_WriteReg(R3, 0x00000000)
TA260 002:357.158 - 0.003ms returns 0
TA260 002:357.162 JLINK_WriteReg(R4, 0x00000000)
TA260 002:357.165 - 0.003ms returns 0
TA260 002:357.169 JLINK_WriteReg(R5, 0x00000000)
TA260 002:357.172 - 0.003ms returns 0
TA260 002:357.177 JLINK_WriteReg(R6, 0x00000000)
TA260 002:357.180 - 0.003ms returns 0
TA260 002:357.184 JLINK_WriteReg(R7, 0x00000000)
TA260 002:357.188 - 0.004ms returns 0
TA260 002:357.192 JLINK_WriteReg(R8, 0x00000000)
TA260 002:357.196 - 0.003ms returns 0
TA260 002:357.200 JLINK_WriteReg(R9, 0x20000180)
TA260 002:357.203 - 0.003ms returns 0
TA260 002:357.207 JLINK_WriteReg(R10, 0x00000000)
TA260 002:357.211 - 0.003ms returns 0
TA260 002:357.215 JLINK_WriteReg(R11, 0x00000000)
TA260 002:357.218 - 0.003ms returns 0
TA260 002:357.222 JLINK_WriteReg(R12, 0x00000000)
TA260 002:357.226 - 0.003ms returns 0
TA260 002:357.230 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:357.233 - 0.003ms returns 0
TA260 002:357.237 JLINK_WriteReg(R14, 0x20000001)
TA260 002:357.241 - 0.003ms returns 0
TA260 002:357.245 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:357.248 - 0.003ms returns 0
TA260 002:357.252 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:357.256 - 0.003ms returns 0
TA260 002:357.260 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:357.263 - 0.003ms returns 0
TA260 002:357.267 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:357.271 - 0.003ms returns 0
TA260 002:357.275 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:357.278 - 0.003ms returns 0
TA260 002:357.283 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:357.287 - 0.004ms returns 0x0000000E
TA260 002:357.291 JLINK_Go()
TA260 002:357.300   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:360.051 - 2.759ms 
TA260 002:360.063 JLINK_IsHalted()
TA260 002:360.536 - 0.472ms returns FALSE
TA260 002:360.544 JLINK_HasError()
TA260 002:361.873 JLINK_IsHalted()
TA260 002:362.360 - 0.486ms returns FALSE
TA260 002:362.366 JLINK_HasError()
TA260 002:363.871 JLINK_IsHalted()
TA260 002:364.359 - 0.488ms returns FALSE
TA260 002:364.365 JLINK_HasError()
TA260 002:366.379 JLINK_IsHalted()
TA260 002:368.732   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:369.234 - 2.854ms returns TRUE
TA260 002:369.240 JLINK_ReadReg(R15 (PC))
TA260 002:369.245 - 0.004ms returns 0x20000000
TA260 002:369.250 JLINK_ClrBPEx(BPHandle = 0x0000000E)
TA260 002:369.254 - 0.003ms returns 0x00
TA260 002:369.258 JLINK_ReadReg(R0)
TA260 002:369.261 - 0.003ms returns 0x00000000
TA260 002:369.611 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:369.619   Data:  BD E8 F0 40 00 F0 2C BD 2D E9 F0 4F 81 B0 41 F6 ...
TA260 002:369.629   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:372.143 - 2.531ms returns 0x27C
TA260 002:372.153 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:372.157   Data:  05 F0 01 02 30 46 4F F4 80 51 00 F0 EB FB 20 46 ...
TA260 002:372.164   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:374.051 - 1.897ms returns 0x184
TA260 002:374.059 JLINK_HasError()
TA260 002:374.668 JLINK_WriteReg(R0, 0x08000C00)
TA260 002:374.674 - 0.005ms returns 0
TA260 002:374.678 JLINK_WriteReg(R1, 0x00000400)
TA260 002:374.681 - 0.003ms returns 0
TA260 002:374.685 JLINK_WriteReg(R2, 0x20000184)
TA260 002:374.689 - 0.003ms returns 0
TA260 002:374.693 JLINK_WriteReg(R3, 0x00000000)
TA260 002:374.696 - 0.003ms returns 0
TA260 002:374.700 JLINK_WriteReg(R4, 0x00000000)
TA260 002:374.704 - 0.003ms returns 0
TA260 002:374.708 JLINK_WriteReg(R5, 0x00000000)
TA260 002:374.711 - 0.003ms returns 0
TA260 002:374.715 JLINK_WriteReg(R6, 0x00000000)
TA260 002:374.718 - 0.003ms returns 0
TA260 002:374.723 JLINK_WriteReg(R7, 0x00000000)
TA260 002:374.726 - 0.003ms returns 0
TA260 002:374.730 JLINK_WriteReg(R8, 0x00000000)
TA260 002:374.733 - 0.003ms returns 0
TA260 002:374.738 JLINK_WriteReg(R9, 0x20000180)
TA260 002:374.741 - 0.003ms returns 0
TA260 002:374.745 JLINK_WriteReg(R10, 0x00000000)
TA260 002:374.748 - 0.003ms returns 0
TA260 002:374.752 JLINK_WriteReg(R11, 0x00000000)
TA260 002:374.756 - 0.003ms returns 0
TA260 002:374.760 JLINK_WriteReg(R12, 0x00000000)
TA260 002:374.763 - 0.003ms returns 0
TA260 002:374.767 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:374.771 - 0.003ms returns 0
TA260 002:374.775 JLINK_WriteReg(R14, 0x20000001)
TA260 002:374.778 - 0.003ms returns 0
TA260 002:374.782 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:374.786 - 0.003ms returns 0
TA260 002:374.790 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:374.793 - 0.003ms returns 0
TA260 002:374.797 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:374.801 - 0.003ms returns 0
TA260 002:374.805 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:374.808 - 0.003ms returns 0
TA260 002:374.812 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:374.815 - 0.003ms returns 0
TA260 002:374.820 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:374.824 - 0.004ms returns 0x0000000F
TA260 002:374.828 JLINK_Go()
TA260 002:374.835   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:377.695 - 2.866ms 
TA260 002:377.711 JLINK_IsHalted()
TA260 002:378.211 - 0.500ms returns FALSE
TA260 002:378.217 JLINK_HasError()
TA260 002:379.884 JLINK_IsHalted()
TA260 002:380.346 - 0.462ms returns FALSE
TA260 002:380.352 JLINK_HasError()
TA260 002:381.882 JLINK_IsHalted()
TA260 002:384.267   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:384.781 - 2.898ms returns TRUE
TA260 002:384.787 JLINK_ReadReg(R15 (PC))
TA260 002:384.792 - 0.004ms returns 0x20000000
TA260 002:384.796 JLINK_ClrBPEx(BPHandle = 0x0000000F)
TA260 002:384.800 - 0.003ms returns 0x00
TA260 002:384.804 JLINK_ReadReg(R0)
TA260 002:384.808 - 0.003ms returns 0x00000000
TA260 002:385.158 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:385.165   Data:  00 90 00 98 14 28 E7 DB 02 B0 BD EC 0A 8B BD E8 ...
TA260 002:385.174   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:387.795 - 2.637ms returns 0x27C
TA260 002:387.811 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:387.815   Data:  3A 43 1A 43 42 EA 09 02 B9 F1 08 0F 08 BF 42 F0 ...
TA260 002:387.825   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:389.701 - 1.890ms returns 0x184
TA260 002:389.744 JLINK_HasError()
TA260 002:389.749 JLINK_WriteReg(R0, 0x08001000)
TA260 002:389.754 - 0.004ms returns 0
TA260 002:389.758 JLINK_WriteReg(R1, 0x00000400)
TA260 002:389.762 - 0.003ms returns 0
TA260 002:389.766 JLINK_WriteReg(R2, 0x20000184)
TA260 002:389.769 - 0.003ms returns 0
TA260 002:389.773 JLINK_WriteReg(R3, 0x00000000)
TA260 002:389.777 - 0.003ms returns 0
TA260 002:389.781 JLINK_WriteReg(R4, 0x00000000)
TA260 002:389.784 - 0.003ms returns 0
TA260 002:389.788 JLINK_WriteReg(R5, 0x00000000)
TA260 002:389.791 - 0.003ms returns 0
TA260 002:389.795 JLINK_WriteReg(R6, 0x00000000)
TA260 002:389.799 - 0.003ms returns 0
TA260 002:389.803 JLINK_WriteReg(R7, 0x00000000)
TA260 002:389.806 - 0.003ms returns 0
TA260 002:389.810 JLINK_WriteReg(R8, 0x00000000)
TA260 002:389.814 - 0.003ms returns 0
TA260 002:389.818 JLINK_WriteReg(R9, 0x20000180)
TA260 002:389.821 - 0.003ms returns 0
TA260 002:389.825 JLINK_WriteReg(R10, 0x00000000)
TA260 002:389.829 - 0.003ms returns 0
TA260 002:389.833 JLINK_WriteReg(R11, 0x00000000)
TA260 002:389.836 - 0.003ms returns 0
TA260 002:389.840 JLINK_WriteReg(R12, 0x00000000)
TA260 002:389.844 - 0.003ms returns 0
TA260 002:389.848 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:389.851 - 0.003ms returns 0
TA260 002:389.855 JLINK_WriteReg(R14, 0x20000001)
TA260 002:389.859 - 0.003ms returns 0
TA260 002:389.863 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:389.866 - 0.003ms returns 0
TA260 002:389.870 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:389.874 - 0.003ms returns 0
TA260 002:389.878 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:389.881 - 0.003ms returns 0
TA260 002:389.885 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:389.888 - 0.003ms returns 0
TA260 002:389.892 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:389.896 - 0.003ms returns 0
TA260 002:389.900 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:389.904 - 0.004ms returns 0x00000010
TA260 002:389.909 JLINK_Go()
TA260 002:389.916   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:392.802 - 2.893ms 
TA260 002:392.817 JLINK_IsHalted()
TA260 002:393.321 - 0.504ms returns FALSE
TA260 002:393.333 JLINK_HasError()
TA260 002:394.896 JLINK_IsHalted()
TA260 002:395.382 - 0.486ms returns FALSE
TA260 002:395.389 JLINK_HasError()
TA260 002:396.903 JLINK_IsHalted()
TA260 002:399.226   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:399.745 - 2.841ms returns TRUE
TA260 002:399.761 JLINK_ReadReg(R15 (PC))
TA260 002:399.766 - 0.010ms returns 0x20000000
TA260 002:399.772 JLINK_ClrBPEx(BPHandle = 0x00000010)
TA260 002:399.776 - 0.004ms returns 0x00
TA260 002:399.810 JLINK_ReadReg(R0)
TA260 002:399.819 - 0.009ms returns 0x00000000
TA260 002:400.159 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:400.167   Data:  35 F9 94 F8 35 10 02 29 25 D1 05 46 20 68 21 6C ...
TA260 002:400.177   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:402.838 - 2.678ms returns 0x27C
TA260 002:402.845 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:402.850   Data:  40 F2 10 41 C2 F2 00 00 C2 F2 00 01 00 78 0A 68 ...
TA260 002:402.858   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:404.690 - 1.844ms returns 0x184
TA260 002:404.696 JLINK_HasError()
TA260 002:404.700 JLINK_WriteReg(R0, 0x08001400)
TA260 002:404.704 - 0.004ms returns 0
TA260 002:404.709 JLINK_WriteReg(R1, 0x00000400)
TA260 002:404.712 - 0.003ms returns 0
TA260 002:404.716 JLINK_WriteReg(R2, 0x20000184)
TA260 002:404.720 - 0.004ms returns 0
TA260 002:404.724 JLINK_WriteReg(R3, 0x00000000)
TA260 002:404.728 - 0.003ms returns 0
TA260 002:404.732 JLINK_WriteReg(R4, 0x00000000)
TA260 002:404.735 - 0.003ms returns 0
TA260 002:404.739 JLINK_WriteReg(R5, 0x00000000)
TA260 002:404.742 - 0.003ms returns 0
TA260 002:404.746 JLINK_WriteReg(R6, 0x00000000)
TA260 002:404.750 - 0.003ms returns 0
TA260 002:404.754 JLINK_WriteReg(R7, 0x00000000)
TA260 002:404.757 - 0.003ms returns 0
TA260 002:404.761 JLINK_WriteReg(R8, 0x00000000)
TA260 002:404.764 - 0.003ms returns 0
TA260 002:404.768 JLINK_WriteReg(R9, 0x20000180)
TA260 002:404.802 - 0.034ms returns 0
TA260 002:404.808 JLINK_WriteReg(R10, 0x00000000)
TA260 002:404.811 - 0.003ms returns 0
TA260 002:404.815 JLINK_WriteReg(R11, 0x00000000)
TA260 002:404.819 - 0.003ms returns 0
TA260 002:404.823 JLINK_WriteReg(R12, 0x00000000)
TA260 002:404.826 - 0.003ms returns 0
TA260 002:404.830 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:404.834 - 0.003ms returns 0
TA260 002:404.838 JLINK_WriteReg(R14, 0x20000001)
TA260 002:404.841 - 0.003ms returns 0
TA260 002:404.845 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:404.848 - 0.003ms returns 0
TA260 002:404.852 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:404.856 - 0.003ms returns 0
TA260 002:404.860 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:404.863 - 0.003ms returns 0
TA260 002:404.867 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:404.870 - 0.003ms returns 0
TA260 002:404.875 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:404.878 - 0.003ms returns 0
TA260 002:404.882 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:404.886 - 0.004ms returns 0x00000011
TA260 002:404.890 JLINK_Go()
TA260 002:404.897   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:407.696 - 2.805ms 
TA260 002:407.711 JLINK_IsHalted()
TA260 002:408.474 - 0.763ms returns FALSE
TA260 002:408.487 JLINK_HasError()
TA260 002:409.924 JLINK_IsHalted()
TA260 002:410.601 - 0.676ms returns FALSE
TA260 002:410.612 JLINK_HasError()
TA260 002:411.922 JLINK_IsHalted()
TA260 002:414.268   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:414.757 - 2.834ms returns TRUE
TA260 002:414.764 JLINK_ReadReg(R15 (PC))
TA260 002:414.769 - 0.005ms returns 0x20000000
TA260 002:414.773 JLINK_ClrBPEx(BPHandle = 0x00000011)
TA260 002:414.777 - 0.003ms returns 0x00
TA260 002:414.781 JLINK_ReadReg(R0)
TA260 002:414.785 - 0.003ms returns 0x00000000
TA260 002:415.114 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:415.121   Data:  01 68 40 26 41 F0 80 51 01 60 00 68 C4 F2 0E 26 ...
TA260 002:415.131   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:417.743 - 2.628ms returns 0x27C
TA260 002:417.756 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:417.760   Data:  00 01 00 23 FE F7 B0 FB 21 68 02 22 C1 F3 01 41 ...
TA260 002:417.770   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:419.610 - 1.854ms returns 0x184
TA260 002:419.616 JLINK_HasError()
TA260 002:419.621 JLINK_WriteReg(R0, 0x08001800)
TA260 002:419.625 - 0.004ms returns 0
TA260 002:419.630 JLINK_WriteReg(R1, 0x00000400)
TA260 002:419.633 - 0.003ms returns 0
TA260 002:419.637 JLINK_WriteReg(R2, 0x20000184)
TA260 002:419.641 - 0.003ms returns 0
TA260 002:419.645 JLINK_WriteReg(R3, 0x00000000)
TA260 002:419.648 - 0.003ms returns 0
TA260 002:419.652 JLINK_WriteReg(R4, 0x00000000)
TA260 002:419.656 - 0.003ms returns 0
TA260 002:419.660 JLINK_WriteReg(R5, 0x00000000)
TA260 002:419.663 - 0.003ms returns 0
TA260 002:419.667 JLINK_WriteReg(R6, 0x00000000)
TA260 002:419.670 - 0.003ms returns 0
TA260 002:419.674 JLINK_WriteReg(R7, 0x00000000)
TA260 002:419.678 - 0.003ms returns 0
TA260 002:419.682 JLINK_WriteReg(R8, 0x00000000)
TA260 002:419.685 - 0.003ms returns 0
TA260 002:419.689 JLINK_WriteReg(R9, 0x20000180)
TA260 002:419.693 - 0.003ms returns 0
TA260 002:419.697 JLINK_WriteReg(R10, 0x00000000)
TA260 002:419.700 - 0.003ms returns 0
TA260 002:419.704 JLINK_WriteReg(R11, 0x00000000)
TA260 002:419.708 - 0.003ms returns 0
TA260 002:419.712 JLINK_WriteReg(R12, 0x00000000)
TA260 002:419.715 - 0.003ms returns 0
TA260 002:419.719 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:419.723 - 0.003ms returns 0
TA260 002:419.727 JLINK_WriteReg(R14, 0x20000001)
TA260 002:419.731 - 0.003ms returns 0
TA260 002:419.735 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:419.738 - 0.003ms returns 0
TA260 002:419.742 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:419.746 - 0.003ms returns 0
TA260 002:419.750 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:419.753 - 0.003ms returns 0
TA260 002:419.757 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:419.760 - 0.003ms returns 0
TA260 002:419.764 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:419.804 - 0.039ms returns 0
TA260 002:419.810 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:419.814 - 0.004ms returns 0x00000012
TA260 002:419.818 JLINK_Go()
TA260 002:419.825   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:422.588 - 2.769ms 
TA260 002:422.598 JLINK_IsHalted()
TA260 002:423.095 - 0.497ms returns FALSE
TA260 002:423.101 JLINK_HasError()
TA260 002:424.985 JLINK_IsHalted()
TA260 002:425.465 - 0.479ms returns FALSE
TA260 002:425.476 JLINK_HasError()
TA260 002:426.985 JLINK_IsHalted()
TA260 002:429.324   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:429.838 - 2.852ms returns TRUE
TA260 002:429.851 JLINK_ReadReg(R15 (PC))
TA260 002:429.857 - 0.005ms returns 0x20000000
TA260 002:429.861 JLINK_ClrBPEx(BPHandle = 0x00000012)
TA260 002:429.865 - 0.003ms returns 0x00
TA260 002:429.869 JLINK_ReadReg(R0)
TA260 002:429.873 - 0.003ms returns 0x00000000
TA260 002:430.264 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:430.272   Data:  E1 6A 00 F0 70 60 B0 EB 01 6F 5E D1 00 20 02 B0 ...
TA260 002:430.284   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:432.884 - 2.619ms returns 0x27C
TA260 002:432.893 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:432.897   Data:  06 96 CD E9 04 66 03 96 02 96 48 D1 01 21 01 70 ...
TA260 002:432.905   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:434.824 - 1.931ms returns 0x184
TA260 002:434.830 JLINK_HasError()
TA260 002:434.836 JLINK_WriteReg(R0, 0x08001C00)
TA260 002:434.841 - 0.004ms returns 0
TA260 002:434.845 JLINK_WriteReg(R1, 0x00000400)
TA260 002:434.848 - 0.003ms returns 0
TA260 002:434.852 JLINK_WriteReg(R2, 0x20000184)
TA260 002:434.856 - 0.003ms returns 0
TA260 002:434.860 JLINK_WriteReg(R3, 0x00000000)
TA260 002:434.863 - 0.003ms returns 0
TA260 002:434.867 JLINK_WriteReg(R4, 0x00000000)
TA260 002:434.870 - 0.003ms returns 0
TA260 002:434.874 JLINK_WriteReg(R5, 0x00000000)
TA260 002:434.878 - 0.003ms returns 0
TA260 002:434.882 JLINK_WriteReg(R6, 0x00000000)
TA260 002:434.885 - 0.003ms returns 0
TA260 002:434.889 JLINK_WriteReg(R7, 0x00000000)
TA260 002:434.892 - 0.003ms returns 0
TA260 002:434.897 JLINK_WriteReg(R8, 0x00000000)
TA260 002:434.900 - 0.003ms returns 0
TA260 002:434.904 JLINK_WriteReg(R9, 0x20000180)
TA260 002:434.907 - 0.003ms returns 0
TA260 002:434.911 JLINK_WriteReg(R10, 0x00000000)
TA260 002:434.915 - 0.003ms returns 0
TA260 002:434.919 JLINK_WriteReg(R11, 0x00000000)
TA260 002:434.922 - 0.003ms returns 0
TA260 002:434.926 JLINK_WriteReg(R12, 0x00000000)
TA260 002:434.930 - 0.004ms returns 0
TA260 002:434.934 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:434.938 - 0.003ms returns 0
TA260 002:434.942 JLINK_WriteReg(R14, 0x20000001)
TA260 002:434.945 - 0.003ms returns 0
TA260 002:434.949 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:434.953 - 0.003ms returns 0
TA260 002:434.957 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:434.960 - 0.003ms returns 0
TA260 002:434.964 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:434.968 - 0.003ms returns 0
TA260 002:434.972 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:434.975 - 0.003ms returns 0
TA260 002:434.979 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:434.982 - 0.003ms returns 0
TA260 002:434.987 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:434.991 - 0.004ms returns 0x00000013
TA260 002:434.995 JLINK_Go()
TA260 002:435.002   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:437.941 - 2.945ms 
TA260 002:437.953 JLINK_IsHalted()
TA260 002:438.460 - 0.507ms returns FALSE
TA260 002:438.466 JLINK_HasError()
TA260 002:440.343 JLINK_IsHalted()
TA260 002:440.803 - 0.459ms returns FALSE
TA260 002:440.810 JLINK_HasError()
TA260 002:442.342 JLINK_IsHalted()
TA260 002:444.631   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:445.109 - 2.767ms returns TRUE
TA260 002:445.115 JLINK_ReadReg(R15 (PC))
TA260 002:445.119 - 0.004ms returns 0x20000000
TA260 002:445.124 JLINK_ClrBPEx(BPHandle = 0x00000013)
TA260 002:445.127 - 0.003ms returns 0x00
TA260 002:445.131 JLINK_ReadReg(R0)
TA260 002:445.135 - 0.003ms returns 0x00000000
TA260 002:445.458 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:445.466   Data:  C8 60 C8 68 40 F0 40 00 C8 60 02 B0 B0 BD D1 07 ...
TA260 002:445.475   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:448.058 - 2.599ms returns 0x27C
TA260 002:448.072 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:448.076   Data:  00 F0 C6 FD 20 68 01 69 21 F4 90 41 01 61 41 69 ...
TA260 002:448.086   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:450.007 - 1.935ms returns 0x184
TA260 002:450.014 JLINK_HasError()
TA260 002:450.019 JLINK_WriteReg(R0, 0x08002000)
TA260 002:450.023 - 0.004ms returns 0
TA260 002:450.028 JLINK_WriteReg(R1, 0x00000400)
TA260 002:450.032 - 0.004ms returns 0
TA260 002:450.036 JLINK_WriteReg(R2, 0x20000184)
TA260 002:450.039 - 0.003ms returns 0
TA260 002:450.043 JLINK_WriteReg(R3, 0x00000000)
TA260 002:450.047 - 0.003ms returns 0
TA260 002:450.051 JLINK_WriteReg(R4, 0x00000000)
TA260 002:450.054 - 0.003ms returns 0
TA260 002:450.058 JLINK_WriteReg(R5, 0x00000000)
TA260 002:450.062 - 0.003ms returns 0
TA260 002:450.066 JLINK_WriteReg(R6, 0x00000000)
TA260 002:450.069 - 0.003ms returns 0
TA260 002:450.073 JLINK_WriteReg(R7, 0x00000000)
TA260 002:450.076 - 0.003ms returns 0
TA260 002:450.080 JLINK_WriteReg(R8, 0x00000000)
TA260 002:450.084 - 0.003ms returns 0
TA260 002:450.088 JLINK_WriteReg(R9, 0x20000180)
TA260 002:450.091 - 0.003ms returns 0
TA260 002:450.095 JLINK_WriteReg(R10, 0x00000000)
TA260 002:450.098 - 0.003ms returns 0
TA260 002:450.102 JLINK_WriteReg(R11, 0x00000000)
TA260 002:450.106 - 0.003ms returns 0
TA260 002:450.110 JLINK_WriteReg(R12, 0x00000000)
TA260 002:450.113 - 0.003ms returns 0
TA260 002:450.117 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:450.121 - 0.003ms returns 0
TA260 002:450.125 JLINK_WriteReg(R14, 0x20000001)
TA260 002:450.129 - 0.003ms returns 0
TA260 002:450.133 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:450.136 - 0.003ms returns 0
TA260 002:450.140 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:450.144 - 0.003ms returns 0
TA260 002:450.148 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:450.151 - 0.003ms returns 0
TA260 002:450.155 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:450.158 - 0.003ms returns 0
TA260 002:450.163 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:450.166 - 0.003ms returns 0
TA260 002:450.171 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:450.174 - 0.004ms returns 0x00000014
TA260 002:450.178 JLINK_Go()
TA260 002:450.186   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:452.949 - 2.770ms 
TA260 002:452.959 JLINK_IsHalted()
TA260 002:453.459 - 0.499ms returns FALSE
TA260 002:453.468 JLINK_HasError()
TA260 002:454.850 JLINK_IsHalted()
TA260 002:455.393 - 0.542ms returns FALSE
TA260 002:455.403 JLINK_HasError()
TA260 002:457.356 JLINK_IsHalted()
TA260 002:459.636   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:460.136 - 2.779ms returns TRUE
TA260 002:460.146 JLINK_ReadReg(R15 (PC))
TA260 002:460.151 - 0.005ms returns 0x20000000
TA260 002:460.155 JLINK_ClrBPEx(BPHandle = 0x00000014)
TA260 002:460.159 - 0.003ms returns 0x00
TA260 002:460.163 JLINK_ReadReg(R0)
TA260 002:460.167 - 0.003ms returns 0x00000000
TA260 002:460.466 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:460.473   Data:  80 72 CA 60 4A 69 42 F0 01 02 4A 61 CA 68 42 F0 ...
TA260 002:460.482   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:463.017 - 2.550ms returns 0x27C
TA260 002:463.025 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:463.029   Data:  19 F1 01 05 0E D0 14 E0 00 27 A9 E7 20 20 84 F8 ...
TA260 002:463.037   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:464.837 - 1.811ms returns 0x184
TA260 002:464.843 JLINK_HasError()
TA260 002:464.848 JLINK_WriteReg(R0, 0x08002400)
TA260 002:464.853 - 0.004ms returns 0
TA260 002:464.857 JLINK_WriteReg(R1, 0x00000400)
TA260 002:464.861 - 0.003ms returns 0
TA260 002:464.871 JLINK_WriteReg(R2, 0x20000184)
TA260 002:464.874 - 0.003ms returns 0
TA260 002:464.879 JLINK_WriteReg(R3, 0x00000000)
TA260 002:464.882 - 0.003ms returns 0
TA260 002:464.890 JLINK_WriteReg(R4, 0x00000000)
TA260 002:464.894 - 0.003ms returns 0
TA260 002:464.898 JLINK_WriteReg(R5, 0x00000000)
TA260 002:464.901 - 0.003ms returns 0
TA260 002:464.905 JLINK_WriteReg(R6, 0x00000000)
TA260 002:464.909 - 0.003ms returns 0
TA260 002:464.913 JLINK_WriteReg(R7, 0x00000000)
TA260 002:464.916 - 0.003ms returns 0
TA260 002:464.920 JLINK_WriteReg(R8, 0x00000000)
TA260 002:464.924 - 0.003ms returns 0
TA260 002:464.928 JLINK_WriteReg(R9, 0x20000180)
TA260 002:464.931 - 0.003ms returns 0
TA260 002:464.935 JLINK_WriteReg(R10, 0x00000000)
TA260 002:464.939 - 0.003ms returns 0
TA260 002:464.943 JLINK_WriteReg(R11, 0x00000000)
TA260 002:464.946 - 0.003ms returns 0
TA260 002:464.950 JLINK_WriteReg(R12, 0x00000000)
TA260 002:464.954 - 0.003ms returns 0
TA260 002:464.958 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:464.961 - 0.003ms returns 0
TA260 002:464.965 JLINK_WriteReg(R14, 0x20000001)
TA260 002:464.969 - 0.003ms returns 0
TA260 002:464.973 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:464.977 - 0.003ms returns 0
TA260 002:464.981 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:464.984 - 0.003ms returns 0
TA260 002:464.988 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:464.992 - 0.003ms returns 0
TA260 002:464.996 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:464.999 - 0.003ms returns 0
TA260 002:465.003 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:465.006 - 0.003ms returns 0
TA260 002:465.011 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:465.014 - 0.004ms returns 0x00000015
TA260 002:465.019 JLINK_Go()
TA260 002:465.026   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:467.800 - 2.781ms 
TA260 002:467.814 JLINK_IsHalted()
TA260 002:468.266 - 0.451ms returns FALSE
TA260 002:468.271 JLINK_HasError()
TA260 002:469.381 JLINK_IsHalted()
TA260 002:469.840 - 0.459ms returns FALSE
TA260 002:469.846 JLINK_HasError()
TA260 002:471.382 JLINK_IsHalted()
TA260 002:471.877 - 0.494ms returns FALSE
TA260 002:471.883 JLINK_HasError()
TA260 002:473.379 JLINK_IsHalted()
TA260 002:475.684   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:476.118 - 2.738ms returns TRUE
TA260 002:476.126 JLINK_ReadReg(R15 (PC))
TA260 002:476.131 - 0.005ms returns 0x20000000
TA260 002:476.136 JLINK_ClrBPEx(BPHandle = 0x00000015)
TA260 002:476.139 - 0.003ms returns 0x00
TA260 002:476.144 JLINK_ReadReg(R0)
TA260 002:476.148 - 0.004ms returns 0x00000000
TA260 002:476.514 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:476.523   Data:  69 46 10 22 FE F7 82 FD 00 28 18 BF FE F7 E8 FC ...
TA260 002:476.534   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:479.100 - 2.585ms returns 0x27C
TA260 002:479.116 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:479.120   Data:  C3 61 FF F7 E5 FB 00 28 08 BF 80 BD BD E8 80 40 ...
TA260 002:479.129   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:481.007 - 1.891ms returns 0x184
TA260 002:481.016 JLINK_HasError()
TA260 002:481.050 JLINK_WriteReg(R0, 0x08002800)
TA260 002:481.058 - 0.008ms returns 0
TA260 002:481.063 JLINK_WriteReg(R1, 0x00000400)
TA260 002:481.066 - 0.003ms returns 0
TA260 002:481.070 JLINK_WriteReg(R2, 0x20000184)
TA260 002:481.074 - 0.003ms returns 0
TA260 002:481.078 JLINK_WriteReg(R3, 0x00000000)
TA260 002:481.082 - 0.003ms returns 0
TA260 002:481.086 JLINK_WriteReg(R4, 0x00000000)
TA260 002:481.089 - 0.003ms returns 0
TA260 002:481.093 JLINK_WriteReg(R5, 0x00000000)
TA260 002:481.100 - 0.007ms returns 0
TA260 002:481.105 JLINK_WriteReg(R6, 0x00000000)
TA260 002:481.108 - 0.003ms returns 0
TA260 002:481.112 JLINK_WriteReg(R7, 0x00000000)
TA260 002:481.115 - 0.003ms returns 0
TA260 002:481.119 JLINK_WriteReg(R8, 0x00000000)
TA260 002:481.123 - 0.003ms returns 0
TA260 002:481.127 JLINK_WriteReg(R9, 0x20000180)
TA260 002:481.130 - 0.003ms returns 0
TA260 002:481.134 JLINK_WriteReg(R10, 0x00000000)
TA260 002:481.138 - 0.003ms returns 0
TA260 002:481.142 JLINK_WriteReg(R11, 0x00000000)
TA260 002:481.145 - 0.003ms returns 0
TA260 002:481.149 JLINK_WriteReg(R12, 0x00000000)
TA260 002:481.155 - 0.006ms returns 0
TA260 002:481.160 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:481.164 - 0.003ms returns 0
TA260 002:481.168 JLINK_WriteReg(R14, 0x20000001)
TA260 002:481.171 - 0.003ms returns 0
TA260 002:481.176 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:481.179 - 0.003ms returns 0
TA260 002:481.183 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:481.187 - 0.003ms returns 0
TA260 002:481.191 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:481.194 - 0.003ms returns 0
TA260 002:481.198 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:481.202 - 0.003ms returns 0
TA260 002:481.206 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:481.209 - 0.003ms returns 0
TA260 002:481.214 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:481.218 - 0.004ms returns 0x00000016
TA260 002:481.222 JLINK_Go()
TA260 002:481.230   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:484.049 - 2.827ms 
TA260 002:484.056 JLINK_IsHalted()
TA260 002:484.507 - 0.450ms returns FALSE
TA260 002:484.513 JLINK_HasError()
TA260 002:486.391 JLINK_IsHalted()
TA260 002:487.092 - 0.700ms returns FALSE
TA260 002:487.104 JLINK_HasError()
TA260 002:488.393 JLINK_IsHalted()
TA260 002:490.684   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:491.177 - 2.784ms returns TRUE
TA260 002:491.185 JLINK_ReadReg(R15 (PC))
TA260 002:491.190 - 0.005ms returns 0x20000000
TA260 002:491.201 JLINK_ClrBPEx(BPHandle = 0x00000016)
TA260 002:491.205 - 0.004ms returns 0x00
TA260 002:491.210 JLINK_ReadReg(R0)
TA260 002:491.213 - 0.003ms returns 0x00000000
TA260 002:491.584 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:491.591   Data:  81 06 04 5D 45 5D F6 68 45 EA 04 24 34 80 02 24 ...
TA260 002:491.602   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:494.241 - 2.657ms returns 0x27C
TA260 002:494.252 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:494.255   Data:  B3 F9 48 F2 1F 51 C5 F2 EB 11 A0 FB 01 23 5A 09 ...
TA260 002:494.264   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:496.155 - 1.902ms returns 0x184
TA260 002:496.170 JLINK_HasError()
TA260 002:496.176 JLINK_WriteReg(R0, 0x08002C00)
TA260 002:496.182 - 0.006ms returns 0
TA260 002:496.187 JLINK_WriteReg(R1, 0x00000400)
TA260 002:496.190 - 0.003ms returns 0
TA260 002:496.194 JLINK_WriteReg(R2, 0x20000184)
TA260 002:496.198 - 0.003ms returns 0
TA260 002:496.202 JLINK_WriteReg(R3, 0x00000000)
TA260 002:496.206 - 0.003ms returns 0
TA260 002:496.210 JLINK_WriteReg(R4, 0x00000000)
TA260 002:496.213 - 0.003ms returns 0
TA260 002:496.217 JLINK_WriteReg(R5, 0x00000000)
TA260 002:496.220 - 0.003ms returns 0
TA260 002:496.224 JLINK_WriteReg(R6, 0x00000000)
TA260 002:496.228 - 0.003ms returns 0
TA260 002:496.232 JLINK_WriteReg(R7, 0x00000000)
TA260 002:496.235 - 0.003ms returns 0
TA260 002:496.239 JLINK_WriteReg(R8, 0x00000000)
TA260 002:496.243 - 0.003ms returns 0
TA260 002:496.247 JLINK_WriteReg(R9, 0x20000180)
TA260 002:496.250 - 0.003ms returns 0
TA260 002:496.254 JLINK_WriteReg(R10, 0x00000000)
TA260 002:496.257 - 0.003ms returns 0
TA260 002:496.262 JLINK_WriteReg(R11, 0x00000000)
TA260 002:496.265 - 0.003ms returns 0
TA260 002:496.269 JLINK_WriteReg(R12, 0x00000000)
TA260 002:496.279 - 0.003ms returns 0
TA260 002:496.284 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:496.288 - 0.004ms returns 0
TA260 002:496.292 JLINK_WriteReg(R14, 0x20000001)
TA260 002:496.296 - 0.003ms returns 0
TA260 002:496.300 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:496.304 - 0.003ms returns 0
TA260 002:496.308 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:496.311 - 0.003ms returns 0
TA260 002:496.315 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:496.318 - 0.003ms returns 0
TA260 002:496.322 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:496.326 - 0.003ms returns 0
TA260 002:496.330 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:496.333 - 0.003ms returns 0
TA260 002:496.338 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:496.342 - 0.004ms returns 0x00000017
TA260 002:496.346 JLINK_Go()
TA260 002:496.355   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:499.226 - 2.879ms 
TA260 002:499.242 JLINK_IsHalted()
TA260 002:499.736 - 0.493ms returns FALSE
TA260 002:499.749 JLINK_HasError()
TA260 002:501.213 JLINK_IsHalted()
TA260 002:501.692 - 0.478ms returns FALSE
TA260 002:501.704 JLINK_HasError()
TA260 002:503.213 JLINK_IsHalted()
TA260 002:505.607   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:506.114 - 2.900ms returns TRUE
TA260 002:506.130 JLINK_ReadReg(R15 (PC))
TA260 002:506.136 - 0.006ms returns 0x20000000
TA260 002:506.166 JLINK_ClrBPEx(BPHandle = 0x00000017)
TA260 002:506.171 - 0.005ms returns 0x00
TA260 002:506.176 JLINK_ReadReg(R0)
TA260 002:506.180 - 0.003ms returns 0x00000000
TA260 002:506.535 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:506.542   Data:  41 0A C0 F6 00 01 01 EE 10 0A 01 EB 80 01 B8 EE ...
TA260 002:506.552   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:509.191 - 2.655ms returns 0x27C
TA260 002:509.198 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:509.202   Data:  04 2B 50 F8 04 2B 41 F8 04 2B 50 F8 04 2B 41 F8 ...
TA260 002:509.208   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:511.103 - 1.905ms returns 0x184
TA260 002:511.116 JLINK_HasError()
TA260 002:511.122 JLINK_WriteReg(R0, 0x08003000)
TA260 002:511.127 - 0.005ms returns 0
TA260 002:511.131 JLINK_WriteReg(R1, 0x00000400)
TA260 002:511.135 - 0.004ms returns 0
TA260 002:511.139 JLINK_WriteReg(R2, 0x20000184)
TA260 002:511.143 - 0.003ms returns 0
TA260 002:511.147 JLINK_WriteReg(R3, 0x00000000)
TA260 002:511.150 - 0.003ms returns 0
TA260 002:511.154 JLINK_WriteReg(R4, 0x00000000)
TA260 002:511.158 - 0.003ms returns 0
TA260 002:511.162 JLINK_WriteReg(R5, 0x00000000)
TA260 002:511.165 - 0.003ms returns 0
TA260 002:511.169 JLINK_WriteReg(R6, 0x00000000)
TA260 002:511.172 - 0.003ms returns 0
TA260 002:511.176 JLINK_WriteReg(R7, 0x00000000)
TA260 002:511.180 - 0.003ms returns 0
TA260 002:511.184 JLINK_WriteReg(R8, 0x00000000)
TA260 002:511.187 - 0.003ms returns 0
TA260 002:511.191 JLINK_WriteReg(R9, 0x20000180)
TA260 002:511.195 - 0.003ms returns 0
TA260 002:511.199 JLINK_WriteReg(R10, 0x00000000)
TA260 002:511.202 - 0.003ms returns 0
TA260 002:511.206 JLINK_WriteReg(R11, 0x00000000)
TA260 002:511.210 - 0.003ms returns 0
TA260 002:511.214 JLINK_WriteReg(R12, 0x00000000)
TA260 002:511.217 - 0.003ms returns 0
TA260 002:511.221 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:511.225 - 0.003ms returns 0
TA260 002:511.229 JLINK_WriteReg(R14, 0x20000001)
TA260 002:511.232 - 0.003ms returns 0
TA260 002:511.237 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:511.240 - 0.003ms returns 0
TA260 002:511.244 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:511.248 - 0.003ms returns 0
TA260 002:511.254 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:511.258 - 0.003ms returns 0
TA260 002:511.262 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:511.265 - 0.003ms returns 0
TA260 002:511.269 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:511.272 - 0.003ms returns 0
TA260 002:511.277 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:511.281 - 0.004ms returns 0x00000018
TA260 002:511.285 JLINK_Go()
TA260 002:511.293   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:513.974 - 2.688ms 
TA260 002:513.983 JLINK_IsHalted()
TA260 002:514.507 - 0.524ms returns FALSE
TA260 002:514.513 JLINK_HasError()
TA260 002:516.020 JLINK_IsHalted()
TA260 002:516.927 - 0.906ms returns FALSE
TA260 002:516.940 JLINK_HasError()
TA260 002:518.020 JLINK_IsHalted()
TA260 002:520.280   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:520.772 - 2.751ms returns TRUE
TA260 002:520.785 JLINK_ReadReg(R15 (PC))
TA260 002:520.791 - 0.005ms returns 0x20000000
TA260 002:520.795 JLINK_ClrBPEx(BPHandle = 0x00000018)
TA260 002:520.799 - 0.004ms returns 0x00
TA260 002:520.804 JLINK_ReadReg(R0)
TA260 002:520.807 - 0.003ms returns 0x00000000
TA260 002:521.186 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:521.194   Data:  04 2B 50 F8 04 2B 41 F8 04 2B 0F CB FF F7 74 F9 ...
TA260 002:521.205   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:523.795 - 2.608ms returns 0x27C
TA260 002:523.809 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:523.814   Data:  01 1A 80 EE 01 0A 9F ED 5A 1A 20 EE 01 0A 10 EE ...
TA260 002:523.823   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:525.696 - 1.886ms returns 0x184
TA260 002:525.709 JLINK_HasError()
TA260 002:525.715 JLINK_WriteReg(R0, 0x08003400)
TA260 002:525.720 - 0.005ms returns 0
TA260 002:525.724 JLINK_WriteReg(R1, 0x00000400)
TA260 002:525.728 - 0.003ms returns 0
TA260 002:525.732 JLINK_WriteReg(R2, 0x20000184)
TA260 002:525.736 - 0.003ms returns 0
TA260 002:525.740 JLINK_WriteReg(R3, 0x00000000)
TA260 002:525.743 - 0.003ms returns 0
TA260 002:525.747 JLINK_WriteReg(R4, 0x00000000)
TA260 002:525.751 - 0.003ms returns 0
TA260 002:525.755 JLINK_WriteReg(R5, 0x00000000)
TA260 002:525.758 - 0.003ms returns 0
TA260 002:525.762 JLINK_WriteReg(R6, 0x00000000)
TA260 002:525.766 - 0.003ms returns 0
TA260 002:525.770 JLINK_WriteReg(R7, 0x00000000)
TA260 002:525.773 - 0.003ms returns 0
TA260 002:525.777 JLINK_WriteReg(R8, 0x00000000)
TA260 002:525.780 - 0.003ms returns 0
TA260 002:525.784 JLINK_WriteReg(R9, 0x20000180)
TA260 002:525.788 - 0.003ms returns 0
TA260 002:525.792 JLINK_WriteReg(R10, 0x00000000)
TA260 002:525.795 - 0.003ms returns 0
TA260 002:525.799 JLINK_WriteReg(R11, 0x00000000)
TA260 002:525.803 - 0.003ms returns 0
TA260 002:525.807 JLINK_WriteReg(R12, 0x00000000)
TA260 002:525.810 - 0.003ms returns 0
TA260 002:525.814 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:525.818 - 0.003ms returns 0
TA260 002:525.823 JLINK_WriteReg(R14, 0x20000001)
TA260 002:525.827 - 0.003ms returns 0
TA260 002:525.831 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:525.834 - 0.003ms returns 0
TA260 002:525.839 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:525.842 - 0.003ms returns 0
TA260 002:525.846 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:525.850 - 0.003ms returns 0
TA260 002:525.854 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:525.857 - 0.003ms returns 0
TA260 002:525.861 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:525.865 - 0.003ms returns 0
TA260 002:525.870 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:525.896 - 0.026ms returns 0x00000019
TA260 002:525.904 JLINK_Go()
TA260 002:525.912   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:528.586 - 2.681ms 
TA260 002:528.598 JLINK_IsHalted()
TA260 002:529.035 - 0.436ms returns FALSE
TA260 002:529.042 JLINK_HasError()
TA260 002:530.526 JLINK_IsHalted()
TA260 002:531.003 - 0.477ms returns FALSE
TA260 002:531.009 JLINK_HasError()
TA260 002:532.528 JLINK_IsHalted()
TA260 002:534.857   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:535.335 - 2.806ms returns TRUE
TA260 002:535.341 JLINK_ReadReg(R15 (PC))
TA260 002:535.346 - 0.004ms returns 0x20000000
TA260 002:535.351 JLINK_ClrBPEx(BPHandle = 0x00000019)
TA260 002:535.354 - 0.003ms returns 0x00
TA260 002:535.359 JLINK_ReadReg(R0)
TA260 002:535.362 - 0.003ms returns 0x00000000
TA260 002:535.715 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:535.722   Data:  00 60 40 21 FD F7 26 FF B0 FA 80 F0 45 09 20 46 ...
TA260 002:535.733   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:538.283 - 2.567ms returns 0x27C
TA260 002:538.296 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:538.300   Data:  50 F8 04 2B 69 46 41 F8 04 2B 50 F8 04 2B 41 F8 ...
TA260 002:538.309   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:540.195 - 1.898ms returns 0x184
TA260 002:540.208 JLINK_HasError()
TA260 002:540.214 JLINK_WriteReg(R0, 0x08003800)
TA260 002:540.219 - 0.005ms returns 0
TA260 002:540.224 JLINK_WriteReg(R1, 0x00000400)
TA260 002:540.227 - 0.003ms returns 0
TA260 002:540.232 JLINK_WriteReg(R2, 0x20000184)
TA260 002:540.235 - 0.003ms returns 0
TA260 002:540.239 JLINK_WriteReg(R3, 0x00000000)
TA260 002:540.243 - 0.003ms returns 0
TA260 002:540.247 JLINK_WriteReg(R4, 0x00000000)
TA260 002:540.250 - 0.003ms returns 0
TA260 002:540.254 JLINK_WriteReg(R5, 0x00000000)
TA260 002:540.258 - 0.003ms returns 0
TA260 002:540.262 JLINK_WriteReg(R6, 0x00000000)
TA260 002:540.265 - 0.003ms returns 0
TA260 002:540.275 JLINK_WriteReg(R7, 0x00000000)
TA260 002:540.278 - 0.003ms returns 0
TA260 002:540.283 JLINK_WriteReg(R8, 0x00000000)
TA260 002:540.286 - 0.003ms returns 0
TA260 002:540.290 JLINK_WriteReg(R9, 0x20000180)
TA260 002:540.294 - 0.003ms returns 0
TA260 002:540.298 JLINK_WriteReg(R10, 0x00000000)
TA260 002:540.301 - 0.003ms returns 0
TA260 002:540.305 JLINK_WriteReg(R11, 0x00000000)
TA260 002:540.308 - 0.003ms returns 0
TA260 002:540.312 JLINK_WriteReg(R12, 0x00000000)
TA260 002:540.316 - 0.003ms returns 0
TA260 002:540.320 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:540.324 - 0.003ms returns 0
TA260 002:540.328 JLINK_WriteReg(R14, 0x20000001)
TA260 002:540.331 - 0.003ms returns 0
TA260 002:540.335 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:540.339 - 0.003ms returns 0
TA260 002:540.343 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:540.346 - 0.003ms returns 0
TA260 002:540.350 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:540.354 - 0.003ms returns 0
TA260 002:540.358 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:540.361 - 0.003ms returns 0
TA260 002:540.365 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:540.368 - 0.003ms returns 0
TA260 002:540.373 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:540.378 - 0.005ms returns 0x0000001A
TA260 002:540.382 JLINK_Go()
TA260 002:540.390   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:543.119 - 2.736ms 
TA260 002:543.125 JLINK_IsHalted()
TA260 002:543.599 - 0.474ms returns FALSE
TA260 002:543.604 JLINK_HasError()
TA260 002:545.043 JLINK_IsHalted()
TA260 002:545.639 - 0.595ms returns FALSE
TA260 002:545.646 JLINK_HasError()
TA260 002:547.544 JLINK_IsHalted()
TA260 002:549.882   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:550.376 - 2.831ms returns TRUE
TA260 002:550.386 JLINK_ReadReg(R15 (PC))
TA260 002:550.392 - 0.006ms returns 0x20000000
TA260 002:550.397 JLINK_ClrBPEx(BPHandle = 0x0000001A)
TA260 002:550.401 - 0.004ms returns 0x00
TA260 002:550.405 JLINK_ReadReg(R0)
TA260 002:550.409 - 0.003ms returns 0x00000000
TA260 002:550.798 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:550.806   Data:  70 40 02 B0 70 47 00 00 40 F2 0C 40 C2 F2 00 00 ...
TA260 002:550.817   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:553.493 - 2.695ms returns 0x27C
TA260 002:553.507 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:553.511   Data:  BA F1 00 0F 13 D0 02 20 00 F0 80 FE 00 F0 4E FE ...
TA260 002:553.521   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:555.400 - 1.892ms returns 0x184
TA260 002:555.413 JLINK_HasError()
TA260 002:555.419 JLINK_WriteReg(R0, 0x08003C00)
TA260 002:555.425 - 0.005ms returns 0
TA260 002:555.429 JLINK_WriteReg(R1, 0x00000400)
TA260 002:555.433 - 0.003ms returns 0
TA260 002:555.437 JLINK_WriteReg(R2, 0x20000184)
TA260 002:555.440 - 0.003ms returns 0
TA260 002:555.445 JLINK_WriteReg(R3, 0x00000000)
TA260 002:555.448 - 0.003ms returns 0
TA260 002:555.452 JLINK_WriteReg(R4, 0x00000000)
TA260 002:555.456 - 0.003ms returns 0
TA260 002:555.460 JLINK_WriteReg(R5, 0x00000000)
TA260 002:555.463 - 0.003ms returns 0
TA260 002:555.467 JLINK_WriteReg(R6, 0x00000000)
TA260 002:555.471 - 0.003ms returns 0
TA260 002:555.475 JLINK_WriteReg(R7, 0x00000000)
TA260 002:555.478 - 0.003ms returns 0
TA260 002:555.482 JLINK_WriteReg(R8, 0x00000000)
TA260 002:555.486 - 0.003ms returns 0
TA260 002:555.490 JLINK_WriteReg(R9, 0x20000180)
TA260 002:555.494 - 0.003ms returns 0
TA260 002:555.498 JLINK_WriteReg(R10, 0x00000000)
TA260 002:555.501 - 0.003ms returns 0
TA260 002:555.505 JLINK_WriteReg(R11, 0x00000000)
TA260 002:555.509 - 0.003ms returns 0
TA260 002:555.513 JLINK_WriteReg(R12, 0x00000000)
TA260 002:555.516 - 0.003ms returns 0
TA260 002:555.521 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:555.525 - 0.004ms returns 0
TA260 002:555.529 JLINK_WriteReg(R14, 0x20000001)
TA260 002:555.532 - 0.003ms returns 0
TA260 002:555.537 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:555.686 - 0.149ms returns 0
TA260 002:555.690 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:555.694 - 0.003ms returns 0
TA260 002:555.708 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:555.711 - 0.003ms returns 0
TA260 002:555.716 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:555.719 - 0.003ms returns 0
TA260 002:555.723 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:555.726 - 0.003ms returns 0
TA260 002:555.731 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:555.736 - 0.004ms returns 0x0000001B
TA260 002:555.740 JLINK_Go()
TA260 002:555.750   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:558.508 - 2.766ms 
TA260 002:558.536 JLINK_IsHalted()
TA260 002:559.007 - 0.470ms returns FALSE
TA260 002:559.016 JLINK_HasError()
TA260 002:560.053 JLINK_IsHalted()
TA260 002:560.532 - 0.478ms returns FALSE
TA260 002:560.544 JLINK_HasError()
TA260 002:562.051 JLINK_IsHalted()
TA260 002:562.508 - 0.456ms returns FALSE
TA260 002:562.513 JLINK_HasError()
TA260 002:564.050 JLINK_IsHalted()
TA260 002:566.395   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:566.904 - 2.853ms returns TRUE
TA260 002:566.914 JLINK_ReadReg(R15 (PC))
TA260 002:566.919 - 0.005ms returns 0x20000000
TA260 002:566.924 JLINK_ClrBPEx(BPHandle = 0x0000001B)
TA260 002:566.928 - 0.004ms returns 0x00
TA260 002:566.932 JLINK_ReadReg(R0)
TA260 002:566.936 - 0.003ms returns 0x00000000
TA260 002:567.315 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:567.324   Data:  80 45 70 DD 4D 49 4E 48 88 45 14 DD B0 42 0D DB ...
TA260 002:567.335   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:569.920 - 2.604ms returns 0x27C
TA260 002:569.934 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:569.938   Data:  1B 0B 53 EC 10 2B 9D ED 0A 0B 51 EC 10 0B FC F7 ...
TA260 002:569.949   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:571.833 - 1.898ms returns 0x184
TA260 002:571.847 JLINK_HasError()
TA260 002:571.853 JLINK_WriteReg(R0, 0x08004000)
TA260 002:571.858 - 0.005ms returns 0
TA260 002:571.862 JLINK_WriteReg(R1, 0x00000400)
TA260 002:571.866 - 0.003ms returns 0
TA260 002:571.870 JLINK_WriteReg(R2, 0x20000184)
TA260 002:571.873 - 0.003ms returns 0
TA260 002:571.877 JLINK_WriteReg(R3, 0x00000000)
TA260 002:571.881 - 0.003ms returns 0
TA260 002:571.885 JLINK_WriteReg(R4, 0x00000000)
TA260 002:571.888 - 0.003ms returns 0
TA260 002:571.893 JLINK_WriteReg(R5, 0x00000000)
TA260 002:571.896 - 0.003ms returns 0
TA260 002:571.900 JLINK_WriteReg(R6, 0x00000000)
TA260 002:571.904 - 0.003ms returns 0
TA260 002:571.908 JLINK_WriteReg(R7, 0x00000000)
TA260 002:571.911 - 0.003ms returns 0
TA260 002:571.915 JLINK_WriteReg(R8, 0x00000000)
TA260 002:571.918 - 0.003ms returns 0
TA260 002:571.922 JLINK_WriteReg(R9, 0x20000180)
TA260 002:571.926 - 0.003ms returns 0
TA260 002:571.930 JLINK_WriteReg(R10, 0x00000000)
TA260 002:571.933 - 0.003ms returns 0
TA260 002:571.938 JLINK_WriteReg(R11, 0x00000000)
TA260 002:571.941 - 0.003ms returns 0
TA260 002:571.945 JLINK_WriteReg(R12, 0x00000000)
TA260 002:571.948 - 0.003ms returns 0
TA260 002:571.953 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:571.956 - 0.003ms returns 0
TA260 002:571.960 JLINK_WriteReg(R14, 0x20000001)
TA260 002:571.964 - 0.003ms returns 0
TA260 002:571.968 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:571.971 - 0.003ms returns 0
TA260 002:571.976 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:571.979 - 0.003ms returns 0
TA260 002:571.983 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:571.987 - 0.003ms returns 0
TA260 002:571.991 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:571.994 - 0.003ms returns 0
TA260 002:571.998 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:572.002 - 0.003ms returns 0
TA260 002:572.006 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:572.010 - 0.004ms returns 0x0000001C
TA260 002:572.014 JLINK_Go()
TA260 002:572.023   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:574.724 - 2.708ms 
TA260 002:574.736 JLINK_IsHalted()
TA260 002:575.232 - 0.496ms returns FALSE
TA260 002:575.238 JLINK_HasError()
TA260 002:577.417 JLINK_IsHalted()
TA260 002:577.907 - 0.488ms returns FALSE
TA260 002:577.913 JLINK_HasError()
TA260 002:579.414 JLINK_IsHalted()
TA260 002:581.775   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:582.241 - 2.826ms returns TRUE
TA260 002:582.250 JLINK_ReadReg(R15 (PC))
TA260 002:582.256 - 0.005ms returns 0x20000000
TA260 002:582.261 JLINK_ClrBPEx(BPHandle = 0x0000001C)
TA260 002:582.265 - 0.004ms returns 0x00
TA260 002:582.270 JLINK_ReadReg(R0)
TA260 002:582.273 - 0.003ms returns 0x00000000
TA260 002:582.644 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:582.655   Data:  12 0B 41 EC 1D 0B 53 EC 10 2B 9D ED 0E 0B 51 EC ...
TA260 002:582.666   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:585.306 - 2.661ms returns 0x27C
TA260 002:585.330 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:585.334   Data:  90 0A 8D ED 02 0B 8D ED 00 8B 61 F3 1E 00 03 90 ...
TA260 002:585.344   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:587.252 - 1.921ms returns 0x184
TA260 002:587.278 JLINK_HasError()
TA260 002:587.316 JLINK_WriteReg(R0, 0x08004400)
TA260 002:587.324 - 0.008ms returns 0
TA260 002:587.328 JLINK_WriteReg(R1, 0x00000400)
TA260 002:587.332 - 0.003ms returns 0
TA260 002:587.336 JLINK_WriteReg(R2, 0x20000184)
TA260 002:587.340 - 0.003ms returns 0
TA260 002:587.344 JLINK_WriteReg(R3, 0x00000000)
TA260 002:587.347 - 0.003ms returns 0
TA260 002:587.351 JLINK_WriteReg(R4, 0x00000000)
TA260 002:587.355 - 0.003ms returns 0
TA260 002:587.360 JLINK_WriteReg(R5, 0x00000000)
TA260 002:587.363 - 0.003ms returns 0
TA260 002:587.367 JLINK_WriteReg(R6, 0x00000000)
TA260 002:587.370 - 0.003ms returns 0
TA260 002:587.374 JLINK_WriteReg(R7, 0x00000000)
TA260 002:587.378 - 0.003ms returns 0
TA260 002:587.382 JLINK_WriteReg(R8, 0x00000000)
TA260 002:587.385 - 0.003ms returns 0
TA260 002:587.389 JLINK_WriteReg(R9, 0x20000180)
TA260 002:587.392 - 0.003ms returns 0
TA260 002:587.396 JLINK_WriteReg(R10, 0x00000000)
TA260 002:587.400 - 0.003ms returns 0
TA260 002:587.404 JLINK_WriteReg(R11, 0x00000000)
TA260 002:587.407 - 0.003ms returns 0
TA260 002:587.411 JLINK_WriteReg(R12, 0x00000000)
TA260 002:587.414 - 0.003ms returns 0
TA260 002:587.419 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:587.422 - 0.004ms returns 0
TA260 002:587.426 JLINK_WriteReg(R14, 0x20000001)
TA260 002:587.430 - 0.003ms returns 0
TA260 002:587.434 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:587.438 - 0.003ms returns 0
TA260 002:587.442 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:587.445 - 0.003ms returns 0
TA260 002:587.449 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:587.453 - 0.003ms returns 0
TA260 002:587.457 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:587.460 - 0.003ms returns 0
TA260 002:587.464 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:587.468 - 0.003ms returns 0
TA260 002:587.472 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:587.477 - 0.004ms returns 0x0000001D
TA260 002:587.481 JLINK_Go()
TA260 002:587.490   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:590.213 - 2.731ms 
TA260 002:590.220 JLINK_IsHalted()
TA260 002:590.689 - 0.468ms returns FALSE
TA260 002:590.694 JLINK_HasError()
TA260 002:594.942 JLINK_IsHalted()
TA260 002:597.323   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:597.838 - 2.896ms returns TRUE
TA260 002:597.846 JLINK_ReadReg(R15 (PC))
TA260 002:597.851 - 0.005ms returns 0x20000000
TA260 002:597.856 JLINK_ClrBPEx(BPHandle = 0x0000001D)
TA260 002:597.860 - 0.004ms returns 0x00
TA260 002:597.864 JLINK_ReadReg(R0)
TA260 002:597.868 - 0.003ms returns 0x00000000
TA260 002:598.170 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:598.177   Data:  10 0B 53 EC 11 2B FB F7 E9 FD 9D ED 02 1B 53 EC ...
TA260 002:598.187   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:600.795 - 2.624ms returns 0x27C
TA260 002:600.808 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:600.812   Data:  FB F7 07 FC 41 EC 10 0B 53 EC 18 2B 51 EC 10 0B ...
TA260 002:600.821   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:602.733 - 1.925ms returns 0x184
TA260 002:602.740 JLINK_HasError()
TA260 002:602.744 JLINK_WriteReg(R0, 0x08004800)
TA260 002:602.749 - 0.004ms returns 0
TA260 002:602.753 JLINK_WriteReg(R1, 0x00000400)
TA260 002:602.761 - 0.007ms returns 0
TA260 002:602.765 JLINK_WriteReg(R2, 0x20000184)
TA260 002:602.768 - 0.003ms returns 0
TA260 002:602.772 JLINK_WriteReg(R3, 0x00000000)
TA260 002:602.776 - 0.003ms returns 0
TA260 002:602.780 JLINK_WriteReg(R4, 0x00000000)
TA260 002:602.783 - 0.003ms returns 0
TA260 002:602.787 JLINK_WriteReg(R5, 0x00000000)
TA260 002:602.791 - 0.003ms returns 0
TA260 002:602.795 JLINK_WriteReg(R6, 0x00000000)
TA260 002:602.798 - 0.003ms returns 0
TA260 002:602.802 JLINK_WriteReg(R7, 0x00000000)
TA260 002:602.806 - 0.003ms returns 0
TA260 002:602.810 JLINK_WriteReg(R8, 0x00000000)
TA260 002:602.813 - 0.003ms returns 0
TA260 002:602.817 JLINK_WriteReg(R9, 0x20000180)
TA260 002:602.820 - 0.003ms returns 0
TA260 002:602.824 JLINK_WriteReg(R10, 0x00000000)
TA260 002:602.828 - 0.003ms returns 0
TA260 002:602.832 JLINK_WriteReg(R11, 0x00000000)
TA260 002:602.835 - 0.003ms returns 0
TA260 002:602.839 JLINK_WriteReg(R12, 0x00000000)
TA260 002:602.842 - 0.003ms returns 0
TA260 002:602.846 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:602.850 - 0.003ms returns 0
TA260 002:602.854 JLINK_WriteReg(R14, 0x20000001)
TA260 002:602.858 - 0.003ms returns 0
TA260 002:602.862 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:602.865 - 0.003ms returns 0
TA260 002:602.869 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:602.872 - 0.003ms returns 0
TA260 002:602.876 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:602.880 - 0.003ms returns 0
TA260 002:602.884 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:602.887 - 0.003ms returns 0
TA260 002:602.891 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:602.895 - 0.003ms returns 0
TA260 002:602.899 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:602.903 - 0.004ms returns 0x0000001E
TA260 002:602.908 JLINK_Go()
TA260 002:602.915   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:605.596 - 2.688ms 
TA260 002:605.602 JLINK_IsHalted()
TA260 002:606.078 - 0.474ms returns FALSE
TA260 002:606.090 JLINK_HasError()
TA260 002:607.456 JLINK_IsHalted()
TA260 002:607.962 - 0.505ms returns FALSE
TA260 002:607.968 JLINK_HasError()
TA260 002:609.453 JLINK_IsHalted()
TA260 002:609.887 - 0.433ms returns FALSE
TA260 002:609.900 JLINK_HasError()
TA260 002:611.456 JLINK_IsHalted()
TA260 002:613.823   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:614.316 - 2.859ms returns TRUE
TA260 002:614.327 JLINK_ReadReg(R15 (PC))
TA260 002:614.331 - 0.004ms returns 0x20000000
TA260 002:614.362 JLINK_ClrBPEx(BPHandle = 0x0000001E)
TA260 002:614.367 - 0.005ms returns 0x00
TA260 002:614.371 JLINK_ReadReg(R0)
TA260 002:614.375 - 0.003ms returns 0x00000000
TA260 002:614.677 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:614.683   Data:  DF E7 04 46 00 21 40 4A 49 18 42 EB 04 50 CD E9 ...
TA260 002:614.693   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:617.298 - 2.620ms returns 0x27C
TA260 002:617.314 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:617.318   Data:  C1 E9 00 52 F6 E7 D9 F8 00 10 0D 80 F2 E7 0D 70 ...
TA260 002:617.328   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:619.204 - 1.889ms returns 0x184
TA260 002:619.217 JLINK_HasError()
TA260 002:619.222 JLINK_WriteReg(R0, 0x08004C00)
TA260 002:619.227 - 0.005ms returns 0
TA260 002:619.231 JLINK_WriteReg(R1, 0x00000400)
TA260 002:619.235 - 0.003ms returns 0
TA260 002:619.239 JLINK_WriteReg(R2, 0x20000184)
TA260 002:619.242 - 0.003ms returns 0
TA260 002:619.246 JLINK_WriteReg(R3, 0x00000000)
TA260 002:619.250 - 0.003ms returns 0
TA260 002:619.254 JLINK_WriteReg(R4, 0x00000000)
TA260 002:619.257 - 0.003ms returns 0
TA260 002:619.261 JLINK_WriteReg(R5, 0x00000000)
TA260 002:619.265 - 0.003ms returns 0
TA260 002:619.269 JLINK_WriteReg(R6, 0x00000000)
TA260 002:619.272 - 0.003ms returns 0
TA260 002:619.276 JLINK_WriteReg(R7, 0x00000000)
TA260 002:619.279 - 0.003ms returns 0
TA260 002:619.284 JLINK_WriteReg(R8, 0x00000000)
TA260 002:619.287 - 0.003ms returns 0
TA260 002:619.291 JLINK_WriteReg(R9, 0x20000180)
TA260 002:619.295 - 0.003ms returns 0
TA260 002:619.302 JLINK_WriteReg(R10, 0x00000000)
TA260 002:619.307 - 0.005ms returns 0
TA260 002:619.311 JLINK_WriteReg(R11, 0x00000000)
TA260 002:619.314 - 0.003ms returns 0
TA260 002:619.318 JLINK_WriteReg(R12, 0x00000000)
TA260 002:619.322 - 0.003ms returns 0
TA260 002:619.326 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:619.329 - 0.003ms returns 0
TA260 002:619.333 JLINK_WriteReg(R14, 0x20000001)
TA260 002:619.337 - 0.003ms returns 0
TA260 002:619.341 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:619.344 - 0.003ms returns 0
TA260 002:619.348 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:619.352 - 0.003ms returns 0
TA260 002:619.356 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:619.359 - 0.003ms returns 0
TA260 002:619.363 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:619.366 - 0.003ms returns 0
TA260 002:619.370 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:619.374 - 0.003ms returns 0
TA260 002:619.379 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:619.383 - 0.004ms returns 0x0000001F
TA260 002:619.387 JLINK_Go()
TA260 002:619.401   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:622.176 - 2.788ms 
TA260 002:622.182 JLINK_IsHalted()
TA260 002:622.655 - 0.472ms returns FALSE
TA260 002:622.661 JLINK_HasError()
TA260 002:623.958 JLINK_IsHalted()
TA260 002:624.460 - 0.501ms returns FALSE
TA260 002:624.465 JLINK_HasError()
TA260 002:626.472 JLINK_IsHalted()
TA260 002:628.782   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:629.268 - 2.795ms returns TRUE
TA260 002:629.275 JLINK_ReadReg(R15 (PC))
TA260 002:629.280 - 0.005ms returns 0x20000000
TA260 002:629.285 JLINK_ClrBPEx(BPHandle = 0x0000001F)
TA260 002:629.289 - 0.003ms returns 0x00
TA260 002:629.293 JLINK_ReadReg(R0)
TA260 002:629.296 - 0.003ms returns 0x00000000
TA260 002:629.662 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:629.670   Data:  0E E0 40 22 8D F8 04 20 01 22 08 E0 5C EA 01 02 ...
TA260 002:629.687   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:632.292 - 2.630ms returns 0x27C
TA260 002:632.302 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:632.306   Data:  30 29 08 D0 B8 42 02 DA 10 F1 04 0F 06 DA 01 21 ...
TA260 002:632.314   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:634.192 - 1.890ms returns 0x184
TA260 002:634.200 JLINK_HasError()
TA260 002:634.204 JLINK_WriteReg(R0, 0x08005000)
TA260 002:634.209 - 0.004ms returns 0
TA260 002:634.214 JLINK_WriteReg(R1, 0x00000400)
TA260 002:634.217 - 0.003ms returns 0
TA260 002:634.221 JLINK_WriteReg(R2, 0x20000184)
TA260 002:634.225 - 0.003ms returns 0
TA260 002:634.229 JLINK_WriteReg(R3, 0x00000000)
TA260 002:634.233 - 0.003ms returns 0
TA260 002:634.237 JLINK_WriteReg(R4, 0x00000000)
TA260 002:634.240 - 0.003ms returns 0
TA260 002:634.250 JLINK_WriteReg(R5, 0x00000000)
TA260 002:634.254 - 0.004ms returns 0
TA260 002:634.258 JLINK_WriteReg(R6, 0x00000000)
TA260 002:634.261 - 0.003ms returns 0
TA260 002:634.265 JLINK_WriteReg(R7, 0x00000000)
TA260 002:634.269 - 0.003ms returns 0
TA260 002:634.273 JLINK_WriteReg(R8, 0x00000000)
TA260 002:634.276 - 0.003ms returns 0
TA260 002:634.280 JLINK_WriteReg(R9, 0x20000180)
TA260 002:634.283 - 0.003ms returns 0
TA260 002:634.287 JLINK_WriteReg(R10, 0x00000000)
TA260 002:634.291 - 0.003ms returns 0
TA260 002:634.295 JLINK_WriteReg(R11, 0x00000000)
TA260 002:634.298 - 0.003ms returns 0
TA260 002:634.302 JLINK_WriteReg(R12, 0x00000000)
TA260 002:634.305 - 0.003ms returns 0
TA260 002:634.310 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:634.313 - 0.003ms returns 0
TA260 002:634.317 JLINK_WriteReg(R14, 0x20000001)
TA260 002:634.321 - 0.003ms returns 0
TA260 002:634.325 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:634.328 - 0.003ms returns 0
TA260 002:634.333 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:634.337 - 0.004ms returns 0
TA260 002:634.341 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:634.344 - 0.003ms returns 0
TA260 002:634.348 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:634.352 - 0.003ms returns 0
TA260 002:634.356 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:634.368 - 0.012ms returns 0
TA260 002:634.379 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:634.384 - 0.004ms returns 0x00000020
TA260 002:634.389 JLINK_Go()
TA260 002:634.397   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:637.206 - 2.816ms 
TA260 002:637.222 JLINK_IsHalted()
TA260 002:637.703 - 0.480ms returns FALSE
TA260 002:637.718 JLINK_HasError()
TA260 002:640.975 JLINK_IsHalted()
TA260 002:641.506 - 0.530ms returns FALSE
TA260 002:641.512 JLINK_HasError()
TA260 002:642.972 JLINK_IsHalted()
TA260 002:645.271   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:645.785 - 2.813ms returns TRUE
TA260 002:645.814 JLINK_ReadReg(R15 (PC))
TA260 002:645.821 - 0.006ms returns 0x20000000
TA260 002:645.826 JLINK_ClrBPEx(BPHandle = 0x00000020)
TA260 002:645.830 - 0.004ms returns 0x00
TA260 002:645.834 JLINK_ReadReg(R0)
TA260 002:645.838 - 0.003ms returns 0x00000000
TA260 002:646.159 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:646.166   Data:  88 04 04 D4 05 E0 39 46 20 20 B0 47 6D 1C 64 1E ...
TA260 002:646.177   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:648.702 - 2.542ms returns 0x27C
TA260 002:648.714 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:648.718   Data:  B0 02 B0 06 B0 01 B0 05 B0 03 B0 07 70 00 70 04 ...
TA260 002:648.728   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:650.613 - 1.898ms returns 0x184
TA260 002:650.621 JLINK_HasError()
TA260 002:650.626 JLINK_WriteReg(R0, 0x08005400)
TA260 002:650.631 - 0.005ms returns 0
TA260 002:650.635 JLINK_WriteReg(R1, 0x00000400)
TA260 002:650.639 - 0.003ms returns 0
TA260 002:650.643 JLINK_WriteReg(R2, 0x20000184)
TA260 002:650.646 - 0.003ms returns 0
TA260 002:650.650 JLINK_WriteReg(R3, 0x00000000)
TA260 002:650.654 - 0.003ms returns 0
TA260 002:650.658 JLINK_WriteReg(R4, 0x00000000)
TA260 002:650.661 - 0.003ms returns 0
TA260 002:650.666 JLINK_WriteReg(R5, 0x00000000)
TA260 002:650.669 - 0.003ms returns 0
TA260 002:650.673 JLINK_WriteReg(R6, 0x00000000)
TA260 002:650.676 - 0.003ms returns 0
TA260 002:650.680 JLINK_WriteReg(R7, 0x00000000)
TA260 002:650.684 - 0.003ms returns 0
TA260 002:650.688 JLINK_WriteReg(R8, 0x00000000)
TA260 002:650.691 - 0.003ms returns 0
TA260 002:650.695 JLINK_WriteReg(R9, 0x20000180)
TA260 002:650.698 - 0.003ms returns 0
TA260 002:650.703 JLINK_WriteReg(R10, 0x00000000)
TA260 002:650.706 - 0.003ms returns 0
TA260 002:650.710 JLINK_WriteReg(R11, 0x00000000)
TA260 002:650.714 - 0.003ms returns 0
TA260 002:650.718 JLINK_WriteReg(R12, 0x00000000)
TA260 002:650.721 - 0.003ms returns 0
TA260 002:650.725 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:650.729 - 0.003ms returns 0
TA260 002:650.733 JLINK_WriteReg(R14, 0x20000001)
TA260 002:650.736 - 0.003ms returns 0
TA260 002:650.740 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:650.744 - 0.003ms returns 0
TA260 002:650.748 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:650.751 - 0.003ms returns 0
TA260 002:650.755 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:650.759 - 0.003ms returns 0
TA260 002:650.763 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:650.766 - 0.003ms returns 0
TA260 002:650.772 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:650.776 - 0.003ms returns 0
TA260 002:650.780 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:650.784 - 0.004ms returns 0x00000021
TA260 002:650.788 JLINK_Go()
TA260 002:650.796   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:653.564 - 2.775ms 
TA260 002:653.570 JLINK_IsHalted()
TA260 002:654.035 - 0.464ms returns FALSE
TA260 002:654.040 JLINK_HasError()
TA260 002:655.985 JLINK_IsHalted()
TA260 002:656.496 - 0.510ms returns FALSE
TA260 002:656.504 JLINK_HasError()
TA260 002:657.986 JLINK_IsHalted()
TA260 002:660.321   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:660.827 - 2.840ms returns TRUE
TA260 002:660.835 JLINK_ReadReg(R15 (PC))
TA260 002:660.840 - 0.005ms returns 0x20000000
TA260 002:660.844 JLINK_ClrBPEx(BPHandle = 0x00000021)
TA260 002:660.849 - 0.004ms returns 0x00
TA260 002:660.853 JLINK_ReadReg(R0)
TA260 002:660.856 - 0.003ms returns 0x00000000
TA260 002:661.225 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:661.234   Data:  A4 01 A4 05 A4 03 A4 07 64 00 64 04 64 02 64 06 ...
TA260 002:661.245   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:663.841 - 2.616ms returns 0x27C
TA260 002:663.854 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:663.872   Data:  B2 02 B2 06 B2 01 B2 05 B2 03 B2 07 72 00 72 04 ...
TA260 002:663.882   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:665.750 - 1.896ms returns 0x184
TA260 002:665.767 JLINK_HasError()
TA260 002:665.803 JLINK_WriteReg(R0, 0x08005800)
TA260 002:665.814 - 0.011ms returns 0
TA260 002:665.819 JLINK_WriteReg(R1, 0x00000400)
TA260 002:665.823 - 0.003ms returns 0
TA260 002:665.827 JLINK_WriteReg(R2, 0x20000184)
TA260 002:665.830 - 0.003ms returns 0
TA260 002:665.834 JLINK_WriteReg(R3, 0x00000000)
TA260 002:665.838 - 0.003ms returns 0
TA260 002:665.842 JLINK_WriteReg(R4, 0x00000000)
TA260 002:665.846 - 0.003ms returns 0
TA260 002:665.850 JLINK_WriteReg(R5, 0x00000000)
TA260 002:665.853 - 0.003ms returns 0
TA260 002:665.857 JLINK_WriteReg(R6, 0x00000000)
TA260 002:665.861 - 0.003ms returns 0
TA260 002:665.865 JLINK_WriteReg(R7, 0x00000000)
TA260 002:665.868 - 0.003ms returns 0
TA260 002:665.872 JLINK_WriteReg(R8, 0x00000000)
TA260 002:665.876 - 0.003ms returns 0
TA260 002:665.880 JLINK_WriteReg(R9, 0x20000180)
TA260 002:665.883 - 0.003ms returns 0
TA260 002:665.888 JLINK_WriteReg(R10, 0x00000000)
TA260 002:665.891 - 0.003ms returns 0
TA260 002:665.895 JLINK_WriteReg(R11, 0x00000000)
TA260 002:665.899 - 0.003ms returns 0
TA260 002:665.903 JLINK_WriteReg(R12, 0x00000000)
TA260 002:665.906 - 0.003ms returns 0
TA260 002:665.910 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:665.914 - 0.004ms returns 0
TA260 002:665.918 JLINK_WriteReg(R14, 0x20000001)
TA260 002:665.921 - 0.003ms returns 0
TA260 002:665.926 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:665.931 - 0.004ms returns 0
TA260 002:665.935 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:665.938 - 0.003ms returns 0
TA260 002:665.942 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:665.946 - 0.003ms returns 0
TA260 002:665.950 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:665.953 - 0.003ms returns 0
TA260 002:665.957 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:665.961 - 0.003ms returns 0
TA260 002:665.965 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:665.970 - 0.004ms returns 0x00000022
TA260 002:665.975 JLINK_Go()
TA260 002:665.984   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:668.780 - 2.804ms 
TA260 002:668.792 JLINK_IsHalted()
TA260 002:669.267 - 0.475ms returns FALSE
TA260 002:669.273 JLINK_HasError()
TA260 002:670.502 JLINK_IsHalted()
TA260 002:671.067 - 0.564ms returns FALSE
TA260 002:671.078 JLINK_HasError()
TA260 002:673.495 JLINK_IsHalted()
TA260 002:675.883   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:676.387 - 2.892ms returns TRUE
TA260 002:676.400 JLINK_ReadReg(R15 (PC))
TA260 002:676.407 - 0.006ms returns 0x20000000
TA260 002:676.411 JLINK_ClrBPEx(BPHandle = 0x00000022)
TA260 002:676.415 - 0.004ms returns 0x00
TA260 002:676.420 JLINK_ReadReg(R0)
TA260 002:676.424 - 0.004ms returns 0x00000000
TA260 002:676.766 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:676.774   Data:  A6 01 A6 05 A6 03 A6 07 66 00 66 04 66 02 66 06 ...
TA260 002:676.784   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:679.464 - 2.697ms returns 0x27C
TA260 002:679.475 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:679.480   Data:  C0 7F 18 3F C6 02 1B 3F D1 7F 1D 3F CB F6 1F 3F ...
TA260 002:679.489   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:681.372 - 1.896ms returns 0x184
TA260 002:681.384 JLINK_HasError()
TA260 002:681.390 JLINK_WriteReg(R0, 0x08005C00)
TA260 002:681.395 - 0.005ms returns 0
TA260 002:681.399 JLINK_WriteReg(R1, 0x00000400)
TA260 002:681.403 - 0.003ms returns 0
TA260 002:681.407 JLINK_WriteReg(R2, 0x20000184)
TA260 002:681.410 - 0.003ms returns 0
TA260 002:681.414 JLINK_WriteReg(R3, 0x00000000)
TA260 002:681.418 - 0.003ms returns 0
TA260 002:681.422 JLINK_WriteReg(R4, 0x00000000)
TA260 002:681.429 - 0.007ms returns 0
TA260 002:681.433 JLINK_WriteReg(R5, 0x00000000)
TA260 002:681.437 - 0.003ms returns 0
TA260 002:681.441 JLINK_WriteReg(R6, 0x00000000)
TA260 002:681.444 - 0.003ms returns 0
TA260 002:681.448 JLINK_WriteReg(R7, 0x00000000)
TA260 002:681.451 - 0.003ms returns 0
TA260 002:681.456 JLINK_WriteReg(R8, 0x00000000)
TA260 002:681.459 - 0.003ms returns 0
TA260 002:681.463 JLINK_WriteReg(R9, 0x20000180)
TA260 002:681.466 - 0.003ms returns 0
TA260 002:681.470 JLINK_WriteReg(R10, 0x00000000)
TA260 002:681.474 - 0.003ms returns 0
TA260 002:681.478 JLINK_WriteReg(R11, 0x00000000)
TA260 002:681.481 - 0.003ms returns 0
TA260 002:681.485 JLINK_WriteReg(R12, 0x00000000)
TA260 002:681.489 - 0.003ms returns 0
TA260 002:681.493 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:681.497 - 0.003ms returns 0
TA260 002:681.501 JLINK_WriteReg(R14, 0x20000001)
TA260 002:681.504 - 0.003ms returns 0
TA260 002:681.508 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:681.512 - 0.003ms returns 0
TA260 002:681.516 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:681.519 - 0.003ms returns 0
TA260 002:681.523 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:681.526 - 0.003ms returns 0
TA260 002:681.531 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:681.534 - 0.003ms returns 0
TA260 002:681.538 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:681.542 - 0.003ms returns 0
TA260 002:681.546 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:681.550 - 0.004ms returns 0x00000023
TA260 002:681.555 JLINK_Go()
TA260 002:681.562   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:684.361 - 2.806ms 
TA260 002:684.368 JLINK_IsHalted()
TA260 002:684.846 - 0.478ms returns FALSE
TA260 002:684.852 JLINK_HasError()
TA260 002:686.514 JLINK_IsHalted()
TA260 002:687.012 - 0.497ms returns FALSE
TA260 002:687.025 JLINK_HasError()
TA260 002:688.510 JLINK_IsHalted()
TA260 002:690.814   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:691.297 - 2.787ms returns TRUE
TA260 002:691.308 JLINK_ReadReg(R15 (PC))
TA260 002:691.313 - 0.005ms returns 0x20000000
TA260 002:691.351 JLINK_ClrBPEx(BPHandle = 0x00000023)
TA260 002:691.362 - 0.010ms returns 0x00
TA260 002:691.367 JLINK_ReadReg(R0)
TA260 002:691.371 - 0.004ms returns 0x00000000
TA260 002:691.738 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:691.744   Data:  C5 8B 77 3F 07 BA 76 3F C6 DE 75 3F 0B FA 74 3F ...
TA260 002:691.754   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:694.383 - 2.645ms returns 0x27C
TA260 002:694.390 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:694.394   Data:  C0 7F 18 BF C6 02 1B BF D1 7F 1D BF CB F6 1F BF ...
TA260 002:694.402   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:696.294 - 1.904ms returns 0x184
TA260 002:696.306 JLINK_HasError()
TA260 002:696.312 JLINK_WriteReg(R0, 0x08006000)
TA260 002:696.317 - 0.004ms returns 0
TA260 002:696.321 JLINK_WriteReg(R1, 0x00000400)
TA260 002:696.325 - 0.003ms returns 0
TA260 002:696.329 JLINK_WriteReg(R2, 0x20000184)
TA260 002:696.332 - 0.003ms returns 0
TA260 002:696.336 JLINK_WriteReg(R3, 0x00000000)
TA260 002:696.340 - 0.003ms returns 0
TA260 002:696.344 JLINK_WriteReg(R4, 0x00000000)
TA260 002:696.347 - 0.003ms returns 0
TA260 002:696.351 JLINK_WriteReg(R5, 0x00000000)
TA260 002:696.354 - 0.003ms returns 0
TA260 002:696.358 JLINK_WriteReg(R6, 0x00000000)
TA260 002:696.362 - 0.003ms returns 0
TA260 002:696.366 JLINK_WriteReg(R7, 0x00000000)
TA260 002:696.370 - 0.003ms returns 0
TA260 002:696.374 JLINK_WriteReg(R8, 0x00000000)
TA260 002:696.377 - 0.003ms returns 0
TA260 002:696.381 JLINK_WriteReg(R9, 0x20000180)
TA260 002:696.385 - 0.003ms returns 0
TA260 002:696.389 JLINK_WriteReg(R10, 0x00000000)
TA260 002:696.392 - 0.003ms returns 0
TA260 002:696.396 JLINK_WriteReg(R11, 0x00000000)
TA260 002:696.400 - 0.003ms returns 0
TA260 002:696.404 JLINK_WriteReg(R12, 0x00000000)
TA260 002:696.407 - 0.003ms returns 0
TA260 002:696.411 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:696.415 - 0.003ms returns 0
TA260 002:696.419 JLINK_WriteReg(R14, 0x20000001)
TA260 002:696.427 - 0.007ms returns 0
TA260 002:696.432 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:696.435 - 0.003ms returns 0
TA260 002:696.439 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:696.443 - 0.003ms returns 0
TA260 002:696.447 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:696.450 - 0.003ms returns 0
TA260 002:696.454 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:696.458 - 0.003ms returns 0
TA260 002:696.462 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:696.465 - 0.003ms returns 0
TA260 002:696.470 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:696.474 - 0.004ms returns 0x00000024
TA260 002:696.478 JLINK_Go()
TA260 002:696.486   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:699.324 - 2.845ms 
TA260 002:699.336 JLINK_IsHalted()
TA260 002:699.848 - 0.511ms returns FALSE
TA260 002:699.854 JLINK_HasError()
TA260 002:701.260 JLINK_IsHalted()
TA260 002:701.779 - 0.519ms returns FALSE
TA260 002:701.786 JLINK_HasError()
TA260 002:703.261 JLINK_IsHalted()
TA260 002:705.644   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:706.080 - 2.818ms returns TRUE
TA260 002:706.093 JLINK_ReadReg(R15 (PC))
TA260 002:706.098 - 0.005ms returns 0x20000000
TA260 002:706.102 JLINK_ClrBPEx(BPHandle = 0x00000024)
TA260 002:706.106 - 0.003ms returns 0x00
TA260 002:706.110 JLINK_ReadReg(R0)
TA260 002:706.114 - 0.003ms returns 0x00000000
TA260 002:706.443 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:706.450   Data:  C5 8B 77 BF 07 BA 76 BF C6 DE 75 BF 0B FA 74 BF ...
TA260 002:706.459   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:709.010 - 2.567ms returns 0x27C
TA260 002:709.020 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:709.024   Data:  59 CB 16 3C 39 FC 7F 3F 02 ED 2F 3C 11 FB 7F 3F ...
TA260 002:709.033   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:710.930 - 1.909ms returns 0x184
TA260 002:710.940 JLINK_HasError()
TA260 002:710.946 JLINK_WriteReg(R0, 0x08006400)
TA260 002:710.950 - 0.005ms returns 0
TA260 002:710.954 JLINK_WriteReg(R1, 0x00000400)
TA260 002:710.958 - 0.003ms returns 0
TA260 002:710.962 JLINK_WriteReg(R2, 0x20000184)
TA260 002:710.966 - 0.003ms returns 0
TA260 002:710.970 JLINK_WriteReg(R3, 0x00000000)
TA260 002:710.973 - 0.003ms returns 0
TA260 002:710.977 JLINK_WriteReg(R4, 0x00000000)
TA260 002:710.981 - 0.003ms returns 0
TA260 002:710.985 JLINK_WriteReg(R5, 0x00000000)
TA260 002:710.988 - 0.003ms returns 0
TA260 002:710.992 JLINK_WriteReg(R6, 0x00000000)
TA260 002:710.995 - 0.003ms returns 0
TA260 002:711.000 JLINK_WriteReg(R7, 0x00000000)
TA260 002:711.003 - 0.003ms returns 0
TA260 002:711.007 JLINK_WriteReg(R8, 0x00000000)
TA260 002:711.010 - 0.003ms returns 0
TA260 002:711.014 JLINK_WriteReg(R9, 0x20000180)
TA260 002:711.018 - 0.003ms returns 0
TA260 002:711.022 JLINK_WriteReg(R10, 0x00000000)
TA260 002:711.025 - 0.003ms returns 0
TA260 002:711.030 JLINK_WriteReg(R11, 0x00000000)
TA260 002:711.033 - 0.003ms returns 0
TA260 002:711.037 JLINK_WriteReg(R12, 0x00000000)
TA260 002:711.040 - 0.003ms returns 0
TA260 002:711.044 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:711.048 - 0.003ms returns 0
TA260 002:711.052 JLINK_WriteReg(R14, 0x20000001)
TA260 002:711.056 - 0.003ms returns 0
TA260 002:711.060 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:711.063 - 0.003ms returns 0
TA260 002:711.067 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:711.071 - 0.003ms returns 0
TA260 002:711.075 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:711.078 - 0.003ms returns 0
TA260 002:711.082 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:711.085 - 0.003ms returns 0
TA260 002:711.090 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:711.093 - 0.003ms returns 0
TA260 002:711.098 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:711.102 - 0.004ms returns 0x00000025
TA260 002:711.106 JLINK_Go()
TA260 002:711.113   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:713.927 - 2.821ms 
TA260 002:713.934 JLINK_IsHalted()
TA260 002:714.460 - 0.526ms returns FALSE
TA260 002:714.466 JLINK_HasError()
TA260 002:715.774 JLINK_IsHalted()
TA260 002:716.245 - 0.471ms returns FALSE
TA260 002:716.257 JLINK_HasError()
TA260 002:717.778 JLINK_IsHalted()
TA260 002:718.281 - 0.502ms returns FALSE
TA260 002:718.287 JLINK_HasError()
TA260 002:719.774 JLINK_IsHalted()
TA260 002:722.086   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:722.610 - 2.836ms returns TRUE
TA260 002:722.616 JLINK_ReadReg(R15 (PC))
TA260 002:722.621 - 0.004ms returns 0x20000000
TA260 002:722.625 JLINK_ClrBPEx(BPHandle = 0x00000025)
TA260 002:722.629 - 0.003ms returns 0x00
TA260 002:722.633 JLINK_ReadReg(R0)
TA260 002:722.636 - 0.003ms returns 0x00000000
TA260 002:723.116 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:723.127   Data:  E4 16 7F 3F 2B 95 AC 3D 58 0E 7F 3F 80 B6 AF 3D ...
TA260 002:723.137   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:725.744 - 2.628ms returns 0x27C
TA260 002:725.759 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:725.764   Data:  F1 01 51 3E C1 87 7A 3F 89 8B 52 3E 02 73 7A 3F ...
TA260 002:725.777   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:728.252 - 2.492ms returns 0x184
TA260 002:728.266 JLINK_HasError()
TA260 002:728.272 JLINK_WriteReg(R0, 0x08006800)
TA260 002:728.277 - 0.005ms returns 0
TA260 002:728.281 JLINK_WriteReg(R1, 0x00000400)
TA260 002:728.284 - 0.003ms returns 0
TA260 002:728.288 JLINK_WriteReg(R2, 0x20000184)
TA260 002:728.292 - 0.003ms returns 0
TA260 002:728.296 JLINK_WriteReg(R3, 0x00000000)
TA260 002:728.299 - 0.003ms returns 0
TA260 002:728.303 JLINK_WriteReg(R4, 0x00000000)
TA260 002:728.307 - 0.003ms returns 0
TA260 002:728.311 JLINK_WriteReg(R5, 0x00000000)
TA260 002:728.314 - 0.003ms returns 0
TA260 002:728.318 JLINK_WriteReg(R6, 0x00000000)
TA260 002:728.321 - 0.003ms returns 0
TA260 002:728.326 JLINK_WriteReg(R7, 0x00000000)
TA260 002:728.329 - 0.003ms returns 0
TA260 002:728.333 JLINK_WriteReg(R8, 0x00000000)
TA260 002:728.337 - 0.003ms returns 0
TA260 002:728.341 JLINK_WriteReg(R9, 0x20000180)
TA260 002:728.344 - 0.003ms returns 0
TA260 002:728.348 JLINK_WriteReg(R10, 0x00000000)
TA260 002:728.352 - 0.003ms returns 0
TA260 002:728.356 JLINK_WriteReg(R11, 0x00000000)
TA260 002:728.359 - 0.003ms returns 0
TA260 002:728.363 JLINK_WriteReg(R12, 0x00000000)
TA260 002:728.367 - 0.003ms returns 0
TA260 002:728.371 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:728.375 - 0.003ms returns 0
TA260 002:728.379 JLINK_WriteReg(R14, 0x20000001)
TA260 002:728.382 - 0.003ms returns 0
TA260 002:728.386 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:728.390 - 0.003ms returns 0
TA260 002:728.394 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:728.397 - 0.003ms returns 0
TA260 002:728.401 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:728.404 - 0.003ms returns 0
TA260 002:728.409 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:728.415 - 0.006ms returns 0
TA260 002:728.420 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:728.423 - 0.003ms returns 0
TA260 002:728.428 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:728.432 - 0.004ms returns 0x00000026
TA260 002:728.436 JLINK_Go()
TA260 002:728.444   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:731.188 - 2.751ms 
TA260 002:731.195 JLINK_IsHalted()
TA260 002:731.688 - 0.493ms returns FALSE
TA260 002:731.694 JLINK_HasError()
TA260 002:733.284 JLINK_IsHalted()
TA260 002:733.756 - 0.471ms returns FALSE
TA260 002:733.763 JLINK_HasError()
TA260 002:735.787 JLINK_IsHalted()
TA260 002:738.149   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:738.609 - 2.822ms returns TRUE
TA260 002:738.616 JLINK_ReadReg(R15 (PC))
TA260 002:738.620 - 0.004ms returns 0x20000000
TA260 002:738.625 JLINK_ClrBPEx(BPHandle = 0x00000026)
TA260 002:738.628 - 0.003ms returns 0x00
TA260 002:738.633 JLINK_ReadReg(R0)
TA260 002:738.636 - 0.003ms returns 0x00000000
TA260 002:738.965 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:738.972   Data:  B3 FA 75 3F FC D8 8D 3E C6 DE 75 3F 22 9A 8E 3E ...
TA260 002:738.982   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:741.609 - 2.643ms returns 0x27C
TA260 002:741.620 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:741.626   Data:  7C 47 C8 3E 95 72 6B 3F 77 00 C9 3E 0C 4B 6B 3F ...
TA260 002:741.636   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:743.492 - 1.872ms returns 0x184
TA260 002:743.500 JLINK_HasError()
TA260 002:743.532 JLINK_WriteReg(R0, 0x08006C00)
TA260 002:743.537 - 0.005ms returns 0
TA260 002:743.541 JLINK_WriteReg(R1, 0x00000400)
TA260 002:743.545 - 0.003ms returns 0
TA260 002:743.549 JLINK_WriteReg(R2, 0x20000184)
TA260 002:743.552 - 0.003ms returns 0
TA260 002:743.556 JLINK_WriteReg(R3, 0x00000000)
TA260 002:743.560 - 0.003ms returns 0
TA260 002:743.564 JLINK_WriteReg(R4, 0x00000000)
TA260 002:743.567 - 0.003ms returns 0
TA260 002:743.571 JLINK_WriteReg(R5, 0x00000000)
TA260 002:743.575 - 0.003ms returns 0
TA260 002:743.579 JLINK_WriteReg(R6, 0x00000000)
TA260 002:743.582 - 0.003ms returns 0
TA260 002:743.586 JLINK_WriteReg(R7, 0x00000000)
TA260 002:743.589 - 0.003ms returns 0
TA260 002:743.594 JLINK_WriteReg(R8, 0x00000000)
TA260 002:743.597 - 0.003ms returns 0
TA260 002:743.601 JLINK_WriteReg(R9, 0x20000180)
TA260 002:743.605 - 0.003ms returns 0
TA260 002:743.609 JLINK_WriteReg(R10, 0x00000000)
TA260 002:743.612 - 0.003ms returns 0
TA260 002:743.616 JLINK_WriteReg(R11, 0x00000000)
TA260 002:743.619 - 0.003ms returns 0
TA260 002:743.624 JLINK_WriteReg(R12, 0x00000000)
TA260 002:743.627 - 0.003ms returns 0
TA260 002:743.631 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:743.635 - 0.003ms returns 0
TA260 002:743.639 JLINK_WriteReg(R14, 0x20000001)
TA260 002:743.642 - 0.003ms returns 0
TA260 002:743.646 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:743.652 - 0.006ms returns 0
TA260 002:743.657 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:743.660 - 0.003ms returns 0
TA260 002:743.664 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:743.668 - 0.003ms returns 0
TA260 002:743.672 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:743.675 - 0.003ms returns 0
TA260 002:743.679 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:743.682 - 0.003ms returns 0
TA260 002:743.687 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:743.691 - 0.004ms returns 0x00000027
TA260 002:743.695 JLINK_Go()
TA260 002:743.702   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:747.034 - 3.338ms 
TA260 002:747.047 JLINK_IsHalted()
TA260 002:747.564 - 0.517ms returns FALSE
TA260 002:747.570 JLINK_HasError()
TA260 002:749.294 JLINK_IsHalted()
TA260 002:749.814 - 0.520ms returns FALSE
TA260 002:749.821 JLINK_HasError()
TA260 002:751.292 JLINK_IsHalted()
TA260 002:753.600   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:754.075 - 2.782ms returns TRUE
TA260 002:754.081 JLINK_ReadReg(R15 (PC))
TA260 002:754.086 - 0.004ms returns 0x20000000
TA260 002:754.090 JLINK_ClrBPEx(BPHandle = 0x00000027)
TA260 002:754.094 - 0.003ms returns 0x00
TA260 002:754.098 JLINK_ReadReg(R0)
TA260 002:754.102 - 0.003ms returns 0x00000000
TA260 002:754.416 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:754.423   Data:  95 6A 63 3F 30 19 EB 3E 5A 3C 63 3F BB CB EB 3E ...
TA260 002:754.432   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:757.079 - 2.662ms returns 0x27C
TA260 002:757.096 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:757.101   Data:  D5 2D 10 3F 18 51 53 3F DC 80 10 3F 49 18 53 3F ...
TA260 002:757.112   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:759.017 - 1.920ms returns 0x184
TA260 002:759.024 JLINK_HasError()
TA260 002:759.029 JLINK_WriteReg(R0, 0x08007000)
TA260 002:759.033 - 0.004ms returns 0
TA260 002:759.038 JLINK_WriteReg(R1, 0x00000400)
TA260 002:759.041 - 0.003ms returns 0
TA260 002:759.045 JLINK_WriteReg(R2, 0x20000184)
TA260 002:759.049 - 0.003ms returns 0
TA260 002:759.053 JLINK_WriteReg(R3, 0x00000000)
TA260 002:759.056 - 0.003ms returns 0
TA260 002:759.060 JLINK_WriteReg(R4, 0x00000000)
TA260 002:759.064 - 0.003ms returns 0
TA260 002:759.068 JLINK_WriteReg(R5, 0x00000000)
TA260 002:759.071 - 0.003ms returns 0
TA260 002:759.075 JLINK_WriteReg(R6, 0x00000000)
TA260 002:759.078 - 0.003ms returns 0
TA260 002:759.082 JLINK_WriteReg(R7, 0x00000000)
TA260 002:759.091 - 0.009ms returns 0
TA260 002:759.095 JLINK_WriteReg(R8, 0x00000000)
TA260 002:759.099 - 0.003ms returns 0
TA260 002:759.103 JLINK_WriteReg(R9, 0x20000180)
TA260 002:759.106 - 0.003ms returns 0
TA260 002:759.110 JLINK_WriteReg(R10, 0x00000000)
TA260 002:759.114 - 0.003ms returns 0
TA260 002:759.118 JLINK_WriteReg(R11, 0x00000000)
TA260 002:759.121 - 0.003ms returns 0
TA260 002:759.125 JLINK_WriteReg(R12, 0x00000000)
TA260 002:759.128 - 0.003ms returns 0
TA260 002:759.133 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:759.136 - 0.004ms returns 0
TA260 002:759.141 JLINK_WriteReg(R14, 0x20000001)
TA260 002:759.144 - 0.003ms returns 0
TA260 002:759.148 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:759.152 - 0.003ms returns 0
TA260 002:759.156 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:759.159 - 0.003ms returns 0
TA260 002:759.163 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:759.167 - 0.003ms returns 0
TA260 002:759.171 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:759.174 - 0.003ms returns 0
TA260 002:759.178 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:759.182 - 0.003ms returns 0
TA260 002:759.186 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:759.190 - 0.004ms returns 0x00000028
TA260 002:759.194 JLINK_Go()
TA260 002:759.201   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:761.926 - 2.731ms 
TA260 002:761.937 JLINK_IsHalted()
TA260 002:762.469 - 0.531ms returns FALSE
TA260 002:762.475 JLINK_HasError()
TA260 002:764.048 JLINK_IsHalted()
TA260 002:764.706 - 0.657ms returns FALSE
TA260 002:764.716 JLINK_HasError()
TA260 002:766.556 JLINK_IsHalted()
TA260 002:768.859   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:769.361 - 2.803ms returns TRUE
TA260 002:769.370 JLINK_ReadReg(R15 (PC))
TA260 002:769.375 - 0.004ms returns 0x20000000
TA260 002:769.405 JLINK_ClrBPEx(BPHandle = 0x00000028)
TA260 002:769.410 - 0.004ms returns 0x00
TA260 002:769.415 JLINK_ReadReg(R0)
TA260 002:769.418 - 0.003ms returns 0x00000000
TA260 002:769.755 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:769.762   Data:  27 1D 48 3F 41 A8 1F 3F 65 DE 47 3F CB F6 1F 3F ...
TA260 002:769.771   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:772.370 - 2.615ms returns 0x27C
TA260 002:772.376 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:772.380   Data:  7F AD 36 3F AF 10 33 3F DF F3 36 3F C9 C8 32 3F ...
TA260 002:772.387   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:774.244 - 1.868ms returns 0x184
TA260 002:774.250 JLINK_HasError()
TA260 002:774.254 JLINK_WriteReg(R0, 0x08007400)
TA260 002:774.258 - 0.004ms returns 0
TA260 002:774.262 JLINK_WriteReg(R1, 0x00000400)
TA260 002:774.266 - 0.003ms returns 0
TA260 002:774.270 JLINK_WriteReg(R2, 0x20000184)
TA260 002:774.273 - 0.003ms returns 0
TA260 002:774.278 JLINK_WriteReg(R3, 0x00000000)
TA260 002:774.281 - 0.003ms returns 0
TA260 002:774.285 JLINK_WriteReg(R4, 0x00000000)
TA260 002:774.288 - 0.003ms returns 0
TA260 002:774.292 JLINK_WriteReg(R5, 0x00000000)
TA260 002:774.296 - 0.003ms returns 0
TA260 002:774.300 JLINK_WriteReg(R6, 0x00000000)
TA260 002:774.303 - 0.003ms returns 0
TA260 002:774.307 JLINK_WriteReg(R7, 0x00000000)
TA260 002:774.310 - 0.003ms returns 0
TA260 002:774.315 JLINK_WriteReg(R8, 0x00000000)
TA260 002:774.318 - 0.003ms returns 0
TA260 002:774.322 JLINK_WriteReg(R9, 0x20000180)
TA260 002:774.325 - 0.003ms returns 0
TA260 002:774.329 JLINK_WriteReg(R10, 0x00000000)
TA260 002:774.333 - 0.003ms returns 0
TA260 002:774.337 JLINK_WriteReg(R11, 0x00000000)
TA260 002:774.340 - 0.003ms returns 0
TA260 002:774.344 JLINK_WriteReg(R12, 0x00000000)
TA260 002:774.348 - 0.003ms returns 0
TA260 002:774.352 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:774.355 - 0.003ms returns 0
TA260 002:774.359 JLINK_WriteReg(R14, 0x20000001)
TA260 002:774.363 - 0.003ms returns 0
TA260 002:774.367 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:774.370 - 0.003ms returns 0
TA260 002:774.374 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:774.378 - 0.003ms returns 0
TA260 002:774.382 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:774.389 - 0.007ms returns 0
TA260 002:774.393 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:774.396 - 0.003ms returns 0
TA260 002:774.400 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:774.404 - 0.003ms returns 0
TA260 002:774.408 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:774.412 - 0.004ms returns 0x00000029
TA260 002:774.416 JLINK_Go()
TA260 002:774.423   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:777.227 - 2.810ms 
TA260 002:777.241 JLINK_IsHalted()
TA260 002:777.755 - 0.513ms returns FALSE
TA260 002:777.762 JLINK_HasError()
TA260 002:779.060 JLINK_IsHalted()
TA260 002:779.552 - 0.492ms returns FALSE
TA260 002:779.558 JLINK_HasError()
TA260 002:781.066 JLINK_IsHalted()
TA260 002:783.498   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:784.007 - 2.941ms returns TRUE
TA260 002:784.014 JLINK_ReadReg(R15 (PC))
TA260 002:784.020 - 0.005ms returns 0x20000000
TA260 002:784.024 JLINK_ClrBPEx(BPHandle = 0x00000029)
TA260 002:784.029 - 0.004ms returns 0x00
TA260 002:784.033 JLINK_ReadReg(R0)
TA260 002:784.038 - 0.004ms returns 0x00000000
TA260 002:784.392 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:784.399   Data:  04 1F 25 3F 38 A1 43 3F 25 D2 24 3F 00 E2 43 3F ...
TA260 002:784.410   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:787.020 - 2.627ms returns 0x27C
TA260 002:787.035 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:787.039   Data:  FE 27 56 3F A5 EE 0B 3F 02 5F 56 3F 6B 9A 0B 3F ...
TA260 002:787.050   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:789.023 - 1.987ms returns 0x184
TA260 002:789.038 JLINK_HasError()
TA260 002:789.044 JLINK_WriteReg(R0, 0x08007800)
TA260 002:789.049 - 0.006ms returns 0
TA260 002:789.053 JLINK_WriteReg(R1, 0x00000400)
TA260 002:789.057 - 0.003ms returns 0
TA260 002:789.061 JLINK_WriteReg(R2, 0x20000184)
TA260 002:789.064 - 0.003ms returns 0
TA260 002:789.068 JLINK_WriteReg(R3, 0x00000000)
TA260 002:789.072 - 0.003ms returns 0
TA260 002:789.076 JLINK_WriteReg(R4, 0x00000000)
TA260 002:789.079 - 0.003ms returns 0
TA260 002:789.083 JLINK_WriteReg(R5, 0x00000000)
TA260 002:789.086 - 0.003ms returns 0
TA260 002:789.090 JLINK_WriteReg(R6, 0x00000000)
TA260 002:789.094 - 0.003ms returns 0
TA260 002:789.098 JLINK_WriteReg(R7, 0x00000000)
TA260 002:789.101 - 0.003ms returns 0
TA260 002:789.106 JLINK_WriteReg(R8, 0x00000000)
TA260 002:789.109 - 0.004ms returns 0
TA260 002:789.113 JLINK_WriteReg(R9, 0x20000180)
TA260 002:789.117 - 0.003ms returns 0
TA260 002:789.124 JLINK_WriteReg(R10, 0x00000000)
TA260 002:789.127 - 0.003ms returns 0
TA260 002:789.131 JLINK_WriteReg(R11, 0x00000000)
TA260 002:789.135 - 0.003ms returns 0
TA260 002:789.139 JLINK_WriteReg(R12, 0x00000000)
TA260 002:789.142 - 0.003ms returns 0
TA260 002:789.146 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:789.150 - 0.003ms returns 0
TA260 002:789.154 JLINK_WriteReg(R14, 0x20000001)
TA260 002:789.158 - 0.003ms returns 0
TA260 002:789.162 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:789.165 - 0.003ms returns 0
TA260 002:789.169 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:789.173 - 0.003ms returns 0
TA260 002:789.177 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:789.180 - 0.003ms returns 0
TA260 002:789.184 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:789.188 - 0.003ms returns 0
TA260 002:789.192 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:789.195 - 0.003ms returns 0
TA260 002:789.200 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:789.204 - 0.004ms returns 0x0000002A
TA260 002:789.208 JLINK_Go()
TA260 002:789.217   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:792.050 - 2.841ms 
TA260 002:792.057 JLINK_IsHalted()
TA260 002:792.506 - 0.449ms returns FALSE
TA260 002:792.512 JLINK_HasError()
TA260 002:795.071 JLINK_IsHalted()
TA260 002:795.511 - 0.439ms returns FALSE
TA260 002:795.517 JLINK_HasError()
TA260 002:797.076 JLINK_IsHalted()
TA260 002:799.481   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:799.982 - 2.906ms returns TRUE
TA260 002:799.989 JLINK_ReadReg(R15 (PC))
TA260 002:799.996 - 0.007ms returns 0x20000000
TA260 002:800.001 JLINK_ClrBPEx(BPHandle = 0x0000002A)
TA260 002:800.005 - 0.004ms returns 0x00
TA260 002:800.010 JLINK_ReadReg(R0)
TA260 002:800.013 - 0.003ms returns 0x00000000
TA260 002:800.361 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:800.368   Data:  DC 90 F7 3E 96 15 60 3F CB E0 F6 3E 21 46 60 3F ...
TA260 002:800.378   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:802.965 - 2.603ms returns 0x27C
TA260 002:802.973 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:802.976   Data:  A1 67 6D 3F EB D7 BE 3E 2D 8D 6D 3F 4A 1D BE 3E ...
TA260 002:802.985   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:804.836 - 1.863ms returns 0x184
TA260 002:804.843 JLINK_HasError()
TA260 002:804.848 JLINK_WriteReg(R0, 0x08007C00)
TA260 002:804.852 - 0.004ms returns 0
TA260 002:804.856 JLINK_WriteReg(R1, 0x00000400)
TA260 002:804.860 - 0.003ms returns 0
TA260 002:804.864 JLINK_WriteReg(R2, 0x20000184)
TA260 002:804.867 - 0.003ms returns 0
TA260 002:804.871 JLINK_WriteReg(R3, 0x00000000)
TA260 002:804.875 - 0.003ms returns 0
TA260 002:804.879 JLINK_WriteReg(R4, 0x00000000)
TA260 002:804.882 - 0.003ms returns 0
TA260 002:804.886 JLINK_WriteReg(R5, 0x00000000)
TA260 002:804.890 - 0.003ms returns 0
TA260 002:804.894 JLINK_WriteReg(R6, 0x00000000)
TA260 002:804.897 - 0.003ms returns 0
TA260 002:804.901 JLINK_WriteReg(R7, 0x00000000)
TA260 002:804.904 - 0.003ms returns 0
TA260 002:804.909 JLINK_WriteReg(R8, 0x00000000)
TA260 002:804.912 - 0.003ms returns 0
TA260 002:804.916 JLINK_WriteReg(R9, 0x20000180)
TA260 002:804.920 - 0.003ms returns 0
TA260 002:804.924 JLINK_WriteReg(R10, 0x00000000)
TA260 002:804.927 - 0.003ms returns 0
TA260 002:804.931 JLINK_WriteReg(R11, 0x00000000)
TA260 002:804.934 - 0.003ms returns 0
TA260 002:804.938 JLINK_WriteReg(R12, 0x00000000)
TA260 002:804.942 - 0.003ms returns 0
TA260 002:804.946 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:804.950 - 0.004ms returns 0
TA260 002:804.954 JLINK_WriteReg(R14, 0x20000001)
TA260 002:804.957 - 0.003ms returns 0
TA260 002:804.961 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:804.965 - 0.003ms returns 0
TA260 002:804.969 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:804.972 - 0.003ms returns 0
TA260 002:804.976 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:804.980 - 0.003ms returns 0
TA260 002:804.984 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:804.987 - 0.003ms returns 0
TA260 002:804.991 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:804.994 - 0.003ms returns 0
TA260 002:804.999 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:805.003 - 0.004ms returns 0x0000002B
TA260 002:805.008 JLINK_Go()
TA260 002:805.015   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:807.745 - 2.737ms 
TA260 002:807.759 JLINK_IsHalted()
TA260 002:808.256 - 0.496ms returns FALSE
TA260 002:808.262 JLINK_HasError()
TA260 002:809.974 JLINK_IsHalted()
TA260 002:810.471 - 0.496ms returns FALSE
TA260 002:810.478 JLINK_HasError()
TA260 002:811.972 JLINK_IsHalted()
TA260 002:814.357   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:814.837 - 2.864ms returns TRUE
TA260 002:814.843 JLINK_ReadReg(R15 (PC))
TA260 002:814.848 - 0.005ms returns 0x20000000
TA260 002:814.853 JLINK_ClrBPEx(BPHandle = 0x0000002B)
TA260 002:814.856 - 0.003ms returns 0x00
TA260 002:814.861 JLINK_ReadReg(R0)
TA260 002:814.864 - 0.003ms returns 0x00000000
TA260 002:815.187 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:815.194   Data:  27 60 9B 3E 6E ED 73 3F 86 A0 9A 3E DD 0B 74 3F ...
TA260 002:815.204   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:817.796 - 2.609ms returns 0x27C
TA260 002:817.811 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:817.815   Data:  B2 87 7B 3F 15 FA 3C 3E 53 9A 7B 3F CF 6E 3B 3E ...
TA260 002:817.825   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:819.735 - 1.924ms returns 0x184
TA260 002:819.745 JLINK_HasError()
TA260 002:819.778 JLINK_WriteReg(R0, 0x08008000)
TA260 002:819.793 - 0.014ms returns 0
TA260 002:819.798 JLINK_WriteReg(R1, 0x00000400)
TA260 002:819.804 - 0.006ms returns 0
TA260 002:819.810 JLINK_WriteReg(R2, 0x20000184)
TA260 002:819.814 - 0.003ms returns 0
TA260 002:819.818 JLINK_WriteReg(R3, 0x00000000)
TA260 002:819.821 - 0.003ms returns 0
TA260 002:819.825 JLINK_WriteReg(R4, 0x00000000)
TA260 002:819.829 - 0.003ms returns 0
TA260 002:819.833 JLINK_WriteReg(R5, 0x00000000)
TA260 002:819.846 - 0.013ms returns 0
TA260 002:819.850 JLINK_WriteReg(R6, 0x00000000)
TA260 002:819.854 - 0.003ms returns 0
TA260 002:819.858 JLINK_WriteReg(R7, 0x00000000)
TA260 002:819.861 - 0.003ms returns 0
TA260 002:819.865 JLINK_WriteReg(R8, 0x00000000)
TA260 002:819.868 - 0.003ms returns 0
TA260 002:819.872 JLINK_WriteReg(R9, 0x20000180)
TA260 002:819.876 - 0.003ms returns 0
TA260 002:819.880 JLINK_WriteReg(R10, 0x00000000)
TA260 002:819.884 - 0.003ms returns 0
TA260 002:819.888 JLINK_WriteReg(R11, 0x00000000)
TA260 002:819.891 - 0.003ms returns 0
TA260 002:819.895 JLINK_WriteReg(R12, 0x00000000)
TA260 002:819.899 - 0.003ms returns 0
TA260 002:819.903 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:819.907 - 0.003ms returns 0
TA260 002:819.911 JLINK_WriteReg(R14, 0x20000001)
TA260 002:819.914 - 0.003ms returns 0
TA260 002:819.918 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:819.921 - 0.003ms returns 0
TA260 002:819.926 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:819.929 - 0.003ms returns 0
TA260 002:819.933 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:819.936 - 0.003ms returns 0
TA260 002:819.941 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:819.944 - 0.003ms returns 0
TA260 002:819.948 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:819.952 - 0.003ms returns 0
TA260 002:819.956 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:819.961 - 0.004ms returns 0x0000002C
TA260 002:819.965 JLINK_Go()
TA260 002:819.974   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:822.740 - 2.775ms 
TA260 002:822.747 JLINK_IsHalted()
TA260 002:823.232 - 0.484ms returns FALSE
TA260 002:823.237 JLINK_HasError()
TA260 002:824.980 JLINK_IsHalted()
TA260 002:825.461 - 0.481ms returns FALSE
TA260 002:825.468 JLINK_HasError()
TA260 002:826.987 JLINK_IsHalted()
TA260 002:829.366   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:829.871 - 2.884ms returns TRUE
TA260 002:829.878 JLINK_ReadReg(R15 (PC))
TA260 002:829.883 - 0.004ms returns 0x20000000
TA260 002:829.887 JLINK_ClrBPEx(BPHandle = 0x0000002C)
TA260 002:829.891 - 0.004ms returns 0x00
TA260 002:829.895 JLINK_ReadReg(R0)
TA260 002:829.899 - 0.003ms returns 0x00000000
TA260 002:830.224 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:830.230   Data:  76 DB E4 3D 88 65 7E 3F 2E BC E1 3D B0 70 7E 3F ...
TA260 002:830.239   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:832.837 - 2.613ms returns 0x27C
TA260 002:832.844 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:832.848   Data:  39 FD 7F 3F 02 ED 2F BC 39 FC 7F 3F 90 0E 49 BC ...
TA260 002:832.855   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:834.745 - 1.900ms returns 0x184
TA260 002:834.750 JLINK_HasError()
TA260 002:834.755 JLINK_WriteReg(R0, 0x08008400)
TA260 002:834.760 - 0.004ms returns 0
TA260 002:834.764 JLINK_WriteReg(R1, 0x00000400)
TA260 002:834.767 - 0.003ms returns 0
TA260 002:834.771 JLINK_WriteReg(R2, 0x20000184)
TA260 002:834.774 - 0.003ms returns 0
TA260 002:834.778 JLINK_WriteReg(R3, 0x00000000)
TA260 002:834.782 - 0.003ms returns 0
TA260 002:834.786 JLINK_WriteReg(R4, 0x00000000)
TA260 002:834.789 - 0.003ms returns 0
TA260 002:834.793 JLINK_WriteReg(R5, 0x00000000)
TA260 002:834.797 - 0.003ms returns 0
TA260 002:834.801 JLINK_WriteReg(R6, 0x00000000)
TA260 002:834.804 - 0.003ms returns 0
TA260 002:834.808 JLINK_WriteReg(R7, 0x00000000)
TA260 002:834.812 - 0.003ms returns 0
TA260 002:834.816 JLINK_WriteReg(R8, 0x00000000)
TA260 002:834.819 - 0.003ms returns 0
TA260 002:834.823 JLINK_WriteReg(R9, 0x20000180)
TA260 002:834.827 - 0.003ms returns 0
TA260 002:834.831 JLINK_WriteReg(R10, 0x00000000)
TA260 002:834.834 - 0.003ms returns 0
TA260 002:834.838 JLINK_WriteReg(R11, 0x00000000)
TA260 002:834.844 - 0.005ms returns 0
TA260 002:834.849 JLINK_WriteReg(R12, 0x00000000)
TA260 002:834.852 - 0.003ms returns 0
TA260 002:834.857 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:834.860 - 0.003ms returns 0
TA260 002:834.864 JLINK_WriteReg(R14, 0x20000001)
TA260 002:834.868 - 0.003ms returns 0
TA260 002:834.872 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:834.876 - 0.003ms returns 0
TA260 002:834.880 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:834.883 - 0.003ms returns 0
TA260 002:834.887 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:834.890 - 0.003ms returns 0
TA260 002:834.894 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:834.898 - 0.003ms returns 0
TA260 002:834.902 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:834.905 - 0.003ms returns 0
TA260 002:834.910 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:834.914 - 0.004ms returns 0x0000002D
TA260 002:834.918 JLINK_Go()
TA260 002:834.924   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:837.746 - 2.827ms 
TA260 002:837.759 JLINK_IsHalted()
TA260 002:838.221 - 0.462ms returns FALSE
TA260 002:838.227 JLINK_HasError()
TA260 002:839.489 JLINK_IsHalted()
TA260 002:839.912 - 0.422ms returns FALSE
TA260 002:839.919 JLINK_HasError()
TA260 002:841.489 JLINK_IsHalted()
TA260 002:841.971 - 0.482ms returns FALSE
TA260 002:841.977 JLINK_HasError()
TA260 002:843.489 JLINK_IsHalted()
TA260 002:845.836   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:846.306 - 2.817ms returns TRUE
TA260 002:846.318 JLINK_ReadReg(R15 (PC))
TA260 002:846.324 - 0.005ms returns 0x20000000
TA260 002:846.328 JLINK_ClrBPEx(BPHandle = 0x0000002D)
TA260 002:846.332 - 0.003ms returns 0x00
TA260 002:846.336 JLINK_ReadReg(R0)
TA260 002:846.340 - 0.003ms returns 0x00000000
TA260 002:846.667 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:846.674   Data:  2B 95 AC BD E4 16 7F 3F 80 B6 AF BD 58 0E 7F 3F ...
TA260 002:846.684   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:849.292 - 2.625ms returns 0x27C
TA260 002:849.304 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:849.307   Data:  59 9C 7A 3F 89 8B 52 BE C1 87 7A 3F 01 15 54 BE ...
TA260 002:849.315   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:851.190 - 1.886ms returns 0x184
TA260 002:851.198 JLINK_HasError()
TA260 002:851.230 JLINK_WriteReg(R0, 0x08008800)
TA260 002:851.235 - 0.005ms returns 0
TA260 002:851.240 JLINK_WriteReg(R1, 0x00000400)
TA260 002:851.243 - 0.003ms returns 0
TA260 002:851.247 JLINK_WriteReg(R2, 0x20000184)
TA260 002:851.250 - 0.003ms returns 0
TA260 002:851.254 JLINK_WriteReg(R3, 0x00000000)
TA260 002:851.258 - 0.003ms returns 0
TA260 002:851.262 JLINK_WriteReg(R4, 0x00000000)
TA260 002:851.265 - 0.003ms returns 0
TA260 002:851.269 JLINK_WriteReg(R5, 0x00000000)
TA260 002:851.272 - 0.003ms returns 0
TA260 002:851.277 JLINK_WriteReg(R6, 0x00000000)
TA260 002:851.280 - 0.003ms returns 0
TA260 002:851.284 JLINK_WriteReg(R7, 0x00000000)
TA260 002:851.288 - 0.003ms returns 0
TA260 002:851.292 JLINK_WriteReg(R8, 0x00000000)
TA260 002:851.295 - 0.003ms returns 0
TA260 002:851.299 JLINK_WriteReg(R9, 0x20000180)
TA260 002:851.303 - 0.003ms returns 0
TA260 002:851.307 JLINK_WriteReg(R10, 0x00000000)
TA260 002:851.310 - 0.003ms returns 0
TA260 002:851.314 JLINK_WriteReg(R11, 0x00000000)
TA260 002:851.318 - 0.003ms returns 0
TA260 002:851.322 JLINK_WriteReg(R12, 0x00000000)
TA260 002:851.325 - 0.003ms returns 0
TA260 002:851.329 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:851.333 - 0.003ms returns 0
TA260 002:851.337 JLINK_WriteReg(R14, 0x20000001)
TA260 002:851.340 - 0.003ms returns 0
TA260 002:851.344 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:851.348 - 0.003ms returns 0
TA260 002:851.352 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:851.355 - 0.003ms returns 0
TA260 002:851.359 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:851.362 - 0.003ms returns 0
TA260 002:851.366 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:851.370 - 0.003ms returns 0
TA260 002:851.374 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:851.377 - 0.003ms returns 0
TA260 002:851.382 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:851.390 - 0.008ms returns 0x0000002E
TA260 002:851.394 JLINK_Go()
TA260 002:851.402   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:854.108 - 2.713ms 
TA260 002:854.113 JLINK_IsHalted()
TA260 002:854.610 - 0.496ms returns FALSE
TA260 002:854.615 JLINK_HasError()
TA260 002:856.503 JLINK_IsHalted()
TA260 002:856.965 - 0.462ms returns FALSE
TA260 002:856.977 JLINK_HasError()
TA260 002:858.501 JLINK_IsHalted()
TA260 002:860.954   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:861.462 - 2.961ms returns TRUE
TA260 002:861.469 JLINK_ReadReg(R15 (PC))
TA260 002:861.474 - 0.005ms returns 0x20000000
TA260 002:861.479 JLINK_ClrBPEx(BPHandle = 0x0000002E)
TA260 002:861.483 - 0.003ms returns 0x00
TA260 002:861.487 JLINK_ReadReg(R0)
TA260 002:861.490 - 0.003ms returns 0x00000000
TA260 002:861.796 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:861.803   Data:  FC D8 8D BE B3 FA 75 3F 22 9A 8E BE C6 DE 75 3F ...
TA260 002:861.812   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:864.472 - 2.676ms returns 0x27C
TA260 002:864.479 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:864.482   Data:  FB 99 6B 3F 77 00 C9 BE 95 72 6B 3F 53 B9 C9 BE ...
TA260 002:864.490   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:866.347 - 1.868ms returns 0x184
TA260 002:866.361 JLINK_HasError()
TA260 002:866.366 JLINK_WriteReg(R0, 0x08008C00)
TA260 002:866.371 - 0.005ms returns 0
TA260 002:866.375 JLINK_WriteReg(R1, 0x00000400)
TA260 002:866.379 - 0.003ms returns 0
TA260 002:866.383 JLINK_WriteReg(R2, 0x20000184)
TA260 002:866.386 - 0.003ms returns 0
TA260 002:866.390 JLINK_WriteReg(R3, 0x00000000)
TA260 002:866.394 - 0.003ms returns 0
TA260 002:866.398 JLINK_WriteReg(R4, 0x00000000)
TA260 002:866.401 - 0.003ms returns 0
TA260 002:866.405 JLINK_WriteReg(R5, 0x00000000)
TA260 002:866.408 - 0.003ms returns 0
TA260 002:866.412 JLINK_WriteReg(R6, 0x00000000)
TA260 002:866.416 - 0.003ms returns 0
TA260 002:866.420 JLINK_WriteReg(R7, 0x00000000)
TA260 002:866.424 - 0.003ms returns 0
TA260 002:866.428 JLINK_WriteReg(R8, 0x00000000)
TA260 002:866.431 - 0.003ms returns 0
TA260 002:866.435 JLINK_WriteReg(R9, 0x20000180)
TA260 002:866.438 - 0.003ms returns 0
TA260 002:866.442 JLINK_WriteReg(R10, 0x00000000)
TA260 002:866.446 - 0.003ms returns 0
TA260 002:866.450 JLINK_WriteReg(R11, 0x00000000)
TA260 002:866.454 - 0.003ms returns 0
TA260 002:866.458 JLINK_WriteReg(R12, 0x00000000)
TA260 002:866.461 - 0.003ms returns 0
TA260 002:866.465 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:866.469 - 0.003ms returns 0
TA260 002:866.473 JLINK_WriteReg(R14, 0x20000001)
TA260 002:866.476 - 0.003ms returns 0
TA260 002:866.480 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:866.484 - 0.003ms returns 0
TA260 002:866.488 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:866.491 - 0.003ms returns 0
TA260 002:866.495 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:866.498 - 0.003ms returns 0
TA260 002:866.502 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:866.506 - 0.003ms returns 0
TA260 002:866.510 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:866.513 - 0.003ms returns 0
TA260 002:866.518 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:866.522 - 0.004ms returns 0x0000002F
TA260 002:866.526 JLINK_Go()
TA260 002:866.534   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:869.336 - 2.809ms 
TA260 002:869.348 JLINK_IsHalted()
TA260 002:869.836 - 0.488ms returns FALSE
TA260 002:869.842 JLINK_HasError()
TA260 002:871.007 JLINK_IsHalted()
TA260 002:871.505 - 0.498ms returns FALSE
TA260 002:871.511 JLINK_HasError()
TA260 002:873.006 JLINK_IsHalted()
TA260 002:873.494 - 0.487ms returns FALSE
TA260 002:873.499 JLINK_HasError()
TA260 002:875.013 JLINK_IsHalted()
TA260 002:877.331   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:877.839 - 2.826ms returns TRUE
TA260 002:877.850 JLINK_ReadReg(R15 (PC))
TA260 002:877.855 - 0.004ms returns 0x20000000
TA260 002:877.883 JLINK_ClrBPEx(BPHandle = 0x0000002F)
TA260 002:877.888 - 0.005ms returns 0x00
TA260 002:877.893 JLINK_ReadReg(R0)
TA260 002:877.901 - 0.008ms returns 0x00000000
TA260 002:878.225 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:878.232   Data:  30 19 EB BE 95 6A 63 3F BB CB EB BE 5A 3C 63 3F ...
TA260 002:878.242   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:880.839 - 2.614ms returns 0x27C
TA260 002:880.846 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:880.849   Data:  C7 89 53 3F DC 80 10 BF 18 51 53 3F CD D3 10 BF ...
TA260 002:880.857   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:882.700 - 1.853ms returns 0x184
TA260 002:882.706 JLINK_HasError()
TA260 002:882.711 JLINK_WriteReg(R0, 0x08009000)
TA260 002:882.715 - 0.004ms returns 0
TA260 002:882.719 JLINK_WriteReg(R1, 0x00000400)
TA260 002:882.723 - 0.003ms returns 0
TA260 002:882.727 JLINK_WriteReg(R2, 0x20000184)
TA260 002:882.730 - 0.003ms returns 0
TA260 002:882.734 JLINK_WriteReg(R3, 0x00000000)
TA260 002:882.738 - 0.003ms returns 0
TA260 002:882.742 JLINK_WriteReg(R4, 0x00000000)
TA260 002:882.745 - 0.003ms returns 0
TA260 002:882.749 JLINK_WriteReg(R5, 0x00000000)
TA260 002:882.752 - 0.003ms returns 0
TA260 002:882.756 JLINK_WriteReg(R6, 0x00000000)
TA260 002:882.760 - 0.003ms returns 0
TA260 002:882.764 JLINK_WriteReg(R7, 0x00000000)
TA260 002:882.768 - 0.003ms returns 0
TA260 002:882.772 JLINK_WriteReg(R8, 0x00000000)
TA260 002:882.775 - 0.003ms returns 0
TA260 002:882.779 JLINK_WriteReg(R9, 0x20000180)
TA260 002:882.782 - 0.003ms returns 0
TA260 002:882.786 JLINK_WriteReg(R10, 0x00000000)
TA260 002:882.790 - 0.003ms returns 0
TA260 002:882.794 JLINK_WriteReg(R11, 0x00000000)
TA260 002:882.797 - 0.003ms returns 0
TA260 002:882.801 JLINK_WriteReg(R12, 0x00000000)
TA260 002:882.805 - 0.003ms returns 0
TA260 002:882.809 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:882.812 - 0.003ms returns 0
TA260 002:882.816 JLINK_WriteReg(R14, 0x20000001)
TA260 002:882.820 - 0.003ms returns 0
TA260 002:882.824 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:882.827 - 0.003ms returns 0
TA260 002:882.831 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:882.834 - 0.003ms returns 0
TA260 002:882.838 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:882.842 - 0.003ms returns 0
TA260 002:882.846 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:882.849 - 0.003ms returns 0
TA260 002:882.853 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:882.857 - 0.003ms returns 0
TA260 002:882.861 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:882.865 - 0.004ms returns 0x00000030
TA260 002:882.869 JLINK_Go()
TA260 002:882.877   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:885.613 - 2.742ms 
TA260 002:885.625 JLINK_IsHalted()
TA260 002:886.744 - 1.118ms returns FALSE
TA260 002:886.756 JLINK_HasError()
TA260 002:888.017 JLINK_IsHalted()
TA260 002:888.496 - 0.479ms returns FALSE
TA260 002:888.502 JLINK_HasError()
TA260 002:890.018 JLINK_IsHalted()
TA260 002:892.481   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:892.983 - 2.964ms returns TRUE
TA260 002:892.989 JLINK_ReadReg(R15 (PC))
TA260 002:892.994 - 0.004ms returns 0x20000000
TA260 002:892.998 JLINK_ClrBPEx(BPHandle = 0x00000030)
TA260 002:893.002 - 0.003ms returns 0x00
TA260 002:893.006 JLINK_ReadReg(R0)
TA260 002:893.010 - 0.003ms returns 0x00000000
TA260 002:893.327 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:893.334   Data:  41 A8 1F BF 27 1D 48 3F CB F6 1F BF 65 DE 47 3F ...
TA260 002:893.343   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:895.966 - 2.639ms returns 0x27C
TA260 002:895.981 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:895.985   Data:  7A 58 33 3F DF F3 36 BF AF 10 33 3F 23 3A 37 BF ...
TA260 002:895.994   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:897.832 - 1.850ms returns 0x184
TA260 002:897.843 JLINK_HasError()
TA260 002:897.848 JLINK_WriteReg(R0, 0x08009400)
TA260 002:897.853 - 0.005ms returns 0
TA260 002:897.857 JLINK_WriteReg(R1, 0x00000400)
TA260 002:897.861 - 0.003ms returns 0
TA260 002:897.865 JLINK_WriteReg(R2, 0x20000184)
TA260 002:897.868 - 0.003ms returns 0
TA260 002:897.872 JLINK_WriteReg(R3, 0x00000000)
TA260 002:897.878 - 0.006ms returns 0
TA260 002:897.883 JLINK_WriteReg(R4, 0x00000000)
TA260 002:897.886 - 0.003ms returns 0
TA260 002:897.890 JLINK_WriteReg(R5, 0x00000000)
TA260 002:897.894 - 0.003ms returns 0
TA260 002:897.898 JLINK_WriteReg(R6, 0x00000000)
TA260 002:897.901 - 0.003ms returns 0
TA260 002:897.905 JLINK_WriteReg(R7, 0x00000000)
TA260 002:897.909 - 0.003ms returns 0
TA260 002:897.913 JLINK_WriteReg(R8, 0x00000000)
TA260 002:897.916 - 0.003ms returns 0
TA260 002:897.921 JLINK_WriteReg(R9, 0x20000180)
TA260 002:897.924 - 0.003ms returns 0
TA260 002:897.928 JLINK_WriteReg(R10, 0x00000000)
TA260 002:897.931 - 0.003ms returns 0
TA260 002:897.935 JLINK_WriteReg(R11, 0x00000000)
TA260 002:897.939 - 0.003ms returns 0
TA260 002:897.943 JLINK_WriteReg(R12, 0x00000000)
TA260 002:897.946 - 0.003ms returns 0
TA260 002:897.950 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:897.954 - 0.003ms returns 0
TA260 002:897.958 JLINK_WriteReg(R14, 0x20000001)
TA260 002:897.961 - 0.003ms returns 0
TA260 002:897.965 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:897.969 - 0.003ms returns 0
TA260 002:897.973 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:897.976 - 0.003ms returns 0
TA260 002:897.980 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:897.984 - 0.003ms returns 0
TA260 002:897.988 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:897.991 - 0.003ms returns 0
TA260 002:897.995 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:897.998 - 0.003ms returns 0
TA260 002:898.003 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:898.007 - 0.004ms returns 0x00000031
TA260 002:898.011 JLINK_Go()
TA260 002:898.019   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:900.747 - 2.735ms 
TA260 002:900.753 JLINK_IsHalted()
TA260 002:901.267 - 0.513ms returns FALSE
TA260 002:901.273 JLINK_HasError()
TA260 002:903.524 JLINK_IsHalted()
TA260 002:904.017 - 0.493ms returns FALSE
TA260 002:904.023 JLINK_HasError()
TA260 002:906.030 JLINK_IsHalted()
TA260 002:908.479   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:908.985 - 2.954ms returns TRUE
TA260 002:908.992 JLINK_ReadReg(R15 (PC))
TA260 002:908.997 - 0.005ms returns 0x20000000
TA260 002:909.001 JLINK_ClrBPEx(BPHandle = 0x00000031)
TA260 002:909.005 - 0.004ms returns 0x00
TA260 002:909.009 JLINK_ReadReg(R0)
TA260 002:909.013 - 0.003ms returns 0x00000000
TA260 002:909.347 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:909.354   Data:  38 A1 43 BF 04 1F 25 3F 00 E2 43 BF 25 D2 24 3F ...
TA260 002:909.365   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:911.973 - 2.626ms returns 0x27C
TA260 002:911.982 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:911.987   Data:  C9 42 0C 3F 02 5F 56 BF A5 EE 0B 3F E5 95 56 BF ...
TA260 002:911.996   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:913.870 - 1.887ms returns 0x184
TA260 002:913.876 JLINK_HasError()
TA260 002:913.882 JLINK_WriteReg(R0, 0x08009800)
TA260 002:913.886 - 0.004ms returns 0
TA260 002:913.890 JLINK_WriteReg(R1, 0x00000400)
TA260 002:913.894 - 0.003ms returns 0
TA260 002:913.898 JLINK_WriteReg(R2, 0x20000184)
TA260 002:913.901 - 0.003ms returns 0
TA260 002:913.905 JLINK_WriteReg(R3, 0x00000000)
TA260 002:913.909 - 0.003ms returns 0
TA260 002:913.913 JLINK_WriteReg(R4, 0x00000000)
TA260 002:913.916 - 0.003ms returns 0
TA260 002:913.920 JLINK_WriteReg(R5, 0x00000000)
TA260 002:913.924 - 0.003ms returns 0
TA260 002:913.928 JLINK_WriteReg(R6, 0x00000000)
TA260 002:913.931 - 0.003ms returns 0
TA260 002:913.935 JLINK_WriteReg(R7, 0x00000000)
TA260 002:913.938 - 0.003ms returns 0
TA260 002:913.942 JLINK_WriteReg(R8, 0x00000000)
TA260 002:913.946 - 0.003ms returns 0
TA260 002:913.950 JLINK_WriteReg(R9, 0x20000180)
TA260 002:913.953 - 0.003ms returns 0
TA260 002:913.957 JLINK_WriteReg(R10, 0x00000000)
TA260 002:913.961 - 0.003ms returns 0
TA260 002:913.965 JLINK_WriteReg(R11, 0x00000000)
TA260 002:913.968 - 0.003ms returns 0
TA260 002:913.972 JLINK_WriteReg(R12, 0x00000000)
TA260 002:913.976 - 0.003ms returns 0
TA260 002:913.980 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:913.987 - 0.006ms returns 0
TA260 002:913.992 JLINK_WriteReg(R14, 0x20000001)
TA260 002:913.995 - 0.003ms returns 0
TA260 002:913.999 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:914.003 - 0.003ms returns 0
TA260 002:914.007 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:914.010 - 0.003ms returns 0
TA260 002:914.014 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:914.018 - 0.003ms returns 0
TA260 002:914.022 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:914.025 - 0.003ms returns 0
TA260 002:914.029 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:914.032 - 0.003ms returns 0
TA260 002:914.037 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:914.041 - 0.004ms returns 0x00000032
TA260 002:914.045 JLINK_Go()
TA260 002:914.052   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:916.782 - 2.736ms 
TA260 002:916.795 JLINK_IsHalted()
TA260 002:917.304 - 0.508ms returns FALSE
TA260 002:917.316 JLINK_HasError()
TA260 002:918.798 JLINK_IsHalted()
TA260 002:919.305 - 0.507ms returns FALSE
TA260 002:919.312 JLINK_HasError()
TA260 002:920.802 JLINK_IsHalted()
TA260 002:923.141   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:923.653 - 2.850ms returns TRUE
TA260 002:923.665 JLINK_ReadReg(R15 (PC))
TA260 002:923.671 - 0.005ms returns 0x20000000
TA260 002:923.675 JLINK_ClrBPEx(BPHandle = 0x00000032)
TA260 002:923.679 - 0.003ms returns 0x00
TA260 002:923.684 JLINK_ReadReg(R0)
TA260 002:923.687 - 0.003ms returns 0x00000000
TA260 002:924.046 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:924.054   Data:  96 15 60 BF DC 90 F7 3E 21 46 60 BF CB E0 F6 3E ...
TA260 002:924.064   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:926.654 - 2.607ms returns 0x27C
TA260 002:926.671 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:926.675   Data:  6F 92 BF 3E 2D 8D 6D BF EB D7 BE 3E 93 B2 6D BF ...
TA260 002:926.686   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:928.510 - 1.838ms returns 0x184
TA260 002:928.528 JLINK_HasError()
TA260 002:928.560 JLINK_WriteReg(R0, 0x08009C00)
TA260 002:928.575 - 0.014ms returns 0
TA260 002:928.580 JLINK_WriteReg(R1, 0x00000400)
TA260 002:928.583 - 0.003ms returns 0
TA260 002:928.587 JLINK_WriteReg(R2, 0x20000184)
TA260 002:928.591 - 0.003ms returns 0
TA260 002:928.595 JLINK_WriteReg(R3, 0x00000000)
TA260 002:928.598 - 0.003ms returns 0
TA260 002:928.602 JLINK_WriteReg(R4, 0x00000000)
TA260 002:928.606 - 0.003ms returns 0
TA260 002:928.610 JLINK_WriteReg(R5, 0x00000000)
TA260 002:928.613 - 0.003ms returns 0
TA260 002:928.617 JLINK_WriteReg(R6, 0x00000000)
TA260 002:928.621 - 0.003ms returns 0
TA260 002:928.625 JLINK_WriteReg(R7, 0x00000000)
TA260 002:928.628 - 0.003ms returns 0
TA260 002:928.632 JLINK_WriteReg(R8, 0x00000000)
TA260 002:928.636 - 0.003ms returns 0
TA260 002:928.640 JLINK_WriteReg(R9, 0x20000180)
TA260 002:928.643 - 0.003ms returns 0
TA260 002:928.647 JLINK_WriteReg(R10, 0x00000000)
TA260 002:928.651 - 0.003ms returns 0
TA260 002:928.655 JLINK_WriteReg(R11, 0x00000000)
TA260 002:928.659 - 0.003ms returns 0
TA260 002:928.663 JLINK_WriteReg(R12, 0x00000000)
TA260 002:928.666 - 0.003ms returns 0
TA260 002:928.670 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:928.674 - 0.004ms returns 0
TA260 002:928.678 JLINK_WriteReg(R14, 0x20000001)
TA260 002:928.682 - 0.003ms returns 0
TA260 002:928.686 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:928.689 - 0.003ms returns 0
TA260 002:928.694 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:928.697 - 0.003ms returns 0
TA260 002:928.701 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:928.704 - 0.003ms returns 0
TA260 002:928.708 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:928.712 - 0.003ms returns 0
TA260 002:928.716 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:928.719 - 0.003ms returns 0
TA260 002:928.724 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:928.728 - 0.004ms returns 0x00000033
TA260 002:928.733 JLINK_Go()
TA260 002:928.742   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:931.470 - 2.737ms 
TA260 002:931.482 JLINK_IsHalted()
TA260 002:931.983 - 0.501ms returns FALSE
TA260 002:931.993 JLINK_HasError()
TA260 002:933.318 JLINK_IsHalted()
TA260 002:933.814 - 0.495ms returns FALSE
TA260 002:933.820 JLINK_HasError()
TA260 002:935.824 JLINK_IsHalted()
TA260 002:938.152   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:938.646 - 2.822ms returns TRUE
TA260 002:938.653 JLINK_ReadReg(R15 (PC))
TA260 002:938.658 - 0.004ms returns 0x20000000
TA260 002:938.662 JLINK_ClrBPEx(BPHandle = 0x00000033)
TA260 002:938.666 - 0.003ms returns 0x00
TA260 002:938.670 JLINK_ReadReg(R0)
TA260 002:938.674 - 0.003ms returns 0x00000000
TA260 002:939.041 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:939.048   Data:  6E ED 73 BF 27 60 9B 3E DD 0B 74 BF 86 A0 9A 3E ...
TA260 002:939.058   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:941.734 - 2.693ms returns 0x27C
TA260 002:941.740 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:941.744   Data:  3E 85 3E 3E 53 9A 7B BF 15 FA 3C 3E CD AC 7B BF ...
TA260 002:941.751   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:943.654 - 1.913ms returns 0x184
TA260 002:943.660 JLINK_HasError()
TA260 002:943.665 JLINK_WriteReg(R0, 0x0800A000)
TA260 002:943.669 - 0.004ms returns 0
TA260 002:943.673 JLINK_WriteReg(R1, 0x00000400)
TA260 002:943.676 - 0.003ms returns 0
TA260 002:943.680 JLINK_WriteReg(R2, 0x20000184)
TA260 002:943.684 - 0.003ms returns 0
TA260 002:943.688 JLINK_WriteReg(R3, 0x00000000)
TA260 002:943.691 - 0.003ms returns 0
TA260 002:943.695 JLINK_WriteReg(R4, 0x00000000)
TA260 002:943.699 - 0.003ms returns 0
TA260 002:943.703 JLINK_WriteReg(R5, 0x00000000)
TA260 002:943.706 - 0.003ms returns 0
TA260 002:943.710 JLINK_WriteReg(R6, 0x00000000)
TA260 002:943.713 - 0.003ms returns 0
TA260 002:943.718 JLINK_WriteReg(R7, 0x00000000)
TA260 002:943.721 - 0.003ms returns 0
TA260 002:943.725 JLINK_WriteReg(R8, 0x00000000)
TA260 002:943.728 - 0.003ms returns 0
TA260 002:943.732 JLINK_WriteReg(R9, 0x20000180)
TA260 002:943.736 - 0.003ms returns 0
TA260 002:943.740 JLINK_WriteReg(R10, 0x00000000)
TA260 002:943.743 - 0.003ms returns 0
TA260 002:943.748 JLINK_WriteReg(R11, 0x00000000)
TA260 002:943.752 - 0.004ms returns 0
TA260 002:943.756 JLINK_WriteReg(R12, 0x00000000)
TA260 002:943.759 - 0.003ms returns 0
TA260 002:943.763 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:943.767 - 0.003ms returns 0
TA260 002:943.771 JLINK_WriteReg(R14, 0x20000001)
TA260 002:943.774 - 0.003ms returns 0
TA260 002:943.778 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:943.782 - 0.003ms returns 0
TA260 002:943.786 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:943.789 - 0.003ms returns 0
TA260 002:943.793 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:943.796 - 0.003ms returns 0
TA260 002:943.800 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:943.804 - 0.003ms returns 0
TA260 002:943.808 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:943.811 - 0.003ms returns 0
TA260 002:943.816 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:943.820 - 0.004ms returns 0x00000034
TA260 002:943.824 JLINK_Go()
TA260 002:943.831   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:946.606 - 2.782ms 
TA260 002:946.622 JLINK_IsHalted()
TA260 002:947.089 - 0.467ms returns FALSE
TA260 002:947.101 JLINK_HasError()
TA260 002:948.215 JLINK_IsHalted()
TA260 002:948.710 - 0.494ms returns FALSE
TA260 002:948.716 JLINK_HasError()
TA260 002:950.214 JLINK_IsHalted()
TA260 002:950.691 - 0.476ms returns FALSE
TA260 002:950.697 JLINK_HasError()
TA260 002:952.220 JLINK_IsHalted()
TA260 002:954.574   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:955.041 - 2.820ms returns TRUE
TA260 002:955.051 JLINK_ReadReg(R15 (PC))
TA260 002:955.056 - 0.005ms returns 0x20000000
TA260 002:955.088 JLINK_ClrBPEx(BPHandle = 0x00000034)
TA260 002:955.098 - 0.010ms returns 0x00
TA260 002:955.104 JLINK_ReadReg(R0)
TA260 002:955.108 - 0.004ms returns 0x00000000
TA260 002:955.436 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:955.443   Data:  88 65 7E BF 76 DB E4 3D B0 70 7E BF 2E BC E1 3D ...
TA260 002:955.459   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:958.024 - 2.589ms returns 0x27C
TA260 002:958.037 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:958.041   Data:  59 CB 16 BC 39 FC 7F BF 02 ED 2F BC 11 FB 7F BF ...
TA260 002:958.050   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:959.929 - 1.892ms returns 0x184
TA260 002:959.943 JLINK_HasError()
TA260 002:959.948 JLINK_WriteReg(R0, 0x0800A400)
TA260 002:959.954 - 0.005ms returns 0
TA260 002:959.958 JLINK_WriteReg(R1, 0x00000400)
TA260 002:959.961 - 0.003ms returns 0
TA260 002:959.965 JLINK_WriteReg(R2, 0x20000184)
TA260 002:959.968 - 0.003ms returns 0
TA260 002:959.973 JLINK_WriteReg(R3, 0x00000000)
TA260 002:959.976 - 0.003ms returns 0
TA260 002:959.980 JLINK_WriteReg(R4, 0x00000000)
TA260 002:959.983 - 0.003ms returns 0
TA260 002:959.987 JLINK_WriteReg(R5, 0x00000000)
TA260 002:959.991 - 0.003ms returns 0
TA260 002:959.995 JLINK_WriteReg(R6, 0x00000000)
TA260 002:959.998 - 0.003ms returns 0
TA260 002:960.002 JLINK_WriteReg(R7, 0x00000000)
TA260 002:960.006 - 0.003ms returns 0
TA260 002:960.010 JLINK_WriteReg(R8, 0x00000000)
TA260 002:960.013 - 0.003ms returns 0
TA260 002:960.017 JLINK_WriteReg(R9, 0x20000180)
TA260 002:960.021 - 0.003ms returns 0
TA260 002:960.025 JLINK_WriteReg(R10, 0x00000000)
TA260 002:960.028 - 0.003ms returns 0
TA260 002:960.032 JLINK_WriteReg(R11, 0x00000000)
TA260 002:960.036 - 0.003ms returns 0
TA260 002:960.040 JLINK_WriteReg(R12, 0x00000000)
TA260 002:960.043 - 0.003ms returns 0
TA260 002:960.047 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:960.051 - 0.003ms returns 0
TA260 002:960.055 JLINK_WriteReg(R14, 0x20000001)
TA260 002:960.058 - 0.003ms returns 0
TA260 002:960.062 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:960.066 - 0.003ms returns 0
TA260 002:960.070 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:960.073 - 0.003ms returns 0
TA260 002:960.077 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:960.081 - 0.003ms returns 0
TA260 002:960.085 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:960.088 - 0.003ms returns 0
TA260 002:960.092 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:960.096 - 0.003ms returns 0
TA260 002:960.100 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:960.104 - 0.004ms returns 0x00000035
TA260 002:960.109 JLINK_Go()
TA260 002:960.116   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:962.838 - 2.729ms 
TA260 002:962.850 JLINK_IsHalted()
TA260 002:963.325 - 0.474ms returns FALSE
TA260 002:963.332 JLINK_HasError()
TA260 002:964.730 JLINK_IsHalted()
TA260 002:965.233 - 0.503ms returns FALSE
TA260 002:965.240 JLINK_HasError()
TA260 002:967.231 JLINK_IsHalted()
TA260 002:969.582   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:970.076 - 2.845ms returns TRUE
TA260 002:970.083 JLINK_ReadReg(R15 (PC))
TA260 002:970.087 - 0.004ms returns 0x20000000
TA260 002:970.092 JLINK_ClrBPEx(BPHandle = 0x00000035)
TA260 002:970.096 - 0.003ms returns 0x00
TA260 002:970.100 JLINK_ReadReg(R0)
TA260 002:970.103 - 0.003ms returns 0x00000000
TA260 002:970.428 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:970.435   Data:  E4 16 7F BF 2B 95 AC BD 58 0E 7F BF 80 B6 AF BD ...
TA260 002:970.443   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:973.067 - 2.639ms returns 0x27C
TA260 002:973.079 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:973.083   Data:  F1 01 51 BE C1 87 7A BF 89 8B 52 BE 02 73 7A BF ...
TA260 002:973.093   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:974.929 - 1.849ms returns 0x184
TA260 002:974.937 JLINK_HasError()
TA260 002:974.942 JLINK_WriteReg(R0, 0x0800A800)
TA260 002:974.947 - 0.004ms returns 0
TA260 002:974.952 JLINK_WriteReg(R1, 0x00000400)
TA260 002:974.955 - 0.003ms returns 0
TA260 002:974.959 JLINK_WriteReg(R2, 0x20000184)
TA260 002:974.962 - 0.003ms returns 0
TA260 002:974.966 JLINK_WriteReg(R3, 0x00000000)
TA260 002:974.970 - 0.003ms returns 0
TA260 002:974.974 JLINK_WriteReg(R4, 0x00000000)
TA260 002:974.977 - 0.003ms returns 0
TA260 002:974.981 JLINK_WriteReg(R5, 0x00000000)
TA260 002:974.985 - 0.003ms returns 0
TA260 002:974.989 JLINK_WriteReg(R6, 0x00000000)
TA260 002:975.064 - 0.074ms returns 0
TA260 002:975.068 JLINK_WriteReg(R7, 0x00000000)
TA260 002:975.071 - 0.003ms returns 0
TA260 002:975.075 JLINK_WriteReg(R8, 0x00000000)
TA260 002:975.079 - 0.003ms returns 0
TA260 002:975.083 JLINK_WriteReg(R9, 0x20000180)
TA260 002:975.086 - 0.003ms returns 0
TA260 002:975.090 JLINK_WriteReg(R10, 0x00000000)
TA260 002:975.104 - 0.013ms returns 0
TA260 002:975.108 JLINK_WriteReg(R11, 0x00000000)
TA260 002:975.111 - 0.003ms returns 0
TA260 002:975.115 JLINK_WriteReg(R12, 0x00000000)
TA260 002:975.119 - 0.003ms returns 0
TA260 002:975.123 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:975.126 - 0.003ms returns 0
TA260 002:975.130 JLINK_WriteReg(R14, 0x20000001)
TA260 002:975.134 - 0.003ms returns 0
TA260 002:975.138 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:975.141 - 0.003ms returns 0
TA260 002:975.145 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:975.149 - 0.003ms returns 0
TA260 002:975.153 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:975.156 - 0.003ms returns 0
TA260 002:975.160 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:975.163 - 0.003ms returns 0
TA260 002:975.167 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:975.171 - 0.003ms returns 0
TA260 002:975.175 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:975.179 - 0.004ms returns 0x00000036
TA260 002:975.183 JLINK_Go()
TA260 002:975.190   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:978.914 - 3.729ms 
TA260 002:978.928 JLINK_IsHalted()
TA260 002:979.389 - 0.460ms returns FALSE
TA260 002:979.394 JLINK_HasError()
TA260 002:980.463 JLINK_IsHalted()
TA260 002:980.938 - 0.474ms returns FALSE
TA260 002:980.949 JLINK_HasError()
TA260 002:982.462 JLINK_IsHalted()
TA260 002:982.903 - 0.440ms returns FALSE
TA260 002:982.908 JLINK_HasError()
TA260 002:984.971 JLINK_IsHalted()
TA260 002:987.536   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:988.053 - 3.081ms returns TRUE
TA260 002:988.060 JLINK_ReadReg(R15 (PC))
TA260 002:988.066 - 0.006ms returns 0x20000000
TA260 002:988.071 JLINK_ClrBPEx(BPHandle = 0x00000036)
TA260 002:988.075 - 0.003ms returns 0x00
TA260 002:988.079 JLINK_ReadReg(R0)
TA260 002:988.083 - 0.003ms returns 0x00000000
TA260 002:988.427 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:988.434   Data:  B3 FA 75 BF FC D8 8D BE C6 DE 75 BF 22 9A 8E BE ...
TA260 002:988.444   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:991.017 - 2.589ms returns 0x27C
TA260 002:991.025 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:991.029   Data:  7C 47 C8 BE 95 72 6B BF 77 00 C9 BE 0C 4B 6B BF ...
TA260 002:991.037   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:993.008 - 1.982ms returns 0x184
TA260 002:993.018 JLINK_HasError()
TA260 002:993.024 JLINK_WriteReg(R0, 0x0800AC00)
TA260 002:993.029 - 0.005ms returns 0
TA260 002:993.033 JLINK_WriteReg(R1, 0x00000400)
TA260 002:993.037 - 0.003ms returns 0
TA260 002:993.041 JLINK_WriteReg(R2, 0x20000184)
TA260 002:993.044 - 0.003ms returns 0
TA260 002:993.048 JLINK_WriteReg(R3, 0x00000000)
TA260 002:993.052 - 0.003ms returns 0
TA260 002:993.056 JLINK_WriteReg(R4, 0x00000000)
TA260 002:993.059 - 0.003ms returns 0
TA260 002:993.063 JLINK_WriteReg(R5, 0x00000000)
TA260 002:993.066 - 0.003ms returns 0
TA260 002:993.070 JLINK_WriteReg(R6, 0x00000000)
TA260 002:993.074 - 0.003ms returns 0
TA260 002:993.078 JLINK_WriteReg(R7, 0x00000000)
TA260 002:993.082 - 0.003ms returns 0
TA260 002:993.093 JLINK_WriteReg(R8, 0x00000000)
TA260 002:993.096 - 0.004ms returns 0
TA260 002:993.100 JLINK_WriteReg(R9, 0x20000180)
TA260 002:993.104 - 0.003ms returns 0
TA260 002:993.108 JLINK_WriteReg(R10, 0x00000000)
TA260 002:993.111 - 0.003ms returns 0
TA260 002:993.115 JLINK_WriteReg(R11, 0x00000000)
TA260 002:993.119 - 0.004ms returns 0
TA260 002:993.123 JLINK_WriteReg(R12, 0x00000000)
TA260 002:993.126 - 0.003ms returns 0
TA260 002:993.131 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:993.134 - 0.003ms returns 0
TA260 002:993.138 JLINK_WriteReg(R14, 0x20000001)
TA260 002:993.142 - 0.003ms returns 0
TA260 002:993.146 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:993.152 - 0.006ms returns 0
TA260 002:993.156 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:993.160 - 0.003ms returns 0
TA260 002:993.164 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:993.167 - 0.003ms returns 0
TA260 002:993.171 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:993.175 - 0.003ms returns 0
TA260 002:993.179 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:993.182 - 0.003ms returns 0
TA260 002:993.187 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:993.191 - 0.004ms returns 0x00000037
TA260 002:993.195 JLINK_Go()
TA260 002:993.207   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:998.391 - 5.195ms 
TA260 002:998.404 JLINK_IsHalted()
TA260 002:999.077 - 0.672ms returns FALSE
TA260 002:999.087 JLINK_HasError()
TA260 003:001.819 JLINK_IsHalted()
TA260 003:002.282 - 0.463ms returns FALSE
TA260 003:002.288 JLINK_HasError()
TA260 003:003.811 JLINK_IsHalted()
TA260 003:006.137   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:006.647 - 2.836ms returns TRUE
TA260 003:006.660 JLINK_ReadReg(R15 (PC))
TA260 003:006.666 - 0.005ms returns 0x20000000
TA260 003:006.670 JLINK_ClrBPEx(BPHandle = 0x00000037)
TA260 003:006.675 - 0.004ms returns 0x00
TA260 003:006.679 JLINK_ReadReg(R0)
TA260 003:006.683 - 0.003ms returns 0x00000000
TA260 003:007.023 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:007.030   Data:  95 6A 63 BF 30 19 EB BE 5A 3C 63 BF BB CB EB BE ...
TA260 003:007.040   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:009.552 - 2.529ms returns 0x27C
TA260 003:009.565 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:009.569   Data:  D5 2D 10 BF 18 51 53 BF DC 80 10 BF 49 18 53 BF ...
TA260 003:009.577   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:011.471 - 1.905ms returns 0x184
TA260 003:011.484 JLINK_HasError()
TA260 003:011.517 JLINK_WriteReg(R0, 0x0800B000)
TA260 003:011.527 - 0.010ms returns 0
TA260 003:011.532 JLINK_WriteReg(R1, 0x00000400)
TA260 003:011.536 - 0.003ms returns 0
TA260 003:011.540 JLINK_WriteReg(R2, 0x20000184)
TA260 003:011.543 - 0.003ms returns 0
TA260 003:011.547 JLINK_WriteReg(R3, 0x00000000)
TA260 003:011.550 - 0.003ms returns 0
TA260 003:011.555 JLINK_WriteReg(R4, 0x00000000)
TA260 003:011.558 - 0.003ms returns 0
TA260 003:011.567 JLINK_WriteReg(R5, 0x00000000)
TA260 003:011.570 - 0.003ms returns 0
TA260 003:011.574 JLINK_WriteReg(R6, 0x00000000)
TA260 003:011.578 - 0.003ms returns 0
TA260 003:011.582 JLINK_WriteReg(R7, 0x00000000)
TA260 003:011.585 - 0.003ms returns 0
TA260 003:011.589 JLINK_WriteReg(R8, 0x00000000)
TA260 003:011.592 - 0.003ms returns 0
TA260 003:011.597 JLINK_WriteReg(R9, 0x20000180)
TA260 003:011.600 - 0.003ms returns 0
TA260 003:011.604 JLINK_WriteReg(R10, 0x00000000)
TA260 003:011.608 - 0.003ms returns 0
TA260 003:011.612 JLINK_WriteReg(R11, 0x00000000)
TA260 003:011.615 - 0.003ms returns 0
TA260 003:011.619 JLINK_WriteReg(R12, 0x00000000)
TA260 003:011.623 - 0.003ms returns 0
TA260 003:011.627 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:011.631 - 0.003ms returns 0
TA260 003:011.635 JLINK_WriteReg(R14, 0x20000001)
TA260 003:011.638 - 0.003ms returns 0
TA260 003:011.642 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:011.646 - 0.003ms returns 0
TA260 003:011.650 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:011.653 - 0.003ms returns 0
TA260 003:011.657 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:011.660 - 0.003ms returns 0
TA260 003:011.665 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:011.668 - 0.003ms returns 0
TA260 003:011.672 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:011.676 - 0.003ms returns 0
TA260 003:011.680 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:011.685 - 0.004ms returns 0x00000038
TA260 003:011.689 JLINK_Go()
TA260 003:011.698   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:014.469 - 2.780ms 
TA260 003:014.476 JLINK_IsHalted()
TA260 003:014.951 - 0.474ms returns FALSE
TA260 003:014.957 JLINK_HasError()
TA260 003:016.296 JLINK_IsHalted()
TA260 003:016.794 - 0.497ms returns FALSE
TA260 003:016.806 JLINK_HasError()
TA260 003:018.797 JLINK_IsHalted()
TA260 003:021.132   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:021.607 - 2.809ms returns TRUE
TA260 003:021.613 JLINK_ReadReg(R15 (PC))
TA260 003:021.617 - 0.004ms returns 0x20000000
TA260 003:021.622 JLINK_ClrBPEx(BPHandle = 0x00000038)
TA260 003:021.626 - 0.003ms returns 0x00
TA260 003:021.630 JLINK_ReadReg(R0)
TA260 003:021.634 - 0.003ms returns 0x00000000
TA260 003:021.972 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:021.979   Data:  27 1D 48 BF 41 A8 1F BF 65 DE 47 BF CB F6 1F BF ...
TA260 003:021.988   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:024.565 - 2.593ms returns 0x27C
TA260 003:024.571 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:024.575   Data:  7F AD 36 BF AF 10 33 BF DF F3 36 BF C9 C8 32 BF ...
TA260 003:024.582   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:026.481 - 1.909ms returns 0x184
TA260 003:026.497 JLINK_HasError()
TA260 003:026.502 JLINK_WriteReg(R0, 0x0800B400)
TA260 003:026.507 - 0.005ms returns 0
TA260 003:026.512 JLINK_WriteReg(R1, 0x00000400)
TA260 003:026.515 - 0.003ms returns 0
TA260 003:026.519 JLINK_WriteReg(R2, 0x20000184)
TA260 003:026.523 - 0.003ms returns 0
TA260 003:026.527 JLINK_WriteReg(R3, 0x00000000)
TA260 003:026.530 - 0.003ms returns 0
TA260 003:026.534 JLINK_WriteReg(R4, 0x00000000)
TA260 003:026.538 - 0.003ms returns 0
TA260 003:026.542 JLINK_WriteReg(R5, 0x00000000)
TA260 003:026.546 - 0.004ms returns 0
TA260 003:026.550 JLINK_WriteReg(R6, 0x00000000)
TA260 003:026.553 - 0.003ms returns 0
TA260 003:026.557 JLINK_WriteReg(R7, 0x00000000)
TA260 003:026.560 - 0.003ms returns 0
TA260 003:026.564 JLINK_WriteReg(R8, 0x00000000)
TA260 003:026.568 - 0.003ms returns 0
TA260 003:026.572 JLINK_WriteReg(R9, 0x20000180)
TA260 003:026.576 - 0.003ms returns 0
TA260 003:026.580 JLINK_WriteReg(R10, 0x00000000)
TA260 003:026.583 - 0.003ms returns 0
TA260 003:026.587 JLINK_WriteReg(R11, 0x00000000)
TA260 003:026.590 - 0.003ms returns 0
TA260 003:026.594 JLINK_WriteReg(R12, 0x00000000)
TA260 003:026.598 - 0.003ms returns 0
TA260 003:026.602 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:026.606 - 0.004ms returns 0
TA260 003:026.610 JLINK_WriteReg(R14, 0x20000001)
TA260 003:026.613 - 0.003ms returns 0
TA260 003:026.617 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:026.621 - 0.003ms returns 0
TA260 003:026.625 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:026.628 - 0.003ms returns 0
TA260 003:026.632 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:026.636 - 0.003ms returns 0
TA260 003:026.640 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:026.644 - 0.003ms returns 0
TA260 003:026.648 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:026.651 - 0.003ms returns 0
TA260 003:026.656 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:026.660 - 0.004ms returns 0x00000039
TA260 003:026.664 JLINK_Go()
TA260 003:026.672   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:029.471 - 2.806ms 
TA260 003:029.483 JLINK_IsHalted()
TA260 003:030.007 - 0.523ms returns FALSE
TA260 003:030.015 JLINK_HasError()
TA260 003:031.306 JLINK_IsHalted()
TA260 003:031.883 - 0.577ms returns FALSE
TA260 003:031.894 JLINK_HasError()
TA260 003:033.552 JLINK_IsHalted()
TA260 003:035.985   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:036.704 - 3.151ms returns TRUE
TA260 003:036.718 JLINK_ReadReg(R15 (PC))
TA260 003:036.724 - 0.006ms returns 0x20000000
TA260 003:036.729 JLINK_ClrBPEx(BPHandle = 0x00000039)
TA260 003:036.733 - 0.004ms returns 0x00
TA260 003:036.738 JLINK_ReadReg(R0)
TA260 003:036.742 - 0.004ms returns 0x00000000
TA260 003:037.581 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:037.593   Data:  04 1F 25 BF 38 A1 43 BF 25 D2 24 BF 00 E2 43 BF ...
TA260 003:037.605   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:040.239 - 2.657ms returns 0x27C
TA260 003:040.250 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:040.254   Data:  FE 27 56 BF A5 EE 0B BF 02 5F 56 BF 6B 9A 0B BF ...
TA260 003:040.264   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:042.086 - 1.835ms returns 0x184
TA260 003:042.096 JLINK_HasError()
TA260 003:042.101 JLINK_WriteReg(R0, 0x0800B800)
TA260 003:042.106 - 0.005ms returns 0
TA260 003:042.111 JLINK_WriteReg(R1, 0x00000400)
TA260 003:042.114 - 0.003ms returns 0
TA260 003:042.118 JLINK_WriteReg(R2, 0x20000184)
TA260 003:042.122 - 0.003ms returns 0
TA260 003:042.126 JLINK_WriteReg(R3, 0x00000000)
TA260 003:042.129 - 0.003ms returns 0
TA260 003:042.133 JLINK_WriteReg(R4, 0x00000000)
TA260 003:042.136 - 0.003ms returns 0
TA260 003:042.140 JLINK_WriteReg(R5, 0x00000000)
TA260 003:042.144 - 0.003ms returns 0
TA260 003:042.148 JLINK_WriteReg(R6, 0x00000000)
TA260 003:042.151 - 0.003ms returns 0
TA260 003:042.155 JLINK_WriteReg(R7, 0x00000000)
TA260 003:042.158 - 0.003ms returns 0
TA260 003:042.162 JLINK_WriteReg(R8, 0x00000000)
TA260 003:042.166 - 0.003ms returns 0
TA260 003:042.170 JLINK_WriteReg(R9, 0x20000180)
TA260 003:042.173 - 0.003ms returns 0
TA260 003:042.177 JLINK_WriteReg(R10, 0x00000000)
TA260 003:042.181 - 0.003ms returns 0
TA260 003:042.185 JLINK_WriteReg(R11, 0x00000000)
TA260 003:042.188 - 0.003ms returns 0
TA260 003:042.192 JLINK_WriteReg(R12, 0x00000000)
TA260 003:042.196 - 0.003ms returns 0
TA260 003:042.200 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:042.204 - 0.004ms returns 0
TA260 003:042.208 JLINK_WriteReg(R14, 0x20000001)
TA260 003:042.211 - 0.003ms returns 0
TA260 003:042.215 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:042.218 - 0.003ms returns 0
TA260 003:042.222 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:042.226 - 0.003ms returns 0
TA260 003:042.230 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:042.233 - 0.003ms returns 0
TA260 003:042.237 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:042.240 - 0.003ms returns 0
TA260 003:042.244 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:042.248 - 0.003ms returns 0
TA260 003:042.252 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:042.256 - 0.004ms returns 0x0000003A
TA260 003:042.261 JLINK_Go()
TA260 003:042.268   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:045.030 - 2.768ms 
TA260 003:045.038 JLINK_IsHalted()
TA260 003:045.539 - 0.501ms returns FALSE
TA260 003:045.545 JLINK_HasError()
TA260 003:047.321 JLINK_IsHalted()
TA260 003:047.838 - 0.516ms returns FALSE
TA260 003:047.844 JLINK_HasError()
TA260 003:049.318 JLINK_IsHalted()
TA260 003:051.577   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:052.075 - 2.756ms returns TRUE
TA260 003:052.081 JLINK_ReadReg(R15 (PC))
TA260 003:052.086 - 0.005ms returns 0x20000000
TA260 003:052.090 JLINK_ClrBPEx(BPHandle = 0x0000003A)
TA260 003:052.094 - 0.003ms returns 0x00
TA260 003:052.101 JLINK_ReadReg(R0)
TA260 003:052.105 - 0.003ms returns 0x00000000
TA260 003:052.442 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:052.449   Data:  DC 90 F7 BE 96 15 60 BF CB E0 F6 BE 21 46 60 BF ...
TA260 003:052.459   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:055.077 - 2.635ms returns 0x27C
TA260 003:055.084 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:055.088   Data:  A1 67 6D BF EB D7 BE BE 2D 8D 6D BF 4A 1D BE BE ...
TA260 003:055.096   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:057.020 - 1.935ms returns 0x184
TA260 003:057.036 JLINK_HasError()
TA260 003:057.042 JLINK_WriteReg(R0, 0x0800BC00)
TA260 003:057.048 - 0.005ms returns 0
TA260 003:057.052 JLINK_WriteReg(R1, 0x00000400)
TA260 003:057.055 - 0.003ms returns 0
TA260 003:057.060 JLINK_WriteReg(R2, 0x20000184)
TA260 003:057.064 - 0.004ms returns 0
TA260 003:057.068 JLINK_WriteReg(R3, 0x00000000)
TA260 003:057.071 - 0.003ms returns 0
TA260 003:057.075 JLINK_WriteReg(R4, 0x00000000)
TA260 003:057.078 - 0.003ms returns 0
TA260 003:057.082 JLINK_WriteReg(R5, 0x00000000)
TA260 003:057.086 - 0.003ms returns 0
TA260 003:057.090 JLINK_WriteReg(R6, 0x00000000)
TA260 003:057.093 - 0.003ms returns 0
TA260 003:057.097 JLINK_WriteReg(R7, 0x00000000)
TA260 003:057.100 - 0.003ms returns 0
TA260 003:057.105 JLINK_WriteReg(R8, 0x00000000)
TA260 003:057.108 - 0.003ms returns 0
TA260 003:057.112 JLINK_WriteReg(R9, 0x20000180)
TA260 003:057.119 - 0.006ms returns 0
TA260 003:057.123 JLINK_WriteReg(R10, 0x00000000)
TA260 003:057.126 - 0.003ms returns 0
TA260 003:057.130 JLINK_WriteReg(R11, 0x00000000)
TA260 003:057.134 - 0.003ms returns 0
TA260 003:057.138 JLINK_WriteReg(R12, 0x00000000)
TA260 003:057.141 - 0.003ms returns 0
TA260 003:057.145 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:057.149 - 0.003ms returns 0
TA260 003:057.153 JLINK_WriteReg(R14, 0x20000001)
TA260 003:057.157 - 0.003ms returns 0
TA260 003:057.161 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:057.164 - 0.003ms returns 0
TA260 003:057.168 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:057.172 - 0.003ms returns 0
TA260 003:057.176 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:057.179 - 0.003ms returns 0
TA260 003:057.183 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:057.186 - 0.003ms returns 0
TA260 003:057.190 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:057.194 - 0.003ms returns 0
TA260 003:057.198 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:057.202 - 0.004ms returns 0x0000003B
TA260 003:057.207 JLINK_Go()
TA260 003:057.214   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:059.996 - 2.789ms 
TA260 003:060.003 JLINK_IsHalted()
TA260 003:060.497 - 0.494ms returns FALSE
TA260 003:060.505 JLINK_HasError()
TA260 003:061.873 JLINK_IsHalted()
TA260 003:062.468 - 0.595ms returns FALSE
TA260 003:062.486 JLINK_HasError()
TA260 003:063.873 JLINK_IsHalted()
TA260 003:064.341 - 0.466ms returns FALSE
TA260 003:064.350 JLINK_HasError()
TA260 003:066.382 JLINK_IsHalted()
TA260 003:068.694   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:069.190 - 2.808ms returns TRUE
TA260 003:069.197 JLINK_ReadReg(R15 (PC))
TA260 003:069.202 - 0.005ms returns 0x20000000
TA260 003:069.207 JLINK_ClrBPEx(BPHandle = 0x0000003B)
TA260 003:069.211 - 0.003ms returns 0x00
TA260 003:069.215 JLINK_ReadReg(R0)
TA260 003:069.219 - 0.003ms returns 0x00000000
TA260 003:069.596 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:069.603   Data:  27 60 9B BE 6E ED 73 BF 86 A0 9A BE DD 0B 74 BF ...
TA260 003:069.614   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:072.197 - 2.600ms returns 0x27C
TA260 003:072.208 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:072.212   Data:  B2 87 7B BF 15 FA 3C BE 53 9A 7B BF CF 6E 3B BE ...
TA260 003:072.223   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:074.064 - 1.856ms returns 0x184
TA260 003:074.070 JLINK_HasError()
TA260 003:074.075 JLINK_WriteReg(R0, 0x0800C000)
TA260 003:074.080 - 0.004ms returns 0
TA260 003:074.084 JLINK_WriteReg(R1, 0x00000400)
TA260 003:074.087 - 0.003ms returns 0
TA260 003:074.091 JLINK_WriteReg(R2, 0x20000184)
TA260 003:074.095 - 0.003ms returns 0
TA260 003:074.099 JLINK_WriteReg(R3, 0x00000000)
TA260 003:074.102 - 0.003ms returns 0
TA260 003:074.106 JLINK_WriteReg(R4, 0x00000000)
TA260 003:074.110 - 0.003ms returns 0
TA260 003:074.114 JLINK_WriteReg(R5, 0x00000000)
TA260 003:074.117 - 0.003ms returns 0
TA260 003:074.121 JLINK_WriteReg(R6, 0x00000000)
TA260 003:074.124 - 0.003ms returns 0
TA260 003:074.128 JLINK_WriteReg(R7, 0x00000000)
TA260 003:074.132 - 0.003ms returns 0
TA260 003:074.136 JLINK_WriteReg(R8, 0x00000000)
TA260 003:074.139 - 0.003ms returns 0
TA260 003:074.143 JLINK_WriteReg(R9, 0x20000180)
TA260 003:074.147 - 0.003ms returns 0
TA260 003:074.151 JLINK_WriteReg(R10, 0x00000000)
TA260 003:074.154 - 0.003ms returns 0
TA260 003:074.158 JLINK_WriteReg(R11, 0x00000000)
TA260 003:074.162 - 0.003ms returns 0
TA260 003:074.166 JLINK_WriteReg(R12, 0x00000000)
TA260 003:074.169 - 0.003ms returns 0
TA260 003:074.173 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:074.177 - 0.004ms returns 0
TA260 003:074.181 JLINK_WriteReg(R14, 0x20000001)
TA260 003:074.185 - 0.003ms returns 0
TA260 003:074.189 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:074.193 - 0.003ms returns 0
TA260 003:074.197 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:074.206 - 0.009ms returns 0
TA260 003:074.211 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:074.214 - 0.003ms returns 0
TA260 003:074.221 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:074.225 - 0.003ms returns 0
TA260 003:074.229 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:074.232 - 0.003ms returns 0
TA260 003:074.237 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:074.241 - 0.004ms returns 0x0000003C
TA260 003:074.245 JLINK_Go()
TA260 003:074.252   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:077.034 - 2.788ms 
TA260 003:077.051 JLINK_IsHalted()
TA260 003:077.601 - 0.549ms returns FALSE
TA260 003:077.613 JLINK_HasError()
TA260 003:078.886 JLINK_IsHalted()
TA260 003:079.371 - 0.484ms returns FALSE
TA260 003:079.377 JLINK_HasError()
TA260 003:083.886 JLINK_IsHalted()
TA260 003:086.189   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:086.708 - 2.822ms returns TRUE
TA260 003:086.722 JLINK_ReadReg(R15 (PC))
TA260 003:086.728 - 0.005ms returns 0x20000000
TA260 003:086.732 JLINK_ClrBPEx(BPHandle = 0x0000003C)
TA260 003:086.736 - 0.003ms returns 0x00
TA260 003:086.741 JLINK_ReadReg(R0)
TA260 003:086.744 - 0.004ms returns 0x00000000
TA260 003:087.037 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:087.044   Data:  76 DB E4 BD 88 65 7E BF 2E BC E1 BD B0 70 7E BF ...
TA260 003:087.054   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:089.749 - 2.712ms returns 0x27C
TA260 003:089.766 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:089.770   Data:  39 FD 7F BF 02 ED 2F 3C 39 FC 7F BF 90 0E 49 3C ...
TA260 003:089.780   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:091.640 - 1.874ms returns 0x184
TA260 003:091.649 JLINK_HasError()
TA260 003:091.671 JLINK_WriteReg(R0, 0x0800C400)
TA260 003:091.676 - 0.004ms returns 0
TA260 003:091.710 JLINK_WriteReg(R1, 0x00000400)
TA260 003:091.719 - 0.009ms returns 0
TA260 003:091.724 JLINK_WriteReg(R2, 0x20000184)
TA260 003:091.727 - 0.003ms returns 0
TA260 003:091.732 JLINK_WriteReg(R3, 0x00000000)
TA260 003:091.735 - 0.003ms returns 0
TA260 003:091.739 JLINK_WriteReg(R4, 0x00000000)
TA260 003:091.742 - 0.003ms returns 0
TA260 003:091.746 JLINK_WriteReg(R5, 0x00000000)
TA260 003:091.750 - 0.003ms returns 0
TA260 003:091.754 JLINK_WriteReg(R6, 0x00000000)
TA260 003:091.758 - 0.003ms returns 0
TA260 003:091.761 JLINK_WriteReg(R7, 0x00000000)
TA260 003:091.765 - 0.003ms returns 0
TA260 003:091.769 JLINK_WriteReg(R8, 0x00000000)
TA260 003:091.772 - 0.003ms returns 0
TA260 003:091.776 JLINK_WriteReg(R9, 0x20000180)
TA260 003:091.780 - 0.003ms returns 0
TA260 003:091.784 JLINK_WriteReg(R10, 0x00000000)
TA260 003:091.787 - 0.003ms returns 0
TA260 003:091.791 JLINK_WriteReg(R11, 0x00000000)
TA260 003:091.794 - 0.003ms returns 0
TA260 003:091.799 JLINK_WriteReg(R12, 0x00000000)
TA260 003:091.802 - 0.003ms returns 0
TA260 003:091.806 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:091.810 - 0.003ms returns 0
TA260 003:091.814 JLINK_WriteReg(R14, 0x20000001)
TA260 003:091.818 - 0.003ms returns 0
TA260 003:091.822 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:091.825 - 0.003ms returns 0
TA260 003:091.829 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:091.833 - 0.003ms returns 0
TA260 003:091.837 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:091.840 - 0.003ms returns 0
TA260 003:091.844 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:091.848 - 0.003ms returns 0
TA260 003:091.852 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:091.855 - 0.003ms returns 0
TA260 003:091.860 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:091.864 - 0.004ms returns 0x0000003D
TA260 003:091.868 JLINK_Go()
TA260 003:091.876   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:094.597 - 2.728ms 
TA260 003:094.605 JLINK_IsHalted()
TA260 003:095.120 - 0.515ms returns FALSE
TA260 003:095.127 JLINK_HasError()
TA260 003:096.663 JLINK_IsHalted()
TA260 003:097.181 - 0.518ms returns FALSE
TA260 003:097.194 JLINK_HasError()
TA260 003:098.663 JLINK_IsHalted()
TA260 003:101.040   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:101.508 - 2.844ms returns TRUE
TA260 003:101.514 JLINK_ReadReg(R15 (PC))
TA260 003:101.518 - 0.004ms returns 0x20000000
TA260 003:101.523 JLINK_ClrBPEx(BPHandle = 0x0000003D)
TA260 003:101.529 - 0.006ms returns 0x00
TA260 003:101.533 JLINK_ReadReg(R0)
TA260 003:101.537 - 0.003ms returns 0x00000000
TA260 003:101.846 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:101.853   Data:  2B 95 AC 3D E4 16 7F BF 80 B6 AF 3D 58 0E 7F BF ...
TA260 003:101.862   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:104.472 - 2.626ms returns 0x27C
TA260 003:104.478 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:104.482   Data:  59 9C 7A BF 89 8B 52 3E C1 87 7A BF 01 15 54 3E ...
TA260 003:104.488   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:106.374 - 1.895ms returns 0x184
TA260 003:106.386 JLINK_HasError()
TA260 003:106.391 JLINK_WriteReg(R0, 0x0800C800)
TA260 003:106.397 - 0.006ms returns 0
TA260 003:106.402 JLINK_WriteReg(R1, 0x00000400)
TA260 003:106.405 - 0.003ms returns 0
TA260 003:106.409 JLINK_WriteReg(R2, 0x20000184)
TA260 003:106.413 - 0.003ms returns 0
TA260 003:106.416 JLINK_WriteReg(R3, 0x00000000)
TA260 003:106.420 - 0.003ms returns 0
TA260 003:106.424 JLINK_WriteReg(R4, 0x00000000)
TA260 003:106.427 - 0.003ms returns 0
TA260 003:106.431 JLINK_WriteReg(R5, 0x00000000)
TA260 003:106.435 - 0.003ms returns 0
TA260 003:106.439 JLINK_WriteReg(R6, 0x00000000)
TA260 003:106.442 - 0.003ms returns 0
TA260 003:106.446 JLINK_WriteReg(R7, 0x00000000)
TA260 003:106.449 - 0.003ms returns 0
TA260 003:106.454 JLINK_WriteReg(R8, 0x00000000)
TA260 003:106.457 - 0.003ms returns 0
TA260 003:106.461 JLINK_WriteReg(R9, 0x20000180)
TA260 003:106.464 - 0.003ms returns 0
TA260 003:106.468 JLINK_WriteReg(R10, 0x00000000)
TA260 003:106.472 - 0.003ms returns 0
TA260 003:106.476 JLINK_WriteReg(R11, 0x00000000)
TA260 003:106.479 - 0.003ms returns 0
TA260 003:106.483 JLINK_WriteReg(R12, 0x00000000)
TA260 003:106.487 - 0.003ms returns 0
TA260 003:106.491 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:106.495 - 0.004ms returns 0
TA260 003:106.499 JLINK_WriteReg(R14, 0x20000001)
TA260 003:106.502 - 0.003ms returns 0
TA260 003:106.506 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:106.510 - 0.003ms returns 0
TA260 003:106.514 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:106.517 - 0.003ms returns 0
TA260 003:106.521 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:106.525 - 0.003ms returns 0
TA260 003:106.529 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:106.532 - 0.003ms returns 0
TA260 003:106.536 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:106.540 - 0.003ms returns 0
TA260 003:106.544 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:106.549 - 0.005ms returns 0x0000003E
TA260 003:106.553 JLINK_Go()
TA260 003:106.561   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:109.326 - 2.772ms 
TA260 003:109.339 JLINK_IsHalted()
TA260 003:109.842 - 0.502ms returns FALSE
TA260 003:109.855 JLINK_HasError()
TA260 003:111.669 JLINK_IsHalted()
TA260 003:112.156 - 0.486ms returns FALSE
TA260 003:112.162 JLINK_HasError()
TA260 003:113.670 JLINK_IsHalted()
TA260 003:115.968   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:116.613 - 2.943ms returns TRUE
TA260 003:116.625 JLINK_ReadReg(R15 (PC))
TA260 003:116.630 - 0.005ms returns 0x20000000
TA260 003:116.660 JLINK_ClrBPEx(BPHandle = 0x0000003E)
TA260 003:116.665 - 0.005ms returns 0x00
TA260 003:116.670 JLINK_ReadReg(R0)
TA260 003:116.673 - 0.003ms returns 0x00000000
TA260 003:117.008 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:117.015   Data:  FC D8 8D 3E B3 FA 75 BF 22 9A 8E 3E C6 DE 75 BF ...
TA260 003:117.025   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:119.560 - 2.551ms returns 0x27C
TA260 003:119.572 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:119.576   Data:  FB 99 6B BF 77 00 C9 3E 95 72 6B BF 53 B9 C9 3E ...
TA260 003:119.585   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:121.462 - 1.890ms returns 0x184
TA260 003:121.470 JLINK_HasError()
TA260 003:121.484 JLINK_WriteReg(R0, 0x0800CC00)
TA260 003:121.489 - 0.005ms returns 0
TA260 003:121.493 JLINK_WriteReg(R1, 0x00000400)
TA260 003:121.496 - 0.003ms returns 0
TA260 003:121.501 JLINK_WriteReg(R2, 0x20000184)
TA260 003:121.507 - 0.005ms returns 0
TA260 003:121.511 JLINK_WriteReg(R3, 0x00000000)
TA260 003:121.514 - 0.003ms returns 0
TA260 003:121.518 JLINK_WriteReg(R4, 0x00000000)
TA260 003:121.522 - 0.003ms returns 0
TA260 003:121.526 JLINK_WriteReg(R5, 0x00000000)
TA260 003:121.529 - 0.003ms returns 0
TA260 003:121.533 JLINK_WriteReg(R6, 0x00000000)
TA260 003:121.537 - 0.003ms returns 0
TA260 003:121.541 JLINK_WriteReg(R7, 0x00000000)
TA260 003:121.544 - 0.003ms returns 0
TA260 003:121.548 JLINK_WriteReg(R8, 0x00000000)
TA260 003:121.551 - 0.003ms returns 0
TA260 003:121.555 JLINK_WriteReg(R9, 0x20000180)
TA260 003:121.559 - 0.003ms returns 0
TA260 003:121.563 JLINK_WriteReg(R10, 0x00000000)
TA260 003:121.566 - 0.003ms returns 0
TA260 003:121.570 JLINK_WriteReg(R11, 0x00000000)
TA260 003:121.574 - 0.003ms returns 0
TA260 003:121.578 JLINK_WriteReg(R12, 0x00000000)
TA260 003:121.581 - 0.003ms returns 0
TA260 003:121.585 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:121.589 - 0.003ms returns 0
TA260 003:121.593 JLINK_WriteReg(R14, 0x20000001)
TA260 003:121.596 - 0.003ms returns 0
TA260 003:121.600 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:121.604 - 0.003ms returns 0
TA260 003:121.608 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:121.611 - 0.003ms returns 0
TA260 003:121.616 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:121.619 - 0.003ms returns 0
TA260 003:121.623 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:121.626 - 0.003ms returns 0
TA260 003:121.630 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:121.634 - 0.003ms returns 0
TA260 003:121.638 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:121.642 - 0.004ms returns 0x0000003F
TA260 003:121.646 JLINK_Go()
TA260 003:121.654   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:124.460 - 2.813ms 
TA260 003:124.466 JLINK_IsHalted()
TA260 003:125.011 - 0.545ms returns FALSE
TA260 003:125.020 JLINK_HasError()
TA260 003:126.563 JLINK_IsHalted()
TA260 003:127.352 - 0.788ms returns FALSE
TA260 003:127.367 JLINK_HasError()
TA260 003:128.558 JLINK_IsHalted()
TA260 003:130.860   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:131.360 - 2.801ms returns TRUE
TA260 003:131.366 JLINK_ReadReg(R15 (PC))
TA260 003:131.372 - 0.005ms returns 0x20000000
TA260 003:131.376 JLINK_ClrBPEx(BPHandle = 0x0000003F)
TA260 003:131.380 - 0.003ms returns 0x00
TA260 003:131.384 JLINK_ReadReg(R0)
TA260 003:131.388 - 0.003ms returns 0x00000000
TA260 003:131.730 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:131.736   Data:  30 19 EB 3E 95 6A 63 BF BB CB EB 3E 5A 3C 63 BF ...
TA260 003:131.746   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:134.383 - 2.653ms returns 0x27C
TA260 003:134.389 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:134.392   Data:  C7 89 53 BF DC 80 10 3F 18 51 53 BF CD D3 10 3F ...
TA260 003:134.399   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:136.299 - 1.910ms returns 0x184
TA260 003:136.311 JLINK_HasError()
TA260 003:136.317 JLINK_WriteReg(R0, 0x0800D000)
TA260 003:136.322 - 0.005ms returns 0
TA260 003:136.326 JLINK_WriteReg(R1, 0x00000400)
TA260 003:136.330 - 0.003ms returns 0
TA260 003:136.334 JLINK_WriteReg(R2, 0x20000184)
TA260 003:136.338 - 0.003ms returns 0
TA260 003:136.342 JLINK_WriteReg(R3, 0x00000000)
TA260 003:136.345 - 0.003ms returns 0
TA260 003:136.349 JLINK_WriteReg(R4, 0x00000000)
TA260 003:136.353 - 0.003ms returns 0
TA260 003:136.357 JLINK_WriteReg(R5, 0x00000000)
TA260 003:136.360 - 0.003ms returns 0
TA260 003:136.364 JLINK_WriteReg(R6, 0x00000000)
TA260 003:136.368 - 0.003ms returns 0
TA260 003:136.371 JLINK_WriteReg(R7, 0x00000000)
TA260 003:136.375 - 0.003ms returns 0
TA260 003:136.379 JLINK_WriteReg(R8, 0x00000000)
TA260 003:136.382 - 0.003ms returns 0
TA260 003:136.386 JLINK_WriteReg(R9, 0x20000180)
TA260 003:136.390 - 0.003ms returns 0
TA260 003:136.394 JLINK_WriteReg(R10, 0x00000000)
TA260 003:136.397 - 0.003ms returns 0
TA260 003:136.401 JLINK_WriteReg(R11, 0x00000000)
TA260 003:136.404 - 0.003ms returns 0
TA260 003:136.409 JLINK_WriteReg(R12, 0x00000000)
TA260 003:136.414 - 0.005ms returns 0
TA260 003:136.419 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:136.422 - 0.003ms returns 0
TA260 003:136.426 JLINK_WriteReg(R14, 0x20000001)
TA260 003:136.430 - 0.003ms returns 0
TA260 003:136.434 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:136.438 - 0.003ms returns 0
TA260 003:136.442 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:136.456 - 0.014ms returns 0
TA260 003:136.460 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:136.463 - 0.003ms returns 0
TA260 003:136.467 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:136.471 - 0.003ms returns 0
TA260 003:136.475 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:136.478 - 0.003ms returns 0
TA260 003:136.483 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:136.487 - 0.004ms returns 0x00000040
TA260 003:136.491 JLINK_Go()
TA260 003:136.500   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:139.234 - 2.742ms 
TA260 003:139.245 JLINK_IsHalted()
TA260 003:139.734 - 0.489ms returns FALSE
TA260 003:139.740 JLINK_HasError()
TA260 003:141.064 JLINK_IsHalted()
TA260 003:141.508 - 0.443ms returns FALSE
TA260 003:141.514 JLINK_HasError()
TA260 003:143.061 JLINK_IsHalted()
TA260 003:143.497 - 0.435ms returns FALSE
TA260 003:143.503 JLINK_HasError()
TA260 003:145.064 JLINK_IsHalted()
TA260 003:147.477   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:147.971 - 2.907ms returns TRUE
TA260 003:147.978 JLINK_ReadReg(R15 (PC))
TA260 003:147.983 - 0.004ms returns 0x20000000
TA260 003:147.987 JLINK_ClrBPEx(BPHandle = 0x00000040)
TA260 003:147.991 - 0.003ms returns 0x00
TA260 003:147.995 JLINK_ReadReg(R0)
TA260 003:147.999 - 0.003ms returns 0x00000000
TA260 003:148.340 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:148.348   Data:  41 A8 1F 3F 27 1D 48 BF CB F6 1F 3F 65 DE 47 BF ...
TA260 003:148.358   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:150.972 - 2.631ms returns 0x27C
TA260 003:150.978 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:150.982   Data:  7A 58 33 BF DF F3 36 3F AF 10 33 BF 23 3A 37 3F ...
TA260 003:150.988   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:152.869 - 1.890ms returns 0x184
TA260 003:152.875 JLINK_HasError()
TA260 003:152.880 JLINK_WriteReg(R0, 0x0800D400)
TA260 003:152.885 - 0.004ms returns 0
TA260 003:152.889 JLINK_WriteReg(R1, 0x00000400)
TA260 003:152.893 - 0.003ms returns 0
TA260 003:152.897 JLINK_WriteReg(R2, 0x20000184)
TA260 003:152.900 - 0.003ms returns 0
TA260 003:152.904 JLINK_WriteReg(R3, 0x00000000)
TA260 003:152.907 - 0.003ms returns 0
TA260 003:152.912 JLINK_WriteReg(R4, 0x00000000)
TA260 003:152.915 - 0.003ms returns 0
TA260 003:152.919 JLINK_WriteReg(R5, 0x00000000)
TA260 003:152.922 - 0.003ms returns 0
TA260 003:152.926 JLINK_WriteReg(R6, 0x00000000)
TA260 003:152.930 - 0.003ms returns 0
TA260 003:152.934 JLINK_WriteReg(R7, 0x00000000)
TA260 003:152.937 - 0.003ms returns 0
TA260 003:152.942 JLINK_WriteReg(R8, 0x00000000)
TA260 003:152.945 - 0.003ms returns 0
TA260 003:152.949 JLINK_WriteReg(R9, 0x20000180)
TA260 003:152.952 - 0.003ms returns 0
TA260 003:152.956 JLINK_WriteReg(R10, 0x00000000)
TA260 003:152.959 - 0.003ms returns 0
TA260 003:152.964 JLINK_WriteReg(R11, 0x00000000)
TA260 003:152.967 - 0.003ms returns 0
TA260 003:152.971 JLINK_WriteReg(R12, 0x00000000)
TA260 003:152.974 - 0.003ms returns 0
TA260 003:152.978 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:152.982 - 0.003ms returns 0
TA260 003:152.986 JLINK_WriteReg(R14, 0x20000001)
TA260 003:152.990 - 0.003ms returns 0
TA260 003:152.994 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:152.997 - 0.003ms returns 0
TA260 003:153.001 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:153.005 - 0.003ms returns 0
TA260 003:153.009 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:153.012 - 0.003ms returns 0
TA260 003:153.016 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:153.020 - 0.003ms returns 0
TA260 003:153.024 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:153.027 - 0.003ms returns 0
TA260 003:153.032 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:153.036 - 0.004ms returns 0x00000041
TA260 003:153.042 JLINK_Go()
TA260 003:153.050   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:155.859 - 2.816ms 
TA260 003:155.872 JLINK_IsHalted()
TA260 003:156.372 - 0.499ms returns FALSE
TA260 003:156.381 JLINK_HasError()
TA260 003:157.936 JLINK_IsHalted()
TA260 003:158.474 - 0.538ms returns FALSE
TA260 003:158.487 JLINK_HasError()
TA260 003:160.438 JLINK_IsHalted()
TA260 003:162.727   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:163.213 - 2.775ms returns TRUE
TA260 003:163.220 JLINK_ReadReg(R15 (PC))
TA260 003:163.225 - 0.005ms returns 0x20000000
TA260 003:163.230 JLINK_ClrBPEx(BPHandle = 0x00000041)
TA260 003:163.233 - 0.003ms returns 0x00
TA260 003:163.238 JLINK_ReadReg(R0)
TA260 003:163.241 - 0.003ms returns 0x00000000
TA260 003:163.582 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:163.589   Data:  38 A1 43 3F 04 1F 25 BF 00 E2 43 3F 25 D2 24 BF ...
TA260 003:163.600   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:166.195 - 2.613ms returns 0x27C
TA260 003:166.206 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:166.210   Data:  C9 42 0C BF 02 5F 56 3F A5 EE 0B BF E5 95 56 3F ...
TA260 003:166.220   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:168.113 - 1.907ms returns 0x184
TA260 003:168.131 JLINK_HasError()
TA260 003:168.164 JLINK_WriteReg(R0, 0x0800D800)
TA260 003:168.178 - 0.014ms returns 0
TA260 003:168.183 JLINK_WriteReg(R1, 0x00000400)
TA260 003:168.187 - 0.003ms returns 0
TA260 003:168.191 JLINK_WriteReg(R2, 0x20000184)
TA260 003:168.194 - 0.003ms returns 0
TA260 003:168.198 JLINK_WriteReg(R3, 0x00000000)
TA260 003:168.202 - 0.003ms returns 0
TA260 003:168.206 JLINK_WriteReg(R4, 0x00000000)
TA260 003:168.209 - 0.003ms returns 0
TA260 003:168.213 JLINK_WriteReg(R5, 0x00000000)
TA260 003:168.216 - 0.003ms returns 0
TA260 003:168.220 JLINK_WriteReg(R6, 0x00000000)
TA260 003:168.224 - 0.003ms returns 0
TA260 003:168.228 JLINK_WriteReg(R7, 0x00000000)
TA260 003:168.231 - 0.003ms returns 0
TA260 003:168.235 JLINK_WriteReg(R8, 0x00000000)
TA260 003:168.239 - 0.003ms returns 0
TA260 003:168.243 JLINK_WriteReg(R9, 0x20000180)
TA260 003:168.246 - 0.003ms returns 0
TA260 003:168.250 JLINK_WriteReg(R10, 0x00000000)
TA260 003:168.254 - 0.003ms returns 0
TA260 003:168.258 JLINK_WriteReg(R11, 0x00000000)
TA260 003:168.261 - 0.003ms returns 0
TA260 003:168.265 JLINK_WriteReg(R12, 0x00000000)
TA260 003:168.268 - 0.003ms returns 0
TA260 003:168.273 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:168.276 - 0.003ms returns 0
TA260 003:168.280 JLINK_WriteReg(R14, 0x20000001)
TA260 003:168.284 - 0.003ms returns 0
TA260 003:168.288 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:168.292 - 0.003ms returns 0
TA260 003:168.296 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:168.299 - 0.003ms returns 0
TA260 003:168.303 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:168.306 - 0.003ms returns 0
TA260 003:168.310 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:168.314 - 0.003ms returns 0
TA260 003:168.318 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:168.321 - 0.003ms returns 0
TA260 003:168.326 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:168.330 - 0.004ms returns 0x00000042
TA260 003:168.335 JLINK_Go()
TA260 003:168.343   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:171.100 - 2.765ms 
TA260 003:171.110 JLINK_IsHalted()
TA260 003:171.609 - 0.499ms returns FALSE
TA260 003:171.615 JLINK_HasError()
TA260 003:172.941 JLINK_IsHalted()
TA260 003:173.461 - 0.520ms returns FALSE
TA260 003:173.466 JLINK_HasError()
TA260 003:174.946 JLINK_IsHalted()
TA260 003:175.461 - 0.515ms returns FALSE
TA260 003:175.468 JLINK_HasError()
TA260 003:177.448 JLINK_IsHalted()
TA260 003:179.770   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:180.245 - 2.797ms returns TRUE
TA260 003:180.252 JLINK_ReadReg(R15 (PC))
TA260 003:180.256 - 0.004ms returns 0x20000000
TA260 003:180.260 JLINK_ClrBPEx(BPHandle = 0x00000042)
TA260 003:180.264 - 0.003ms returns 0x00
TA260 003:180.269 JLINK_ReadReg(R0)
TA260 003:180.272 - 0.003ms returns 0x00000000
TA260 003:180.599 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:180.609   Data:  96 15 60 3F DC 90 F7 BE 21 46 60 3F CB E0 F6 BE ...
TA260 003:180.619   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:183.155 - 2.555ms returns 0x27C
TA260 003:183.161 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:183.165   Data:  6F 92 BF BE 2D 8D 6D 3F EB D7 BE BE 93 B2 6D 3F ...
TA260 003:183.172   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:185.018 - 1.856ms returns 0x184
TA260 003:185.024 JLINK_HasError()
TA260 003:185.029 JLINK_WriteReg(R0, 0x0800DC00)
TA260 003:185.034 - 0.004ms returns 0
TA260 003:185.038 JLINK_WriteReg(R1, 0x00000400)
TA260 003:185.041 - 0.003ms returns 0
TA260 003:185.046 JLINK_WriteReg(R2, 0x20000184)
TA260 003:185.049 - 0.003ms returns 0
TA260 003:185.053 JLINK_WriteReg(R3, 0x00000000)
TA260 003:185.056 - 0.003ms returns 0
TA260 003:185.061 JLINK_WriteReg(R4, 0x00000000)
TA260 003:185.064 - 0.003ms returns 0
TA260 003:185.068 JLINK_WriteReg(R5, 0x00000000)
TA260 003:185.072 - 0.003ms returns 0
TA260 003:185.076 JLINK_WriteReg(R6, 0x00000000)
TA260 003:185.080 - 0.003ms returns 0
TA260 003:185.084 JLINK_WriteReg(R7, 0x00000000)
TA260 003:185.087 - 0.003ms returns 0
TA260 003:185.091 JLINK_WriteReg(R8, 0x00000000)
TA260 003:185.095 - 0.003ms returns 0
TA260 003:185.099 JLINK_WriteReg(R9, 0x20000180)
TA260 003:185.102 - 0.003ms returns 0
TA260 003:185.106 JLINK_WriteReg(R10, 0x00000000)
TA260 003:185.110 - 0.003ms returns 0
TA260 003:185.114 JLINK_WriteReg(R11, 0x00000000)
TA260 003:185.117 - 0.003ms returns 0
TA260 003:185.121 JLINK_WriteReg(R12, 0x00000000)
TA260 003:185.124 - 0.003ms returns 0
TA260 003:185.128 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:185.132 - 0.003ms returns 0
TA260 003:185.136 JLINK_WriteReg(R14, 0x20000001)
TA260 003:185.140 - 0.003ms returns 0
TA260 003:185.144 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:185.147 - 0.003ms returns 0
TA260 003:185.151 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:185.155 - 0.003ms returns 0
TA260 003:185.159 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:185.162 - 0.003ms returns 0
TA260 003:185.166 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:185.170 - 0.003ms returns 0
TA260 003:185.174 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:185.177 - 0.003ms returns 0
TA260 003:185.181 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:185.185 - 0.004ms returns 0x00000043
TA260 003:185.189 JLINK_Go()
TA260 003:185.196   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:188.091 - 2.900ms 
TA260 003:188.116 JLINK_IsHalted()
TA260 003:188.613 - 0.496ms returns FALSE
TA260 003:188.619 JLINK_HasError()
TA260 003:190.551 JLINK_IsHalted()
TA260 003:190.989 - 0.437ms returns FALSE
TA260 003:190.995 JLINK_HasError()
TA260 003:192.551 JLINK_IsHalted()
TA260 003:194.841   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:195.307 - 2.755ms returns TRUE
TA260 003:195.321 JLINK_ReadReg(R15 (PC))
TA260 003:195.327 - 0.006ms returns 0x20000000
TA260 003:195.371 JLINK_ClrBPEx(BPHandle = 0x00000043)
TA260 003:195.386 - 0.015ms returns 0x00
TA260 003:195.392 JLINK_ReadReg(R0)
TA260 003:195.396 - 0.004ms returns 0x00000000
TA260 003:195.955 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:195.964   Data:  6E ED 73 3F 27 60 9B BE DD 0B 74 3F 86 A0 9A BE ...
TA260 003:195.976   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:198.574 - 2.618ms returns 0x27C
TA260 003:198.597 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:198.602   Data:  3E 85 3E BE 53 9A 7B 3F 15 FA 3C BE CD AC 7B 3F ...
TA260 003:198.615   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:200.514 - 1.916ms returns 0x184
TA260 003:200.524 JLINK_HasError()
TA260 003:200.533 JLINK_WriteReg(R0, 0x0800E000)
TA260 003:200.540 - 0.007ms returns 0
TA260 003:200.545 JLINK_WriteReg(R1, 0x00000400)
TA260 003:200.549 - 0.003ms returns 0
TA260 003:200.553 JLINK_WriteReg(R2, 0x20000184)
TA260 003:200.556 - 0.003ms returns 0
TA260 003:200.561 JLINK_WriteReg(R3, 0x00000000)
TA260 003:200.564 - 0.003ms returns 0
TA260 003:200.568 JLINK_WriteReg(R4, 0x00000000)
TA260 003:200.574 - 0.005ms returns 0
TA260 003:200.578 JLINK_WriteReg(R5, 0x00000000)
TA260 003:200.582 - 0.003ms returns 0
TA260 003:200.586 JLINK_WriteReg(R6, 0x00000000)
TA260 003:200.589 - 0.003ms returns 0
TA260 003:200.593 JLINK_WriteReg(R7, 0x00000000)
TA260 003:200.597 - 0.003ms returns 0
TA260 003:200.601 JLINK_WriteReg(R8, 0x00000000)
TA260 003:200.604 - 0.003ms returns 0
TA260 003:200.608 JLINK_WriteReg(R9, 0x20000180)
TA260 003:200.612 - 0.003ms returns 0
TA260 003:200.616 JLINK_WriteReg(R10, 0x00000000)
TA260 003:200.619 - 0.003ms returns 0
TA260 003:200.623 JLINK_WriteReg(R11, 0x00000000)
TA260 003:200.627 - 0.003ms returns 0
TA260 003:200.631 JLINK_WriteReg(R12, 0x00000000)
TA260 003:200.634 - 0.003ms returns 0
TA260 003:200.638 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:200.642 - 0.004ms returns 0
TA260 003:200.646 JLINK_WriteReg(R14, 0x20000001)
TA260 003:200.650 - 0.003ms returns 0
TA260 003:200.654 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:200.657 - 0.003ms returns 0
TA260 003:200.662 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:200.666 - 0.003ms returns 0
TA260 003:200.670 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:200.673 - 0.003ms returns 0
TA260 003:200.677 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:200.681 - 0.003ms returns 0
TA260 003:200.685 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:200.688 - 0.003ms returns 0
TA260 003:200.693 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:200.698 - 0.005ms returns 0x00000044
TA260 003:200.702 JLINK_Go()
TA260 003:200.711   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:203.478 - 2.775ms 
TA260 003:203.494 JLINK_IsHalted()
TA260 003:203.988 - 0.493ms returns FALSE
TA260 003:203.994 JLINK_HasError()
TA260 003:205.763 JLINK_IsHalted()
TA260 003:206.304 - 0.540ms returns FALSE
TA260 003:206.320 JLINK_HasError()
TA260 003:207.764 JLINK_IsHalted()
TA260 003:210.166   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:210.706 - 2.941ms returns TRUE
TA260 003:210.717 JLINK_ReadReg(R15 (PC))
TA260 003:210.722 - 0.005ms returns 0x20000000
TA260 003:210.726 JLINK_ClrBPEx(BPHandle = 0x00000044)
TA260 003:210.730 - 0.003ms returns 0x00
TA260 003:210.734 JLINK_ReadReg(R0)
TA260 003:210.738 - 0.003ms returns 0x00000000
TA260 003:211.228 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:211.238   Data:  88 65 7E 3F 76 DB E4 BD B0 70 7E 3F 2E BC E1 BD ...
TA260 003:211.249   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:213.884 - 2.654ms returns 0x27C
TA260 003:213.897 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:213.901   Data:  00 00 59 40 00 00 A0 40 9A 99 99 40 D7 A3 90 40 ...
TA260 003:213.911   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:215.862 - 1.964ms returns 0x184
TA260 003:215.886 JLINK_HasError()
TA260 003:215.892 JLINK_WriteReg(R0, 0x0800E400)
TA260 003:215.899 - 0.007ms returns 0
TA260 003:215.904 JLINK_WriteReg(R1, 0x00000318)
TA260 003:215.907 - 0.003ms returns 0
TA260 003:215.911 JLINK_WriteReg(R2, 0x20000184)
TA260 003:215.915 - 0.003ms returns 0
TA260 003:215.919 JLINK_WriteReg(R3, 0x00000000)
TA260 003:215.922 - 0.003ms returns 0
TA260 003:215.926 JLINK_WriteReg(R4, 0x00000000)
TA260 003:215.930 - 0.003ms returns 0
TA260 003:215.934 JLINK_WriteReg(R5, 0x00000000)
TA260 003:215.937 - 0.003ms returns 0
TA260 003:215.942 JLINK_WriteReg(R6, 0x00000000)
TA260 003:215.945 - 0.003ms returns 0
TA260 003:215.949 JLINK_WriteReg(R7, 0x00000000)
TA260 003:215.952 - 0.003ms returns 0
TA260 003:215.956 JLINK_WriteReg(R8, 0x00000000)
TA260 003:215.960 - 0.003ms returns 0
TA260 003:215.964 JLINK_WriteReg(R9, 0x20000180)
TA260 003:215.967 - 0.003ms returns 0
TA260 003:215.971 JLINK_WriteReg(R10, 0x00000000)
TA260 003:215.975 - 0.003ms returns 0
TA260 003:215.979 JLINK_WriteReg(R11, 0x00000000)
TA260 003:215.982 - 0.003ms returns 0
TA260 003:215.986 JLINK_WriteReg(R12, 0x00000000)
TA260 003:215.990 - 0.003ms returns 0
TA260 003:215.994 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:215.998 - 0.004ms returns 0
TA260 003:216.002 JLINK_WriteReg(R14, 0x20000001)
TA260 003:216.010 - 0.007ms returns 0
TA260 003:216.014 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:216.018 - 0.003ms returns 0
TA260 003:216.022 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:216.026 - 0.003ms returns 0
TA260 003:216.030 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:216.033 - 0.003ms returns 0
TA260 003:216.037 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:216.041 - 0.003ms returns 0
TA260 003:216.045 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:216.048 - 0.003ms returns 0
TA260 003:216.053 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:216.057 - 0.004ms returns 0x00000045
TA260 003:216.062 JLINK_Go()
TA260 003:216.071   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:218.943 - 2.881ms 
TA260 003:218.966 JLINK_IsHalted()
TA260 003:219.466 - 0.499ms returns FALSE
TA260 003:219.473 JLINK_HasError()
TA260 003:221.772 JLINK_IsHalted()
TA260 003:222.236 - 0.464ms returns FALSE
TA260 003:222.244 JLINK_HasError()
TA260 003:223.768 JLINK_IsHalted()
TA260 003:226.158   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:226.617 - 2.848ms returns TRUE
TA260 003:226.627 JLINK_ReadReg(R15 (PC))
TA260 003:226.633 - 0.005ms returns 0x20000000
TA260 003:226.638 JLINK_ClrBPEx(BPHandle = 0x00000045)
TA260 003:226.641 - 0.003ms returns 0x00
TA260 003:226.646 JLINK_ReadReg(R0)
TA260 003:226.649 - 0.003ms returns 0x00000000
TA260 003:226.654 JLINK_HasError()
TA260 003:226.660 JLINK_WriteReg(R0, 0x00000002)
TA260 003:226.664 - 0.004ms returns 0
TA260 003:226.668 JLINK_WriteReg(R1, 0x00000318)
TA260 003:226.672 - 0.003ms returns 0
TA260 003:226.676 JLINK_WriteReg(R2, 0x20000184)
TA260 003:226.679 - 0.003ms returns 0
TA260 003:226.683 JLINK_WriteReg(R3, 0x00000000)
TA260 003:226.687 - 0.003ms returns 0
TA260 003:226.691 JLINK_WriteReg(R4, 0x00000000)
TA260 003:226.694 - 0.003ms returns 0
TA260 003:226.698 JLINK_WriteReg(R5, 0x00000000)
TA260 003:226.701 - 0.003ms returns 0
TA260 003:226.705 JLINK_WriteReg(R6, 0x00000000)
TA260 003:226.709 - 0.003ms returns 0
TA260 003:226.713 JLINK_WriteReg(R7, 0x00000000)
TA260 003:226.716 - 0.003ms returns 0
TA260 003:226.720 JLINK_WriteReg(R8, 0x00000000)
TA260 003:226.765 - 0.044ms returns 0
TA260 003:226.770 JLINK_WriteReg(R9, 0x20000180)
TA260 003:226.773 - 0.003ms returns 0
TA260 003:226.777 JLINK_WriteReg(R10, 0x00000000)
TA260 003:226.781 - 0.003ms returns 0
TA260 003:226.785 JLINK_WriteReg(R11, 0x00000000)
TA260 003:226.788 - 0.003ms returns 0
TA260 003:226.792 JLINK_WriteReg(R12, 0x00000000)
TA260 003:226.796 - 0.003ms returns 0
TA260 003:226.800 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:226.804 - 0.003ms returns 0
TA260 003:226.808 JLINK_WriteReg(R14, 0x20000001)
TA260 003:226.811 - 0.003ms returns 0
TA260 003:226.815 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 003:226.818 - 0.003ms returns 0
TA260 003:226.839 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:226.843 - 0.004ms returns 0
TA260 003:226.847 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:226.880 - 0.032ms returns 0
TA260 003:226.884 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:226.887 - 0.003ms returns 0
TA260 003:226.891 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:226.895 - 0.003ms returns 0
TA260 003:226.899 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:226.904 - 0.004ms returns 0x00000046
TA260 003:226.908 JLINK_Go()
TA260 003:226.916   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:229.704 - 2.796ms 
TA260 003:229.715 JLINK_IsHalted()
TA260 003:232.008   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:232.495 - 2.779ms returns TRUE
TA260 003:232.505 JLINK_ReadReg(R15 (PC))
TA260 003:232.511 - 0.006ms returns 0x20000000
TA260 003:232.516 JLINK_ClrBPEx(BPHandle = 0x00000046)
TA260 003:232.519 - 0.003ms returns 0x00
TA260 003:232.524 JLINK_ReadReg(R0)
TA260 003:232.527 - 0.003ms returns 0x00000000
TA260 003:288.662 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 003:288.678   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 003:288.695   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 003:290.581 - 1.918ms returns 0x184
TA260 003:290.625 JLINK_HasError()
TA260 003:290.631 JLINK_WriteReg(R0, 0x08000000)
TA260 003:290.637 - 0.006ms returns 0
TA260 003:290.642 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 003:290.647 - 0.004ms returns 0
TA260 003:290.651 JLINK_WriteReg(R2, 0x00000003)
TA260 003:290.655 - 0.003ms returns 0
TA260 003:290.659 JLINK_WriteReg(R3, 0x00000000)
TA260 003:290.662 - 0.003ms returns 0
TA260 003:290.667 JLINK_WriteReg(R4, 0x00000000)
TA260 003:290.670 - 0.003ms returns 0
TA260 003:290.674 JLINK_WriteReg(R5, 0x00000000)
TA260 003:290.677 - 0.003ms returns 0
TA260 003:290.681 JLINK_WriteReg(R6, 0x00000000)
TA260 003:290.685 - 0.003ms returns 0
TA260 003:290.689 JLINK_WriteReg(R7, 0x00000000)
TA260 003:290.692 - 0.003ms returns 0
TA260 003:290.696 JLINK_WriteReg(R8, 0x00000000)
TA260 003:290.700 - 0.003ms returns 0
TA260 003:290.704 JLINK_WriteReg(R9, 0x20000180)
TA260 003:290.707 - 0.003ms returns 0
TA260 003:290.711 JLINK_WriteReg(R10, 0x00000000)
TA260 003:290.714 - 0.003ms returns 0
TA260 003:290.718 JLINK_WriteReg(R11, 0x00000000)
TA260 003:290.722 - 0.003ms returns 0
TA260 003:290.726 JLINK_WriteReg(R12, 0x00000000)
TA260 003:290.729 - 0.003ms returns 0
TA260 003:290.734 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:290.737 - 0.004ms returns 0
TA260 003:290.742 JLINK_WriteReg(R14, 0x20000001)
TA260 003:290.745 - 0.003ms returns 0
TA260 003:290.750 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 003:290.753 - 0.003ms returns 0
TA260 003:290.757 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:290.761 - 0.003ms returns 0
TA260 003:290.765 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:290.768 - 0.003ms returns 0
TA260 003:290.772 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:290.776 - 0.003ms returns 0
TA260 003:290.780 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:290.783 - 0.003ms returns 0
TA260 003:290.788 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:290.795   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:291.272 - 0.484ms returns 0x00000047
TA260 003:291.278 JLINK_Go()
TA260 003:291.293   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 003:291.822   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:294.569 - 3.289ms 
TA260 003:294.594 JLINK_IsHalted()
TA260 003:296.891   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:297.478 - 2.884ms returns TRUE
TA260 003:297.532 JLINK_ReadReg(R15 (PC))
TA260 003:297.541 - 0.008ms returns 0x20000000
TA260 003:297.546 JLINK_ClrBPEx(BPHandle = 0x00000047)
TA260 003:297.550 - 0.004ms returns 0x00
TA260 003:297.555 JLINK_ReadReg(R0)
TA260 003:297.558 - 0.004ms returns 0x00000000
TA260 003:297.564 JLINK_HasError()
TA260 003:297.569 JLINK_WriteReg(R0, 0xFFFFFFFF)
TA260 003:297.573 - 0.003ms returns 0
TA260 003:297.577 JLINK_WriteReg(R1, 0x08000000)
TA260 003:297.581 - 0.004ms returns 0
TA260 003:297.585 JLINK_WriteReg(R2, 0x0000E718)
TA260 003:297.588 - 0.003ms returns 0
TA260 003:297.592 JLINK_WriteReg(R3, 0x04C11DB7)
TA260 003:297.596 - 0.003ms returns 0
TA260 003:297.600 JLINK_WriteReg(R4, 0x00000000)
TA260 003:297.603 - 0.003ms returns 0
TA260 003:297.607 JLINK_WriteReg(R5, 0x00000000)
TA260 003:297.611 - 0.003ms returns 0
TA260 003:297.615 JLINK_WriteReg(R6, 0x00000000)
TA260 003:297.618 - 0.003ms returns 0
TA260 003:297.622 JLINK_WriteReg(R7, 0x00000000)
TA260 003:297.626 - 0.003ms returns 0
TA260 003:297.630 JLINK_WriteReg(R8, 0x00000000)
TA260 003:297.633 - 0.003ms returns 0
TA260 003:297.637 JLINK_WriteReg(R9, 0x20000180)
TA260 003:297.640 - 0.003ms returns 0
TA260 003:297.644 JLINK_WriteReg(R10, 0x00000000)
TA260 003:297.648 - 0.003ms returns 0
TA260 003:297.652 JLINK_WriteReg(R11, 0x00000000)
TA260 003:297.655 - 0.003ms returns 0
TA260 003:297.660 JLINK_WriteReg(R12, 0x00000000)
TA260 003:297.663 - 0.003ms returns 0
TA260 003:297.667 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:297.671 - 0.003ms returns 0
TA260 003:297.675 JLINK_WriteReg(R14, 0x20000001)
TA260 003:297.678 - 0.003ms returns 0
TA260 003:297.682 JLINK_WriteReg(R15 (PC), 0x20000002)
TA260 003:297.686 - 0.003ms returns 0
TA260 003:297.690 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:297.696 - 0.006ms returns 0
TA260 003:297.700 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:297.703 - 0.003ms returns 0
TA260 003:297.707 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:297.711 - 0.003ms returns 0
TA260 003:297.715 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:297.718 - 0.003ms returns 0
TA260 003:297.723 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:297.727 - 0.004ms returns 0x00000048
TA260 003:297.731 JLINK_Go()
TA260 003:297.739   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:300.521 - 2.789ms 
TA260 003:300.546 JLINK_IsHalted()
TA260 003:301.095 - 0.548ms returns FALSE
TA260 003:301.109 JLINK_HasError()
TA260 003:303.436 JLINK_IsHalted()
TA260 003:303.884 - 0.447ms returns FALSE
TA260 003:303.898 JLINK_HasError()
TA260 003:305.935 JLINK_IsHalted()
TA260 003:306.466 - 0.530ms returns FALSE
TA260 003:306.514 JLINK_HasError()
TA260 003:307.933 JLINK_IsHalted()
TA260 003:308.503 - 0.569ms returns FALSE
TA260 003:308.525 JLINK_HasError()
TA260 003:309.568 JLINK_IsHalted()
TA260 003:310.046 - 0.477ms returns FALSE
TA260 003:310.056 JLINK_HasError()
TA260 003:311.570 JLINK_IsHalted()
TA260 003:312.033 - 0.463ms returns FALSE
TA260 003:312.041 JLINK_HasError()
TA260 003:313.583 JLINK_IsHalted()
TA260 003:314.041 - 0.457ms returns FALSE
TA260 003:314.052 JLINK_HasError()
TA260 003:316.076 JLINK_IsHalted()
TA260 003:316.561 - 0.485ms returns FALSE
TA260 003:316.575 JLINK_HasError()
TA260 003:318.075 JLINK_IsHalted()
TA260 003:318.511 - 0.435ms returns FALSE
TA260 003:318.517 JLINK_HasError()
TA260 003:320.078 JLINK_IsHalted()
TA260 003:320.519 - 0.441ms returns FALSE
TA260 003:320.530 JLINK_HasError()
TA260 003:322.073 JLINK_IsHalted()
TA260 003:322.600 - 0.527ms returns FALSE
TA260 003:322.607 JLINK_HasError()
TA260 003:324.075 JLINK_IsHalted()
TA260 003:324.615 - 0.539ms returns FALSE
TA260 003:324.630 JLINK_HasError()
TA260 003:326.590 JLINK_IsHalted()
TA260 003:327.098 - 0.507ms returns FALSE
TA260 003:327.114 JLINK_HasError()
TA260 003:328.586 JLINK_IsHalted()
TA260 003:329.102 - 0.515ms returns FALSE
TA260 003:329.111 JLINK_HasError()
TA260 003:330.589 JLINK_IsHalted()
TA260 003:331.040 - 0.450ms returns FALSE
TA260 003:331.050 JLINK_HasError()
TA260 003:332.588 JLINK_IsHalted()
TA260 003:333.044 - 0.457ms returns FALSE
TA260 003:333.052 JLINK_HasError()
TA260 003:335.093 JLINK_IsHalted()
TA260 003:335.598 - 0.505ms returns FALSE
TA260 003:335.605 JLINK_HasError()
TA260 003:337.098 JLINK_IsHalted()
TA260 003:337.617 - 0.519ms returns FALSE
TA260 003:337.624 JLINK_HasError()
TA260 003:339.090 JLINK_IsHalted()
TA260 003:339.613 - 0.523ms returns FALSE
TA260 003:339.621 JLINK_HasError()
TA260 003:340.703 JLINK_IsHalted()
TA260 003:341.163 - 0.460ms returns FALSE
TA260 003:341.171 JLINK_HasError()
TA260 003:342.703 JLINK_IsHalted()
TA260 003:343.226 - 0.522ms returns FALSE
TA260 003:343.234 JLINK_HasError()
TA260 003:344.699 JLINK_IsHalted()
TA260 003:345.141 - 0.441ms returns FALSE
TA260 003:345.148 JLINK_HasError()
TA260 003:346.209 JLINK_IsHalted()
TA260 003:346.693 - 0.484ms returns FALSE
TA260 003:346.701 JLINK_HasError()
TA260 003:348.215 JLINK_IsHalted()
TA260 003:348.697 - 0.481ms returns FALSE
TA260 003:348.746 JLINK_HasError()
TA260 003:349.800 JLINK_IsHalted()
TA260 003:350.292 - 0.491ms returns FALSE
TA260 003:350.300 JLINK_HasError()
TA260 003:352.307 JLINK_IsHalted()
TA260 003:352.842 - 0.535ms returns FALSE
TA260 003:352.852 JLINK_HasError()
TA260 003:354.810 JLINK_IsHalted()
TA260 003:355.330 - 0.519ms returns FALSE
TA260 003:355.341 JLINK_HasError()
TA260 003:356.812 JLINK_IsHalted()
TA260 003:357.288 - 0.475ms returns FALSE
TA260 003:357.304 JLINK_HasError()
TA260 003:358.811 JLINK_IsHalted()
TA260 003:359.309 - 0.497ms returns FALSE
TA260 003:359.318 JLINK_HasError()
TA260 003:360.817 JLINK_IsHalted()
TA260 003:361.282 - 0.464ms returns FALSE
TA260 003:361.296 JLINK_HasError()
TA260 003:362.813 JLINK_IsHalted()
TA260 003:363.292 - 0.477ms returns FALSE
TA260 003:363.307 JLINK_HasError()
TA260 003:364.814 JLINK_IsHalted()
TA260 003:365.284 - 0.469ms returns FALSE
TA260 003:365.290 JLINK_HasError()
TA260 003:367.316 JLINK_IsHalted()
TA260 003:367.839 - 0.523ms returns FALSE
TA260 003:367.848 JLINK_HasError()
TA260 003:369.324 JLINK_IsHalted()
TA260 003:369.852 - 0.528ms returns FALSE
TA260 003:369.859 JLINK_HasError()
TA260 003:371.320 JLINK_IsHalted()
TA260 003:371.844 - 0.524ms returns FALSE
TA260 003:371.856 JLINK_HasError()
TA260 003:373.324 JLINK_IsHalted()
TA260 003:373.804 - 0.480ms returns FALSE
TA260 003:373.820 JLINK_HasError()
TA260 003:375.826 JLINK_IsHalted()
TA260 003:376.387 - 0.561ms returns FALSE
TA260 003:376.397 JLINK_HasError()
TA260 003:377.828 JLINK_IsHalted()
TA260 003:378.306 - 0.478ms returns FALSE
TA260 003:378.313 JLINK_HasError()
TA260 003:379.829 JLINK_IsHalted()
TA260 003:380.294 - 0.464ms returns FALSE
TA260 003:380.305 JLINK_HasError()
TA260 003:382.048 JLINK_IsHalted()
TA260 003:382.556 - 0.508ms returns FALSE
TA260 003:382.565 JLINK_HasError()
TA260 003:384.052 JLINK_IsHalted()
TA260 003:384.542 - 0.490ms returns FALSE
TA260 003:384.551 JLINK_HasError()
TA260 003:386.558 JLINK_IsHalted()
TA260 003:387.030 - 0.471ms returns FALSE
TA260 003:387.040 JLINK_HasError()
TA260 003:388.554 JLINK_IsHalted()
TA260 003:389.057 - 0.503ms returns FALSE
TA260 003:389.064 JLINK_HasError()
TA260 003:390.558 JLINK_IsHalted()
TA260 003:391.068 - 0.510ms returns FALSE
TA260 003:391.077 JLINK_HasError()
TA260 003:392.563 JLINK_IsHalted()
TA260 003:393.054 - 0.491ms returns FALSE
TA260 003:393.064 JLINK_HasError()
TA260 003:395.062 JLINK_IsHalted()
TA260 003:395.611 - 0.548ms returns FALSE
TA260 003:395.623 JLINK_HasError()
TA260 003:397.072 JLINK_IsHalted()
TA260 003:397.613 - 0.542ms returns FALSE
TA260 003:397.623 JLINK_HasError()
TA260 003:399.068 JLINK_IsHalted()
TA260 003:399.510 - 0.442ms returns FALSE
TA260 003:399.525 JLINK_HasError()
TA260 003:401.071 JLINK_IsHalted()
TA260 003:401.560 - 0.489ms returns FALSE
TA260 003:401.568 JLINK_HasError()
TA260 003:403.066 JLINK_IsHalted()
TA260 003:403.548 - 0.482ms returns FALSE
TA260 003:403.556 JLINK_HasError()
TA260 003:405.074 JLINK_IsHalted()
TA260 003:405.639 - 0.564ms returns FALSE
TA260 003:405.647 JLINK_HasError()
TA260 003:408.080 JLINK_IsHalted()
TA260 003:408.542 - 0.461ms returns FALSE
TA260 003:408.560 JLINK_HasError()
TA260 003:410.082 JLINK_IsHalted()
TA260 003:410.558 - 0.476ms returns FALSE
TA260 003:410.569 JLINK_HasError()
TA260 003:412.082 JLINK_IsHalted()
TA260 003:412.612 - 0.530ms returns FALSE
TA260 003:412.622 JLINK_HasError()
TA260 003:414.080 JLINK_IsHalted()
TA260 003:414.612 - 0.532ms returns FALSE
TA260 003:414.618 JLINK_HasError()
TA260 003:416.588 JLINK_IsHalted()
TA260 003:417.069 - 0.481ms returns FALSE
TA260 003:417.080 JLINK_HasError()
TA260 003:418.590 JLINK_IsHalted()
TA260 003:419.049 - 0.458ms returns FALSE
TA260 003:419.066 JLINK_HasError()
TA260 003:420.586 JLINK_IsHalted()
TA260 003:421.059 - 0.472ms returns FALSE
TA260 003:421.068 JLINK_HasError()
TA260 003:422.585 JLINK_IsHalted()
TA260 003:423.123 - 0.537ms returns FALSE
TA260 003:423.133 JLINK_HasError()
TA260 003:425.089 JLINK_IsHalted()
TA260 003:425.613 - 0.524ms returns FALSE
TA260 003:425.624 JLINK_HasError()
TA260 003:427.100 JLINK_IsHalted()
TA260 003:427.614 - 0.513ms returns FALSE
TA260 003:427.622 JLINK_HasError()
TA260 003:429.100 JLINK_IsHalted()
TA260 003:429.602 - 0.501ms returns FALSE
TA260 003:429.609 JLINK_HasError()
TA260 003:431.094 JLINK_IsHalted()
TA260 003:431.600 - 0.505ms returns FALSE
TA260 003:431.610 JLINK_HasError()
TA260 003:433.099 JLINK_IsHalted()
TA260 003:433.574 - 0.474ms returns FALSE
TA260 003:433.591 JLINK_HasError()
TA260 003:435.598 JLINK_IsHalted()
TA260 003:436.101 - 0.503ms returns FALSE
TA260 003:436.113 JLINK_HasError()
TA260 003:437.607 JLINK_IsHalted()
TA260 003:438.068 - 0.460ms returns FALSE
TA260 003:438.075 JLINK_HasError()
TA260 003:439.612 JLINK_IsHalted()
TA260 003:440.181 - 0.569ms returns FALSE
TA260 003:440.188 JLINK_HasError()
TA260 003:441.601 JLINK_IsHalted()
TA260 003:442.067 - 0.465ms returns FALSE
TA260 003:442.076 JLINK_HasError()
TA260 003:443.606 JLINK_IsHalted()
TA260 003:444.035 - 0.429ms returns FALSE
TA260 003:444.044 JLINK_HasError()
TA260 003:445.109 JLINK_IsHalted()
TA260 003:445.545 - 0.436ms returns FALSE
TA260 003:445.561 JLINK_HasError()
TA260 003:447.117 JLINK_IsHalted()
TA260 003:447.614 - 0.496ms returns FALSE
TA260 003:447.622 JLINK_HasError()
TA260 003:448.913 JLINK_IsHalted()
TA260 003:449.395 - 0.481ms returns FALSE
TA260 003:449.470 JLINK_HasError()
TA260 003:450.954 JLINK_IsHalted()
TA260 003:451.476 - 0.522ms returns FALSE
TA260 003:451.491 JLINK_HasError()
TA260 003:452.916 JLINK_IsHalted()
TA260 003:453.468 - 0.550ms returns FALSE
TA260 003:453.477 JLINK_HasError()
TA260 003:454.922 JLINK_IsHalted()
TA260 003:455.466 - 0.543ms returns FALSE
TA260 003:455.472 JLINK_HasError()
TA260 003:457.428 JLINK_IsHalted()
TA260 003:457.895 - 0.466ms returns FALSE
TA260 003:457.903 JLINK_HasError()
TA260 003:459.424 JLINK_IsHalted()
TA260 003:459.902 - 0.477ms returns FALSE
TA260 003:459.918 JLINK_HasError()
TA260 003:461.427 JLINK_IsHalted()
TA260 003:461.963 - 0.536ms returns FALSE
TA260 003:461.972 JLINK_HasError()
TA260 003:463.426 JLINK_IsHalted()
TA260 003:463.931 - 0.505ms returns FALSE
TA260 003:463.940 JLINK_HasError()
TA260 003:465.932 JLINK_IsHalted()
TA260 003:466.476 - 0.543ms returns FALSE
TA260 003:466.490 JLINK_HasError()
TA260 003:467.928 JLINK_IsHalted()
TA260 003:468.472 - 0.543ms returns FALSE
TA260 003:468.479 JLINK_HasError()
TA260 003:469.933 JLINK_IsHalted()
TA260 003:470.386 - 0.453ms returns FALSE
TA260 003:470.396 JLINK_HasError()
TA260 003:471.930 JLINK_IsHalted()
TA260 003:472.464 - 0.533ms returns FALSE
TA260 003:472.470 JLINK_HasError()
TA260 003:473.930 JLINK_IsHalted()
TA260 003:474.471 - 0.541ms returns FALSE
TA260 003:474.478 JLINK_HasError()
TA260 003:476.445 JLINK_IsHalted()
TA260 003:476.877 - 0.432ms returns FALSE
TA260 003:476.930 JLINK_HasError()
TA260 003:478.439 JLINK_IsHalted()
TA260 003:478.929 - 0.490ms returns FALSE
TA260 003:478.936 JLINK_HasError()
TA260 003:480.443 JLINK_IsHalted()
TA260 003:480.968 - 0.524ms returns FALSE
TA260 003:480.978 JLINK_HasError()
TA260 003:482.495 JLINK_IsHalted()
TA260 003:482.964 - 0.468ms returns FALSE
TA260 003:482.971 JLINK_HasError()
TA260 003:485.001 JLINK_IsHalted()
TA260 003:485.474 - 0.473ms returns FALSE
TA260 003:485.483 JLINK_HasError()
TA260 003:487.002 JLINK_IsHalted()
TA260 003:487.466 - 0.464ms returns FALSE
TA260 003:487.481 JLINK_HasError()
TA260 003:488.998 JLINK_IsHalted()
TA260 003:489.505 - 0.505ms returns FALSE
TA260 003:489.515 JLINK_HasError()
TA260 003:491.005 JLINK_IsHalted()
TA260 003:491.500 - 0.494ms returns FALSE
TA260 003:491.510 JLINK_HasError()
TA260 003:492.999 JLINK_IsHalted()
TA260 003:493.502 - 0.502ms returns FALSE
TA260 003:493.511 JLINK_HasError()
TA260 003:495.008 JLINK_IsHalted()
TA260 003:495.615 - 0.606ms returns FALSE
TA260 003:495.675 JLINK_HasError()
TA260 003:497.516 JLINK_IsHalted()
TA260 003:498.034 - 0.519ms returns FALSE
TA260 003:498.041 JLINK_HasError()
TA260 003:499.512 JLINK_IsHalted()
TA260 003:499.998 - 0.486ms returns FALSE
TA260 003:500.006 JLINK_HasError()
TA260 003:501.518 JLINK_IsHalted()
TA260 003:502.009 - 0.491ms returns FALSE
TA260 003:502.025 JLINK_HasError()
TA260 003:503.510 JLINK_IsHalted()
TA260 003:504.020 - 0.510ms returns FALSE
TA260 003:504.030 JLINK_HasError()
TA260 003:506.021 JLINK_IsHalted()
TA260 003:506.511 - 0.489ms returns FALSE
TA260 003:506.521 JLINK_HasError()
TA260 003:508.020 JLINK_IsHalted()
TA260 003:508.499 - 0.478ms returns FALSE
TA260 003:508.505 JLINK_HasError()
TA260 003:510.520 JLINK_IsHalted()
TA260 003:511.033 - 0.513ms returns FALSE
TA260 003:511.042 JLINK_HasError()
TA260 003:512.522 JLINK_IsHalted()
TA260 003:513.068 - 0.546ms returns FALSE
TA260 003:513.084 JLINK_HasError()
TA260 003:515.028 JLINK_IsHalted()
TA260 003:515.626 - 0.598ms returns FALSE
TA260 003:515.637 JLINK_HasError()
TA260 003:517.032 JLINK_IsHalted()
TA260 003:517.502 - 0.469ms returns FALSE
TA260 003:517.519 JLINK_HasError()
TA260 003:519.031 JLINK_IsHalted()
TA260 003:519.508 - 0.476ms returns FALSE
TA260 003:519.515 JLINK_HasError()
TA260 003:521.033 JLINK_IsHalted()
TA260 003:521.474 - 0.440ms returns FALSE
TA260 003:521.484 JLINK_HasError()
TA260 003:523.032 JLINK_IsHalted()
TA260 003:523.511 - 0.479ms returns FALSE
TA260 003:523.520 JLINK_HasError()
TA260 003:525.032 JLINK_IsHalted()
TA260 003:525.537 - 0.505ms returns FALSE
TA260 003:525.544 JLINK_HasError()
TA260 003:527.536 JLINK_IsHalted()
TA260 003:528.092 - 0.556ms returns FALSE
TA260 003:528.100 JLINK_HasError()
TA260 003:529.539 JLINK_IsHalted()
TA260 003:530.025 - 0.485ms returns FALSE
TA260 003:530.035 JLINK_HasError()
TA260 003:531.536 JLINK_IsHalted()
TA260 003:532.022 - 0.485ms returns FALSE
TA260 003:532.030 JLINK_HasError()
TA260 003:533.534 JLINK_IsHalted()
TA260 003:533.996 - 0.462ms returns FALSE
TA260 003:534.005 JLINK_HasError()
TA260 003:536.044 JLINK_IsHalted()
TA260 003:536.598 - 0.554ms returns FALSE
TA260 003:536.605 JLINK_HasError()
TA260 003:538.046 JLINK_IsHalted()
TA260 003:538.510 - 0.463ms returns FALSE
TA260 003:538.517 JLINK_HasError()
TA260 003:540.045 JLINK_IsHalted()
TA260 003:540.615 - 0.570ms returns FALSE
TA260 003:540.625 JLINK_HasError()
TA260 003:542.043 JLINK_IsHalted()
TA260 003:542.509 - 0.465ms returns FALSE
TA260 003:542.517 JLINK_HasError()
TA260 003:544.044 JLINK_IsHalted()
TA260 003:544.663 - 0.619ms returns FALSE
TA260 003:544.684 JLINK_HasError()
TA260 003:547.552 JLINK_IsHalted()
TA260 003:548.038 - 0.484ms returns FALSE
TA260 003:548.054 JLINK_HasError()
TA260 003:549.552 JLINK_IsHalted()
TA260 003:550.028 - 0.475ms returns FALSE
TA260 003:550.036 JLINK_HasError()
TA260 003:551.559 JLINK_IsHalted()
TA260 003:552.036 - 0.476ms returns FALSE
TA260 003:552.049 JLINK_HasError()
TA260 003:553.988 JLINK_IsHalted()
TA260 003:554.468 - 0.480ms returns FALSE
TA260 003:554.479 JLINK_HasError()
TA260 003:556.494 JLINK_IsHalted()
TA260 003:556.980 - 0.486ms returns FALSE
TA260 003:556.993 JLINK_HasError()
TA260 003:558.492 JLINK_IsHalted()
TA260 003:558.988 - 0.495ms returns FALSE
TA260 003:558.994 JLINK_HasError()
TA260 003:560.495 JLINK_IsHalted()
TA260 003:560.954 - 0.458ms returns FALSE
TA260 003:560.964 JLINK_HasError()
TA260 003:562.494 JLINK_IsHalted()
TA260 003:563.012 - 0.517ms returns FALSE
TA260 003:563.020 JLINK_HasError()
TA260 003:565.016 JLINK_IsHalted()
TA260 003:565.497 - 0.480ms returns FALSE
TA260 003:565.504 JLINK_HasError()
TA260 003:567.007 JLINK_IsHalted()
TA260 003:567.512 - 0.504ms returns FALSE
TA260 003:567.522 JLINK_HasError()
TA260 003:568.999 JLINK_IsHalted()
TA260 003:569.508 - 0.509ms returns FALSE
TA260 003:569.515 JLINK_HasError()
TA260 003:571.007 JLINK_IsHalted()
TA260 003:571.518 - 0.510ms returns FALSE
TA260 003:571.525 JLINK_HasError()
TA260 003:573.001 JLINK_IsHalted()
TA260 003:573.514 - 0.511ms returns FALSE
TA260 003:573.524 JLINK_HasError()
TA260 003:575.004 JLINK_IsHalted()
TA260 003:575.522 - 0.517ms returns FALSE
TA260 003:575.538 JLINK_HasError()
TA260 003:579.514 JLINK_IsHalted()
TA260 003:580.057 - 0.543ms returns FALSE
TA260 003:580.065 JLINK_HasError()
TA260 003:581.513 JLINK_IsHalted()
TA260 003:581.951 - 0.437ms returns FALSE
TA260 003:581.960 JLINK_HasError()
TA260 003:583.511 JLINK_IsHalted()
TA260 003:583.962 - 0.451ms returns FALSE
TA260 003:583.970 JLINK_HasError()
TA260 003:585.020 JLINK_IsHalted()
TA260 003:585.495 - 0.474ms returns FALSE
TA260 003:585.501 JLINK_HasError()
TA260 003:587.016 JLINK_IsHalted()
TA260 003:587.512 - 0.494ms returns FALSE
TA260 003:587.526 JLINK_HasError()
TA260 003:588.744 JLINK_IsHalted()
TA260 003:589.216 - 0.471ms returns FALSE
TA260 003:589.225 JLINK_HasError()
TA260 003:590.754 JLINK_IsHalted()
TA260 003:591.286 - 0.532ms returns FALSE
TA260 003:591.298 JLINK_HasError()
TA260 003:592.746 JLINK_IsHalted()
TA260 003:593.259 - 0.512ms returns FALSE
TA260 003:593.268 JLINK_HasError()
TA260 003:594.748 JLINK_IsHalted()
TA260 003:595.215 - 0.466ms returns FALSE
TA260 003:595.222 JLINK_HasError()
TA260 003:597.253 JLINK_IsHalted()
TA260 003:597.701 - 0.447ms returns FALSE
TA260 003:597.708 JLINK_HasError()
TA260 003:599.269 JLINK_IsHalted()
TA260 003:599.750 - 0.480ms returns FALSE
TA260 003:599.756 JLINK_HasError()
TA260 003:601.258 JLINK_IsHalted()
TA260 003:601.704 - 0.445ms returns FALSE
TA260 003:601.714 JLINK_HasError()
TA260 003:603.258 JLINK_IsHalted()
TA260 003:603.748 - 0.490ms returns FALSE
TA260 003:603.762 JLINK_HasError()
TA260 003:605.762 JLINK_IsHalted()
TA260 003:606.283 - 0.521ms returns FALSE
TA260 003:606.290 JLINK_HasError()
TA260 003:608.269 JLINK_IsHalted()
TA260 003:608.745 - 0.475ms returns FALSE
TA260 003:608.752 JLINK_HasError()
TA260 003:610.266 JLINK_IsHalted()
TA260 003:610.784 - 0.518ms returns FALSE
TA260 003:610.793 JLINK_HasError()
TA260 003:612.266 JLINK_IsHalted()
TA260 003:612.756 - 0.490ms returns FALSE
TA260 003:612.764 JLINK_HasError()
TA260 003:614.690 JLINK_IsHalted()
TA260 003:615.165 - 0.474ms returns FALSE
TA260 003:615.175 JLINK_HasError()
TA260 003:617.196 JLINK_IsHalted()
TA260 003:617.646 - 0.450ms returns FALSE
TA260 003:617.658 JLINK_HasError()
TA260 003:619.199 JLINK_IsHalted()
TA260 003:619.693 - 0.494ms returns FALSE
TA260 003:619.701 JLINK_HasError()
TA260 003:621.200 JLINK_IsHalted()
TA260 003:621.699 - 0.499ms returns FALSE
TA260 003:621.709 JLINK_HasError()
TA260 003:623.200 JLINK_IsHalted()
TA260 003:623.716 - 0.515ms returns FALSE
TA260 003:623.732 JLINK_HasError()
TA260 003:625.712 JLINK_IsHalted()
TA260 003:628.112   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:628.612 - 2.900ms returns TRUE
TA260 003:628.621 JLINK_ReadReg(R15 (PC))
TA260 003:628.627 - 0.005ms returns 0x20000000
TA260 003:628.632 JLINK_ClrBPEx(BPHandle = 0x00000048)
TA260 003:628.636 - 0.003ms returns 0x00
TA260 003:628.640 JLINK_ReadReg(R0)
TA260 003:628.644 - 0.003ms returns 0x70242851
TA260 003:630.428 JLINK_HasError()
TA260 003:630.443 JLINK_WriteReg(R0, 0x00000003)
TA260 003:630.448 - 0.005ms returns 0
TA260 003:630.453 JLINK_WriteReg(R1, 0x08000000)
TA260 003:630.456 - 0.003ms returns 0
TA260 003:630.460 JLINK_WriteReg(R2, 0x0000E718)
TA260 003:630.464 - 0.003ms returns 0
TA260 003:630.468 JLINK_WriteReg(R3, 0x04C11DB7)
TA260 003:630.472 - 0.003ms returns 0
TA260 003:630.476 JLINK_WriteReg(R4, 0x00000000)
TA260 003:630.479 - 0.003ms returns 0
TA260 003:630.483 JLINK_WriteReg(R5, 0x00000000)
TA260 003:630.487 - 0.004ms returns 0
TA260 003:630.492 JLINK_WriteReg(R6, 0x00000000)
TA260 003:630.495 - 0.003ms returns 0
TA260 003:630.499 JLINK_WriteReg(R7, 0x00000000)
TA260 003:630.502 - 0.003ms returns 0
TA260 003:630.506 JLINK_WriteReg(R8, 0x00000000)
TA260 003:630.510 - 0.003ms returns 0
TA260 003:630.514 JLINK_WriteReg(R9, 0x20000180)
TA260 003:630.518 - 0.004ms returns 0
TA260 003:630.522 JLINK_WriteReg(R10, 0x00000000)
TA260 003:630.525 - 0.003ms returns 0
TA260 003:630.529 JLINK_WriteReg(R11, 0x00000000)
TA260 003:630.533 - 0.003ms returns 0
TA260 003:630.537 JLINK_WriteReg(R12, 0x00000000)
TA260 003:630.540 - 0.003ms returns 0
TA260 003:630.544 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:630.548 - 0.003ms returns 0
TA260 003:630.552 JLINK_WriteReg(R14, 0x20000001)
TA260 003:630.556 - 0.003ms returns 0
TA260 003:630.560 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 003:630.564 - 0.003ms returns 0
TA260 003:630.568 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:630.571 - 0.003ms returns 0
TA260 003:630.575 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:630.579 - 0.003ms returns 0
TA260 003:630.583 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:630.586 - 0.003ms returns 0
TA260 003:630.590 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:630.594 - 0.003ms returns 0
TA260 003:630.599 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:630.608 - 0.009ms returns 0x00000049
TA260 003:630.614 JLINK_Go()
TA260 003:630.625   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:633.471 - 2.856ms 
TA260 003:633.484 JLINK_IsHalted()
TA260 003:635.847   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:636.386 - 2.902ms returns TRUE
TA260 003:636.395 JLINK_ReadReg(R15 (PC))
TA260 003:636.401 - 0.005ms returns 0x20000000
TA260 003:636.406 JLINK_ClrBPEx(BPHandle = 0x00000049)
TA260 003:636.410 - 0.003ms returns 0x00
TA260 003:636.414 JLINK_ReadReg(R0)
TA260 003:636.418 - 0.003ms returns 0x00000000
TA260 003:689.354 JLINK_WriteMemEx(0x20000000, 0x00000002 Bytes, Flags = 0x02000000)
TA260 003:689.378   Data:  FE E7
TA260 003:689.398   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 003:689.882 - 0.528ms returns 0x2
TA260 003:689.900 JLINK_HasError()
TA260 003:700.391 JLINK_Close()
TA260 003:703.028   OnDisconnectTarget() start
TA260 003:703.049    J-Link Script File: Executing OnDisconnectTarget()
TA260 003:703.064   CPU_WriteMem(4 bytes @ 0xE0042004)
TA260 003:703.560   CPU_WriteMem(4 bytes @ 0xE0042008)
TA260 003:705.610   OnDisconnectTarget() end - Took 993us
TA260 003:705.638   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:722.982 - 22.590ms
TA260 003:723.013   
TA260 003:723.017   Closed
