TA260 000:004.089   SEGGER J-Link V8.16 Log File
TA260 000:004.193   DLL Compiled: Feb 26 2025 12:07:26
TA260 000:004.198   Logging started @ 2025-08-02 09:10
TA260 000:004.202   Process: G:\keil\keil arm\UV4\UV4.exe
TA260 000:004.212 - 4.206ms 
TA260 000:004.249 JLINK_SetWarnOutHandler(...)
TA260 000:004.254 - 0.008ms 
TA260 000:004.265 JLINK_OpenEx(...)
TA260 000:007.890   Firmware: J-Link V9 compiled May  7 2021 16:26:12
TA260 000:009.269   Firmware: J-Link V9 compiled May  7 2021 16:26:12
TA260 000:009.374   Decompressing FW timestamp took 81 us
TA260 000:017.047   Hardware: V9.60
TA260 000:017.060   S/N: 69655018
TA260 000:017.065   OEM: SEGGER
TA260 000:017.070   Feature(s): R<PERSON>, G<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>B<PERSON>, JFlash
TA260 000:018.367   Bootloader: (FW returned invalid version)
TA260 000:019.768   TELNET listener socket opened on port 19021
TA260 000:019.834   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
TA260 000:020.096   WEBSRV Webserver running on local port 19080
TA260 000:020.165   Looking for J-Link GUI Server exe at: G:\keil\keil arm\ARM\Segger\JLinkGUIServer.exe
TA260 000:020.247   Looking for J-Link GUI Server exe at: C:\Program Files (x86)\SEGGER\JLink_V630d\\JLinkGUIServer.exe
TA260 000:326.416   Failed to connect to J-Link GUI Server.
TA260 000:326.443 - 322.170ms returns "O.K."
TA260 000:326.457 JLINK_GetEmuCaps()
TA260 000:326.463 - 0.004ms returns 0xB9FF7BBF
TA260 000:326.473 JLINK_TIF_GetAvailable(...)
TA260 000:326.888 - 0.416ms 
TA260 000:326.900 JLINK_SetErrorOutHandler(...)
TA260 000:326.904 - 0.003ms 
TA260 000:326.924 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\2025ele_ori\zuolan_stm32\MDK-ARM\JLinkSettings.ini"", ...). 
TA260 000:337.833 - 10.910ms returns 0x00
TA260 000:340.315 JLINK_ExecCommand("Device = STM32F429IGTx", ...). 
TA260 000:341.779   Flash bank @ 0x90000000: SFL: Parsing sectorization info from ELF file
TA260 000:341.798     FlashDevice.SectorInfo[0]: .SectorSize = 0x00010000, .SectorStartAddr = 0x00000000
TA260 000:347.024   Device "STM32F429IG" selected.
TA260 000:347.299 - 6.959ms returns 0x00
TA260 000:347.312 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
TA260 000:347.328   ERROR: Unknown command
TA260 000:347.333 - 0.015ms returns 0x01
TA260 000:347.339 JLINK_GetHardwareVersion()
TA260 000:347.343 - 0.003ms returns 96000
TA260 000:347.347 JLINK_GetDLLVersion()
TA260 000:347.351 - 0.003ms returns 81600
TA260 000:347.356 JLINK_GetOEMString(...)
TA260 000:347.360 JLINK_GetFirmwareString(...)
TA260 000:347.364 - 0.003ms 
TA260 000:352.689 JLINK_GetDLLVersion()
TA260 000:352.707 - 0.017ms returns 81600
TA260 000:352.712 JLINK_GetCompileDateTime()
TA260 000:352.716 - 0.003ms 
TA260 000:354.228 JLINK_GetFirmwareString(...)
TA260 000:354.242 - 0.013ms 
TA260 000:355.601 JLINK_GetHardwareVersion()
TA260 000:355.614 - 0.012ms returns 96000
TA260 000:357.023 JLINK_GetSN()
TA260 000:357.095 - 0.071ms returns 69655018
TA260 000:358.558 JLINK_GetOEMString(...)
TA260 000:362.223 JLINK_TIF_Select(JLINKARM_TIF_SWD)
TA260 000:363.733 - 1.513ms returns 0x00
TA260 000:363.750 JLINK_HasError()
TA260 000:363.763 JLINK_SetSpeed(5000)
TA260 000:364.099 - 0.337ms 
TA260 000:364.113 JLINK_GetId()
TA260 000:366.330   InitTarget() start
TA260 000:366.365    J-Link Script File: Executing InitTarget()
TA260 000:368.393   SWD selected. Executing JTAG -> SWD switching sequence.
TA260 000:373.505   DAP initialized successfully.
TA260 000:386.013   InitTarget() end - Took 17.8ms
TA260 000:388.709   Found SW-DP with ID 0x2BA01477
TA260 000:393.878   DPIDR: 0x2BA01477
TA260 000:395.493   CoreSight SoC-400 or earlier
TA260 000:397.084   Scanning AP map to find all available APs
TA260 000:399.576   AP[1]: Stopped AP scan as end of AP map has been reached
TA260 000:401.488   AP[0]: AHB-AP (IDR: 0x24770011, ADDR: 0x00000000)
TA260 000:403.124   Iterating through AP map to find AHB-AP to use
TA260 000:405.916   AP[0]: Core found
TA260 000:408.209   AP[0]: AHB-AP ROM base: 0xE00FF000
TA260 000:410.862   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
TA260 000:412.818   Found Cortex-M4 r0p1, Little endian.
TA260 000:413.691   -- Max. mem block: 0x00010C40
TA260 000:414.479   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:415.006   CPU_ReadMem(4 bytes @ 0x********)
TA260 000:417.305   FPUnit: 6 code (BP) slots and 2 literal slots
TA260 000:417.333   CPU_ReadMem(4 bytes @ 0xE000EDFC)
TA260 000:417.864   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:418.369   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:418.844   CPU_WriteMem(4 bytes @ 0xE0001000)
TA260 000:419.327   CPU_ReadMem(4 bytes @ 0xE000ED88)
TA260 000:420.004   CPU_WriteMem(4 bytes @ 0xE000ED88)
TA260 000:420.465   CPU_ReadMem(4 bytes @ 0xE000ED88)
TA260 000:420.999   CPU_WriteMem(4 bytes @ 0xE000ED88)
TA260 000:423.591   CoreSight components:
TA260 000:425.296   ROMTbl[0] @ E00FF000
TA260 000:425.321   CPU_ReadMem(64 bytes @ 0xE00FF000)
TA260 000:426.115   CPU_ReadMem(32 bytes @ 0xE000EFE0)
TA260 000:428.600   [0][0]: E000E000 CID B105E00D PID 000BB00C SCS-M7
TA260 000:428.627   CPU_ReadMem(32 bytes @ 0xE0001FE0)
TA260 000:431.102   [0][1]: E0001000 CID B105E00D PID 003BB002 DWT
TA260 000:431.131   CPU_ReadMem(32 bytes @ 0xE0002FE0)
TA260 000:433.410   [0][2]: ******** CID B105E00D PID 002BB003 FPB
TA260 000:433.435   CPU_ReadMem(32 bytes @ 0xE0000FE0)
TA260 000:435.750   [0][3]: ******** CID B105E00D PID 003BB001 ITM
TA260 000:435.775   CPU_ReadMem(32 bytes @ 0xE0040FE0)
TA260 000:438.543   [0][4]: ******** CID B105900D PID 000BB9A1 TPIU
TA260 000:438.577   CPU_ReadMem(32 bytes @ 0xE0041FE0)
TA260 000:441.102   [0][5]: ******** CID B105900D PID 000BB925 ETM
TA260 000:441.647 - 77.532ms returns 0x2BA01477
TA260 000:441.705 JLINK_GetDLLVersion()
TA260 000:441.710 - 0.005ms returns 81600
TA260 000:441.723 JLINK_CORE_GetFound()
TA260 000:441.727 - 0.003ms returns 0xE0000FF
TA260 000:441.732 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
TA260 000:441.739   Value=0xE00FF000
TA260 000:441.744 - 0.012ms returns 0
TA260 000:443.505 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
TA260 000:443.523   Value=0xE00FF000
TA260 000:443.530 - 0.024ms returns 0
TA260 000:443.535 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
TA260 000:443.538   Value=0x********
TA260 000:443.543 - 0.008ms returns 0
TA260 000:443.548 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
TA260 000:443.576   CPU_ReadMem(32 bytes @ 0xE0041FD0)
TA260 000:444.259   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
TA260 000:444.274 - 0.725ms returns 32 (0x20)
TA260 000:444.280 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
TA260 000:444.284   Value=0x00000000
TA260 000:444.289 - 0.009ms returns 0
TA260 000:444.294 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
TA260 000:444.297   Value=0x********
TA260 000:444.302 - 0.008ms returns 0
TA260 000:444.306 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
TA260 000:444.310   Value=0x********
TA260 000:444.314 - 0.008ms returns 0
TA260 000:444.319 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
TA260 000:444.322   Value=0xE0001000
TA260 000:444.327 - 0.008ms returns 0
TA260 000:444.331 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
TA260 000:444.334   Value=0x********
TA260 000:444.339 - 0.008ms returns 0
TA260 000:444.343 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
TA260 000:444.347   Value=0xE000E000
TA260 000:444.351 - 0.008ms returns 0
TA260 000:444.356 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
TA260 000:444.359   Value=0xE000EDF0
TA260 000:444.364 - 0.008ms returns 0
TA260 000:444.368 JLINK_GetDebugInfo(0x01 = Unknown)
TA260 000:444.372   Value=0x00000001
TA260 000:444.376 - 0.008ms returns 0
TA260 000:444.381 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
TA260 000:444.388   CPU_ReadMem(4 bytes @ 0xE000ED00)
TA260 000:444.846   Data:  41 C2 0F 41
TA260 000:444.855   Debug reg: CPUID
TA260 000:444.860 - 0.479ms returns 1 (0x1)
TA260 000:444.865 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
TA260 000:444.872   Value=0x00000000
TA260 000:444.877 - 0.012ms returns 0
TA260 000:444.882 JLINK_HasError()
TA260 000:444.887 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
TA260 000:444.891 - 0.004ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
TA260 000:444.895 JLINK_Reset()
TA260 000:444.903   JLINK_GetResetTypeDesc
TA260 000:444.907   - 0.004ms 
TA260 000:446.859   Reset type: NORMAL (https://wiki.segger.com/J-Link_Reset_Strategies)
TA260 000:446.891   CPU is running
TA260 000:446.901   CPU_WriteMem(4 bytes @ 0xE000EDF0)
TA260 000:447.372   CPU is running
TA260 000:447.387   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:449.457   Reset: Halt core after reset via DEMCR.VC_CORERESET.
TA260 000:452.378   Reset: Reset device via AIRCR.SYSRESETREQ.
TA260 000:452.409   CPU is running
TA260 000:452.420   CPU_WriteMem(4 bytes @ 0xE000ED0C)
TA260 000:506.508   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:507.015   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:509.801   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:516.312   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:519.147   CPU_WriteMem(4 bytes @ 0x********)
TA260 000:519.648   CPU_ReadMem(4 bytes @ 0xE000EDFC)
TA260 000:520.130   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:520.636 - 75.740ms 
TA260 000:520.690 JLINK_Halt()
TA260 000:520.702 - 0.011ms returns 0x00
TA260 000:520.708 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
TA260 000:520.719   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:521.253   Data:  03 00 03 00
TA260 000:521.260   Debug reg: DHCSR
TA260 000:521.266 - 0.558ms returns 1 (0x1)
TA260 000:521.272 JLINK_WriteU32(0xE000EDF0, 0xA05F0003)
TA260 000:521.275   Debug reg: DHCSR
TA260 000:521.522   CPU_WriteMem(4 bytes @ 0xE000EDF0)
TA260 000:522.054 - 0.782ms returns 0 (0x00000000)
TA260 000:522.066 JLINK_WriteU32(0xE000EDFC, 0x01000000)
TA260 000:522.070   Debug reg: DEMCR
TA260 000:522.080   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:522.656 - 0.590ms returns 0 (0x00000000)
TA260 000:529.547 JLINK_GetHWStatus(...)
TA260 000:529.913 - 0.365ms returns 0
TA260 000:534.609 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
TA260 000:534.642 - 0.033ms returns 0x06
TA260 000:534.647 JLINK_GetNumBPUnits(Type = 0xF0)
TA260 000:534.651 - 0.003ms returns 0x2000
TA260 000:534.655 JLINK_GetNumWPUnits()
TA260 000:534.659 - 0.003ms returns 4
TA260 000:539.375 JLINK_GetSpeed()
TA260 000:539.400 - 0.024ms returns 4000
TA260 000:542.746 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
TA260 000:542.775   CPU_ReadMem(4 bytes @ 0xE000E004)
TA260 000:543.259   Data:  02 00 00 00
TA260 000:543.268 - 0.523ms returns 1 (0x1)
TA260 000:543.275 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
TA260 000:543.281   CPU_ReadMem(4 bytes @ 0xE000E004)
TA260 000:543.766   Data:  02 00 00 00
TA260 000:543.772 - 0.497ms returns 1 (0x1)
TA260 000:543.778 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
TA260 000:543.781   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
TA260 000:543.790   CPU_WriteMem(28 bytes @ 0xE0001000)
TA260 000:544.378 - 0.600ms returns 0x1C
TA260 000:544.390 JLINK_Halt()
TA260 000:544.394 - 0.004ms returns 0x00
TA260 000:544.398 JLINK_IsHalted()
TA260 000:544.403 - 0.004ms returns TRUE
TA260 000:546.914 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 000:546.926   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 000:547.183   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 000:548.989 - 2.074ms returns 0x184
TA260 000:549.028 JLINK_HasError()
TA260 000:549.034 JLINK_WriteReg(R0, 0x08000000)
TA260 000:549.040 - 0.006ms returns 0
TA260 000:549.044 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 000:549.048 - 0.003ms returns 0
TA260 000:549.052 JLINK_WriteReg(R2, 0x00000001)
TA260 000:549.056 - 0.003ms returns 0
TA260 000:549.060 JLINK_WriteReg(R3, 0x00000000)
TA260 000:549.064 - 0.003ms returns 0
TA260 000:549.068 JLINK_WriteReg(R4, 0x00000000)
TA260 000:549.071 - 0.003ms returns 0
TA260 000:549.076 JLINK_WriteReg(R5, 0x00000000)
TA260 000:549.083 - 0.007ms returns 0
TA260 000:549.089 JLINK_WriteReg(R6, 0x00000000)
TA260 000:549.094 - 0.004ms returns 0
TA260 000:549.099 JLINK_WriteReg(R7, 0x00000000)
TA260 000:549.103 - 0.004ms returns 0
TA260 000:549.120 JLINK_WriteReg(R8, 0x00000000)
TA260 000:549.124 - 0.017ms returns 0
TA260 000:549.129 JLINK_WriteReg(R9, 0x20000180)
TA260 000:549.132 - 0.003ms returns 0
TA260 000:549.136 JLINK_WriteReg(R10, 0x00000000)
TA260 000:549.139 - 0.003ms returns 0
TA260 000:549.143 JLINK_WriteReg(R11, 0x00000000)
TA260 000:549.147 - 0.003ms returns 0
TA260 000:549.151 JLINK_WriteReg(R12, 0x00000000)
TA260 000:549.154 - 0.003ms returns 0
TA260 000:549.158 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:549.163 - 0.004ms returns 0
TA260 000:549.167 JLINK_WriteReg(R14, 0x20000001)
TA260 000:549.170 - 0.003ms returns 0
TA260 000:549.178 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 000:549.182 - 0.007ms returns 0
TA260 000:549.186 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:549.190 - 0.003ms returns 0
TA260 000:549.194 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:549.197 - 0.003ms returns 0
TA260 000:549.201 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:549.205 - 0.003ms returns 0
TA260 000:549.209 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:549.213 - 0.003ms returns 0
TA260 000:549.218 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:549.225   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:549.758 - 0.540ms returns 0x00000001
TA260 000:549.766 JLINK_Go()
TA260 000:549.771   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 000:550.259   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:550.772   CPU_WriteMem(4 bytes @ 0xE0002008)
TA260 000:550.785   CPU_WriteMem(4 bytes @ 0xE000200C)
TA260 000:550.791   CPU_WriteMem(4 bytes @ 0xE0002010)
TA260 000:550.796   CPU_WriteMem(4 bytes @ 0xE0002014)
TA260 000:550.801   CPU_WriteMem(4 bytes @ 0xE0002018)
TA260 000:550.805   CPU_WriteMem(4 bytes @ 0xE000201C)
TA260 000:551.997   CPU_WriteMem(4 bytes @ 0xE0001004)
TA260 000:556.688   Memory map 'after startup completion point' is active
TA260 000:556.715 - 6.947ms 
TA260 000:556.723 JLINK_IsHalted()
TA260 000:559.022   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:559.540 - 2.816ms returns TRUE
TA260 000:559.549 JLINK_ReadReg(R15 (PC))
TA260 000:559.556 - 0.006ms returns 0x20000000
TA260 000:559.561 JLINK_ClrBPEx(BPHandle = 0x00000001)
TA260 000:559.565 - 0.003ms returns 0x00
TA260 000:559.569 JLINK_ReadReg(R0)
TA260 000:559.573 - 0.003ms returns 0x00000000
TA260 000:560.093 JLINK_HasError()
TA260 000:560.116 JLINK_WriteReg(R0, 0x08000000)
TA260 000:560.123 - 0.006ms returns 0
TA260 000:560.127 JLINK_WriteReg(R1, 0x00004000)
TA260 000:560.131 - 0.003ms returns 0
TA260 000:560.135 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:560.139 - 0.003ms returns 0
TA260 000:560.143 JLINK_WriteReg(R3, 0x00000000)
TA260 000:560.146 - 0.003ms returns 0
TA260 000:560.150 JLINK_WriteReg(R4, 0x00000000)
TA260 000:560.154 - 0.003ms returns 0
TA260 000:560.158 JLINK_WriteReg(R5, 0x00000000)
TA260 000:560.161 - 0.003ms returns 0
TA260 000:560.165 JLINK_WriteReg(R6, 0x00000000)
TA260 000:560.169 - 0.003ms returns 0
TA260 000:560.173 JLINK_WriteReg(R7, 0x00000000)
TA260 000:560.176 - 0.003ms returns 0
TA260 000:560.180 JLINK_WriteReg(R8, 0x00000000)
TA260 000:560.184 - 0.003ms returns 0
TA260 000:560.188 JLINK_WriteReg(R9, 0x20000180)
TA260 000:560.192 - 0.003ms returns 0
TA260 000:560.196 JLINK_WriteReg(R10, 0x00000000)
TA260 000:560.200 - 0.003ms returns 0
TA260 000:560.204 JLINK_WriteReg(R11, 0x00000000)
TA260 000:560.207 - 0.003ms returns 0
TA260 000:560.211 JLINK_WriteReg(R12, 0x00000000)
TA260 000:560.215 - 0.003ms returns 0
TA260 000:560.219 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:560.222 - 0.004ms returns 0
TA260 000:560.226 JLINK_WriteReg(R14, 0x20000001)
TA260 000:560.230 - 0.003ms returns 0
TA260 000:560.234 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 000:560.238 - 0.003ms returns 0
TA260 000:560.242 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:560.245 - 0.003ms returns 0
TA260 000:560.304 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:560.311 - 0.007ms returns 0
TA260 000:560.315 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:560.318 - 0.003ms returns 0
TA260 000:560.322 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:560.326 - 0.003ms returns 0
TA260 000:560.330 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:560.335 - 0.004ms returns 0x00000002
TA260 000:560.339 JLINK_Go()
TA260 000:560.350   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:562.964 - 2.624ms 
TA260 000:562.981 JLINK_IsHalted()
TA260 000:565.318   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:565.799 - 2.817ms returns TRUE
TA260 000:565.807 JLINK_ReadReg(R15 (PC))
TA260 000:565.813 - 0.005ms returns 0x20000000
TA260 000:565.817 JLINK_ClrBPEx(BPHandle = 0x00000002)
TA260 000:565.821 - 0.003ms returns 0x00
TA260 000:565.826 JLINK_ReadReg(R0)
TA260 000:565.829 - 0.003ms returns 0x00000001
TA260 000:565.834 JLINK_HasError()
TA260 000:565.838 JLINK_WriteReg(R0, 0x08000000)
TA260 000:565.842 - 0.003ms returns 0
TA260 000:565.846 JLINK_WriteReg(R1, 0x00004000)
TA260 000:565.850 - 0.003ms returns 0
TA260 000:565.854 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:565.857 - 0.003ms returns 0
TA260 000:565.861 JLINK_WriteReg(R3, 0x00000000)
TA260 000:565.865 - 0.003ms returns 0
TA260 000:565.869 JLINK_WriteReg(R4, 0x00000000)
TA260 000:565.872 - 0.003ms returns 0
TA260 000:565.876 JLINK_WriteReg(R5, 0x00000000)
TA260 000:565.880 - 0.003ms returns 0
TA260 000:565.884 JLINK_WriteReg(R6, 0x00000000)
TA260 000:565.888 - 0.003ms returns 0
TA260 000:565.893 JLINK_WriteReg(R7, 0x00000000)
TA260 000:565.896 - 0.003ms returns 0
TA260 000:565.900 JLINK_WriteReg(R8, 0x00000000)
TA260 000:565.904 - 0.003ms returns 0
TA260 000:565.908 JLINK_WriteReg(R9, 0x20000180)
TA260 000:565.911 - 0.003ms returns 0
TA260 000:565.915 JLINK_WriteReg(R10, 0x00000000)
TA260 000:565.919 - 0.003ms returns 0
TA260 000:565.923 JLINK_WriteReg(R11, 0x00000000)
TA260 000:565.926 - 0.003ms returns 0
TA260 000:565.930 JLINK_WriteReg(R12, 0x00000000)
TA260 000:565.934 - 0.003ms returns 0
TA260 000:565.938 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:565.941 - 0.003ms returns 0
TA260 000:565.945 JLINK_WriteReg(R14, 0x20000001)
TA260 000:565.949 - 0.003ms returns 0
TA260 000:565.953 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 000:565.956 - 0.003ms returns 0
TA260 000:565.960 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:565.964 - 0.003ms returns 0
TA260 000:565.968 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:565.971 - 0.003ms returns 0
TA260 000:565.975 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:565.979 - 0.003ms returns 0
TA260 000:565.983 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:565.986 - 0.003ms returns 0
TA260 000:565.991 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:565.994 - 0.004ms returns 0x00000003
TA260 000:565.999 JLINK_Go()
TA260 000:566.007   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:568.710 - 2.710ms 
TA260 000:568.722 JLINK_IsHalted()
TA260 000:569.187 - 0.464ms returns FALSE
TA260 000:569.195 JLINK_HasError()
TA260 000:579.912 JLINK_IsHalted()
TA260 000:580.361 - 0.449ms returns FALSE
TA260 000:580.379 JLINK_HasError()
TA260 000:581.898 JLINK_IsHalted()
TA260 000:582.373 - 0.475ms returns FALSE
TA260 000:582.386 JLINK_HasError()
TA260 000:583.891 JLINK_IsHalted()
TA260 000:584.389 - 0.497ms returns FALSE
TA260 000:584.395 JLINK_HasError()
TA260 000:586.398 JLINK_IsHalted()
TA260 000:586.822 - 0.423ms returns FALSE
TA260 000:586.834 JLINK_HasError()
TA260 000:588.400 JLINK_IsHalted()
TA260 000:588.856 - 0.456ms returns FALSE
TA260 000:588.863 JLINK_HasError()
TA260 000:590.397 JLINK_IsHalted()
TA260 000:590.916 - 0.518ms returns FALSE
TA260 000:590.930 JLINK_HasError()
TA260 000:592.409 JLINK_IsHalted()
TA260 000:592.863 - 0.454ms returns FALSE
TA260 000:592.878 JLINK_HasError()
TA260 000:594.396 JLINK_IsHalted()
TA260 000:594.891 - 0.495ms returns FALSE
TA260 000:594.900 JLINK_HasError()
TA260 000:596.399 JLINK_IsHalted()
TA260 000:596.820 - 0.421ms returns FALSE
TA260 000:596.834 JLINK_HasError()
TA260 000:597.905 JLINK_IsHalted()
TA260 000:598.347 - 0.442ms returns FALSE
TA260 000:598.356 JLINK_HasError()
TA260 000:599.919 JLINK_IsHalted()
TA260 000:600.370 - 0.450ms returns FALSE
TA260 000:600.379 JLINK_HasError()
TA260 000:601.907 JLINK_IsHalted()
TA260 000:602.411 - 0.503ms returns FALSE
TA260 000:602.430 JLINK_HasError()
TA260 000:603.904 JLINK_IsHalted()
TA260 000:604.401 - 0.496ms returns FALSE
TA260 000:604.409 JLINK_HasError()
TA260 000:606.415 JLINK_IsHalted()
TA260 000:606.891 - 0.475ms returns FALSE
TA260 000:606.902 JLINK_HasError()
TA260 000:608.415 JLINK_IsHalted()
TA260 000:608.858 - 0.443ms returns FALSE
TA260 000:608.867 JLINK_HasError()
TA260 000:611.414 JLINK_IsHalted()
TA260 000:611.945 - 0.530ms returns FALSE
TA260 000:611.963 JLINK_HasError()
TA260 000:613.419 JLINK_IsHalted()
TA260 000:613.892 - 0.472ms returns FALSE
TA260 000:613.908 JLINK_HasError()
TA260 000:615.127 JLINK_IsHalted()
TA260 000:615.627 - 0.499ms returns FALSE
TA260 000:615.636 JLINK_HasError()
TA260 000:617.638 JLINK_IsHalted()
TA260 000:618.140 - 0.502ms returns FALSE
TA260 000:618.147 JLINK_HasError()
TA260 000:619.640 JLINK_IsHalted()
TA260 000:620.128 - 0.487ms returns FALSE
TA260 000:620.135 JLINK_HasError()
TA260 000:621.638 JLINK_IsHalted()
TA260 000:622.158 - 0.519ms returns FALSE
TA260 000:622.165 JLINK_HasError()
TA260 000:623.641 JLINK_IsHalted()
TA260 000:624.168 - 0.527ms returns FALSE
TA260 000:624.175 JLINK_HasError()
TA260 000:626.145 JLINK_IsHalted()
TA260 000:626.663 - 0.517ms returns FALSE
TA260 000:626.675 JLINK_HasError()
TA260 000:628.147 JLINK_IsHalted()
TA260 000:628.628 - 0.480ms returns FALSE
TA260 000:628.638 JLINK_HasError()
TA260 000:630.149 JLINK_IsHalted()
TA260 000:630.626 - 0.477ms returns FALSE
TA260 000:630.633 JLINK_HasError()
TA260 000:632.152 JLINK_IsHalted()
TA260 000:632.665 - 0.513ms returns FALSE
TA260 000:632.672 JLINK_HasError()
TA260 000:634.154 JLINK_IsHalted()
TA260 000:634.624 - 0.469ms returns FALSE
TA260 000:634.632 JLINK_HasError()
TA260 000:636.149 JLINK_IsHalted()
TA260 000:636.618 - 0.468ms returns FALSE
TA260 000:636.627 JLINK_HasError()
TA260 000:638.660 JLINK_IsHalted()
TA260 000:639.177 - 0.518ms returns FALSE
TA260 000:639.185 JLINK_HasError()
TA260 000:643.658 JLINK_IsHalted()
TA260 000:644.141 - 0.482ms returns FALSE
TA260 000:644.148 JLINK_HasError()
TA260 000:645.296 JLINK_IsHalted()
TA260 000:645.764 - 0.467ms returns FALSE
TA260 000:645.772 JLINK_HasError()
TA260 000:647.309 JLINK_IsHalted()
TA260 000:647.798 - 0.489ms returns FALSE
TA260 000:647.806 JLINK_HasError()
TA260 000:649.310 JLINK_IsHalted()
TA260 000:649.767 - 0.456ms returns FALSE
TA260 000:649.775 JLINK_HasError()
TA260 000:651.316 JLINK_IsHalted()
TA260 000:651.867 - 0.550ms returns FALSE
TA260 000:651.911 JLINK_HasError()
TA260 000:653.311 JLINK_IsHalted()
TA260 000:653.844 - 0.532ms returns FALSE
TA260 000:653.855 JLINK_HasError()
TA260 000:655.315 JLINK_IsHalted()
TA260 000:655.853 - 0.538ms returns FALSE
TA260 000:655.864 JLINK_HasError()
TA260 000:657.413 JLINK_IsHalted()
TA260 000:657.857 - 0.444ms returns FALSE
TA260 000:657.864 JLINK_HasError()
TA260 000:659.417 JLINK_IsHalted()
TA260 000:659.928 - 0.511ms returns FALSE
TA260 000:659.935 JLINK_HasError()
TA260 000:661.417 JLINK_IsHalted()
TA260 000:661.939 - 0.521ms returns FALSE
TA260 000:661.947 JLINK_HasError()
TA260 000:663.414 JLINK_IsHalted()
TA260 000:663.901 - 0.486ms returns FALSE
TA260 000:663.907 JLINK_HasError()
TA260 000:665.413 JLINK_IsHalted()
TA260 000:665.858 - 0.444ms returns FALSE
TA260 000:665.867 JLINK_HasError()
TA260 000:666.930 JLINK_IsHalted()
TA260 000:667.412 - 0.482ms returns FALSE
TA260 000:667.422 JLINK_HasError()
TA260 000:668.919 JLINK_IsHalted()
TA260 000:669.437 - 0.517ms returns FALSE
TA260 000:669.445 JLINK_HasError()
TA260 000:671.428 JLINK_IsHalted()
TA260 000:671.997 - 0.568ms returns FALSE
TA260 000:672.050 JLINK_HasError()
TA260 000:673.424 JLINK_IsHalted()
TA260 000:673.858 - 0.432ms returns FALSE
TA260 000:673.867 JLINK_HasError()
TA260 000:675.428 JLINK_IsHalted()
TA260 000:675.938 - 0.509ms returns FALSE
TA260 000:675.945 JLINK_HasError()
TA260 000:677.943 JLINK_IsHalted()
TA260 000:678.426 - 0.483ms returns FALSE
TA260 000:678.432 JLINK_HasError()
TA260 000:679.932 JLINK_IsHalted()
TA260 000:680.392 - 0.459ms returns FALSE
TA260 000:680.405 JLINK_HasError()
TA260 000:682.839 JLINK_IsHalted()
TA260 000:683.359 - 0.520ms returns FALSE
TA260 000:683.365 JLINK_HasError()
TA260 000:686.344 JLINK_IsHalted()
TA260 000:686.861 - 0.516ms returns FALSE
TA260 000:686.870 JLINK_HasError()
TA260 000:688.345 JLINK_IsHalted()
TA260 000:688.853 - 0.507ms returns FALSE
TA260 000:688.861 JLINK_HasError()
TA260 000:690.345 JLINK_IsHalted()
TA260 000:690.846 - 0.500ms returns FALSE
TA260 000:690.853 JLINK_HasError()
TA260 000:692.345 JLINK_IsHalted()
TA260 000:692.811 - 0.466ms returns FALSE
TA260 000:692.818 JLINK_HasError()
TA260 000:693.937 JLINK_IsHalted()
TA260 000:694.436 - 0.499ms returns FALSE
TA260 000:694.446 JLINK_HasError()
TA260 000:695.943 JLINK_IsHalted()
TA260 000:696.400 - 0.457ms returns FALSE
TA260 000:696.407 JLINK_HasError()
TA260 000:697.444 JLINK_IsHalted()
TA260 000:697.935 - 0.490ms returns FALSE
TA260 000:697.943 JLINK_HasError()
TA260 000:700.448 JLINK_IsHalted()
TA260 000:700.985 - 0.536ms returns FALSE
TA260 000:700.996 JLINK_HasError()
TA260 000:702.445 JLINK_IsHalted()
TA260 000:702.940 - 0.494ms returns FALSE
TA260 000:702.946 JLINK_HasError()
TA260 000:704.447 JLINK_IsHalted()
TA260 000:704.939 - 0.491ms returns FALSE
TA260 000:704.949 JLINK_HasError()
TA260 000:706.954 JLINK_IsHalted()
TA260 000:707.441 - 0.486ms returns FALSE
TA260 000:707.457 JLINK_HasError()
TA260 000:708.952 JLINK_IsHalted()
TA260 000:709.475 - 0.523ms returns FALSE
TA260 000:709.487 JLINK_HasError()
TA260 000:710.957 JLINK_IsHalted()
TA260 000:711.462 - 0.504ms returns FALSE
TA260 000:711.469 JLINK_HasError()
TA260 000:712.956 JLINK_IsHalted()
TA260 000:713.424 - 0.468ms returns FALSE
TA260 000:713.432 JLINK_HasError()
TA260 000:714.955 JLINK_IsHalted()
TA260 000:715.495 - 0.538ms returns FALSE
TA260 000:715.504 JLINK_HasError()
TA260 000:717.462 JLINK_IsHalted()
TA260 000:717.946 - 0.483ms returns FALSE
TA260 000:717.953 JLINK_HasError()
TA260 000:719.465 JLINK_IsHalted()
TA260 000:719.946 - 0.480ms returns FALSE
TA260 000:719.953 JLINK_HasError()
TA260 000:721.466 JLINK_IsHalted()
TA260 000:721.958 - 0.492ms returns FALSE
TA260 000:721.972 JLINK_HasError()
TA260 000:723.460 JLINK_IsHalted()
TA260 000:723.937 - 0.475ms returns FALSE
TA260 000:723.944 JLINK_HasError()
TA260 000:726.971 JLINK_IsHalted()
TA260 000:727.474 - 0.502ms returns FALSE
TA260 000:727.483 JLINK_HasError()
TA260 000:728.968 JLINK_IsHalted()
TA260 000:729.426 - 0.457ms returns FALSE
TA260 000:729.432 JLINK_HasError()
TA260 000:730.825 JLINK_IsHalted()
TA260 000:731.297 - 0.471ms returns FALSE
TA260 000:731.306 JLINK_HasError()
TA260 000:732.825 JLINK_IsHalted()
TA260 000:733.315 - 0.489ms returns FALSE
TA260 000:733.323 JLINK_HasError()
TA260 000:734.824 JLINK_IsHalted()
TA260 000:735.312 - 0.487ms returns FALSE
TA260 000:735.321 JLINK_HasError()
TA260 000:737.326 JLINK_IsHalted()
TA260 000:737.796 - 0.469ms returns FALSE
TA260 000:737.803 JLINK_HasError()
TA260 000:739.324 JLINK_IsHalted()
TA260 000:739.772 - 0.447ms returns FALSE
TA260 000:739.779 JLINK_HasError()
TA260 000:741.329 JLINK_IsHalted()
TA260 000:741.856 - 0.526ms returns FALSE
TA260 000:741.863 JLINK_HasError()
TA260 000:743.327 JLINK_IsHalted()
TA260 000:743.798 - 0.471ms returns FALSE
TA260 000:743.804 JLINK_HasError()
TA260 000:745.327 JLINK_IsHalted()
TA260 000:745.810 - 0.483ms returns FALSE
TA260 000:745.817 JLINK_HasError()
TA260 000:747.835 JLINK_IsHalted()
TA260 000:748.299 - 0.463ms returns FALSE
TA260 000:748.309 JLINK_HasError()
TA260 000:750.836 JLINK_IsHalted()
TA260 000:751.302 - 0.465ms returns FALSE
TA260 000:751.309 JLINK_HasError()
TA260 000:752.836 JLINK_IsHalted()
TA260 000:753.265 - 0.429ms returns FALSE
TA260 000:753.274 JLINK_HasError()
TA260 000:754.833 JLINK_IsHalted()
TA260 000:755.298 - 0.464ms returns FALSE
TA260 000:755.306 JLINK_HasError()
TA260 000:757.343 JLINK_IsHalted()
TA260 000:757.848 - 0.505ms returns FALSE
TA260 000:757.858 JLINK_HasError()
TA260 000:759.344 JLINK_IsHalted()
TA260 000:759.968 - 0.466ms returns FALSE
TA260 000:759.980 JLINK_HasError()
TA260 000:762.350 JLINK_IsHalted()
TA260 000:762.817 - 0.467ms returns FALSE
TA260 000:762.825 JLINK_HasError()
TA260 000:764.343 JLINK_IsHalted()
TA260 000:764.788 - 0.445ms returns FALSE
TA260 000:764.795 JLINK_HasError()
TA260 000:766.345 JLINK_IsHalted()
TA260 000:766.847 - 0.502ms returns FALSE
TA260 000:766.854 JLINK_HasError()
TA260 000:768.849 JLINK_IsHalted()
TA260 000:769.313 - 0.463ms returns FALSE
TA260 000:769.319 JLINK_HasError()
TA260 000:771.354 JLINK_IsHalted()
TA260 000:771.850 - 0.495ms returns FALSE
TA260 000:771.863 JLINK_HasError()
TA260 000:773.354 JLINK_IsHalted()
TA260 000:773.846 - 0.491ms returns FALSE
TA260 000:773.856 JLINK_HasError()
TA260 000:775.299 JLINK_IsHalted()
TA260 000:775.765 - 0.466ms returns FALSE
TA260 000:775.772 JLINK_HasError()
TA260 000:777.807 JLINK_IsHalted()
TA260 000:778.323 - 0.516ms returns FALSE
TA260 000:778.330 JLINK_HasError()
TA260 000:780.807 JLINK_IsHalted()
TA260 000:781.344 - 0.536ms returns FALSE
TA260 000:781.351 JLINK_HasError()
TA260 000:782.807 JLINK_IsHalted()
TA260 000:783.314 - 0.506ms returns FALSE
TA260 000:783.328 JLINK_HasError()
TA260 000:784.808 JLINK_IsHalted()
TA260 000:785.313 - 0.504ms returns FALSE
TA260 000:785.322 JLINK_HasError()
TA260 000:787.311 JLINK_IsHalted()
TA260 000:787.847 - 0.535ms returns FALSE
TA260 000:787.855 JLINK_HasError()
TA260 000:789.310 JLINK_IsHalted()
TA260 000:789.770 - 0.459ms returns FALSE
TA260 000:789.803 JLINK_HasError()
TA260 000:791.311 JLINK_IsHalted()
TA260 000:791.770 - 0.458ms returns FALSE
TA260 000:791.777 JLINK_HasError()
TA260 000:793.313 JLINK_IsHalted()
TA260 000:793.772 - 0.458ms returns FALSE
TA260 000:793.780 JLINK_HasError()
TA260 000:795.315 JLINK_IsHalted()
TA260 000:795.856 - 0.541ms returns FALSE
TA260 000:795.865 JLINK_HasError()
TA260 000:797.821 JLINK_IsHalted()
TA260 000:798.348 - 0.526ms returns FALSE
TA260 000:798.366 JLINK_HasError()
TA260 000:799.829 JLINK_IsHalted()
TA260 000:800.259 - 0.429ms returns FALSE
TA260 000:800.273 JLINK_HasError()
TA260 000:801.819 JLINK_IsHalted()
TA260 000:802.310 - 0.491ms returns FALSE
TA260 000:802.322 JLINK_HasError()
TA260 000:803.820 JLINK_IsHalted()
TA260 000:804.244 - 0.423ms returns FALSE
TA260 000:804.253 JLINK_HasError()
TA260 000:806.327 JLINK_IsHalted()
TA260 000:806.801 - 0.474ms returns FALSE
TA260 000:806.809 JLINK_HasError()
TA260 000:808.325 JLINK_IsHalted()
TA260 000:808.769 - 0.444ms returns FALSE
TA260 000:808.777 JLINK_HasError()
TA260 000:811.332 JLINK_IsHalted()
TA260 000:811.849 - 0.516ms returns FALSE
TA260 000:811.856 JLINK_HasError()
TA260 000:813.325 JLINK_IsHalted()
TA260 000:813.799 - 0.473ms returns FALSE
TA260 000:813.807 JLINK_HasError()
TA260 000:815.326 JLINK_IsHalted()
TA260 000:815.802 - 0.476ms returns FALSE
TA260 000:815.810 JLINK_HasError()
TA260 000:817.835 JLINK_IsHalted()
TA260 000:818.357 - 0.522ms returns FALSE
TA260 000:818.366 JLINK_HasError()
TA260 000:820.837 JLINK_IsHalted()
TA260 000:821.318 - 0.480ms returns FALSE
TA260 000:821.325 JLINK_HasError()
TA260 000:822.837 JLINK_IsHalted()
TA260 000:823.367 - 0.530ms returns FALSE
TA260 000:823.379 JLINK_HasError()
TA260 000:824.832 JLINK_IsHalted()
TA260 000:825.335 - 0.502ms returns FALSE
TA260 000:825.344 JLINK_HasError()
TA260 000:828.348 JLINK_IsHalted()
TA260 000:828.816 - 0.468ms returns FALSE
TA260 000:828.823 JLINK_HasError()
TA260 000:830.343 JLINK_IsHalted()
TA260 000:830.814 - 0.471ms returns FALSE
TA260 000:830.822 JLINK_HasError()
TA260 000:832.345 JLINK_IsHalted()
TA260 000:832.788 - 0.443ms returns FALSE
TA260 000:832.843 JLINK_HasError()
TA260 000:834.340 JLINK_IsHalted()
TA260 000:834.767 - 0.427ms returns FALSE
TA260 000:834.774 JLINK_HasError()
TA260 000:836.340 JLINK_IsHalted()
TA260 000:836.807 - 0.467ms returns FALSE
TA260 000:836.814 JLINK_HasError()
TA260 000:837.848 JLINK_IsHalted()
TA260 000:838.357 - 0.509ms returns FALSE
TA260 000:838.513 JLINK_HasError()
TA260 000:839.861 JLINK_IsHalted()
TA260 000:840.346 - 0.484ms returns FALSE
TA260 000:840.354 JLINK_HasError()
TA260 000:841.855 JLINK_IsHalted()
TA260 000:842.440 - 0.585ms returns FALSE
TA260 000:842.451 JLINK_HasError()
TA260 000:843.850 JLINK_IsHalted()
TA260 000:844.347 - 0.496ms returns FALSE
TA260 000:844.356 JLINK_HasError()
TA260 000:847.358 JLINK_IsHalted()
TA260 000:847.860 - 0.502ms returns FALSE
TA260 000:847.868 JLINK_HasError()
TA260 000:849.355 JLINK_IsHalted()
TA260 000:849.767 - 0.411ms returns FALSE
TA260 000:849.773 JLINK_HasError()
TA260 000:851.359 JLINK_IsHalted()
TA260 000:851.899 - 0.539ms returns FALSE
TA260 000:851.916 JLINK_HasError()
TA260 000:854.361 JLINK_IsHalted()
TA260 000:854.847 - 0.486ms returns FALSE
TA260 000:854.854 JLINK_HasError()
TA260 000:856.358 JLINK_IsHalted()
TA260 000:856.867 - 0.509ms returns FALSE
TA260 000:856.875 JLINK_HasError()
TA260 000:858.579 JLINK_IsHalted()
TA260 000:859.016 - 0.437ms returns FALSE
TA260 000:859.023 JLINK_HasError()
TA260 000:861.585 JLINK_IsHalted()
TA260 000:862.101 - 0.515ms returns FALSE
TA260 000:862.116 JLINK_HasError()
TA260 000:863.584 JLINK_IsHalted()
TA260 000:864.084 - 0.499ms returns FALSE
TA260 000:864.094 JLINK_HasError()
TA260 000:866.085 JLINK_IsHalted()
TA260 000:866.627 - 0.542ms returns FALSE
TA260 000:866.635 JLINK_HasError()
TA260 000:869.088 JLINK_IsHalted()
TA260 000:869.621 - 0.532ms returns FALSE
TA260 000:869.629 JLINK_HasError()
TA260 000:871.592 JLINK_IsHalted()
TA260 000:872.095 - 0.502ms returns FALSE
TA260 000:872.101 JLINK_HasError()
TA260 000:873.593 JLINK_IsHalted()
TA260 000:874.131 - 0.538ms returns FALSE
TA260 000:874.146 JLINK_HasError()
TA260 000:876.095 JLINK_IsHalted()
TA260 000:876.626 - 0.530ms returns FALSE
TA260 000:876.633 JLINK_HasError()
TA260 000:878.099 JLINK_IsHalted()
TA260 000:878.629 - 0.529ms returns FALSE
TA260 000:878.635 JLINK_HasError()
TA260 000:880.104 JLINK_IsHalted()
TA260 000:880.619 - 0.514ms returns FALSE
TA260 000:880.629 JLINK_HasError()
TA260 000:883.095 JLINK_IsHalted()
TA260 000:883.628 - 0.532ms returns FALSE
TA260 000:883.634 JLINK_HasError()
TA260 000:885.096 JLINK_IsHalted()
TA260 000:885.631 - 0.535ms returns FALSE
TA260 000:885.642 JLINK_HasError()
TA260 000:887.609 JLINK_IsHalted()
TA260 000:888.118 - 0.509ms returns FALSE
TA260 000:888.125 JLINK_HasError()
TA260 000:889.607 JLINK_IsHalted()
TA260 000:890.083 - 0.476ms returns FALSE
TA260 000:890.104 JLINK_HasError()
TA260 000:891.611 JLINK_IsHalted()
TA260 000:892.125 - 0.514ms returns FALSE
TA260 000:892.141 JLINK_HasError()
TA260 000:893.605 JLINK_IsHalted()
TA260 000:894.119 - 0.514ms returns FALSE
TA260 000:894.128 JLINK_HasError()
TA260 000:896.116 JLINK_IsHalted()
TA260 000:896.666 - 0.550ms returns FALSE
TA260 000:896.674 JLINK_HasError()
TA260 000:898.116 JLINK_IsHalted()
TA260 000:898.616 - 0.500ms returns FALSE
TA260 000:898.624 JLINK_HasError()
TA260 000:900.114 JLINK_IsHalted()
TA260 000:900.616 - 0.501ms returns FALSE
TA260 000:900.626 JLINK_HasError()
TA260 000:902.115 JLINK_IsHalted()
TA260 000:902.628 - 0.513ms returns FALSE
TA260 000:902.639 JLINK_HasError()
TA260 000:904.112 JLINK_IsHalted()
TA260 000:904.617 - 0.505ms returns FALSE
TA260 000:904.626 JLINK_HasError()
TA260 000:908.627 JLINK_IsHalted()
TA260 000:909.088 - 0.461ms returns FALSE
TA260 000:909.096 JLINK_HasError()
TA260 000:910.621 JLINK_IsHalted()
TA260 000:911.121 - 0.499ms returns FALSE
TA260 000:911.129 JLINK_HasError()
TA260 000:912.622 JLINK_IsHalted()
TA260 000:913.069 - 0.447ms returns FALSE
TA260 000:913.077 JLINK_HasError()
TA260 000:914.621 JLINK_IsHalted()
TA260 000:915.082 - 0.460ms returns FALSE
TA260 000:915.090 JLINK_HasError()
TA260 000:917.124 JLINK_IsHalted()
TA260 000:917.743 - 0.618ms returns FALSE
TA260 000:917.755 JLINK_HasError()
TA260 000:919.127 JLINK_IsHalted()
TA260 000:919.618 - 0.491ms returns FALSE
TA260 000:919.625 JLINK_HasError()
TA260 000:921.130 JLINK_IsHalted()
TA260 000:921.618 - 0.488ms returns FALSE
TA260 000:921.627 JLINK_HasError()
TA260 000:923.128 JLINK_IsHalted()
TA260 000:923.698 - 0.569ms returns FALSE
TA260 000:923.706 JLINK_HasError()
TA260 000:926.127 JLINK_IsHalted()
TA260 000:926.665 - 0.537ms returns FALSE
TA260 000:926.671 JLINK_HasError()
TA260 000:928.637 JLINK_IsHalted()
TA260 000:929.133 - 0.496ms returns FALSE
TA260 000:929.140 JLINK_HasError()
TA260 000:930.634 JLINK_IsHalted()
TA260 000:931.155 - 0.520ms returns FALSE
TA260 000:931.173 JLINK_HasError()
TA260 000:932.632 JLINK_IsHalted()
TA260 000:933.142 - 0.509ms returns FALSE
TA260 000:933.151 JLINK_HasError()
TA260 000:934.635 JLINK_IsHalted()
TA260 000:935.129 - 0.494ms returns FALSE
TA260 000:935.137 JLINK_HasError()
TA260 000:937.140 JLINK_IsHalted()
TA260 000:937.621 - 0.481ms returns FALSE
TA260 000:937.633 JLINK_HasError()
TA260 000:939.144 JLINK_IsHalted()
TA260 000:939.619 - 0.475ms returns FALSE
TA260 000:939.630 JLINK_HasError()
TA260 000:941.142 JLINK_IsHalted()
TA260 000:941.663 - 0.520ms returns FALSE
TA260 000:941.669 JLINK_HasError()
TA260 000:943.141 JLINK_IsHalted()
TA260 000:943.629 - 0.487ms returns FALSE
TA260 000:943.637 JLINK_HasError()
TA260 000:945.143 JLINK_IsHalted()
TA260 000:945.629 - 0.485ms returns FALSE
TA260 000:945.635 JLINK_HasError()
TA260 000:947.652 JLINK_IsHalted()
TA260 000:948.150 - 0.498ms returns FALSE
TA260 000:948.160 JLINK_HasError()
TA260 000:950.652 JLINK_IsHalted()
TA260 000:951.121 - 0.468ms returns FALSE
TA260 000:951.128 JLINK_HasError()
TA260 000:952.644 JLINK_IsHalted()
TA260 000:953.108 - 0.462ms returns FALSE
TA260 000:953.115 JLINK_HasError()
TA260 000:954.649 JLINK_IsHalted()
TA260 000:955.127 - 0.478ms returns FALSE
TA260 000:955.135 JLINK_HasError()
TA260 000:957.155 JLINK_IsHalted()
TA260 000:959.497   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:960.012 - 2.856ms returns TRUE
TA260 000:960.026 JLINK_ReadReg(R15 (PC))
TA260 000:960.032 - 0.006ms returns 0x20000000
TA260 000:960.037 JLINK_ClrBPEx(BPHandle = 0x00000003)
TA260 000:960.041 - 0.004ms returns 0x00
TA260 000:960.046 JLINK_ReadReg(R0)
TA260 000:960.049 - 0.003ms returns 0x00000000
TA260 000:960.474 JLINK_HasError()
TA260 000:960.488 JLINK_WriteReg(R0, 0x08004000)
TA260 000:960.493 - 0.005ms returns 0
TA260 000:960.497 JLINK_WriteReg(R1, 0x00004000)
TA260 000:960.501 - 0.003ms returns 0
TA260 000:960.505 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:960.509 - 0.004ms returns 0
TA260 000:960.513 JLINK_WriteReg(R3, 0x00000000)
TA260 000:960.516 - 0.003ms returns 0
TA260 000:960.520 JLINK_WriteReg(R4, 0x00000000)
TA260 000:960.523 - 0.003ms returns 0
TA260 000:960.528 JLINK_WriteReg(R5, 0x00000000)
TA260 000:960.531 - 0.003ms returns 0
TA260 000:960.535 JLINK_WriteReg(R6, 0x00000000)
TA260 000:960.538 - 0.003ms returns 0
TA260 000:960.543 JLINK_WriteReg(R7, 0x00000000)
TA260 000:960.546 - 0.003ms returns 0
TA260 000:960.550 JLINK_WriteReg(R8, 0x00000000)
TA260 000:960.553 - 0.003ms returns 0
TA260 000:960.557 JLINK_WriteReg(R9, 0x20000180)
TA260 000:960.561 - 0.003ms returns 0
TA260 000:960.565 JLINK_WriteReg(R10, 0x00000000)
TA260 000:960.568 - 0.003ms returns 0
TA260 000:960.572 JLINK_WriteReg(R11, 0x00000000)
TA260 000:960.576 - 0.003ms returns 0
TA260 000:960.580 JLINK_WriteReg(R12, 0x00000000)
TA260 000:960.583 - 0.003ms returns 0
TA260 000:960.587 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:960.591 - 0.003ms returns 0
TA260 000:960.595 JLINK_WriteReg(R14, 0x20000001)
TA260 000:960.599 - 0.003ms returns 0
TA260 000:960.603 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 000:960.606 - 0.003ms returns 0
TA260 000:960.610 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:960.618 - 0.007ms returns 0
TA260 000:960.624 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:960.627 - 0.003ms returns 0
TA260 000:960.631 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:960.635 - 0.003ms returns 0
TA260 000:960.639 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:960.642 - 0.003ms returns 0
TA260 000:960.647 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:960.651 - 0.004ms returns 0x00000004
TA260 000:960.655 JLINK_Go()
TA260 000:960.665   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:963.382 - 2.726ms 
TA260 000:963.394 JLINK_IsHalted()
TA260 000:965.711   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:966.225 - 2.831ms returns TRUE
TA260 000:966.238 JLINK_ReadReg(R15 (PC))
TA260 000:966.243 - 0.005ms returns 0x20000000
TA260 000:966.249 JLINK_ClrBPEx(BPHandle = 0x00000004)
TA260 000:966.252 - 0.003ms returns 0x00
TA260 000:966.257 JLINK_ReadReg(R0)
TA260 000:966.261 - 0.003ms returns 0x00000001
TA260 000:966.266 JLINK_HasError()
TA260 000:966.270 JLINK_WriteReg(R0, 0x08004000)
TA260 000:966.274 - 0.004ms returns 0
TA260 000:966.278 JLINK_WriteReg(R1, 0x00004000)
TA260 000:966.282 - 0.003ms returns 0
TA260 000:966.286 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:966.289 - 0.003ms returns 0
TA260 000:966.293 JLINK_WriteReg(R3, 0x00000000)
TA260 000:966.297 - 0.003ms returns 0
TA260 000:966.301 JLINK_WriteReg(R4, 0x00000000)
TA260 000:966.305 - 0.003ms returns 0
TA260 000:966.309 JLINK_WriteReg(R5, 0x00000000)
TA260 000:966.312 - 0.003ms returns 0
TA260 000:966.316 JLINK_WriteReg(R6, 0x00000000)
TA260 000:966.320 - 0.003ms returns 0
TA260 000:966.324 JLINK_WriteReg(R7, 0x00000000)
TA260 000:966.327 - 0.003ms returns 0
TA260 000:966.331 JLINK_WriteReg(R8, 0x00000000)
TA260 000:966.335 - 0.003ms returns 0
TA260 000:966.339 JLINK_WriteReg(R9, 0x20000180)
TA260 000:966.342 - 0.003ms returns 0
TA260 000:966.346 JLINK_WriteReg(R10, 0x00000000)
TA260 000:966.350 - 0.003ms returns 0
TA260 000:966.354 JLINK_WriteReg(R11, 0x00000000)
TA260 000:966.357 - 0.003ms returns 0
TA260 000:966.361 JLINK_WriteReg(R12, 0x00000000)
TA260 000:966.365 - 0.003ms returns 0
TA260 000:966.369 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:966.373 - 0.003ms returns 0
TA260 000:966.377 JLINK_WriteReg(R14, 0x20000001)
TA260 000:966.380 - 0.003ms returns 0
TA260 000:966.385 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 000:966.389 - 0.003ms returns 0
TA260 000:966.393 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:966.396 - 0.003ms returns 0
TA260 000:966.400 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:966.404 - 0.003ms returns 0
TA260 000:966.408 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:966.411 - 0.003ms returns 0
TA260 000:966.415 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:966.419 - 0.003ms returns 0
TA260 000:966.424 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:966.428 - 0.004ms returns 0x00000005
TA260 000:966.432 JLINK_Go()
TA260 000:966.440   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:969.211 - 2.778ms 
TA260 000:969.230 JLINK_IsHalted()
TA260 000:969.755 - 0.525ms returns FALSE
TA260 000:969.762 JLINK_HasError()
TA260 000:973.174 JLINK_IsHalted()
TA260 000:973.759 - 0.584ms returns FALSE
TA260 000:973.767 JLINK_HasError()
TA260 000:975.164 JLINK_IsHalted()
TA260 000:975.646 - 0.481ms returns FALSE
TA260 000:975.665 JLINK_HasError()
TA260 000:977.669 JLINK_IsHalted()
TA260 000:978.159 - 0.490ms returns FALSE
TA260 000:978.165 JLINK_HasError()
TA260 000:980.671 JLINK_IsHalted()
TA260 000:981.212 - 0.540ms returns FALSE
TA260 000:981.219 JLINK_HasError()
TA260 000:982.668 JLINK_IsHalted()
TA260 000:983.219 - 0.550ms returns FALSE
TA260 000:983.231 JLINK_HasError()
TA260 000:986.178 JLINK_IsHalted()
TA260 000:986.626 - 0.447ms returns FALSE
TA260 000:986.633 JLINK_HasError()
TA260 000:988.177 JLINK_IsHalted()
TA260 000:988.629 - 0.452ms returns FALSE
TA260 000:988.642 JLINK_HasError()
TA260 000:990.177 JLINK_IsHalted()
TA260 000:990.759 - 0.581ms returns FALSE
TA260 000:990.772 JLINK_HasError()
TA260 000:992.184 JLINK_IsHalted()
TA260 000:992.697 - 0.512ms returns FALSE
TA260 000:992.709 JLINK_HasError()
TA260 000:994.174 JLINK_IsHalted()
TA260 000:994.648 - 0.473ms returns FALSE
TA260 000:994.654 JLINK_HasError()
TA260 000:996.178 JLINK_IsHalted()
TA260 000:996.721 - 0.542ms returns FALSE
TA260 000:996.727 JLINK_HasError()
TA260 000:998.680 JLINK_IsHalted()
TA260 000:999.159 - 0.478ms returns FALSE
TA260 000:999.165 JLINK_HasError()
TA260 001:000.682 JLINK_IsHalted()
TA260 001:001.184 - 0.501ms returns FALSE
TA260 001:001.195 JLINK_HasError()
TA260 001:003.682 JLINK_IsHalted()
TA260 001:004.173 - 0.490ms returns FALSE
TA260 001:004.179 JLINK_HasError()
TA260 001:006.182 JLINK_IsHalted()
TA260 001:006.720 - 0.537ms returns FALSE
TA260 001:006.726 JLINK_HasError()
TA260 001:008.186 JLINK_IsHalted()
TA260 001:008.661 - 0.474ms returns FALSE
TA260 001:008.667 JLINK_HasError()
TA260 001:010.186 JLINK_IsHalted()
TA260 001:010.669 - 0.482ms returns FALSE
TA260 001:010.674 JLINK_HasError()
TA260 001:012.186 JLINK_IsHalted()
TA260 001:012.666 - 0.480ms returns FALSE
TA260 001:012.672 JLINK_HasError()
TA260 001:014.185 JLINK_IsHalted()
TA260 001:014.662 - 0.476ms returns FALSE
TA260 001:014.667 JLINK_HasError()
TA260 001:016.190 JLINK_IsHalted()
TA260 001:016.759 - 0.569ms returns FALSE
TA260 001:016.771 JLINK_HasError()
TA260 001:018.695 JLINK_IsHalted()
TA260 001:019.189 - 0.493ms returns FALSE
TA260 001:019.195 JLINK_HasError()
TA260 001:020.694 JLINK_IsHalted()
TA260 001:021.242 - 0.548ms returns FALSE
TA260 001:021.250 JLINK_HasError()
TA260 001:022.328 JLINK_IsHalted()
TA260 001:022.845 - 0.517ms returns FALSE
TA260 001:022.851 JLINK_HasError()
TA260 001:024.331 JLINK_IsHalted()
TA260 001:024.818 - 0.486ms returns FALSE
TA260 001:024.830 JLINK_HasError()
TA260 001:026.335 JLINK_IsHalted()
TA260 001:026.844 - 0.508ms returns FALSE
TA260 001:026.855 JLINK_HasError()
TA260 001:028.838 JLINK_IsHalted()
TA260 001:029.367 - 0.528ms returns FALSE
TA260 001:029.373 JLINK_HasError()
TA260 001:030.841 JLINK_IsHalted()
TA260 001:031.346 - 0.505ms returns FALSE
TA260 001:031.356 JLINK_HasError()
TA260 001:032.840 JLINK_IsHalted()
TA260 001:033.318 - 0.478ms returns FALSE
TA260 001:033.324 JLINK_HasError()
TA260 001:034.841 JLINK_IsHalted()
TA260 001:035.369 - 0.528ms returns FALSE
TA260 001:035.375 JLINK_HasError()
TA260 001:037.342 JLINK_IsHalted()
TA260 001:037.843 - 0.500ms returns FALSE
TA260 001:037.849 JLINK_HasError()
TA260 001:040.351 JLINK_IsHalted()
TA260 001:040.850 - 0.498ms returns FALSE
TA260 001:040.857 JLINK_HasError()
TA260 001:042.345 JLINK_IsHalted()
TA260 001:042.855 - 0.509ms returns FALSE
TA260 001:042.861 JLINK_HasError()
TA260 001:044.345 JLINK_IsHalted()
TA260 001:044.796 - 0.450ms returns FALSE
TA260 001:044.801 JLINK_HasError()
TA260 001:046.349 JLINK_IsHalted()
TA260 001:046.811 - 0.461ms returns FALSE
TA260 001:046.820 JLINK_HasError()
TA260 001:047.855 JLINK_IsHalted()
TA260 001:048.308 - 0.453ms returns FALSE
TA260 001:048.317 JLINK_HasError()
TA260 001:049.874 JLINK_IsHalted()
TA260 001:050.426 - 0.551ms returns FALSE
TA260 001:050.435 JLINK_HasError()
TA260 001:051.863 JLINK_IsHalted()
TA260 001:052.368 - 0.504ms returns FALSE
TA260 001:052.382 JLINK_HasError()
TA260 001:053.822 JLINK_IsHalted()
TA260 001:054.322 - 0.499ms returns FALSE
TA260 001:054.329 JLINK_HasError()
TA260 001:056.208 JLINK_IsHalted()
TA260 001:056.709 - 0.500ms returns FALSE
TA260 001:056.718 JLINK_HasError()
TA260 001:058.713 JLINK_IsHalted()
TA260 001:059.173 - 0.459ms returns FALSE
TA260 001:059.179 JLINK_HasError()
TA260 001:060.713 JLINK_IsHalted()
TA260 001:061.209 - 0.495ms returns FALSE
TA260 001:061.216 JLINK_HasError()
TA260 001:062.714 JLINK_IsHalted()
TA260 001:063.177 - 0.462ms returns FALSE
TA260 001:063.191 JLINK_HasError()
TA260 001:066.218 JLINK_IsHalted()
TA260 001:066.671 - 0.453ms returns FALSE
TA260 001:066.684 JLINK_HasError()
TA260 001:068.218 JLINK_IsHalted()
TA260 001:068.694 - 0.475ms returns FALSE
TA260 001:068.700 JLINK_HasError()
TA260 001:070.720 JLINK_IsHalted()
TA260 001:071.231 - 0.511ms returns FALSE
TA260 001:071.238 JLINK_HasError()
TA260 001:072.722 JLINK_IsHalted()
TA260 001:073.210 - 0.488ms returns FALSE
TA260 001:073.217 JLINK_HasError()
TA260 001:076.223 JLINK_IsHalted()
TA260 001:076.751 - 0.527ms returns FALSE
TA260 001:076.757 JLINK_HasError()
TA260 001:078.231 JLINK_IsHalted()
TA260 001:078.664 - 0.432ms returns FALSE
TA260 001:078.671 JLINK_HasError()
TA260 001:081.227 JLINK_IsHalted()
TA260 001:081.664 - 0.437ms returns FALSE
TA260 001:081.671 JLINK_HasError()
TA260 001:083.226 JLINK_IsHalted()
TA260 001:083.718 - 0.491ms returns FALSE
TA260 001:083.724 JLINK_HasError()
TA260 001:085.223 JLINK_IsHalted()
TA260 001:085.764 - 0.540ms returns FALSE
TA260 001:085.770 JLINK_HasError()
TA260 001:087.730 JLINK_IsHalted()
TA260 001:088.206 - 0.476ms returns FALSE
TA260 001:088.212 JLINK_HasError()
TA260 001:089.731 JLINK_IsHalted()
TA260 001:090.186 - 0.454ms returns FALSE
TA260 001:090.198 JLINK_HasError()
TA260 001:091.731 JLINK_IsHalted()
TA260 001:092.212 - 0.480ms returns FALSE
TA260 001:092.225 JLINK_HasError()
TA260 001:093.733 JLINK_IsHalted()
TA260 001:094.222 - 0.488ms returns FALSE
TA260 001:094.229 JLINK_HasError()
TA260 001:096.241 JLINK_IsHalted()
TA260 001:096.768 - 0.526ms returns FALSE
TA260 001:096.780 JLINK_HasError()
TA260 001:098.242 JLINK_IsHalted()
TA260 001:098.754 - 0.511ms returns FALSE
TA260 001:098.760 JLINK_HasError()
TA260 001:100.238 JLINK_IsHalted()
TA260 001:100.766 - 0.528ms returns FALSE
TA260 001:100.772 JLINK_HasError()
TA260 001:102.238 JLINK_IsHalted()
TA260 001:102.750 - 0.512ms returns FALSE
TA260 001:102.760 JLINK_HasError()
TA260 001:104.236 JLINK_IsHalted()
TA260 001:104.705 - 0.469ms returns FALSE
TA260 001:104.711 JLINK_HasError()
TA260 001:106.240 JLINK_IsHalted()
TA260 001:106.763 - 0.523ms returns FALSE
TA260 001:106.771 JLINK_HasError()
TA260 001:108.743 JLINK_IsHalted()
TA260 001:109.297 - 0.554ms returns FALSE
TA260 001:109.308 JLINK_HasError()
TA260 001:110.742 JLINK_IsHalted()
TA260 001:111.185 - 0.442ms returns FALSE
TA260 001:111.194 JLINK_HasError()
TA260 001:112.742 JLINK_IsHalted()
TA260 001:113.241 - 0.498ms returns FALSE
TA260 001:113.247 JLINK_HasError()
TA260 001:114.742 JLINK_IsHalted()
TA260 001:115.182 - 0.439ms returns FALSE
TA260 001:115.188 JLINK_HasError()
TA260 001:116.244 JLINK_IsHalted()
TA260 001:116.764 - 0.519ms returns FALSE
TA260 001:116.769 JLINK_HasError()
TA260 001:118.248 JLINK_IsHalted()
TA260 001:118.764 - 0.515ms returns FALSE
TA260 001:118.770 JLINK_HasError()
TA260 001:120.249 JLINK_IsHalted()
TA260 001:120.769 - 0.520ms returns FALSE
TA260 001:120.781 JLINK_HasError()
TA260 001:122.252 JLINK_IsHalted()
TA260 001:122.756 - 0.504ms returns FALSE
TA260 001:122.767 JLINK_HasError()
TA260 001:124.248 JLINK_IsHalted()
TA260 001:124.752 - 0.503ms returns FALSE
TA260 001:124.762 JLINK_HasError()
TA260 001:126.249 JLINK_IsHalted()
TA260 001:126.764 - 0.515ms returns FALSE
TA260 001:126.770 JLINK_HasError()
TA260 001:128.754 JLINK_IsHalted()
TA260 001:129.229 - 0.475ms returns FALSE
TA260 001:129.235 JLINK_HasError()
TA260 001:130.754 JLINK_IsHalted()
TA260 001:131.220 - 0.465ms returns FALSE
TA260 001:131.232 JLINK_HasError()
TA260 001:132.753 JLINK_IsHalted()
TA260 001:133.262 - 0.508ms returns FALSE
TA260 001:133.267 JLINK_HasError()
TA260 001:134.753 JLINK_IsHalted()
TA260 001:135.239 - 0.486ms returns FALSE
TA260 001:135.245 JLINK_HasError()
TA260 001:137.257 JLINK_IsHalted()
TA260 001:137.765 - 0.508ms returns FALSE
TA260 001:137.771 JLINK_HasError()
TA260 001:139.260 JLINK_IsHalted()
TA260 001:139.750 - 0.490ms returns FALSE
TA260 001:139.759 JLINK_HasError()
TA260 001:141.261 JLINK_IsHalted()
TA260 001:141.670 - 0.408ms returns FALSE
TA260 001:141.682 JLINK_HasError()
TA260 001:145.258 JLINK_IsHalted()
TA260 001:145.751 - 0.492ms returns FALSE
TA260 001:145.757 JLINK_HasError()
TA260 001:147.765 JLINK_IsHalted()
TA260 001:148.261 - 0.495ms returns FALSE
TA260 001:148.271 JLINK_HasError()
TA260 001:149.764 JLINK_IsHalted()
TA260 001:150.264 - 0.499ms returns FALSE
TA260 001:150.274 JLINK_HasError()
TA260 001:151.766 JLINK_IsHalted()
TA260 001:152.296 - 0.529ms returns FALSE
TA260 001:152.309 JLINK_HasError()
TA260 001:153.764 JLINK_IsHalted()
TA260 001:154.260 - 0.495ms returns FALSE
TA260 001:154.267 JLINK_HasError()
TA260 001:156.270 JLINK_IsHalted()
TA260 001:156.904 - 0.633ms returns FALSE
TA260 001:156.916 JLINK_HasError()
TA260 001:158.023 JLINK_IsHalted()
TA260 001:158.538 - 0.514ms returns FALSE
TA260 001:158.551 JLINK_HasError()
TA260 001:160.066 JLINK_IsHalted()
TA260 001:160.550 - 0.484ms returns FALSE
TA260 001:160.556 JLINK_HasError()
TA260 001:161.606 JLINK_IsHalted()
TA260 001:162.094 - 0.487ms returns FALSE
TA260 001:162.100 JLINK_HasError()
TA260 001:163.139 JLINK_IsHalted()
TA260 001:163.662 - 0.521ms returns FALSE
TA260 001:163.671 JLINK_HasError()
TA260 001:165.180 JLINK_IsHalted()
TA260 001:165.661 - 0.481ms returns FALSE
TA260 001:165.667 JLINK_HasError()
TA260 001:166.711 JLINK_IsHalted()
TA260 001:167.209 - 0.497ms returns FALSE
TA260 001:167.217 JLINK_HasError()
TA260 001:168.761 JLINK_IsHalted()
TA260 001:169.263 - 0.501ms returns FALSE
TA260 001:169.272 JLINK_HasError()
TA260 001:170.798 JLINK_IsHalted()
TA260 001:171.219 - 0.420ms returns FALSE
TA260 001:171.234 JLINK_HasError()
TA260 001:172.340 JLINK_IsHalted()
TA260 001:172.766 - 0.426ms returns FALSE
TA260 001:172.778 JLINK_HasError()
TA260 001:173.844 JLINK_IsHalted()
TA260 001:174.309 - 0.464ms returns FALSE
TA260 001:174.314 JLINK_HasError()
TA260 001:176.346 JLINK_IsHalted()
TA260 001:176.842 - 0.496ms returns FALSE
TA260 001:176.848 JLINK_HasError()
TA260 001:178.351 JLINK_IsHalted()
TA260 001:178.843 - 0.492ms returns FALSE
TA260 001:178.856 JLINK_HasError()
TA260 001:180.350 JLINK_IsHalted()
TA260 001:180.810 - 0.459ms returns FALSE
TA260 001:180.817 JLINK_HasError()
TA260 001:182.349 JLINK_IsHalted()
TA260 001:182.858 - 0.508ms returns FALSE
TA260 001:182.864 JLINK_HasError()
TA260 001:184.349 JLINK_IsHalted()
TA260 001:184.845 - 0.495ms returns FALSE
TA260 001:184.851 JLINK_HasError()
TA260 001:186.349 JLINK_IsHalted()
TA260 001:186.896 - 0.547ms returns FALSE
TA260 001:186.904 JLINK_HasError()
TA260 001:188.855 JLINK_IsHalted()
TA260 001:189.358 - 0.503ms returns FALSE
TA260 001:189.366 JLINK_HasError()
TA260 001:191.860 JLINK_IsHalted()
TA260 001:192.311 - 0.451ms returns FALSE
TA260 001:192.324 JLINK_HasError()
TA260 001:193.859 JLINK_IsHalted()
TA260 001:194.332 - 0.472ms returns FALSE
TA260 001:194.339 JLINK_HasError()
TA260 001:196.358 JLINK_IsHalted()
TA260 001:196.857 - 0.498ms returns FALSE
TA260 001:196.862 JLINK_HasError()
TA260 001:199.360 JLINK_IsHalted()
TA260 001:199.963 - 0.603ms returns FALSE
TA260 001:199.971 JLINK_HasError()
TA260 001:201.205 JLINK_IsHalted()
TA260 001:201.652 - 0.446ms returns FALSE
TA260 001:201.665 JLINK_HasError()
TA260 001:202.705 JLINK_IsHalted()
TA260 001:203.193 - 0.487ms returns FALSE
TA260 001:203.199 JLINK_HasError()
TA260 001:204.706 JLINK_IsHalted()
TA260 001:205.152 - 0.445ms returns FALSE
TA260 001:205.157 JLINK_HasError()
TA260 001:206.209 JLINK_IsHalted()
TA260 001:206.756 - 0.546ms returns FALSE
TA260 001:206.764 JLINK_HasError()
TA260 001:208.211 JLINK_IsHalted()
TA260 001:208.662 - 0.451ms returns FALSE
TA260 001:208.668 JLINK_HasError()
TA260 001:210.210 JLINK_IsHalted()
TA260 001:210.767 - 0.556ms returns FALSE
TA260 001:210.774 JLINK_HasError()
TA260 001:212.211 JLINK_IsHalted()
TA260 001:212.755 - 0.544ms returns FALSE
TA260 001:212.762 JLINK_HasError()
TA260 001:214.212 JLINK_IsHalted()
TA260 001:214.765 - 0.553ms returns FALSE
TA260 001:214.773 JLINK_HasError()
TA260 001:216.211 JLINK_IsHalted()
TA260 001:216.661 - 0.450ms returns FALSE
TA260 001:216.667 JLINK_HasError()
TA260 001:217.714 JLINK_IsHalted()
TA260 001:218.184 - 0.469ms returns FALSE
TA260 001:218.192 JLINK_HasError()
TA260 001:219.717 JLINK_IsHalted()
TA260 001:220.348 - 0.629ms returns FALSE
TA260 001:220.358 JLINK_HasError()
TA260 001:221.718 JLINK_IsHalted()
TA260 001:222.174 - 0.455ms returns FALSE
TA260 001:222.188 JLINK_HasError()
TA260 001:223.717 JLINK_IsHalted()
TA260 001:224.242 - 0.524ms returns FALSE
TA260 001:224.249 JLINK_HasError()
TA260 001:226.219 JLINK_IsHalted()
TA260 001:226.720 - 0.500ms returns FALSE
TA260 001:226.726 JLINK_HasError()
TA260 001:228.235 JLINK_IsHalted()
TA260 001:228.750 - 0.514ms returns FALSE
TA260 001:228.756 JLINK_HasError()
TA260 001:230.222 JLINK_IsHalted()
TA260 001:230.765 - 0.542ms returns FALSE
TA260 001:230.774 JLINK_HasError()
TA260 001:232.223 JLINK_IsHalted()
TA260 001:232.668 - 0.444ms returns FALSE
TA260 001:232.674 JLINK_HasError()
TA260 001:234.221 JLINK_IsHalted()
TA260 001:234.696 - 0.474ms returns FALSE
TA260 001:234.701 JLINK_HasError()
TA260 001:236.223 JLINK_IsHalted()
TA260 001:236.753 - 0.529ms returns FALSE
TA260 001:236.759 JLINK_HasError()
TA260 001:238.726 JLINK_IsHalted()
TA260 001:239.185 - 0.458ms returns FALSE
TA260 001:239.192 JLINK_HasError()
TA260 001:240.727 JLINK_IsHalted()
TA260 001:241.194 - 0.466ms returns FALSE
TA260 001:241.203 JLINK_HasError()
TA260 001:242.725 JLINK_IsHalted()
TA260 001:243.183 - 0.457ms returns FALSE
TA260 001:243.189 JLINK_HasError()
TA260 001:244.726 JLINK_IsHalted()
TA260 001:245.217 - 0.491ms returns FALSE
TA260 001:245.223 JLINK_HasError()
TA260 001:247.232 JLINK_IsHalted()
TA260 001:247.719 - 0.487ms returns FALSE
TA260 001:247.726 JLINK_HasError()
TA260 001:249.230 JLINK_IsHalted()
TA260 001:249.689 - 0.458ms returns FALSE
TA260 001:249.695 JLINK_HasError()
TA260 001:251.233 JLINK_IsHalted()
TA260 001:251.768 - 0.534ms returns FALSE
TA260 001:251.778 JLINK_HasError()
TA260 001:253.232 JLINK_IsHalted()
TA260 001:253.751 - 0.519ms returns FALSE
TA260 001:253.757 JLINK_HasError()
TA260 001:255.232 JLINK_IsHalted()
TA260 001:255.751 - 0.518ms returns FALSE
TA260 001:255.757 JLINK_HasError()
TA260 001:257.739 JLINK_IsHalted()
TA260 001:258.264 - 0.524ms returns FALSE
TA260 001:258.270 JLINK_HasError()
TA260 001:259.737 JLINK_IsHalted()
TA260 001:260.254 - 0.516ms returns FALSE
TA260 001:260.261 JLINK_HasError()
TA260 001:261.741 JLINK_IsHalted()
TA260 001:262.219 - 0.477ms returns FALSE
TA260 001:262.225 JLINK_HasError()
TA260 001:263.739 JLINK_IsHalted()
TA260 001:264.220 - 0.481ms returns FALSE
TA260 001:264.227 JLINK_HasError()
TA260 001:266.241 JLINK_IsHalted()
TA260 001:266.765 - 0.523ms returns FALSE
TA260 001:266.771 JLINK_HasError()
TA260 001:268.242 JLINK_IsHalted()
TA260 001:268.707 - 0.464ms returns FALSE
TA260 001:268.713 JLINK_HasError()
TA260 001:269.744 JLINK_IsHalted()
TA260 001:270.301 - 0.557ms returns FALSE
TA260 001:270.309 JLINK_HasError()
TA260 001:271.747 JLINK_IsHalted()
TA260 001:272.403 - 0.655ms returns FALSE
TA260 001:272.412 JLINK_HasError()
TA260 001:273.745 JLINK_IsHalted()
TA260 001:274.193 - 0.448ms returns FALSE
TA260 001:274.199 JLINK_HasError()
TA260 001:276.247 JLINK_IsHalted()
TA260 001:276.749 - 0.502ms returns FALSE
TA260 001:276.755 JLINK_HasError()
TA260 001:278.251 JLINK_IsHalted()
TA260 001:278.766 - 0.514ms returns FALSE
TA260 001:278.772 JLINK_HasError()
TA260 001:280.251 JLINK_IsHalted()
TA260 001:280.755 - 0.503ms returns FALSE
TA260 001:280.768 JLINK_HasError()
TA260 001:282.251 JLINK_IsHalted()
TA260 001:282.795 - 0.544ms returns FALSE
TA260 001:282.805 JLINK_HasError()
TA260 001:284.255 JLINK_IsHalted()
TA260 001:285.662 - 1.407ms returns FALSE
TA260 001:285.670 JLINK_HasError()
TA260 001:286.752 JLINK_IsHalted()
TA260 001:287.232 - 0.479ms returns FALSE
TA260 001:287.238 JLINK_HasError()
TA260 001:288.756 JLINK_IsHalted()
TA260 001:289.482 - 0.725ms returns FALSE
TA260 001:289.490 JLINK_HasError()
TA260 001:290.759 JLINK_IsHalted()
TA260 001:292.469 - 1.709ms returns FALSE
TA260 001:292.483 JLINK_HasError()
TA260 001:296.053 JLINK_IsHalted()
TA260 001:296.498 - 0.445ms returns FALSE
TA260 001:296.508 JLINK_HasError()
TA260 001:298.053 JLINK_IsHalted()
TA260 001:298.626 - 0.573ms returns FALSE
TA260 001:298.634 JLINK_HasError()
TA260 001:300.055 JLINK_IsHalted()
TA260 001:300.695 - 0.639ms returns FALSE
TA260 001:300.706 JLINK_HasError()
TA260 001:302.057 JLINK_IsHalted()
TA260 001:302.629 - 0.571ms returns FALSE
TA260 001:302.637 JLINK_HasError()
TA260 001:304.055 JLINK_IsHalted()
TA260 001:304.536 - 0.481ms returns FALSE
TA260 001:304.543 JLINK_HasError()
TA260 001:306.056 JLINK_IsHalted()
TA260 001:306.493 - 0.436ms returns FALSE
TA260 001:306.499 JLINK_HasError()
TA260 001:307.562 JLINK_IsHalted()
TA260 001:308.095 - 0.532ms returns FALSE
TA260 001:308.102 JLINK_HasError()
TA260 001:309.562 JLINK_IsHalted()
TA260 001:310.060 - 0.498ms returns FALSE
TA260 001:310.070 JLINK_HasError()
TA260 001:311.564 JLINK_IsHalted()
TA260 001:312.061 - 0.496ms returns FALSE
TA260 001:312.068 JLINK_HasError()
TA260 001:313.561 JLINK_IsHalted()
TA260 001:314.034 - 0.473ms returns FALSE
TA260 001:314.040 JLINK_HasError()
TA260 001:316.065 JLINK_IsHalted()
TA260 001:316.627 - 0.562ms returns FALSE
TA260 001:316.633 JLINK_HasError()
TA260 001:318.070 JLINK_IsHalted()
TA260 001:318.687 - 0.617ms returns FALSE
TA260 001:318.693 JLINK_HasError()
TA260 001:320.069 JLINK_IsHalted()
TA260 001:320.536 - 0.467ms returns FALSE
TA260 001:320.543 JLINK_HasError()
TA260 001:322.070 JLINK_IsHalted()
TA260 001:322.538 - 0.467ms returns FALSE
TA260 001:322.551 JLINK_HasError()
TA260 001:324.068 JLINK_IsHalted()
TA260 001:324.546 - 0.477ms returns FALSE
TA260 001:324.553 JLINK_HasError()
TA260 001:326.067 JLINK_IsHalted()
TA260 001:326.615 - 0.547ms returns FALSE
TA260 001:326.620 JLINK_HasError()
TA260 001:328.572 JLINK_IsHalted()
TA260 001:329.058 - 0.486ms returns FALSE
TA260 001:329.064 JLINK_HasError()
TA260 001:330.573 JLINK_IsHalted()
TA260 001:331.042 - 0.468ms returns FALSE
TA260 001:331.061 JLINK_HasError()
TA260 001:332.572 JLINK_IsHalted()
TA260 001:333.103 - 0.531ms returns FALSE
TA260 001:333.109 JLINK_HasError()
TA260 001:334.573 JLINK_IsHalted()
TA260 001:335.058 - 0.485ms returns FALSE
TA260 001:335.063 JLINK_HasError()
TA260 001:337.074 JLINK_IsHalted()
TA260 001:337.614 - 0.539ms returns FALSE
TA260 001:337.620 JLINK_HasError()
TA260 001:339.080 JLINK_IsHalted()
TA260 001:339.626 - 0.546ms returns FALSE
TA260 001:339.635 JLINK_HasError()
TA260 001:341.079 JLINK_IsHalted()
TA260 001:341.616 - 0.536ms returns FALSE
TA260 001:341.622 JLINK_HasError()
TA260 001:343.078 JLINK_IsHalted()
TA260 001:343.622 - 0.543ms returns FALSE
TA260 001:343.628 JLINK_HasError()
TA260 001:345.077 JLINK_IsHalted()
TA260 001:345.622 - 0.544ms returns FALSE
TA260 001:345.633 JLINK_HasError()
TA260 001:347.584 JLINK_IsHalted()
TA260 001:348.083 - 0.498ms returns FALSE
TA260 001:348.089 JLINK_HasError()
TA260 001:349.585 JLINK_IsHalted()
TA260 001:350.066 - 0.480ms returns FALSE
TA260 001:350.079 JLINK_HasError()
TA260 001:351.586 JLINK_IsHalted()
TA260 001:352.099 - 0.512ms returns FALSE
TA260 001:352.111 JLINK_HasError()
TA260 001:353.584 JLINK_IsHalted()
TA260 001:354.081 - 0.496ms returns FALSE
TA260 001:354.087 JLINK_HasError()
TA260 001:356.089 JLINK_IsHalted()
TA260 001:358.471   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:358.991 - 2.902ms returns TRUE
TA260 001:359.002 JLINK_ReadReg(R15 (PC))
TA260 001:359.009 - 0.006ms returns 0x20000000
TA260 001:359.038 JLINK_ClrBPEx(BPHandle = 0x00000005)
TA260 001:359.043 - 0.005ms returns 0x00
TA260 001:359.048 JLINK_ReadReg(R0)
TA260 001:359.051 - 0.003ms returns 0x00000000
TA260 001:359.402 JLINK_HasError()
TA260 001:359.411 JLINK_WriteReg(R0, 0x08008000)
TA260 001:359.415 - 0.004ms returns 0
TA260 001:359.419 JLINK_WriteReg(R1, 0x00004000)
TA260 001:359.423 - 0.003ms returns 0
TA260 001:359.427 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:359.430 - 0.003ms returns 0
TA260 001:359.434 JLINK_WriteReg(R3, 0x00000000)
TA260 001:359.438 - 0.003ms returns 0
TA260 001:359.442 JLINK_WriteReg(R4, 0x00000000)
TA260 001:359.483 - 0.040ms returns 0
TA260 001:359.489 JLINK_WriteReg(R5, 0x00000000)
TA260 001:359.492 - 0.003ms returns 0
TA260 001:359.497 JLINK_WriteReg(R6, 0x00000000)
TA260 001:359.500 - 0.003ms returns 0
TA260 001:359.504 JLINK_WriteReg(R7, 0x00000000)
TA260 001:359.507 - 0.003ms returns 0
TA260 001:359.511 JLINK_WriteReg(R8, 0x00000000)
TA260 001:359.515 - 0.003ms returns 0
TA260 001:359.519 JLINK_WriteReg(R9, 0x20000180)
TA260 001:359.522 - 0.003ms returns 0
TA260 001:359.527 JLINK_WriteReg(R10, 0x00000000)
TA260 001:359.530 - 0.003ms returns 0
TA260 001:359.534 JLINK_WriteReg(R11, 0x00000000)
TA260 001:359.538 - 0.003ms returns 0
TA260 001:359.542 JLINK_WriteReg(R12, 0x00000000)
TA260 001:359.545 - 0.003ms returns 0
TA260 001:359.549 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:359.553 - 0.003ms returns 0
TA260 001:359.557 JLINK_WriteReg(R14, 0x20000001)
TA260 001:359.560 - 0.003ms returns 0
TA260 001:359.564 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 001:359.568 - 0.003ms returns 0
TA260 001:359.572 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:359.575 - 0.003ms returns 0
TA260 001:359.579 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:359.583 - 0.003ms returns 0
TA260 001:359.587 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:359.590 - 0.003ms returns 0
TA260 001:359.594 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:359.598 - 0.003ms returns 0
TA260 001:359.602 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:359.606 - 0.004ms returns 0x00000006
TA260 001:359.610 JLINK_Go()
TA260 001:359.619   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:362.479 - 2.868ms 
TA260 001:362.493 JLINK_IsHalted()
TA260 001:364.923   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:365.355 - 2.862ms returns TRUE
TA260 001:365.361 JLINK_ReadReg(R15 (PC))
TA260 001:365.367 - 0.005ms returns 0x20000000
TA260 001:365.371 JLINK_ClrBPEx(BPHandle = 0x00000006)
TA260 001:365.375 - 0.003ms returns 0x00
TA260 001:365.379 JLINK_ReadReg(R0)
TA260 001:365.383 - 0.003ms returns 0x00000001
TA260 001:365.387 JLINK_HasError()
TA260 001:365.392 JLINK_WriteReg(R0, 0x08008000)
TA260 001:365.396 - 0.004ms returns 0
TA260 001:365.400 JLINK_WriteReg(R1, 0x00004000)
TA260 001:365.404 - 0.003ms returns 0
TA260 001:365.408 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:365.411 - 0.003ms returns 0
TA260 001:365.415 JLINK_WriteReg(R3, 0x00000000)
TA260 001:365.418 - 0.003ms returns 0
TA260 001:365.422 JLINK_WriteReg(R4, 0x00000000)
TA260 001:365.426 - 0.003ms returns 0
TA260 001:365.430 JLINK_WriteReg(R5, 0x00000000)
TA260 001:365.434 - 0.003ms returns 0
TA260 001:365.438 JLINK_WriteReg(R6, 0x00000000)
TA260 001:365.441 - 0.003ms returns 0
TA260 001:365.445 JLINK_WriteReg(R7, 0x00000000)
TA260 001:365.448 - 0.003ms returns 0
TA260 001:365.452 JLINK_WriteReg(R8, 0x00000000)
TA260 001:365.456 - 0.003ms returns 0
TA260 001:365.460 JLINK_WriteReg(R9, 0x20000180)
TA260 001:365.463 - 0.003ms returns 0
TA260 001:365.467 JLINK_WriteReg(R10, 0x00000000)
TA260 001:365.470 - 0.003ms returns 0
TA260 001:365.474 JLINK_WriteReg(R11, 0x00000000)
TA260 001:365.478 - 0.003ms returns 0
TA260 001:365.482 JLINK_WriteReg(R12, 0x00000000)
TA260 001:365.485 - 0.003ms returns 0
TA260 001:365.489 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:365.493 - 0.003ms returns 0
TA260 001:365.497 JLINK_WriteReg(R14, 0x20000001)
TA260 001:365.500 - 0.003ms returns 0
TA260 001:365.504 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 001:365.508 - 0.003ms returns 0
TA260 001:365.512 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:365.515 - 0.003ms returns 0
TA260 001:365.519 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:365.523 - 0.003ms returns 0
TA260 001:365.527 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:365.530 - 0.003ms returns 0
TA260 001:365.534 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:365.538 - 0.003ms returns 0
TA260 001:365.542 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:365.546 - 0.004ms returns 0x00000007
TA260 001:365.550 JLINK_Go()
TA260 001:365.557   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:368.273 - 2.722ms 
TA260 001:368.280 JLINK_IsHalted()
TA260 001:368.748 - 0.468ms returns FALSE
TA260 001:368.754 JLINK_HasError()
TA260 001:370.097 JLINK_IsHalted()
TA260 001:370.625 - 0.527ms returns FALSE
TA260 001:370.631 JLINK_HasError()
TA260 001:372.098 JLINK_IsHalted()
TA260 001:372.618 - 0.519ms returns FALSE
TA260 001:372.630 JLINK_HasError()
TA260 001:374.094 JLINK_IsHalted()
TA260 001:374.615 - 0.521ms returns FALSE
TA260 001:374.621 JLINK_HasError()
TA260 001:376.095 JLINK_IsHalted()
TA260 001:376.630 - 0.534ms returns FALSE
TA260 001:376.646 JLINK_HasError()
TA260 001:378.602 JLINK_IsHalted()
TA260 001:379.094 - 0.491ms returns FALSE
TA260 001:379.105 JLINK_HasError()
TA260 001:380.605 JLINK_IsHalted()
TA260 001:381.083 - 0.477ms returns FALSE
TA260 001:381.092 JLINK_HasError()
TA260 001:382.604 JLINK_IsHalted()
TA260 001:383.105 - 0.500ms returns FALSE
TA260 001:383.111 JLINK_HasError()
TA260 001:384.602 JLINK_IsHalted()
TA260 001:385.068 - 0.466ms returns FALSE
TA260 001:385.074 JLINK_HasError()
TA260 001:387.106 JLINK_IsHalted()
TA260 001:387.625 - 0.519ms returns FALSE
TA260 001:387.632 JLINK_HasError()
TA260 001:390.108 JLINK_IsHalted()
TA260 001:390.623 - 0.514ms returns FALSE
TA260 001:390.629 JLINK_HasError()
TA260 001:392.110 JLINK_IsHalted()
TA260 001:392.617 - 0.507ms returns FALSE
TA260 001:392.626 JLINK_HasError()
TA260 001:394.107 JLINK_IsHalted()
TA260 001:394.624 - 0.516ms returns FALSE
TA260 001:394.630 JLINK_HasError()
TA260 001:396.609 JLINK_IsHalted()
TA260 001:397.105 - 0.496ms returns FALSE
TA260 001:397.111 JLINK_HasError()
TA260 001:398.614 JLINK_IsHalted()
TA260 001:399.093 - 0.479ms returns FALSE
TA260 001:399.099 JLINK_HasError()
TA260 001:401.619 JLINK_IsHalted()
TA260 001:402.098 - 0.478ms returns FALSE
TA260 001:402.111 JLINK_HasError()
TA260 001:403.614 JLINK_IsHalted()
TA260 001:404.070 - 0.455ms returns FALSE
TA260 001:404.075 JLINK_HasError()
TA260 001:406.116 JLINK_IsHalted()
TA260 001:406.624 - 0.507ms returns FALSE
TA260 001:406.632 JLINK_HasError()
TA260 001:408.119 JLINK_IsHalted()
TA260 001:408.758 - 0.638ms returns FALSE
TA260 001:408.768 JLINK_HasError()
TA260 001:411.121 JLINK_IsHalted()
TA260 001:411.546 - 0.424ms returns FALSE
TA260 001:411.552 JLINK_HasError()
TA260 001:413.120 JLINK_IsHalted()
TA260 001:413.627 - 0.506ms returns FALSE
TA260 001:413.635 JLINK_HasError()
TA260 001:415.119 JLINK_IsHalted()
TA260 001:415.616 - 0.496ms returns FALSE
TA260 001:415.621 JLINK_HasError()
TA260 001:417.624 JLINK_IsHalted()
TA260 001:418.128 - 0.503ms returns FALSE
TA260 001:418.134 JLINK_HasError()
TA260 001:419.625 JLINK_IsHalted()
TA260 001:420.259 - 0.634ms returns FALSE
TA260 001:420.270 JLINK_HasError()
TA260 001:421.626 JLINK_IsHalted()
TA260 001:422.228 - 0.601ms returns FALSE
TA260 001:422.238 JLINK_HasError()
TA260 001:423.625 JLINK_IsHalted()
TA260 001:424.378 - 0.752ms returns FALSE
TA260 001:424.389 JLINK_HasError()
TA260 001:426.127 JLINK_IsHalted()
TA260 001:426.627 - 0.500ms returns FALSE
TA260 001:426.633 JLINK_HasError()
TA260 001:428.130 JLINK_IsHalted()
TA260 001:428.615 - 0.484ms returns FALSE
TA260 001:428.623 JLINK_HasError()
TA260 001:430.130 JLINK_IsHalted()
TA260 001:430.614 - 0.483ms returns FALSE
TA260 001:430.620 JLINK_HasError()
TA260 001:432.133 JLINK_IsHalted()
TA260 001:432.626 - 0.493ms returns FALSE
TA260 001:432.635 JLINK_HasError()
TA260 001:434.130 JLINK_IsHalted()
TA260 001:434.647 - 0.516ms returns FALSE
TA260 001:434.656 JLINK_HasError()
TA260 001:436.133 JLINK_IsHalted()
TA260 001:436.631 - 0.497ms returns FALSE
TA260 001:436.637 JLINK_HasError()
TA260 001:438.640 JLINK_IsHalted()
TA260 001:439.485 - 0.844ms returns FALSE
TA260 001:439.497 JLINK_HasError()
TA260 001:440.847 JLINK_IsHalted()
TA260 001:441.310 - 0.462ms returns FALSE
TA260 001:441.316 JLINK_HasError()
TA260 001:442.850 JLINK_IsHalted()
TA260 001:443.308 - 0.458ms returns FALSE
TA260 001:443.315 JLINK_HasError()
TA260 001:444.848 JLINK_IsHalted()
TA260 001:445.332 - 0.483ms returns FALSE
TA260 001:445.339 JLINK_HasError()
TA260 001:447.351 JLINK_IsHalted()
TA260 001:447.845 - 0.493ms returns FALSE
TA260 001:447.852 JLINK_HasError()
TA260 001:449.351 JLINK_IsHalted()
TA260 001:449.924 - 0.573ms returns FALSE
TA260 001:449.930 JLINK_HasError()
TA260 001:451.354 JLINK_IsHalted()
TA260 001:451.860 - 0.505ms returns FALSE
TA260 001:451.872 JLINK_HasError()
TA260 001:453.352 JLINK_IsHalted()
TA260 001:453.883 - 0.530ms returns FALSE
TA260 001:453.892 JLINK_HasError()
TA260 001:455.155 JLINK_IsHalted()
TA260 001:455.628 - 0.471ms returns FALSE
TA260 001:455.638 JLINK_HasError()
TA260 001:457.659 JLINK_IsHalted()
TA260 001:458.070 - 0.411ms returns FALSE
TA260 001:458.084 JLINK_HasError()
TA260 001:459.659 JLINK_IsHalted()
TA260 001:460.255 - 0.596ms returns FALSE
TA260 001:460.270 JLINK_HasError()
TA260 001:461.662 JLINK_IsHalted()
TA260 001:462.175 - 0.513ms returns FALSE
TA260 001:462.182 JLINK_HasError()
TA260 001:463.659 JLINK_IsHalted()
TA260 001:464.158 - 0.499ms returns FALSE
TA260 001:464.165 JLINK_HasError()
TA260 001:466.164 JLINK_IsHalted()
TA260 001:466.662 - 0.497ms returns FALSE
TA260 001:466.669 JLINK_HasError()
TA260 001:468.164 JLINK_IsHalted()
TA260 001:468.660 - 0.496ms returns FALSE
TA260 001:468.666 JLINK_HasError()
TA260 001:470.668 JLINK_IsHalted()
TA260 001:471.101 - 0.433ms returns FALSE
TA260 001:471.109 JLINK_HasError()
TA260 001:472.666 JLINK_IsHalted()
TA260 001:473.164 - 0.497ms returns FALSE
TA260 001:473.170 JLINK_HasError()
TA260 001:474.666 JLINK_IsHalted()
TA260 001:475.159 - 0.493ms returns FALSE
TA260 001:475.164 JLINK_HasError()
TA260 001:477.170 JLINK_IsHalted()
TA260 001:477.660 - 0.489ms returns FALSE
TA260 001:477.666 JLINK_HasError()
TA260 001:479.171 JLINK_IsHalted()
TA260 001:479.646 - 0.474ms returns FALSE
TA260 001:479.651 JLINK_HasError()
TA260 001:481.173 JLINK_IsHalted()
TA260 001:481.616 - 0.443ms returns FALSE
TA260 001:481.622 JLINK_HasError()
TA260 001:483.171 JLINK_IsHalted()
TA260 001:483.660 - 0.488ms returns FALSE
TA260 001:483.666 JLINK_HasError()
TA260 001:485.211 JLINK_IsHalted()
TA260 001:485.693 - 0.482ms returns FALSE
TA260 001:485.705 JLINK_HasError()
TA260 001:487.678 JLINK_IsHalted()
TA260 001:488.161 - 0.482ms returns FALSE
TA260 001:488.167 JLINK_HasError()
TA260 001:489.724 JLINK_IsHalted()
TA260 001:490.210 - 0.486ms returns FALSE
TA260 001:490.217 JLINK_HasError()
TA260 001:491.681 JLINK_IsHalted()
TA260 001:492.171 - 0.489ms returns FALSE
TA260 001:492.177 JLINK_HasError()
TA260 001:493.682 JLINK_IsHalted()
TA260 001:494.200 - 0.517ms returns FALSE
TA260 001:494.207 JLINK_HasError()
TA260 001:496.184 JLINK_IsHalted()
TA260 001:496.661 - 0.476ms returns FALSE
TA260 001:496.667 JLINK_HasError()
TA260 001:498.187 JLINK_IsHalted()
TA260 001:498.750 - 0.563ms returns FALSE
TA260 001:498.755 JLINK_HasError()
TA260 001:500.187 JLINK_IsHalted()
TA260 001:500.628 - 0.441ms returns FALSE
TA260 001:500.636 JLINK_HasError()
TA260 001:502.188 JLINK_IsHalted()
TA260 001:502.618 - 0.429ms returns FALSE
TA260 001:502.624 JLINK_HasError()
TA260 001:504.185 JLINK_IsHalted()
TA260 001:504.662 - 0.476ms returns FALSE
TA260 001:504.668 JLINK_HasError()
TA260 001:506.189 JLINK_IsHalted()
TA260 001:506.765 - 0.576ms returns FALSE
TA260 001:506.774 JLINK_HasError()
TA260 001:508.690 JLINK_IsHalted()
TA260 001:509.183 - 0.492ms returns FALSE
TA260 001:509.189 JLINK_HasError()
TA260 001:510.693 JLINK_IsHalted()
TA260 001:511.245 - 0.551ms returns FALSE
TA260 001:511.262 JLINK_HasError()
TA260 001:512.689 JLINK_IsHalted()
TA260 001:513.162 - 0.472ms returns FALSE
TA260 001:513.169 JLINK_HasError()
TA260 001:514.691 JLINK_IsHalted()
TA260 001:515.164 - 0.472ms returns FALSE
TA260 001:515.170 JLINK_HasError()
TA260 001:517.199 JLINK_IsHalted()
TA260 001:517.668 - 0.468ms returns FALSE
TA260 001:517.676 JLINK_HasError()
TA260 001:520.201 JLINK_IsHalted()
TA260 001:520.749 - 0.548ms returns FALSE
TA260 001:520.755 JLINK_HasError()
TA260 001:522.202 JLINK_IsHalted()
TA260 001:522.723 - 0.520ms returns FALSE
TA260 001:522.732 JLINK_HasError()
TA260 001:524.198 JLINK_IsHalted()
TA260 001:524.650 - 0.451ms returns FALSE
TA260 001:524.656 JLINK_HasError()
TA260 001:526.198 JLINK_IsHalted()
TA260 001:526.697 - 0.499ms returns FALSE
TA260 001:526.703 JLINK_HasError()
TA260 001:528.704 JLINK_IsHalted()
TA260 001:529.208 - 0.503ms returns FALSE
TA260 001:529.215 JLINK_HasError()
TA260 001:530.702 JLINK_IsHalted()
TA260 001:531.206 - 0.504ms returns FALSE
TA260 001:531.212 JLINK_HasError()
TA260 001:532.703 JLINK_IsHalted()
TA260 001:533.194 - 0.490ms returns FALSE
TA260 001:533.206 JLINK_HasError()
TA260 001:534.704 JLINK_IsHalted()
TA260 001:535.151 - 0.446ms returns FALSE
TA260 001:535.163 JLINK_HasError()
TA260 001:536.207 JLINK_IsHalted()
TA260 001:536.707 - 0.500ms returns FALSE
TA260 001:536.718 JLINK_HasError()
TA260 001:538.209 JLINK_IsHalted()
TA260 001:538.708 - 0.498ms returns FALSE
TA260 001:538.716 JLINK_HasError()
TA260 001:540.210 JLINK_IsHalted()
TA260 001:540.767 - 0.556ms returns FALSE
TA260 001:540.773 JLINK_HasError()
TA260 001:542.210 JLINK_IsHalted()
TA260 001:542.693 - 0.482ms returns FALSE
TA260 001:542.699 JLINK_HasError()
TA260 001:544.208 JLINK_IsHalted()
TA260 001:544.667 - 0.459ms returns FALSE
TA260 001:544.673 JLINK_HasError()
TA260 001:546.231 JLINK_IsHalted()
TA260 001:546.766 - 0.535ms returns FALSE
TA260 001:546.830 JLINK_HasError()
TA260 001:548.714 JLINK_IsHalted()
TA260 001:549.218 - 0.504ms returns FALSE
TA260 001:549.224 JLINK_HasError()
TA260 001:550.714 JLINK_IsHalted()
TA260 001:551.173 - 0.458ms returns FALSE
TA260 001:551.178 JLINK_HasError()
TA260 001:552.713 JLINK_IsHalted()
TA260 001:553.206 - 0.492ms returns FALSE
TA260 001:553.213 JLINK_HasError()
TA260 001:554.713 JLINK_IsHalted()
TA260 001:555.194 - 0.480ms returns FALSE
TA260 001:555.200 JLINK_HasError()
TA260 001:557.217 JLINK_IsHalted()
TA260 001:557.767 - 0.549ms returns FALSE
TA260 001:557.773 JLINK_HasError()
TA260 001:559.218 JLINK_IsHalted()
TA260 001:559.690 - 0.472ms returns FALSE
TA260 001:559.696 JLINK_HasError()
TA260 001:561.222 JLINK_IsHalted()
TA260 001:561.666 - 0.444ms returns FALSE
TA260 001:561.678 JLINK_HasError()
TA260 001:563.220 JLINK_IsHalted()
TA260 001:563.893 - 0.673ms returns FALSE
TA260 001:563.905 JLINK_HasError()
TA260 001:566.221 JLINK_IsHalted()
TA260 001:566.765 - 0.543ms returns FALSE
TA260 001:566.771 JLINK_HasError()
TA260 001:569.227 JLINK_IsHalted()
TA260 001:569.720 - 0.492ms returns FALSE
TA260 001:569.727 JLINK_HasError()
TA260 001:571.228 JLINK_IsHalted()
TA260 001:571.663 - 0.434ms returns FALSE
TA260 001:571.668 JLINK_HasError()
TA260 001:573.228 JLINK_IsHalted()
TA260 001:573.663 - 0.434ms returns FALSE
TA260 001:573.672 JLINK_HasError()
TA260 001:575.226 JLINK_IsHalted()
TA260 001:575.765 - 0.539ms returns FALSE
TA260 001:575.770 JLINK_HasError()
TA260 001:577.729 JLINK_IsHalted()
TA260 001:578.206 - 0.477ms returns FALSE
TA260 001:578.212 JLINK_HasError()
TA260 001:579.734 JLINK_IsHalted()
TA260 001:580.225 - 0.490ms returns FALSE
TA260 001:580.235 JLINK_HasError()
TA260 001:582.733 JLINK_IsHalted()
TA260 001:583.336 - 0.602ms returns FALSE
TA260 001:583.342 JLINK_HasError()
TA260 001:584.732 JLINK_IsHalted()
TA260 001:585.229 - 0.496ms returns FALSE
TA260 001:585.235 JLINK_HasError()
TA260 001:587.234 JLINK_IsHalted()
TA260 001:587.765 - 0.530ms returns FALSE
TA260 001:587.770 JLINK_HasError()
TA260 001:589.238 JLINK_IsHalted()
TA260 001:589.766 - 0.528ms returns FALSE
TA260 001:589.774 JLINK_HasError()
TA260 001:591.237 JLINK_IsHalted()
TA260 001:591.788 - 0.550ms returns FALSE
TA260 001:591.794 JLINK_HasError()
TA260 001:593.238 JLINK_IsHalted()
TA260 001:593.764 - 0.525ms returns FALSE
TA260 001:593.770 JLINK_HasError()
TA260 001:595.237 JLINK_IsHalted()
TA260 001:595.764 - 0.527ms returns FALSE
TA260 001:595.770 JLINK_HasError()
TA260 001:597.745 JLINK_IsHalted()
TA260 001:598.240 - 0.495ms returns FALSE
TA260 001:598.249 JLINK_HasError()
TA260 001:599.743 JLINK_IsHalted()
TA260 001:600.244 - 0.501ms returns FALSE
TA260 001:600.253 JLINK_HasError()
TA260 001:601.745 JLINK_IsHalted()
TA260 001:602.244 - 0.499ms returns FALSE
TA260 001:602.254 JLINK_HasError()
TA260 001:603.743 JLINK_IsHalted()
TA260 001:604.260 - 0.517ms returns FALSE
TA260 001:604.266 JLINK_HasError()
TA260 001:606.247 JLINK_IsHalted()
TA260 001:606.651 - 0.404ms returns FALSE
TA260 001:606.661 JLINK_HasError()
TA260 001:608.249 JLINK_IsHalted()
TA260 001:608.749 - 0.500ms returns FALSE
TA260 001:608.755 JLINK_HasError()
TA260 001:610.251 JLINK_IsHalted()
TA260 001:610.725 - 0.473ms returns FALSE
TA260 001:610.738 JLINK_HasError()
TA260 001:612.250 JLINK_IsHalted()
TA260 001:612.691 - 0.440ms returns FALSE
TA260 001:612.697 JLINK_HasError()
TA260 001:614.249 JLINK_IsHalted()
TA260 001:614.763 - 0.514ms returns FALSE
TA260 001:614.769 JLINK_HasError()
TA260 001:616.249 JLINK_IsHalted()
TA260 001:616.763 - 0.514ms returns FALSE
TA260 001:616.769 JLINK_HasError()
TA260 001:618.752 JLINK_IsHalted()
TA260 001:619.241 - 0.488ms returns FALSE
TA260 001:619.247 JLINK_HasError()
TA260 001:620.756 JLINK_IsHalted()
TA260 001:621.203 - 0.447ms returns FALSE
TA260 001:621.209 JLINK_HasError()
TA260 001:622.754 JLINK_IsHalted()
TA260 001:623.222 - 0.467ms returns FALSE
TA260 001:623.228 JLINK_HasError()
TA260 001:624.753 JLINK_IsHalted()
TA260 001:625.311 - 0.558ms returns FALSE
TA260 001:625.321 JLINK_HasError()
TA260 001:627.257 JLINK_IsHalted()
TA260 001:627.764 - 0.506ms returns FALSE
TA260 001:627.770 JLINK_HasError()
TA260 001:629.258 JLINK_IsHalted()
TA260 001:629.765 - 0.506ms returns FALSE
TA260 001:629.771 JLINK_HasError()
TA260 001:631.261 JLINK_IsHalted()
TA260 001:631.751 - 0.489ms returns FALSE
TA260 001:631.757 JLINK_HasError()
TA260 001:633.259 JLINK_IsHalted()
TA260 001:633.767 - 0.507ms returns FALSE
TA260 001:633.772 JLINK_HasError()
TA260 001:635.257 JLINK_IsHalted()
TA260 001:635.765 - 0.507ms returns FALSE
TA260 001:635.770 JLINK_HasError()
TA260 001:637.765 JLINK_IsHalted()
TA260 001:638.378 - 0.613ms returns FALSE
TA260 001:638.384 JLINK_HasError()
TA260 001:639.765 JLINK_IsHalted()
TA260 001:640.211 - 0.445ms returns FALSE
TA260 001:640.222 JLINK_HasError()
TA260 001:641.766 JLINK_IsHalted()
TA260 001:642.231 - 0.464ms returns FALSE
TA260 001:642.245 JLINK_HasError()
TA260 001:646.269 JLINK_IsHalted()
TA260 001:646.767 - 0.498ms returns FALSE
TA260 001:646.773 JLINK_HasError()
TA260 001:648.269 JLINK_IsHalted()
TA260 001:648.768 - 0.499ms returns FALSE
TA260 001:648.774 JLINK_HasError()
TA260 001:650.270 JLINK_IsHalted()
TA260 001:650.783 - 0.512ms returns FALSE
TA260 001:650.790 JLINK_HasError()
TA260 001:652.272 JLINK_IsHalted()
TA260 001:652.765 - 0.493ms returns FALSE
TA260 001:652.772 JLINK_HasError()
TA260 001:654.269 JLINK_IsHalted()
TA260 001:654.749 - 0.479ms returns FALSE
TA260 001:654.755 JLINK_HasError()
TA260 001:656.272 JLINK_IsHalted()
TA260 001:656.945 - 0.672ms returns FALSE
TA260 001:656.955 JLINK_HasError()
TA260 001:658.774 JLINK_IsHalted()
TA260 001:659.286 - 0.511ms returns FALSE
TA260 001:659.292 JLINK_HasError()
TA260 001:660.776 JLINK_IsHalted()
TA260 001:661.233 - 0.456ms returns FALSE
TA260 001:661.247 JLINK_HasError()
TA260 001:662.775 JLINK_IsHalted()
TA260 001:663.275 - 0.499ms returns FALSE
TA260 001:663.283 JLINK_HasError()
TA260 001:664.776 JLINK_IsHalted()
TA260 001:665.219 - 0.442ms returns FALSE
TA260 001:665.224 JLINK_HasError()
TA260 001:666.279 JLINK_IsHalted()
TA260 001:666.763 - 0.483ms returns FALSE
TA260 001:666.768 JLINK_HasError()
TA260 001:668.279 JLINK_IsHalted()
TA260 001:668.763 - 0.483ms returns FALSE
TA260 001:668.769 JLINK_HasError()
TA260 001:670.784 JLINK_IsHalted()
TA260 001:671.297 - 0.513ms returns FALSE
TA260 001:671.303 JLINK_HasError()
TA260 001:672.786 JLINK_IsHalted()
TA260 001:673.276 - 0.490ms returns FALSE
TA260 001:673.287 JLINK_HasError()
TA260 001:674.782 JLINK_IsHalted()
TA260 001:675.286 - 0.503ms returns FALSE
TA260 001:675.292 JLINK_HasError()
TA260 001:677.286 JLINK_IsHalted()
TA260 001:677.800 - 0.513ms returns FALSE
TA260 001:677.806 JLINK_HasError()
TA260 001:679.287 JLINK_IsHalted()
TA260 001:679.765 - 0.477ms returns FALSE
TA260 001:679.771 JLINK_HasError()
TA260 001:681.288 JLINK_IsHalted()
TA260 001:681.813 - 0.524ms returns FALSE
TA260 001:681.826 JLINK_HasError()
TA260 001:683.289 JLINK_IsHalted()
TA260 001:683.764 - 0.475ms returns FALSE
TA260 001:683.770 JLINK_HasError()
TA260 001:685.392 JLINK_IsHalted()
TA260 001:685.850 - 0.456ms returns FALSE
TA260 001:685.864 JLINK_HasError()
TA260 001:687.888 JLINK_IsHalted()
TA260 001:688.381 - 0.493ms returns FALSE
TA260 001:688.392 JLINK_HasError()
TA260 001:689.889 JLINK_IsHalted()
TA260 001:690.496 - 0.606ms returns FALSE
TA260 001:690.510 JLINK_HasError()
TA260 001:691.890 JLINK_IsHalted()
TA260 001:692.313 - 0.422ms returns FALSE
TA260 001:692.319 JLINK_HasError()
TA260 001:693.887 JLINK_IsHalted()
TA260 001:694.388 - 0.500ms returns FALSE
TA260 001:694.395 JLINK_HasError()
TA260 001:696.396 JLINK_IsHalted()
TA260 001:696.890 - 0.494ms returns FALSE
TA260 001:696.899 JLINK_HasError()
TA260 001:698.395 JLINK_IsHalted()
TA260 001:698.901 - 0.505ms returns FALSE
TA260 001:698.909 JLINK_HasError()
TA260 001:700.398 JLINK_IsHalted()
TA260 001:700.860 - 0.461ms returns FALSE
TA260 001:700.872 JLINK_HasError()
TA260 001:702.643 JLINK_IsHalted()
TA260 001:703.119 - 0.476ms returns FALSE
TA260 001:703.127 JLINK_HasError()
TA260 001:704.458 JLINK_IsHalted()
TA260 001:704.935 - 0.477ms returns FALSE
TA260 001:704.942 JLINK_HasError()
TA260 001:706.968 JLINK_IsHalted()
TA260 001:707.461 - 0.493ms returns FALSE
TA260 001:707.474 JLINK_HasError()
TA260 001:708.961 JLINK_IsHalted()
TA260 001:709.444 - 0.483ms returns FALSE
TA260 001:709.450 JLINK_HasError()
TA260 001:710.963 JLINK_IsHalted()
TA260 001:711.403 - 0.439ms returns FALSE
TA260 001:711.409 JLINK_HasError()
TA260 001:712.964 JLINK_IsHalted()
TA260 001:713.444 - 0.480ms returns FALSE
TA260 001:713.451 JLINK_HasError()
TA260 001:714.962 JLINK_IsHalted()
TA260 001:715.434 - 0.471ms returns FALSE
TA260 001:715.439 JLINK_HasError()
TA260 001:717.467 JLINK_IsHalted()
TA260 001:717.946 - 0.479ms returns FALSE
TA260 001:717.952 JLINK_HasError()
TA260 001:719.472 JLINK_IsHalted()
TA260 001:719.956 - 0.483ms returns FALSE
TA260 001:719.967 JLINK_HasError()
TA260 001:721.471 JLINK_IsHalted()
TA260 001:721.933 - 0.462ms returns FALSE
TA260 001:721.940 JLINK_HasError()
TA260 001:723.469 JLINK_IsHalted()
TA260 001:723.968 - 0.499ms returns FALSE
TA260 001:723.974 JLINK_HasError()
TA260 001:725.970 JLINK_IsHalted()
TA260 001:726.447 - 0.477ms returns FALSE
TA260 001:726.453 JLINK_HasError()
TA260 001:727.978 JLINK_IsHalted()
TA260 001:728.479 - 0.500ms returns FALSE
TA260 001:728.486 JLINK_HasError()
TA260 001:729.974 JLINK_IsHalted()
TA260 001:730.471 - 0.497ms returns FALSE
TA260 001:730.479 JLINK_HasError()
TA260 001:731.974 JLINK_IsHalted()
TA260 001:732.491 - 0.516ms returns FALSE
TA260 001:732.496 JLINK_HasError()
TA260 001:733.973 JLINK_IsHalted()
TA260 001:734.493 - 0.519ms returns FALSE
TA260 001:734.503 JLINK_HasError()
TA260 001:736.478 JLINK_IsHalted()
TA260 001:736.992 - 0.514ms returns FALSE
TA260 001:736.998 JLINK_HasError()
TA260 001:738.485 JLINK_IsHalted()
TA260 001:738.957 - 0.471ms returns FALSE
TA260 001:738.966 JLINK_HasError()
TA260 001:740.987 JLINK_IsHalted()
TA260 001:741.518 - 0.530ms returns FALSE
TA260 001:741.531 JLINK_HasError()
TA260 001:742.987 JLINK_IsHalted()
TA260 001:743.444 - 0.457ms returns FALSE
TA260 001:743.450 JLINK_HasError()
TA260 001:744.985 JLINK_IsHalted()
TA260 001:745.478 - 0.493ms returns FALSE
TA260 001:745.484 JLINK_HasError()
TA260 001:747.490 JLINK_IsHalted()
TA260 001:747.991 - 0.501ms returns FALSE
TA260 001:747.997 JLINK_HasError()
TA260 001:749.489 JLINK_IsHalted()
TA260 001:750.004 - 0.514ms returns FALSE
TA260 001:750.015 JLINK_HasError()
TA260 001:751.491 JLINK_IsHalted()
TA260 001:751.995 - 0.503ms returns FALSE
TA260 001:752.008 JLINK_HasError()
TA260 001:753.491 JLINK_IsHalted()
TA260 001:753.990 - 0.499ms returns FALSE
TA260 001:753.996 JLINK_HasError()
TA260 001:755.991 JLINK_IsHalted()
TA260 001:756.491 - 0.499ms returns FALSE
TA260 001:756.496 JLINK_HasError()
TA260 001:757.999 JLINK_IsHalted()
TA260 001:760.299   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:760.804 - 2.804ms returns TRUE
TA260 001:760.817 JLINK_ReadReg(R15 (PC))
TA260 001:760.824 - 0.006ms returns 0x20000000
TA260 001:760.829 JLINK_ClrBPEx(BPHandle = 0x00000007)
TA260 001:760.833 - 0.004ms returns 0x00
TA260 001:760.838 JLINK_ReadReg(R0)
TA260 001:760.841 - 0.003ms returns 0x00000000
TA260 001:761.209 JLINK_HasError()
TA260 001:761.219 JLINK_WriteReg(R0, 0x0800C000)
TA260 001:761.224 - 0.005ms returns 0
TA260 001:761.229 JLINK_WriteReg(R1, 0x00004000)
TA260 001:761.233 - 0.003ms returns 0
TA260 001:761.238 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:761.241 - 0.003ms returns 0
TA260 001:761.245 JLINK_WriteReg(R3, 0x00000000)
TA260 001:761.249 - 0.003ms returns 0
TA260 001:761.254 JLINK_WriteReg(R4, 0x00000000)
TA260 001:761.258 - 0.003ms returns 0
TA260 001:761.262 JLINK_WriteReg(R5, 0x00000000)
TA260 001:761.266 - 0.004ms returns 0
TA260 001:761.270 JLINK_WriteReg(R6, 0x00000000)
TA260 001:761.274 - 0.003ms returns 0
TA260 001:761.278 JLINK_WriteReg(R7, 0x00000000)
TA260 001:761.282 - 0.003ms returns 0
TA260 001:761.286 JLINK_WriteReg(R8, 0x00000000)
TA260 001:761.290 - 0.004ms returns 0
TA260 001:761.294 JLINK_WriteReg(R9, 0x20000180)
TA260 001:761.298 - 0.003ms returns 0
TA260 001:761.302 JLINK_WriteReg(R10, 0x00000000)
TA260 001:761.306 - 0.003ms returns 0
TA260 001:761.310 JLINK_WriteReg(R11, 0x00000000)
TA260 001:761.314 - 0.004ms returns 0
TA260 001:761.318 JLINK_WriteReg(R12, 0x00000000)
TA260 001:761.322 - 0.003ms returns 0
TA260 001:761.326 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:761.330 - 0.004ms returns 0
TA260 001:761.335 JLINK_WriteReg(R14, 0x20000001)
TA260 001:761.338 - 0.003ms returns 0
TA260 001:761.343 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 001:761.346 - 0.003ms returns 0
TA260 001:761.350 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:761.355 - 0.004ms returns 0
TA260 001:761.359 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:761.363 - 0.003ms returns 0
TA260 001:761.367 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:761.370 - 0.003ms returns 0
TA260 001:761.375 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:761.378 - 0.003ms returns 0
TA260 001:761.383 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:761.387 - 0.004ms returns 0x00000008
TA260 001:761.392 JLINK_Go()
TA260 001:761.401   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:764.182 - 2.790ms 
TA260 001:764.196 JLINK_IsHalted()
TA260 001:766.474   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:766.975 - 2.779ms returns TRUE
TA260 001:766.985 JLINK_ReadReg(R15 (PC))
TA260 001:766.990 - 0.004ms returns 0x20000000
TA260 001:767.018 JLINK_ClrBPEx(BPHandle = 0x00000008)
TA260 001:767.023 - 0.004ms returns 0x00
TA260 001:767.027 JLINK_ReadReg(R0)
TA260 001:767.031 - 0.003ms returns 0x00000001
TA260 001:767.035 JLINK_HasError()
TA260 001:767.040 JLINK_WriteReg(R0, 0x0800C000)
TA260 001:767.044 - 0.003ms returns 0
TA260 001:767.048 JLINK_WriteReg(R1, 0x00004000)
TA260 001:767.052 - 0.003ms returns 0
TA260 001:767.056 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:767.059 - 0.003ms returns 0
TA260 001:767.063 JLINK_WriteReg(R3, 0x00000000)
TA260 001:767.067 - 0.003ms returns 0
TA260 001:767.071 JLINK_WriteReg(R4, 0x00000000)
TA260 001:767.074 - 0.003ms returns 0
TA260 001:767.078 JLINK_WriteReg(R5, 0x00000000)
TA260 001:767.082 - 0.003ms returns 0
TA260 001:767.086 JLINK_WriteReg(R6, 0x00000000)
TA260 001:767.102 - 0.016ms returns 0
TA260 001:767.106 JLINK_WriteReg(R7, 0x00000000)
TA260 001:767.109 - 0.003ms returns 0
TA260 001:767.114 JLINK_WriteReg(R8, 0x00000000)
TA260 001:767.117 - 0.003ms returns 0
TA260 001:767.121 JLINK_WriteReg(R9, 0x20000180)
TA260 001:767.129 - 0.008ms returns 0
TA260 001:767.152 JLINK_WriteReg(R10, 0x00000000)
TA260 001:767.157 - 0.005ms returns 0
TA260 001:767.161 JLINK_WriteReg(R11, 0x00000000)
TA260 001:767.165 - 0.003ms returns 0
TA260 001:767.169 JLINK_WriteReg(R12, 0x00000000)
TA260 001:767.172 - 0.003ms returns 0
TA260 001:767.176 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:767.180 - 0.004ms returns 0
TA260 001:767.184 JLINK_WriteReg(R14, 0x20000001)
TA260 001:767.188 - 0.003ms returns 0
TA260 001:767.192 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 001:767.195 - 0.003ms returns 0
TA260 001:767.199 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:767.203 - 0.003ms returns 0
TA260 001:767.207 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:767.210 - 0.003ms returns 0
TA260 001:767.214 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:767.218 - 0.003ms returns 0
TA260 001:767.222 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:767.225 - 0.003ms returns 0
TA260 001:767.230 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:767.234 - 0.004ms returns 0x00000009
TA260 001:767.238 JLINK_Go()
TA260 001:767.246   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:770.084 - 2.845ms 
TA260 001:770.093 JLINK_IsHalted()
TA260 001:770.620 - 0.526ms returns FALSE
TA260 001:770.633 JLINK_HasError()
TA260 001:772.007 JLINK_IsHalted()
TA260 001:772.474 - 0.466ms returns FALSE
TA260 001:772.487 JLINK_HasError()
TA260 001:774.003 JLINK_IsHalted()
TA260 001:774.479 - 0.475ms returns FALSE
TA260 001:774.486 JLINK_HasError()
TA260 001:776.002 JLINK_IsHalted()
TA260 001:776.500 - 0.497ms returns FALSE
TA260 001:776.506 JLINK_HasError()
TA260 001:778.510 JLINK_IsHalted()
TA260 001:779.001 - 0.491ms returns FALSE
TA260 001:779.009 JLINK_HasError()
TA260 001:780.746 JLINK_IsHalted()
TA260 001:781.497 - 0.750ms returns FALSE
TA260 001:781.510 JLINK_HasError()
TA260 001:782.747 JLINK_IsHalted()
TA260 001:783.188 - 0.440ms returns FALSE
TA260 001:783.195 JLINK_HasError()
TA260 001:784.744 JLINK_IsHalted()
TA260 001:785.227 - 0.482ms returns FALSE
TA260 001:785.239 JLINK_HasError()
TA260 001:787.251 JLINK_IsHalted()
TA260 001:787.690 - 0.438ms returns FALSE
TA260 001:787.696 JLINK_HasError()
TA260 001:789.252 JLINK_IsHalted()
TA260 001:789.722 - 0.469ms returns FALSE
TA260 001:789.730 JLINK_HasError()
TA260 001:791.250 JLINK_IsHalted()
TA260 001:791.710 - 0.459ms returns FALSE
TA260 001:791.722 JLINK_HasError()
TA260 001:793.250 JLINK_IsHalted()
TA260 001:793.751 - 0.501ms returns FALSE
TA260 001:793.757 JLINK_HasError()
TA260 001:795.249 JLINK_IsHalted()
TA260 001:795.752 - 0.502ms returns FALSE
TA260 001:795.763 JLINK_HasError()
TA260 001:797.757 JLINK_IsHalted()
TA260 001:798.263 - 0.505ms returns FALSE
TA260 001:798.268 JLINK_HasError()
TA260 001:799.756 JLINK_IsHalted()
TA260 001:800.301 - 0.545ms returns FALSE
TA260 001:800.309 JLINK_HasError()
TA260 001:801.757 JLINK_IsHalted()
TA260 001:802.243 - 0.485ms returns FALSE
TA260 001:802.249 JLINK_HasError()
TA260 001:803.755 JLINK_IsHalted()
TA260 001:804.262 - 0.506ms returns FALSE
TA260 001:804.268 JLINK_HasError()
TA260 001:806.262 JLINK_IsHalted()
TA260 001:806.690 - 0.427ms returns FALSE
TA260 001:806.697 JLINK_HasError()
TA260 001:808.263 JLINK_IsHalted()
TA260 001:808.764 - 0.501ms returns FALSE
TA260 001:808.773 JLINK_HasError()
TA260 001:810.261 JLINK_IsHalted()
TA260 001:810.800 - 0.538ms returns FALSE
TA260 001:810.813 JLINK_HasError()
TA260 001:812.263 JLINK_IsHalted()
TA260 001:812.894 - 0.630ms returns FALSE
TA260 001:812.904 JLINK_HasError()
TA260 001:814.270 JLINK_IsHalted()
TA260 001:814.830 - 0.559ms returns FALSE
TA260 001:814.839 JLINK_HasError()
TA260 001:816.263 JLINK_IsHalted()
TA260 001:816.708 - 0.444ms returns FALSE
TA260 001:816.714 JLINK_HasError()
TA260 001:817.774 JLINK_IsHalted()
TA260 001:818.245 - 0.470ms returns FALSE
TA260 001:818.263 JLINK_HasError()
TA260 001:819.768 JLINK_IsHalted()
TA260 001:820.310 - 0.541ms returns FALSE
TA260 001:820.324 JLINK_HasError()
TA260 001:821.776 JLINK_IsHalted()
TA260 001:822.233 - 0.456ms returns FALSE
TA260 001:822.243 JLINK_HasError()
TA260 001:823.767 JLINK_IsHalted()
TA260 001:824.285 - 0.517ms returns FALSE
TA260 001:824.291 JLINK_HasError()
TA260 001:826.270 JLINK_IsHalted()
TA260 001:826.750 - 0.479ms returns FALSE
TA260 001:826.756 JLINK_HasError()
TA260 001:828.276 JLINK_IsHalted()
TA260 001:828.800 - 0.524ms returns FALSE
TA260 001:828.810 JLINK_HasError()
TA260 001:830.278 JLINK_IsHalted()
TA260 001:830.753 - 0.474ms returns FALSE
TA260 001:830.759 JLINK_HasError()
TA260 001:832.280 JLINK_IsHalted()
TA260 001:832.767 - 0.486ms returns FALSE
TA260 001:832.774 JLINK_HasError()
TA260 001:834.272 JLINK_IsHalted()
TA260 001:834.764 - 0.491ms returns FALSE
TA260 001:834.770 JLINK_HasError()
TA260 001:836.274 JLINK_IsHalted()
TA260 001:836.764 - 0.490ms returns FALSE
TA260 001:836.771 JLINK_HasError()
TA260 001:838.779 JLINK_IsHalted()
TA260 001:839.223 - 0.444ms returns FALSE
TA260 001:839.234 JLINK_HasError()
TA260 001:840.785 JLINK_IsHalted()
TA260 001:841.255 - 0.470ms returns FALSE
TA260 001:841.262 JLINK_HasError()
TA260 001:842.786 JLINK_IsHalted()
TA260 001:843.308 - 0.521ms returns FALSE
TA260 001:843.319 JLINK_HasError()
TA260 001:844.781 JLINK_IsHalted()
TA260 001:845.262 - 0.481ms returns FALSE
TA260 001:845.268 JLINK_HasError()
TA260 001:847.290 JLINK_IsHalted()
TA260 001:847.769 - 0.478ms returns FALSE
TA260 001:847.776 JLINK_HasError()
TA260 001:849.287 JLINK_IsHalted()
TA260 001:849.765 - 0.478ms returns FALSE
TA260 001:849.771 JLINK_HasError()
TA260 001:851.291 JLINK_IsHalted()
TA260 001:851.752 - 0.460ms returns FALSE
TA260 001:851.759 JLINK_HasError()
TA260 001:853.292 JLINK_IsHalted()
TA260 001:853.764 - 0.471ms returns FALSE
TA260 001:853.770 JLINK_HasError()
TA260 001:855.286 JLINK_IsHalted()
TA260 001:855.763 - 0.477ms returns FALSE
TA260 001:855.770 JLINK_HasError()
TA260 001:857.794 JLINK_IsHalted()
TA260 001:858.294 - 0.500ms returns FALSE
TA260 001:858.302 JLINK_HasError()
TA260 001:859.804 JLINK_IsHalted()
TA260 001:860.369 - 0.565ms returns FALSE
TA260 001:860.380 JLINK_HasError()
TA260 001:862.402 JLINK_IsHalted()
TA260 001:862.857 - 0.454ms returns FALSE
TA260 001:862.863 JLINK_HasError()
TA260 001:864.402 JLINK_IsHalted()
TA260 001:864.912 - 0.509ms returns FALSE
TA260 001:864.918 JLINK_HasError()
TA260 001:866.401 JLINK_IsHalted()
TA260 001:866.898 - 0.496ms returns FALSE
TA260 001:866.903 JLINK_HasError()
TA260 001:869.407 JLINK_IsHalted()
TA260 001:869.889 - 0.481ms returns FALSE
TA260 001:869.900 JLINK_HasError()
TA260 001:871.412 JLINK_IsHalted()
TA260 001:871.893 - 0.481ms returns FALSE
TA260 001:871.901 JLINK_HasError()
TA260 001:873.409 JLINK_IsHalted()
TA260 001:873.909 - 0.499ms returns FALSE
TA260 001:873.915 JLINK_HasError()
TA260 001:875.421 JLINK_IsHalted()
TA260 001:875.884 - 0.463ms returns FALSE
TA260 001:875.896 JLINK_HasError()
TA260 001:877.917 JLINK_IsHalted()
TA260 001:878.353 - 0.435ms returns FALSE
TA260 001:878.359 JLINK_HasError()
TA260 001:879.918 JLINK_IsHalted()
TA260 001:880.492 - 0.573ms returns FALSE
TA260 001:880.504 JLINK_HasError()
TA260 001:881.920 JLINK_IsHalted()
TA260 001:882.379 - 0.458ms returns FALSE
TA260 001:882.385 JLINK_HasError()
TA260 001:883.918 JLINK_IsHalted()
TA260 001:884.410 - 0.491ms returns FALSE
TA260 001:884.416 JLINK_HasError()
TA260 001:885.916 JLINK_IsHalted()
TA260 001:886.412 - 0.496ms returns FALSE
TA260 001:886.417 JLINK_HasError()
TA260 001:888.422 JLINK_IsHalted()
TA260 001:888.896 - 0.473ms returns FALSE
TA260 001:888.903 JLINK_HasError()
TA260 001:890.424 JLINK_IsHalted()
TA260 001:890.891 - 0.465ms returns FALSE
TA260 001:890.903 JLINK_HasError()
TA260 001:892.422 JLINK_IsHalted()
TA260 001:892.924 - 0.501ms returns FALSE
TA260 001:892.930 JLINK_HasError()
TA260 001:894.426 JLINK_IsHalted()
TA260 001:894.899 - 0.473ms returns FALSE
TA260 001:894.905 JLINK_HasError()
TA260 001:896.925 JLINK_IsHalted()
TA260 001:897.440 - 0.514ms returns FALSE
TA260 001:897.450 JLINK_HasError()
TA260 001:898.925 JLINK_IsHalted()
TA260 001:899.411 - 0.486ms returns FALSE
TA260 001:899.417 JLINK_HasError()
TA260 001:900.928 JLINK_IsHalted()
TA260 001:901.367 - 0.438ms returns FALSE
TA260 001:901.373 JLINK_HasError()
TA260 001:902.927 JLINK_IsHalted()
TA260 001:903.400 - 0.472ms returns FALSE
TA260 001:903.406 JLINK_HasError()
TA260 001:904.929 JLINK_IsHalted()
TA260 001:905.399 - 0.469ms returns FALSE
TA260 001:905.408 JLINK_HasError()
TA260 001:907.434 JLINK_IsHalted()
TA260 001:907.903 - 0.469ms returns FALSE
TA260 001:907.912 JLINK_HasError()
TA260 001:909.432 JLINK_IsHalted()
TA260 001:909.899 - 0.466ms returns FALSE
TA260 001:909.912 JLINK_HasError()
TA260 001:911.435 JLINK_IsHalted()
TA260 001:911.943 - 0.507ms returns FALSE
TA260 001:911.956 JLINK_HasError()
TA260 001:913.435 JLINK_IsHalted()
TA260 001:913.935 - 0.500ms returns FALSE
TA260 001:913.941 JLINK_HasError()
TA260 001:915.938 JLINK_IsHalted()
TA260 001:916.411 - 0.472ms returns FALSE
TA260 001:916.417 JLINK_HasError()
TA260 001:917.939 JLINK_IsHalted()
TA260 001:918.436 - 0.497ms returns FALSE
TA260 001:918.444 JLINK_HasError()
TA260 001:919.940 JLINK_IsHalted()
TA260 001:920.650 - 0.709ms returns FALSE
TA260 001:920.661 JLINK_HasError()
TA260 001:921.940 JLINK_IsHalted()
TA260 001:922.392 - 0.451ms returns FALSE
TA260 001:922.399 JLINK_HasError()
TA260 001:923.945 JLINK_IsHalted()
TA260 001:924.424 - 0.478ms returns FALSE
TA260 001:924.430 JLINK_HasError()
TA260 001:925.939 JLINK_IsHalted()
TA260 001:926.411 - 0.471ms returns FALSE
TA260 001:926.416 JLINK_HasError()
TA260 001:928.446 JLINK_IsHalted()
TA260 001:928.912 - 0.466ms returns FALSE
TA260 001:928.919 JLINK_HasError()
TA260 001:930.445 JLINK_IsHalted()
TA260 001:930.936 - 0.491ms returns FALSE
TA260 001:930.945 JLINK_HasError()
TA260 001:932.443 JLINK_IsHalted()
TA260 001:932.856 - 0.412ms returns FALSE
TA260 001:932.862 JLINK_HasError()
TA260 001:934.443 JLINK_IsHalted()
TA260 001:934.900 - 0.456ms returns FALSE
TA260 001:934.906 JLINK_HasError()
TA260 001:935.946 JLINK_IsHalted()
TA260 001:936.540 - 0.593ms returns FALSE
TA260 001:936.550 JLINK_HasError()
TA260 001:937.958 JLINK_IsHalted()
TA260 001:938.619 - 0.660ms returns FALSE
TA260 001:938.631 JLINK_HasError()
TA260 001:939.961 JLINK_IsHalted()
TA260 001:940.519 - 0.557ms returns FALSE
TA260 001:940.532 JLINK_HasError()
TA260 001:941.953 JLINK_IsHalted()
TA260 001:942.449 - 0.495ms returns FALSE
TA260 001:942.457 JLINK_HasError()
TA260 001:943.950 JLINK_IsHalted()
TA260 001:944.458 - 0.507ms returns FALSE
TA260 001:944.469 JLINK_HasError()
TA260 001:945.953 JLINK_IsHalted()
TA260 001:946.444 - 0.491ms returns FALSE
TA260 001:946.450 JLINK_HasError()
TA260 001:948.456 JLINK_IsHalted()
TA260 001:948.933 - 0.477ms returns FALSE
TA260 001:948.939 JLINK_HasError()
TA260 001:950.456 JLINK_IsHalted()
TA260 001:950.892 - 0.435ms returns FALSE
TA260 001:950.898 JLINK_HasError()
TA260 001:952.456 JLINK_IsHalted()
TA260 001:952.892 - 0.435ms returns FALSE
TA260 001:952.898 JLINK_HasError()
TA260 001:955.960 JLINK_IsHalted()
TA260 001:956.475 - 0.514ms returns FALSE
TA260 001:956.482 JLINK_HasError()
TA260 001:957.961 JLINK_IsHalted()
TA260 001:958.437 - 0.476ms returns FALSE
TA260 001:958.447 JLINK_HasError()
TA260 001:959.962 JLINK_IsHalted()
TA260 001:960.444 - 0.481ms returns FALSE
TA260 001:960.452 JLINK_HasError()
TA260 001:961.962 JLINK_IsHalted()
TA260 001:962.390 - 0.428ms returns FALSE
TA260 001:962.396 JLINK_HasError()
TA260 001:963.963 JLINK_IsHalted()
TA260 001:964.433 - 0.470ms returns FALSE
TA260 001:964.440 JLINK_HasError()
TA260 001:965.962 JLINK_IsHalted()
TA260 001:966.434 - 0.471ms returns FALSE
TA260 001:966.439 JLINK_HasError()
TA260 001:968.466 JLINK_IsHalted()
TA260 001:968.900 - 0.433ms returns FALSE
TA260 001:968.906 JLINK_HasError()
TA260 001:969.967 JLINK_IsHalted()
TA260 001:970.392 - 0.424ms returns FALSE
TA260 001:970.401 JLINK_HasError()
TA260 001:971.971 JLINK_IsHalted()
TA260 001:972.420 - 0.448ms returns FALSE
TA260 001:972.431 JLINK_HasError()
TA260 001:973.967 JLINK_IsHalted()
TA260 001:974.418 - 0.451ms returns FALSE
TA260 001:974.424 JLINK_HasError()
TA260 001:975.968 JLINK_IsHalted()
TA260 001:976.491 - 0.522ms returns FALSE
TA260 001:976.496 JLINK_HasError()
TA260 001:978.475 JLINK_IsHalted()
TA260 001:979.035 - 0.560ms returns FALSE
TA260 001:979.041 JLINK_HasError()
TA260 001:980.475 JLINK_IsHalted()
TA260 001:980.913 - 0.437ms returns FALSE
TA260 001:980.921 JLINK_HasError()
TA260 001:982.474 JLINK_IsHalted()
TA260 001:982.944 - 0.470ms returns FALSE
TA260 001:982.950 JLINK_HasError()
TA260 001:984.478 JLINK_IsHalted()
TA260 001:984.963 - 0.485ms returns FALSE
TA260 001:984.970 JLINK_HasError()
TA260 001:986.978 JLINK_IsHalted()
TA260 001:987.462 - 0.484ms returns FALSE
TA260 001:987.471 JLINK_HasError()
TA260 001:988.982 JLINK_IsHalted()
TA260 001:989.472 - 0.489ms returns FALSE
TA260 001:989.481 JLINK_HasError()
TA260 001:990.983 JLINK_IsHalted()
TA260 001:991.437 - 0.454ms returns FALSE
TA260 001:991.445 JLINK_HasError()
TA260 001:992.985 JLINK_IsHalted()
TA260 001:993.431 - 0.446ms returns FALSE
TA260 001:993.438 JLINK_HasError()
TA260 001:994.981 JLINK_IsHalted()
TA260 001:995.441 - 0.459ms returns FALSE
TA260 001:995.457 JLINK_HasError()
TA260 001:997.488 JLINK_IsHalted()
TA260 001:997.949 - 0.460ms returns FALSE
TA260 001:997.960 JLINK_HasError()
TA260 001:999.493 JLINK_IsHalted()
TA260 002:000.011 - 0.517ms returns FALSE
TA260 002:000.028 JLINK_HasError()
TA260 002:001.510 JLINK_IsHalted()
TA260 002:002.025 - 0.515ms returns FALSE
TA260 002:002.038 JLINK_HasError()
TA260 002:003.499 JLINK_IsHalted()
TA260 002:004.014 - 0.514ms returns FALSE
TA260 002:004.028 JLINK_HasError()
TA260 002:008.025 JLINK_IsHalted()
TA260 002:008.632 - 0.607ms returns FALSE
TA260 002:008.652 JLINK_HasError()
TA260 002:010.019 JLINK_IsHalted()
TA260 002:010.536 - 0.516ms returns FALSE
TA260 002:010.550 JLINK_HasError()
TA260 002:012.023 JLINK_IsHalted()
TA260 002:012.581 - 0.557ms returns FALSE
TA260 002:012.608 JLINK_HasError()
TA260 002:014.025 JLINK_IsHalted()
TA260 002:014.640 - 0.615ms returns FALSE
TA260 002:014.663 JLINK_HasError()
TA260 002:016.017 JLINK_IsHalted()
TA260 002:016.549 - 0.531ms returns FALSE
TA260 002:016.578 JLINK_HasError()
TA260 002:018.527 JLINK_IsHalted()
TA260 002:019.044 - 0.517ms returns FALSE
TA260 002:019.053 JLINK_HasError()
TA260 002:020.533 JLINK_IsHalted()
TA260 002:021.064 - 0.529ms returns FALSE
TA260 002:021.099 JLINK_HasError()
TA260 002:022.529 JLINK_IsHalted()
TA260 002:023.059 - 0.530ms returns FALSE
TA260 002:023.074 JLINK_HasError()
TA260 002:024.535 JLINK_IsHalted()
TA260 002:025.036 - 0.511ms returns FALSE
TA260 002:025.052 JLINK_HasError()
TA260 002:027.027 JLINK_IsHalted()
TA260 002:027.516 - 0.488ms returns FALSE
TA260 002:027.528 JLINK_HasError()
TA260 002:029.029 JLINK_IsHalted()
TA260 002:029.521 - 0.491ms returns FALSE
TA260 002:029.529 JLINK_HasError()
TA260 002:031.031 JLINK_IsHalted()
TA260 002:031.522 - 0.490ms returns FALSE
TA260 002:031.531 JLINK_HasError()
TA260 002:033.029 JLINK_IsHalted()
TA260 002:033.481 - 0.451ms returns FALSE
TA260 002:033.489 JLINK_HasError()
TA260 002:035.038 JLINK_IsHalted()
TA260 002:035.637 - 0.599ms returns FALSE
TA260 002:035.653 JLINK_HasError()
TA260 002:037.548 JLINK_IsHalted()
TA260 002:038.061 - 0.513ms returns FALSE
TA260 002:038.079 JLINK_HasError()
TA260 002:039.544 JLINK_IsHalted()
TA260 002:040.016 - 0.472ms returns FALSE
TA260 002:040.036 JLINK_HasError()
TA260 002:041.552 JLINK_IsHalted()
TA260 002:042.101 - 0.550ms returns FALSE
TA260 002:042.119 JLINK_HasError()
TA260 002:043.550 JLINK_IsHalted()
TA260 002:044.023 - 0.473ms returns FALSE
TA260 002:044.039 JLINK_HasError()
TA260 002:046.047 JLINK_IsHalted()
TA260 002:046.629 - 0.581ms returns FALSE
TA260 002:046.644 JLINK_HasError()
TA260 002:048.650 JLINK_IsHalted()
TA260 002:049.122 - 0.470ms returns FALSE
TA260 002:049.133 JLINK_HasError()
TA260 002:050.654 JLINK_IsHalted()
TA260 002:051.128 - 0.474ms returns FALSE
TA260 002:051.138 JLINK_HasError()
TA260 002:052.652 JLINK_IsHalted()
TA260 002:053.130 - 0.478ms returns FALSE
TA260 002:053.136 JLINK_HasError()
TA260 002:054.648 JLINK_IsHalted()
TA260 002:055.142 - 0.493ms returns FALSE
TA260 002:055.152 JLINK_HasError()
TA260 002:057.158 JLINK_IsHalted()
TA260 002:057.667 - 0.509ms returns FALSE
TA260 002:057.677 JLINK_HasError()
TA260 002:059.154 JLINK_IsHalted()
TA260 002:059.618 - 0.463ms returns FALSE
TA260 002:059.627 JLINK_HasError()
TA260 002:061.158 JLINK_IsHalted()
TA260 002:061.623 - 0.464ms returns FALSE
TA260 002:061.641 JLINK_HasError()
TA260 002:062.763 JLINK_IsHalted()
TA260 002:063.212 - 0.449ms returns FALSE
TA260 002:063.220 JLINK_HasError()
TA260 002:066.246 JLINK_IsHalted()
TA260 002:066.693 - 0.447ms returns FALSE
TA260 002:066.701 JLINK_HasError()
TA260 002:068.246 JLINK_IsHalted()
TA260 002:068.768 - 0.521ms returns FALSE
TA260 002:068.776 JLINK_HasError()
TA260 002:070.753 JLINK_IsHalted()
TA260 002:071.244 - 0.490ms returns FALSE
TA260 002:071.251 JLINK_HasError()
TA260 002:072.750 JLINK_IsHalted()
TA260 002:073.265 - 0.515ms returns FALSE
TA260 002:073.272 JLINK_HasError()
TA260 002:074.751 JLINK_IsHalted()
TA260 002:075.267 - 0.516ms returns FALSE
TA260 002:075.274 JLINK_HasError()
TA260 002:077.254 JLINK_IsHalted()
TA260 002:077.796 - 0.542ms returns FALSE
TA260 002:077.802 JLINK_HasError()
TA260 002:079.256 JLINK_IsHalted()
TA260 002:079.754 - 0.497ms returns FALSE
TA260 002:079.761 JLINK_HasError()
TA260 002:081.260 JLINK_IsHalted()
TA260 002:081.774 - 0.513ms returns FALSE
TA260 002:081.789 JLINK_HasError()
TA260 002:083.258 JLINK_IsHalted()
TA260 002:083.670 - 0.411ms returns FALSE
TA260 002:083.679 JLINK_HasError()
TA260 002:085.256 JLINK_IsHalted()
TA260 002:085.664 - 0.408ms returns FALSE
TA260 002:085.673 JLINK_HasError()
TA260 002:086.758 JLINK_IsHalted()
TA260 002:087.266 - 0.508ms returns FALSE
TA260 002:087.278 JLINK_HasError()
TA260 002:088.767 JLINK_IsHalted()
TA260 002:089.214 - 0.446ms returns FALSE
TA260 002:089.223 JLINK_HasError()
TA260 002:090.767 JLINK_IsHalted()
TA260 002:091.209 - 0.441ms returns FALSE
TA260 002:091.216 JLINK_HasError()
TA260 002:092.766 JLINK_IsHalted()
TA260 002:093.320 - 0.554ms returns FALSE
TA260 002:093.328 JLINK_HasError()
TA260 002:094.771 JLINK_IsHalted()
TA260 002:095.256 - 0.484ms returns FALSE
TA260 002:095.268 JLINK_HasError()
TA260 002:097.274 JLINK_IsHalted()
TA260 002:097.798 - 0.523ms returns FALSE
TA260 002:097.813 JLINK_HasError()
TA260 002:099.275 JLINK_IsHalted()
TA260 002:099.753 - 0.477ms returns FALSE
TA260 002:099.760 JLINK_HasError()
TA260 002:101.275 JLINK_IsHalted()
TA260 002:101.774 - 0.499ms returns FALSE
TA260 002:101.791 JLINK_HasError()
TA260 002:103.277 JLINK_IsHalted()
TA260 002:103.758 - 0.480ms returns FALSE
TA260 002:103.767 JLINK_HasError()
TA260 002:105.274 JLINK_IsHalted()
TA260 002:105.766 - 0.491ms returns FALSE
TA260 002:105.771 JLINK_HasError()
TA260 002:107.781 JLINK_IsHalted()
TA260 002:108.248 - 0.466ms returns FALSE
TA260 002:108.262 JLINK_HasError()
TA260 002:109.789 JLINK_IsHalted()
TA260 002:110.293 - 0.503ms returns FALSE
TA260 002:110.306 JLINK_HasError()
TA260 002:111.782 JLINK_IsHalted()
TA260 002:112.296 - 0.513ms returns FALSE
TA260 002:112.304 JLINK_HasError()
TA260 002:113.782 JLINK_IsHalted()
TA260 002:114.284 - 0.502ms returns FALSE
TA260 002:114.295 JLINK_HasError()
TA260 002:116.286 JLINK_IsHalted()
TA260 002:116.769 - 0.483ms returns FALSE
TA260 002:116.776 JLINK_HasError()
TA260 002:118.365 JLINK_IsHalted()
TA260 002:118.937 - 0.571ms returns FALSE
TA260 002:118.944 JLINK_HasError()
TA260 002:120.290 JLINK_IsHalted()
TA260 002:120.770 - 0.479ms returns FALSE
TA260 002:120.776 JLINK_HasError()
TA260 002:122.291 JLINK_IsHalted()
TA260 002:122.849 - 0.557ms returns FALSE
TA260 002:122.870 JLINK_HasError()
TA260 002:124.288 JLINK_IsHalted()
TA260 002:124.774 - 0.486ms returns FALSE
TA260 002:124.795 JLINK_HasError()
TA260 002:126.288 JLINK_IsHalted()
TA260 002:126.801 - 0.511ms returns FALSE
TA260 002:126.808 JLINK_HasError()
TA260 002:128.794 JLINK_IsHalted()
TA260 002:129.370 - 0.576ms returns FALSE
TA260 002:129.377 JLINK_HasError()
TA260 002:130.795 JLINK_IsHalted()
TA260 002:131.269 - 0.473ms returns FALSE
TA260 002:131.279 JLINK_HasError()
TA260 002:132.792 JLINK_IsHalted()
TA260 002:133.264 - 0.472ms returns FALSE
TA260 002:133.273 JLINK_HasError()
TA260 002:134.796 JLINK_IsHalted()
TA260 002:135.254 - 0.457ms returns FALSE
TA260 002:135.260 JLINK_HasError()
TA260 002:136.302 JLINK_IsHalted()
TA260 002:136.763 - 0.461ms returns FALSE
TA260 002:136.769 JLINK_HasError()
TA260 002:138.313 JLINK_IsHalted()
TA260 002:138.939 - 0.626ms returns FALSE
TA260 002:138.948 JLINK_HasError()
TA260 002:140.304 JLINK_IsHalted()
TA260 002:140.862 - 0.556ms returns FALSE
TA260 002:140.878 JLINK_HasError()
TA260 002:144.308 JLINK_IsHalted()
TA260 002:144.756 - 0.447ms returns FALSE
TA260 002:144.763 JLINK_HasError()
TA260 002:146.304 JLINK_IsHalted()
TA260 002:146.772 - 0.467ms returns FALSE
TA260 002:146.778 JLINK_HasError()
TA260 002:148.806 JLINK_IsHalted()
TA260 002:149.288 - 0.481ms returns FALSE
TA260 002:149.297 JLINK_HasError()
TA260 002:150.811 JLINK_IsHalted()
TA260 002:151.347 - 0.536ms returns FALSE
TA260 002:151.355 JLINK_HasError()
TA260 002:152.809 JLINK_IsHalted()
TA260 002:153.311 - 0.501ms returns FALSE
TA260 002:153.322 JLINK_HasError()
TA260 002:154.809 JLINK_IsHalted()
TA260 002:155.469 - 0.659ms returns FALSE
TA260 002:155.480 JLINK_HasError()
TA260 002:157.314 JLINK_IsHalted()
TA260 002:157.846 - 0.532ms returns FALSE
TA260 002:157.854 JLINK_HasError()
TA260 002:159.316 JLINK_IsHalted()
TA260 002:159.852 - 0.534ms returns FALSE
TA260 002:159.874 JLINK_HasError()
TA260 002:161.316 JLINK_IsHalted()
TA260 002:161.775 - 0.458ms returns FALSE
TA260 002:161.791 JLINK_HasError()
TA260 002:163.314 JLINK_IsHalted()
TA260 002:163.788 - 0.474ms returns FALSE
TA260 002:163.797 JLINK_HasError()
TA260 002:165.316 JLINK_IsHalted()
TA260 002:165.814 - 0.498ms returns FALSE
TA260 002:165.824 JLINK_HasError()
TA260 002:167.837 JLINK_IsHalted()
TA260 002:168.346 - 0.508ms returns FALSE
TA260 002:168.356 JLINK_HasError()
TA260 002:169.830 JLINK_IsHalted()
TA260 002:170.303 - 0.471ms returns FALSE
TA260 002:170.317 JLINK_HasError()
TA260 002:172.330 JLINK_IsHalted()
TA260 002:172.852 - 0.522ms returns FALSE
TA260 002:172.866 JLINK_HasError()
TA260 002:175.327 JLINK_IsHalted()
TA260 002:175.855 - 0.527ms returns FALSE
TA260 002:175.863 JLINK_HasError()
TA260 002:177.840 JLINK_IsHalted()
TA260 002:178.336 - 0.495ms returns FALSE
TA260 002:178.344 JLINK_HasError()
TA260 002:179.841 JLINK_IsHalted()
TA260 002:180.365 - 0.524ms returns FALSE
TA260 002:180.376 JLINK_HasError()
TA260 002:181.834 JLINK_IsHalted()
TA260 002:182.315 - 0.481ms returns FALSE
TA260 002:182.326 JLINK_HasError()
TA260 002:183.924 JLINK_IsHalted()
TA260 002:186.248   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:186.756 - 2.831ms returns TRUE
TA260 002:186.766 JLINK_ReadReg(R15 (PC))
TA260 002:186.772 - 0.005ms returns 0x20000000
TA260 002:186.777 JLINK_ClrBPEx(BPHandle = 0x00000009)
TA260 002:186.781 - 0.004ms returns 0x00
TA260 002:186.785 JLINK_ReadReg(R0)
TA260 002:186.789 - 0.003ms returns 0x00000000
TA260 002:187.079 JLINK_HasError()
TA260 002:187.109 JLINK_WriteReg(R0, 0x00000001)
TA260 002:187.114 - 0.005ms returns 0
TA260 002:187.119 JLINK_WriteReg(R1, 0x00004000)
TA260 002:187.122 - 0.003ms returns 0
TA260 002:187.126 JLINK_WriteReg(R2, 0x000000FF)
TA260 002:187.130 - 0.003ms returns 0
TA260 002:187.134 JLINK_WriteReg(R3, 0x00000000)
TA260 002:187.138 - 0.004ms returns 0
TA260 002:187.142 JLINK_WriteReg(R4, 0x00000000)
TA260 002:187.145 - 0.003ms returns 0
TA260 002:187.149 JLINK_WriteReg(R5, 0x00000000)
TA260 002:187.153 - 0.003ms returns 0
TA260 002:187.157 JLINK_WriteReg(R6, 0x00000000)
TA260 002:187.160 - 0.003ms returns 0
TA260 002:187.172 JLINK_WriteReg(R7, 0x00000000)
TA260 002:187.176 - 0.003ms returns 0
TA260 002:187.180 JLINK_WriteReg(R8, 0x00000000)
TA260 002:187.184 - 0.003ms returns 0
TA260 002:187.188 JLINK_WriteReg(R9, 0x20000180)
TA260 002:187.191 - 0.003ms returns 0
TA260 002:187.195 JLINK_WriteReg(R10, 0x00000000)
TA260 002:187.199 - 0.003ms returns 0
TA260 002:187.203 JLINK_WriteReg(R11, 0x00000000)
TA260 002:187.206 - 0.003ms returns 0
TA260 002:187.210 JLINK_WriteReg(R12, 0x00000000)
TA260 002:187.214 - 0.003ms returns 0
TA260 002:187.218 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:187.222 - 0.004ms returns 0
TA260 002:187.226 JLINK_WriteReg(R14, 0x20000001)
TA260 002:187.230 - 0.003ms returns 0
TA260 002:187.234 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 002:187.237 - 0.003ms returns 0
TA260 002:187.241 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:187.245 - 0.003ms returns 0
TA260 002:187.249 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:187.252 - 0.003ms returns 0
TA260 002:187.256 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:187.260 - 0.003ms returns 0
TA260 002:187.264 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:187.267 - 0.003ms returns 0
TA260 002:187.272 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:187.276 - 0.004ms returns 0x0000000A
TA260 002:187.280 JLINK_Go()
TA260 002:187.290   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:190.158 - 2.877ms 
TA260 002:190.183 JLINK_IsHalted()
TA260 002:192.536   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:193.098 - 2.915ms returns TRUE
TA260 002:193.108 JLINK_ReadReg(R15 (PC))
TA260 002:193.115 - 0.006ms returns 0x20000000
TA260 002:193.120 JLINK_ClrBPEx(BPHandle = 0x0000000A)
TA260 002:193.124 - 0.003ms returns 0x00
TA260 002:193.128 JLINK_ReadReg(R0)
TA260 002:193.132 - 0.003ms returns 0x00000000
TA260 002:249.207 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 002:249.221   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 002:249.238   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 002:251.146 - 1.938ms returns 0x184
TA260 002:251.203 JLINK_HasError()
TA260 002:251.211 JLINK_WriteReg(R0, 0x08000000)
TA260 002:251.217 - 0.006ms returns 0
TA260 002:251.221 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 002:251.225 - 0.003ms returns 0
TA260 002:251.229 JLINK_WriteReg(R2, 0x00000002)
TA260 002:251.232 - 0.003ms returns 0
TA260 002:251.236 JLINK_WriteReg(R3, 0x00000000)
TA260 002:251.240 - 0.003ms returns 0
TA260 002:251.244 JLINK_WriteReg(R4, 0x00000000)
TA260 002:251.247 - 0.003ms returns 0
TA260 002:251.252 JLINK_WriteReg(R5, 0x00000000)
TA260 002:251.255 - 0.003ms returns 0
TA260 002:251.259 JLINK_WriteReg(R6, 0x00000000)
TA260 002:251.262 - 0.003ms returns 0
TA260 002:251.266 JLINK_WriteReg(R7, 0x00000000)
TA260 002:251.270 - 0.003ms returns 0
TA260 002:251.274 JLINK_WriteReg(R8, 0x00000000)
TA260 002:251.277 - 0.003ms returns 0
TA260 002:251.281 JLINK_WriteReg(R9, 0x20000180)
TA260 002:251.285 - 0.003ms returns 0
TA260 002:251.289 JLINK_WriteReg(R10, 0x00000000)
TA260 002:251.292 - 0.003ms returns 0
TA260 002:251.296 JLINK_WriteReg(R11, 0x00000000)
TA260 002:251.300 - 0.003ms returns 0
TA260 002:251.304 JLINK_WriteReg(R12, 0x00000000)
TA260 002:251.307 - 0.003ms returns 0
TA260 002:251.312 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:251.315 - 0.004ms returns 0
TA260 002:251.319 JLINK_WriteReg(R14, 0x20000001)
TA260 002:251.323 - 0.003ms returns 0
TA260 002:251.327 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 002:251.330 - 0.003ms returns 0
TA260 002:251.334 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:251.338 - 0.003ms returns 0
TA260 002:251.342 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:251.346 - 0.003ms returns 0
TA260 002:251.350 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:251.353 - 0.003ms returns 0
TA260 002:251.357 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:251.360 - 0.003ms returns 0
TA260 002:251.365 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:251.373   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:251.858 - 0.492ms returns 0x0000000B
TA260 002:251.878 JLINK_Go()
TA260 002:251.887   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 002:252.346   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:255.270 - 3.390ms 
TA260 002:255.288 JLINK_IsHalted()
TA260 002:257.703   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:258.242 - 2.954ms returns TRUE
TA260 002:258.251 JLINK_ReadReg(R15 (PC))
TA260 002:258.256 - 0.005ms returns 0x20000000
TA260 002:258.261 JLINK_ClrBPEx(BPHandle = 0x0000000B)
TA260 002:258.264 - 0.003ms returns 0x00
TA260 002:258.269 JLINK_ReadReg(R0)
TA260 002:258.272 - 0.003ms returns 0x00000000
TA260 002:258.562 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:258.570   Data:  18 18 00 20 C1 01 00 08 59 2B 00 08 3D 28 00 08 ...
TA260 002:258.582   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:261.240 - 2.678ms returns 0x27C
TA260 002:261.259 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:261.263   Data:  10 B5 13 46 0A 46 04 46 19 46 FF F7 F0 FF 20 46 ...
TA260 002:261.278   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:263.176 - 1.916ms returns 0x184
TA260 002:263.198 JLINK_HasError()
TA260 002:263.204 JLINK_WriteReg(R0, 0x08000000)
TA260 002:263.210 - 0.006ms returns 0
TA260 002:263.214 JLINK_WriteReg(R1, 0x00000400)
TA260 002:263.218 - 0.003ms returns 0
TA260 002:263.222 JLINK_WriteReg(R2, 0x20000184)
TA260 002:263.225 - 0.003ms returns 0
TA260 002:263.229 JLINK_WriteReg(R3, 0x00000000)
TA260 002:263.233 - 0.003ms returns 0
TA260 002:263.237 JLINK_WriteReg(R4, 0x00000000)
TA260 002:263.240 - 0.003ms returns 0
TA260 002:263.245 JLINK_WriteReg(R5, 0x00000000)
TA260 002:263.248 - 0.003ms returns 0
TA260 002:263.252 JLINK_WriteReg(R6, 0x00000000)
TA260 002:263.255 - 0.003ms returns 0
TA260 002:263.260 JLINK_WriteReg(R7, 0x00000000)
TA260 002:263.263 - 0.004ms returns 0
TA260 002:263.267 JLINK_WriteReg(R8, 0x00000000)
TA260 002:263.271 - 0.003ms returns 0
TA260 002:263.275 JLINK_WriteReg(R9, 0x20000180)
TA260 002:263.278 - 0.003ms returns 0
TA260 002:263.282 JLINK_WriteReg(R10, 0x00000000)
TA260 002:263.286 - 0.003ms returns 0
TA260 002:263.290 JLINK_WriteReg(R11, 0x00000000)
TA260 002:263.293 - 0.003ms returns 0
TA260 002:263.297 JLINK_WriteReg(R12, 0x00000000)
TA260 002:263.301 - 0.003ms returns 0
TA260 002:263.305 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:263.309 - 0.003ms returns 0
TA260 002:263.313 JLINK_WriteReg(R14, 0x20000001)
TA260 002:263.316 - 0.003ms returns 0
TA260 002:263.320 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:263.324 - 0.003ms returns 0
TA260 002:263.328 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:263.331 - 0.003ms returns 0
TA260 002:263.335 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:263.339 - 0.003ms returns 0
TA260 002:263.343 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:263.346 - 0.003ms returns 0
TA260 002:263.350 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:263.353 - 0.003ms returns 0
TA260 002:263.358 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:263.363 - 0.004ms returns 0x0000000C
TA260 002:263.367 JLINK_Go()
TA260 002:263.376   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:266.181 - 2.813ms 
TA260 002:266.196 JLINK_IsHalted()
TA260 002:266.651 - 0.455ms returns FALSE
TA260 002:266.658 JLINK_HasError()
TA260 002:269.797 JLINK_IsHalted()
TA260 002:270.329 - 0.532ms returns FALSE
TA260 002:270.347 JLINK_HasError()
TA260 002:271.789 JLINK_IsHalted()
TA260 002:274.156   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:274.657 - 2.867ms returns TRUE
TA260 002:274.666 JLINK_ReadReg(R15 (PC))
TA260 002:274.671 - 0.005ms returns 0x20000000
TA260 002:274.675 JLINK_ClrBPEx(BPHandle = 0x0000000C)
TA260 002:274.679 - 0.003ms returns 0x00
TA260 002:274.684 JLINK_ReadReg(R0)
TA260 002:274.687 - 0.003ms returns 0x00000000
TA260 002:275.108 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:275.118   Data:  5B D0 C3 F3 0A 54 C1 F3 0A 55 2C 44 A4 F2 F3 34 ...
TA260 002:275.129   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:277.728 - 2.619ms returns 0x27C
TA260 002:277.747 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:277.756   Data:  01 02 06 D0 0A 0D A2 F5 60 72 C1 F3 13 01 00 2A ...
TA260 002:277.769   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:279.656 - 1.908ms returns 0x184
TA260 002:279.670 JLINK_HasError()
TA260 002:279.709 JLINK_WriteReg(R0, 0x08000400)
TA260 002:279.716 - 0.006ms returns 0
TA260 002:279.720 JLINK_WriteReg(R1, 0x00000400)
TA260 002:279.724 - 0.003ms returns 0
TA260 002:279.728 JLINK_WriteReg(R2, 0x20000184)
TA260 002:279.732 - 0.003ms returns 0
TA260 002:279.736 JLINK_WriteReg(R3, 0x00000000)
TA260 002:279.740 - 0.003ms returns 0
TA260 002:279.744 JLINK_WriteReg(R4, 0x00000000)
TA260 002:279.747 - 0.003ms returns 0
TA260 002:279.751 JLINK_WriteReg(R5, 0x00000000)
TA260 002:279.754 - 0.003ms returns 0
TA260 002:279.926 JLINK_WriteReg(R6, 0x00000000)
TA260 002:279.930 - 0.004ms returns 0
TA260 002:279.934 JLINK_WriteReg(R7, 0x00000000)
TA260 002:279.937 - 0.003ms returns 0
TA260 002:279.941 JLINK_WriteReg(R8, 0x00000000)
TA260 002:279.945 - 0.004ms returns 0
TA260 002:279.950 JLINK_WriteReg(R9, 0x20000180)
TA260 002:279.953 - 0.003ms returns 0
TA260 002:279.957 JLINK_WriteReg(R10, 0x00000000)
TA260 002:279.960 - 0.003ms returns 0
TA260 002:279.965 JLINK_WriteReg(R11, 0x00000000)
TA260 002:279.968 - 0.003ms returns 0
TA260 002:279.972 JLINK_WriteReg(R12, 0x00000000)
TA260 002:279.976 - 0.003ms returns 0
TA260 002:279.980 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:279.984 - 0.003ms returns 0
TA260 002:279.989 JLINK_WriteReg(R14, 0x20000001)
TA260 002:279.992 - 0.003ms returns 0
TA260 002:279.996 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:280.000 - 0.003ms returns 0
TA260 002:280.008 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:280.012 - 0.003ms returns 0
TA260 002:280.016 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:280.019 - 0.003ms returns 0
TA260 002:280.023 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:280.026 - 0.003ms returns 0
TA260 002:280.030 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:280.034 - 0.003ms returns 0
TA260 002:280.039 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:280.043 - 0.004ms returns 0x0000000D
TA260 002:280.048 JLINK_Go()
TA260 002:280.058   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:282.861 - 2.812ms 
TA260 002:282.888 JLINK_IsHalted()
TA260 002:283.423 - 0.534ms returns FALSE
TA260 002:283.435 JLINK_HasError()
TA260 002:285.295 JLINK_IsHalted()
TA260 002:285.770 - 0.474ms returns FALSE
TA260 002:285.777 JLINK_HasError()
TA260 002:287.798 JLINK_IsHalted()
TA260 002:290.143   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:290.663 - 2.864ms returns TRUE
TA260 002:290.672 JLINK_ReadReg(R15 (PC))
TA260 002:290.678 - 0.006ms returns 0x20000000
TA260 002:290.682 JLINK_ClrBPEx(BPHandle = 0x0000000D)
TA260 002:290.686 - 0.003ms returns 0x00
TA260 002:290.691 JLINK_ReadReg(R0)
TA260 002:290.694 - 0.003ms returns 0x00000000
TA260 002:291.063 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:291.071   Data:  F0 4D 92 46 9B 46 11 B1 B1 FA 81 F2 02 E0 B0 FA ...
TA260 002:291.083   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:293.724 - 2.661ms returns 0x27C
TA260 002:293.747 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:293.752   Data:  35 FE 20 46 4F F4 00 51 00 22 00 F0 2F FE 01 20 ...
TA260 002:293.769   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:295.631 - 1.882ms returns 0x184
TA260 002:295.648 JLINK_HasError()
TA260 002:295.654 JLINK_WriteReg(R0, 0x08000800)
TA260 002:295.660 - 0.006ms returns 0
TA260 002:295.665 JLINK_WriteReg(R1, 0x00000400)
TA260 002:295.668 - 0.003ms returns 0
TA260 002:295.672 JLINK_WriteReg(R2, 0x20000184)
TA260 002:295.676 - 0.003ms returns 0
TA260 002:295.681 JLINK_WriteReg(R3, 0x00000000)
TA260 002:295.684 - 0.003ms returns 0
TA260 002:295.688 JLINK_WriteReg(R4, 0x00000000)
TA260 002:295.692 - 0.003ms returns 0
TA260 002:295.696 JLINK_WriteReg(R5, 0x00000000)
TA260 002:295.699 - 0.003ms returns 0
TA260 002:295.703 JLINK_WriteReg(R6, 0x00000000)
TA260 002:295.706 - 0.003ms returns 0
TA260 002:295.711 JLINK_WriteReg(R7, 0x00000000)
TA260 002:295.714 - 0.003ms returns 0
TA260 002:295.767 JLINK_WriteReg(R8, 0x00000000)
TA260 002:295.770 - 0.004ms returns 0
TA260 002:295.775 JLINK_WriteReg(R9, 0x20000180)
TA260 002:295.778 - 0.003ms returns 0
TA260 002:295.782 JLINK_WriteReg(R10, 0x00000000)
TA260 002:295.797 - 0.014ms returns 0
TA260 002:295.802 JLINK_WriteReg(R11, 0x00000000)
TA260 002:295.805 - 0.003ms returns 0
TA260 002:295.809 JLINK_WriteReg(R12, 0x00000000)
TA260 002:295.812 - 0.003ms returns 0
TA260 002:295.817 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:295.821 - 0.004ms returns 0
TA260 002:295.825 JLINK_WriteReg(R14, 0x20000001)
TA260 002:295.828 - 0.003ms returns 0
TA260 002:295.832 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:295.837 - 0.004ms returns 0
TA260 002:295.841 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:295.845 - 0.004ms returns 0
TA260 002:295.849 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:295.853 - 0.004ms returns 0
TA260 002:295.857 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:295.860 - 0.003ms returns 0
TA260 002:295.864 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:295.868 - 0.003ms returns 0
TA260 002:295.872 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:295.877 - 0.004ms returns 0x0000000E
TA260 002:295.881 JLINK_Go()
TA260 002:295.891   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:298.666 - 2.784ms 
TA260 002:298.681 JLINK_IsHalted()
TA260 002:299.175 - 0.493ms returns FALSE
TA260 002:299.182 JLINK_HasError()
TA260 002:300.309 JLINK_IsHalted()
TA260 002:300.818 - 0.508ms returns FALSE
TA260 002:300.826 JLINK_HasError()
TA260 002:302.308 JLINK_IsHalted()
TA260 002:302.754 - 0.445ms returns FALSE
TA260 002:302.761 JLINK_HasError()
TA260 002:304.306 JLINK_IsHalted()
TA260 002:306.641   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:307.177 - 2.871ms returns TRUE
TA260 002:307.192 JLINK_ReadReg(R15 (PC))
TA260 002:307.197 - 0.006ms returns 0x20000000
TA260 002:307.234 JLINK_ClrBPEx(BPHandle = 0x0000000E)
TA260 002:307.239 - 0.005ms returns 0x00
TA260 002:307.243 JLINK_ReadReg(R0)
TA260 002:307.247 - 0.003ms returns 0x00000000
TA260 002:307.679 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:307.689   Data:  BD E8 F0 40 00 F0 70 BD 2D E9 F0 4F 81 B0 41 F6 ...
TA260 002:307.701   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:310.316 - 2.636ms returns 0x27C
TA260 002:310.331 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:310.336   Data:  05 F0 01 02 30 46 4F F4 80 51 00 F0 2F FC 20 46 ...
TA260 002:310.346   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:312.223 - 1.891ms returns 0x184
TA260 002:312.243 JLINK_HasError()
TA260 002:312.256 JLINK_WriteReg(R0, 0x08000C00)
TA260 002:312.262 - 0.006ms returns 0
TA260 002:312.267 JLINK_WriteReg(R1, 0x00000400)
TA260 002:312.270 - 0.003ms returns 0
TA260 002:312.275 JLINK_WriteReg(R2, 0x20000184)
TA260 002:312.278 - 0.003ms returns 0
TA260 002:312.282 JLINK_WriteReg(R3, 0x00000000)
TA260 002:312.286 - 0.003ms returns 0
TA260 002:312.290 JLINK_WriteReg(R4, 0x00000000)
TA260 002:312.293 - 0.003ms returns 0
TA260 002:312.297 JLINK_WriteReg(R5, 0x00000000)
TA260 002:312.300 - 0.003ms returns 0
TA260 002:312.304 JLINK_WriteReg(R6, 0x00000000)
TA260 002:312.308 - 0.003ms returns 0
TA260 002:312.312 JLINK_WriteReg(R7, 0x00000000)
TA260 002:312.315 - 0.003ms returns 0
TA260 002:312.319 JLINK_WriteReg(R8, 0x00000000)
TA260 002:312.323 - 0.003ms returns 0
TA260 002:312.327 JLINK_WriteReg(R9, 0x20000180)
TA260 002:312.330 - 0.003ms returns 0
TA260 002:312.334 JLINK_WriteReg(R10, 0x00000000)
TA260 002:312.338 - 0.003ms returns 0
TA260 002:312.342 JLINK_WriteReg(R11, 0x00000000)
TA260 002:312.345 - 0.003ms returns 0
TA260 002:312.349 JLINK_WriteReg(R12, 0x00000000)
TA260 002:312.353 - 0.003ms returns 0
TA260 002:312.357 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:312.361 - 0.004ms returns 0
TA260 002:312.365 JLINK_WriteReg(R14, 0x20000001)
TA260 002:312.368 - 0.003ms returns 0
TA260 002:312.373 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:312.376 - 0.003ms returns 0
TA260 002:312.380 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:312.391 - 0.010ms returns 0
TA260 002:312.395 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:312.398 - 0.003ms returns 0
TA260 002:312.402 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:312.406 - 0.003ms returns 0
TA260 002:312.410 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:312.413 - 0.003ms returns 0
TA260 002:312.418 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:312.422 - 0.004ms returns 0x0000000F
TA260 002:312.426 JLINK_Go()
TA260 002:312.436   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:315.209 - 2.781ms 
TA260 002:315.228 JLINK_IsHalted()
TA260 002:315.756 - 0.528ms returns FALSE
TA260 002:315.774 JLINK_HasError()
TA260 002:317.319 JLINK_IsHalted()
TA260 002:317.789 - 0.469ms returns FALSE
TA260 002:317.802 JLINK_HasError()
TA260 002:319.324 JLINK_IsHalted()
TA260 002:321.727   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:322.249 - 2.925ms returns TRUE
TA260 002:322.266 JLINK_ReadReg(R15 (PC))
TA260 002:322.273 - 0.006ms returns 0x20000000
TA260 002:322.278 JLINK_ClrBPEx(BPHandle = 0x0000000F)
TA260 002:322.282 - 0.004ms returns 0x00
TA260 002:322.286 JLINK_ReadReg(R0)
TA260 002:322.290 - 0.003ms returns 0x00000000
TA260 002:322.681 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:322.689   Data:  00 90 00 98 14 28 E7 DB 02 B0 BD EC 0A 8B BD E8 ...
TA260 002:322.700   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:325.360 - 2.679ms returns 0x27C
TA260 002:325.373 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:325.377   Data:  B0 B5 53 F8 22 C0 00 20 CC F6 F0 70 0C EA 00 0C ...
TA260 002:325.386   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:327.263 - 1.889ms returns 0x184
TA260 002:327.281 JLINK_HasError()
TA260 002:327.288 JLINK_WriteReg(R0, 0x08001000)
TA260 002:327.295 - 0.006ms returns 0
TA260 002:327.300 JLINK_WriteReg(R1, 0x00000400)
TA260 002:327.305 - 0.004ms returns 0
TA260 002:327.311 JLINK_WriteReg(R2, 0x20000184)
TA260 002:327.315 - 0.004ms returns 0
TA260 002:327.320 JLINK_WriteReg(R3, 0x00000000)
TA260 002:327.325 - 0.005ms returns 0
TA260 002:327.330 JLINK_WriteReg(R4, 0x00000000)
TA260 002:327.333 - 0.003ms returns 0
TA260 002:327.338 JLINK_WriteReg(R5, 0x00000000)
TA260 002:327.341 - 0.003ms returns 0
TA260 002:327.345 JLINK_WriteReg(R6, 0x00000000)
TA260 002:327.349 - 0.003ms returns 0
TA260 002:327.353 JLINK_WriteReg(R7, 0x00000000)
TA260 002:327.356 - 0.003ms returns 0
TA260 002:327.360 JLINK_WriteReg(R8, 0x00000000)
TA260 002:327.364 - 0.003ms returns 0
TA260 002:327.368 JLINK_WriteReg(R9, 0x20000180)
TA260 002:327.371 - 0.003ms returns 0
TA260 002:327.375 JLINK_WriteReg(R10, 0x00000000)
TA260 002:327.378 - 0.003ms returns 0
TA260 002:327.382 JLINK_WriteReg(R11, 0x00000000)
TA260 002:327.386 - 0.003ms returns 0
TA260 002:327.390 JLINK_WriteReg(R12, 0x00000000)
TA260 002:327.394 - 0.003ms returns 0
TA260 002:327.398 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:327.402 - 0.004ms returns 0
TA260 002:327.406 JLINK_WriteReg(R14, 0x20000001)
TA260 002:327.410 - 0.003ms returns 0
TA260 002:327.415 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:327.418 - 0.004ms returns 0
TA260 002:327.422 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:327.426 - 0.003ms returns 0
TA260 002:327.430 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:327.433 - 0.003ms returns 0
TA260 002:327.437 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:327.441 - 0.003ms returns 0
TA260 002:327.445 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:327.448 - 0.003ms returns 0
TA260 002:327.453 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:327.458 - 0.005ms returns 0x00000010
TA260 002:327.462 JLINK_Go()
TA260 002:327.472   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:330.323 - 2.860ms 
TA260 002:330.344 JLINK_IsHalted()
TA260 002:330.858 - 0.513ms returns FALSE
TA260 002:330.865 JLINK_HasError()
TA260 002:332.838 JLINK_IsHalted()
TA260 002:333.332 - 0.494ms returns FALSE
TA260 002:333.339 JLINK_HasError()
TA260 002:334.828 JLINK_IsHalted()
TA260 002:337.212   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:337.801 - 2.972ms returns TRUE
TA260 002:337.809 JLINK_ReadReg(R15 (PC))
TA260 002:337.815 - 0.005ms returns 0x20000000
TA260 002:337.819 JLINK_ClrBPEx(BPHandle = 0x00000010)
TA260 002:337.823 - 0.003ms returns 0x00
TA260 002:337.827 JLINK_ReadReg(R0)
TA260 002:337.831 - 0.003ms returns 0x00000000
TA260 002:338.226 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:338.236   Data:  10 40 02 21 01 71 00 21 01 22 01 61 02 71 08 46 ...
TA260 002:338.261   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:340.906 - 2.679ms returns 0x27C
TA260 002:340.922 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:340.926   Data:  AB 43 01 9D 05 FA 06 F6 33 43 42 F8 0C 30 22 68 ...
TA260 002:340.939   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:342.856 - 1.933ms returns 0x184
TA260 002:342.874 JLINK_HasError()
TA260 002:342.880 JLINK_WriteReg(R0, 0x08001400)
TA260 002:342.888 - 0.007ms returns 0
TA260 002:342.892 JLINK_WriteReg(R1, 0x00000400)
TA260 002:342.895 - 0.003ms returns 0
TA260 002:342.900 JLINK_WriteReg(R2, 0x20000184)
TA260 002:342.903 - 0.003ms returns 0
TA260 002:342.908 JLINK_WriteReg(R3, 0x00000000)
TA260 002:342.911 - 0.003ms returns 0
TA260 002:342.915 JLINK_WriteReg(R4, 0x00000000)
TA260 002:342.919 - 0.003ms returns 0
TA260 002:342.923 JLINK_WriteReg(R5, 0x00000000)
TA260 002:342.926 - 0.003ms returns 0
TA260 002:342.930 JLINK_WriteReg(R6, 0x00000000)
TA260 002:342.934 - 0.003ms returns 0
TA260 002:342.938 JLINK_WriteReg(R7, 0x00000000)
TA260 002:342.941 - 0.003ms returns 0
TA260 002:342.946 JLINK_WriteReg(R8, 0x00000000)
TA260 002:342.949 - 0.003ms returns 0
TA260 002:342.953 JLINK_WriteReg(R9, 0x20000180)
TA260 002:342.956 - 0.003ms returns 0
TA260 002:342.960 JLINK_WriteReg(R10, 0x00000000)
TA260 002:342.964 - 0.003ms returns 0
TA260 002:342.968 JLINK_WriteReg(R11, 0x00000000)
TA260 002:342.971 - 0.003ms returns 0
TA260 002:342.975 JLINK_WriteReg(R12, 0x00000000)
TA260 002:342.979 - 0.003ms returns 0
TA260 002:342.983 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:342.987 - 0.004ms returns 0
TA260 002:342.991 JLINK_WriteReg(R14, 0x20000001)
TA260 002:342.995 - 0.003ms returns 0
TA260 002:342.999 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:343.002 - 0.003ms returns 0
TA260 002:343.007 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:343.010 - 0.003ms returns 0
TA260 002:343.014 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:343.123 - 0.108ms returns 0
TA260 002:343.128 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:343.131 - 0.003ms returns 0
TA260 002:343.135 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:343.139 - 0.003ms returns 0
TA260 002:343.144 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:343.148 - 0.004ms returns 0x00000011
TA260 002:343.152 JLINK_Go()
TA260 002:343.162   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:345.902 - 2.749ms 
TA260 002:345.920 JLINK_IsHalted()
TA260 002:346.412 - 0.492ms returns FALSE
TA260 002:346.419 JLINK_HasError()
TA260 002:347.845 JLINK_IsHalted()
TA260 002:348.349 - 0.503ms returns FALSE
TA260 002:348.364 JLINK_HasError()
TA260 002:349.854 JLINK_IsHalted()
TA260 002:352.171   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:352.665 - 2.810ms returns TRUE
TA260 002:352.673 JLINK_ReadReg(R15 (PC))
TA260 002:352.679 - 0.005ms returns 0x20000000
TA260 002:352.684 JLINK_ClrBPEx(BPHandle = 0x00000011)
TA260 002:352.687 - 0.003ms returns 0x00
TA260 002:352.692 JLINK_ReadReg(R0)
TA260 002:352.696 - 0.003ms returns 0x00000000
TA260 002:353.098 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:353.106   Data:  10 B5 4E F6 0C 5C CE F2 00 0C DC F8 00 30 C3 F3 ...
TA260 002:353.118   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:355.734 - 2.634ms returns 0x27C
TA260 002:355.747 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:355.750   Data:  C1 F3 82 21 C0 F6 00 02 51 5C C8 40 70 47 00 00 ...
TA260 002:355.759   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:357.629 - 1.882ms returns 0x184
TA260 002:357.680 JLINK_HasError()
TA260 002:357.686 JLINK_WriteReg(R0, 0x08001800)
TA260 002:357.692 - 0.005ms returns 0
TA260 002:357.704 JLINK_WriteReg(R1, 0x00000400)
TA260 002:357.709 - 0.004ms returns 0
TA260 002:357.713 JLINK_WriteReg(R2, 0x20000184)
TA260 002:357.716 - 0.003ms returns 0
TA260 002:357.720 JLINK_WriteReg(R3, 0x00000000)
TA260 002:357.724 - 0.003ms returns 0
TA260 002:357.728 JLINK_WriteReg(R4, 0x00000000)
TA260 002:357.731 - 0.003ms returns 0
TA260 002:357.736 JLINK_WriteReg(R5, 0x00000000)
TA260 002:357.740 - 0.003ms returns 0
TA260 002:357.744 JLINK_WriteReg(R6, 0x00000000)
TA260 002:357.747 - 0.003ms returns 0
TA260 002:357.751 JLINK_WriteReg(R7, 0x00000000)
TA260 002:357.754 - 0.003ms returns 0
TA260 002:357.758 JLINK_WriteReg(R8, 0x00000000)
TA260 002:357.762 - 0.003ms returns 0
TA260 002:357.766 JLINK_WriteReg(R9, 0x20000180)
TA260 002:357.770 - 0.003ms returns 0
TA260 002:357.774 JLINK_WriteReg(R10, 0x00000000)
TA260 002:357.777 - 0.003ms returns 0
TA260 002:357.781 JLINK_WriteReg(R11, 0x00000000)
TA260 002:357.784 - 0.003ms returns 0
TA260 002:357.788 JLINK_WriteReg(R12, 0x00000000)
TA260 002:357.792 - 0.003ms returns 0
TA260 002:357.796 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:357.801 - 0.004ms returns 0
TA260 002:357.805 JLINK_WriteReg(R14, 0x20000001)
TA260 002:357.808 - 0.003ms returns 0
TA260 002:357.812 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:357.816 - 0.003ms returns 0
TA260 002:357.820 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:357.823 - 0.003ms returns 0
TA260 002:357.827 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:357.831 - 0.003ms returns 0
TA260 002:357.900 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:357.905 - 0.004ms returns 0
TA260 002:357.909 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:357.912 - 0.003ms returns 0
TA260 002:357.917 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:357.921 - 0.004ms returns 0x00000012
TA260 002:357.927 JLINK_Go()
TA260 002:357.937   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:361.185 - 3.257ms 
TA260 002:361.208 JLINK_IsHalted()
TA260 002:361.764 - 0.555ms returns FALSE
TA260 002:361.771 JLINK_HasError()
TA260 002:363.141 JLINK_IsHalted()
TA260 002:363.617 - 0.475ms returns FALSE
TA260 002:363.624 JLINK_HasError()
TA260 002:365.138 JLINK_IsHalted()
TA260 002:367.482   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:367.993 - 2.855ms returns TRUE
TA260 002:368.001 JLINK_ReadReg(R15 (PC))
TA260 002:368.007 - 0.005ms returns 0x20000000
TA260 002:368.011 JLINK_ClrBPEx(BPHandle = 0x00000012)
TA260 002:368.015 - 0.003ms returns 0x00
TA260 002:368.019 JLINK_ReadReg(R0)
TA260 002:368.023 - 0.004ms returns 0x00000000
TA260 002:368.428 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:368.439   Data:  0C 00 08 28 40 F0 85 80 70 68 40 02 00 F1 81 80 ...
TA260 002:368.450   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:371.103 - 2.674ms returns 0x27C
TA260 002:371.126 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:371.130   Data:  3B FC 04 46 30 68 80 01 3F F5 06 AF FF F7 34 FC ...
TA260 002:371.144   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:372.997 - 1.870ms returns 0x184
TA260 002:373.006 JLINK_HasError()
TA260 002:373.012 JLINK_WriteReg(R0, 0x08001C00)
TA260 002:373.018 - 0.005ms returns 0
TA260 002:373.022 JLINK_WriteReg(R1, 0x00000400)
TA260 002:373.026 - 0.004ms returns 0
TA260 002:373.030 JLINK_WriteReg(R2, 0x20000184)
TA260 002:373.034 - 0.003ms returns 0
TA260 002:373.038 JLINK_WriteReg(R3, 0x00000000)
TA260 002:373.041 - 0.003ms returns 0
TA260 002:373.045 JLINK_WriteReg(R4, 0x00000000)
TA260 002:373.049 - 0.003ms returns 0
TA260 002:373.053 JLINK_WriteReg(R5, 0x00000000)
TA260 002:373.056 - 0.003ms returns 0
TA260 002:373.060 JLINK_WriteReg(R6, 0x00000000)
TA260 002:373.064 - 0.003ms returns 0
TA260 002:373.068 JLINK_WriteReg(R7, 0x00000000)
TA260 002:373.071 - 0.003ms returns 0
TA260 002:373.075 JLINK_WriteReg(R8, 0x00000000)
TA260 002:373.079 - 0.003ms returns 0
TA260 002:373.083 JLINK_WriteReg(R9, 0x20000180)
TA260 002:373.086 - 0.003ms returns 0
TA260 002:373.090 JLINK_WriteReg(R10, 0x00000000)
TA260 002:373.094 - 0.003ms returns 0
TA260 002:373.106 JLINK_WriteReg(R11, 0x00000000)
TA260 002:373.109 - 0.003ms returns 0
TA260 002:373.113 JLINK_WriteReg(R12, 0x00000000)
TA260 002:373.116 - 0.003ms returns 0
TA260 002:373.121 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:373.124 - 0.004ms returns 0
TA260 002:373.129 JLINK_WriteReg(R14, 0x20000001)
TA260 002:373.133 - 0.003ms returns 0
TA260 002:373.137 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:373.141 - 0.004ms returns 0
TA260 002:373.145 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:373.148 - 0.003ms returns 0
TA260 002:373.152 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:373.156 - 0.003ms returns 0
TA260 002:373.160 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:373.163 - 0.003ms returns 0
TA260 002:373.167 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:373.171 - 0.003ms returns 0
TA260 002:373.177 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:373.182 - 0.004ms returns 0x00000013
TA260 002:373.186 JLINK_Go()
TA260 002:373.194   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:376.026 - 2.840ms 
TA260 002:376.038 JLINK_IsHalted()
TA260 002:376.547 - 0.508ms returns FALSE
TA260 002:376.564 JLINK_HasError()
TA260 002:377.655 JLINK_IsHalted()
TA260 002:378.163 - 0.507ms returns FALSE
TA260 002:378.169 JLINK_HasError()
TA260 002:379.656 JLINK_IsHalted()
TA260 002:380.138 - 0.480ms returns FALSE
TA260 002:380.158 JLINK_HasError()
TA260 002:381.657 JLINK_IsHalted()
TA260 002:384.022   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:384.520 - 2.862ms returns TRUE
TA260 002:385.105 JLINK_ReadReg(R15 (PC))
TA260 002:385.114 - 0.008ms returns 0x20000000
TA260 002:385.118 JLINK_ClrBPEx(BPHandle = 0x00000013)
TA260 002:385.122 - 0.003ms returns 0x00
TA260 002:385.126 JLINK_ReadReg(R0)
TA260 002:385.130 - 0.003ms returns 0x00000000
TA260 002:385.522 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:385.532   Data:  5F EA 02 7C 1E BF 03 F0 01 0E 04 F4 90 73 13 EB ...
TA260 002:385.543   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:388.139 - 2.616ms returns 0x27C
TA260 002:388.157 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:388.161   Data:  05 2F 03 68 22 F0 01 02 43 E8 05 25 00 2D F5 D1 ...
TA260 002:388.172   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:390.002 - 1.844ms returns 0x184
TA260 002:390.021 JLINK_HasError()
TA260 002:390.028 JLINK_WriteReg(R0, 0x08002000)
TA260 002:390.034 - 0.006ms returns 0
TA260 002:390.038 JLINK_WriteReg(R1, 0x00000400)
TA260 002:390.043 - 0.004ms returns 0
TA260 002:390.047 JLINK_WriteReg(R2, 0x20000184)
TA260 002:390.050 - 0.003ms returns 0
TA260 002:390.054 JLINK_WriteReg(R3, 0x00000000)
TA260 002:390.058 - 0.003ms returns 0
TA260 002:390.062 JLINK_WriteReg(R4, 0x00000000)
TA260 002:390.065 - 0.003ms returns 0
TA260 002:390.069 JLINK_WriteReg(R5, 0x00000000)
TA260 002:390.073 - 0.003ms returns 0
TA260 002:390.077 JLINK_WriteReg(R6, 0x00000000)
TA260 002:390.080 - 0.003ms returns 0
TA260 002:390.084 JLINK_WriteReg(R7, 0x00000000)
TA260 002:390.087 - 0.003ms returns 0
TA260 002:390.092 JLINK_WriteReg(R8, 0x00000000)
TA260 002:390.095 - 0.003ms returns 0
TA260 002:390.099 JLINK_WriteReg(R9, 0x20000180)
TA260 002:390.102 - 0.003ms returns 0
TA260 002:390.106 JLINK_WriteReg(R10, 0x00000000)
TA260 002:390.110 - 0.003ms returns 0
TA260 002:390.113 JLINK_WriteReg(R11, 0x00000000)
TA260 002:390.117 - 0.003ms returns 0
TA260 002:390.121 JLINK_WriteReg(R12, 0x00000000)
TA260 002:390.124 - 0.003ms returns 0
TA260 002:390.128 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:390.132 - 0.004ms returns 0
TA260 002:390.136 JLINK_WriteReg(R14, 0x20000001)
TA260 002:390.140 - 0.003ms returns 0
TA260 002:390.144 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:390.148 - 0.003ms returns 0
TA260 002:390.152 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:390.156 - 0.003ms returns 0
TA260 002:390.160 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:390.163 - 0.003ms returns 0
TA260 002:390.167 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:390.170 - 0.003ms returns 0
TA260 002:390.175 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:390.184 - 0.009ms returns 0
TA260 002:390.193 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:390.197 - 0.004ms returns 0x00000014
TA260 002:390.201 JLINK_Go()
TA260 002:390.211   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:393.073 - 2.871ms 
TA260 002:393.097 JLINK_IsHalted()
TA260 002:393.686 - 0.589ms returns FALSE
TA260 002:393.694 JLINK_HasError()
TA260 002:396.165 JLINK_IsHalted()
TA260 002:396.665 - 0.500ms returns FALSE
TA260 002:396.672 JLINK_HasError()
TA260 002:398.673 JLINK_IsHalted()
TA260 002:400.950   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:401.462 - 2.788ms returns TRUE
TA260 002:401.470 JLINK_ReadReg(R15 (PC))
TA260 002:401.476 - 0.005ms returns 0x20000000
TA260 002:401.480 JLINK_ClrBPEx(BPHandle = 0x00000014)
TA260 002:401.484 - 0.003ms returns 0x00
TA260 002:401.488 JLINK_ReadReg(R0)
TA260 002:401.492 - 0.003ms returns 0x00000000
TA260 002:401.878 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:401.886   Data:  01 68 41 F0 01 01 01 60 00 68 0C 21 00 F0 01 00 ...
TA260 002:401.898   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:404.445 - 2.566ms returns 0x27C
TA260 002:404.456 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:404.460   Data:  01 0B 21 68 48 60 E0 8C 01 38 E0 84 E0 8C A0 B3 ...
TA260 002:404.469   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:406.349 - 1.893ms returns 0x184
TA260 002:406.361 JLINK_HasError()
TA260 002:406.366 JLINK_WriteReg(R0, 0x08002400)
TA260 002:406.371 - 0.005ms returns 0
TA260 002:406.375 JLINK_WriteReg(R1, 0x00000400)
TA260 002:406.379 - 0.003ms returns 0
TA260 002:406.383 JLINK_WriteReg(R2, 0x20000184)
TA260 002:406.387 - 0.004ms returns 0
TA260 002:406.391 JLINK_WriteReg(R3, 0x00000000)
TA260 002:406.395 - 0.003ms returns 0
TA260 002:406.399 JLINK_WriteReg(R4, 0x00000000)
TA260 002:406.402 - 0.003ms returns 0
TA260 002:406.406 JLINK_WriteReg(R5, 0x00000000)
TA260 002:406.410 - 0.003ms returns 0
TA260 002:406.414 JLINK_WriteReg(R6, 0x00000000)
TA260 002:406.417 - 0.003ms returns 0
TA260 002:406.421 JLINK_WriteReg(R7, 0x00000000)
TA260 002:406.425 - 0.003ms returns 0
TA260 002:406.429 JLINK_WriteReg(R8, 0x00000000)
TA260 002:406.432 - 0.003ms returns 0
TA260 002:406.436 JLINK_WriteReg(R9, 0x20000180)
TA260 002:406.440 - 0.003ms returns 0
TA260 002:406.444 JLINK_WriteReg(R10, 0x00000000)
TA260 002:406.447 - 0.003ms returns 0
TA260 002:406.451 JLINK_WriteReg(R11, 0x00000000)
TA260 002:406.454 - 0.003ms returns 0
TA260 002:406.458 JLINK_WriteReg(R12, 0x00000000)
TA260 002:406.462 - 0.003ms returns 0
TA260 002:406.466 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:406.470 - 0.004ms returns 0
TA260 002:406.474 JLINK_WriteReg(R14, 0x20000001)
TA260 002:406.477 - 0.003ms returns 0
TA260 002:406.482 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:406.485 - 0.004ms returns 0
TA260 002:406.489 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:406.493 - 0.003ms returns 0
TA260 002:406.497 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:406.500 - 0.003ms returns 0
TA260 002:406.504 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:406.508 - 0.003ms returns 0
TA260 002:406.512 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:406.515 - 0.003ms returns 0
TA260 002:406.520 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:406.524 - 0.004ms returns 0x00000015
TA260 002:406.528 JLINK_Go()
TA260 002:406.537   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:409.382 - 2.852ms 
TA260 002:409.399 JLINK_IsHalted()
TA260 002:409.914 - 0.515ms returns FALSE
TA260 002:409.927 JLINK_HasError()
TA260 002:411.178 JLINK_IsHalted()
TA260 002:411.628 - 0.450ms returns FALSE
TA260 002:411.638 JLINK_HasError()
TA260 002:413.178 JLINK_IsHalted()
TA260 002:413.619 - 0.439ms returns FALSE
TA260 002:413.629 JLINK_HasError()
TA260 002:415.180 JLINK_IsHalted()
TA260 002:417.526   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:418.043 - 2.863ms returns TRUE
TA260 002:418.052 JLINK_ReadReg(R15 (PC))
TA260 002:418.058 - 0.005ms returns 0x20000000
TA260 002:418.062 JLINK_ClrBPEx(BPHandle = 0x00000015)
TA260 002:418.071 - 0.009ms returns 0x00
TA260 002:418.078 JLINK_ReadReg(R0)
TA260 002:418.082 - 0.003ms returns 0x00000000
TA260 002:418.465 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:418.474   Data:  84 B0 70 B5 92 B0 0D F1 58 0C 01 AE 8C E8 07 00 ...
TA260 002:418.485   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:421.052 - 2.586ms returns 0x27C
TA260 002:421.071 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:421.075   Data:  80 60 29 46 04 94 FE F7 63 FD 4F F4 14 70 CD E9 ...
TA260 002:421.086   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:423.005 - 1.934ms returns 0x184
TA260 002:423.019 JLINK_HasError()
TA260 002:423.025 JLINK_WriteReg(R0, 0x08002800)
TA260 002:423.031 - 0.006ms returns 0
TA260 002:423.036 JLINK_WriteReg(R1, 0x00000400)
TA260 002:423.039 - 0.004ms returns 0
TA260 002:423.044 JLINK_WriteReg(R2, 0x20000184)
TA260 002:423.047 - 0.003ms returns 0
TA260 002:423.052 JLINK_WriteReg(R3, 0x00000000)
TA260 002:423.056 - 0.003ms returns 0
TA260 002:423.060 JLINK_WriteReg(R4, 0x00000000)
TA260 002:423.063 - 0.003ms returns 0
TA260 002:423.067 JLINK_WriteReg(R5, 0x00000000)
TA260 002:423.070 - 0.003ms returns 0
TA260 002:423.075 JLINK_WriteReg(R6, 0x00000000)
TA260 002:423.078 - 0.003ms returns 0
TA260 002:423.082 JLINK_WriteReg(R7, 0x00000000)
TA260 002:423.085 - 0.003ms returns 0
TA260 002:423.090 JLINK_WriteReg(R8, 0x00000000)
TA260 002:423.093 - 0.003ms returns 0
TA260 002:423.097 JLINK_WriteReg(R9, 0x20000180)
TA260 002:423.100 - 0.003ms returns 0
TA260 002:423.104 JLINK_WriteReg(R10, 0x00000000)
TA260 002:423.108 - 0.003ms returns 0
TA260 002:423.112 JLINK_WriteReg(R11, 0x00000000)
TA260 002:423.115 - 0.003ms returns 0
TA260 002:423.119 JLINK_WriteReg(R12, 0x00000000)
TA260 002:423.123 - 0.003ms returns 0
TA260 002:423.127 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:423.131 - 0.004ms returns 0
TA260 002:423.135 JLINK_WriteReg(R14, 0x20000001)
TA260 002:423.138 - 0.003ms returns 0
TA260 002:423.143 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:423.147 - 0.003ms returns 0
TA260 002:423.151 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:423.155 - 0.003ms returns 0
TA260 002:423.159 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:423.162 - 0.003ms returns 0
TA260 002:423.166 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:423.170 - 0.003ms returns 0
TA260 002:423.174 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:423.177 - 0.003ms returns 0
TA260 002:423.182 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:423.187 - 0.004ms returns 0x00000016
TA260 002:423.191 JLINK_Go()
TA260 002:423.201   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:425.998 - 2.807ms 
TA260 002:426.012 JLINK_IsHalted()
TA260 002:426.513 - 0.500ms returns FALSE
TA260 002:426.519 JLINK_HasError()
TA260 002:428.190 JLINK_IsHalted()
TA260 002:428.766 - 0.576ms returns FALSE
TA260 002:428.775 JLINK_HasError()
TA260 002:430.195 JLINK_IsHalted()
TA260 002:432.521   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:433.040 - 2.845ms returns TRUE
TA260 002:433.048 JLINK_ReadReg(R15 (PC))
TA260 002:433.053 - 0.005ms returns 0x20000000
TA260 002:433.058 JLINK_ClrBPEx(BPHandle = 0x00000016)
TA260 002:433.061 - 0.003ms returns 0x00
TA260 002:433.066 JLINK_ReadReg(R0)
TA260 002:433.069 - 0.003ms returns 0x00000000
TA260 002:433.462 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:433.472   Data:  29 44 F3 D1 5C 1C BC F1 00 0F 0C D0 03 5D BC F1 ...
TA260 002:433.483   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:436.098 - 2.635ms returns 0x27C
TA260 002:436.124 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:436.128   Data:  01 B0 FF F7 A9 B8 01 68 82 6A 49 68 6F F3 5F 21 ...
TA260 002:436.141   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:437.986 - 1.861ms returns 0x184
TA260 002:438.003 JLINK_HasError()
TA260 002:438.035 JLINK_WriteReg(R0, 0x08002C00)
TA260 002:438.041 - 0.006ms returns 0
TA260 002:438.046 JLINK_WriteReg(R1, 0x00000400)
TA260 002:438.049 - 0.003ms returns 0
TA260 002:438.053 JLINK_WriteReg(R2, 0x20000184)
TA260 002:438.060 - 0.007ms returns 0
TA260 002:438.067 JLINK_WriteReg(R3, 0x00000000)
TA260 002:438.070 - 0.003ms returns 0
TA260 002:438.075 JLINK_WriteReg(R4, 0x00000000)
TA260 002:438.078 - 0.003ms returns 0
TA260 002:438.082 JLINK_WriteReg(R5, 0x00000000)
TA260 002:438.086 - 0.003ms returns 0
TA260 002:438.090 JLINK_WriteReg(R6, 0x00000000)
TA260 002:438.093 - 0.003ms returns 0
TA260 002:438.097 JLINK_WriteReg(R7, 0x00000000)
TA260 002:438.100 - 0.003ms returns 0
TA260 002:438.105 JLINK_WriteReg(R8, 0x00000000)
TA260 002:438.108 - 0.003ms returns 0
TA260 002:438.112 JLINK_WriteReg(R9, 0x20000180)
TA260 002:438.115 - 0.003ms returns 0
TA260 002:438.120 JLINK_WriteReg(R10, 0x00000000)
TA260 002:438.124 - 0.003ms returns 0
TA260 002:438.128 JLINK_WriteReg(R11, 0x00000000)
TA260 002:438.131 - 0.003ms returns 0
TA260 002:438.135 JLINK_WriteReg(R12, 0x00000000)
TA260 002:438.138 - 0.003ms returns 0
TA260 002:438.142 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:438.147 - 0.004ms returns 0
TA260 002:438.151 JLINK_WriteReg(R14, 0x20000001)
TA260 002:438.154 - 0.003ms returns 0
TA260 002:438.160 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:438.163 - 0.004ms returns 0
TA260 002:438.168 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:438.171 - 0.003ms returns 0
TA260 002:438.175 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:438.179 - 0.003ms returns 0
TA260 002:438.183 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:438.186 - 0.003ms returns 0
TA260 002:438.190 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:438.194 - 0.003ms returns 0
TA260 002:438.199 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:438.204 - 0.005ms returns 0x00000017
TA260 002:438.209 JLINK_Go()
TA260 002:438.219   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:440.980 - 2.770ms 
TA260 002:441.010 JLINK_IsHalted()
TA260 002:441.515 - 0.505ms returns FALSE
TA260 002:441.524 JLINK_HasError()
TA260 002:443.129 JLINK_IsHalted()
TA260 002:443.628 - 0.498ms returns FALSE
TA260 002:443.635 JLINK_HasError()
TA260 002:445.125 JLINK_IsHalted()
TA260 002:447.532   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:448.040 - 2.915ms returns TRUE
TA260 002:448.048 JLINK_ReadReg(R15 (PC))
TA260 002:448.055 - 0.006ms returns 0x20000000
TA260 002:448.059 JLINK_ClrBPEx(BPHandle = 0x00000017)
TA260 002:448.063 - 0.004ms returns 0x00
TA260 002:448.068 JLINK_ReadReg(R0)
TA260 002:448.071 - 0.003ms returns 0x00000000
TA260 002:448.471 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:448.482   Data:  08 0A 02 F5 FF 72 4F F4 80 71 00 23 81 81 C1 81 ...
TA260 002:448.493   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:451.056 - 2.583ms returns 0x27C
TA260 002:451.077 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:451.081   Data:  40 F2 08 04 C2 F2 00 04 94 ED 01 0A B7 EE 00 1A ...
TA260 002:451.094   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:452.998 - 1.921ms returns 0x184
TA260 002:453.012 JLINK_HasError()
TA260 002:453.017 JLINK_WriteReg(R0, 0x08003000)
TA260 002:453.023 - 0.006ms returns 0
TA260 002:453.028 JLINK_WriteReg(R1, 0x00000400)
TA260 002:453.031 - 0.003ms returns 0
TA260 002:453.036 JLINK_WriteReg(R2, 0x20000184)
TA260 002:453.039 - 0.003ms returns 0
TA260 002:453.043 JLINK_WriteReg(R3, 0x00000000)
TA260 002:453.046 - 0.003ms returns 0
TA260 002:453.050 JLINK_WriteReg(R4, 0x00000000)
TA260 002:453.054 - 0.003ms returns 0
TA260 002:453.058 JLINK_WriteReg(R5, 0x00000000)
TA260 002:453.062 - 0.003ms returns 0
TA260 002:453.066 JLINK_WriteReg(R6, 0x00000000)
TA260 002:453.070 - 0.003ms returns 0
TA260 002:453.074 JLINK_WriteReg(R7, 0x00000000)
TA260 002:453.077 - 0.003ms returns 0
TA260 002:453.081 JLINK_WriteReg(R8, 0x00000000)
TA260 002:453.084 - 0.003ms returns 0
TA260 002:453.089 JLINK_WriteReg(R9, 0x20000180)
TA260 002:453.092 - 0.003ms returns 0
TA260 002:453.096 JLINK_WriteReg(R10, 0x00000000)
TA260 002:453.100 - 0.003ms returns 0
TA260 002:453.104 JLINK_WriteReg(R11, 0x00000000)
TA260 002:453.107 - 0.003ms returns 0
TA260 002:453.111 JLINK_WriteReg(R12, 0x00000000)
TA260 002:453.120 - 0.008ms returns 0
TA260 002:453.127 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:453.131 - 0.004ms returns 0
TA260 002:453.135 JLINK_WriteReg(R14, 0x20000001)
TA260 002:453.139 - 0.003ms returns 0
TA260 002:453.143 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:453.157 - 0.014ms returns 0
TA260 002:453.162 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:453.165 - 0.003ms returns 0
TA260 002:453.170 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:453.173 - 0.003ms returns 0
TA260 002:453.177 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:453.192 - 0.014ms returns 0
TA260 002:453.197 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:453.200 - 0.003ms returns 0
TA260 002:453.206 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:453.211 - 0.005ms returns 0x00000018
TA260 002:453.215 JLINK_Go()
TA260 002:453.224   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:456.029 - 2.813ms 
TA260 002:456.038 JLINK_IsHalted()
TA260 002:456.542 - 0.504ms returns FALSE
TA260 002:456.551 JLINK_HasError()
TA260 002:458.139 JLINK_IsHalted()
TA260 002:458.621 - 0.481ms returns FALSE
TA260 002:458.629 JLINK_HasError()
TA260 002:460.140 JLINK_IsHalted()
TA260 002:462.487   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:462.906 - 2.766ms returns TRUE
TA260 002:462.915 JLINK_ReadReg(R15 (PC))
TA260 002:462.920 - 0.005ms returns 0x20000000
TA260 002:462.925 JLINK_ClrBPEx(BPHandle = 0x00000018)
TA260 002:462.929 - 0.003ms returns 0x00
TA260 002:462.934 JLINK_ReadReg(R0)
TA260 002:462.938 - 0.003ms returns 0x00000000
TA260 002:463.334 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:463.344   Data:  53 EC 10 2B FD F7 5C F8 FD F7 32 F9 00 EE 10 0A ...
TA260 002:463.356   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:466.005 - 2.670ms returns 0x27C
TA260 002:466.021 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:466.025   Data:  04 2B 50 F8 04 2B 41 F8 04 2B 50 F8 04 2B 41 F8 ...
TA260 002:466.035   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:467.998 - 1.976ms returns 0x184
TA260 002:468.011 JLINK_HasError()
TA260 002:468.017 JLINK_WriteReg(R0, 0x08003400)
TA260 002:468.023 - 0.005ms returns 0
TA260 002:468.027 JLINK_WriteReg(R1, 0x00000400)
TA260 002:468.030 - 0.003ms returns 0
TA260 002:468.035 JLINK_WriteReg(R2, 0x20000184)
TA260 002:468.038 - 0.003ms returns 0
TA260 002:468.042 JLINK_WriteReg(R3, 0x00000000)
TA260 002:468.046 - 0.003ms returns 0
TA260 002:468.050 JLINK_WriteReg(R4, 0x00000000)
TA260 002:468.053 - 0.003ms returns 0
TA260 002:468.057 JLINK_WriteReg(R5, 0x00000000)
TA260 002:468.060 - 0.003ms returns 0
TA260 002:468.065 JLINK_WriteReg(R6, 0x00000000)
TA260 002:468.068 - 0.003ms returns 0
TA260 002:468.073 JLINK_WriteReg(R7, 0x00000000)
TA260 002:468.076 - 0.003ms returns 0
TA260 002:468.081 JLINK_WriteReg(R8, 0x00000000)
TA260 002:468.084 - 0.003ms returns 0
TA260 002:468.088 JLINK_WriteReg(R9, 0x20000180)
TA260 002:468.092 - 0.003ms returns 0
TA260 002:468.096 JLINK_WriteReg(R10, 0x00000000)
TA260 002:468.099 - 0.003ms returns 0
TA260 002:468.103 JLINK_WriteReg(R11, 0x00000000)
TA260 002:468.106 - 0.003ms returns 0
TA260 002:468.110 JLINK_WriteReg(R12, 0x00000000)
TA260 002:468.114 - 0.003ms returns 0
TA260 002:468.118 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:468.122 - 0.004ms returns 0
TA260 002:468.126 JLINK_WriteReg(R14, 0x20000001)
TA260 002:468.130 - 0.003ms returns 0
TA260 002:468.134 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:468.137 - 0.003ms returns 0
TA260 002:468.141 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:468.145 - 0.003ms returns 0
TA260 002:468.149 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:468.152 - 0.003ms returns 0
TA260 002:468.156 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:468.160 - 0.003ms returns 0
TA260 002:468.164 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:468.167 - 0.003ms returns 0
TA260 002:468.172 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:468.176 - 0.004ms returns 0x00000019
TA260 002:468.180 JLINK_Go()
TA260 002:468.190   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:470.918 - 2.737ms 
TA260 002:470.941 JLINK_IsHalted()
TA260 002:471.460 - 0.518ms returns FALSE
TA260 002:471.467 JLINK_HasError()
TA260 002:473.156 JLINK_IsHalted()
TA260 002:473.667 - 0.510ms returns FALSE
TA260 002:473.684 JLINK_HasError()
TA260 002:475.149 JLINK_IsHalted()
TA260 002:477.533   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:478.066 - 2.915ms returns TRUE
TA260 002:478.077 JLINK_ReadReg(R15 (PC))
TA260 002:478.082 - 0.005ms returns 0x20000000
TA260 002:478.087 JLINK_ClrBPEx(BPHandle = 0x00000019)
TA260 002:478.091 - 0.004ms returns 0x00
TA260 002:478.096 JLINK_ReadReg(R0)
TA260 002:478.099 - 0.003ms returns 0x00000000
TA260 002:478.510 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:478.522   Data:  50 F8 04 2B 41 F8 04 2B 50 F8 04 2B 41 F8 04 2B ...
TA260 002:478.535   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:481.140 - 2.630ms returns 0x27C
TA260 002:481.165 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:481.169   Data:  28 46 50 F8 04 2B 69 46 41 F8 04 2B 50 F8 04 2B ...
TA260 002:481.182   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:483.034 - 1.868ms returns 0x184
TA260 002:483.054 JLINK_HasError()
TA260 002:483.060 JLINK_WriteReg(R0, 0x08003800)
TA260 002:483.066 - 0.006ms returns 0
TA260 002:483.071 JLINK_WriteReg(R1, 0x00000400)
TA260 002:483.074 - 0.003ms returns 0
TA260 002:483.078 JLINK_WriteReg(R2, 0x20000184)
TA260 002:483.081 - 0.003ms returns 0
TA260 002:483.085 JLINK_WriteReg(R3, 0x00000000)
TA260 002:483.089 - 0.003ms returns 0
TA260 002:483.093 JLINK_WriteReg(R4, 0x00000000)
TA260 002:483.096 - 0.003ms returns 0
TA260 002:483.100 JLINK_WriteReg(R5, 0x00000000)
TA260 002:483.104 - 0.003ms returns 0
TA260 002:483.108 JLINK_WriteReg(R6, 0x00000000)
TA260 002:483.111 - 0.003ms returns 0
TA260 002:483.115 JLINK_WriteReg(R7, 0x00000000)
TA260 002:483.119 - 0.003ms returns 0
TA260 002:483.123 JLINK_WriteReg(R8, 0x00000000)
TA260 002:483.126 - 0.003ms returns 0
TA260 002:483.131 JLINK_WriteReg(R9, 0x20000180)
TA260 002:483.134 - 0.003ms returns 0
TA260 002:483.138 JLINK_WriteReg(R10, 0x00000000)
TA260 002:483.141 - 0.003ms returns 0
TA260 002:483.146 JLINK_WriteReg(R11, 0x00000000)
TA260 002:483.149 - 0.003ms returns 0
TA260 002:483.153 JLINK_WriteReg(R12, 0x00000000)
TA260 002:483.156 - 0.003ms returns 0
TA260 002:483.161 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:483.164 - 0.004ms returns 0
TA260 002:483.168 JLINK_WriteReg(R14, 0x20000001)
TA260 002:483.172 - 0.003ms returns 0
TA260 002:483.176 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:483.180 - 0.003ms returns 0
TA260 002:483.184 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:483.187 - 0.003ms returns 0
TA260 002:483.191 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:483.194 - 0.003ms returns 0
TA260 002:483.198 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:483.202 - 0.003ms returns 0
TA260 002:483.206 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:483.209 - 0.003ms returns 0
TA260 002:483.214 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:483.218 - 0.004ms returns 0x0000001A
TA260 002:483.223 JLINK_Go()
TA260 002:483.232   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:486.048 - 2.824ms 
TA260 002:486.061 JLINK_IsHalted()
TA260 002:486.625 - 0.563ms returns FALSE
TA260 002:486.634 JLINK_HasError()
TA260 002:488.170 JLINK_IsHalted()
TA260 002:488.630 - 0.460ms returns FALSE
TA260 002:488.678 JLINK_HasError()
TA260 002:490.165 JLINK_IsHalted()
TA260 002:492.450   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:492.939 - 2.773ms returns TRUE
TA260 002:492.950 JLINK_ReadReg(R15 (PC))
TA260 002:492.955 - 0.005ms returns 0x20000000
TA260 002:492.960 JLINK_ClrBPEx(BPHandle = 0x0000001A)
TA260 002:492.963 - 0.003ms returns 0x00
TA260 002:492.968 JLINK_ReadReg(R0)
TA260 002:492.971 - 0.003ms returns 0x00000000
TA260 002:493.362 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:493.372   Data:  FF FD 00 BF 00 F0 4C F8 FC E7 00 BF 00 BF 00 BF ...
TA260 002:493.384   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:496.004 - 2.641ms returns 0x27C
TA260 002:496.022 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:496.029   Data:  51 EC 10 0B 81 F0 00 41 41 EC 10 0B 14 B0 BD EC ...
TA260 002:496.037   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:497.997 - 1.974ms returns 0x184
TA260 002:498.008 JLINK_HasError()
TA260 002:498.013 JLINK_WriteReg(R0, 0x08003C00)
TA260 002:498.019 - 0.005ms returns 0
TA260 002:498.023 JLINK_WriteReg(R1, 0x00000400)
TA260 002:498.027 - 0.003ms returns 0
TA260 002:498.031 JLINK_WriteReg(R2, 0x20000184)
TA260 002:498.034 - 0.003ms returns 0
TA260 002:498.038 JLINK_WriteReg(R3, 0x00000000)
TA260 002:498.042 - 0.003ms returns 0
TA260 002:498.046 JLINK_WriteReg(R4, 0x00000000)
TA260 002:498.049 - 0.003ms returns 0
TA260 002:498.053 JLINK_WriteReg(R5, 0x00000000)
TA260 002:498.056 - 0.003ms returns 0
TA260 002:498.060 JLINK_WriteReg(R6, 0x00000000)
TA260 002:498.064 - 0.003ms returns 0
TA260 002:498.068 JLINK_WriteReg(R7, 0x00000000)
TA260 002:498.072 - 0.003ms returns 0
TA260 002:498.076 JLINK_WriteReg(R8, 0x00000000)
TA260 002:498.079 - 0.003ms returns 0
TA260 002:498.083 JLINK_WriteReg(R9, 0x20000180)
TA260 002:498.087 - 0.003ms returns 0
TA260 002:498.091 JLINK_WriteReg(R10, 0x00000000)
TA260 002:498.094 - 0.003ms returns 0
TA260 002:498.098 JLINK_WriteReg(R11, 0x00000000)
TA260 002:498.102 - 0.003ms returns 0
TA260 002:498.106 JLINK_WriteReg(R12, 0x00000000)
TA260 002:498.109 - 0.003ms returns 0
TA260 002:498.113 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:498.117 - 0.004ms returns 0
TA260 002:498.121 JLINK_WriteReg(R14, 0x20000001)
TA260 002:498.125 - 0.003ms returns 0
TA260 002:498.129 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:498.132 - 0.003ms returns 0
TA260 002:498.136 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:498.140 - 0.003ms returns 0
TA260 002:498.144 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:498.147 - 0.003ms returns 0
TA260 002:498.151 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:498.155 - 0.003ms returns 0
TA260 002:498.159 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:498.162 - 0.003ms returns 0
TA260 002:498.167 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:498.171 - 0.004ms returns 0x0000001B
TA260 002:498.175 JLINK_Go()
TA260 002:498.184   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:500.986 - 2.809ms 
TA260 002:501.006 JLINK_IsHalted()
TA260 002:501.522 - 0.515ms returns FALSE
TA260 002:501.538 JLINK_HasError()
TA260 002:502.667 JLINK_IsHalted()
TA260 002:503.190 - 0.522ms returns FALSE
TA260 002:503.201 JLINK_HasError()
TA260 002:504.673 JLINK_IsHalted()
TA260 002:505.110 - 0.436ms returns FALSE
TA260 002:505.127 JLINK_HasError()
TA260 002:507.178 JLINK_IsHalted()
TA260 002:509.544   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:510.056 - 2.876ms returns TRUE
TA260 002:510.078 JLINK_ReadReg(R15 (PC))
TA260 002:510.086 - 0.007ms returns 0x20000000
TA260 002:510.091 JLINK_ClrBPEx(BPHandle = 0x0000001B)
TA260 002:510.095 - 0.004ms returns 0x00
TA260 002:510.100 JLINK_ReadReg(R0)
TA260 002:510.103 - 0.003ms returns 0x00000000
TA260 002:510.510 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:510.518   Data:  4F F0 FF 30 FC F7 CB FA DC E7 00 2D 01 DD 00 2C ...
TA260 002:510.529   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:513.142 - 2.631ms returns 0x27C
TA260 002:513.222 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:513.228   Data:  1B 2B FC F7 AA F8 41 EC 10 0B 00 F0 A9 B9 00 20 ...
TA260 002:513.240   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:515.135 - 1.977ms returns 0x184
TA260 002:515.153 JLINK_HasError()
TA260 002:515.197 JLINK_WriteReg(R0, 0x08004000)
TA260 002:515.204 - 0.015ms returns 0
TA260 002:515.210 JLINK_WriteReg(R1, 0x00000400)
TA260 002:515.214 - 0.003ms returns 0
TA260 002:515.218 JLINK_WriteReg(R2, 0x20000184)
TA260 002:515.221 - 0.003ms returns 0
TA260 002:515.226 JLINK_WriteReg(R3, 0x00000000)
TA260 002:515.230 - 0.003ms returns 0
TA260 002:515.234 JLINK_WriteReg(R4, 0x00000000)
TA260 002:515.238 - 0.003ms returns 0
TA260 002:515.243 JLINK_WriteReg(R5, 0x00000000)
TA260 002:515.252 - 0.008ms returns 0
TA260 002:515.258 JLINK_WriteReg(R6, 0x00000000)
TA260 002:515.261 - 0.003ms returns 0
TA260 002:515.266 JLINK_WriteReg(R7, 0x00000000)
TA260 002:515.270 - 0.004ms returns 0
TA260 002:515.275 JLINK_WriteReg(R8, 0x00000000)
TA260 002:515.278 - 0.003ms returns 0
TA260 002:515.282 JLINK_WriteReg(R9, 0x20000180)
TA260 002:515.286 - 0.003ms returns 0
TA260 002:515.290 JLINK_WriteReg(R10, 0x00000000)
TA260 002:515.294 - 0.003ms returns 0
TA260 002:515.299 JLINK_WriteReg(R11, 0x00000000)
TA260 002:515.303 - 0.003ms returns 0
TA260 002:515.307 JLINK_WriteReg(R12, 0x00000000)
TA260 002:515.311 - 0.004ms returns 0
TA260 002:515.315 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:515.320 - 0.004ms returns 0
TA260 002:515.324 JLINK_WriteReg(R14, 0x20000001)
TA260 002:515.327 - 0.003ms returns 0
TA260 002:515.332 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:515.335 - 0.004ms returns 0
TA260 002:515.340 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:515.344 - 0.003ms returns 0
TA260 002:515.348 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:515.352 - 0.003ms returns 0
TA260 002:515.356 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:515.359 - 0.003ms returns 0
TA260 002:515.363 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:515.367 - 0.003ms returns 0
TA260 002:515.372 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:515.377 - 0.005ms returns 0x0000001C
TA260 002:515.381 JLINK_Go()
TA260 002:515.391   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:518.163 - 2.781ms 
TA260 002:518.179 JLINK_IsHalted()
TA260 002:518.651 - 0.472ms returns FALSE
TA260 002:518.657 JLINK_HasError()
TA260 002:520.688 JLINK_IsHalted()
TA260 002:521.224 - 0.534ms returns FALSE
TA260 002:521.232 JLINK_HasError()
TA260 002:522.690 JLINK_IsHalted()
TA260 002:525.017   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:525.530 - 2.839ms returns TRUE
TA260 002:525.540 JLINK_ReadReg(R15 (PC))
TA260 002:525.546 - 0.005ms returns 0x20000000
TA260 002:525.551 JLINK_ClrBPEx(BPHandle = 0x0000001C)
TA260 002:525.555 - 0.003ms returns 0x00
TA260 002:525.560 JLINK_ReadReg(R0)
TA260 002:525.563 - 0.003ms returns 0x00000000
TA260 002:525.981 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:525.993   Data:  FB F7 EC FF 53 EC 1D 2B FB F7 E8 FF 9D ED 0E 0B ...
TA260 002:526.004   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:528.634 - 2.652ms returns 0x27C
TA260 002:528.648 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:528.652   Data:  53 EC 19 2B FB F7 05 FE 9D ED 04 0B 41 EC 19 0B ...
TA260 002:528.660   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:530.534 - 1.885ms returns 0x184
TA260 002:530.548 JLINK_HasError()
TA260 002:530.554 JLINK_WriteReg(R0, 0x08004400)
TA260 002:530.561 - 0.007ms returns 0
TA260 002:530.566 JLINK_WriteReg(R1, 0x00000400)
TA260 002:530.569 - 0.003ms returns 0
TA260 002:530.573 JLINK_WriteReg(R2, 0x20000184)
TA260 002:530.576 - 0.003ms returns 0
TA260 002:530.580 JLINK_WriteReg(R3, 0x00000000)
TA260 002:530.584 - 0.003ms returns 0
TA260 002:530.588 JLINK_WriteReg(R4, 0x00000000)
TA260 002:530.591 - 0.003ms returns 0
TA260 002:530.596 JLINK_WriteReg(R5, 0x00000000)
TA260 002:530.600 - 0.003ms returns 0
TA260 002:530.604 JLINK_WriteReg(R6, 0x00000000)
TA260 002:530.607 - 0.003ms returns 0
TA260 002:530.611 JLINK_WriteReg(R7, 0x00000000)
TA260 002:530.614 - 0.003ms returns 0
TA260 002:530.619 JLINK_WriteReg(R8, 0x00000000)
TA260 002:530.622 - 0.003ms returns 0
TA260 002:530.626 JLINK_WriteReg(R9, 0x20000180)
TA260 002:530.630 - 0.003ms returns 0
TA260 002:530.634 JLINK_WriteReg(R10, 0x00000000)
TA260 002:530.637 - 0.003ms returns 0
TA260 002:530.641 JLINK_WriteReg(R11, 0x00000000)
TA260 002:530.645 - 0.003ms returns 0
TA260 002:530.649 JLINK_WriteReg(R12, 0x00000000)
TA260 002:530.652 - 0.003ms returns 0
TA260 002:530.656 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:530.660 - 0.004ms returns 0
TA260 002:530.664 JLINK_WriteReg(R14, 0x20000001)
TA260 002:530.668 - 0.003ms returns 0
TA260 002:530.672 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:530.680 - 0.008ms returns 0
TA260 002:530.686 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:530.689 - 0.003ms returns 0
TA260 002:530.694 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:530.697 - 0.003ms returns 0
TA260 002:530.701 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:530.704 - 0.003ms returns 0
TA260 002:530.708 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:530.712 - 0.003ms returns 0
TA260 002:530.716 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:530.721 - 0.005ms returns 0x0000001D
TA260 002:530.725 JLINK_Go()
TA260 002:530.734   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:533.539 - 2.813ms 
TA260 002:533.555 JLINK_IsHalted()
TA260 002:534.012 - 0.457ms returns FALSE
TA260 002:534.019 JLINK_HasError()
TA260 002:535.193 JLINK_IsHalted()
TA260 002:535.770 - 0.577ms returns FALSE
TA260 002:535.784 JLINK_HasError()
TA260 002:537.702 JLINK_IsHalted()
TA260 002:540.134   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:540.627 - 2.924ms returns TRUE
TA260 002:540.635 JLINK_ReadReg(R15 (PC))
TA260 002:540.641 - 0.005ms returns 0x20000000
TA260 002:540.645 JLINK_ClrBPEx(BPHandle = 0x0000001D)
TA260 002:540.650 - 0.004ms returns 0x00
TA260 002:540.654 JLINK_ReadReg(R0)
TA260 002:540.658 - 0.004ms returns 0x00000000
TA260 002:541.037 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:541.047   Data:  41 EC 1C 0B 53 EC 10 2B 9D ED 0A 0B 51 EC 10 0B ...
TA260 002:541.057   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:543.640 - 2.602ms returns 0x27C
TA260 002:543.667 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:543.671   Data:  04 EB C5 00 90 ED 00 1B 51 EC 10 0B 53 EC 11 2B ...
TA260 002:543.684   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:545.539 - 1.873ms returns 0x184
TA260 002:545.547 JLINK_HasError()
TA260 002:545.553 JLINK_WriteReg(R0, 0x08004800)
TA260 002:545.558 - 0.005ms returns 0
TA260 002:545.562 JLINK_WriteReg(R1, 0x00000400)
TA260 002:545.565 - 0.003ms returns 0
TA260 002:545.569 JLINK_WriteReg(R2, 0x20000184)
TA260 002:545.572 - 0.003ms returns 0
TA260 002:545.577 JLINK_WriteReg(R3, 0x00000000)
TA260 002:545.580 - 0.003ms returns 0
TA260 002:545.584 JLINK_WriteReg(R4, 0x00000000)
TA260 002:545.588 - 0.003ms returns 0
TA260 002:545.592 JLINK_WriteReg(R5, 0x00000000)
TA260 002:545.596 - 0.003ms returns 0
TA260 002:545.600 JLINK_WriteReg(R6, 0x00000000)
TA260 002:545.603 - 0.004ms returns 0
TA260 002:545.608 JLINK_WriteReg(R7, 0x00000000)
TA260 002:545.611 - 0.003ms returns 0
TA260 002:545.615 JLINK_WriteReg(R8, 0x00000000)
TA260 002:545.619 - 0.003ms returns 0
TA260 002:545.623 JLINK_WriteReg(R9, 0x20000180)
TA260 002:545.626 - 0.003ms returns 0
TA260 002:545.630 JLINK_WriteReg(R10, 0x00000000)
TA260 002:545.634 - 0.003ms returns 0
TA260 002:545.638 JLINK_WriteReg(R11, 0x00000000)
TA260 002:545.642 - 0.003ms returns 0
TA260 002:545.646 JLINK_WriteReg(R12, 0x00000000)
TA260 002:545.649 - 0.003ms returns 0
TA260 002:545.653 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:545.657 - 0.004ms returns 0
TA260 002:545.661 JLINK_WriteReg(R14, 0x20000001)
TA260 002:545.665 - 0.003ms returns 0
TA260 002:545.669 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:545.672 - 0.003ms returns 0
TA260 002:545.677 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:545.680 - 0.003ms returns 0
TA260 002:545.684 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:545.688 - 0.003ms returns 0
TA260 002:545.692 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:545.696 - 0.004ms returns 0
TA260 002:545.701 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:545.704 - 0.003ms returns 0
TA260 002:545.709 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:545.713 - 0.004ms returns 0x0000001E
TA260 002:545.717 JLINK_Go()
TA260 002:545.726   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:548.473 - 2.754ms 
TA260 002:548.488 JLINK_IsHalted()
TA260 002:549.002 - 0.514ms returns FALSE
TA260 002:549.010 JLINK_HasError()
TA260 002:550.212 JLINK_IsHalted()
TA260 002:550.674 - 0.460ms returns FALSE
TA260 002:550.680 JLINK_HasError()
TA260 002:552.213 JLINK_IsHalted()
TA260 002:552.693 - 0.480ms returns FALSE
TA260 002:552.700 JLINK_HasError()
TA260 002:554.214 JLINK_IsHalted()
TA260 002:556.637   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:557.112 - 2.897ms returns TRUE
TA260 002:557.124 JLINK_ReadReg(R15 (PC))
TA260 002:557.131 - 0.006ms returns 0x20000000
TA260 002:557.136 JLINK_ClrBPEx(BPHandle = 0x0000001E)
TA260 002:557.140 - 0.004ms returns 0x00
TA260 002:557.144 JLINK_ReadReg(R0)
TA260 002:557.148 - 0.003ms returns 0x00000000
TA260 002:557.520 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:557.530   Data:  00 20 01 E0 01 C1 12 1F 00 2A FB D1 70 47 00 00 ...
TA260 002:557.542   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:560.188 - 2.666ms returns 0x27C
TA260 002:560.204 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:560.209   Data:  04 F5 80 14 76 1C 76 1C 30 78 66 28 0B D0 13 DC ...
TA260 002:560.222   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:562.185 - 1.980ms returns 0x184
TA260 002:562.205 JLINK_HasError()
TA260 002:562.211 JLINK_WriteReg(R0, 0x08004C00)
TA260 002:562.218 - 0.007ms returns 0
TA260 002:562.222 JLINK_WriteReg(R1, 0x00000400)
TA260 002:562.226 - 0.003ms returns 0
TA260 002:562.230 JLINK_WriteReg(R2, 0x20000184)
TA260 002:562.233 - 0.003ms returns 0
TA260 002:562.237 JLINK_WriteReg(R3, 0x00000000)
TA260 002:562.241 - 0.003ms returns 0
TA260 002:562.245 JLINK_WriteReg(R4, 0x00000000)
TA260 002:562.248 - 0.003ms returns 0
TA260 002:562.253 JLINK_WriteReg(R5, 0x00000000)
TA260 002:562.257 - 0.003ms returns 0
TA260 002:562.261 JLINK_WriteReg(R6, 0x00000000)
TA260 002:562.264 - 0.003ms returns 0
TA260 002:562.268 JLINK_WriteReg(R7, 0x00000000)
TA260 002:562.271 - 0.003ms returns 0
TA260 002:562.276 JLINK_WriteReg(R8, 0x00000000)
TA260 002:562.279 - 0.003ms returns 0
TA260 002:562.283 JLINK_WriteReg(R9, 0x20000180)
TA260 002:562.286 - 0.003ms returns 0
TA260 002:562.290 JLINK_WriteReg(R10, 0x00000000)
TA260 002:562.294 - 0.003ms returns 0
TA260 002:562.298 JLINK_WriteReg(R11, 0x00000000)
TA260 002:562.301 - 0.003ms returns 0
TA260 002:562.305 JLINK_WriteReg(R12, 0x00000000)
TA260 002:562.309 - 0.003ms returns 0
TA260 002:562.313 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:562.317 - 0.004ms returns 0
TA260 002:562.321 JLINK_WriteReg(R14, 0x20000001)
TA260 002:562.325 - 0.003ms returns 0
TA260 002:562.329 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:562.332 - 0.003ms returns 0
TA260 002:562.336 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:562.340 - 0.003ms returns 0
TA260 002:562.344 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:562.347 - 0.003ms returns 0
TA260 002:562.351 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:562.355 - 0.003ms returns 0
TA260 002:562.358 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:562.362 - 0.003ms returns 0
TA260 002:562.366 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:562.371 - 0.004ms returns 0x0000001F
TA260 002:562.375 JLINK_Go()
TA260 002:562.384   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:565.259 - 2.883ms 
TA260 002:565.277 JLINK_IsHalted()
TA260 002:565.770 - 0.492ms returns FALSE
TA260 002:565.779 JLINK_HasError()
TA260 002:567.220 JLINK_IsHalted()
TA260 002:567.766 - 0.546ms returns FALSE
TA260 002:567.779 JLINK_HasError()
TA260 002:569.732 JLINK_IsHalted()
TA260 002:572.218   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:572.749 - 3.017ms returns TRUE
TA260 002:572.757 JLINK_ReadReg(R15 (PC))
TA260 002:572.763 - 0.005ms returns 0x20000000
TA260 002:572.768 JLINK_ClrBPEx(BPHandle = 0x0000001F)
TA260 002:572.771 - 0.003ms returns 0x00
TA260 002:572.776 JLINK_ReadReg(R0)
TA260 002:572.780 - 0.003ms returns 0x00000000
TA260 002:573.239 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:573.250   Data:  2B 22 8D F8 04 20 01 22 03 E0 E2 07 01 D0 20 22 ...
TA260 002:573.262   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:575.902 - 2.663ms returns 0x27C
TA260 002:575.920 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:575.924   Data:  CD E9 01 10 06 A9 0E A8 FF F7 CA FC DD E9 0F 02 ...
TA260 002:575.940   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:577.779 - 1.859ms returns 0x184
TA260 002:577.794 JLINK_HasError()
TA260 002:577.802 JLINK_WriteReg(R0, 0x08005000)
TA260 002:577.809 - 0.007ms returns 0
TA260 002:577.814 JLINK_WriteReg(R1, 0x00000400)
TA260 002:577.819 - 0.004ms returns 0
TA260 002:577.824 JLINK_WriteReg(R2, 0x20000184)
TA260 002:577.828 - 0.004ms returns 0
TA260 002:577.832 JLINK_WriteReg(R3, 0x00000000)
TA260 002:577.835 - 0.003ms returns 0
TA260 002:577.839 JLINK_WriteReg(R4, 0x00000000)
TA260 002:577.843 - 0.003ms returns 0
TA260 002:577.847 JLINK_WriteReg(R5, 0x00000000)
TA260 002:577.850 - 0.003ms returns 0
TA260 002:577.855 JLINK_WriteReg(R6, 0x00000000)
TA260 002:577.858 - 0.003ms returns 0
TA260 002:577.862 JLINK_WriteReg(R7, 0x00000000)
TA260 002:577.866 - 0.003ms returns 0
TA260 002:577.870 JLINK_WriteReg(R8, 0x00000000)
TA260 002:577.873 - 0.003ms returns 0
TA260 002:577.877 JLINK_WriteReg(R9, 0x20000180)
TA260 002:577.880 - 0.003ms returns 0
TA260 002:577.884 JLINK_WriteReg(R10, 0x00000000)
TA260 002:577.888 - 0.003ms returns 0
TA260 002:577.892 JLINK_WriteReg(R11, 0x00000000)
TA260 002:577.895 - 0.003ms returns 0
TA260 002:577.900 JLINK_WriteReg(R12, 0x00000000)
TA260 002:577.903 - 0.003ms returns 0
TA260 002:577.907 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:577.911 - 0.004ms returns 0
TA260 002:577.916 JLINK_WriteReg(R14, 0x20000001)
TA260 002:577.920 - 0.004ms returns 0
TA260 002:577.925 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:577.928 - 0.003ms returns 0
TA260 002:577.933 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:577.936 - 0.004ms returns 0
TA260 002:577.940 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:577.944 - 0.003ms returns 0
TA260 002:577.948 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:577.951 - 0.003ms returns 0
TA260 002:577.955 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:577.959 - 0.003ms returns 0
TA260 002:577.963 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:577.968 - 0.004ms returns 0x00000020
TA260 002:577.972 JLINK_Go()
TA260 002:577.981   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:580.697 - 2.724ms 
TA260 002:580.716 JLINK_IsHalted()
TA260 002:581.194 - 0.477ms returns FALSE
TA260 002:581.201 JLINK_HasError()
TA260 002:582.299 JLINK_IsHalted()
TA260 002:582.948 - 0.649ms returns FALSE
TA260 002:582.963 JLINK_HasError()
TA260 002:584.293 JLINK_IsHalted()
TA260 002:584.770 - 0.477ms returns FALSE
TA260 002:584.779 JLINK_HasError()
TA260 002:586.295 JLINK_IsHalted()
TA260 002:588.640   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:589.168 - 2.873ms returns TRUE
TA260 002:589.178 JLINK_ReadReg(R15 (PC))
TA260 002:589.184 - 0.006ms returns 0x20000000
TA260 002:589.188 JLINK_ClrBPEx(BPHandle = 0x00000020)
TA260 002:589.192 - 0.003ms returns 0x00
TA260 002:589.197 JLINK_ReadReg(R0)
TA260 002:589.201 - 0.003ms returns 0x00000000
TA260 002:589.601 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:589.611   Data:  05 98 00 F0 4D F8 05 44 1C E0 04 98 00 28 07 DB ...
TA260 002:589.623   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:592.274 - 2.672ms returns 0x27C
TA260 002:592.295 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:592.299   Data:  20 03 20 07 A0 00 A0 04 A0 02 A0 06 A0 01 A0 05 ...
TA260 002:592.314   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:594.270 - 1.975ms returns 0x184
TA260 002:594.285 JLINK_HasError()
TA260 002:594.324 JLINK_WriteReg(R0, 0x08005400)
TA260 002:594.331 - 0.006ms returns 0
TA260 002:594.336 JLINK_WriteReg(R1, 0x00000400)
TA260 002:594.339 - 0.003ms returns 0
TA260 002:594.343 JLINK_WriteReg(R2, 0x20000184)
TA260 002:594.347 - 0.003ms returns 0
TA260 002:594.351 JLINK_WriteReg(R3, 0x00000000)
TA260 002:594.355 - 0.003ms returns 0
TA260 002:594.359 JLINK_WriteReg(R4, 0x00000000)
TA260 002:594.362 - 0.003ms returns 0
TA260 002:594.366 JLINK_WriteReg(R5, 0x00000000)
TA260 002:594.370 - 0.003ms returns 0
TA260 002:594.374 JLINK_WriteReg(R6, 0x00000000)
TA260 002:594.378 - 0.003ms returns 0
TA260 002:594.382 JLINK_WriteReg(R7, 0x00000000)
TA260 002:594.390 - 0.008ms returns 0
TA260 002:594.397 JLINK_WriteReg(R8, 0x00000000)
TA260 002:594.400 - 0.004ms returns 0
TA260 002:594.404 JLINK_WriteReg(R9, 0x20000180)
TA260 002:594.408 - 0.003ms returns 0
TA260 002:594.412 JLINK_WriteReg(R10, 0x00000000)
TA260 002:594.415 - 0.003ms returns 0
TA260 002:594.419 JLINK_WriteReg(R11, 0x00000000)
TA260 002:594.422 - 0.003ms returns 0
TA260 002:594.427 JLINK_WriteReg(R12, 0x00000000)
TA260 002:594.430 - 0.003ms returns 0
TA260 002:594.434 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:594.439 - 0.004ms returns 0
TA260 002:594.443 JLINK_WriteReg(R14, 0x20000001)
TA260 002:594.446 - 0.003ms returns 0
TA260 002:594.450 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:594.454 - 0.003ms returns 0
TA260 002:594.458 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:594.461 - 0.003ms returns 0
TA260 002:594.466 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:594.469 - 0.003ms returns 0
TA260 002:594.473 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:594.476 - 0.003ms returns 0
TA260 002:594.480 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:594.484 - 0.003ms returns 0
TA260 002:594.489 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:594.493 - 0.004ms returns 0x00000021
TA260 002:594.497 JLINK_Go()
TA260 002:594.507   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:597.237 - 2.739ms 
TA260 002:597.250 JLINK_IsHalted()
TA260 002:597.751 - 0.500ms returns FALSE
TA260 002:597.758 JLINK_HasError()
TA260 002:599.306 JLINK_IsHalted()
TA260 002:599.872 - 0.564ms returns FALSE
TA260 002:599.887 JLINK_HasError()
TA260 002:601.307 JLINK_IsHalted()
TA260 002:603.634   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:604.130 - 2.822ms returns TRUE
TA260 002:604.141 JLINK_ReadReg(R15 (PC))
TA260 002:604.147 - 0.006ms returns 0x20000000
TA260 002:604.152 JLINK_ClrBPEx(BPHandle = 0x00000021)
TA260 002:604.156 - 0.004ms returns 0x00
TA260 002:604.161 JLINK_ReadReg(R0)
TA260 002:604.165 - 0.003ms returns 0x00000000
TA260 002:604.553 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:604.565   Data:  B8 00 B8 04 B8 02 B8 06 B8 01 B8 05 B8 03 B8 07 ...
TA260 002:604.577   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:607.174 - 2.620ms returns 0x27C
TA260 002:607.193 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:607.197   Data:  22 03 22 07 A2 00 A2 04 A2 02 A2 06 A2 01 A2 05 ...
TA260 002:607.210   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:609.167 - 1.973ms returns 0x184
TA260 002:609.179 JLINK_HasError()
TA260 002:609.185 JLINK_WriteReg(R0, 0x08005800)
TA260 002:609.190 - 0.005ms returns 0
TA260 002:609.194 JLINK_WriteReg(R1, 0x00000400)
TA260 002:609.198 - 0.003ms returns 0
TA260 002:609.202 JLINK_WriteReg(R2, 0x20000184)
TA260 002:609.205 - 0.003ms returns 0
TA260 002:609.209 JLINK_WriteReg(R3, 0x00000000)
TA260 002:609.213 - 0.003ms returns 0
TA260 002:609.217 JLINK_WriteReg(R4, 0x00000000)
TA260 002:609.220 - 0.003ms returns 0
TA260 002:609.224 JLINK_WriteReg(R5, 0x00000000)
TA260 002:609.228 - 0.003ms returns 0
TA260 002:609.232 JLINK_WriteReg(R6, 0x00000000)
TA260 002:609.235 - 0.003ms returns 0
TA260 002:609.239 JLINK_WriteReg(R7, 0x00000000)
TA260 002:609.242 - 0.003ms returns 0
TA260 002:609.246 JLINK_WriteReg(R8, 0x00000000)
TA260 002:609.250 - 0.003ms returns 0
TA260 002:609.254 JLINK_WriteReg(R9, 0x20000180)
TA260 002:609.257 - 0.003ms returns 0
TA260 002:609.261 JLINK_WriteReg(R10, 0x00000000)
TA260 002:609.265 - 0.003ms returns 0
TA260 002:609.269 JLINK_WriteReg(R11, 0x00000000)
TA260 002:609.272 - 0.003ms returns 0
TA260 002:609.276 JLINK_WriteReg(R12, 0x00000000)
TA260 002:609.279 - 0.003ms returns 0
TA260 002:609.284 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:609.288 - 0.004ms returns 0
TA260 002:609.292 JLINK_WriteReg(R14, 0x20000001)
TA260 002:609.295 - 0.003ms returns 0
TA260 002:609.299 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:609.303 - 0.003ms returns 0
TA260 002:609.307 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:609.310 - 0.003ms returns 0
TA260 002:609.314 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:609.361 - 0.046ms returns 0
TA260 002:609.365 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:609.368 - 0.003ms returns 0
TA260 002:609.373 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:609.376 - 0.003ms returns 0
TA260 002:609.380 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:609.394 - 0.014ms returns 0x00000022
TA260 002:609.399 JLINK_Go()
TA260 002:609.407   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:612.212 - 2.812ms 
TA260 002:612.226 JLINK_IsHalted()
TA260 002:612.766 - 0.540ms returns FALSE
TA260 002:612.774 JLINK_HasError()
TA260 002:613.822 JLINK_IsHalted()
TA260 002:614.303 - 0.480ms returns FALSE
TA260 002:614.313 JLINK_HasError()
TA260 002:616.320 JLINK_IsHalted()
TA260 002:618.653   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:619.192 - 2.872ms returns TRUE
TA260 002:619.200 JLINK_ReadReg(R15 (PC))
TA260 002:619.206 - 0.005ms returns 0x20000000
TA260 002:619.211 JLINK_ClrBPEx(BPHandle = 0x00000022)
TA260 002:619.215 - 0.004ms returns 0x00
TA260 002:619.220 JLINK_ReadReg(R0)
TA260 002:619.223 - 0.003ms returns 0x00000000
TA260 002:619.618 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:619.626   Data:  BA 00 BA 04 BA 02 BA 06 BA 01 BA 05 BA 03 BA 07 ...
TA260 002:619.636   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:622.226 - 2.607ms returns 0x27C
TA260 002:622.250 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:622.254   Data:  13 5C 60 3E 7F 9A 6C 3E CC CF 78 3E C0 7D 82 3E ...
TA260 002:622.268   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:624.136 - 1.886ms returns 0x184
TA260 002:624.148 JLINK_HasError()
TA260 002:624.154 JLINK_WriteReg(R0, 0x08005C00)
TA260 002:624.160 - 0.005ms returns 0
TA260 002:624.165 JLINK_WriteReg(R1, 0x00000400)
TA260 002:624.168 - 0.003ms returns 0
TA260 002:624.172 JLINK_WriteReg(R2, 0x20000184)
TA260 002:624.176 - 0.003ms returns 0
TA260 002:624.180 JLINK_WriteReg(R3, 0x00000000)
TA260 002:624.183 - 0.003ms returns 0
TA260 002:624.187 JLINK_WriteReg(R4, 0x00000000)
TA260 002:624.191 - 0.003ms returns 0
TA260 002:624.195 JLINK_WriteReg(R5, 0x00000000)
TA260 002:624.198 - 0.003ms returns 0
TA260 002:624.202 JLINK_WriteReg(R6, 0x00000000)
TA260 002:624.206 - 0.003ms returns 0
TA260 002:624.210 JLINK_WriteReg(R7, 0x00000000)
TA260 002:624.213 - 0.003ms returns 0
TA260 002:624.217 JLINK_WriteReg(R8, 0x00000000)
TA260 002:624.220 - 0.003ms returns 0
TA260 002:624.224 JLINK_WriteReg(R9, 0x20000180)
TA260 002:624.228 - 0.003ms returns 0
TA260 002:624.232 JLINK_WriteReg(R10, 0x00000000)
TA260 002:624.235 - 0.003ms returns 0
TA260 002:624.239 JLINK_WriteReg(R11, 0x00000000)
TA260 002:624.243 - 0.003ms returns 0
TA260 002:624.247 JLINK_WriteReg(R12, 0x00000000)
TA260 002:624.250 - 0.003ms returns 0
TA260 002:624.255 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:624.259 - 0.004ms returns 0
TA260 002:624.263 JLINK_WriteReg(R14, 0x20000001)
TA260 002:624.266 - 0.003ms returns 0
TA260 002:624.270 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:624.274 - 0.003ms returns 0
TA260 002:624.278 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:624.281 - 0.003ms returns 0
TA260 002:624.286 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:624.289 - 0.003ms returns 0
TA260 002:624.293 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:624.296 - 0.003ms returns 0
TA260 002:624.300 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:624.304 - 0.003ms returns 0
TA260 002:624.308 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:624.313 - 0.004ms returns 0x00000023
TA260 002:624.317 JLINK_Go()
TA260 002:624.327   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:627.049 - 2.731ms 
TA260 002:627.056 JLINK_IsHalted()
TA260 002:627.516 - 0.459ms returns FALSE
TA260 002:627.524 JLINK_HasError()
TA260 002:629.851 JLINK_IsHalted()
TA260 002:630.397 - 0.546ms returns FALSE
TA260 002:630.409 JLINK_HasError()
TA260 002:631.830 JLINK_IsHalted()
TA260 002:634.207   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:634.770 - 2.940ms returns TRUE
TA260 002:634.782 JLINK_ReadReg(R15 (PC))
TA260 002:634.831 - 0.048ms returns 0x20000000
TA260 002:634.840 JLINK_ClrBPEx(BPHandle = 0x00000023)
TA260 002:634.855 - 0.014ms returns 0x00
TA260 002:634.860 JLINK_ReadReg(R0)
TA260 002:634.864 - 0.003ms returns 0x00000000
TA260 002:635.284 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:635.296   Data:  C9 BF 7C 3F AC 3A 7D 3F CC AB 7D 3F 23 13 7E 3F ...
TA260 002:635.307   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:637.924 - 2.639ms returns 0x27C
TA260 002:637.936 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:637.939   Data:  13 5C 60 BE 7F 9A 6C BE CC CF 78 BE C0 7D 82 BE ...
TA260 002:637.948   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:639.854 - 1.917ms returns 0x184
TA260 002:639.871 JLINK_HasError()
TA260 002:639.877 JLINK_WriteReg(R0, 0x08006000)
TA260 002:639.883 - 0.005ms returns 0
TA260 002:639.888 JLINK_WriteReg(R1, 0x00000400)
TA260 002:639.892 - 0.003ms returns 0
TA260 002:639.896 JLINK_WriteReg(R2, 0x20000184)
TA260 002:639.900 - 0.003ms returns 0
TA260 002:639.904 JLINK_WriteReg(R3, 0x00000000)
TA260 002:639.907 - 0.003ms returns 0
TA260 002:639.911 JLINK_WriteReg(R4, 0x00000000)
TA260 002:639.914 - 0.003ms returns 0
TA260 002:639.918 JLINK_WriteReg(R5, 0x00000000)
TA260 002:639.922 - 0.003ms returns 0
TA260 002:639.926 JLINK_WriteReg(R6, 0x00000000)
TA260 002:639.929 - 0.003ms returns 0
TA260 002:639.933 JLINK_WriteReg(R7, 0x00000000)
TA260 002:639.937 - 0.003ms returns 0
TA260 002:639.941 JLINK_WriteReg(R8, 0x00000000)
TA260 002:639.944 - 0.003ms returns 0
TA260 002:639.948 JLINK_WriteReg(R9, 0x20000180)
TA260 002:639.951 - 0.003ms returns 0
TA260 002:639.955 JLINK_WriteReg(R10, 0x00000000)
TA260 002:639.959 - 0.003ms returns 0
TA260 002:639.963 JLINK_WriteReg(R11, 0x00000000)
TA260 002:639.966 - 0.003ms returns 0
TA260 002:639.970 JLINK_WriteReg(R12, 0x00000000)
TA260 002:639.974 - 0.003ms returns 0
TA260 002:639.978 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:639.982 - 0.004ms returns 0
TA260 002:639.986 JLINK_WriteReg(R14, 0x20000001)
TA260 002:639.990 - 0.003ms returns 0
TA260 002:639.994 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:639.997 - 0.003ms returns 0
TA260 002:640.002 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:640.005 - 0.003ms returns 0
TA260 002:640.009 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:640.012 - 0.003ms returns 0
TA260 002:640.016 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:640.020 - 0.003ms returns 0
TA260 002:640.024 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:640.027 - 0.003ms returns 0
TA260 002:640.032 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:640.036 - 0.004ms returns 0x00000024
TA260 002:640.040 JLINK_Go()
TA260 002:640.049   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:642.764 - 2.723ms 
TA260 002:642.778 JLINK_IsHalted()
TA260 002:643.275 - 0.497ms returns FALSE
TA260 002:643.282 JLINK_HasError()
TA260 002:644.332 JLINK_IsHalted()
TA260 002:644.844 - 0.511ms returns FALSE
TA260 002:644.854 JLINK_HasError()
TA260 002:646.336 JLINK_IsHalted()
TA260 002:649.166   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:649.654 - 3.317ms returns TRUE
TA260 002:649.661 JLINK_ReadReg(R15 (PC))
TA260 002:649.666 - 0.004ms returns 0x20000000
TA260 002:649.670 JLINK_ClrBPEx(BPHandle = 0x00000024)
TA260 002:649.674 - 0.003ms returns 0x00
TA260 002:649.679 JLINK_ReadReg(R0)
TA260 002:649.682 - 0.003ms returns 0x00000000
TA260 002:650.268 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:650.276   Data:  C9 BF 7C BF AC 3A 7D BF CC AB 7D BF 23 13 7E BF ...
TA260 002:650.288   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:652.894 - 2.626ms returns 0x27C
TA260 002:652.904 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:652.908   Data:  00 4D 4F 44 45 5F 44 49 53 41 4E 54 49 00 4D 4F ...
TA260 002:652.916   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:654.766 - 1.862ms returns 0x184
TA260 002:654.776 JLINK_HasError()
TA260 002:654.781 JLINK_WriteReg(R0, 0x08006400)
TA260 002:654.786 - 0.004ms returns 0
TA260 002:654.790 JLINK_WriteReg(R1, 0x00000400)
TA260 002:654.829 - 0.039ms returns 0
TA260 002:654.835 JLINK_WriteReg(R2, 0x20000184)
TA260 002:654.839 - 0.003ms returns 0
TA260 002:654.843 JLINK_WriteReg(R3, 0x00000000)
TA260 002:654.847 - 0.003ms returns 0
TA260 002:654.851 JLINK_WriteReg(R4, 0x00000000)
TA260 002:654.854 - 0.003ms returns 0
TA260 002:654.858 JLINK_WriteReg(R5, 0x00000000)
TA260 002:654.862 - 0.003ms returns 0
TA260 002:654.866 JLINK_WriteReg(R6, 0x00000000)
TA260 002:654.869 - 0.003ms returns 0
TA260 002:654.873 JLINK_WriteReg(R7, 0x00000000)
TA260 002:654.877 - 0.003ms returns 0
TA260 002:654.881 JLINK_WriteReg(R8, 0x00000000)
TA260 002:654.884 - 0.003ms returns 0
TA260 002:654.888 JLINK_WriteReg(R9, 0x20000180)
TA260 002:654.892 - 0.003ms returns 0
TA260 002:654.896 JLINK_WriteReg(R10, 0x00000000)
TA260 002:654.899 - 0.003ms returns 0
TA260 002:654.903 JLINK_WriteReg(R11, 0x00000000)
TA260 002:654.907 - 0.003ms returns 0
TA260 002:654.911 JLINK_WriteReg(R12, 0x00000000)
TA260 002:654.914 - 0.003ms returns 0
TA260 002:654.918 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:654.922 - 0.003ms returns 0
TA260 002:654.926 JLINK_WriteReg(R14, 0x20000001)
TA260 002:654.929 - 0.003ms returns 0
TA260 002:654.934 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:654.937 - 0.003ms returns 0
TA260 002:654.941 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:654.944 - 0.003ms returns 0
TA260 002:654.949 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:654.952 - 0.003ms returns 0
TA260 002:654.956 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:654.959 - 0.003ms returns 0
TA260 002:654.963 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:654.967 - 0.003ms returns 0
TA260 002:654.972 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:654.976 - 0.004ms returns 0x00000025
TA260 002:654.980 JLINK_Go()
TA260 002:654.988   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:657.669 - 2.688ms 
TA260 002:657.679 JLINK_IsHalted()
TA260 002:658.172 - 0.493ms returns FALSE
TA260 002:658.178 JLINK_HasError()
TA260 002:661.349 JLINK_IsHalted()
TA260 002:661.855 - 0.505ms returns FALSE
TA260 002:661.897 JLINK_HasError()
TA260 002:663.346 JLINK_IsHalted()
TA260 002:665.696   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:666.161 - 2.814ms returns TRUE
TA260 002:666.167 JLINK_ReadReg(R15 (PC))
TA260 002:666.172 - 0.005ms returns 0x20000000
TA260 002:666.177 JLINK_ClrBPEx(BPHandle = 0x00000025)
TA260 002:666.180 - 0.003ms returns 0x00
TA260 002:666.185 JLINK_ReadReg(R0)
TA260 002:666.188 - 0.003ms returns 0x00000000
TA260 002:666.494 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:666.501   Data:  B1 90 7F 3F 38 A0 6E 3D C2 8A 7F 3F E9 E5 74 3D ...
TA260 002:666.511   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:669.128 - 2.634ms returns 0x27C
TA260 002:669.135 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:669.139   Data:  50 CC 36 3E 4D D1 7B 3F EC 57 38 3E 20 BF 7B 3F ...
TA260 002:669.150   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:671.031 - 1.895ms returns 0x184
TA260 002:671.091 JLINK_HasError()
TA260 002:671.127 JLINK_WriteReg(R0, 0x08006800)
TA260 002:671.134 - 0.006ms returns 0
TA260 002:671.138 JLINK_WriteReg(R1, 0x00000400)
TA260 002:671.142 - 0.003ms returns 0
TA260 002:671.150 JLINK_WriteReg(R2, 0x20000184)
TA260 002:671.153 - 0.003ms returns 0
TA260 002:671.157 JLINK_WriteReg(R3, 0x00000000)
TA260 002:671.160 - 0.003ms returns 0
TA260 002:671.164 JLINK_WriteReg(R4, 0x00000000)
TA260 002:671.168 - 0.003ms returns 0
TA260 002:671.172 JLINK_WriteReg(R5, 0x00000000)
TA260 002:671.175 - 0.003ms returns 0
TA260 002:671.179 JLINK_WriteReg(R6, 0x00000000)
TA260 002:671.182 - 0.003ms returns 0
TA260 002:671.186 JLINK_WriteReg(R7, 0x00000000)
TA260 002:671.190 - 0.003ms returns 0
TA260 002:671.194 JLINK_WriteReg(R8, 0x00000000)
TA260 002:671.197 - 0.003ms returns 0
TA260 002:671.201 JLINK_WriteReg(R9, 0x20000180)
TA260 002:671.204 - 0.003ms returns 0
TA260 002:671.208 JLINK_WriteReg(R10, 0x00000000)
TA260 002:671.237 - 0.028ms returns 0
TA260 002:671.242 JLINK_WriteReg(R11, 0x00000000)
TA260 002:671.245 - 0.003ms returns 0
TA260 002:671.290 JLINK_WriteReg(R12, 0x00000000)
TA260 002:671.294 - 0.003ms returns 0
TA260 002:671.298 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:671.303 - 0.004ms returns 0
TA260 002:671.307 JLINK_WriteReg(R14, 0x20000001)
TA260 002:671.310 - 0.003ms returns 0
TA260 002:671.314 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:671.318 - 0.003ms returns 0
TA260 002:671.322 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:671.325 - 0.003ms returns 0
TA260 002:671.329 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:671.332 - 0.003ms returns 0
TA260 002:671.336 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:671.340 - 0.003ms returns 0
TA260 002:671.344 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:671.347 - 0.003ms returns 0
TA260 002:671.352 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:671.356 - 0.004ms returns 0x00000026
TA260 002:671.360 JLINK_Go()
TA260 002:671.369   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:674.116 - 2.755ms 
TA260 002:674.124 JLINK_IsHalted()
TA260 002:674.659 - 0.534ms returns FALSE
TA260 002:674.665 JLINK_HasError()
TA260 002:676.352 JLINK_IsHalted()
TA260 002:676.796 - 0.443ms returns FALSE
TA260 002:676.801 JLINK_HasError()
TA260 002:677.860 JLINK_IsHalted()
TA260 002:678.386 - 0.525ms returns FALSE
TA260 002:678.392 JLINK_HasError()
TA260 002:679.859 JLINK_IsHalted()
TA260 002:682.169   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:682.652 - 2.792ms returns TRUE
TA260 002:682.659 JLINK_ReadReg(R15 (PC))
TA260 002:682.664 - 0.005ms returns 0x20000000
TA260 002:682.668 JLINK_ClrBPEx(BPHandle = 0x00000026)
TA260 002:682.672 - 0.003ms returns 0x00
TA260 002:682.676 JLINK_ReadReg(R0)
TA260 002:682.680 - 0.003ms returns 0x00000000
TA260 002:683.011 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:683.019   Data:  B7 BE 77 3F C0 F8 80 3E 51 A5 77 3F 4A BB 81 3E ...
TA260 002:683.028   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:685.626 - 2.614ms returns 0x27C
TA260 002:685.632 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:685.636   Data:  B6 EC BB 3E F2 FC 6D 3F AF A7 BC 3E D5 D7 6D 3F ...
TA260 002:685.643   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:687.493 - 1.861ms returns 0x184
TA260 002:687.502 JLINK_HasError()
TA260 002:687.508 JLINK_WriteReg(R0, 0x08006C00)
TA260 002:687.513 - 0.005ms returns 0
TA260 002:687.519 JLINK_WriteReg(R1, 0x00000400)
TA260 002:687.523 - 0.004ms returns 0
TA260 002:687.528 JLINK_WriteReg(R2, 0x20000184)
TA260 002:687.533 - 0.004ms returns 0
TA260 002:687.538 JLINK_WriteReg(R3, 0x00000000)
TA260 002:687.542 - 0.004ms returns 0
TA260 002:687.547 JLINK_WriteReg(R4, 0x00000000)
TA260 002:687.551 - 0.004ms returns 0
TA260 002:687.556 JLINK_WriteReg(R5, 0x00000000)
TA260 002:687.560 - 0.004ms returns 0
TA260 002:687.566 JLINK_WriteReg(R6, 0x00000000)
TA260 002:687.570 - 0.004ms returns 0
TA260 002:687.575 JLINK_WriteReg(R7, 0x00000000)
TA260 002:687.579 - 0.004ms returns 0
TA260 002:687.584 JLINK_WriteReg(R8, 0x00000000)
TA260 002:687.589 - 0.004ms returns 0
TA260 002:687.594 JLINK_WriteReg(R9, 0x20000180)
TA260 002:687.598 - 0.004ms returns 0
TA260 002:687.604 JLINK_WriteReg(R10, 0x00000000)
TA260 002:687.608 - 0.004ms returns 0
TA260 002:687.613 JLINK_WriteReg(R11, 0x00000000)
TA260 002:687.617 - 0.004ms returns 0
TA260 002:687.623 JLINK_WriteReg(R12, 0x00000000)
TA260 002:687.626 - 0.003ms returns 0
TA260 002:687.631 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:687.634 - 0.003ms returns 0
TA260 002:687.638 JLINK_WriteReg(R14, 0x20000001)
TA260 002:687.642 - 0.003ms returns 0
TA260 002:687.652 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:687.656 - 0.010ms returns 0
TA260 002:687.660 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:687.663 - 0.003ms returns 0
TA260 002:687.667 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:687.671 - 0.003ms returns 0
TA260 002:687.675 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:687.678 - 0.003ms returns 0
TA260 002:687.682 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:687.686 - 0.003ms returns 0
TA260 002:687.690 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:687.698 - 0.008ms returns 0x00000027
TA260 002:687.702 JLINK_Go()
TA260 002:687.709   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:690.402 - 2.699ms 
TA260 002:690.413 JLINK_IsHalted()
TA260 002:690.915 - 0.501ms returns FALSE
TA260 002:690.928 JLINK_HasError()
TA260 002:692.365 JLINK_IsHalted()
TA260 002:692.846 - 0.480ms returns FALSE
TA260 002:692.856 JLINK_HasError()
TA260 002:694.365 JLINK_IsHalted()
TA260 002:696.711   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:697.197 - 2.831ms returns TRUE
TA260 002:697.211 JLINK_ReadReg(R15 (PC))
TA260 002:697.217 - 0.006ms returns 0x20000000
TA260 002:697.231 JLINK_ClrBPEx(BPHandle = 0x00000027)
TA260 002:697.236 - 0.005ms returns 0x00
TA260 002:697.308 JLINK_ReadReg(R0)
TA260 002:697.312 - 0.004ms returns 0x00000000
TA260 002:697.625 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:697.632   Data:  71 67 66 3F A9 28 DF 3E 8E 3B 66 3F 8D DD DF 3E ...
TA260 002:697.641   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:700.227 - 2.602ms returns 0x27C
TA260 002:700.237 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:700.241   Data:  3D 9D 0A 3F 48 03 57 3F B7 F1 0A 3F A7 CC 56 3F ...
TA260 002:700.250   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:702.177 - 1.939ms returns 0x184
TA260 002:702.188 JLINK_HasError()
TA260 002:702.194 JLINK_WriteReg(R0, 0x08007000)
TA260 002:702.199 - 0.005ms returns 0
TA260 002:702.203 JLINK_WriteReg(R1, 0x00000400)
TA260 002:702.207 - 0.003ms returns 0
TA260 002:702.211 JLINK_WriteReg(R2, 0x20000184)
TA260 002:702.214 - 0.003ms returns 0
TA260 002:702.218 JLINK_WriteReg(R3, 0x00000000)
TA260 002:702.222 - 0.003ms returns 0
TA260 002:702.226 JLINK_WriteReg(R4, 0x00000000)
TA260 002:702.229 - 0.003ms returns 0
TA260 002:702.233 JLINK_WriteReg(R5, 0x00000000)
TA260 002:702.236 - 0.003ms returns 0
TA260 002:702.240 JLINK_WriteReg(R6, 0x00000000)
TA260 002:702.244 - 0.003ms returns 0
TA260 002:702.248 JLINK_WriteReg(R7, 0x00000000)
TA260 002:702.251 - 0.003ms returns 0
TA260 002:702.255 JLINK_WriteReg(R8, 0x00000000)
TA260 002:702.259 - 0.003ms returns 0
TA260 002:702.263 JLINK_WriteReg(R9, 0x20000180)
TA260 002:702.266 - 0.003ms returns 0
TA260 002:702.270 JLINK_WriteReg(R10, 0x00000000)
TA260 002:702.274 - 0.003ms returns 0
TA260 002:702.278 JLINK_WriteReg(R11, 0x00000000)
TA260 002:702.281 - 0.003ms returns 0
TA260 002:702.285 JLINK_WriteReg(R12, 0x00000000)
TA260 002:702.288 - 0.003ms returns 0
TA260 002:702.293 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:702.296 - 0.003ms returns 0
TA260 002:702.300 JLINK_WriteReg(R14, 0x20000001)
TA260 002:702.304 - 0.003ms returns 0
TA260 002:702.308 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:702.311 - 0.003ms returns 0
TA260 002:702.315 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:702.411 - 0.095ms returns 0
TA260 002:702.415 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:702.419 - 0.003ms returns 0
TA260 002:702.423 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:702.426 - 0.003ms returns 0
TA260 002:702.430 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:702.434 - 0.003ms returns 0
TA260 002:702.438 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:702.442 - 0.004ms returns 0x00000028
TA260 002:702.446 JLINK_Go()
TA260 002:702.454   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:705.180 - 2.733ms 
TA260 002:705.186 JLINK_IsHalted()
TA260 002:705.660 - 0.474ms returns FALSE
TA260 002:705.666 JLINK_HasError()
TA260 002:707.374 JLINK_IsHalted()
TA260 002:707.890 - 0.515ms returns FALSE
TA260 002:707.896 JLINK_HasError()
TA260 002:709.377 JLINK_IsHalted()
TA260 002:711.704   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:712.185 - 2.808ms returns TRUE
TA260 002:712.192 JLINK_ReadReg(R15 (PC))
TA260 002:712.196 - 0.004ms returns 0x20000000
TA260 002:712.201 JLINK_ClrBPEx(BPHandle = 0x00000028)
TA260 002:712.204 - 0.003ms returns 0x00
TA260 002:712.209 JLINK_ReadReg(R0)
TA260 002:712.212 - 0.003ms returns 0x00000000
TA260 002:712.537 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:712.547   Data:  78 35 4C 3F 93 62 1A 3F C7 F8 4B 3F B8 B2 1A 3F ...
TA260 002:712.558   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:715.128 - 2.590ms returns 0x27C
TA260 002:715.135 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:715.138   Data:  71 F0 31 3F 55 C6 37 3F AA 38 32 3F 4A 80 37 3F ...
TA260 002:715.145   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:716.991 - 1.856ms returns 0x184
TA260 002:716.997 JLINK_HasError()
TA260 002:717.002 JLINK_WriteReg(R0, 0x08007400)
TA260 002:717.006 - 0.004ms returns 0
TA260 002:717.011 JLINK_WriteReg(R1, 0x00000400)
TA260 002:717.014 - 0.003ms returns 0
TA260 002:717.018 JLINK_WriteReg(R2, 0x20000184)
TA260 002:717.022 - 0.003ms returns 0
TA260 002:717.026 JLINK_WriteReg(R3, 0x00000000)
TA260 002:717.029 - 0.003ms returns 0
TA260 002:717.033 JLINK_WriteReg(R4, 0x00000000)
TA260 002:717.036 - 0.003ms returns 0
TA260 002:717.040 JLINK_WriteReg(R5, 0x00000000)
TA260 002:717.044 - 0.003ms returns 0
TA260 002:717.048 JLINK_WriteReg(R6, 0x00000000)
TA260 002:717.051 - 0.003ms returns 0
TA260 002:717.055 JLINK_WriteReg(R7, 0x00000000)
TA260 002:717.058 - 0.003ms returns 0
TA260 002:717.062 JLINK_WriteReg(R8, 0x00000000)
TA260 002:717.066 - 0.003ms returns 0
TA260 002:717.070 JLINK_WriteReg(R9, 0x20000180)
TA260 002:717.073 - 0.003ms returns 0
TA260 002:717.093 JLINK_WriteReg(R10, 0x00000000)
TA260 002:717.097 - 0.020ms returns 0
TA260 002:717.101 JLINK_WriteReg(R11, 0x00000000)
TA260 002:717.105 - 0.003ms returns 0
TA260 002:717.109 JLINK_WriteReg(R12, 0x00000000)
TA260 002:717.112 - 0.003ms returns 0
TA260 002:717.116 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:717.120 - 0.003ms returns 0
TA260 002:717.124 JLINK_WriteReg(R14, 0x20000001)
TA260 002:717.128 - 0.003ms returns 0
TA260 002:717.132 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:717.135 - 0.003ms returns 0
TA260 002:717.139 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:717.143 - 0.003ms returns 0
TA260 002:717.147 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:717.150 - 0.003ms returns 0
TA260 002:717.154 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:717.158 - 0.003ms returns 0
TA260 002:717.162 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:717.165 - 0.003ms returns 0
TA260 002:717.169 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:717.173 - 0.004ms returns 0x00000029
TA260 002:717.177 JLINK_Go()
TA260 002:717.184   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:719.894 - 2.715ms 
TA260 002:719.904 JLINK_IsHalted()
TA260 002:720.369 - 0.463ms returns FALSE
TA260 002:720.381 JLINK_HasError()
TA260 002:721.886 JLINK_IsHalted()
TA260 002:722.369 - 0.482ms returns FALSE
TA260 002:722.380 JLINK_HasError()
TA260 002:723.881 JLINK_IsHalted()
TA260 002:726.240   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:726.766 - 2.884ms returns TRUE
TA260 002:726.772 JLINK_ReadReg(R15 (PC))
TA260 002:726.777 - 0.004ms returns 0x20000000
TA260 002:726.782 JLINK_ClrBPEx(BPHandle = 0x00000029)
TA260 002:726.785 - 0.003ms returns 0x00
TA260 002:726.790 JLINK_ReadReg(R0)
TA260 002:726.793 - 0.003ms returns 0x00000000
TA260 002:727.154 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:727.164   Data:  80 2A 2A 3F FC 41 3F 3F 57 DF 29 3F C0 84 3F 3F ...
TA260 002:727.176   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:729.710 - 2.556ms returns 0x27C
TA260 002:729.721 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:729.725   Data:  18 6D 52 3F 6B 79 11 3F 49 A6 52 3F A7 26 11 3F ...
TA260 002:729.734   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:731.729 - 2.007ms returns 0x184
TA260 002:731.743 JLINK_HasError()
TA260 002:731.748 JLINK_WriteReg(R0, 0x08007800)
TA260 002:731.754 - 0.005ms returns 0
TA260 002:731.758 JLINK_WriteReg(R1, 0x00000400)
TA260 002:731.762 - 0.003ms returns 0
TA260 002:731.766 JLINK_WriteReg(R2, 0x20000184)
TA260 002:731.769 - 0.003ms returns 0
TA260 002:731.774 JLINK_WriteReg(R3, 0x00000000)
TA260 002:731.777 - 0.004ms returns 0
TA260 002:731.781 JLINK_WriteReg(R4, 0x00000000)
TA260 002:731.785 - 0.003ms returns 0
TA260 002:731.794 JLINK_WriteReg(R5, 0x00000000)
TA260 002:731.797 - 0.003ms returns 0
TA260 002:731.801 JLINK_WriteReg(R6, 0x00000000)
TA260 002:731.804 - 0.003ms returns 0
TA260 002:731.808 JLINK_WriteReg(R7, 0x00000000)
TA260 002:731.812 - 0.003ms returns 0
TA260 002:731.816 JLINK_WriteReg(R8, 0x00000000)
TA260 002:731.819 - 0.003ms returns 0
TA260 002:731.823 JLINK_WriteReg(R9, 0x20000180)
TA260 002:731.827 - 0.003ms returns 0
TA260 002:731.831 JLINK_WriteReg(R10, 0x00000000)
TA260 002:731.834 - 0.003ms returns 0
TA260 002:731.838 JLINK_WriteReg(R11, 0x00000000)
TA260 002:731.841 - 0.003ms returns 0
TA260 002:731.846 JLINK_WriteReg(R12, 0x00000000)
TA260 002:731.849 - 0.003ms returns 0
TA260 002:731.853 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:731.857 - 0.003ms returns 0
TA260 002:731.861 JLINK_WriteReg(R14, 0x20000001)
TA260 002:731.864 - 0.003ms returns 0
TA260 002:731.868 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:731.872 - 0.003ms returns 0
TA260 002:731.876 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:731.879 - 0.003ms returns 0
TA260 002:731.883 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:731.886 - 0.003ms returns 0
TA260 002:731.890 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:731.894 - 0.003ms returns 0
TA260 002:731.898 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:731.901 - 0.003ms returns 0
TA260 002:731.906 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:731.910 - 0.004ms returns 0x0000002A
TA260 002:731.914 JLINK_Go()
TA260 002:731.922   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:734.718 - 2.804ms 
TA260 002:734.726 JLINK_IsHalted()
TA260 002:735.191 - 0.464ms returns FALSE
TA260 002:735.196 JLINK_HasError()
TA260 002:737.895 JLINK_IsHalted()
TA260 002:738.333 - 0.437ms returns FALSE
TA260 002:738.344 JLINK_HasError()
TA260 002:739.894 JLINK_IsHalted()
TA260 002:742.537   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:743.047 - 3.152ms returns TRUE
TA260 002:743.054 JLINK_ReadReg(R15 (PC))
TA260 002:743.059 - 0.004ms returns 0x20000000
TA260 002:743.064 JLINK_ClrBPEx(BPHandle = 0x0000002A)
TA260 002:743.068 - 0.003ms returns 0x00
TA260 002:743.072 JLINK_ReadReg(R0)
TA260 002:743.076 - 0.003ms returns 0x00000000
TA260 002:743.396 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:743.403   Data:  73 95 01 3F D1 C7 5C 3F B5 3E 01 3F A3 FA 5C 3F ...
TA260 002:743.413   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:745.995 - 2.599ms returns 0x27C
TA260 002:746.003 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:746.006   Data:  95 D3 6A 3F AE 2A CB 3E 8C FB 6A 3F 10 72 CA 3E ...
TA260 002:746.014   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:747.853 - 1.850ms returns 0x184
TA260 002:747.865 JLINK_HasError()
TA260 002:747.894 JLINK_WriteReg(R0, 0x08007C00)
TA260 002:747.899 - 0.005ms returns 0
TA260 002:747.904 JLINK_WriteReg(R1, 0x00000400)
TA260 002:747.907 - 0.003ms returns 0
TA260 002:747.912 JLINK_WriteReg(R2, 0x20000184)
TA260 002:747.915 - 0.003ms returns 0
TA260 002:747.919 JLINK_WriteReg(R3, 0x00000000)
TA260 002:747.922 - 0.003ms returns 0
TA260 002:747.926 JLINK_WriteReg(R4, 0x00000000)
TA260 002:747.930 - 0.003ms returns 0
TA260 002:747.934 JLINK_WriteReg(R5, 0x00000000)
TA260 002:747.937 - 0.003ms returns 0
TA260 002:747.941 JLINK_WriteReg(R6, 0x00000000)
TA260 002:747.944 - 0.003ms returns 0
TA260 002:747.948 JLINK_WriteReg(R7, 0x00000000)
TA260 002:747.952 - 0.003ms returns 0
TA260 002:747.956 JLINK_WriteReg(R8, 0x00000000)
TA260 002:747.959 - 0.003ms returns 0
TA260 002:747.963 JLINK_WriteReg(R9, 0x20000180)
TA260 002:747.967 - 0.003ms returns 0
TA260 002:747.971 JLINK_WriteReg(R10, 0x00000000)
TA260 002:747.974 - 0.003ms returns 0
TA260 002:747.978 JLINK_WriteReg(R11, 0x00000000)
TA260 002:747.982 - 0.003ms returns 0
TA260 002:747.986 JLINK_WriteReg(R12, 0x00000000)
TA260 002:747.989 - 0.003ms returns 0
TA260 002:747.993 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:747.997 - 0.003ms returns 0
TA260 002:748.001 JLINK_WriteReg(R14, 0x20000001)
TA260 002:748.004 - 0.003ms returns 0
TA260 002:748.013 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:748.016 - 0.003ms returns 0
TA260 002:748.020 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:748.024 - 0.003ms returns 0
TA260 002:748.028 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:748.031 - 0.003ms returns 0
TA260 002:748.035 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:748.038 - 0.003ms returns 0
TA260 002:748.042 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:748.046 - 0.003ms returns 0
TA260 002:748.050 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:748.054 - 0.004ms returns 0x0000002B
TA260 002:748.059 JLINK_Go()
TA260 002:748.066   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:750.918 - 2.859ms 
TA260 002:750.932 JLINK_IsHalted()
TA260 002:751.376 - 0.444ms returns FALSE
TA260 002:751.384 JLINK_HasError()
TA260 002:753.399 JLINK_IsHalted()
TA260 002:753.868 - 0.468ms returns FALSE
TA260 002:753.880 JLINK_HasError()
TA260 002:755.398 JLINK_IsHalted()
TA260 002:757.881   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:758.367 - 2.969ms returns TRUE
TA260 002:758.374 JLINK_ReadReg(R15 (PC))
TA260 002:758.378 - 0.004ms returns 0x20000000
TA260 002:758.382 JLINK_ClrBPEx(BPHandle = 0x0000002B)
TA260 002:758.386 - 0.003ms returns 0x00
TA260 002:758.390 JLINK_ReadReg(R0)
TA260 002:758.394 - 0.003ms returns 0x00000000
TA260 002:758.708 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:758.715   Data:  1F 0B A8 3E 9F D1 71 3F 25 4D A7 3E 8C F2 71 3F ...
TA260 002:758.725   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:761.306 - 2.597ms returns 0x27C
TA260 002:761.320 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:761.324   Data:  DD 33 7A 3F 8F 27 57 3E 10 49 7A 3F 58 9E 55 3E ...
TA260 002:761.334   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:763.172 - 1.851ms returns 0x184
TA260 002:763.183 JLINK_HasError()
TA260 002:763.188 JLINK_WriteReg(R0, 0x08008000)
TA260 002:763.193 - 0.005ms returns 0
TA260 002:763.198 JLINK_WriteReg(R1, 0x00000400)
TA260 002:763.201 - 0.003ms returns 0
TA260 002:763.205 JLINK_WriteReg(R2, 0x20000184)
TA260 002:763.209 - 0.003ms returns 0
TA260 002:763.213 JLINK_WriteReg(R3, 0x00000000)
TA260 002:763.216 - 0.003ms returns 0
TA260 002:763.220 JLINK_WriteReg(R4, 0x00000000)
TA260 002:763.224 - 0.003ms returns 0
TA260 002:763.263 JLINK_WriteReg(R5, 0x00000000)
TA260 002:763.266 - 0.003ms returns 0
TA260 002:763.270 JLINK_WriteReg(R6, 0x00000000)
TA260 002:763.274 - 0.003ms returns 0
TA260 002:763.278 JLINK_WriteReg(R7, 0x00000000)
TA260 002:763.282 - 0.003ms returns 0
TA260 002:763.286 JLINK_WriteReg(R8, 0x00000000)
TA260 002:763.290 - 0.003ms returns 0
TA260 002:763.294 JLINK_WriteReg(R9, 0x20000180)
TA260 002:763.297 - 0.003ms returns 0
TA260 002:763.301 JLINK_WriteReg(R10, 0x00000000)
TA260 002:763.305 - 0.003ms returns 0
TA260 002:763.309 JLINK_WriteReg(R11, 0x00000000)
TA260 002:763.312 - 0.003ms returns 0
TA260 002:763.316 JLINK_WriteReg(R12, 0x00000000)
TA260 002:763.319 - 0.003ms returns 0
TA260 002:763.323 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:763.327 - 0.003ms returns 0
TA260 002:763.331 JLINK_WriteReg(R14, 0x20000001)
TA260 002:763.335 - 0.003ms returns 0
TA260 002:763.342 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:763.346 - 0.003ms returns 0
TA260 002:763.350 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:763.353 - 0.003ms returns 0
TA260 002:763.357 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:763.360 - 0.003ms returns 0
TA260 002:763.364 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:763.368 - 0.003ms returns 0
TA260 002:763.372 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:763.375 - 0.003ms returns 0
TA260 002:763.380 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:763.384 - 0.004ms returns 0x0000002C
TA260 002:763.441 JLINK_Go()
TA260 002:763.449   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:766.240 - 2.798ms 
TA260 002:766.246 JLINK_IsHalted()
TA260 002:766.765 - 0.518ms returns FALSE
TA260 002:766.770 JLINK_HasError()
TA260 002:768.408 JLINK_IsHalted()
TA260 002:768.888 - 0.479ms returns FALSE
TA260 002:768.899 JLINK_HasError()
TA260 002:770.912 JLINK_IsHalted()
TA260 002:773.260   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:773.788 - 2.875ms returns TRUE
TA260 002:773.799 JLINK_ReadReg(R15 (PC))
TA260 002:773.804 - 0.005ms returns 0x20000000
TA260 002:773.835 JLINK_ClrBPEx(BPHandle = 0x0000002C)
TA260 002:773.850 - 0.015ms returns 0x00
TA260 002:773.854 JLINK_ReadReg(R0)
TA260 002:773.858 - 0.003ms returns 0x00000000
TA260 002:774.192 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:774.199   Data:  4A EC 0C 3E 6E 90 7D 3F F3 5D 0B 3E 30 9E 7D 3F ...
TA260 002:774.209   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:776.811 - 2.618ms returns 0x27C
TA260 002:776.817 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:776.821   Data:  AC F6 7F 3F 4B 51 7B 3C 4A F8 7F 3F 00 30 62 3C ...
TA260 002:776.828   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:778.660 - 1.843ms returns 0x184
TA260 002:778.668 JLINK_HasError()
TA260 002:778.673 JLINK_WriteReg(R0, 0x08008400)
TA260 002:778.677 - 0.004ms returns 0
TA260 002:778.682 JLINK_WriteReg(R1, 0x00000400)
TA260 002:778.685 - 0.003ms returns 0
TA260 002:778.689 JLINK_WriteReg(R2, 0x20000184)
TA260 002:778.693 - 0.003ms returns 0
TA260 002:778.697 JLINK_WriteReg(R3, 0x00000000)
TA260 002:778.700 - 0.003ms returns 0
TA260 002:778.704 JLINK_WriteReg(R4, 0x00000000)
TA260 002:778.708 - 0.003ms returns 0
TA260 002:778.712 JLINK_WriteReg(R5, 0x00000000)
TA260 002:778.715 - 0.003ms returns 0
TA260 002:778.719 JLINK_WriteReg(R6, 0x00000000)
TA260 002:778.722 - 0.003ms returns 0
TA260 002:778.726 JLINK_WriteReg(R7, 0x00000000)
TA260 002:778.730 - 0.003ms returns 0
TA260 002:778.734 JLINK_WriteReg(R8, 0x00000000)
TA260 002:778.738 - 0.003ms returns 0
TA260 002:778.742 JLINK_WriteReg(R9, 0x20000180)
TA260 002:778.745 - 0.003ms returns 0
TA260 002:778.749 JLINK_WriteReg(R10, 0x00000000)
TA260 002:778.752 - 0.003ms returns 0
TA260 002:778.756 JLINK_WriteReg(R11, 0x00000000)
TA260 002:778.760 - 0.003ms returns 0
TA260 002:778.764 JLINK_WriteReg(R12, 0x00000000)
TA260 002:778.767 - 0.003ms returns 0
TA260 002:778.771 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:778.774 - 0.003ms returns 0
TA260 002:778.778 JLINK_WriteReg(R14, 0x20000001)
TA260 002:778.782 - 0.003ms returns 0
TA260 002:778.786 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:778.789 - 0.003ms returns 0
TA260 002:778.793 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:778.797 - 0.003ms returns 0
TA260 002:778.801 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:778.804 - 0.003ms returns 0
TA260 002:778.808 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:778.812 - 0.003ms returns 0
TA260 002:778.816 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:778.819 - 0.003ms returns 0
TA260 002:778.824 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:778.828 - 0.004ms returns 0x0000002D
TA260 002:778.832 JLINK_Go()
TA260 002:778.842   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:781.665 - 2.832ms 
TA260 002:781.674 JLINK_IsHalted()
TA260 002:782.202 - 0.528ms returns FALSE
TA260 002:782.216 JLINK_HasError()
TA260 002:783.435 JLINK_IsHalted()
TA260 002:783.934 - 0.498ms returns FALSE
TA260 002:783.940 JLINK_HasError()
TA260 002:785.937 JLINK_IsHalted()
TA260 002:788.268   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:788.798 - 2.860ms returns TRUE
TA260 002:788.805 JLINK_ReadReg(R15 (PC))
TA260 002:788.812 - 0.007ms returns 0x20000000
TA260 002:788.816 JLINK_ClrBPEx(BPHandle = 0x0000002D)
TA260 002:788.820 - 0.004ms returns 0x00
TA260 002:788.824 JLINK_ReadReg(R0)
TA260 002:788.828 - 0.003ms returns 0x00000000
TA260 002:789.214 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:789.223   Data:  38 A0 6E BD B1 90 7F 3F E9 E5 74 BD C2 8A 7F 3F ...
TA260 002:789.234   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:791.807 - 2.592ms returns 0x27C
TA260 002:791.820 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:791.824   Data:  53 E3 7B 3F EC 57 38 BE 4D D1 7B 3F 6C E3 39 BE ...
TA260 002:791.834   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:793.706 - 1.886ms returns 0x184
TA260 002:793.720 JLINK_HasError()
TA260 002:793.725 JLINK_WriteReg(R0, 0x08008800)
TA260 002:793.731 - 0.005ms returns 0
TA260 002:793.735 JLINK_WriteReg(R1, 0x00000400)
TA260 002:793.738 - 0.003ms returns 0
TA260 002:793.742 JLINK_WriteReg(R2, 0x20000184)
TA260 002:793.746 - 0.003ms returns 0
TA260 002:793.750 JLINK_WriteReg(R3, 0x00000000)
TA260 002:793.753 - 0.003ms returns 0
TA260 002:793.757 JLINK_WriteReg(R4, 0x00000000)
TA260 002:793.761 - 0.003ms returns 0
TA260 002:793.765 JLINK_WriteReg(R5, 0x00000000)
TA260 002:793.768 - 0.003ms returns 0
TA260 002:793.772 JLINK_WriteReg(R6, 0x00000000)
TA260 002:793.776 - 0.003ms returns 0
TA260 002:793.780 JLINK_WriteReg(R7, 0x00000000)
TA260 002:793.784 - 0.003ms returns 0
TA260 002:793.788 JLINK_WriteReg(R8, 0x00000000)
TA260 002:793.791 - 0.003ms returns 0
TA260 002:793.795 JLINK_WriteReg(R9, 0x20000180)
TA260 002:793.799 - 0.003ms returns 0
TA260 002:793.803 JLINK_WriteReg(R10, 0x00000000)
TA260 002:793.806 - 0.003ms returns 0
TA260 002:793.810 JLINK_WriteReg(R11, 0x00000000)
TA260 002:793.813 - 0.003ms returns 0
TA260 002:793.818 JLINK_WriteReg(R12, 0x00000000)
TA260 002:793.821 - 0.003ms returns 0
TA260 002:793.825 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:793.829 - 0.003ms returns 0
TA260 002:793.833 JLINK_WriteReg(R14, 0x20000001)
TA260 002:793.836 - 0.003ms returns 0
TA260 002:793.840 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:793.844 - 0.003ms returns 0
TA260 002:793.848 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:793.852 - 0.003ms returns 0
TA260 002:793.856 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:793.859 - 0.003ms returns 0
TA260 002:793.863 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:793.866 - 0.003ms returns 0
TA260 002:793.870 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:793.874 - 0.003ms returns 0
TA260 002:793.879 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:793.883 - 0.005ms returns 0x0000002E
TA260 002:793.887 JLINK_Go()
TA260 002:793.896   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:796.626 - 2.738ms 
TA260 002:796.638 JLINK_IsHalted()
TA260 002:797.164 - 0.525ms returns FALSE
TA260 002:797.170 JLINK_HasError()
TA260 002:799.142 JLINK_IsHalted()
TA260 002:799.659 - 0.517ms returns FALSE
TA260 002:799.665 JLINK_HasError()
TA260 002:801.142 JLINK_IsHalted()
TA260 002:803.482   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:803.956 - 2.814ms returns TRUE
TA260 002:803.963 JLINK_ReadReg(R15 (PC))
TA260 002:803.968 - 0.004ms returns 0x20000000
TA260 002:803.972 JLINK_ClrBPEx(BPHandle = 0x0000002E)
TA260 002:803.976 - 0.003ms returns 0x00
TA260 002:803.980 JLINK_ReadReg(R0)
TA260 002:803.984 - 0.003ms returns 0x00000000
TA260 002:804.340 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:804.347   Data:  C0 F8 80 BE B7 BE 77 3F 4A BB 81 BE 51 A5 77 3F ...
TA260 002:804.356   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:806.995 - 2.655ms returns 0x27C
TA260 002:807.004 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:807.008   Data:  EB 21 6E 3F AF A7 BC BE F2 FC 6D 3F 8B 62 BD BE ...
TA260 002:807.016   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:808.900 - 1.895ms returns 0x184
TA260 002:808.908 JLINK_HasError()
TA260 002:808.913 JLINK_WriteReg(R0, 0x08008C00)
TA260 002:808.917 - 0.004ms returns 0
TA260 002:808.922 JLINK_WriteReg(R1, 0x00000400)
TA260 002:808.925 - 0.003ms returns 0
TA260 002:808.929 JLINK_WriteReg(R2, 0x20000184)
TA260 002:808.933 - 0.003ms returns 0
TA260 002:808.937 JLINK_WriteReg(R3, 0x00000000)
TA260 002:808.940 - 0.003ms returns 0
TA260 002:808.944 JLINK_WriteReg(R4, 0x00000000)
TA260 002:808.948 - 0.003ms returns 0
TA260 002:808.952 JLINK_WriteReg(R5, 0x00000000)
TA260 002:808.955 - 0.003ms returns 0
TA260 002:808.959 JLINK_WriteReg(R6, 0x00000000)
TA260 002:808.963 - 0.003ms returns 0
TA260 002:808.967 JLINK_WriteReg(R7, 0x00000000)
TA260 002:808.970 - 0.003ms returns 0
TA260 002:808.974 JLINK_WriteReg(R8, 0x00000000)
TA260 002:808.977 - 0.003ms returns 0
TA260 002:808.984 JLINK_WriteReg(R9, 0x20000180)
TA260 002:808.990 - 0.005ms returns 0
TA260 002:808.994 JLINK_WriteReg(R10, 0x00000000)
TA260 002:808.997 - 0.003ms returns 0
TA260 002:809.001 JLINK_WriteReg(R11, 0x00000000)
TA260 002:809.004 - 0.003ms returns 0
TA260 002:809.008 JLINK_WriteReg(R12, 0x00000000)
TA260 002:809.012 - 0.003ms returns 0
TA260 002:809.016 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:809.020 - 0.003ms returns 0
TA260 002:809.024 JLINK_WriteReg(R14, 0x20000001)
TA260 002:809.027 - 0.003ms returns 0
TA260 002:809.031 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:809.035 - 0.003ms returns 0
TA260 002:809.039 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:809.042 - 0.003ms returns 0
TA260 002:809.046 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:809.050 - 0.003ms returns 0
TA260 002:809.054 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:809.057 - 0.003ms returns 0
TA260 002:809.061 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:809.064 - 0.003ms returns 0
TA260 002:809.069 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:809.073 - 0.004ms returns 0x0000002F
TA260 002:809.077 JLINK_Go()
TA260 002:809.085   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:811.857 - 2.779ms 
TA260 002:811.870 JLINK_IsHalted()
TA260 002:812.366 - 0.496ms returns FALSE
TA260 002:812.372 JLINK_HasError()
TA260 002:813.648 JLINK_IsHalted()
TA260 002:814.112 - 0.464ms returns FALSE
TA260 002:814.118 JLINK_HasError()
TA260 002:816.150 JLINK_IsHalted()
TA260 002:818.474   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:818.956 - 2.806ms returns TRUE
TA260 002:818.963 JLINK_ReadReg(R15 (PC))
TA260 002:818.967 - 0.004ms returns 0x20000000
TA260 002:818.972 JLINK_ClrBPEx(BPHandle = 0x0000002F)
TA260 002:818.976 - 0.003ms returns 0x00
TA260 002:818.980 JLINK_ReadReg(R0)
TA260 002:818.983 - 0.003ms returns 0x00000000
TA260 002:819.297 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:819.305   Data:  A9 28 DF BE 71 67 66 3F 8D DD DF BE 8E 3B 66 3F ...
TA260 002:819.313   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:821.944 - 2.646ms returns 0x27C
TA260 002:821.961 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:821.965   Data:  C7 39 57 3F B7 F1 0A BF 48 03 57 3F 1C 46 0B BF ...
TA260 002:821.975   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:823.892 - 1.930ms returns 0x184
TA260 002:823.908 JLINK_HasError()
TA260 002:823.937 JLINK_WriteReg(R0, 0x08009000)
TA260 002:823.943 - 0.006ms returns 0
TA260 002:823.947 JLINK_WriteReg(R1, 0x00000400)
TA260 002:823.951 - 0.003ms returns 0
TA260 002:823.955 JLINK_WriteReg(R2, 0x20000184)
TA260 002:823.959 - 0.003ms returns 0
TA260 002:823.963 JLINK_WriteReg(R3, 0x00000000)
TA260 002:823.966 - 0.003ms returns 0
TA260 002:823.970 JLINK_WriteReg(R4, 0x00000000)
TA260 002:823.974 - 0.003ms returns 0
TA260 002:823.978 JLINK_WriteReg(R5, 0x00000000)
TA260 002:823.981 - 0.003ms returns 0
TA260 002:823.985 JLINK_WriteReg(R6, 0x00000000)
TA260 002:823.988 - 0.003ms returns 0
TA260 002:823.992 JLINK_WriteReg(R7, 0x00000000)
TA260 002:823.996 - 0.003ms returns 0
TA260 002:824.000 JLINK_WriteReg(R8, 0x00000000)
TA260 002:824.003 - 0.003ms returns 0
TA260 002:824.007 JLINK_WriteReg(R9, 0x20000180)
TA260 002:824.010 - 0.003ms returns 0
TA260 002:824.014 JLINK_WriteReg(R10, 0x00000000)
TA260 002:824.018 - 0.003ms returns 0
TA260 002:824.022 JLINK_WriteReg(R11, 0x00000000)
TA260 002:824.026 - 0.003ms returns 0
TA260 002:824.030 JLINK_WriteReg(R12, 0x00000000)
TA260 002:824.033 - 0.003ms returns 0
TA260 002:824.037 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:824.040 - 0.003ms returns 0
TA260 002:824.044 JLINK_WriteReg(R14, 0x20000001)
TA260 002:824.048 - 0.003ms returns 0
TA260 002:824.052 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:824.055 - 0.003ms returns 0
TA260 002:824.060 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:824.063 - 0.003ms returns 0
TA260 002:824.067 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:824.070 - 0.003ms returns 0
TA260 002:824.074 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:824.078 - 0.003ms returns 0
TA260 002:824.086 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:824.091 - 0.005ms returns 0
TA260 002:824.095 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:824.099 - 0.004ms returns 0x00000030
TA260 002:824.104 JLINK_Go()
TA260 002:824.111   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:826.854 - 2.750ms 
TA260 002:826.860 JLINK_IsHalted()
TA260 002:827.334 - 0.474ms returns FALSE
TA260 002:827.343 JLINK_HasError()
TA260 002:828.662 JLINK_IsHalted()
TA260 002:829.162 - 0.499ms returns FALSE
TA260 002:829.169 JLINK_HasError()
TA260 002:830.660 JLINK_IsHalted()
TA260 002:831.130 - 0.469ms returns FALSE
TA260 002:831.141 JLINK_HasError()
TA260 002:832.658 JLINK_IsHalted()
TA260 002:835.057   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:835.628 - 2.970ms returns TRUE
TA260 002:835.635 JLINK_ReadReg(R15 (PC))
TA260 002:835.641 - 0.005ms returns 0x20000000
TA260 002:835.646 JLINK_ClrBPEx(BPHandle = 0x00000030)
TA260 002:835.649 - 0.003ms returns 0x00
TA260 002:835.654 JLINK_ReadReg(R0)
TA260 002:835.657 - 0.003ms returns 0x00000000
TA260 002:836.009 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:836.016   Data:  93 62 1A BF 78 35 4C 3F B8 B2 1A BF C7 F8 4B 3F ...
TA260 002:836.027   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:838.629 - 2.619ms returns 0x27C
TA260 002:838.637 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:838.641   Data:  43 0C 38 3F AA 38 32 BF 55 C6 37 3F C7 80 32 BF ...
TA260 002:838.649   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:840.528 - 1.890ms returns 0x184
TA260 002:840.541 JLINK_HasError()
TA260 002:840.546 JLINK_WriteReg(R0, 0x08009400)
TA260 002:840.552 - 0.005ms returns 0
TA260 002:840.556 JLINK_WriteReg(R1, 0x00000400)
TA260 002:840.559 - 0.003ms returns 0
TA260 002:840.563 JLINK_WriteReg(R2, 0x20000184)
TA260 002:840.567 - 0.003ms returns 0
TA260 002:840.571 JLINK_WriteReg(R3, 0x00000000)
TA260 002:840.575 - 0.003ms returns 0
TA260 002:840.579 JLINK_WriteReg(R4, 0x00000000)
TA260 002:840.582 - 0.003ms returns 0
TA260 002:840.586 JLINK_WriteReg(R5, 0x00000000)
TA260 002:840.590 - 0.003ms returns 0
TA260 002:840.594 JLINK_WriteReg(R6, 0x00000000)
TA260 002:840.597 - 0.003ms returns 0
TA260 002:840.601 JLINK_WriteReg(R7, 0x00000000)
TA260 002:840.604 - 0.003ms returns 0
TA260 002:840.608 JLINK_WriteReg(R8, 0x00000000)
TA260 002:840.612 - 0.003ms returns 0
TA260 002:840.616 JLINK_WriteReg(R9, 0x20000180)
TA260 002:840.620 - 0.003ms returns 0
TA260 002:840.624 JLINK_WriteReg(R10, 0x00000000)
TA260 002:840.627 - 0.003ms returns 0
TA260 002:840.631 JLINK_WriteReg(R11, 0x00000000)
TA260 002:840.634 - 0.003ms returns 0
TA260 002:840.638 JLINK_WriteReg(R12, 0x00000000)
TA260 002:840.642 - 0.003ms returns 0
TA260 002:840.646 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:840.650 - 0.003ms returns 0
TA260 002:840.654 JLINK_WriteReg(R14, 0x20000001)
TA260 002:840.657 - 0.003ms returns 0
TA260 002:840.661 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:840.664 - 0.003ms returns 0
TA260 002:840.668 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:840.672 - 0.003ms returns 0
TA260 002:840.676 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:840.679 - 0.003ms returns 0
TA260 002:840.684 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:840.687 - 0.003ms returns 0
TA260 002:840.691 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:840.694 - 0.003ms returns 0
TA260 002:840.699 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:840.703 - 0.004ms returns 0x00000031
TA260 002:840.707 JLINK_Go()
TA260 002:840.715   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:843.618 - 2.910ms 
TA260 002:843.632 JLINK_IsHalted()
TA260 002:844.123 - 0.491ms returns FALSE
TA260 002:844.129 JLINK_HasError()
TA260 002:846.166 JLINK_IsHalted()
TA260 002:846.625 - 0.458ms returns FALSE
TA260 002:846.631 JLINK_HasError()
TA260 002:847.668 JLINK_IsHalted()
TA260 002:849.976   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:850.487 - 2.818ms returns TRUE
TA260 002:850.502 JLINK_ReadReg(R15 (PC))
TA260 002:850.508 - 0.005ms returns 0x20000000
TA260 002:850.515 JLINK_ClrBPEx(BPHandle = 0x00000031)
TA260 002:850.520 - 0.005ms returns 0x00
TA260 002:850.525 JLINK_ReadReg(R0)
TA260 002:850.528 - 0.003ms returns 0x00000000
TA260 002:850.848 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:850.855   Data:  FC 41 3F BF 80 2A 2A 3F C0 84 3F BF 57 DF 29 3F ...
TA260 002:850.864   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:853.629 - 2.780ms returns 0x27C
TA260 002:853.643 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:853.647   Data:  19 CC 11 3F 49 A6 52 BF 6B 79 11 3F 59 DF 52 BF ...
TA260 002:853.656   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:855.523 - 1.879ms returns 0x184
TA260 002:855.532 JLINK_HasError()
TA260 002:855.561 JLINK_WriteReg(R0, 0x08009800)
TA260 002:855.566 - 0.005ms returns 0
TA260 002:855.571 JLINK_WriteReg(R1, 0x00000400)
TA260 002:855.574 - 0.003ms returns 0
TA260 002:855.578 JLINK_WriteReg(R2, 0x20000184)
TA260 002:855.582 - 0.003ms returns 0
TA260 002:855.586 JLINK_WriteReg(R3, 0x00000000)
TA260 002:855.589 - 0.003ms returns 0
TA260 002:855.593 JLINK_WriteReg(R4, 0x00000000)
TA260 002:855.596 - 0.003ms returns 0
TA260 002:855.600 JLINK_WriteReg(R5, 0x00000000)
TA260 002:855.604 - 0.003ms returns 0
TA260 002:855.608 JLINK_WriteReg(R6, 0x00000000)
TA260 002:855.611 - 0.003ms returns 0
TA260 002:855.615 JLINK_WriteReg(R7, 0x00000000)
TA260 002:855.619 - 0.003ms returns 0
TA260 002:855.623 JLINK_WriteReg(R8, 0x00000000)
TA260 002:855.626 - 0.003ms returns 0
TA260 002:855.630 JLINK_WriteReg(R9, 0x20000180)
TA260 002:855.634 - 0.003ms returns 0
TA260 002:855.638 JLINK_WriteReg(R10, 0x00000000)
TA260 002:855.641 - 0.003ms returns 0
TA260 002:855.645 JLINK_WriteReg(R11, 0x00000000)
TA260 002:855.648 - 0.003ms returns 0
TA260 002:855.653 JLINK_WriteReg(R12, 0x00000000)
TA260 002:855.656 - 0.003ms returns 0
TA260 002:855.660 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:855.664 - 0.003ms returns 0
TA260 002:855.668 JLINK_WriteReg(R14, 0x20000001)
TA260 002:855.671 - 0.003ms returns 0
TA260 002:855.675 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:855.679 - 0.003ms returns 0
TA260 002:855.683 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:855.686 - 0.003ms returns 0
TA260 002:855.690 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:855.694 - 0.003ms returns 0
TA260 002:855.698 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:855.701 - 0.003ms returns 0
TA260 002:855.705 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:855.709 - 0.003ms returns 0
TA260 002:855.713 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:855.717 - 0.004ms returns 0x00000032
TA260 002:855.721 JLINK_Go()
TA260 002:855.728   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:858.493 - 2.771ms 
TA260 002:858.503 JLINK_IsHalted()
TA260 002:858.959 - 0.455ms returns FALSE
TA260 002:858.964 JLINK_HasError()
TA260 002:860.177 JLINK_IsHalted()
TA260 002:860.662 - 0.484ms returns FALSE
TA260 002:860.711 JLINK_HasError()
TA260 002:862.179 JLINK_IsHalted()
TA260 002:862.619 - 0.439ms returns FALSE
TA260 002:862.625 JLINK_HasError()
TA260 002:864.174 JLINK_IsHalted()
TA260 002:866.639   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:867.132 - 2.957ms returns TRUE
TA260 002:867.146 JLINK_ReadReg(R15 (PC))
TA260 002:867.167 - 0.020ms returns 0x20000000
TA260 002:867.185 JLINK_ClrBPEx(BPHandle = 0x00000032)
TA260 002:867.190 - 0.004ms returns 0x00
TA260 002:867.195 JLINK_ReadReg(R0)
TA260 002:867.198 - 0.003ms returns 0x00000000
TA260 002:867.998 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:868.010   Data:  D1 C7 5C BF 73 95 01 3F A3 FA 5C BF B5 3E 01 3F ...
TA260 002:868.021   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:870.637 - 2.638ms returns 0x27C
TA260 002:870.654 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:870.658   Data:  2C E3 CB 3E 8C FB 6A BF AE 2A CB 3E 5E 23 6B BF ...
TA260 002:870.670   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:872.533 - 1.878ms returns 0x184
TA260 002:872.543 JLINK_HasError()
TA260 002:872.549 JLINK_WriteReg(R0, 0x08009C00)
TA260 002:872.555 - 0.006ms returns 0
TA260 002:872.565 JLINK_WriteReg(R1, 0x00000400)
TA260 002:872.568 - 0.003ms returns 0
TA260 002:872.573 JLINK_WriteReg(R2, 0x20000184)
TA260 002:872.577 - 0.004ms returns 0
TA260 002:872.581 JLINK_WriteReg(R3, 0x00000000)
TA260 002:872.584 - 0.003ms returns 0
TA260 002:872.588 JLINK_WriteReg(R4, 0x00000000)
TA260 002:872.591 - 0.003ms returns 0
TA260 002:872.595 JLINK_WriteReg(R5, 0x00000000)
TA260 002:872.599 - 0.003ms returns 0
TA260 002:872.603 JLINK_WriteReg(R6, 0x00000000)
TA260 002:872.606 - 0.003ms returns 0
TA260 002:872.610 JLINK_WriteReg(R7, 0x00000000)
TA260 002:872.614 - 0.003ms returns 0
TA260 002:872.618 JLINK_WriteReg(R8, 0x00000000)
TA260 002:872.621 - 0.003ms returns 0
TA260 002:872.625 JLINK_WriteReg(R9, 0x20000180)
TA260 002:872.628 - 0.003ms returns 0
TA260 002:872.632 JLINK_WriteReg(R10, 0x00000000)
TA260 002:872.636 - 0.003ms returns 0
TA260 002:872.640 JLINK_WriteReg(R11, 0x00000000)
TA260 002:872.643 - 0.003ms returns 0
TA260 002:872.647 JLINK_WriteReg(R12, 0x00000000)
TA260 002:872.651 - 0.003ms returns 0
TA260 002:872.655 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:872.659 - 0.004ms returns 0
TA260 002:872.663 JLINK_WriteReg(R14, 0x20000001)
TA260 002:872.666 - 0.003ms returns 0
TA260 002:872.670 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:872.674 - 0.003ms returns 0
TA260 002:872.678 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:872.681 - 0.003ms returns 0
TA260 002:872.685 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:872.689 - 0.003ms returns 0
TA260 002:872.693 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:872.696 - 0.003ms returns 0
TA260 002:872.700 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:872.704 - 0.003ms returns 0
TA260 002:872.708 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:872.719 - 0.010ms returns 0x00000033
TA260 002:872.723 JLINK_Go()
TA260 002:872.731   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:875.536 - 2.812ms 
TA260 002:875.544 JLINK_IsHalted()
TA260 002:876.016 - 0.471ms returns FALSE
TA260 002:876.022 JLINK_HasError()
TA260 002:877.688 JLINK_IsHalted()
TA260 002:878.162 - 0.473ms returns FALSE
TA260 002:878.168 JLINK_HasError()
TA260 002:879.692 JLINK_IsHalted()
TA260 002:882.056   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:882.537 - 2.844ms returns TRUE
TA260 002:882.548 JLINK_ReadReg(R15 (PC))
TA260 002:882.553 - 0.004ms returns 0x20000000
TA260 002:882.580 JLINK_ClrBPEx(BPHandle = 0x00000033)
TA260 002:882.586 - 0.005ms returns 0x00
TA260 002:882.590 JLINK_ReadReg(R0)
TA260 002:882.594 - 0.003ms returns 0x00000000
TA260 002:882.940 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:882.947   Data:  9F D1 71 BF 1F 0B A8 3E 8C F2 71 BF 25 4D A7 3E ...
TA260 002:882.956   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:885.625 - 2.685ms returns 0x27C
TA260 002:885.631 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:885.634   Data:  A4 B0 58 3E 10 49 7A BF 8F 27 57 3E 1C 5E 7A BF ...
TA260 002:885.641   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:887.496 - 1.864ms returns 0x184
TA260 002:887.507 JLINK_HasError()
TA260 002:887.513 JLINK_WriteReg(R0, 0x0800A000)
TA260 002:887.519 - 0.005ms returns 0
TA260 002:887.524 JLINK_WriteReg(R1, 0x00000400)
TA260 002:887.529 - 0.004ms returns 0
TA260 002:887.534 JLINK_WriteReg(R2, 0x20000184)
TA260 002:887.538 - 0.004ms returns 0
TA260 002:887.543 JLINK_WriteReg(R3, 0x00000000)
TA260 002:887.548 - 0.004ms returns 0
TA260 002:887.553 JLINK_WriteReg(R4, 0x00000000)
TA260 002:887.557 - 0.004ms returns 0
TA260 002:887.562 JLINK_WriteReg(R5, 0x00000000)
TA260 002:887.574 - 0.011ms returns 0
TA260 002:887.579 JLINK_WriteReg(R6, 0x00000000)
TA260 002:887.584 - 0.004ms returns 0
TA260 002:887.589 JLINK_WriteReg(R7, 0x00000000)
TA260 002:887.593 - 0.004ms returns 0
TA260 002:887.598 JLINK_WriteReg(R8, 0x00000000)
TA260 002:887.602 - 0.004ms returns 0
TA260 002:887.608 JLINK_WriteReg(R9, 0x20000180)
TA260 002:887.612 - 0.004ms returns 0
TA260 002:887.617 JLINK_WriteReg(R10, 0x00000000)
TA260 002:887.621 - 0.004ms returns 0
TA260 002:887.631 JLINK_WriteReg(R11, 0x00000000)
TA260 002:887.635 - 0.004ms returns 0
TA260 002:887.640 JLINK_WriteReg(R12, 0x00000000)
TA260 002:887.645 - 0.004ms returns 0
TA260 002:887.650 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:887.654 - 0.004ms returns 0
TA260 002:887.660 JLINK_WriteReg(R14, 0x20000001)
TA260 002:887.664 - 0.004ms returns 0
TA260 002:887.669 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:887.674 - 0.004ms returns 0
TA260 002:887.679 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:887.682 - 0.003ms returns 0
TA260 002:887.686 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:887.690 - 0.003ms returns 0
TA260 002:887.694 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:887.697 - 0.003ms returns 0
TA260 002:887.701 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:887.704 - 0.003ms returns 0
TA260 002:887.709 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:887.713 - 0.004ms returns 0x00000034
TA260 002:887.717 JLINK_Go()
TA260 002:887.724   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:890.618 - 2.899ms 
TA260 002:890.630 JLINK_IsHalted()
TA260 002:891.148 - 0.517ms returns FALSE
TA260 002:891.161 JLINK_HasError()
TA260 002:892.198 JLINK_IsHalted()
TA260 002:892.655 - 0.456ms returns FALSE
TA260 002:892.664 JLINK_HasError()
TA260 002:894.195 JLINK_IsHalted()
TA260 002:894.706 - 0.510ms returns FALSE
TA260 002:894.711 JLINK_HasError()
TA260 002:896.200 JLINK_IsHalted()
TA260 002:898.634   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:899.138 - 2.937ms returns TRUE
TA260 002:899.144 JLINK_ReadReg(R15 (PC))
TA260 002:899.148 - 0.004ms returns 0x20000000
TA260 002:899.153 JLINK_ClrBPEx(BPHandle = 0x00000034)
TA260 002:899.157 - 0.003ms returns 0x00
TA260 002:899.162 JLINK_ReadReg(R0)
TA260 002:899.165 - 0.004ms returns 0x00000000
TA260 002:899.504 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:899.511   Data:  6E 90 7D BF 4A EC 0C 3E 30 9E 7D BF F3 5D 0B 3E ...
TA260 002:899.522   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:902.137 - 2.633ms returns 0x27C
TA260 002:902.156 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:902.160   Data:  38 39 8A 3C 4A F8 7F BF 4B 51 7B 3C C1 F9 7F BF ...
TA260 002:902.171   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:903.980 - 1.823ms returns 0x184
TA260 002:903.987 JLINK_HasError()
TA260 002:903.992 JLINK_WriteReg(R0, 0x0800A400)
TA260 002:903.997 - 0.004ms returns 0
TA260 002:904.001 JLINK_WriteReg(R1, 0x00000400)
TA260 002:904.004 - 0.003ms returns 0
TA260 002:904.008 JLINK_WriteReg(R2, 0x20000184)
TA260 002:904.012 - 0.003ms returns 0
TA260 002:904.016 JLINK_WriteReg(R3, 0x00000000)
TA260 002:904.020 - 0.003ms returns 0
TA260 002:904.024 JLINK_WriteReg(R4, 0x00000000)
TA260 002:904.027 - 0.003ms returns 0
TA260 002:904.031 JLINK_WriteReg(R5, 0x00000000)
TA260 002:904.038 - 0.007ms returns 0
TA260 002:904.042 JLINK_WriteReg(R6, 0x00000000)
TA260 002:904.046 - 0.003ms returns 0
TA260 002:904.050 JLINK_WriteReg(R7, 0x00000000)
TA260 002:904.053 - 0.003ms returns 0
TA260 002:904.057 JLINK_WriteReg(R8, 0x00000000)
TA260 002:904.060 - 0.003ms returns 0
TA260 002:904.064 JLINK_WriteReg(R9, 0x20000180)
TA260 002:904.068 - 0.003ms returns 0
TA260 002:904.072 JLINK_WriteReg(R10, 0x00000000)
TA260 002:904.075 - 0.003ms returns 0
TA260 002:904.079 JLINK_WriteReg(R11, 0x00000000)
TA260 002:904.083 - 0.003ms returns 0
TA260 002:904.087 JLINK_WriteReg(R12, 0x00000000)
TA260 002:904.090 - 0.003ms returns 0
TA260 002:904.094 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:904.098 - 0.003ms returns 0
TA260 002:904.102 JLINK_WriteReg(R14, 0x20000001)
TA260 002:904.106 - 0.003ms returns 0
TA260 002:904.110 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:904.113 - 0.003ms returns 0
TA260 002:904.117 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:904.120 - 0.003ms returns 0
TA260 002:904.124 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:904.128 - 0.003ms returns 0
TA260 002:904.132 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:904.135 - 0.003ms returns 0
TA260 002:904.139 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:904.146 - 0.006ms returns 0
TA260 002:904.153 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:904.157 - 0.004ms returns 0x00000035
TA260 002:904.161 JLINK_Go()
TA260 002:904.169   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:906.887 - 2.725ms 
TA260 002:906.894 JLINK_IsHalted()
TA260 002:907.368 - 0.473ms returns FALSE
TA260 002:907.379 JLINK_HasError()
TA260 002:909.208 JLINK_IsHalted()
TA260 002:909.661 - 0.453ms returns FALSE
TA260 002:909.667 JLINK_HasError()
TA260 002:911.210 JLINK_IsHalted()
TA260 002:913.515   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:914.001 - 2.790ms returns TRUE
TA260 002:914.008 JLINK_ReadReg(R15 (PC))
TA260 002:914.012 - 0.004ms returns 0x20000000
TA260 002:914.017 JLINK_ClrBPEx(BPHandle = 0x00000035)
TA260 002:914.021 - 0.003ms returns 0x00
TA260 002:914.025 JLINK_ReadReg(R0)
TA260 002:914.029 - 0.003ms returns 0x00000000
TA260 002:914.374 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:914.381   Data:  B1 90 7F BF 38 A0 6E BD C2 8A 7F BF E9 E5 74 BD ...
TA260 002:914.391   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:916.947 - 2.573ms returns 0x27C
TA260 002:916.954 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:916.958   Data:  50 CC 36 BE 4D D1 7B BF EC 57 38 BE 20 BF 7B BF ...
TA260 002:916.965   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:918.795 - 1.841ms returns 0x184
TA260 002:918.802 JLINK_HasError()
TA260 002:918.807 JLINK_WriteReg(R0, 0x0800A800)
TA260 002:918.812 - 0.004ms returns 0
TA260 002:918.816 JLINK_WriteReg(R1, 0x00000400)
TA260 002:918.820 - 0.003ms returns 0
TA260 002:918.824 JLINK_WriteReg(R2, 0x20000184)
TA260 002:918.827 - 0.003ms returns 0
TA260 002:918.831 JLINK_WriteReg(R3, 0x00000000)
TA260 002:918.835 - 0.003ms returns 0
TA260 002:918.839 JLINK_WriteReg(R4, 0x00000000)
TA260 002:918.842 - 0.003ms returns 0
TA260 002:918.846 JLINK_WriteReg(R5, 0x00000000)
TA260 002:918.850 - 0.003ms returns 0
TA260 002:918.854 JLINK_WriteReg(R6, 0x00000000)
TA260 002:918.857 - 0.003ms returns 0
TA260 002:918.861 JLINK_WriteReg(R7, 0x00000000)
TA260 002:918.864 - 0.003ms returns 0
TA260 002:918.868 JLINK_WriteReg(R8, 0x00000000)
TA260 002:918.872 - 0.003ms returns 0
TA260 002:918.876 JLINK_WriteReg(R9, 0x20000180)
TA260 002:918.879 - 0.003ms returns 0
TA260 002:918.883 JLINK_WriteReg(R10, 0x00000000)
TA260 002:918.887 - 0.003ms returns 0
TA260 002:918.891 JLINK_WriteReg(R11, 0x00000000)
TA260 002:918.894 - 0.003ms returns 0
TA260 002:918.898 JLINK_WriteReg(R12, 0x00000000)
TA260 002:918.902 - 0.003ms returns 0
TA260 002:918.906 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:918.909 - 0.003ms returns 0
TA260 002:918.913 JLINK_WriteReg(R14, 0x20000001)
TA260 002:918.917 - 0.003ms returns 0
TA260 002:918.921 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:918.924 - 0.003ms returns 0
TA260 002:918.928 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:918.932 - 0.003ms returns 0
TA260 002:918.936 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:918.939 - 0.003ms returns 0
TA260 002:918.943 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:918.947 - 0.003ms returns 0
TA260 002:918.951 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:918.954 - 0.003ms returns 0
TA260 002:918.958 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:918.962 - 0.004ms returns 0x00000036
TA260 002:918.966 JLINK_Go()
TA260 002:918.974   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:921.849 - 2.882ms 
TA260 002:921.864 JLINK_IsHalted()
TA260 002:922.369 - 0.504ms returns FALSE
TA260 002:922.376 JLINK_HasError()
TA260 002:923.716 JLINK_IsHalted()
TA260 002:924.177 - 0.461ms returns FALSE
TA260 002:924.184 JLINK_HasError()
TA260 002:926.217 JLINK_IsHalted()
TA260 002:928.646   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:929.141 - 2.923ms returns TRUE
TA260 002:929.148 JLINK_ReadReg(R15 (PC))
TA260 002:929.154 - 0.005ms returns 0x20000000
TA260 002:929.158 JLINK_ClrBPEx(BPHandle = 0x00000036)
TA260 002:929.162 - 0.003ms returns 0x00
TA260 002:929.166 JLINK_ReadReg(R0)
TA260 002:929.170 - 0.003ms returns 0x00000000
TA260 002:929.552 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:929.559   Data:  B7 BE 77 BF C0 F8 80 BE 51 A5 77 BF 4A BB 81 BE ...
TA260 002:929.570   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:932.220 - 2.668ms returns 0x27C
TA260 002:932.277 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:932.282   Data:  B6 EC BB BE F2 FC 6D BF AF A7 BC BE D5 D7 6D BF ...
TA260 002:932.303   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:934.128 - 1.851ms returns 0x184
TA260 002:934.138 JLINK_HasError()
TA260 002:934.144 JLINK_WriteReg(R0, 0x0800AC00)
TA260 002:934.149 - 0.005ms returns 0
TA260 002:934.153 JLINK_WriteReg(R1, 0x00000400)
TA260 002:934.157 - 0.003ms returns 0
TA260 002:934.161 JLINK_WriteReg(R2, 0x20000184)
TA260 002:934.165 - 0.003ms returns 0
TA260 002:934.169 JLINK_WriteReg(R3, 0x00000000)
TA260 002:934.172 - 0.003ms returns 0
TA260 002:934.176 JLINK_WriteReg(R4, 0x00000000)
TA260 002:934.180 - 0.003ms returns 0
TA260 002:934.184 JLINK_WriteReg(R5, 0x00000000)
TA260 002:934.187 - 0.003ms returns 0
TA260 002:934.191 JLINK_WriteReg(R6, 0x00000000)
TA260 002:934.195 - 0.003ms returns 0
TA260 002:934.199 JLINK_WriteReg(R7, 0x00000000)
TA260 002:934.202 - 0.003ms returns 0
TA260 002:934.206 JLINK_WriteReg(R8, 0x00000000)
TA260 002:934.210 - 0.003ms returns 0
TA260 002:934.214 JLINK_WriteReg(R9, 0x20000180)
TA260 002:934.217 - 0.003ms returns 0
TA260 002:934.221 JLINK_WriteReg(R10, 0x00000000)
TA260 002:934.224 - 0.003ms returns 0
TA260 002:934.228 JLINK_WriteReg(R11, 0x00000000)
TA260 002:934.232 - 0.003ms returns 0
TA260 002:934.236 JLINK_WriteReg(R12, 0x00000000)
TA260 002:934.239 - 0.003ms returns 0
TA260 002:934.243 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:934.247 - 0.003ms returns 0
TA260 002:934.251 JLINK_WriteReg(R14, 0x20000001)
TA260 002:934.255 - 0.003ms returns 0
TA260 002:934.259 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:934.263 - 0.004ms returns 0
TA260 002:934.267 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:934.270 - 0.003ms returns 0
TA260 002:934.275 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:934.278 - 0.003ms returns 0
TA260 002:934.282 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:934.286 - 0.003ms returns 0
TA260 002:934.290 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:934.293 - 0.003ms returns 0
TA260 002:934.298 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:934.302 - 0.004ms returns 0x00000037
TA260 002:934.306 JLINK_Go()
TA260 002:934.314   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:937.048 - 2.741ms 
TA260 002:937.055 JLINK_IsHalted()
TA260 002:937.527 - 0.471ms returns FALSE
TA260 002:937.534 JLINK_HasError()
TA260 002:938.874 JLINK_IsHalted()
TA260 002:939.390 - 0.515ms returns FALSE
TA260 002:939.397 JLINK_HasError()
TA260 002:940.874 JLINK_IsHalted()
TA260 002:941.368 - 0.493ms returns FALSE
TA260 002:941.378 JLINK_HasError()
TA260 002:942.875 JLINK_IsHalted()
TA260 002:945.195   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:945.766 - 2.891ms returns TRUE
TA260 002:945.773 JLINK_ReadReg(R15 (PC))
TA260 002:945.778 - 0.005ms returns 0x20000000
TA260 002:945.783 JLINK_ClrBPEx(BPHandle = 0x00000037)
TA260 002:945.786 - 0.003ms returns 0x00
TA260 002:945.791 JLINK_ReadReg(R0)
TA260 002:945.794 - 0.003ms returns 0x00000000
TA260 002:946.157 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:946.164   Data:  71 67 66 BF A9 28 DF BE 8E 3B 66 BF 8D DD DF BE ...
TA260 002:946.174   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:948.707 - 2.549ms returns 0x27C
TA260 002:948.720 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:948.725   Data:  3D 9D 0A BF 48 03 57 BF B7 F1 0A BF A7 CC 56 BF ...
TA260 002:948.732   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:950.622 - 1.901ms returns 0x184
TA260 002:950.632 JLINK_HasError()
TA260 002:950.681 JLINK_WriteReg(R0, 0x0800B000)
TA260 002:950.687 - 0.006ms returns 0
TA260 002:950.692 JLINK_WriteReg(R1, 0x00000400)
TA260 002:950.696 - 0.003ms returns 0
TA260 002:950.700 JLINK_WriteReg(R2, 0x20000184)
TA260 002:950.707 - 0.006ms returns 0
TA260 002:950.711 JLINK_WriteReg(R3, 0x00000000)
TA260 002:950.714 - 0.003ms returns 0
TA260 002:950.718 JLINK_WriteReg(R4, 0x00000000)
TA260 002:950.722 - 0.003ms returns 0
TA260 002:950.726 JLINK_WriteReg(R5, 0x00000000)
TA260 002:950.729 - 0.003ms returns 0
TA260 002:950.733 JLINK_WriteReg(R6, 0x00000000)
TA260 002:950.737 - 0.003ms returns 0
TA260 002:950.741 JLINK_WriteReg(R7, 0x00000000)
TA260 002:950.744 - 0.003ms returns 0
TA260 002:950.748 JLINK_WriteReg(R8, 0x00000000)
TA260 002:950.752 - 0.003ms returns 0
TA260 002:950.756 JLINK_WriteReg(R9, 0x20000180)
TA260 002:950.759 - 0.003ms returns 0
TA260 002:950.763 JLINK_WriteReg(R10, 0x00000000)
TA260 002:950.767 - 0.003ms returns 0
TA260 002:950.771 JLINK_WriteReg(R11, 0x00000000)
TA260 002:950.774 - 0.003ms returns 0
TA260 002:950.782 JLINK_WriteReg(R12, 0x00000000)
TA260 002:950.786 - 0.003ms returns 0
TA260 002:950.790 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:950.794 - 0.004ms returns 0
TA260 002:950.798 JLINK_WriteReg(R14, 0x20000001)
TA260 002:950.802 - 0.003ms returns 0
TA260 002:950.806 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:950.809 - 0.003ms returns 0
TA260 002:950.813 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:950.817 - 0.003ms returns 0
TA260 002:950.821 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:950.824 - 0.003ms returns 0
TA260 002:950.828 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:950.831 - 0.003ms returns 0
TA260 002:950.835 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:950.839 - 0.003ms returns 0
TA260 002:950.843 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:950.847 - 0.004ms returns 0x00000038
TA260 002:950.852 JLINK_Go()
TA260 002:950.859   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:953.625 - 2.773ms 
TA260 002:953.637 JLINK_IsHalted()
TA260 002:954.159 - 0.521ms returns FALSE
TA260 002:954.165 JLINK_HasError()
TA260 002:956.381 JLINK_IsHalted()
TA260 002:956.909 - 0.527ms returns FALSE
TA260 002:956.922 JLINK_HasError()
TA260 002:958.884 JLINK_IsHalted()
TA260 002:961.249   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:961.766 - 2.882ms returns TRUE
TA260 002:961.774 JLINK_ReadReg(R15 (PC))
TA260 002:961.778 - 0.004ms returns 0x20000000
TA260 002:961.783 JLINK_ClrBPEx(BPHandle = 0x00000038)
TA260 002:961.787 - 0.003ms returns 0x00
TA260 002:961.791 JLINK_ReadReg(R0)
TA260 002:961.795 - 0.003ms returns 0x00000000
TA260 002:962.122 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:962.129   Data:  78 35 4C BF 93 62 1A BF C7 F8 4B BF B8 B2 1A BF ...
TA260 002:962.138   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:964.709 - 2.587ms returns 0x27C
TA260 002:964.717 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:964.721   Data:  71 F0 31 BF 55 C6 37 BF AA 38 32 BF 4A 80 37 BF ...
TA260 002:964.730   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:966.629 - 1.912ms returns 0x184
TA260 002:966.638 JLINK_HasError()
TA260 002:966.643 JLINK_WriteReg(R0, 0x0800B400)
TA260 002:966.647 - 0.004ms returns 0
TA260 002:966.652 JLINK_WriteReg(R1, 0x00000400)
TA260 002:966.655 - 0.003ms returns 0
TA260 002:966.660 JLINK_WriteReg(R2, 0x20000184)
TA260 002:966.664 - 0.003ms returns 0
TA260 002:966.668 JLINK_WriteReg(R3, 0x00000000)
TA260 002:966.671 - 0.003ms returns 0
TA260 002:966.675 JLINK_WriteReg(R4, 0x00000000)
TA260 002:966.679 - 0.003ms returns 0
TA260 002:966.683 JLINK_WriteReg(R5, 0x00000000)
TA260 002:966.686 - 0.003ms returns 0
TA260 002:966.690 JLINK_WriteReg(R6, 0x00000000)
TA260 002:966.693 - 0.003ms returns 0
TA260 002:966.697 JLINK_WriteReg(R7, 0x00000000)
TA260 002:966.701 - 0.003ms returns 0
TA260 002:966.705 JLINK_WriteReg(R8, 0x00000000)
TA260 002:966.708 - 0.003ms returns 0
TA260 002:966.712 JLINK_WriteReg(R9, 0x20000180)
TA260 002:966.716 - 0.003ms returns 0
TA260 002:966.720 JLINK_WriteReg(R10, 0x00000000)
TA260 002:966.723 - 0.003ms returns 0
TA260 002:966.727 JLINK_WriteReg(R11, 0x00000000)
TA260 002:966.730 - 0.003ms returns 0
TA260 002:966.734 JLINK_WriteReg(R12, 0x00000000)
TA260 002:966.740 - 0.006ms returns 0
TA260 002:966.745 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:966.749 - 0.004ms returns 0
TA260 002:966.753 JLINK_WriteReg(R14, 0x20000001)
TA260 002:966.756 - 0.003ms returns 0
TA260 002:966.760 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:966.764 - 0.003ms returns 0
TA260 002:966.768 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:966.771 - 0.003ms returns 0
TA260 002:966.775 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:966.779 - 0.003ms returns 0
TA260 002:966.783 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:966.786 - 0.003ms returns 0
TA260 002:966.790 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:966.794 - 0.003ms returns 0
TA260 002:966.801 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:966.805 - 0.004ms returns 0x00000039
TA260 002:966.809 JLINK_Go()
TA260 002:966.817   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:969.536 - 2.726ms 
TA260 002:969.543 JLINK_IsHalted()
TA260 002:970.099 - 0.555ms returns FALSE
TA260 002:970.108 JLINK_HasError()
TA260 002:971.892 JLINK_IsHalted()
TA260 002:972.347 - 0.454ms returns FALSE
TA260 002:972.354 JLINK_HasError()
TA260 002:973.892 JLINK_IsHalted()
TA260 002:976.241   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:976.752 - 2.860ms returns TRUE
TA260 002:976.759 JLINK_ReadReg(R15 (PC))
TA260 002:976.764 - 0.004ms returns 0x20000000
TA260 002:976.768 JLINK_ClrBPEx(BPHandle = 0x00000039)
TA260 002:976.772 - 0.003ms returns 0x00
TA260 002:976.776 JLINK_ReadReg(R0)
TA260 002:976.780 - 0.003ms returns 0x00000000
TA260 002:977.110 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:977.118   Data:  80 2A 2A BF FC 41 3F BF 57 DF 29 BF C0 84 3F BF ...
TA260 002:977.128   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:979.908 - 2.636ms returns 0x27C
TA260 002:979.916 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:979.920   Data:  18 6D 52 BF 6B 79 11 BF 49 A6 52 BF A7 26 11 BF ...
TA260 002:979.928   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:981.778 - 1.861ms returns 0x184
TA260 002:981.792 JLINK_HasError()
TA260 002:981.797 JLINK_WriteReg(R0, 0x0800B800)
TA260 002:981.803 - 0.005ms returns 0
TA260 002:981.807 JLINK_WriteReg(R1, 0x00000400)
TA260 002:981.811 - 0.003ms returns 0
TA260 002:981.815 JLINK_WriteReg(R2, 0x20000184)
TA260 002:981.818 - 0.003ms returns 0
TA260 002:981.822 JLINK_WriteReg(R3, 0x00000000)
TA260 002:981.826 - 0.003ms returns 0
TA260 002:981.830 JLINK_WriteReg(R4, 0x00000000)
TA260 002:981.834 - 0.003ms returns 0
TA260 002:981.838 JLINK_WriteReg(R5, 0x00000000)
TA260 002:981.841 - 0.003ms returns 0
TA260 002:981.845 JLINK_WriteReg(R6, 0x00000000)
TA260 002:981.848 - 0.003ms returns 0
TA260 002:981.852 JLINK_WriteReg(R7, 0x00000000)
TA260 002:981.856 - 0.003ms returns 0
TA260 002:981.860 JLINK_WriteReg(R8, 0x00000000)
TA260 002:981.863 - 0.003ms returns 0
TA260 002:981.867 JLINK_WriteReg(R9, 0x20000180)
TA260 002:981.871 - 0.003ms returns 0
TA260 002:981.875 JLINK_WriteReg(R10, 0x00000000)
TA260 002:981.878 - 0.003ms returns 0
TA260 002:981.882 JLINK_WriteReg(R11, 0x00000000)
TA260 002:981.886 - 0.003ms returns 0
TA260 002:981.890 JLINK_WriteReg(R12, 0x00000000)
TA260 002:981.893 - 0.003ms returns 0
TA260 002:981.897 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:981.901 - 0.003ms returns 0
TA260 002:981.932 JLINK_WriteReg(R14, 0x20000001)
TA260 002:981.935 - 0.003ms returns 0
TA260 002:981.940 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:981.943 - 0.003ms returns 0
TA260 002:981.947 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:981.951 - 0.003ms returns 0
TA260 002:981.955 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:981.958 - 0.003ms returns 0
TA260 002:981.962 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:981.966 - 0.003ms returns 0
TA260 002:981.970 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:981.973 - 0.003ms returns 0
TA260 002:981.978 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:981.982 - 0.004ms returns 0x0000003A
TA260 002:981.986 JLINK_Go()
TA260 002:981.994   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:984.743 - 2.756ms 
TA260 002:984.760 JLINK_IsHalted()
TA260 002:985.218 - 0.458ms returns FALSE
TA260 002:985.226 JLINK_HasError()
TA260 002:986.398 JLINK_IsHalted()
TA260 002:986.888 - 0.490ms returns FALSE
TA260 002:986.894 JLINK_HasError()
TA260 002:988.901 JLINK_IsHalted()
TA260 002:991.251   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:991.769 - 2.867ms returns TRUE
TA260 002:991.776 JLINK_ReadReg(R15 (PC))
TA260 002:991.781 - 0.004ms returns 0x20000000
TA260 002:991.785 JLINK_ClrBPEx(BPHandle = 0x0000003A)
TA260 002:991.789 - 0.004ms returns 0x00
TA260 002:991.794 JLINK_ReadReg(R0)
TA260 002:991.797 - 0.003ms returns 0x00000000
TA260 002:992.121 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:992.128   Data:  73 95 01 BF D1 C7 5C BF B5 3E 01 BF A3 FA 5C BF ...
TA260 002:992.137   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:994.751 - 2.630ms returns 0x27C
TA260 002:994.758 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:994.762   Data:  95 D3 6A BF AE 2A CB BE 8C FB 6A BF 10 72 CA BE ...
TA260 002:994.769   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:996.625 - 1.866ms returns 0x184
TA260 002:996.671 JLINK_HasError()
TA260 002:996.676 JLINK_WriteReg(R0, 0x0800BC00)
TA260 002:996.681 - 0.004ms returns 0
TA260 002:996.685 JLINK_WriteReg(R1, 0x00000400)
TA260 002:996.689 - 0.003ms returns 0
TA260 002:996.693 JLINK_WriteReg(R2, 0x20000184)
TA260 002:996.697 - 0.004ms returns 0
TA260 002:996.701 JLINK_WriteReg(R3, 0x00000000)
TA260 002:996.705 - 0.003ms returns 0
TA260 002:996.709 JLINK_WriteReg(R4, 0x00000000)
TA260 002:996.713 - 0.003ms returns 0
TA260 002:996.717 JLINK_WriteReg(R5, 0x00000000)
TA260 002:996.720 - 0.003ms returns 0
TA260 002:996.724 JLINK_WriteReg(R6, 0x00000000)
TA260 002:996.728 - 0.003ms returns 0
TA260 002:996.732 JLINK_WriteReg(R7, 0x00000000)
TA260 002:996.735 - 0.003ms returns 0
TA260 002:996.739 JLINK_WriteReg(R8, 0x00000000)
TA260 002:996.742 - 0.003ms returns 0
TA260 002:996.750 JLINK_WriteReg(R9, 0x20000180)
TA260 002:996.754 - 0.003ms returns 0
TA260 002:996.758 JLINK_WriteReg(R10, 0x00000000)
TA260 002:996.761 - 0.003ms returns 0
TA260 002:996.765 JLINK_WriteReg(R11, 0x00000000)
TA260 002:996.768 - 0.003ms returns 0
TA260 002:996.772 JLINK_WriteReg(R12, 0x00000000)
TA260 002:996.776 - 0.003ms returns 0
TA260 002:996.780 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:996.784 - 0.003ms returns 0
TA260 002:996.788 JLINK_WriteReg(R14, 0x20000001)
TA260 002:996.791 - 0.003ms returns 0
TA260 002:996.795 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:996.799 - 0.003ms returns 0
TA260 002:996.803 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:996.808 - 0.004ms returns 0
TA260 002:996.812 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:996.815 - 0.003ms returns 0
TA260 002:996.820 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:996.881 - 0.061ms returns 0
TA260 002:996.885 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:996.889 - 0.003ms returns 0
TA260 002:996.893 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:996.897 - 0.004ms returns 0x0000003B
TA260 002:996.901 JLINK_Go()
TA260 002:996.908   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:999.616 - 2.714ms 
TA260 002:999.623 JLINK_IsHalted()
TA260 003:000.142 - 0.518ms returns FALSE
TA260 003:000.153 JLINK_HasError()
TA260 003:001.411 JLINK_IsHalted()
TA260 003:001.850 - 0.437ms returns FALSE
TA260 003:001.863 JLINK_HasError()
TA260 003:003.409 JLINK_IsHalted()
TA260 003:003.889 - 0.479ms returns FALSE
TA260 003:003.895 JLINK_HasError()
TA260 003:005.911 JLINK_IsHalted()
TA260 003:008.563   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:009.048 - 3.137ms returns TRUE
TA260 003:009.055 JLINK_ReadReg(R15 (PC))
TA260 003:009.060 - 0.005ms returns 0x20000000
TA260 003:009.064 JLINK_ClrBPEx(BPHandle = 0x0000003B)
TA260 003:009.068 - 0.004ms returns 0x00
TA260 003:009.073 JLINK_ReadReg(R0)
TA260 003:009.076 - 0.003ms returns 0x00000000
TA260 003:009.414 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:009.421   Data:  1F 0B A8 BE 9F D1 71 BF 25 4D A7 BE 8C F2 71 BF ...
TA260 003:009.434   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:012.032 - 2.617ms returns 0x27C
TA260 003:012.049 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:012.054   Data:  DD 33 7A BF 8F 27 57 BE 10 49 7A BF 58 9E 55 BE ...
TA260 003:012.064   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:013.991 - 1.942ms returns 0x184
TA260 003:014.001 JLINK_HasError()
TA260 003:014.032 JLINK_WriteReg(R0, 0x0800C000)
TA260 003:014.038 - 0.005ms returns 0
TA260 003:014.042 JLINK_WriteReg(R1, 0x00000400)
TA260 003:014.046 - 0.003ms returns 0
TA260 003:014.050 JLINK_WriteReg(R2, 0x20000184)
TA260 003:014.054 - 0.004ms returns 0
TA260 003:014.059 JLINK_WriteReg(R3, 0x00000000)
TA260 003:014.063 - 0.003ms returns 0
TA260 003:014.067 JLINK_WriteReg(R4, 0x00000000)
TA260 003:014.070 - 0.003ms returns 0
TA260 003:014.074 JLINK_WriteReg(R5, 0x00000000)
TA260 003:014.078 - 0.003ms returns 0
TA260 003:014.082 JLINK_WriteReg(R6, 0x00000000)
TA260 003:014.086 - 0.003ms returns 0
TA260 003:014.090 JLINK_WriteReg(R7, 0x00000000)
TA260 003:014.093 - 0.003ms returns 0
TA260 003:014.097 JLINK_WriteReg(R8, 0x00000000)
TA260 003:014.100 - 0.003ms returns 0
TA260 003:014.104 JLINK_WriteReg(R9, 0x20000180)
TA260 003:014.108 - 0.003ms returns 0
TA260 003:014.112 JLINK_WriteReg(R10, 0x00000000)
TA260 003:014.151 - 0.039ms returns 0
TA260 003:014.155 JLINK_WriteReg(R11, 0x00000000)
TA260 003:014.159 - 0.003ms returns 0
TA260 003:014.163 JLINK_WriteReg(R12, 0x00000000)
TA260 003:014.166 - 0.003ms returns 0
TA260 003:014.170 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:014.174 - 0.003ms returns 0
TA260 003:014.178 JLINK_WriteReg(R14, 0x20000001)
TA260 003:014.182 - 0.003ms returns 0
TA260 003:014.186 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:014.189 - 0.003ms returns 0
TA260 003:014.194 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:014.197 - 0.003ms returns 0
TA260 003:014.201 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:014.204 - 0.003ms returns 0
TA260 003:014.208 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:014.212 - 0.003ms returns 0
TA260 003:014.216 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:014.219 - 0.003ms returns 0
TA260 003:014.224 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:014.228 - 0.004ms returns 0x0000003C
TA260 003:014.232 JLINK_Go()
TA260 003:014.240   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:016.954 - 2.721ms 
TA260 003:016.963 JLINK_IsHalted()
TA260 003:017.493 - 0.529ms returns FALSE
TA260 003:017.500 JLINK_HasError()
TA260 003:019.422 JLINK_IsHalted()
TA260 003:019.902 - 0.479ms returns FALSE
TA260 003:019.909 JLINK_HasError()
TA260 003:022.001 JLINK_IsHalted()
TA260 003:024.494   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:024.992 - 2.990ms returns TRUE
TA260 003:024.999 JLINK_ReadReg(R15 (PC))
TA260 003:025.005 - 0.005ms returns 0x20000000
TA260 003:025.018 JLINK_ClrBPEx(BPHandle = 0x0000003C)
TA260 003:025.022 - 0.004ms returns 0x00
TA260 003:025.026 JLINK_ReadReg(R0)
TA260 003:025.030 - 0.003ms returns 0x00000000
TA260 003:025.375 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:025.382   Data:  4A EC 0C BE 6E 90 7D BF F3 5D 0B BE 30 9E 7D BF ...
TA260 003:025.392   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:028.002 - 2.626ms returns 0x27C
TA260 003:028.015 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:028.020   Data:  AC F6 7F BF 4B 51 7B BC 4A F8 7F BF 00 30 62 BC ...
TA260 003:028.029   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:029.900 - 1.884ms returns 0x184
TA260 003:029.923 JLINK_HasError()
TA260 003:029.929 JLINK_WriteReg(R0, 0x0800C400)
TA260 003:029.935 - 0.006ms returns 0
TA260 003:029.939 JLINK_WriteReg(R1, 0x00000400)
TA260 003:029.943 - 0.003ms returns 0
TA260 003:029.947 JLINK_WriteReg(R2, 0x20000184)
TA260 003:029.950 - 0.003ms returns 0
TA260 003:029.954 JLINK_WriteReg(R3, 0x00000000)
TA260 003:029.958 - 0.003ms returns 0
TA260 003:029.962 JLINK_WriteReg(R4, 0x00000000)
TA260 003:029.965 - 0.003ms returns 0
TA260 003:029.970 JLINK_WriteReg(R5, 0x00000000)
TA260 003:029.978 - 0.008ms returns 0
TA260 003:029.982 JLINK_WriteReg(R6, 0x00000000)
TA260 003:029.986 - 0.003ms returns 0
TA260 003:029.990 JLINK_WriteReg(R7, 0x00000000)
TA260 003:029.994 - 0.003ms returns 0
TA260 003:029.998 JLINK_WriteReg(R8, 0x00000000)
TA260 003:030.001 - 0.003ms returns 0
TA260 003:030.005 JLINK_WriteReg(R9, 0x20000180)
TA260 003:030.008 - 0.003ms returns 0
TA260 003:030.012 JLINK_WriteReg(R10, 0x00000000)
TA260 003:030.016 - 0.003ms returns 0
TA260 003:030.020 JLINK_WriteReg(R11, 0x00000000)
TA260 003:030.023 - 0.003ms returns 0
TA260 003:030.027 JLINK_WriteReg(R12, 0x00000000)
TA260 003:030.030 - 0.003ms returns 0
TA260 003:030.035 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:030.039 - 0.004ms returns 0
TA260 003:030.043 JLINK_WriteReg(R14, 0x20000001)
TA260 003:030.047 - 0.004ms returns 0
TA260 003:030.051 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:030.055 - 0.003ms returns 0
TA260 003:030.059 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:030.063 - 0.003ms returns 0
TA260 003:030.067 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:030.070 - 0.003ms returns 0
TA260 003:030.074 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:030.082 - 0.007ms returns 0
TA260 003:030.086 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:030.090 - 0.003ms returns 0
TA260 003:030.094 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:030.099 - 0.005ms returns 0x0000003D
TA260 003:030.104 JLINK_Go()
TA260 003:030.113   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:032.934 - 2.829ms 
TA260 003:032.954 JLINK_IsHalted()
TA260 003:033.445 - 0.490ms returns FALSE
TA260 003:033.451 JLINK_HasError()
TA260 003:034.507 JLINK_IsHalted()
TA260 003:034.991 - 0.484ms returns FALSE
TA260 003:034.997 JLINK_HasError()
TA260 003:037.006 JLINK_IsHalted()
TA260 003:039.366   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:039.886 - 2.879ms returns TRUE
TA260 003:039.907 JLINK_ReadReg(R15 (PC))
TA260 003:039.914 - 0.006ms returns 0x20000000
TA260 003:040.419 JLINK_ClrBPEx(BPHandle = 0x0000003D)
TA260 003:040.427 - 0.008ms returns 0x00
TA260 003:040.433 JLINK_ReadReg(R0)
TA260 003:040.436 - 0.004ms returns 0x00000000
TA260 003:040.799 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:040.806   Data:  38 A0 6E 3D B1 90 7F BF E9 E5 74 3D C2 8A 7F BF ...
TA260 003:040.817   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:043.409 - 2.609ms returns 0x27C
TA260 003:043.424 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:043.428   Data:  53 E3 7B BF EC 57 38 3E 4D D1 7B BF 6C E3 39 3E ...
TA260 003:043.438   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:045.308 - 1.883ms returns 0x184
TA260 003:045.315 JLINK_HasError()
TA260 003:045.320 JLINK_WriteReg(R0, 0x0800C800)
TA260 003:045.325 - 0.005ms returns 0
TA260 003:045.331 JLINK_WriteReg(R1, 0x00000400)
TA260 003:045.334 - 0.003ms returns 0
TA260 003:045.338 JLINK_WriteReg(R2, 0x20000184)
TA260 003:045.342 - 0.003ms returns 0
TA260 003:045.346 JLINK_WriteReg(R3, 0x00000000)
TA260 003:045.349 - 0.003ms returns 0
TA260 003:045.353 JLINK_WriteReg(R4, 0x00000000)
TA260 003:045.357 - 0.003ms returns 0
TA260 003:045.361 JLINK_WriteReg(R5, 0x00000000)
TA260 003:045.364 - 0.003ms returns 0
TA260 003:045.368 JLINK_WriteReg(R6, 0x00000000)
TA260 003:045.372 - 0.003ms returns 0
TA260 003:045.376 JLINK_WriteReg(R7, 0x00000000)
TA260 003:045.380 - 0.003ms returns 0
TA260 003:045.384 JLINK_WriteReg(R8, 0x00000000)
TA260 003:045.387 - 0.003ms returns 0
TA260 003:045.391 JLINK_WriteReg(R9, 0x20000180)
TA260 003:045.394 - 0.003ms returns 0
TA260 003:045.398 JLINK_WriteReg(R10, 0x00000000)
TA260 003:045.402 - 0.003ms returns 0
TA260 003:045.406 JLINK_WriteReg(R11, 0x00000000)
TA260 003:045.409 - 0.003ms returns 0
TA260 003:045.413 JLINK_WriteReg(R12, 0x00000000)
TA260 003:045.417 - 0.003ms returns 0
TA260 003:045.421 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:045.424 - 0.003ms returns 0
TA260 003:045.428 JLINK_WriteReg(R14, 0x20000001)
TA260 003:045.432 - 0.003ms returns 0
TA260 003:045.436 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:045.442 - 0.006ms returns 0
TA260 003:045.446 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:045.450 - 0.003ms returns 0
TA260 003:045.454 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:045.457 - 0.003ms returns 0
TA260 003:045.461 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:045.465 - 0.003ms returns 0
TA260 003:045.469 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:045.472 - 0.003ms returns 0
TA260 003:045.477 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:045.481 - 0.004ms returns 0x0000003E
TA260 003:045.485 JLINK_Go()
TA260 003:045.493   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:048.206 - 2.720ms 
TA260 003:048.214 JLINK_IsHalted()
TA260 003:048.717 - 0.503ms returns FALSE
TA260 003:048.723 JLINK_HasError()
TA260 003:050.448 JLINK_IsHalted()
TA260 003:050.938 - 0.490ms returns FALSE
TA260 003:050.947 JLINK_HasError()
TA260 003:052.447 JLINK_IsHalted()
TA260 003:054.742   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:055.177 - 2.729ms returns TRUE
TA260 003:055.184 JLINK_ReadReg(R15 (PC))
TA260 003:055.188 - 0.004ms returns 0x20000000
TA260 003:055.194 JLINK_ClrBPEx(BPHandle = 0x0000003E)
TA260 003:055.198 - 0.004ms returns 0x00
TA260 003:055.202 JLINK_ReadReg(R0)
TA260 003:055.206 - 0.003ms returns 0x00000000
TA260 003:055.551 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:055.559   Data:  C0 F8 80 3E B7 BE 77 BF 4A BB 81 3E 51 A5 77 BF ...
TA260 003:055.570   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:058.167 - 2.615ms returns 0x27C
TA260 003:058.180 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:058.184   Data:  EB 21 6E BF AF A7 BC 3E F2 FC 6D BF 8B 62 BD 3E ...
TA260 003:058.193   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:059.981 - 1.801ms returns 0x184
TA260 003:059.989 JLINK_HasError()
TA260 003:059.995 JLINK_WriteReg(R0, 0x0800CC00)
TA260 003:060.000 - 0.005ms returns 0
TA260 003:060.004 JLINK_WriteReg(R1, 0x00000400)
TA260 003:060.008 - 0.003ms returns 0
TA260 003:060.012 JLINK_WriteReg(R2, 0x20000184)
TA260 003:060.015 - 0.003ms returns 0
TA260 003:060.019 JLINK_WriteReg(R3, 0x00000000)
TA260 003:060.023 - 0.003ms returns 0
TA260 003:060.027 JLINK_WriteReg(R4, 0x00000000)
TA260 003:060.030 - 0.003ms returns 0
TA260 003:060.034 JLINK_WriteReg(R5, 0x00000000)
TA260 003:060.038 - 0.003ms returns 0
TA260 003:060.042 JLINK_WriteReg(R6, 0x00000000)
TA260 003:060.045 - 0.003ms returns 0
TA260 003:060.049 JLINK_WriteReg(R7, 0x00000000)
TA260 003:060.052 - 0.003ms returns 0
TA260 003:060.056 JLINK_WriteReg(R8, 0x00000000)
TA260 003:060.060 - 0.003ms returns 0
TA260 003:060.064 JLINK_WriteReg(R9, 0x20000180)
TA260 003:060.067 - 0.003ms returns 0
TA260 003:060.071 JLINK_WriteReg(R10, 0x00000000)
TA260 003:060.075 - 0.003ms returns 0
TA260 003:060.079 JLINK_WriteReg(R11, 0x00000000)
TA260 003:060.082 - 0.003ms returns 0
TA260 003:060.086 JLINK_WriteReg(R12, 0x00000000)
TA260 003:060.090 - 0.003ms returns 0
TA260 003:060.094 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:060.099 - 0.004ms returns 0
TA260 003:060.103 JLINK_WriteReg(R14, 0x20000001)
TA260 003:060.106 - 0.003ms returns 0
TA260 003:060.110 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:060.114 - 0.003ms returns 0
TA260 003:060.118 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:060.121 - 0.003ms returns 0
TA260 003:060.125 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:060.129 - 0.003ms returns 0
TA260 003:060.133 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:060.136 - 0.003ms returns 0
TA260 003:060.140 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:060.143 - 0.003ms returns 0
TA260 003:060.148 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:060.152 - 0.004ms returns 0x0000003F
TA260 003:060.156 JLINK_Go()
TA260 003:060.171   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:062.983 - 2.826ms 
TA260 003:062.998 JLINK_IsHalted()
TA260 003:063.492 - 0.494ms returns FALSE
TA260 003:063.498 JLINK_HasError()
TA260 003:065.960 JLINK_IsHalted()
TA260 003:066.472 - 0.512ms returns FALSE
TA260 003:066.479 JLINK_HasError()
TA260 003:068.465 JLINK_IsHalted()
TA260 003:070.742   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:071.220 - 2.754ms returns TRUE
TA260 003:071.228 JLINK_ReadReg(R15 (PC))
TA260 003:071.234 - 0.006ms returns 0x20000000
TA260 003:071.238 JLINK_ClrBPEx(BPHandle = 0x0000003F)
TA260 003:071.242 - 0.004ms returns 0x00
TA260 003:071.247 JLINK_ReadReg(R0)
TA260 003:071.251 - 0.004ms returns 0x00000000
TA260 003:071.616 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:071.624   Data:  A9 28 DF 3E 71 67 66 BF 8D DD DF 3E 8E 3B 66 BF ...
TA260 003:071.634   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:074.257 - 2.640ms returns 0x27C
TA260 003:074.270 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:074.274   Data:  C7 39 57 BF B7 F1 0A 3F 48 03 57 BF 1C 46 0B 3F ...
TA260 003:074.284   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:076.167 - 1.897ms returns 0x184
TA260 003:076.182 JLINK_HasError()
TA260 003:076.187 JLINK_WriteReg(R0, 0x0800D000)
TA260 003:076.193 - 0.005ms returns 0
TA260 003:076.197 JLINK_WriteReg(R1, 0x00000400)
TA260 003:076.201 - 0.003ms returns 0
TA260 003:076.205 JLINK_WriteReg(R2, 0x20000184)
TA260 003:076.208 - 0.003ms returns 0
TA260 003:076.212 JLINK_WriteReg(R3, 0x00000000)
TA260 003:076.216 - 0.003ms returns 0
TA260 003:076.220 JLINK_WriteReg(R4, 0x00000000)
TA260 003:076.223 - 0.003ms returns 0
TA260 003:076.228 JLINK_WriteReg(R5, 0x00000000)
TA260 003:076.232 - 0.004ms returns 0
TA260 003:076.236 JLINK_WriteReg(R6, 0x00000000)
TA260 003:076.240 - 0.003ms returns 0
TA260 003:076.278 JLINK_WriteReg(R7, 0x00000000)
TA260 003:076.285 - 0.006ms returns 0
TA260 003:076.290 JLINK_WriteReg(R8, 0x00000000)
TA260 003:076.294 - 0.003ms returns 0
TA260 003:076.298 JLINK_WriteReg(R9, 0x20000180)
TA260 003:076.301 - 0.003ms returns 0
TA260 003:076.305 JLINK_WriteReg(R10, 0x00000000)
TA260 003:076.309 - 0.003ms returns 0
TA260 003:076.313 JLINK_WriteReg(R11, 0x00000000)
TA260 003:076.316 - 0.003ms returns 0
TA260 003:076.320 JLINK_WriteReg(R12, 0x00000000)
TA260 003:076.323 - 0.003ms returns 0
TA260 003:076.328 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:076.333 - 0.004ms returns 0
TA260 003:076.337 JLINK_WriteReg(R14, 0x20000001)
TA260 003:076.341 - 0.003ms returns 0
TA260 003:076.345 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:076.349 - 0.004ms returns 0
TA260 003:076.354 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:076.358 - 0.004ms returns 0
TA260 003:076.362 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:076.383 - 0.021ms returns 0
TA260 003:076.389 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:076.392 - 0.003ms returns 0
TA260 003:076.396 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:076.400 - 0.003ms returns 0
TA260 003:076.405 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:076.410 - 0.005ms returns 0x00000040
TA260 003:076.414 JLINK_Go()
TA260 003:076.422   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:079.253 - 2.839ms 
TA260 003:079.264 JLINK_IsHalted()
TA260 003:079.858 - 0.593ms returns FALSE
TA260 003:079.874 JLINK_HasError()
TA260 003:082.479 JLINK_IsHalted()
TA260 003:082.961 - 0.481ms returns FALSE
TA260 003:082.968 JLINK_HasError()
TA260 003:084.475 JLINK_IsHalted()
TA260 003:086.782   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:087.289 - 2.813ms returns TRUE
TA260 003:087.297 JLINK_ReadReg(R15 (PC))
TA260 003:087.303 - 0.005ms returns 0x20000000
TA260 003:087.309 JLINK_ClrBPEx(BPHandle = 0x00000040)
TA260 003:087.313 - 0.004ms returns 0x00
TA260 003:087.319 JLINK_ReadReg(R0)
TA260 003:087.323 - 0.004ms returns 0x00000000
TA260 003:087.653 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:087.660   Data:  93 62 1A 3F 78 35 4C BF B8 B2 1A 3F C7 F8 4B BF ...
TA260 003:087.669   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:090.307 - 2.653ms returns 0x27C
TA260 003:090.324 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:090.328   Data:  43 0C 38 BF AA 38 32 3F 55 C6 37 BF C7 80 32 3F ...
TA260 003:090.337   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:092.201 - 1.877ms returns 0x184
TA260 003:092.213 JLINK_HasError()
TA260 003:092.249 JLINK_WriteReg(R0, 0x0800D400)
TA260 003:092.255 - 0.006ms returns 0
TA260 003:092.260 JLINK_WriteReg(R1, 0x00000400)
TA260 003:092.263 - 0.003ms returns 0
TA260 003:092.267 JLINK_WriteReg(R2, 0x20000184)
TA260 003:092.270 - 0.003ms returns 0
TA260 003:092.274 JLINK_WriteReg(R3, 0x00000000)
TA260 003:092.278 - 0.003ms returns 0
TA260 003:092.282 JLINK_WriteReg(R4, 0x00000000)
TA260 003:092.295 - 0.013ms returns 0
TA260 003:092.299 JLINK_WriteReg(R5, 0x00000000)
TA260 003:092.303 - 0.003ms returns 0
TA260 003:092.307 JLINK_WriteReg(R6, 0x00000000)
TA260 003:092.310 - 0.003ms returns 0
TA260 003:092.314 JLINK_WriteReg(R7, 0x00000000)
TA260 003:092.317 - 0.003ms returns 0
TA260 003:092.321 JLINK_WriteReg(R8, 0x00000000)
TA260 003:092.325 - 0.003ms returns 0
TA260 003:092.329 JLINK_WriteReg(R9, 0x20000180)
TA260 003:092.332 - 0.003ms returns 0
TA260 003:092.336 JLINK_WriteReg(R10, 0x00000000)
TA260 003:092.339 - 0.003ms returns 0
TA260 003:092.343 JLINK_WriteReg(R11, 0x00000000)
TA260 003:092.347 - 0.003ms returns 0
TA260 003:092.351 JLINK_WriteReg(R12, 0x00000000)
TA260 003:092.354 - 0.003ms returns 0
TA260 003:092.359 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:092.390 - 0.030ms returns 0
TA260 003:092.394 JLINK_WriteReg(R14, 0x20000001)
TA260 003:092.397 - 0.003ms returns 0
TA260 003:092.401 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:092.405 - 0.003ms returns 0
TA260 003:092.409 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:092.412 - 0.003ms returns 0
TA260 003:092.416 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:092.420 - 0.003ms returns 0
TA260 003:092.424 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:092.427 - 0.003ms returns 0
TA260 003:092.431 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:092.434 - 0.003ms returns 0
TA260 003:092.439 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:092.443 - 0.004ms returns 0x00000041
TA260 003:092.447 JLINK_Go()
TA260 003:092.455   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:095.187 - 2.739ms 
TA260 003:095.194 JLINK_IsHalted()
TA260 003:095.661 - 0.467ms returns FALSE
TA260 003:095.667 JLINK_HasError()
TA260 003:097.486 JLINK_IsHalted()
TA260 003:097.957 - 0.471ms returns FALSE
TA260 003:097.964 JLINK_HasError()
TA260 003:099.488 JLINK_IsHalted()
TA260 003:101.940   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:102.423 - 2.934ms returns TRUE
TA260 003:102.430 JLINK_ReadReg(R15 (PC))
TA260 003:102.435 - 0.005ms returns 0x20000000
TA260 003:102.440 JLINK_ClrBPEx(BPHandle = 0x00000041)
TA260 003:102.444 - 0.003ms returns 0x00
TA260 003:102.448 JLINK_ReadReg(R0)
TA260 003:102.452 - 0.003ms returns 0x00000000
TA260 003:102.793 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:102.801   Data:  FC 41 3F 3F 80 2A 2A BF C0 84 3F 3F 57 DF 29 BF ...
TA260 003:102.810   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:105.444 - 2.650ms returns 0x27C
TA260 003:105.450 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:105.454   Data:  19 CC 11 BF 49 A6 52 3F 6B 79 11 BF 59 DF 52 3F ...
TA260 003:105.461   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:107.346 - 1.895ms returns 0x184
TA260 003:107.356 JLINK_HasError()
TA260 003:107.361 JLINK_WriteReg(R0, 0x0800D800)
TA260 003:107.365 - 0.005ms returns 0
TA260 003:107.370 JLINK_WriteReg(R1, 0x00000400)
TA260 003:107.373 - 0.003ms returns 0
TA260 003:107.377 JLINK_WriteReg(R2, 0x20000184)
TA260 003:107.381 - 0.003ms returns 0
TA260 003:107.385 JLINK_WriteReg(R3, 0x00000000)
TA260 003:107.388 - 0.003ms returns 0
TA260 003:107.392 JLINK_WriteReg(R4, 0x00000000)
TA260 003:107.395 - 0.003ms returns 0
TA260 003:107.400 JLINK_WriteReg(R5, 0x00000000)
TA260 003:107.403 - 0.003ms returns 0
TA260 003:107.407 JLINK_WriteReg(R6, 0x00000000)
TA260 003:107.452 - 0.045ms returns 0
TA260 003:107.457 JLINK_WriteReg(R7, 0x00000000)
TA260 003:107.460 - 0.003ms returns 0
TA260 003:107.464 JLINK_WriteReg(R8, 0x00000000)
TA260 003:107.467 - 0.003ms returns 0
TA260 003:107.471 JLINK_WriteReg(R9, 0x20000180)
TA260 003:107.475 - 0.003ms returns 0
TA260 003:107.482 JLINK_WriteReg(R10, 0x00000000)
TA260 003:107.486 - 0.004ms returns 0
TA260 003:107.490 JLINK_WriteReg(R11, 0x00000000)
TA260 003:107.493 - 0.003ms returns 0
TA260 003:107.497 JLINK_WriteReg(R12, 0x00000000)
TA260 003:107.500 - 0.003ms returns 0
TA260 003:107.505 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:107.508 - 0.003ms returns 0
TA260 003:107.512 JLINK_WriteReg(R14, 0x20000001)
TA260 003:107.516 - 0.003ms returns 0
TA260 003:107.520 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:107.523 - 0.003ms returns 0
TA260 003:107.527 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:107.531 - 0.003ms returns 0
TA260 003:107.535 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:107.564 - 0.028ms returns 0
TA260 003:107.568 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:107.571 - 0.003ms returns 0
TA260 003:107.575 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:107.578 - 0.003ms returns 0
TA260 003:107.583 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:107.587 - 0.004ms returns 0x00000042
TA260 003:107.591 JLINK_Go()
TA260 003:107.599   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:110.434 - 2.842ms 
TA260 003:110.444 JLINK_IsHalted()
TA260 003:110.940 - 0.495ms returns FALSE
TA260 003:110.954 JLINK_HasError()
TA260 003:111.992 JLINK_IsHalted()
TA260 003:112.490 - 0.497ms returns FALSE
TA260 003:112.496 JLINK_HasError()
TA260 003:113.992 JLINK_IsHalted()
TA260 003:114.517 - 0.524ms returns FALSE
TA260 003:114.526 JLINK_HasError()
TA260 003:115.995 JLINK_IsHalted()
TA260 003:118.380   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:118.899 - 2.903ms returns TRUE
TA260 003:118.910 JLINK_ReadReg(R15 (PC))
TA260 003:118.915 - 0.005ms returns 0x20000000
TA260 003:118.943 JLINK_ClrBPEx(BPHandle = 0x00000042)
TA260 003:118.948 - 0.005ms returns 0x00
TA260 003:118.953 JLINK_ReadReg(R0)
TA260 003:118.956 - 0.003ms returns 0x00000000
TA260 003:119.277 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:119.284   Data:  D1 C7 5C 3F 73 95 01 BF A3 FA 5C 3F B5 3E 01 BF ...
TA260 003:119.293   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:121.907 - 2.630ms returns 0x27C
TA260 003:121.923 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:121.928   Data:  2C E3 CB BE 8C FB 6A 3F AE 2A CB BE 5E 23 6B 3F ...
TA260 003:121.939   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:123.846 - 1.922ms returns 0x184
TA260 003:123.854 JLINK_HasError()
TA260 003:123.859 JLINK_WriteReg(R0, 0x0800DC00)
TA260 003:123.864 - 0.005ms returns 0
TA260 003:123.868 JLINK_WriteReg(R1, 0x00000400)
TA260 003:123.872 - 0.003ms returns 0
TA260 003:123.876 JLINK_WriteReg(R2, 0x20000184)
TA260 003:123.880 - 0.004ms returns 0
TA260 003:123.884 JLINK_WriteReg(R3, 0x00000000)
TA260 003:123.887 - 0.003ms returns 0
TA260 003:123.892 JLINK_WriteReg(R4, 0x00000000)
TA260 003:123.895 - 0.003ms returns 0
TA260 003:123.899 JLINK_WriteReg(R5, 0x00000000)
TA260 003:123.902 - 0.003ms returns 0
TA260 003:123.906 JLINK_WriteReg(R6, 0x00000000)
TA260 003:123.910 - 0.003ms returns 0
TA260 003:123.914 JLINK_WriteReg(R7, 0x00000000)
TA260 003:123.917 - 0.003ms returns 0
TA260 003:123.921 JLINK_WriteReg(R8, 0x00000000)
TA260 003:123.924 - 0.003ms returns 0
TA260 003:123.928 JLINK_WriteReg(R9, 0x20000180)
TA260 003:123.932 - 0.003ms returns 0
TA260 003:123.936 JLINK_WriteReg(R10, 0x00000000)
TA260 003:123.939 - 0.003ms returns 0
TA260 003:123.943 JLINK_WriteReg(R11, 0x00000000)
TA260 003:123.947 - 0.003ms returns 0
TA260 003:123.951 JLINK_WriteReg(R12, 0x00000000)
TA260 003:123.954 - 0.003ms returns 0
TA260 003:123.958 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:123.962 - 0.003ms returns 0
TA260 003:123.966 JLINK_WriteReg(R14, 0x20000001)
TA260 003:123.969 - 0.003ms returns 0
TA260 003:123.973 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:123.977 - 0.003ms returns 0
TA260 003:123.981 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:123.984 - 0.003ms returns 0
TA260 003:123.988 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:123.992 - 0.003ms returns 0
TA260 003:123.996 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:124.002 - 0.006ms returns 0
TA260 003:124.006 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:124.010 - 0.003ms returns 0
TA260 003:124.014 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:124.018 - 0.004ms returns 0x00000043
TA260 003:124.022 JLINK_Go()
TA260 003:124.030   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:126.854 - 2.832ms 
TA260 003:126.860 JLINK_IsHalted()
TA260 003:127.328 - 0.467ms returns FALSE
TA260 003:127.336 JLINK_HasError()
TA260 003:129.001 JLINK_IsHalted()
TA260 003:129.491 - 0.490ms returns FALSE
TA260 003:129.496 JLINK_HasError()
TA260 003:131.004 JLINK_IsHalted()
TA260 003:133.286   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:133.801 - 2.797ms returns TRUE
TA260 003:133.808 JLINK_ReadReg(R15 (PC))
TA260 003:133.812 - 0.004ms returns 0x20000000
TA260 003:133.817 JLINK_ClrBPEx(BPHandle = 0x00000043)
TA260 003:133.821 - 0.003ms returns 0x00
TA260 003:133.825 JLINK_ReadReg(R0)
TA260 003:133.828 - 0.003ms returns 0x00000000
TA260 003:134.161 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:134.169   Data:  9F D1 71 3F 1F 0B A8 BE 8C F2 71 3F 25 4D A7 BE ...
TA260 003:134.178   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:136.706 - 2.545ms returns 0x27C
TA260 003:136.712 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:136.716   Data:  A4 B0 58 BE 10 49 7A 3F 8F 27 57 BE 1C 5E 7A 3F ...
TA260 003:136.723   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:138.630 - 1.917ms returns 0x184
TA260 003:138.642 JLINK_HasError()
TA260 003:138.649 JLINK_WriteReg(R0, 0x0800E000)
TA260 003:138.655 - 0.006ms returns 0
TA260 003:138.660 JLINK_WriteReg(R1, 0x00000400)
TA260 003:138.665 - 0.004ms returns 0
TA260 003:138.670 JLINK_WriteReg(R2, 0x20000184)
TA260 003:138.675 - 0.005ms returns 0
TA260 003:138.680 JLINK_WriteReg(R3, 0x00000000)
TA260 003:138.684 - 0.004ms returns 0
TA260 003:138.689 JLINK_WriteReg(R4, 0x00000000)
TA260 003:138.694 - 0.004ms returns 0
TA260 003:138.699 JLINK_WriteReg(R5, 0x00000000)
TA260 003:138.703 - 0.004ms returns 0
TA260 003:138.708 JLINK_WriteReg(R6, 0x00000000)
TA260 003:138.712 - 0.004ms returns 0
TA260 003:138.717 JLINK_WriteReg(R7, 0x00000000)
TA260 003:138.722 - 0.004ms returns 0
TA260 003:138.727 JLINK_WriteReg(R8, 0x00000000)
TA260 003:138.731 - 0.004ms returns 0
TA260 003:138.735 JLINK_WriteReg(R9, 0x20000180)
TA260 003:138.739 - 0.003ms returns 0
TA260 003:138.743 JLINK_WriteReg(R10, 0x00000000)
TA260 003:138.746 - 0.003ms returns 0
TA260 003:138.750 JLINK_WriteReg(R11, 0x00000000)
TA260 003:138.754 - 0.004ms returns 0
TA260 003:138.758 JLINK_WriteReg(R12, 0x00000000)
TA260 003:138.762 - 0.003ms returns 0
TA260 003:138.766 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:138.769 - 0.003ms returns 0
TA260 003:138.773 JLINK_WriteReg(R14, 0x20000001)
TA260 003:138.777 - 0.003ms returns 0
TA260 003:138.781 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:138.784 - 0.003ms returns 0
TA260 003:138.788 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:138.792 - 0.003ms returns 0
TA260 003:138.796 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:138.799 - 0.003ms returns 0
TA260 003:138.803 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:138.806 - 0.003ms returns 0
TA260 003:138.810 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:138.814 - 0.003ms returns 0
TA260 003:138.818 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:138.822 - 0.004ms returns 0x00000044
TA260 003:138.826 JLINK_Go()
TA260 003:138.834   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:141.629 - 2.802ms 
TA260 003:141.644 JLINK_IsHalted()
TA260 003:142.137 - 0.492ms returns FALSE
TA260 003:142.143 JLINK_HasError()
TA260 003:143.509 JLINK_IsHalted()
TA260 003:143.991 - 0.482ms returns FALSE
TA260 003:144.001 JLINK_HasError()
TA260 003:146.010 JLINK_IsHalted()
TA260 003:148.433   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:148.911 - 2.901ms returns TRUE
TA260 003:148.918 JLINK_ReadReg(R15 (PC))
TA260 003:148.922 - 0.004ms returns 0x20000000
TA260 003:148.927 JLINK_ClrBPEx(BPHandle = 0x00000044)
TA260 003:148.931 - 0.004ms returns 0x00
TA260 003:148.938 JLINK_ReadReg(R0)
TA260 003:148.942 - 0.004ms returns 0x00000000
TA260 003:149.359 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:149.367   Data:  6E 90 7D 3F 4A EC 0C BE 30 9E 7D 3F F3 5D 0B BE ...
TA260 003:149.376   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:152.002 - 2.642ms returns 0x27C
TA260 003:152.017 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:152.021   Data:  38 39 8A BC 4A F8 7F 3F 4B 51 7B BC C1 F9 7F 3F ...
TA260 003:152.030   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:153.848 - 1.830ms returns 0x184
TA260 003:153.855 JLINK_HasError()
TA260 003:153.860 JLINK_WriteReg(R0, 0x0800E400)
TA260 003:153.866 - 0.005ms returns 0
TA260 003:153.871 JLINK_WriteReg(R1, 0x000003A0)
TA260 003:153.875 - 0.003ms returns 0
TA260 003:153.879 JLINK_WriteReg(R2, 0x20000184)
TA260 003:153.882 - 0.003ms returns 0
TA260 003:153.887 JLINK_WriteReg(R3, 0x00000000)
TA260 003:153.890 - 0.003ms returns 0
TA260 003:153.894 JLINK_WriteReg(R4, 0x00000000)
TA260 003:153.897 - 0.003ms returns 0
TA260 003:153.901 JLINK_WriteReg(R5, 0x00000000)
TA260 003:153.905 - 0.003ms returns 0
TA260 003:153.909 JLINK_WriteReg(R6, 0x00000000)
TA260 003:153.912 - 0.003ms returns 0
TA260 003:153.916 JLINK_WriteReg(R7, 0x00000000)
TA260 003:153.920 - 0.003ms returns 0
TA260 003:153.924 JLINK_WriteReg(R8, 0x00000000)
TA260 003:153.927 - 0.003ms returns 0
TA260 003:153.931 JLINK_WriteReg(R9, 0x20000180)
TA260 003:153.935 - 0.003ms returns 0
TA260 003:153.939 JLINK_WriteReg(R10, 0x00000000)
TA260 003:153.942 - 0.003ms returns 0
TA260 003:153.946 JLINK_WriteReg(R11, 0x00000000)
TA260 003:153.949 - 0.003ms returns 0
TA260 003:153.953 JLINK_WriteReg(R12, 0x00000000)
TA260 003:153.957 - 0.003ms returns 0
TA260 003:153.961 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:153.964 - 0.003ms returns 0
TA260 003:153.969 JLINK_WriteReg(R14, 0x20000001)
TA260 003:153.972 - 0.003ms returns 0
TA260 003:153.976 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:153.980 - 0.003ms returns 0
TA260 003:153.984 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:153.987 - 0.003ms returns 0
TA260 003:153.991 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:153.994 - 0.003ms returns 0
TA260 003:153.999 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:154.002 - 0.003ms returns 0
TA260 003:154.006 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:154.010 - 0.003ms returns 0
TA260 003:154.014 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:154.018 - 0.004ms returns 0x00000045
TA260 003:154.022 JLINK_Go()
TA260 003:154.029   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:156.708 - 2.686ms 
TA260 003:156.719 JLINK_IsHalted()
TA260 003:157.185 - 0.465ms returns FALSE
TA260 003:157.192 JLINK_HasError()
TA260 003:161.525 JLINK_IsHalted()
TA260 003:164.016   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:164.529 - 3.003ms returns TRUE
TA260 003:164.543 JLINK_ReadReg(R15 (PC))
TA260 003:164.548 - 0.005ms returns 0x20000000
TA260 003:164.553 JLINK_ClrBPEx(BPHandle = 0x00000045)
TA260 003:164.557 - 0.004ms returns 0x00
TA260 003:164.561 JLINK_ReadReg(R0)
TA260 003:164.565 - 0.003ms returns 0x00000000
TA260 003:164.570 JLINK_HasError()
TA260 003:164.574 JLINK_WriteReg(R0, 0x00000002)
TA260 003:164.578 - 0.004ms returns 0
TA260 003:164.583 JLINK_WriteReg(R1, 0x000003A0)
TA260 003:164.586 - 0.003ms returns 0
TA260 003:164.590 JLINK_WriteReg(R2, 0x20000184)
TA260 003:164.594 - 0.003ms returns 0
TA260 003:164.598 JLINK_WriteReg(R3, 0x00000000)
TA260 003:164.601 - 0.003ms returns 0
TA260 003:164.605 JLINK_WriteReg(R4, 0x00000000)
TA260 003:164.608 - 0.003ms returns 0
TA260 003:164.612 JLINK_WriteReg(R5, 0x00000000)
TA260 003:164.616 - 0.003ms returns 0
TA260 003:164.620 JLINK_WriteReg(R6, 0x00000000)
TA260 003:164.623 - 0.003ms returns 0
TA260 003:164.627 JLINK_WriteReg(R7, 0x00000000)
TA260 003:164.630 - 0.003ms returns 0
TA260 003:164.635 JLINK_WriteReg(R8, 0x00000000)
TA260 003:164.638 - 0.003ms returns 0
TA260 003:164.642 JLINK_WriteReg(R9, 0x20000180)
TA260 003:164.646 - 0.003ms returns 0
TA260 003:164.653 JLINK_WriteReg(R10, 0x00000000)
TA260 003:164.656 - 0.003ms returns 0
TA260 003:164.660 JLINK_WriteReg(R11, 0x00000000)
TA260 003:164.664 - 0.003ms returns 0
TA260 003:164.668 JLINK_WriteReg(R12, 0x00000000)
TA260 003:164.671 - 0.003ms returns 0
TA260 003:164.675 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:164.679 - 0.003ms returns 0
TA260 003:164.683 JLINK_WriteReg(R14, 0x20000001)
TA260 003:164.686 - 0.003ms returns 0
TA260 003:164.690 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 003:164.694 - 0.003ms returns 0
TA260 003:164.698 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:164.701 - 0.003ms returns 0
TA260 003:164.705 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:164.709 - 0.003ms returns 0
TA260 003:164.713 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:164.716 - 0.003ms returns 0
TA260 003:164.720 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:164.724 - 0.003ms returns 0
TA260 003:164.728 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:164.732 - 0.004ms returns 0x00000046
TA260 003:164.736 JLINK_Go()
TA260 003:164.744   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:167.485 - 2.743ms 
TA260 003:167.493 JLINK_IsHalted()
TA260 003:169.876   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:170.471 - 2.977ms returns TRUE
TA260 003:170.485 JLINK_ReadReg(R15 (PC))
TA260 003:170.491 - 0.005ms returns 0x20000000
TA260 003:170.496 JLINK_ClrBPEx(BPHandle = 0x00000046)
TA260 003:170.500 - 0.004ms returns 0x00
TA260 003:170.504 JLINK_ReadReg(R0)
TA260 003:170.508 - 0.003ms returns 0x00000000
TA260 003:225.671 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 003:225.685   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 003:225.701   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 003:227.552 - 1.880ms returns 0x184
TA260 003:227.576 JLINK_HasError()
TA260 003:227.581 JLINK_WriteReg(R0, 0x08000000)
TA260 003:227.586 - 0.005ms returns 0
TA260 003:227.591 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 003:227.594 - 0.003ms returns 0
TA260 003:227.598 JLINK_WriteReg(R2, 0x00000003)
TA260 003:227.602 - 0.003ms returns 0
TA260 003:227.606 JLINK_WriteReg(R3, 0x00000000)
TA260 003:227.609 - 0.003ms returns 0
TA260 003:227.613 JLINK_WriteReg(R4, 0x00000000)
TA260 003:227.617 - 0.003ms returns 0
TA260 003:227.621 JLINK_WriteReg(R5, 0x00000000)
TA260 003:227.624 - 0.003ms returns 0
TA260 003:227.628 JLINK_WriteReg(R6, 0x00000000)
TA260 003:227.631 - 0.003ms returns 0
TA260 003:227.635 JLINK_WriteReg(R7, 0x00000000)
TA260 003:227.639 - 0.003ms returns 0
TA260 003:227.643 JLINK_WriteReg(R8, 0x00000000)
TA260 003:227.646 - 0.003ms returns 0
TA260 003:227.650 JLINK_WriteReg(R9, 0x20000180)
TA260 003:227.654 - 0.003ms returns 0
TA260 003:227.658 JLINK_WriteReg(R10, 0x00000000)
TA260 003:227.661 - 0.003ms returns 0
TA260 003:227.665 JLINK_WriteReg(R11, 0x00000000)
TA260 003:227.668 - 0.003ms returns 0
TA260 003:227.672 JLINK_WriteReg(R12, 0x00000000)
TA260 003:227.676 - 0.003ms returns 0
TA260 003:227.680 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:227.684 - 0.003ms returns 0
TA260 003:227.688 JLINK_WriteReg(R14, 0x20000001)
TA260 003:227.691 - 0.003ms returns 0
TA260 003:227.695 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 003:227.699 - 0.003ms returns 0
TA260 003:227.703 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:227.706 - 0.003ms returns 0
TA260 003:227.710 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:227.714 - 0.003ms returns 0
TA260 003:227.718 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:227.721 - 0.003ms returns 0
TA260 003:227.725 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:227.729 - 0.003ms returns 0
TA260 003:227.733 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:227.739   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:228.222 - 0.488ms returns 0x00000047
TA260 003:228.227 JLINK_Go()
TA260 003:228.233   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 003:228.802   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:231.622 - 3.393ms 
TA260 003:231.639 JLINK_IsHalted()
TA260 003:233.966   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:234.446 - 2.807ms returns TRUE
TA260 003:234.453 JLINK_ReadReg(R15 (PC))
TA260 003:234.458 - 0.004ms returns 0x20000000
TA260 003:234.462 JLINK_ClrBPEx(BPHandle = 0x00000047)
TA260 003:234.466 - 0.004ms returns 0x00
TA260 003:234.471 JLINK_ReadReg(R0)
TA260 003:234.474 - 0.003ms returns 0x00000000
TA260 003:234.479 JLINK_HasError()
TA260 003:234.483 JLINK_WriteReg(R0, 0xFFFFFFFF)
TA260 003:234.488 - 0.003ms returns 0
TA260 003:234.492 JLINK_WriteReg(R1, 0x08000000)
TA260 003:234.495 - 0.003ms returns 0
TA260 003:234.499 JLINK_WriteReg(R2, 0x0000E7A0)
TA260 003:234.502 - 0.003ms returns 0
TA260 003:234.507 JLINK_WriteReg(R3, 0x04C11DB7)
TA260 003:234.510 - 0.003ms returns 0
TA260 003:234.514 JLINK_WriteReg(R4, 0x00000000)
TA260 003:234.517 - 0.003ms returns 0
TA260 003:234.521 JLINK_WriteReg(R5, 0x00000000)
TA260 003:234.525 - 0.003ms returns 0
TA260 003:234.529 JLINK_WriteReg(R6, 0x00000000)
TA260 003:234.532 - 0.003ms returns 0
TA260 003:234.536 JLINK_WriteReg(R7, 0x00000000)
TA260 003:234.540 - 0.003ms returns 0
TA260 003:234.544 JLINK_WriteReg(R8, 0x00000000)
TA260 003:234.547 - 0.003ms returns 0
TA260 003:234.551 JLINK_WriteReg(R9, 0x20000180)
TA260 003:234.555 - 0.003ms returns 0
TA260 003:234.559 JLINK_WriteReg(R10, 0x00000000)
TA260 003:234.563 - 0.003ms returns 0
TA260 003:234.567 JLINK_WriteReg(R11, 0x00000000)
TA260 003:234.570 - 0.003ms returns 0
TA260 003:234.581 JLINK_WriteReg(R12, 0x00000000)
TA260 003:234.584 - 0.003ms returns 0
TA260 003:234.589 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:234.592 - 0.003ms returns 0
TA260 003:234.596 JLINK_WriteReg(R14, 0x20000001)
TA260 003:234.600 - 0.003ms returns 0
TA260 003:234.604 JLINK_WriteReg(R15 (PC), 0x20000002)
TA260 003:234.607 - 0.003ms returns 0
TA260 003:234.612 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:234.615 - 0.004ms returns 0
TA260 003:234.619 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:234.622 - 0.003ms returns 0
TA260 003:234.626 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:234.630 - 0.003ms returns 0
TA260 003:234.634 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:234.637 - 0.003ms returns 0
TA260 003:234.642 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:234.645 - 0.004ms returns 0x00000048
TA260 003:234.650 JLINK_Go()
TA260 003:234.657   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:237.458 - 2.807ms 
TA260 003:237.464 JLINK_IsHalted()
TA260 003:237.934 - 0.469ms returns FALSE
TA260 003:237.940 JLINK_HasError()
TA260 003:240.068 JLINK_IsHalted()
TA260 003:240.634 - 0.566ms returns FALSE
TA260 003:240.647 JLINK_HasError()
TA260 003:243.068 JLINK_IsHalted()
TA260 003:243.626 - 0.558ms returns FALSE
TA260 003:243.633 JLINK_HasError()
TA260 003:245.068 JLINK_IsHalted()
TA260 003:245.546 - 0.478ms returns FALSE
TA260 003:245.552 JLINK_HasError()
TA260 003:247.570 JLINK_IsHalted()
TA260 003:248.072 - 0.501ms returns FALSE
TA260 003:248.079 JLINK_HasError()
TA260 003:249.571 JLINK_IsHalted()
TA260 003:250.073 - 0.501ms returns FALSE
TA260 003:250.081 JLINK_HasError()
TA260 003:251.572 JLINK_IsHalted()
TA260 003:252.063 - 0.490ms returns FALSE
TA260 003:252.076 JLINK_HasError()
TA260 003:253.570 JLINK_IsHalted()
TA260 003:254.062 - 0.491ms returns FALSE
TA260 003:254.067 JLINK_HasError()
TA260 003:256.078 JLINK_IsHalted()
TA260 003:256.628 - 0.549ms returns FALSE
TA260 003:256.635 JLINK_HasError()
TA260 003:258.077 JLINK_IsHalted()
TA260 003:258.625 - 0.548ms returns FALSE
TA260 003:258.631 JLINK_HasError()
TA260 003:260.078 JLINK_IsHalted()
TA260 003:260.501 - 0.422ms returns FALSE
TA260 003:260.507 JLINK_HasError()
TA260 003:262.078 JLINK_IsHalted()
TA260 003:262.491 - 0.412ms returns FALSE
TA260 003:262.497 JLINK_HasError()
TA260 003:264.078 JLINK_IsHalted()
TA260 003:264.590 - 0.511ms returns FALSE
TA260 003:264.602 JLINK_HasError()
TA260 003:266.079 JLINK_IsHalted()
TA260 003:266.615 - 0.536ms returns FALSE
TA260 003:266.621 JLINK_HasError()
TA260 003:268.582 JLINK_IsHalted()
TA260 003:269.050 - 0.467ms returns FALSE
TA260 003:269.057 JLINK_HasError()
TA260 003:270.590 JLINK_IsHalted()
TA260 003:271.099 - 0.509ms returns FALSE
TA260 003:271.114 JLINK_HasError()
TA260 003:273.086 JLINK_IsHalted()
TA260 003:273.534 - 0.447ms returns FALSE
TA260 003:273.541 JLINK_HasError()
TA260 003:275.084 JLINK_IsHalted()
TA260 003:275.625 - 0.540ms returns FALSE
TA260 003:275.630 JLINK_HasError()
TA260 003:277.588 JLINK_IsHalted()
TA260 003:278.104 - 0.516ms returns FALSE
TA260 003:278.110 JLINK_HasError()
TA260 003:279.591 JLINK_IsHalted()
TA260 003:280.097 - 0.505ms returns FALSE
TA260 003:280.106 JLINK_HasError()
TA260 003:281.591 JLINK_IsHalted()
TA260 003:282.187 - 0.595ms returns FALSE
TA260 003:282.194 JLINK_HasError()
TA260 003:283.589 JLINK_IsHalted()
TA260 003:284.084 - 0.495ms returns FALSE
TA260 003:284.090 JLINK_HasError()
TA260 003:286.094 JLINK_IsHalted()
TA260 003:286.767 - 0.673ms returns FALSE
TA260 003:286.777 JLINK_HasError()
TA260 003:288.094 JLINK_IsHalted()
TA260 003:288.615 - 0.520ms returns FALSE
TA260 003:288.622 JLINK_HasError()
TA260 003:289.840 JLINK_IsHalted()
TA260 003:290.494 - 0.654ms returns FALSE
TA260 003:290.505 JLINK_HasError()
TA260 003:291.813 JLINK_IsHalted()
TA260 003:292.310 - 0.496ms returns FALSE
TA260 003:292.316 JLINK_HasError()
TA260 003:293.811 JLINK_IsHalted()
TA260 003:294.311 - 0.500ms returns FALSE
TA260 003:294.317 JLINK_HasError()
TA260 003:296.321 JLINK_IsHalted()
TA260 003:296.810 - 0.488ms returns FALSE
TA260 003:296.820 JLINK_HasError()
TA260 003:298.317 JLINK_IsHalted()
TA260 003:298.765 - 0.447ms returns FALSE
TA260 003:298.770 JLINK_HasError()
TA260 003:300.318 JLINK_IsHalted()
TA260 003:300.804 - 0.485ms returns FALSE
TA260 003:300.814 JLINK_HasError()
TA260 003:302.319 JLINK_IsHalted()
TA260 003:302.753 - 0.434ms returns FALSE
TA260 003:302.760 JLINK_HasError()
TA260 003:304.317 JLINK_IsHalted()
TA260 003:304.787 - 0.470ms returns FALSE
TA260 003:304.793 JLINK_HasError()
TA260 003:306.320 JLINK_IsHalted()
TA260 003:306.845 - 0.525ms returns FALSE
TA260 003:306.851 JLINK_HasError()
TA260 003:308.822 JLINK_IsHalted()
TA260 003:309.320 - 0.498ms returns FALSE
TA260 003:309.326 JLINK_HasError()
TA260 003:310.825 JLINK_IsHalted()
TA260 003:311.269 - 0.443ms returns FALSE
TA260 003:311.280 JLINK_HasError()
TA260 003:312.824 JLINK_IsHalted()
TA260 003:313.298 - 0.474ms returns FALSE
TA260 003:313.305 JLINK_HasError()
TA260 003:314.824 JLINK_IsHalted()
TA260 003:315.299 - 0.475ms returns FALSE
TA260 003:315.305 JLINK_HasError()
TA260 003:317.327 JLINK_IsHalted()
TA260 003:317.800 - 0.473ms returns FALSE
TA260 003:317.806 JLINK_HasError()
TA260 003:319.334 JLINK_IsHalted()
TA260 003:319.823 - 0.487ms returns FALSE
TA260 003:319.835 JLINK_HasError()
TA260 003:321.332 JLINK_IsHalted()
TA260 003:322.122 - 0.790ms returns FALSE
TA260 003:322.135 JLINK_HasError()
TA260 003:323.329 JLINK_IsHalted()
TA260 003:323.845 - 0.516ms returns FALSE
TA260 003:323.852 JLINK_HasError()
TA260 003:325.327 JLINK_IsHalted()
TA260 003:325.845 - 0.518ms returns FALSE
TA260 003:325.853 JLINK_HasError()
TA260 003:327.836 JLINK_IsHalted()
TA260 003:328.273 - 0.436ms returns FALSE
TA260 003:328.280 JLINK_HasError()
TA260 003:329.836 JLINK_IsHalted()
TA260 003:331.620 - 1.783ms returns FALSE
TA260 003:331.633 JLINK_HasError()
TA260 003:332.837 JLINK_IsHalted()
TA260 003:333.298 - 0.460ms returns FALSE
TA260 003:333.306 JLINK_HasError()
TA260 003:334.834 JLINK_IsHalted()
TA260 003:335.308 - 0.473ms returns FALSE
TA260 003:335.315 JLINK_HasError()
TA260 003:337.340 JLINK_IsHalted()
TA260 003:337.842 - 0.502ms returns FALSE
TA260 003:337.849 JLINK_HasError()
TA260 003:339.343 JLINK_IsHalted()
TA260 003:339.790 - 0.447ms returns FALSE
TA260 003:339.801 JLINK_HasError()
TA260 003:341.345 JLINK_IsHalted()
TA260 003:341.860 - 0.514ms returns FALSE
TA260 003:341.873 JLINK_HasError()
TA260 003:343.341 JLINK_IsHalted()
TA260 003:343.788 - 0.446ms returns FALSE
TA260 003:343.793 JLINK_HasError()
TA260 003:345.340 JLINK_IsHalted()
TA260 003:345.903 - 0.562ms returns FALSE
TA260 003:345.913 JLINK_HasError()
TA260 003:347.847 JLINK_IsHalted()
TA260 003:348.418 - 0.570ms returns FALSE
TA260 003:348.429 JLINK_HasError()
TA260 003:349.849 JLINK_IsHalted()
TA260 003:350.346 - 0.496ms returns FALSE
TA260 003:350.357 JLINK_HasError()
TA260 003:351.850 JLINK_IsHalted()
TA260 003:352.311 - 0.460ms returns FALSE
TA260 003:352.318 JLINK_HasError()
TA260 003:353.848 JLINK_IsHalted()
TA260 003:354.345 - 0.497ms returns FALSE
TA260 003:354.352 JLINK_HasError()
TA260 003:356.354 JLINK_IsHalted()
TA260 003:356.813 - 0.458ms returns FALSE
TA260 003:356.824 JLINK_HasError()
TA260 003:358.358 JLINK_IsHalted()
TA260 003:358.847 - 0.489ms returns FALSE
TA260 003:358.854 JLINK_HasError()
TA260 003:360.356 JLINK_IsHalted()
TA260 003:360.876 - 0.520ms returns FALSE
TA260 003:360.883 JLINK_HasError()
TA260 003:362.355 JLINK_IsHalted()
TA260 003:362.809 - 0.453ms returns FALSE
TA260 003:362.815 JLINK_HasError()
TA260 003:364.357 JLINK_IsHalted()
TA260 003:364.880 - 0.522ms returns FALSE
TA260 003:364.890 JLINK_HasError()
TA260 003:366.355 JLINK_IsHalted()
TA260 003:366.856 - 0.500ms returns FALSE
TA260 003:366.861 JLINK_HasError()
TA260 003:368.855 JLINK_IsHalted()
TA260 003:369.354 - 0.498ms returns FALSE
TA260 003:369.360 JLINK_HasError()
TA260 003:370.859 JLINK_IsHalted()
TA260 003:371.347 - 0.488ms returns FALSE
TA260 003:371.360 JLINK_HasError()
TA260 003:373.363 JLINK_IsHalted()
TA260 003:373.865 - 0.501ms returns FALSE
TA260 003:373.875 JLINK_HasError()
TA260 003:375.360 JLINK_IsHalted()
TA260 003:375.855 - 0.494ms returns FALSE
TA260 003:375.860 JLINK_HasError()
TA260 003:377.867 JLINK_IsHalted()
TA260 003:378.344 - 0.476ms returns FALSE
TA260 003:378.352 JLINK_HasError()
TA260 003:379.867 JLINK_IsHalted()
TA260 003:380.459 - 0.591ms returns FALSE
TA260 003:380.469 JLINK_HasError()
TA260 003:382.869 JLINK_IsHalted()
TA260 003:383.358 - 0.489ms returns FALSE
TA260 003:383.364 JLINK_HasError()
TA260 003:384.868 JLINK_IsHalted()
TA260 003:385.390 - 0.521ms returns FALSE
TA260 003:385.396 JLINK_HasError()
TA260 003:387.369 JLINK_IsHalted()
TA260 003:387.845 - 0.475ms returns FALSE
TA260 003:387.851 JLINK_HasError()
TA260 003:389.370 JLINK_IsHalted()
TA260 003:389.851 - 0.479ms returns FALSE
TA260 003:389.863 JLINK_HasError()
TA260 003:391.375 JLINK_IsHalted()
TA260 003:391.851 - 0.475ms returns FALSE
TA260 003:391.863 JLINK_HasError()
TA260 003:393.372 JLINK_IsHalted()
TA260 003:393.855 - 0.483ms returns FALSE
TA260 003:393.860 JLINK_HasError()
TA260 003:395.372 JLINK_IsHalted()
TA260 003:395.856 - 0.484ms returns FALSE
TA260 003:395.863 JLINK_HasError()
TA260 003:397.877 JLINK_IsHalted()
TA260 003:398.356 - 0.478ms returns FALSE
TA260 003:398.363 JLINK_HasError()
TA260 003:399.878 JLINK_IsHalted()
TA260 003:400.390 - 0.511ms returns FALSE
TA260 003:400.400 JLINK_HasError()
TA260 003:401.878 JLINK_IsHalted()
TA260 003:402.346 - 0.467ms returns FALSE
TA260 003:402.352 JLINK_HasError()
TA260 003:403.876 JLINK_IsHalted()
TA260 003:404.366 - 0.489ms returns FALSE
TA260 003:404.372 JLINK_HasError()
TA260 003:406.380 JLINK_IsHalted()
TA260 003:406.856 - 0.475ms returns FALSE
TA260 003:406.862 JLINK_HasError()
TA260 003:408.382 JLINK_IsHalted()
TA260 003:408.844 - 0.461ms returns FALSE
TA260 003:408.850 JLINK_HasError()
TA260 003:410.386 JLINK_IsHalted()
TA260 003:410.898 - 0.511ms returns FALSE
TA260 003:410.912 JLINK_HasError()
TA260 003:412.383 JLINK_IsHalted()
TA260 003:412.890 - 0.506ms returns FALSE
TA260 003:412.896 JLINK_HasError()
TA260 003:414.383 JLINK_IsHalted()
TA260 003:414.844 - 0.461ms returns FALSE
TA260 003:414.850 JLINK_HasError()
TA260 003:416.384 JLINK_IsHalted()
TA260 003:416.864 - 0.479ms returns FALSE
TA260 003:416.870 JLINK_HasError()
TA260 003:418.887 JLINK_IsHalted()
TA260 003:419.388 - 0.500ms returns FALSE
TA260 003:419.394 JLINK_HasError()
TA260 003:420.890 JLINK_IsHalted()
TA260 003:421.312 - 0.421ms returns FALSE
TA260 003:421.321 JLINK_HasError()
TA260 003:422.889 JLINK_IsHalted()
TA260 003:423.434 - 0.545ms returns FALSE
TA260 003:423.441 JLINK_HasError()
TA260 003:424.888 JLINK_IsHalted()
TA260 003:425.487 - 0.599ms returns FALSE
TA260 003:425.496 JLINK_HasError()
TA260 003:427.391 JLINK_IsHalted()
TA260 003:427.889 - 0.497ms returns FALSE
TA260 003:427.895 JLINK_HasError()
TA260 003:429.393 JLINK_IsHalted()
TA260 003:429.914 - 0.520ms returns FALSE
TA260 003:429.937 JLINK_HasError()
TA260 003:431.395 JLINK_IsHalted()
TA260 003:431.894 - 0.498ms returns FALSE
TA260 003:431.904 JLINK_HasError()
TA260 003:433.394 JLINK_IsHalted()
TA260 003:433.888 - 0.494ms returns FALSE
TA260 003:433.894 JLINK_HasError()
TA260 003:435.395 JLINK_IsHalted()
TA260 003:435.892 - 0.497ms returns FALSE
TA260 003:435.898 JLINK_HasError()
TA260 003:437.902 JLINK_IsHalted()
TA260 003:438.500 - 0.597ms returns FALSE
TA260 003:438.510 JLINK_HasError()
TA260 003:439.903 JLINK_IsHalted()
TA260 003:440.320 - 0.416ms returns FALSE
TA260 003:440.326 JLINK_HasError()
TA260 003:441.432 JLINK_IsHalted()
TA260 003:441.893 - 0.460ms returns FALSE
TA260 003:441.905 JLINK_HasError()
TA260 003:442.951 JLINK_IsHalted()
TA260 003:443.420 - 0.469ms returns FALSE
TA260 003:443.426 JLINK_HasError()
TA260 003:444.486 JLINK_IsHalted()
TA260 003:444.957 - 0.471ms returns FALSE
TA260 003:444.964 JLINK_HasError()
TA260 003:446.008 JLINK_IsHalted()
TA260 003:446.491 - 0.482ms returns FALSE
TA260 003:446.497 JLINK_HasError()
TA260 003:447.554 JLINK_IsHalted()
TA260 003:448.046 - 0.492ms returns FALSE
TA260 003:448.053 JLINK_HasError()
TA260 003:449.082 JLINK_IsHalted()
TA260 003:449.625 - 0.542ms returns FALSE
TA260 003:449.631 JLINK_HasError()
TA260 003:451.125 JLINK_IsHalted()
TA260 003:451.616 - 0.490ms returns FALSE
TA260 003:451.621 JLINK_HasError()
TA260 003:453.640 JLINK_IsHalted()
TA260 003:454.068 - 0.428ms returns FALSE
TA260 003:454.075 JLINK_HasError()
TA260 003:456.141 JLINK_IsHalted()
TA260 003:456.695 - 0.552ms returns FALSE
TA260 003:456.712 JLINK_HasError()
TA260 003:458.148 JLINK_IsHalted()
TA260 003:458.670 - 0.521ms returns FALSE
TA260 003:458.718 JLINK_HasError()
TA260 003:460.147 JLINK_IsHalted()
TA260 003:460.618 - 0.471ms returns FALSE
TA260 003:460.624 JLINK_HasError()
TA260 003:462.146 JLINK_IsHalted()
TA260 003:462.623 - 0.477ms returns FALSE
TA260 003:462.629 JLINK_HasError()
TA260 003:464.145 JLINK_IsHalted()
TA260 003:464.626 - 0.480ms returns FALSE
TA260 003:464.632 JLINK_HasError()
TA260 003:466.146 JLINK_IsHalted()
TA260 003:466.625 - 0.478ms returns FALSE
TA260 003:466.631 JLINK_HasError()
TA260 003:468.648 JLINK_IsHalted()
TA260 003:469.136 - 0.487ms returns FALSE
TA260 003:469.141 JLINK_HasError()
TA260 003:471.153 JLINK_IsHalted()
TA260 003:471.753 - 0.599ms returns FALSE
TA260 003:471.766 JLINK_HasError()
TA260 003:473.154 JLINK_IsHalted()
TA260 003:473.648 - 0.493ms returns FALSE
TA260 003:473.654 JLINK_HasError()
TA260 003:475.152 JLINK_IsHalted()
TA260 003:475.616 - 0.463ms returns FALSE
TA260 003:475.622 JLINK_HasError()
TA260 003:476.654 JLINK_IsHalted()
TA260 003:477.154 - 0.499ms returns FALSE
TA260 003:477.161 JLINK_HasError()
TA260 003:478.658 JLINK_IsHalted()
TA260 003:479.152 - 0.493ms returns FALSE
TA260 003:479.158 JLINK_HasError()
TA260 003:480.660 JLINK_IsHalted()
TA260 003:481.222 - 0.561ms returns FALSE
TA260 003:481.235 JLINK_HasError()
TA260 003:482.658 JLINK_IsHalted()
TA260 003:483.138 - 0.480ms returns FALSE
TA260 003:483.144 JLINK_HasError()
TA260 003:484.658 JLINK_IsHalted()
TA260 003:485.128 - 0.470ms returns FALSE
TA260 003:485.135 JLINK_HasError()
TA260 003:487.161 JLINK_IsHalted()
TA260 003:487.800 - 0.637ms returns FALSE
TA260 003:487.811 JLINK_HasError()
TA260 003:490.178 JLINK_IsHalted()
TA260 003:490.759 - 0.580ms returns FALSE
TA260 003:490.771 JLINK_HasError()
TA260 003:492.168 JLINK_IsHalted()
TA260 003:492.767 - 0.597ms returns FALSE
TA260 003:492.774 JLINK_HasError()
TA260 003:494.164 JLINK_IsHalted()
TA260 003:494.709 - 0.545ms returns FALSE
TA260 003:494.720 JLINK_HasError()
TA260 003:496.167 JLINK_IsHalted()
TA260 003:496.658 - 0.490ms returns FALSE
TA260 003:496.670 JLINK_HasError()
TA260 003:498.668 JLINK_IsHalted()
TA260 003:499.162 - 0.493ms returns FALSE
TA260 003:499.170 JLINK_HasError()
TA260 003:500.670 JLINK_IsHalted()
TA260 003:501.170 - 0.500ms returns FALSE
TA260 003:501.176 JLINK_HasError()
TA260 003:502.674 JLINK_IsHalted()
TA260 003:503.306 - 0.630ms returns FALSE
TA260 003:503.320 JLINK_HasError()
TA260 003:504.672 JLINK_IsHalted()
TA260 003:505.174 - 0.502ms returns FALSE
TA260 003:505.180 JLINK_HasError()
TA260 003:507.176 JLINK_IsHalted()
TA260 003:507.755 - 0.578ms returns FALSE
TA260 003:507.765 JLINK_HasError()
TA260 003:509.179 JLINK_IsHalted()
TA260 003:509.652 - 0.473ms returns FALSE
TA260 003:509.658 JLINK_HasError()
TA260 003:511.175 JLINK_IsHalted()
TA260 003:511.661 - 0.485ms returns FALSE
TA260 003:511.667 JLINK_HasError()
TA260 003:513.176 JLINK_IsHalted()
TA260 003:513.661 - 0.485ms returns FALSE
TA260 003:513.667 JLINK_HasError()
TA260 003:515.174 JLINK_IsHalted()
TA260 003:515.660 - 0.486ms returns FALSE
TA260 003:515.666 JLINK_HasError()
TA260 003:517.680 JLINK_IsHalted()
TA260 003:518.238 - 0.557ms returns FALSE
TA260 003:518.250 JLINK_HasError()
TA260 003:519.687 JLINK_IsHalted()
TA260 003:520.234 - 0.546ms returns FALSE
TA260 003:520.244 JLINK_HasError()
TA260 003:521.682 JLINK_IsHalted()
TA260 003:522.152 - 0.469ms returns FALSE
TA260 003:522.164 JLINK_HasError()
TA260 003:523.682 JLINK_IsHalted()
TA260 003:524.185 - 0.503ms returns FALSE
TA260 003:524.192 JLINK_HasError()
TA260 003:526.184 JLINK_IsHalted()
TA260 003:526.766 - 0.581ms returns FALSE
TA260 003:526.772 JLINK_HasError()
TA260 003:528.186 JLINK_IsHalted()
TA260 003:528.683 - 0.496ms returns FALSE
TA260 003:528.689 JLINK_HasError()
TA260 003:530.188 JLINK_IsHalted()
TA260 003:530.619 - 0.430ms returns FALSE
TA260 003:530.627 JLINK_HasError()
TA260 003:532.188 JLINK_IsHalted()
TA260 003:532.626 - 0.438ms returns FALSE
TA260 003:532.633 JLINK_HasError()
TA260 003:534.193 JLINK_IsHalted()
TA260 003:534.632 - 0.438ms returns FALSE
TA260 003:534.642 JLINK_HasError()
TA260 003:536.190 JLINK_IsHalted()
TA260 003:536.694 - 0.504ms returns FALSE
TA260 003:536.705 JLINK_HasError()
TA260 003:538.693 JLINK_IsHalted()
TA260 003:539.130 - 0.436ms returns FALSE
TA260 003:539.137 JLINK_HasError()
TA260 003:540.692 JLINK_IsHalted()
TA260 003:541.129 - 0.436ms returns FALSE
TA260 003:541.136 JLINK_HasError()
TA260 003:542.692 JLINK_IsHalted()
TA260 003:543.173 - 0.481ms returns FALSE
TA260 003:543.179 JLINK_HasError()
TA260 003:544.691 JLINK_IsHalted()
TA260 003:545.161 - 0.470ms returns FALSE
TA260 003:545.166 JLINK_HasError()
TA260 003:547.196 JLINK_IsHalted()
TA260 003:547.691 - 0.494ms returns FALSE
TA260 003:547.696 JLINK_HasError()
TA260 003:549.195 JLINK_IsHalted()
TA260 003:549.651 - 0.455ms returns FALSE
TA260 003:549.660 JLINK_HasError()
TA260 003:551.198 JLINK_IsHalted()
TA260 003:551.753 - 0.554ms returns FALSE
TA260 003:551.759 JLINK_HasError()
TA260 003:553.197 JLINK_IsHalted()
TA260 003:553.705 - 0.508ms returns FALSE
TA260 003:553.711 JLINK_HasError()
TA260 003:554.882 JLINK_IsHalted()
TA260 003:555.390 - 0.508ms returns FALSE
TA260 003:555.403 JLINK_HasError()
TA260 003:556.991 JLINK_IsHalted()
TA260 003:557.482 - 0.490ms returns FALSE
TA260 003:557.492 JLINK_HasError()
TA260 003:558.992 JLINK_IsHalted()
TA260 003:559.469 - 0.477ms returns FALSE
TA260 003:559.474 JLINK_HasError()
TA260 003:560.994 JLINK_IsHalted()
TA260 003:561.456 - 0.461ms returns FALSE
TA260 003:561.462 JLINK_HasError()
TA260 003:562.996 JLINK_IsHalted()
TA260 003:565.872   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:566.791 - 3.794ms returns TRUE
TA260 003:566.803 JLINK_ReadReg(R15 (PC))
TA260 003:566.809 - 0.005ms returns 0x20000000
TA260 003:566.813 JLINK_ClrBPEx(BPHandle = 0x00000048)
TA260 003:566.817 - 0.003ms returns 0x00
TA260 003:566.821 JLINK_ReadReg(R0)
TA260 003:566.825 - 0.003ms returns 0x98C3A923
TA260 003:568.629 JLINK_HasError()
TA260 003:568.650 JLINK_WriteReg(R0, 0x00000003)
TA260 003:568.656 - 0.005ms returns 0
TA260 003:568.660 JLINK_WriteReg(R1, 0x08000000)
TA260 003:568.663 - 0.003ms returns 0
TA260 003:568.667 JLINK_WriteReg(R2, 0x0000E7A0)
TA260 003:568.671 - 0.003ms returns 0
TA260 003:568.675 JLINK_WriteReg(R3, 0x04C11DB7)
TA260 003:568.678 - 0.003ms returns 0
TA260 003:568.682 JLINK_WriteReg(R4, 0x00000000)
TA260 003:568.685 - 0.003ms returns 0
TA260 003:568.689 JLINK_WriteReg(R5, 0x00000000)
TA260 003:568.693 - 0.003ms returns 0
TA260 003:568.697 JLINK_WriteReg(R6, 0x00000000)
TA260 003:568.700 - 0.003ms returns 0
TA260 003:568.704 JLINK_WriteReg(R7, 0x00000000)
TA260 003:568.708 - 0.003ms returns 0
TA260 003:568.712 JLINK_WriteReg(R8, 0x00000000)
TA260 003:568.715 - 0.003ms returns 0
TA260 003:568.719 JLINK_WriteReg(R9, 0x20000180)
TA260 003:568.722 - 0.003ms returns 0
TA260 003:568.726 JLINK_WriteReg(R10, 0x00000000)
TA260 003:568.730 - 0.003ms returns 0
TA260 003:568.734 JLINK_WriteReg(R11, 0x00000000)
TA260 003:568.737 - 0.003ms returns 0
TA260 003:568.741 JLINK_WriteReg(R12, 0x00000000)
TA260 003:568.744 - 0.003ms returns 0
TA260 003:568.748 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:568.752 - 0.004ms returns 0
TA260 003:568.756 JLINK_WriteReg(R14, 0x20000001)
TA260 003:568.760 - 0.003ms returns 0
TA260 003:568.764 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 003:568.768 - 0.003ms returns 0
TA260 003:568.772 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:568.775 - 0.003ms returns 0
TA260 003:568.779 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:568.783 - 0.003ms returns 0
TA260 003:568.787 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:568.790 - 0.003ms returns 0
TA260 003:568.794 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:568.797 - 0.003ms returns 0
TA260 003:568.802 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:568.806 - 0.004ms returns 0x00000049
TA260 003:568.810 JLINK_Go()
TA260 003:568.819   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:571.902 - 3.091ms 
TA260 003:571.945 JLINK_IsHalted()
TA260 003:574.635   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:575.137 - 3.192ms returns TRUE
TA260 003:575.148 JLINK_ReadReg(R15 (PC))
TA260 003:575.154 - 0.006ms returns 0x20000000
TA260 003:575.160 JLINK_ClrBPEx(BPHandle = 0x00000049)
TA260 003:575.165 - 0.004ms returns 0x00
TA260 003:575.171 JLINK_ReadReg(R0)
TA260 003:575.175 - 0.004ms returns 0x00000000
TA260 003:628.708 JLINK_WriteMemEx(0x20000000, 0x00000002 Bytes, Flags = 0x02000000)
TA260 003:628.725   Data:  FE E7
TA260 003:628.743   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 003:629.228 - 0.519ms returns 0x2
TA260 003:629.244 JLINK_HasError()
TA260 003:636.156 JLINK_Close()
TA260 003:638.329   OnDisconnectTarget() start
TA260 003:638.344    J-Link Script File: Executing OnDisconnectTarget()
TA260 003:638.371   CPU_WriteMem(4 bytes @ 0xE0042004)
TA260 003:638.857   CPU_WriteMem(4 bytes @ 0xE0042008)
TA260 003:641.128   OnDisconnectTarget() end - Took 1.01ms
TA260 003:641.152   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:657.780 - 21.622ms
TA260 003:657.808   
TA260 003:657.812   Closed
