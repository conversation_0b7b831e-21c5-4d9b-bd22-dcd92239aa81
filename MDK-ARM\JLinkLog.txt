TA260 000:006.007   SEGGER J-Link V8.16 Log File
TA260 000:006.129   DLL Compiled: Feb 26 2025 12:07:26
TA260 000:006.133   Logging started @ 2025-08-02 07:37
TA260 000:006.136   Process: G:\keil\keil arm\UV4\UV4.exe
TA260 000:006.148 - 6.140ms 
TA260 000:006.156 JLINK_SetWarnOutHandler(...)
TA260 000:006.159 - 0.004ms 
TA260 000:006.167 JLINK_OpenEx(...)
TA260 000:010.724   Firmware: J-Link V9 compiled May  7 2021 16:26:12
TA260 000:012.093   Firmware: J-Link V9 compiled May  7 2021 16:26:12
TA260 000:012.222   Decompressing FW timestamp took 84 us
TA260 000:020.245   Hardware: V9.60
TA260 000:020.272   S/N: 69655018
TA260 000:020.277   OEM: SEGGER
TA260 000:020.289   Feature(s): R<PERSON>, GD<PERSON>, FlashDL, <PERSON>B<PERSON>, JFlash
TA260 000:021.567   Bootloader: (FW returned invalid version)
TA260 000:023.053   TELNET listener socket opened on port 19021
TA260 000:023.129   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
TA260 000:023.257   WEBSRV Webserver running on local port 19080
TA260 000:023.329   Looking for J-Link GUI Server exe at: G:\keil\keil arm\ARM\Segger\JLinkGUIServer.exe
TA260 000:023.415   Looking for J-Link GUI Server exe at: C:\Program Files (x86)\SEGGER\JLink_V630d\\JLinkGUIServer.exe
TA260 000:323.248   Failed to connect to J-Link GUI Server.
TA260 000:323.288 - 317.113ms returns "O.K."
TA260 000:323.306 JLINK_GetEmuCaps()
TA260 000:323.312 - 0.004ms returns 0xB9FF7BBF
TA260 000:323.324 JLINK_TIF_GetAvailable(...)
TA260 000:323.681 - 0.358ms 
TA260 000:323.694 JLINK_SetErrorOutHandler(...)
TA260 000:323.697 - 0.003ms 
TA260 000:323.722 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\2025ele_ori\zuolan_stm32\MDK-ARM\JLinkSettings.ini"", ...). 
TA260 000:337.604 - 13.883ms returns 0x00
TA260 000:339.584 JLINK_ExecCommand("Device = STM32F429IGTx", ...). 
TA260 000:340.909   Flash bank @ 0x90000000: SFL: Parsing sectorization info from ELF file
TA260 000:340.925     FlashDevice.SectorInfo[0]: .SectorSize = 0x00010000, .SectorStartAddr = 0x00000000
TA260 000:346.728   Device "STM32F429IG" selected.
TA260 000:347.144 - 7.537ms returns 0x00
TA260 000:347.157 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
TA260 000:347.180   ERROR: Unknown command
TA260 000:347.185 - 0.023ms returns 0x01
TA260 000:347.191 JLINK_GetHardwareVersion()
TA260 000:347.195 - 0.004ms returns 96000
TA260 000:347.200 JLINK_GetDLLVersion()
TA260 000:347.203 - 0.003ms returns 81600
TA260 000:347.207 JLINK_GetOEMString(...)
TA260 000:347.212 JLINK_GetFirmwareString(...)
TA260 000:347.217 - 0.004ms 
TA260 000:352.292 JLINK_GetDLLVersion()
TA260 000:352.320 - 0.028ms returns 81600
TA260 000:352.324 JLINK_GetCompileDateTime()
TA260 000:352.329 - 0.004ms 
TA260 000:353.757 JLINK_GetFirmwareString(...)
TA260 000:353.775 - 0.017ms 
TA260 000:355.525 JLINK_GetHardwareVersion()
TA260 000:355.552 - 0.026ms returns 96000
TA260 000:357.275 JLINK_GetSN()
TA260 000:357.300 - 0.024ms returns 69655018
TA260 000:362.709 JLINK_GetOEMString(...)
TA260 000:366.533 JLINK_TIF_Select(JLINKARM_TIF_SWD)
TA260 000:368.050 - 1.520ms returns 0x00
TA260 000:368.069 JLINK_HasError()
TA260 000:368.114 JLINK_SetSpeed(5000)
TA260 000:368.452 - 0.339ms 
TA260 000:368.462 JLINK_GetId()
TA260 000:370.689   InitTarget() start
TA260 000:370.724    J-Link Script File: Executing InitTarget()
TA260 000:372.718   SWD selected. Executing JTAG -> SWD switching sequence.
TA260 000:377.554   DAP initialized successfully.
TA260 000:390.580   InitTarget() end - Took 17.6ms
TA260 000:393.548   Found SW-DP with ID 0x2BA01477
TA260 000:398.826   DPIDR: 0x2BA01477
TA260 000:400.486   CoreSight SoC-400 or earlier
TA260 000:402.052   Scanning AP map to find all available APs
TA260 000:405.003   AP[1]: Stopped AP scan as end of AP map has been reached
TA260 000:407.416   AP[0]: AHB-AP (IDR: 0x24770011, ADDR: 0x00000000)
TA260 000:409.236   Iterating through AP map to find AHB-AP to use
TA260 000:412.474   AP[0]: Core found
TA260 000:414.238   AP[0]: AHB-AP ROM base: 0xE00FF000
TA260 000:416.957   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
TA260 000:419.007   Found Cortex-M4 r0p1, Little endian.
TA260 000:419.872   -- Max. mem block: 0x00010C40
TA260 000:420.669   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:421.213   CPU_ReadMem(4 bytes @ 0x********)
TA260 000:423.766   FPUnit: 6 code (BP) slots and 2 literal slots
TA260 000:423.800   CPU_ReadMem(4 bytes @ 0xE000EDFC)
TA260 000:424.268   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:424.795   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:425.279   CPU_WriteMem(4 bytes @ 0xE0001000)
TA260 000:425.766   CPU_ReadMem(4 bytes @ 0xE000ED88)
TA260 000:426.413   CPU_WriteMem(4 bytes @ 0xE000ED88)
TA260 000:426.943   CPU_ReadMem(4 bytes @ 0xE000ED88)
TA260 000:427.539   CPU_WriteMem(4 bytes @ 0xE000ED88)
TA260 000:430.018   CoreSight components:
TA260 000:431.852   ROMTbl[0] @ E00FF000
TA260 000:431.873   CPU_ReadMem(64 bytes @ 0xE00FF000)
TA260 000:432.605   CPU_ReadMem(32 bytes @ 0xE000EFE0)
TA260 000:435.197   [0][0]: E000E000 CID B105E00D PID 000BB00C SCS-M7
TA260 000:435.245   CPU_ReadMem(32 bytes @ 0xE0001FE0)
TA260 000:438.346   [0][1]: E0001000 CID B105E00D PID 003BB002 DWT
TA260 000:438.382   CPU_ReadMem(32 bytes @ 0xE0002FE0)
TA260 000:440.924   [0][2]: ******** CID B105E00D PID 002BB003 FPB
TA260 000:440.948   CPU_ReadMem(32 bytes @ 0xE0000FE0)
TA260 000:443.271   [0][3]: ******** CID B105E00D PID 003BB001 ITM
TA260 000:443.291   CPU_ReadMem(32 bytes @ 0xE0040FE0)
TA260 000:446.270   [0][4]: ******** CID B105900D PID 000BB9A1 TPIU
TA260 000:446.322   CPU_ReadMem(32 bytes @ 0xE0041FE0)
TA260 000:448.877   [0][5]: ******** CID B105900D PID 000BB925 ETM
TA260 000:449.407 - 80.944ms returns 0x2BA01477
TA260 000:449.469 JLINK_GetDLLVersion()
TA260 000:449.475 - 0.005ms returns 81600
TA260 000:449.490 JLINK_CORE_GetFound()
TA260 000:449.494 - 0.004ms returns 0xE0000FF
TA260 000:449.501 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
TA260 000:449.508   Value=0xE00FF000
TA260 000:449.514 - 0.014ms returns 0
TA260 000:451.329 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
TA260 000:451.351   Value=0xE00FF000
TA260 000:451.357 - 0.028ms returns 0
TA260 000:451.362 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
TA260 000:451.365   Value=0x********
TA260 000:451.371 - 0.009ms returns 0
TA260 000:451.376 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
TA260 000:451.413   CPU_ReadMem(32 bytes @ 0xE0041FD0)
TA260 000:451.982   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
TA260 000:451.989 - 0.613ms returns 32 (0x20)
TA260 000:451.995 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
TA260 000:451.998   Value=0x00000000
TA260 000:452.003 - 0.008ms returns 0
TA260 000:452.007 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
TA260 000:452.011   Value=0x********
TA260 000:452.016 - 0.008ms returns 0
TA260 000:452.020 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
TA260 000:452.023   Value=0x********
TA260 000:452.028 - 0.008ms returns 0
TA260 000:452.032 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
TA260 000:452.035   Value=0xE0001000
TA260 000:452.040 - 0.008ms returns 0
TA260 000:452.044 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
TA260 000:452.048   Value=0x********
TA260 000:452.052 - 0.008ms returns 0
TA260 000:452.057 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
TA260 000:452.060   Value=0xE000E000
TA260 000:452.065 - 0.009ms returns 0
TA260 000:452.070 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
TA260 000:452.073   Value=0xE000EDF0
TA260 000:452.078 - 0.008ms returns 0
TA260 000:452.082 JLINK_GetDebugInfo(0x01 = Unknown)
TA260 000:452.085   Value=0x00000001
TA260 000:452.090 - 0.008ms returns 0
TA260 000:452.094 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
TA260 000:452.100   CPU_ReadMem(4 bytes @ 0xE000ED00)
TA260 000:452.566   Data:  41 C2 0F 41
TA260 000:452.574   Debug reg: CPUID
TA260 000:452.579 - 0.484ms returns 1 (0x1)
TA260 000:452.584 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
TA260 000:452.592   Value=0x00000000
TA260 000:452.597 - 0.013ms returns 0
TA260 000:452.602 JLINK_HasError()
TA260 000:452.607 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
TA260 000:452.610 - 0.003ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
TA260 000:452.614 JLINK_Reset()
TA260 000:452.621   JLINK_GetResetTypeDesc
TA260 000:452.625   - 0.004ms 
TA260 000:455.236   Reset type: NORMAL (https://wiki.segger.com/J-Link_Reset_Strategies)
TA260 000:455.296   CPU is running
TA260 000:455.309   CPU_WriteMem(4 bytes @ 0xE000EDF0)
TA260 000:455.916   CPU is running
TA260 000:455.936   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:458.600   Reset: Halt core after reset via DEMCR.VC_CORERESET.
TA260 000:461.456   Reset: Reset device via AIRCR.SYSRESETREQ.
TA260 000:461.491   CPU is running
TA260 000:461.501   CPU_WriteMem(4 bytes @ 0xE000ED0C)
TA260 000:516.499   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:517.033   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:519.894   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:526.335   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:529.240   CPU_WriteMem(4 bytes @ 0x********)
TA260 000:529.747   CPU_ReadMem(4 bytes @ 0xE000EDFC)
TA260 000:530.238   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:530.707 - 78.092ms 
TA260 000:530.765 JLINK_Halt()
TA260 000:530.774 - 0.008ms returns 0x00
TA260 000:530.779 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
TA260 000:530.789   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:531.280   Data:  03 00 03 00
TA260 000:531.293   Debug reg: DHCSR
TA260 000:531.298 - 0.519ms returns 1 (0x1)
TA260 000:531.304 JLINK_WriteU32(0xE000EDF0, 0xA05F0003)
TA260 000:531.307   Debug reg: DHCSR
TA260 000:531.545   CPU_WriteMem(4 bytes @ 0xE000EDF0)
TA260 000:532.057 - 0.753ms returns 0 (0x00000000)
TA260 000:532.067 JLINK_WriteU32(0xE000EDFC, 0x01000000)
TA260 000:532.071   Debug reg: DEMCR
TA260 000:532.080   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:532.538 - 0.470ms returns 0 (0x00000000)
TA260 000:539.341 JLINK_GetHWStatus(...)
TA260 000:539.722 - 0.387ms returns 0
TA260 000:543.940 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
TA260 000:543.962 - 0.021ms returns 0x06
TA260 000:543.966 JLINK_GetNumBPUnits(Type = 0xF0)
TA260 000:543.970 - 0.003ms returns 0x2000
TA260 000:543.975 JLINK_GetNumWPUnits()
TA260 000:543.978 - 0.003ms returns 4
TA260 000:549.564 JLINK_GetSpeed()
TA260 000:549.581 - 0.016ms returns 4000
TA260 000:552.544 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
TA260 000:552.568   CPU_ReadMem(4 bytes @ 0xE000E004)
TA260 000:553.139   Data:  02 00 00 00
TA260 000:553.146 - 0.602ms returns 1 (0x1)
TA260 000:553.151 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
TA260 000:553.156   CPU_ReadMem(4 bytes @ 0xE000E004)
TA260 000:553.610   Data:  02 00 00 00
TA260 000:553.616 - 0.464ms returns 1 (0x1)
TA260 000:553.622 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
TA260 000:553.625   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
TA260 000:553.634   CPU_WriteMem(28 bytes @ 0xE0001000)
TA260 000:554.222 - 0.600ms returns 0x1C
TA260 000:554.230 JLINK_Halt()
TA260 000:554.234 - 0.003ms returns 0x00
TA260 000:554.238 JLINK_IsHalted()
TA260 000:554.242 - 0.004ms returns TRUE
TA260 000:556.797 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 000:556.812   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 000:557.070   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 000:558.945 - 2.148ms returns 0x184
TA260 000:558.989 JLINK_HasError()
TA260 000:558.995 JLINK_WriteReg(R0, 0x08000000)
TA260 000:559.001 - 0.005ms returns 0
TA260 000:559.005 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 000:559.008 - 0.003ms returns 0
TA260 000:559.013 JLINK_WriteReg(R2, 0x00000001)
TA260 000:559.016 - 0.003ms returns 0
TA260 000:559.020 JLINK_WriteReg(R3, 0x00000000)
TA260 000:559.023 - 0.003ms returns 0
TA260 000:559.027 JLINK_WriteReg(R4, 0x00000000)
TA260 000:559.031 - 0.003ms returns 0
TA260 000:559.035 JLINK_WriteReg(R5, 0x00000000)
TA260 000:559.043 - 0.008ms returns 0
TA260 000:559.049 JLINK_WriteReg(R6, 0x00000000)
TA260 000:559.052 - 0.003ms returns 0
TA260 000:559.056 JLINK_WriteReg(R7, 0x00000000)
TA260 000:559.060 - 0.003ms returns 0
TA260 000:559.074 JLINK_WriteReg(R8, 0x00000000)
TA260 000:559.078 - 0.014ms returns 0
TA260 000:559.082 JLINK_WriteReg(R9, 0x20000180)
TA260 000:559.085 - 0.003ms returns 0
TA260 000:559.090 JLINK_WriteReg(R10, 0x00000000)
TA260 000:559.094 - 0.003ms returns 0
TA260 000:559.098 JLINK_WriteReg(R11, 0x00000000)
TA260 000:559.101 - 0.003ms returns 0
TA260 000:559.105 JLINK_WriteReg(R12, 0x00000000)
TA260 000:559.109 - 0.003ms returns 0
TA260 000:559.113 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:559.118 - 0.004ms returns 0
TA260 000:559.122 JLINK_WriteReg(R14, 0x20000001)
TA260 000:559.125 - 0.003ms returns 0
TA260 000:559.134 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 000:559.137 - 0.008ms returns 0
TA260 000:559.141 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:559.145 - 0.003ms returns 0
TA260 000:559.149 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:559.152 - 0.003ms returns 0
TA260 000:559.156 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:559.160 - 0.003ms returns 0
TA260 000:559.164 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:559.167 - 0.003ms returns 0
TA260 000:559.172 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:559.179   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:559.658 - 0.486ms returns 0x00000001
TA260 000:559.665 JLINK_Go()
TA260 000:559.671   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 000:560.181   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:560.583   CPU_WriteMem(4 bytes @ 0xE0002008)
TA260 000:560.590   CPU_WriteMem(4 bytes @ 0xE000200C)
TA260 000:560.595   CPU_WriteMem(4 bytes @ 0xE0002010)
TA260 000:560.600   CPU_WriteMem(4 bytes @ 0xE0002014)
TA260 000:560.605   CPU_WriteMem(4 bytes @ 0xE0002018)
TA260 000:560.610   CPU_WriteMem(4 bytes @ 0xE000201C)
TA260 000:561.932   CPU_WriteMem(4 bytes @ 0xE0001004)
TA260 000:566.678   Memory map 'after startup completion point' is active
TA260 000:566.708 - 7.042ms 
TA260 000:566.716 JLINK_IsHalted()
TA260 000:569.072   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:569.587 - 2.870ms returns TRUE
TA260 000:569.598 JLINK_ReadReg(R15 (PC))
TA260 000:569.603 - 0.005ms returns 0x20000000
TA260 000:569.608 JLINK_ClrBPEx(BPHandle = 0x00000001)
TA260 000:569.612 - 0.003ms returns 0x00
TA260 000:569.617 JLINK_ReadReg(R0)
TA260 000:569.621 - 0.003ms returns 0x00000000
TA260 000:570.037 JLINK_HasError()
TA260 000:570.051 JLINK_WriteReg(R0, 0x08000000)
TA260 000:570.056 - 0.005ms returns 0
TA260 000:570.060 JLINK_WriteReg(R1, 0x00004000)
TA260 000:570.064 - 0.003ms returns 0
TA260 000:570.068 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:570.072 - 0.004ms returns 0
TA260 000:570.076 JLINK_WriteReg(R3, 0x00000000)
TA260 000:570.079 - 0.003ms returns 0
TA260 000:570.083 JLINK_WriteReg(R4, 0x00000000)
TA260 000:570.087 - 0.003ms returns 0
TA260 000:570.091 JLINK_WriteReg(R5, 0x00000000)
TA260 000:570.095 - 0.004ms returns 0
TA260 000:570.099 JLINK_WriteReg(R6, 0x00000000)
TA260 000:570.102 - 0.003ms returns 0
TA260 000:570.107 JLINK_WriteReg(R7, 0x00000000)
TA260 000:570.110 - 0.003ms returns 0
TA260 000:570.114 JLINK_WriteReg(R8, 0x00000000)
TA260 000:570.117 - 0.003ms returns 0
TA260 000:570.121 JLINK_WriteReg(R9, 0x20000180)
TA260 000:570.125 - 0.003ms returns 0
TA260 000:570.129 JLINK_WriteReg(R10, 0x00000000)
TA260 000:570.132 - 0.003ms returns 0
TA260 000:570.136 JLINK_WriteReg(R11, 0x00000000)
TA260 000:570.140 - 0.003ms returns 0
TA260 000:570.144 JLINK_WriteReg(R12, 0x00000000)
TA260 000:570.147 - 0.003ms returns 0
TA260 000:570.151 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:570.155 - 0.003ms returns 0
TA260 000:570.159 JLINK_WriteReg(R14, 0x20000001)
TA260 000:570.162 - 0.003ms returns 0
TA260 000:570.167 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 000:570.170 - 0.003ms returns 0
TA260 000:570.174 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:570.178 - 0.003ms returns 0
TA260 000:570.224 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:570.229 - 0.005ms returns 0
TA260 000:570.233 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:570.237 - 0.003ms returns 0
TA260 000:570.241 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:570.244 - 0.003ms returns 0
TA260 000:570.249 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:570.253 - 0.005ms returns 0x00000002
TA260 000:570.258 JLINK_Go()
TA260 000:570.267   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:572.973 - 2.715ms 
TA260 000:572.985 JLINK_IsHalted()
TA260 000:575.299   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:575.770 - 2.784ms returns TRUE
TA260 000:575.786 JLINK_ReadReg(R15 (PC))
TA260 000:575.793 - 0.007ms returns 0x20000000
TA260 000:575.798 JLINK_ClrBPEx(BPHandle = 0x00000002)
TA260 000:575.802 - 0.003ms returns 0x00
TA260 000:575.806 JLINK_ReadReg(R0)
TA260 000:575.810 - 0.003ms returns 0x00000001
TA260 000:575.815 JLINK_HasError()
TA260 000:575.821 JLINK_WriteReg(R0, 0x08000000)
TA260 000:575.825 - 0.004ms returns 0
TA260 000:575.829 JLINK_WriteReg(R1, 0x00004000)
TA260 000:575.832 - 0.003ms returns 0
TA260 000:575.837 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:575.840 - 0.003ms returns 0
TA260 000:575.844 JLINK_WriteReg(R3, 0x00000000)
TA260 000:575.847 - 0.003ms returns 0
TA260 000:575.853 JLINK_WriteReg(R4, 0x00000000)
TA260 000:575.856 - 0.003ms returns 0
TA260 000:575.860 JLINK_WriteReg(R5, 0x00000000)
TA260 000:575.864 - 0.003ms returns 0
TA260 000:575.869 JLINK_WriteReg(R6, 0x00000000)
TA260 000:575.872 - 0.003ms returns 0
TA260 000:575.876 JLINK_WriteReg(R7, 0x00000000)
TA260 000:575.880 - 0.003ms returns 0
TA260 000:575.884 JLINK_WriteReg(R8, 0x00000000)
TA260 000:575.887 - 0.003ms returns 0
TA260 000:575.891 JLINK_WriteReg(R9, 0x20000180)
TA260 000:575.894 - 0.003ms returns 0
TA260 000:575.898 JLINK_WriteReg(R10, 0x00000000)
TA260 000:575.902 - 0.003ms returns 0
TA260 000:575.906 JLINK_WriteReg(R11, 0x00000000)
TA260 000:575.909 - 0.003ms returns 0
TA260 000:575.914 JLINK_WriteReg(R12, 0x00000000)
TA260 000:575.917 - 0.003ms returns 0
TA260 000:575.921 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:575.925 - 0.003ms returns 0
TA260 000:575.929 JLINK_WriteReg(R14, 0x20000001)
TA260 000:575.932 - 0.003ms returns 0
TA260 000:575.936 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 000:575.939 - 0.003ms returns 0
TA260 000:575.945 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:575.949 - 0.004ms returns 0
TA260 000:575.953 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:575.956 - 0.003ms returns 0
TA260 000:575.960 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:575.964 - 0.003ms returns 0
TA260 000:575.968 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:575.971 - 0.003ms returns 0
TA260 000:575.975 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:575.979 - 0.004ms returns 0x00000003
TA260 000:575.983 JLINK_Go()
TA260 000:575.992   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:578.732 - 2.747ms 
TA260 000:578.756 JLINK_IsHalted()
TA260 000:579.283 - 0.526ms returns FALSE
TA260 000:579.331 JLINK_HasError()
TA260 000:587.885 JLINK_IsHalted()
TA260 000:588.389 - 0.503ms returns FALSE
TA260 000:588.407 JLINK_HasError()
TA260 000:590.395 JLINK_IsHalted()
TA260 000:590.941 - 0.545ms returns FALSE
TA260 000:590.948 JLINK_HasError()
TA260 000:592.391 JLINK_IsHalted()
TA260 000:592.881 - 0.489ms returns FALSE
TA260 000:592.887 JLINK_HasError()
TA260 000:594.391 JLINK_IsHalted()
TA260 000:594.869 - 0.477ms returns FALSE
TA260 000:594.888 JLINK_HasError()
TA260 000:597.400 JLINK_IsHalted()
TA260 000:597.889 - 0.488ms returns FALSE
TA260 000:597.906 JLINK_HasError()
TA260 000:599.897 JLINK_IsHalted()
TA260 000:600.429 - 0.531ms returns FALSE
TA260 000:600.438 JLINK_HasError()
TA260 000:601.901 JLINK_IsHalted()
TA260 000:602.382 - 0.480ms returns FALSE
TA260 000:602.388 JLINK_HasError()
TA260 000:603.899 JLINK_IsHalted()
TA260 000:604.381 - 0.481ms returns FALSE
TA260 000:604.387 JLINK_HasError()
TA260 000:605.907 JLINK_IsHalted()
TA260 000:606.395 - 0.488ms returns FALSE
TA260 000:606.411 JLINK_HasError()
TA260 000:607.906 JLINK_IsHalted()
TA260 000:608.395 - 0.488ms returns FALSE
TA260 000:608.402 JLINK_HasError()
TA260 000:610.410 JLINK_IsHalted()
TA260 000:610.875 - 0.465ms returns FALSE
TA260 000:610.882 JLINK_HasError()
TA260 000:612.412 JLINK_IsHalted()
TA260 000:612.833 - 0.421ms returns FALSE
TA260 000:612.839 JLINK_HasError()
TA260 000:614.407 JLINK_IsHalted()
TA260 000:614.898 - 0.490ms returns FALSE
TA260 000:614.914 JLINK_HasError()
TA260 000:616.415 JLINK_IsHalted()
TA260 000:616.925 - 0.509ms returns FALSE
TA260 000:616.932 JLINK_HasError()
TA260 000:618.919 JLINK_IsHalted()
TA260 000:619.383 - 0.463ms returns FALSE
TA260 000:619.389 JLINK_HasError()
TA260 000:620.917 JLINK_IsHalted()
TA260 000:621.414 - 0.496ms returns FALSE
TA260 000:621.419 JLINK_HasError()
TA260 000:622.917 JLINK_IsHalted()
TA260 000:623.412 - 0.495ms returns FALSE
TA260 000:623.418 JLINK_HasError()
TA260 000:624.921 JLINK_IsHalted()
TA260 000:625.404 - 0.482ms returns FALSE
TA260 000:625.427 JLINK_HasError()
TA260 000:626.924 JLINK_IsHalted()
TA260 000:627.386 - 0.463ms returns FALSE
TA260 000:627.396 JLINK_HasError()
TA260 000:628.922 JLINK_IsHalted()
TA260 000:629.430 - 0.507ms returns FALSE
TA260 000:629.447 JLINK_HasError()
TA260 000:631.431 JLINK_IsHalted()
TA260 000:631.872 - 0.439ms returns FALSE
TA260 000:631.883 JLINK_HasError()
TA260 000:633.429 JLINK_IsHalted()
TA260 000:633.931 - 0.501ms returns FALSE
TA260 000:633.937 JLINK_HasError()
TA260 000:635.937 JLINK_IsHalted()
TA260 000:636.422 - 0.484ms returns FALSE
TA260 000:636.435 JLINK_HasError()
TA260 000:638.938 JLINK_IsHalted()
TA260 000:639.397 - 0.458ms returns FALSE
TA260 000:639.403 JLINK_HasError()
TA260 000:640.441 JLINK_IsHalted()
TA260 000:640.963 - 0.521ms returns FALSE
TA260 000:640.971 JLINK_HasError()
TA260 000:644.365 JLINK_IsHalted()
TA260 000:644.927 - 0.562ms returns FALSE
TA260 000:644.941 JLINK_HasError()
TA260 000:646.365 JLINK_IsHalted()
TA260 000:646.818 - 0.453ms returns FALSE
TA260 000:646.830 JLINK_HasError()
TA260 000:648.869 JLINK_IsHalted()
TA260 000:649.407 - 0.537ms returns FALSE
TA260 000:649.420 JLINK_HasError()
TA260 000:650.869 JLINK_IsHalted()
TA260 000:651.382 - 0.512ms returns FALSE
TA260 000:651.389 JLINK_HasError()
TA260 000:653.873 JLINK_IsHalted()
TA260 000:654.394 - 0.520ms returns FALSE
TA260 000:654.400 JLINK_HasError()
TA260 000:655.877 JLINK_IsHalted()
TA260 000:656.421 - 0.542ms returns FALSE
TA260 000:656.436 JLINK_HasError()
TA260 000:657.876 JLINK_IsHalted()
TA260 000:658.417 - 0.540ms returns FALSE
TA260 000:658.424 JLINK_HasError()
TA260 000:660.383 JLINK_IsHalted()
TA260 000:660.831 - 0.447ms returns FALSE
TA260 000:660.846 JLINK_HasError()
TA260 000:662.383 JLINK_IsHalted()
TA260 000:662.862 - 0.479ms returns FALSE
TA260 000:662.868 JLINK_HasError()
TA260 000:664.381 JLINK_IsHalted()
TA260 000:664.866 - 0.484ms returns FALSE
TA260 000:664.879 JLINK_HasError()
TA260 000:666.386 JLINK_IsHalted()
TA260 000:666.853 - 0.465ms returns FALSE
TA260 000:666.870 JLINK_HasError()
TA260 000:668.894 JLINK_IsHalted()
TA260 000:669.409 - 0.515ms returns FALSE
TA260 000:669.423 JLINK_HasError()
TA260 000:670.896 JLINK_IsHalted()
TA260 000:671.382 - 0.485ms returns FALSE
TA260 000:671.390 JLINK_HasError()
TA260 000:673.892 JLINK_IsHalted()
TA260 000:674.386 - 0.493ms returns FALSE
TA260 000:674.395 JLINK_HasError()
TA260 000:675.893 JLINK_IsHalted()
TA260 000:676.394 - 0.500ms returns FALSE
TA260 000:676.403 JLINK_HasError()
TA260 000:677.891 JLINK_IsHalted()
TA260 000:678.391 - 0.499ms returns FALSE
TA260 000:678.398 JLINK_HasError()
TA260 000:680.399 JLINK_IsHalted()
TA260 000:680.885 - 0.485ms returns FALSE
TA260 000:680.892 JLINK_HasError()
TA260 000:682.400 JLINK_IsHalted()
TA260 000:682.833 - 0.433ms returns FALSE
TA260 000:682.843 JLINK_HasError()
TA260 000:684.399 JLINK_IsHalted()
TA260 000:684.897 - 0.498ms returns FALSE
TA260 000:684.906 JLINK_HasError()
TA260 000:686.402 JLINK_IsHalted()
TA260 000:686.969 - 0.566ms returns FALSE
TA260 000:686.977 JLINK_HasError()
TA260 000:688.910 JLINK_IsHalted()
TA260 000:689.396 - 0.484ms returns FALSE
TA260 000:689.402 JLINK_HasError()
TA260 000:696.929 JLINK_IsHalted()
TA260 000:697.448 - 0.518ms returns FALSE
TA260 000:697.461 JLINK_HasError()
TA260 000:700.421 JLINK_IsHalted()
TA260 000:700.875 - 0.454ms returns FALSE
TA260 000:700.882 JLINK_HasError()
TA260 000:702.428 JLINK_IsHalted()
TA260 000:702.927 - 0.498ms returns FALSE
TA260 000:702.933 JLINK_HasError()
TA260 000:704.421 JLINK_IsHalted()
TA260 000:704.893 - 0.471ms returns FALSE
TA260 000:704.914 JLINK_HasError()
TA260 000:707.433 JLINK_IsHalted()
TA260 000:707.977 - 0.544ms returns FALSE
TA260 000:707.989 JLINK_HasError()
TA260 000:709.947 JLINK_IsHalted()
TA260 000:710.488 - 0.539ms returns FALSE
TA260 000:710.514 JLINK_HasError()
TA260 000:711.928 JLINK_IsHalted()
TA260 000:712.390 - 0.462ms returns FALSE
TA260 000:712.396 JLINK_HasError()
TA260 000:713.930 JLINK_IsHalted()
TA260 000:714.415 - 0.485ms returns FALSE
TA260 000:714.424 JLINK_HasError()
TA260 000:715.935 JLINK_IsHalted()
TA260 000:716.398 - 0.463ms returns FALSE
TA260 000:716.405 JLINK_HasError()
TA260 000:717.930 JLINK_IsHalted()
TA260 000:718.420 - 0.489ms returns FALSE
TA260 000:718.428 JLINK_HasError()
TA260 000:720.439 JLINK_IsHalted()
TA260 000:720.953 - 0.513ms returns FALSE
TA260 000:720.960 JLINK_HasError()
TA260 000:722.436 JLINK_IsHalted()
TA260 000:722.905 - 0.469ms returns FALSE
TA260 000:722.911 JLINK_HasError()
TA260 000:724.435 JLINK_IsHalted()
TA260 000:724.933 - 0.497ms returns FALSE
TA260 000:724.947 JLINK_HasError()
TA260 000:727.441 JLINK_IsHalted()
TA260 000:727.909 - 0.467ms returns FALSE
TA260 000:727.924 JLINK_HasError()
TA260 000:729.944 JLINK_IsHalted()
TA260 000:730.429 - 0.485ms returns FALSE
TA260 000:730.437 JLINK_HasError()
TA260 000:731.947 JLINK_IsHalted()
TA260 000:732.428 - 0.480ms returns FALSE
TA260 000:732.437 JLINK_HasError()
TA260 000:733.949 JLINK_IsHalted()
TA260 000:734.428 - 0.479ms returns FALSE
TA260 000:734.437 JLINK_HasError()
TA260 000:736.464 JLINK_IsHalted()
TA260 000:736.942 - 0.478ms returns FALSE
TA260 000:736.951 JLINK_HasError()
TA260 000:738.958 JLINK_IsHalted()
TA260 000:739.472 - 0.513ms returns FALSE
TA260 000:739.478 JLINK_HasError()
TA260 000:741.957 JLINK_IsHalted()
TA260 000:742.392 - 0.434ms returns FALSE
TA260 000:742.398 JLINK_HasError()
TA260 000:743.959 JLINK_IsHalted()
TA260 000:744.448 - 0.488ms returns FALSE
TA260 000:744.458 JLINK_HasError()
TA260 000:745.961 JLINK_IsHalted()
TA260 000:746.525 - 0.563ms returns FALSE
TA260 000:746.539 JLINK_HasError()
TA260 000:747.960 JLINK_IsHalted()
TA260 000:748.384 - 0.423ms returns FALSE
TA260 000:748.390 JLINK_HasError()
TA260 000:749.461 JLINK_IsHalted()
TA260 000:749.992 - 0.530ms returns FALSE
TA260 000:750.005 JLINK_HasError()
TA260 000:751.465 JLINK_IsHalted()
TA260 000:751.953 - 0.487ms returns FALSE
TA260 000:751.961 JLINK_HasError()
TA260 000:754.141 JLINK_IsHalted()
TA260 000:754.577 - 0.435ms returns FALSE
TA260 000:754.590 JLINK_HasError()
TA260 000:756.140 JLINK_IsHalted()
TA260 000:756.578 - 0.437ms returns FALSE
TA260 000:756.592 JLINK_HasError()
TA260 000:758.167 JLINK_IsHalted()
TA260 000:758.679 - 0.511ms returns FALSE
TA260 000:758.686 JLINK_HasError()
TA260 000:760.640 JLINK_IsHalted()
TA260 000:761.143 - 0.503ms returns FALSE
TA260 000:761.149 JLINK_HasError()
TA260 000:764.647 JLINK_IsHalted()
TA260 000:765.138 - 0.491ms returns FALSE
TA260 000:765.153 JLINK_HasError()
TA260 000:766.645 JLINK_IsHalted()
TA260 000:767.124 - 0.478ms returns FALSE
TA260 000:767.131 JLINK_HasError()
TA260 000:769.148 JLINK_IsHalted()
TA260 000:769.576 - 0.428ms returns FALSE
TA260 000:769.582 JLINK_HasError()
TA260 000:772.149 JLINK_IsHalted()
TA260 000:772.612 - 0.462ms returns FALSE
TA260 000:772.618 JLINK_HasError()
TA260 000:774.148 JLINK_IsHalted()
TA260 000:774.761 - 0.612ms returns FALSE
TA260 000:774.766 JLINK_HasError()
TA260 000:776.154 JLINK_IsHalted()
TA260 000:776.670 - 0.516ms returns FALSE
TA260 000:776.683 JLINK_HasError()
TA260 000:780.656 JLINK_IsHalted()
TA260 000:781.166 - 0.510ms returns FALSE
TA260 000:781.173 JLINK_HasError()
TA260 000:782.658 JLINK_IsHalted()
TA260 000:783.126 - 0.461ms returns FALSE
TA260 000:783.132 JLINK_HasError()
TA260 000:784.660 JLINK_IsHalted()
TA260 000:785.127 - 0.467ms returns FALSE
TA260 000:785.142 JLINK_HasError()
TA260 000:786.657 JLINK_IsHalted()
TA260 000:787.160 - 0.502ms returns FALSE
TA260 000:787.167 JLINK_HasError()
TA260 000:789.161 JLINK_IsHalted()
TA260 000:789.654 - 0.493ms returns FALSE
TA260 000:789.660 JLINK_HasError()
TA260 000:791.161 JLINK_IsHalted()
TA260 000:791.614 - 0.452ms returns FALSE
TA260 000:791.620 JLINK_HasError()
TA260 000:795.172 JLINK_IsHalted()
TA260 000:796.023 - 0.851ms returns FALSE
TA260 000:796.038 JLINK_HasError()
TA260 000:797.163 JLINK_IsHalted()
TA260 000:797.730 - 0.566ms returns FALSE
TA260 000:797.740 JLINK_HasError()
TA260 000:799.164 JLINK_IsHalted()
TA260 000:799.655 - 0.491ms returns FALSE
TA260 000:799.661 JLINK_HasError()
TA260 000:801.666 JLINK_IsHalted()
TA260 000:802.155 - 0.489ms returns FALSE
TA260 000:802.161 JLINK_HasError()
TA260 000:803.668 JLINK_IsHalted()
TA260 000:804.153 - 0.485ms returns FALSE
TA260 000:804.159 JLINK_HasError()
TA260 000:805.672 JLINK_IsHalted()
TA260 000:806.178 - 0.506ms returns FALSE
TA260 000:806.185 JLINK_HasError()
TA260 000:807.673 JLINK_IsHalted()
TA260 000:808.169 - 0.495ms returns FALSE
TA260 000:808.182 JLINK_HasError()
TA260 000:810.172 JLINK_IsHalted()
TA260 000:810.610 - 0.437ms returns FALSE
TA260 000:810.616 JLINK_HasError()
TA260 000:812.173 JLINK_IsHalted()
TA260 000:812.658 - 0.484ms returns FALSE
TA260 000:812.670 JLINK_HasError()
TA260 000:815.177 JLINK_IsHalted()
TA260 000:815.613 - 0.436ms returns FALSE
TA260 000:815.626 JLINK_HasError()
TA260 000:817.180 JLINK_IsHalted()
TA260 000:817.691 - 0.509ms returns FALSE
TA260 000:817.704 JLINK_HasError()
TA260 000:819.179 JLINK_IsHalted()
TA260 000:819.703 - 0.523ms returns FALSE
TA260 000:819.710 JLINK_HasError()
TA260 000:821.679 JLINK_IsHalted()
TA260 000:822.179 - 0.500ms returns FALSE
TA260 000:822.185 JLINK_HasError()
TA260 000:823.679 JLINK_IsHalted()
TA260 000:824.181 - 0.501ms returns FALSE
TA260 000:824.186 JLINK_HasError()
TA260 000:825.682 JLINK_IsHalted()
TA260 000:826.148 - 0.465ms returns FALSE
TA260 000:826.155 JLINK_HasError()
TA260 000:827.696 JLINK_IsHalted()
TA260 000:828.204 - 0.507ms returns FALSE
TA260 000:828.217 JLINK_HasError()
TA260 000:829.419 JLINK_IsHalted()
TA260 000:829.959 - 0.539ms returns FALSE
TA260 000:829.966 JLINK_HasError()
TA260 000:831.459 JLINK_IsHalted()
TA260 000:831.926 - 0.466ms returns FALSE
TA260 000:831.931 JLINK_HasError()
TA260 000:832.985 JLINK_IsHalted()
TA260 000:833.533 - 0.547ms returns FALSE
TA260 000:833.539 JLINK_HasError()
TA260 000:835.033 JLINK_IsHalted()
TA260 000:835.535 - 0.502ms returns FALSE
TA260 000:835.553 JLINK_HasError()
TA260 000:837.028 JLINK_IsHalted()
TA260 000:837.518 - 0.489ms returns FALSE
TA260 000:837.532 JLINK_HasError()
TA260 000:838.573 JLINK_IsHalted()
TA260 000:839.079 - 0.506ms returns FALSE
TA260 000:839.085 JLINK_HasError()
TA260 000:841.082 JLINK_IsHalted()
TA260 000:841.528 - 0.446ms returns FALSE
TA260 000:841.534 JLINK_HasError()
TA260 000:843.081 JLINK_IsHalted()
TA260 000:843.577 - 0.495ms returns FALSE
TA260 000:843.585 JLINK_HasError()
TA260 000:845.085 JLINK_IsHalted()
TA260 000:845.536 - 0.450ms returns FALSE
TA260 000:845.547 JLINK_HasError()
TA260 000:847.086 JLINK_IsHalted()
TA260 000:847.520 - 0.433ms returns FALSE
TA260 000:847.527 JLINK_HasError()
TA260 000:849.086 JLINK_IsHalted()
TA260 000:849.567 - 0.480ms returns FALSE
TA260 000:849.574 JLINK_HasError()
TA260 000:851.591 JLINK_IsHalted()
TA260 000:852.081 - 0.490ms returns FALSE
TA260 000:852.087 JLINK_HasError()
TA260 000:853.590 JLINK_IsHalted()
TA260 000:854.076 - 0.485ms returns FALSE
TA260 000:854.122 JLINK_HasError()
TA260 000:855.593 JLINK_IsHalted()
TA260 000:856.020 - 0.427ms returns FALSE
TA260 000:856.028 JLINK_HasError()
TA260 000:857.593 JLINK_IsHalted()
TA260 000:858.080 - 0.486ms returns FALSE
TA260 000:858.094 JLINK_HasError()
TA260 000:860.096 JLINK_IsHalted()
TA260 000:860.640 - 0.527ms returns FALSE
TA260 000:860.650 JLINK_HasError()
TA260 000:862.094 JLINK_IsHalted()
TA260 000:862.561 - 0.467ms returns FALSE
TA260 000:862.566 JLINK_HasError()
TA260 000:864.094 JLINK_IsHalted()
TA260 000:864.562 - 0.467ms returns FALSE
TA260 000:864.569 JLINK_HasError()
TA260 000:866.098 JLINK_IsHalted()
TA260 000:866.656 - 0.557ms returns FALSE
TA260 000:866.662 JLINK_HasError()
TA260 000:868.095 JLINK_IsHalted()
TA260 000:868.613 - 0.518ms returns FALSE
TA260 000:868.627 JLINK_HasError()
TA260 000:870.605 JLINK_IsHalted()
TA260 000:871.077 - 0.472ms returns FALSE
TA260 000:871.083 JLINK_HasError()
TA260 000:872.602 JLINK_IsHalted()
TA260 000:873.074 - 0.472ms returns FALSE
TA260 000:873.079 JLINK_HasError()
TA260 000:874.601 JLINK_IsHalted()
TA260 000:875.208 - 0.605ms returns FALSE
TA260 000:875.221 JLINK_HasError()
TA260 000:876.605 JLINK_IsHalted()
TA260 000:877.114 - 0.508ms returns FALSE
TA260 000:877.121 JLINK_HasError()
TA260 000:879.108 JLINK_IsHalted()
TA260 000:879.576 - 0.468ms returns FALSE
TA260 000:879.581 JLINK_HasError()
TA260 000:881.108 JLINK_IsHalted()
TA260 000:881.611 - 0.503ms returns FALSE
TA260 000:881.617 JLINK_HasError()
TA260 000:883.108 JLINK_IsHalted()
TA260 000:883.621 - 0.512ms returns FALSE
TA260 000:883.627 JLINK_HasError()
TA260 000:885.111 JLINK_IsHalted()
TA260 000:885.534 - 0.423ms returns FALSE
TA260 000:885.570 JLINK_HasError()
TA260 000:887.108 JLINK_IsHalted()
TA260 000:887.612 - 0.503ms returns FALSE
TA260 000:887.619 JLINK_HasError()
TA260 000:889.111 JLINK_IsHalted()
TA260 000:889.575 - 0.464ms returns FALSE
TA260 000:889.581 JLINK_HasError()
TA260 000:890.613 JLINK_IsHalted()
TA260 000:891.078 - 0.464ms returns FALSE
TA260 000:891.086 JLINK_HasError()
TA260 000:892.614 JLINK_IsHalted()
TA260 000:893.109 - 0.494ms returns FALSE
TA260 000:893.114 JLINK_HasError()
TA260 000:894.614 JLINK_IsHalted()
TA260 000:895.043 - 0.428ms returns FALSE
TA260 000:895.057 JLINK_HasError()
TA260 000:896.617 JLINK_IsHalted()
TA260 000:897.156 - 0.538ms returns FALSE
TA260 000:897.169 JLINK_HasError()
TA260 000:899.121 JLINK_IsHalted()
TA260 000:899.612 - 0.491ms returns FALSE
TA260 000:899.619 JLINK_HasError()
TA260 000:901.121 JLINK_IsHalted()
TA260 000:901.622 - 0.500ms returns FALSE
TA260 000:901.627 JLINK_HasError()
TA260 000:903.120 JLINK_IsHalted()
TA260 000:903.619 - 0.498ms returns FALSE
TA260 000:903.624 JLINK_HasError()
TA260 000:905.126 JLINK_IsHalted()
TA260 000:905.615 - 0.489ms returns FALSE
TA260 000:905.628 JLINK_HasError()
TA260 000:907.126 JLINK_IsHalted()
TA260 000:907.617 - 0.490ms returns FALSE
TA260 000:907.623 JLINK_HasError()
TA260 000:909.627 JLINK_IsHalted()
TA260 000:910.112 - 0.485ms returns FALSE
TA260 000:910.121 JLINK_HasError()
TA260 000:914.630 JLINK_IsHalted()
TA260 000:915.242 - 0.612ms returns FALSE
TA260 000:915.253 JLINK_HasError()
TA260 000:919.140 JLINK_IsHalted()
TA260 000:919.692 - 0.551ms returns FALSE
TA260 000:919.700 JLINK_HasError()
TA260 000:922.138 JLINK_IsHalted()
TA260 000:922.607 - 0.468ms returns FALSE
TA260 000:922.618 JLINK_HasError()
TA260 000:925.138 JLINK_IsHalted()
TA260 000:925.568 - 0.428ms returns FALSE
TA260 000:925.582 JLINK_HasError()
TA260 000:928.138 JLINK_IsHalted()
TA260 000:928.668 - 0.530ms returns FALSE
TA260 000:928.674 JLINK_HasError()
TA260 000:930.643 JLINK_IsHalted()
TA260 000:931.155 - 0.511ms returns FALSE
TA260 000:931.161 JLINK_HasError()
TA260 000:933.644 JLINK_IsHalted()
TA260 000:934.154 - 0.510ms returns FALSE
TA260 000:934.161 JLINK_HasError()
TA260 000:936.180 JLINK_IsHalted()
TA260 000:936.621 - 0.440ms returns FALSE
TA260 000:936.636 JLINK_HasError()
TA260 000:940.655 JLINK_IsHalted()
TA260 000:941.156 - 0.500ms returns FALSE
TA260 000:941.162 JLINK_HasError()
TA260 000:943.656 JLINK_IsHalted()
TA260 000:944.155 - 0.498ms returns FALSE
TA260 000:944.163 JLINK_HasError()
TA260 000:946.659 JLINK_IsHalted()
TA260 000:947.186 - 0.526ms returns FALSE
TA260 000:947.202 JLINK_HasError()
TA260 000:952.160 JLINK_IsHalted()
TA260 000:952.691 - 0.530ms returns FALSE
TA260 000:952.698 JLINK_HasError()
TA260 000:955.165 JLINK_IsHalted()
TA260 000:955.660 - 0.494ms returns FALSE
TA260 000:955.674 JLINK_HasError()
TA260 000:958.167 JLINK_IsHalted()
TA260 000:958.691 - 0.524ms returns FALSE
TA260 000:958.698 JLINK_HasError()
TA260 000:961.669 JLINK_IsHalted()
TA260 000:962.178 - 0.509ms returns FALSE
TA260 000:962.189 JLINK_HasError()
TA260 000:965.672 JLINK_IsHalted()
TA260 000:966.119 - 0.447ms returns FALSE
TA260 000:966.126 JLINK_HasError()
TA260 000:972.179 JLINK_IsHalted()
TA260 000:972.679 - 0.500ms returns FALSE
TA260 000:972.687 JLINK_HasError()
TA260 000:976.183 JLINK_IsHalted()
TA260 000:976.702 - 0.518ms returns FALSE
TA260 000:976.717 JLINK_HasError()
TA260 000:980.693 JLINK_IsHalted()
TA260 000:981.215 - 0.521ms returns FALSE
TA260 000:981.221 JLINK_HasError()
TA260 000:984.698 JLINK_IsHalted()
TA260 000:985.212 - 0.512ms returns FALSE
TA260 000:985.235 JLINK_HasError()
TA260 000:987.690 JLINK_IsHalted()
TA260 000:988.206 - 0.515ms returns FALSE
TA260 000:988.213 JLINK_HasError()
TA260 000:990.195 JLINK_IsHalted()
TA260 000:990.666 - 0.471ms returns FALSE
TA260 000:990.672 JLINK_HasError()
TA260 000:993.194 JLINK_IsHalted()
TA260 000:995.553   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:996.046 - 2.850ms returns TRUE
TA260 000:996.053 JLINK_ReadReg(R15 (PC))
TA260 000:996.059 - 0.005ms returns 0x20000000
TA260 000:996.064 JLINK_ClrBPEx(BPHandle = 0x00000003)
TA260 000:996.068 - 0.003ms returns 0x00
TA260 000:996.073 JLINK_ReadReg(R0)
TA260 000:996.076 - 0.003ms returns 0x00000000
TA260 000:996.459 JLINK_HasError()
TA260 000:996.470 JLINK_WriteReg(R0, 0x08004000)
TA260 000:996.476 - 0.005ms returns 0
TA260 000:996.480 JLINK_WriteReg(R1, 0x00004000)
TA260 000:996.502 - 0.022ms returns 0
TA260 000:996.506 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:996.510 - 0.003ms returns 0
TA260 000:996.514 JLINK_WriteReg(R3, 0x00000000)
TA260 000:996.518 - 0.004ms returns 0
TA260 000:996.522 JLINK_WriteReg(R4, 0x00000000)
TA260 000:996.526 - 0.003ms returns 0
TA260 000:996.530 JLINK_WriteReg(R5, 0x00000000)
TA260 000:996.533 - 0.003ms returns 0
TA260 000:996.537 JLINK_WriteReg(R6, 0x00000000)
TA260 000:996.618 - 0.081ms returns 0
TA260 000:996.622 JLINK_WriteReg(R7, 0x00000000)
TA260 000:996.626 - 0.003ms returns 0
TA260 000:996.630 JLINK_WriteReg(R8, 0x00000000)
TA260 000:996.633 - 0.003ms returns 0
TA260 000:996.637 JLINK_WriteReg(R9, 0x20000180)
TA260 000:996.640 - 0.003ms returns 0
TA260 000:996.645 JLINK_WriteReg(R10, 0x00000000)
TA260 000:996.649 - 0.004ms returns 0
TA260 000:996.653 JLINK_WriteReg(R11, 0x00000000)
TA260 000:996.656 - 0.003ms returns 0
TA260 000:996.660 JLINK_WriteReg(R12, 0x00000000)
TA260 000:996.664 - 0.003ms returns 0
TA260 000:996.668 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:996.671 - 0.004ms returns 0
TA260 000:996.675 JLINK_WriteReg(R14, 0x20000001)
TA260 000:996.679 - 0.003ms returns 0
TA260 000:996.683 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 000:996.686 - 0.003ms returns 0
TA260 000:996.691 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:996.694 - 0.003ms returns 0
TA260 000:996.698 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:996.702 - 0.003ms returns 0
TA260 000:996.706 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:996.709 - 0.003ms returns 0
TA260 000:996.713 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:996.717 - 0.003ms returns 0
TA260 000:996.721 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:996.725 - 0.004ms returns 0x00000004
TA260 000:996.730 JLINK_Go()
TA260 000:996.739   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:999.434 - 2.704ms 
TA260 000:999.448 JLINK_IsHalted()
TA260 001:001.887   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:002.414 - 2.965ms returns TRUE
TA260 001:002.427 JLINK_ReadReg(R15 (PC))
TA260 001:002.432 - 0.004ms returns 0x20000000
TA260 001:002.436 JLINK_ClrBPEx(BPHandle = 0x00000004)
TA260 001:002.440 - 0.003ms returns 0x00
TA260 001:002.445 JLINK_ReadReg(R0)
TA260 001:002.448 - 0.003ms returns 0x00000001
TA260 001:002.453 JLINK_HasError()
TA260 001:002.458 JLINK_WriteReg(R0, 0x08004000)
TA260 001:002.462 - 0.003ms returns 0
TA260 001:002.466 JLINK_WriteReg(R1, 0x00004000)
TA260 001:002.469 - 0.003ms returns 0
TA260 001:002.473 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:002.476 - 0.003ms returns 0
TA260 001:002.480 JLINK_WriteReg(R3, 0x00000000)
TA260 001:002.484 - 0.003ms returns 0
TA260 001:002.488 JLINK_WriteReg(R4, 0x00000000)
TA260 001:002.491 - 0.003ms returns 0
TA260 001:002.495 JLINK_WriteReg(R5, 0x00000000)
TA260 001:002.499 - 0.003ms returns 0
TA260 001:002.503 JLINK_WriteReg(R6, 0x00000000)
TA260 001:002.506 - 0.003ms returns 0
TA260 001:002.510 JLINK_WriteReg(R7, 0x00000000)
TA260 001:002.514 - 0.003ms returns 0
TA260 001:002.518 JLINK_WriteReg(R8, 0x00000000)
TA260 001:002.521 - 0.003ms returns 0
TA260 001:002.525 JLINK_WriteReg(R9, 0x20000180)
TA260 001:002.528 - 0.003ms returns 0
TA260 001:002.532 JLINK_WriteReg(R10, 0x00000000)
TA260 001:002.536 - 0.003ms returns 0
TA260 001:002.540 JLINK_WriteReg(R11, 0x00000000)
TA260 001:002.543 - 0.003ms returns 0
TA260 001:002.547 JLINK_WriteReg(R12, 0x00000000)
TA260 001:002.551 - 0.003ms returns 0
TA260 001:002.555 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:002.558 - 0.003ms returns 0
TA260 001:002.562 JLINK_WriteReg(R14, 0x20000001)
TA260 001:002.566 - 0.003ms returns 0
TA260 001:002.570 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 001:002.573 - 0.003ms returns 0
TA260 001:002.577 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:002.581 - 0.003ms returns 0
TA260 001:002.585 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:002.588 - 0.003ms returns 0
TA260 001:002.592 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:002.595 - 0.003ms returns 0
TA260 001:002.599 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:002.603 - 0.003ms returns 0
TA260 001:002.607 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:002.611 - 0.004ms returns 0x00000005
TA260 001:002.615 JLINK_Go()
TA260 001:002.623   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:005.415 - 2.799ms 
TA260 001:005.441 JLINK_IsHalted()
TA260 001:005.956 - 0.515ms returns FALSE
TA260 001:005.969 JLINK_HasError()
TA260 001:012.212 JLINK_IsHalted()
TA260 001:012.761 - 0.548ms returns FALSE
TA260 001:012.772 JLINK_HasError()
TA260 001:015.211 JLINK_IsHalted()
TA260 001:015.754 - 0.541ms returns FALSE
TA260 001:015.768 JLINK_HasError()
TA260 001:019.213 JLINK_IsHalted()
TA260 001:019.745 - 0.531ms returns FALSE
TA260 001:019.760 JLINK_HasError()
TA260 001:021.711 JLINK_IsHalted()
TA260 001:022.211 - 0.499ms returns FALSE
TA260 001:022.218 JLINK_HasError()
TA260 001:024.713 JLINK_IsHalted()
TA260 001:025.262 - 0.548ms returns FALSE
TA260 001:025.275 JLINK_HasError()
TA260 001:026.727 JLINK_IsHalted()
TA260 001:027.206 - 0.478ms returns FALSE
TA260 001:027.216 JLINK_HasError()
TA260 001:029.218 JLINK_IsHalted()
TA260 001:029.746 - 0.527ms returns FALSE
TA260 001:029.752 JLINK_HasError()
TA260 001:031.218 JLINK_IsHalted()
TA260 001:031.691 - 0.473ms returns FALSE
TA260 001:031.697 JLINK_HasError()
TA260 001:033.220 JLINK_IsHalted()
TA260 001:033.703 - 0.483ms returns FALSE
TA260 001:033.709 JLINK_HasError()
TA260 001:035.728 JLINK_IsHalted()
TA260 001:036.204 - 0.475ms returns FALSE
TA260 001:036.210 JLINK_HasError()
TA260 001:037.768 JLINK_IsHalted()
TA260 001:038.260 - 0.492ms returns FALSE
TA260 001:038.267 JLINK_HasError()
TA260 001:040.234 JLINK_IsHalted()
TA260 001:040.724 - 0.489ms returns FALSE
TA260 001:040.732 JLINK_HasError()
TA260 001:042.231 JLINK_IsHalted()
TA260 001:042.712 - 0.480ms returns FALSE
TA260 001:042.717 JLINK_HasError()
TA260 001:044.234 JLINK_IsHalted()
TA260 001:044.760 - 0.526ms returns FALSE
TA260 001:044.773 JLINK_HasError()
TA260 001:046.238 JLINK_IsHalted()
TA260 001:046.726 - 0.487ms returns FALSE
TA260 001:046.734 JLINK_HasError()
TA260 001:048.240 JLINK_IsHalted()
TA260 001:048.726 - 0.486ms returns FALSE
TA260 001:048.739 JLINK_HasError()
TA260 001:050.746 JLINK_IsHalted()
TA260 001:051.243 - 0.496ms returns FALSE
TA260 001:051.253 JLINK_HasError()
TA260 001:052.748 JLINK_IsHalted()
TA260 001:053.195 - 0.446ms returns FALSE
TA260 001:053.208 JLINK_HasError()
TA260 001:054.780 JLINK_IsHalted()
TA260 001:055.399 - 0.619ms returns FALSE
TA260 001:055.414 JLINK_HasError()
TA260 001:056.751 JLINK_IsHalted()
TA260 001:057.210 - 0.458ms returns FALSE
TA260 001:057.225 JLINK_HasError()
TA260 001:059.264 JLINK_IsHalted()
TA260 001:059.754 - 0.489ms returns FALSE
TA260 001:059.767 JLINK_HasError()
TA260 001:065.266 JLINK_IsHalted()
TA260 001:065.810 - 0.542ms returns FALSE
TA260 001:065.826 JLINK_HasError()
TA260 001:069.767 JLINK_IsHalted()
TA260 001:070.312 - 0.544ms returns FALSE
TA260 001:070.322 JLINK_HasError()
TA260 001:076.774 JLINK_IsHalted()
TA260 001:077.324 - 0.549ms returns FALSE
TA260 001:077.333 JLINK_HasError()
TA260 001:081.274 JLINK_IsHalted()
TA260 001:081.782 - 0.507ms returns FALSE
TA260 001:081.789 JLINK_HasError()
TA260 001:084.273 JLINK_IsHalted()
TA260 001:084.820 - 0.546ms returns FALSE
TA260 001:084.833 JLINK_HasError()
TA260 001:088.786 JLINK_IsHalted()
TA260 001:089.295 - 0.508ms returns FALSE
TA260 001:089.307 JLINK_HasError()
TA260 001:092.782 JLINK_IsHalted()
TA260 001:093.317 - 0.535ms returns FALSE
TA260 001:093.324 JLINK_HasError()
TA260 001:100.291 JLINK_IsHalted()
TA260 001:100.770 - 0.478ms returns FALSE
TA260 001:100.777 JLINK_HasError()
TA260 001:104.290 JLINK_IsHalted()
TA260 001:104.785 - 0.495ms returns FALSE
TA260 001:104.799 JLINK_HasError()
TA260 001:109.795 JLINK_IsHalted()
TA260 001:110.421 - 0.625ms returns FALSE
TA260 001:110.433 JLINK_HasError()
TA260 001:112.797 JLINK_IsHalted()
TA260 001:113.303 - 0.506ms returns FALSE
TA260 001:113.310 JLINK_HasError()
TA260 001:116.800 JLINK_IsHalted()
TA260 001:117.322 - 0.521ms returns FALSE
TA260 001:117.339 JLINK_HasError()
TA260 001:122.308 JLINK_IsHalted()
TA260 001:122.803 - 0.494ms returns FALSE
TA260 001:122.816 JLINK_HasError()
TA260 001:128.814 JLINK_IsHalted()
TA260 001:129.411 - 0.596ms returns FALSE
TA260 001:129.417 JLINK_HasError()
TA260 001:131.809 JLINK_IsHalted()
TA260 001:132.303 - 0.493ms returns FALSE
TA260 001:132.309 JLINK_HasError()
TA260 001:136.321 JLINK_IsHalted()
TA260 001:136.855 - 0.534ms returns FALSE
TA260 001:136.870 JLINK_HasError()
TA260 001:139.821 JLINK_IsHalted()
TA260 001:140.319 - 0.497ms returns FALSE
TA260 001:140.331 JLINK_HasError()
TA260 001:150.337 JLINK_IsHalted()
TA260 001:150.851 - 0.514ms returns FALSE
TA260 001:150.863 JLINK_HasError()
TA260 001:154.333 JLINK_IsHalted()
TA260 001:154.869 - 0.535ms returns FALSE
TA260 001:154.883 JLINK_HasError()
TA260 001:157.338 JLINK_IsHalted()
TA260 001:157.901 - 0.562ms returns FALSE
TA260 001:157.913 JLINK_HasError()
TA260 001:161.837 JLINK_IsHalted()
TA260 001:162.415 - 0.576ms returns FALSE
TA260 001:162.422 JLINK_HasError()
TA260 001:164.841 JLINK_IsHalted()
TA260 001:165.400 - 0.558ms returns FALSE
TA260 001:165.414 JLINK_HasError()
TA260 001:166.856 JLINK_IsHalted()
TA260 001:167.307 - 0.450ms returns FALSE
TA260 001:167.320 JLINK_HasError()
TA260 001:168.844 JLINK_IsHalted()
TA260 001:169.313 - 0.468ms returns FALSE
TA260 001:169.320 JLINK_HasError()
TA260 001:172.349 JLINK_IsHalted()
TA260 001:172.827 - 0.478ms returns FALSE
TA260 001:172.833 JLINK_HasError()
TA260 001:175.349 JLINK_IsHalted()
TA260 001:175.854 - 0.504ms returns FALSE
TA260 001:175.868 JLINK_HasError()
TA260 001:178.856 JLINK_IsHalted()
TA260 001:179.414 - 0.558ms returns FALSE
TA260 001:179.420 JLINK_HasError()
TA260 001:181.855 JLINK_IsHalted()
TA260 001:182.407 - 0.551ms returns FALSE
TA260 001:182.413 JLINK_HasError()
TA260 001:185.861 JLINK_IsHalted()
TA260 001:186.412 - 0.550ms returns FALSE
TA260 001:186.501 JLINK_HasError()
TA260 001:188.862 JLINK_IsHalted()
TA260 001:189.406 - 0.543ms returns FALSE
TA260 001:189.416 JLINK_HasError()
TA260 001:191.360 JLINK_IsHalted()
TA260 001:191.828 - 0.468ms returns FALSE
TA260 001:191.834 JLINK_HasError()
TA260 001:194.363 JLINK_IsHalted()
TA260 001:194.866 - 0.503ms returns FALSE
TA260 001:194.879 JLINK_HasError()
TA260 001:196.380 JLINK_IsHalted()
TA260 001:196.850 - 0.469ms returns FALSE
TA260 001:196.857 JLINK_HasError()
TA260 001:198.869 JLINK_IsHalted()
TA260 001:199.408 - 0.538ms returns FALSE
TA260 001:199.414 JLINK_HasError()
TA260 001:201.866 JLINK_IsHalted()
TA260 001:202.407 - 0.540ms returns FALSE
TA260 001:202.413 JLINK_HasError()
TA260 001:204.867 JLINK_IsHalted()
TA260 001:205.409 - 0.542ms returns FALSE
TA260 001:205.420 JLINK_HasError()
TA260 001:207.872 JLINK_IsHalted()
TA260 001:208.396 - 0.523ms returns FALSE
TA260 001:208.402 JLINK_HasError()
TA260 001:210.373 JLINK_IsHalted()
TA260 001:210.859 - 0.485ms returns FALSE
TA260 001:210.865 JLINK_HasError()
TA260 001:213.373 JLINK_IsHalted()
TA260 001:213.918 - 0.544ms returns FALSE
TA260 001:213.926 JLINK_HasError()
TA260 001:215.380 JLINK_IsHalted()
TA260 001:215.833 - 0.453ms returns FALSE
TA260 001:215.841 JLINK_HasError()
TA260 001:217.382 JLINK_IsHalted()
TA260 001:217.848 - 0.465ms returns FALSE
TA260 001:217.854 JLINK_HasError()
TA260 001:219.882 JLINK_IsHalted()
TA260 001:220.396 - 0.513ms returns FALSE
TA260 001:220.407 JLINK_HasError()
TA260 001:221.879 JLINK_IsHalted()
TA260 001:222.404 - 0.525ms returns FALSE
TA260 001:222.411 JLINK_HasError()
TA260 001:224.881 JLINK_IsHalted()
TA260 001:225.393 - 0.511ms returns FALSE
TA260 001:225.400 JLINK_HasError()
TA260 001:227.885 JLINK_IsHalted()
TA260 001:228.390 - 0.505ms returns FALSE
TA260 001:228.406 JLINK_HasError()
TA260 001:230.386 JLINK_IsHalted()
TA260 001:230.850 - 0.463ms returns FALSE
TA260 001:230.860 JLINK_HasError()
TA260 001:232.388 JLINK_IsHalted()
TA260 001:232.836 - 0.448ms returns FALSE
TA260 001:232.842 JLINK_HasError()
TA260 001:234.387 JLINK_IsHalted()
TA260 001:234.865 - 0.477ms returns FALSE
TA260 001:234.877 JLINK_HasError()
TA260 001:236.896 JLINK_IsHalted()
TA260 001:237.385 - 0.488ms returns FALSE
TA260 001:237.393 JLINK_HasError()
TA260 001:238.893 JLINK_IsHalted()
TA260 001:239.394 - 0.501ms returns FALSE
TA260 001:239.431 JLINK_HasError()
TA260 001:241.399 JLINK_IsHalted()
TA260 001:241.881 - 0.482ms returns FALSE
TA260 001:241.887 JLINK_HasError()
TA260 001:243.399 JLINK_IsHalted()
TA260 001:243.893 - 0.493ms returns FALSE
TA260 001:243.899 JLINK_HasError()
TA260 001:245.400 JLINK_IsHalted()
TA260 001:245.866 - 0.464ms returns FALSE
TA260 001:245.879 JLINK_HasError()
TA260 001:247.405 JLINK_IsHalted()
TA260 001:247.938 - 0.532ms returns FALSE
TA260 001:247.951 JLINK_HasError()
TA260 001:249.963 JLINK_IsHalted()
TA260 001:250.461 - 0.497ms returns FALSE
TA260 001:250.473 JLINK_HasError()
TA260 001:252.908 JLINK_IsHalted()
TA260 001:253.384 - 0.476ms returns FALSE
TA260 001:253.390 JLINK_HasError()
TA260 001:254.906 JLINK_IsHalted()
TA260 001:255.396 - 0.489ms returns FALSE
TA260 001:255.407 JLINK_HasError()
TA260 001:256.906 JLINK_IsHalted()
TA260 001:257.426 - 0.519ms returns FALSE
TA260 001:257.433 JLINK_HasError()
TA260 001:258.911 JLINK_IsHalted()
TA260 001:259.432 - 0.520ms returns FALSE
TA260 001:259.445 JLINK_HasError()
TA260 001:261.411 JLINK_IsHalted()
TA260 001:261.906 - 0.494ms returns FALSE
TA260 001:261.912 JLINK_HasError()
TA260 001:263.413 JLINK_IsHalted()
TA260 001:263.903 - 0.490ms returns FALSE
TA260 001:263.910 JLINK_HasError()
TA260 001:266.419 JLINK_IsHalted()
TA260 001:266.945 - 0.525ms returns FALSE
TA260 001:266.953 JLINK_HasError()
TA260 001:269.920 JLINK_IsHalted()
TA260 001:270.429 - 0.508ms returns FALSE
TA260 001:270.435 JLINK_HasError()
TA260 001:271.916 JLINK_IsHalted()
TA260 001:272.425 - 0.508ms returns FALSE
TA260 001:272.435 JLINK_HasError()
TA260 001:274.929 JLINK_IsHalted()
TA260 001:275.537 - 0.607ms returns FALSE
TA260 001:275.550 JLINK_HasError()
TA260 001:277.920 JLINK_IsHalted()
TA260 001:278.415 - 0.494ms returns FALSE
TA260 001:278.421 JLINK_HasError()
TA260 001:280.427 JLINK_IsHalted()
TA260 001:280.928 - 0.501ms returns FALSE
TA260 001:280.936 JLINK_HasError()
TA260 001:282.427 JLINK_IsHalted()
TA260 001:282.927 - 0.500ms returns FALSE
TA260 001:282.933 JLINK_HasError()
TA260 001:284.424 JLINK_IsHalted()
TA260 001:284.964 - 0.539ms returns FALSE
TA260 001:284.978 JLINK_HasError()
TA260 001:286.429 JLINK_IsHalted()
TA260 001:286.973 - 0.543ms returns FALSE
TA260 001:286.980 JLINK_HasError()
TA260 001:288.933 JLINK_IsHalted()
TA260 001:289.532 - 0.599ms returns FALSE
TA260 001:289.538 JLINK_HasError()
TA260 001:290.935 JLINK_IsHalted()
TA260 001:291.419 - 0.484ms returns FALSE
TA260 001:291.425 JLINK_HasError()
TA260 001:292.932 JLINK_IsHalted()
TA260 001:293.389 - 0.457ms returns FALSE
TA260 001:293.394 JLINK_HasError()
TA260 001:295.936 JLINK_IsHalted()
TA260 001:296.403 - 0.466ms returns FALSE
TA260 001:296.416 JLINK_HasError()
TA260 001:297.934 JLINK_IsHalted()
TA260 001:298.424 - 0.489ms returns FALSE
TA260 001:298.440 JLINK_HasError()
TA260 001:300.441 JLINK_IsHalted()
TA260 001:300.951 - 0.510ms returns FALSE
TA260 001:300.957 JLINK_HasError()
TA260 001:302.438 JLINK_IsHalted()
TA260 001:302.907 - 0.468ms returns FALSE
TA260 001:302.913 JLINK_HasError()
TA260 001:304.438 JLINK_IsHalted()
TA260 001:304.937 - 0.498ms returns FALSE
TA260 001:304.958 JLINK_HasError()
TA260 001:306.452 JLINK_IsHalted()
TA260 001:307.009 - 0.555ms returns FALSE
TA260 001:307.023 JLINK_HasError()
TA260 001:308.944 JLINK_IsHalted()
TA260 001:309.456 - 0.511ms returns FALSE
TA260 001:309.461 JLINK_HasError()
TA260 001:310.944 JLINK_IsHalted()
TA260 001:311.417 - 0.472ms returns FALSE
TA260 001:311.423 JLINK_HasError()
TA260 001:312.947 JLINK_IsHalted()
TA260 001:313.430 - 0.483ms returns FALSE
TA260 001:313.437 JLINK_HasError()
TA260 001:314.953 JLINK_IsHalted()
TA260 001:315.536 - 0.582ms returns FALSE
TA260 001:315.556 JLINK_HasError()
TA260 001:316.948 JLINK_IsHalted()
TA260 001:317.417 - 0.468ms returns FALSE
TA260 001:317.423 JLINK_HasError()
TA260 001:318.951 JLINK_IsHalted()
TA260 001:319.431 - 0.479ms returns FALSE
TA260 001:319.437 JLINK_HasError()
TA260 001:321.450 JLINK_IsHalted()
TA260 001:321.911 - 0.461ms returns FALSE
TA260 001:321.917 JLINK_HasError()
TA260 001:323.449 JLINK_IsHalted()
TA260 001:323.916 - 0.466ms returns FALSE
TA260 001:323.922 JLINK_HasError()
TA260 001:325.462 JLINK_IsHalted()
TA260 001:325.921 - 0.459ms returns FALSE
TA260 001:325.935 JLINK_HasError()
TA260 001:327.454 JLINK_IsHalted()
TA260 001:327.976 - 0.522ms returns FALSE
TA260 001:327.986 JLINK_HasError()
TA260 001:329.958 JLINK_IsHalted()
TA260 001:330.429 - 0.470ms returns FALSE
TA260 001:330.436 JLINK_HasError()
TA260 001:331.959 JLINK_IsHalted()
TA260 001:332.458 - 0.498ms returns FALSE
TA260 001:332.465 JLINK_HasError()
TA260 001:333.959 JLINK_IsHalted()
TA260 001:334.473 - 0.513ms returns FALSE
TA260 001:334.479 JLINK_HasError()
TA260 001:336.473 JLINK_IsHalted()
TA260 001:336.999 - 0.526ms returns FALSE
TA260 001:337.005 JLINK_HasError()
TA260 001:340.971 JLINK_IsHalted()
TA260 001:341.458 - 0.486ms returns FALSE
TA260 001:341.463 JLINK_HasError()
TA260 001:343.972 JLINK_IsHalted()
TA260 001:344.518 - 0.546ms returns FALSE
TA260 001:344.524 JLINK_HasError()
TA260 001:346.987 JLINK_IsHalted()
TA260 001:347.479 - 0.492ms returns FALSE
TA260 001:347.493 JLINK_HasError()
TA260 001:348.977 JLINK_IsHalted()
TA260 001:349.532 - 0.555ms returns FALSE
TA260 001:349.539 JLINK_HasError()
TA260 001:352.479 JLINK_IsHalted()
TA260 001:353.008 - 0.529ms returns FALSE
TA260 001:353.014 JLINK_HasError()
TA260 001:354.477 JLINK_IsHalted()
TA260 001:354.993 - 0.515ms returns FALSE
TA260 001:355.006 JLINK_HasError()
TA260 001:358.988 JLINK_IsHalted()
TA260 001:359.549 - 0.560ms returns FALSE
TA260 001:359.566 JLINK_HasError()
TA260 001:363.995 JLINK_IsHalted()
TA260 001:364.539 - 0.545ms returns FALSE
TA260 001:364.554 JLINK_HasError()
TA260 001:368.044 JLINK_IsHalted()
TA260 001:368.536 - 0.491ms returns FALSE
TA260 001:368.543 JLINK_HasError()
TA260 001:370.541 JLINK_IsHalted()
TA260 001:371.031 - 0.490ms returns FALSE
TA260 001:371.037 JLINK_HasError()
TA260 001:372.541 JLINK_IsHalted()
TA260 001:373.029 - 0.487ms returns FALSE
TA260 001:373.035 JLINK_HasError()
TA260 001:375.548 JLINK_IsHalted()
TA260 001:376.023 - 0.474ms returns FALSE
TA260 001:376.031 JLINK_HasError()
TA260 001:380.051 JLINK_IsHalted()
TA260 001:380.622 - 0.569ms returns FALSE
TA260 001:380.636 JLINK_HasError()
TA260 001:383.052 JLINK_IsHalted()
TA260 001:383.532 - 0.479ms returns FALSE
TA260 001:383.538 JLINK_HasError()
TA260 001:386.054 JLINK_IsHalted()
TA260 001:386.536 - 0.481ms returns FALSE
TA260 001:386.556 JLINK_HasError()
TA260 001:389.059 JLINK_IsHalted()
TA260 001:389.535 - 0.476ms returns FALSE
TA260 001:389.542 JLINK_HasError()
TA260 001:392.559 JLINK_IsHalted()
TA260 001:393.055 - 0.496ms returns FALSE
TA260 001:393.062 JLINK_HasError()
TA260 001:395.560 JLINK_IsHalted()
TA260 001:396.141 - 0.580ms returns FALSE
TA260 001:396.155 JLINK_HasError()
TA260 001:399.064 JLINK_IsHalted()
TA260 001:399.617 - 0.553ms returns FALSE
TA260 001:399.623 JLINK_HasError()
TA260 001:403.066 JLINK_IsHalted()
TA260 001:403.565 - 0.498ms returns FALSE
TA260 001:403.571 JLINK_HasError()
TA260 001:406.073 JLINK_IsHalted()
TA260 001:406.598 - 0.525ms returns FALSE
TA260 001:406.605 JLINK_HasError()
TA260 001:408.066 JLINK_IsHalted()
TA260 001:408.533 - 0.466ms returns FALSE
TA260 001:408.539 JLINK_HasError()
TA260 001:411.571 JLINK_IsHalted()
TA260 001:412.057 - 0.485ms returns FALSE
TA260 001:412.064 JLINK_HasError()
TA260 001:413.572 JLINK_IsHalted()
TA260 001:414.069 - 0.496ms returns FALSE
TA260 001:414.075 JLINK_HasError()
TA260 001:415.580 JLINK_IsHalted()
TA260 001:416.063 - 0.482ms returns FALSE
TA260 001:416.081 JLINK_HasError()
TA260 001:417.571 JLINK_IsHalted()
TA260 001:418.037 - 0.465ms returns FALSE
TA260 001:418.050 JLINK_HasError()
TA260 001:420.074 JLINK_IsHalted()
TA260 001:422.410   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:422.909 - 2.835ms returns TRUE
TA260 001:422.916 JLINK_ReadReg(R15 (PC))
TA260 001:422.922 - 0.005ms returns 0x20000000
TA260 001:422.926 JLINK_ClrBPEx(BPHandle = 0x00000005)
TA260 001:422.930 - 0.003ms returns 0x00
TA260 001:422.934 JLINK_ReadReg(R0)
TA260 001:422.938 - 0.003ms returns 0x00000000
TA260 001:423.287 JLINK_HasError()
TA260 001:423.296 JLINK_WriteReg(R0, 0x08008000)
TA260 001:423.301 - 0.004ms returns 0
TA260 001:423.305 JLINK_WriteReg(R1, 0x00004000)
TA260 001:423.308 - 0.003ms returns 0
TA260 001:423.313 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:423.316 - 0.003ms returns 0
TA260 001:423.320 JLINK_WriteReg(R3, 0x00000000)
TA260 001:423.324 - 0.003ms returns 0
TA260 001:423.328 JLINK_WriteReg(R4, 0x00000000)
TA260 001:423.331 - 0.003ms returns 0
TA260 001:423.335 JLINK_WriteReg(R5, 0x00000000)
TA260 001:423.339 - 0.003ms returns 0
TA260 001:423.343 JLINK_WriteReg(R6, 0x00000000)
TA260 001:423.346 - 0.003ms returns 0
TA260 001:423.350 JLINK_WriteReg(R7, 0x00000000)
TA260 001:423.354 - 0.003ms returns 0
TA260 001:423.358 JLINK_WriteReg(R8, 0x00000000)
TA260 001:423.361 - 0.003ms returns 0
TA260 001:423.365 JLINK_WriteReg(R9, 0x20000180)
TA260 001:423.369 - 0.003ms returns 0
TA260 001:423.373 JLINK_WriteReg(R10, 0x00000000)
TA260 001:423.376 - 0.003ms returns 0
TA260 001:423.385 JLINK_WriteReg(R11, 0x00000000)
TA260 001:423.389 - 0.003ms returns 0
TA260 001:423.393 JLINK_WriteReg(R12, 0x00000000)
TA260 001:423.396 - 0.003ms returns 0
TA260 001:423.400 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:423.404 - 0.003ms returns 0
TA260 001:423.408 JLINK_WriteReg(R14, 0x20000001)
TA260 001:423.412 - 0.003ms returns 0
TA260 001:423.416 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 001:423.424 - 0.008ms returns 0
TA260 001:423.429 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:423.432 - 0.003ms returns 0
TA260 001:423.436 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:423.439 - 0.003ms returns 0
TA260 001:423.443 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:423.447 - 0.003ms returns 0
TA260 001:423.451 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:423.454 - 0.003ms returns 0
TA260 001:423.459 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:423.463 - 0.004ms returns 0x00000006
TA260 001:423.467 JLINK_Go()
TA260 001:423.476   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:426.208 - 2.740ms 
TA260 001:426.226 JLINK_IsHalted()
TA260 001:428.615   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:429.099 - 2.873ms returns TRUE
TA260 001:429.106 JLINK_ReadReg(R15 (PC))
TA260 001:429.112 - 0.005ms returns 0x20000000
TA260 001:429.116 JLINK_ClrBPEx(BPHandle = 0x00000006)
TA260 001:429.120 - 0.003ms returns 0x00
TA260 001:429.124 JLINK_ReadReg(R0)
TA260 001:429.128 - 0.004ms returns 0x00000001
TA260 001:429.133 JLINK_HasError()
TA260 001:429.138 JLINK_WriteReg(R0, 0x08008000)
TA260 001:429.142 - 0.003ms returns 0
TA260 001:429.146 JLINK_WriteReg(R1, 0x00004000)
TA260 001:429.149 - 0.003ms returns 0
TA260 001:429.153 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:429.156 - 0.003ms returns 0
TA260 001:429.160 JLINK_WriteReg(R3, 0x00000000)
TA260 001:429.164 - 0.003ms returns 0
TA260 001:429.168 JLINK_WriteReg(R4, 0x00000000)
TA260 001:429.171 - 0.003ms returns 0
TA260 001:429.176 JLINK_WriteReg(R5, 0x00000000)
TA260 001:429.179 - 0.003ms returns 0
TA260 001:429.183 JLINK_WriteReg(R6, 0x00000000)
TA260 001:429.187 - 0.003ms returns 0
TA260 001:429.191 JLINK_WriteReg(R7, 0x00000000)
TA260 001:429.194 - 0.003ms returns 0
TA260 001:429.198 JLINK_WriteReg(R8, 0x00000000)
TA260 001:429.201 - 0.003ms returns 0
TA260 001:429.205 JLINK_WriteReg(R9, 0x20000180)
TA260 001:429.209 - 0.003ms returns 0
TA260 001:429.213 JLINK_WriteReg(R10, 0x00000000)
TA260 001:429.216 - 0.003ms returns 0
TA260 001:429.220 JLINK_WriteReg(R11, 0x00000000)
TA260 001:429.224 - 0.003ms returns 0
TA260 001:429.228 JLINK_WriteReg(R12, 0x00000000)
TA260 001:429.231 - 0.003ms returns 0
TA260 001:429.235 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:429.239 - 0.003ms returns 0
TA260 001:429.243 JLINK_WriteReg(R14, 0x20000001)
TA260 001:429.246 - 0.003ms returns 0
TA260 001:429.250 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 001:429.254 - 0.003ms returns 0
TA260 001:429.258 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:429.261 - 0.003ms returns 0
TA260 001:429.265 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:429.269 - 0.003ms returns 0
TA260 001:429.273 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:429.276 - 0.003ms returns 0
TA260 001:429.280 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:429.284 - 0.003ms returns 0
TA260 001:429.288 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:429.292 - 0.004ms returns 0x00000007
TA260 001:429.296 JLINK_Go()
TA260 001:429.303   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:432.133 - 2.836ms 
TA260 001:432.141 JLINK_IsHalted()
TA260 001:432.629 - 0.488ms returns FALSE
TA260 001:432.635 JLINK_HasError()
TA260 001:434.095 JLINK_IsHalted()
TA260 001:434.614 - 0.518ms returns FALSE
TA260 001:434.620 JLINK_HasError()
TA260 001:436.374 JLINK_IsHalted()
TA260 001:436.867 - 0.492ms returns FALSE
TA260 001:436.879 JLINK_HasError()
TA260 001:438.879 JLINK_IsHalted()
TA260 001:439.384 - 0.504ms returns FALSE
TA260 001:439.390 JLINK_HasError()
TA260 001:441.382 JLINK_IsHalted()
TA260 001:441.862 - 0.479ms returns FALSE
TA260 001:441.873 JLINK_HasError()
TA260 001:443.379 JLINK_IsHalted()
TA260 001:443.872 - 0.492ms returns FALSE
TA260 001:443.878 JLINK_HasError()
TA260 001:445.394 JLINK_IsHalted()
TA260 001:445.863 - 0.468ms returns FALSE
TA260 001:445.878 JLINK_HasError()
TA260 001:447.392 JLINK_IsHalted()
TA260 001:447.941 - 0.548ms returns FALSE
TA260 001:447.954 JLINK_HasError()
TA260 001:450.892 JLINK_IsHalted()
TA260 001:451.415 - 0.522ms returns FALSE
TA260 001:451.422 JLINK_HasError()
TA260 001:453.891 JLINK_IsHalted()
TA260 001:454.408 - 0.516ms returns FALSE
TA260 001:454.414 JLINK_HasError()
TA260 001:456.897 JLINK_IsHalted()
TA260 001:457.430 - 0.532ms returns FALSE
TA260 001:457.442 JLINK_HasError()
TA260 001:459.399 JLINK_IsHalted()
TA260 001:459.851 - 0.450ms returns FALSE
TA260 001:459.858 JLINK_HasError()
TA260 001:463.398 JLINK_IsHalted()
TA260 001:463.911 - 0.512ms returns FALSE
TA260 001:463.924 JLINK_HasError()
TA260 001:466.405 JLINK_IsHalted()
TA260 001:466.969 - 0.563ms returns FALSE
TA260 001:466.981 JLINK_HasError()
TA260 001:472.909 JLINK_IsHalted()
TA260 001:473.416 - 0.507ms returns FALSE
TA260 001:473.423 JLINK_HasError()
TA260 001:475.910 JLINK_IsHalted()
TA260 001:476.413 - 0.502ms returns FALSE
TA260 001:476.421 JLINK_HasError()
TA260 001:478.915 JLINK_IsHalted()
TA260 001:479.464 - 0.549ms returns FALSE
TA260 001:479.482 JLINK_HasError()
TA260 001:482.420 JLINK_IsHalted()
TA260 001:482.910 - 0.490ms returns FALSE
TA260 001:482.920 JLINK_HasError()
TA260 001:487.419 JLINK_IsHalted()
TA260 001:487.934 - 0.515ms returns FALSE
TA260 001:487.942 JLINK_HasError()
TA260 001:489.936 JLINK_IsHalted()
TA260 001:490.520 - 0.583ms returns FALSE
TA260 001:490.527 JLINK_HasError()
TA260 001:492.922 JLINK_IsHalted()
TA260 001:493.429 - 0.506ms returns FALSE
TA260 001:493.435 JLINK_HasError()
TA260 001:495.925 JLINK_IsHalted()
TA260 001:496.469 - 0.543ms returns FALSE
TA260 001:496.485 JLINK_HasError()
TA260 001:498.927 JLINK_IsHalted()
TA260 001:499.436 - 0.508ms returns FALSE
TA260 001:499.448 JLINK_HasError()
TA260 001:501.432 JLINK_IsHalted()
TA260 001:501.906 - 0.474ms returns FALSE
TA260 001:501.913 JLINK_HasError()
TA260 001:503.438 JLINK_IsHalted()
TA260 001:503.869 - 0.430ms returns FALSE
TA260 001:503.875 JLINK_HasError()
TA260 001:507.439 JLINK_IsHalted()
TA260 001:507.991 - 0.551ms returns FALSE
TA260 001:508.008 JLINK_HasError()
TA260 001:509.939 JLINK_IsHalted()
TA260 001:510.433 - 0.494ms returns FALSE
TA260 001:510.440 JLINK_HasError()
TA260 001:511.942 JLINK_IsHalted()
TA260 001:512.561 - 0.618ms returns FALSE
TA260 001:512.572 JLINK_HasError()
TA260 001:513.949 JLINK_IsHalted()
TA260 001:514.430 - 0.481ms returns FALSE
TA260 001:514.437 JLINK_HasError()
TA260 001:515.946 JLINK_IsHalted()
TA260 001:516.481 - 0.534ms returns FALSE
TA260 001:516.490 JLINK_HasError()
TA260 001:517.938 JLINK_IsHalted()
TA260 001:518.429 - 0.490ms returns FALSE
TA260 001:518.436 JLINK_HasError()
TA260 001:520.449 JLINK_IsHalted()
TA260 001:520.977 - 0.527ms returns FALSE
TA260 001:520.984 JLINK_HasError()
TA260 001:522.449 JLINK_IsHalted()
TA260 001:522.991 - 0.541ms returns FALSE
TA260 001:523.001 JLINK_HasError()
TA260 001:524.456 JLINK_IsHalted()
TA260 001:524.958 - 0.502ms returns FALSE
TA260 001:524.971 JLINK_HasError()
TA260 001:526.456 JLINK_IsHalted()
TA260 001:526.963 - 0.506ms returns FALSE
TA260 001:526.978 JLINK_HasError()
TA260 001:528.960 JLINK_IsHalted()
TA260 001:529.535 - 0.574ms returns FALSE
TA260 001:529.541 JLINK_HasError()
TA260 001:530.954 JLINK_IsHalted()
TA260 001:531.437 - 0.482ms returns FALSE
TA260 001:531.448 JLINK_HasError()
TA260 001:532.960 JLINK_IsHalted()
TA260 001:533.386 - 0.425ms returns FALSE
TA260 001:533.395 JLINK_HasError()
TA260 001:535.468 JLINK_IsHalted()
TA260 001:535.991 - 0.521ms returns FALSE
TA260 001:536.005 JLINK_HasError()
TA260 001:537.464 JLINK_IsHalted()
TA260 001:537.920 - 0.455ms returns FALSE
TA260 001:537.932 JLINK_HasError()
TA260 001:539.974 JLINK_IsHalted()
TA260 001:540.538 - 0.564ms returns FALSE
TA260 001:540.554 JLINK_HasError()
TA260 001:541.969 JLINK_IsHalted()
TA260 001:542.476 - 0.506ms returns FALSE
TA260 001:542.491 JLINK_HasError()
TA260 001:543.974 JLINK_IsHalted()
TA260 001:544.511 - 0.536ms returns FALSE
TA260 001:544.524 JLINK_HasError()
TA260 001:545.974 JLINK_IsHalted()
TA260 001:546.521 - 0.546ms returns FALSE
TA260 001:546.533 JLINK_HasError()
TA260 001:547.976 JLINK_IsHalted()
TA260 001:548.491 - 0.515ms returns FALSE
TA260 001:548.497 JLINK_HasError()
TA260 001:550.479 JLINK_IsHalted()
TA260 001:550.976 - 0.497ms returns FALSE
TA260 001:550.984 JLINK_HasError()
TA260 001:552.476 JLINK_IsHalted()
TA260 001:553.000 - 0.524ms returns FALSE
TA260 001:553.010 JLINK_HasError()
TA260 001:555.484 JLINK_IsHalted()
TA260 001:556.060 - 0.576ms returns FALSE
TA260 001:556.068 JLINK_HasError()
TA260 001:559.991 JLINK_IsHalted()
TA260 001:560.562 - 0.551ms returns FALSE
TA260 001:560.580 JLINK_HasError()
TA260 001:563.990 JLINK_IsHalted()
TA260 001:564.537 - 0.546ms returns FALSE
TA260 001:564.545 JLINK_HasError()
TA260 001:566.996 JLINK_IsHalted()
TA260 001:567.539 - 0.542ms returns FALSE
TA260 001:567.561 JLINK_HasError()
TA260 001:570.498 JLINK_IsHalted()
TA260 001:571.033 - 0.534ms returns FALSE
TA260 001:571.040 JLINK_HasError()
TA260 001:573.497 JLINK_IsHalted()
TA260 001:574.022 - 0.524ms returns FALSE
TA260 001:574.030 JLINK_HasError()
TA260 001:575.495 JLINK_IsHalted()
TA260 001:575.987 - 0.491ms returns FALSE
TA260 001:576.001 JLINK_HasError()
TA260 001:579.005 JLINK_IsHalted()
TA260 001:579.569 - 0.563ms returns FALSE
TA260 001:579.576 JLINK_HasError()
TA260 001:582.005 JLINK_IsHalted()
TA260 001:582.534 - 0.529ms returns FALSE
TA260 001:582.541 JLINK_HasError()
TA260 001:586.007 JLINK_IsHalted()
TA260 001:586.524 - 0.516ms returns FALSE
TA260 001:586.541 JLINK_HasError()
TA260 001:588.019 JLINK_IsHalted()
TA260 001:588.524 - 0.504ms returns FALSE
TA260 001:588.538 JLINK_HasError()
TA260 001:590.515 JLINK_IsHalted()
TA260 001:591.012 - 0.497ms returns FALSE
TA260 001:591.022 JLINK_HasError()
TA260 001:592.513 JLINK_IsHalted()
TA260 001:593.023 - 0.509ms returns FALSE
TA260 001:593.038 JLINK_HasError()
TA260 001:595.515 JLINK_IsHalted()
TA260 001:596.029 - 0.513ms returns FALSE
TA260 001:596.046 JLINK_HasError()
TA260 001:597.515 JLINK_IsHalted()
TA260 001:598.034 - 0.519ms returns FALSE
TA260 001:598.041 JLINK_HasError()
TA260 001:601.023 JLINK_IsHalted()
TA260 001:601.537 - 0.513ms returns FALSE
TA260 001:601.545 JLINK_HasError()
TA260 001:603.020 JLINK_IsHalted()
TA260 001:603.522 - 0.501ms returns FALSE
TA260 001:603.529 JLINK_HasError()
TA260 001:605.022 JLINK_IsHalted()
TA260 001:605.536 - 0.513ms returns FALSE
TA260 001:605.545 JLINK_HasError()
TA260 001:607.021 JLINK_IsHalted()
TA260 001:607.533 - 0.511ms returns FALSE
TA260 001:607.539 JLINK_HasError()
TA260 001:609.023 JLINK_IsHalted()
TA260 001:609.569 - 0.545ms returns FALSE
TA260 001:609.584 JLINK_HasError()
TA260 001:611.525 JLINK_IsHalted()
TA260 001:612.012 - 0.487ms returns FALSE
TA260 001:612.020 JLINK_HasError()
TA260 001:613.532 JLINK_IsHalted()
TA260 001:614.055 - 0.523ms returns FALSE
TA260 001:614.063 JLINK_HasError()
TA260 001:615.531 JLINK_IsHalted()
TA260 001:616.026 - 0.494ms returns FALSE
TA260 001:616.034 JLINK_HasError()
TA260 001:617.528 JLINK_IsHalted()
TA260 001:618.033 - 0.504ms returns FALSE
TA260 001:618.046 JLINK_HasError()
TA260 001:620.035 JLINK_IsHalted()
TA260 001:620.519 - 0.484ms returns FALSE
TA260 001:620.528 JLINK_HasError()
TA260 001:622.033 JLINK_IsHalted()
TA260 001:622.522 - 0.489ms returns FALSE
TA260 001:622.532 JLINK_HasError()
TA260 001:624.040 JLINK_IsHalted()
TA260 001:624.751 - 0.710ms returns FALSE
TA260 001:624.771 JLINK_HasError()
TA260 001:626.035 JLINK_IsHalted()
TA260 001:626.522 - 0.486ms returns FALSE
TA260 001:626.530 JLINK_HasError()
TA260 001:628.035 JLINK_IsHalted()
TA260 001:628.611 - 0.576ms returns FALSE
TA260 001:628.618 JLINK_HasError()
TA260 001:630.544 JLINK_IsHalted()
TA260 001:631.027 - 0.483ms returns FALSE
TA260 001:631.034 JLINK_HasError()
TA260 001:632.542 JLINK_IsHalted()
TA260 001:633.035 - 0.493ms returns FALSE
TA260 001:633.042 JLINK_HasError()
TA260 001:634.545 JLINK_IsHalted()
TA260 001:635.065 - 0.519ms returns FALSE
TA260 001:635.088 JLINK_HasError()
TA260 001:637.057 JLINK_IsHalted()
TA260 001:637.629 - 0.572ms returns FALSE
TA260 001:637.647 JLINK_HasError()
TA260 001:640.557 JLINK_IsHalted()
TA260 001:641.078 - 0.520ms returns FALSE
TA260 001:641.085 JLINK_HasError()
TA260 001:644.560 JLINK_IsHalted()
TA260 001:645.071 - 0.509ms returns FALSE
TA260 001:645.091 JLINK_HasError()
TA260 001:647.566 JLINK_IsHalted()
TA260 001:648.081 - 0.514ms returns FALSE
TA260 001:648.094 JLINK_HasError()
TA260 001:651.071 JLINK_IsHalted()
TA260 001:651.613 - 0.542ms returns FALSE
TA260 001:651.619 JLINK_HasError()
TA260 001:654.069 JLINK_IsHalted()
TA260 001:654.541 - 0.470ms returns FALSE
TA260 001:654.547 JLINK_HasError()
TA260 001:656.072 JLINK_IsHalted()
TA260 001:656.569 - 0.496ms returns FALSE
TA260 001:656.584 JLINK_HasError()
TA260 001:657.901 JLINK_IsHalted()
TA260 001:658.386 - 0.484ms returns FALSE
TA260 001:658.393 JLINK_HasError()
TA260 001:661.413 JLINK_IsHalted()
TA260 001:661.943 - 0.530ms returns FALSE
TA260 001:661.950 JLINK_HasError()
TA260 001:663.411 JLINK_IsHalted()
TA260 001:663.862 - 0.449ms returns FALSE
TA260 001:663.869 JLINK_HasError()
TA260 001:665.413 JLINK_IsHalted()
TA260 001:665.993 - 0.580ms returns FALSE
TA260 001:666.008 JLINK_HasError()
TA260 001:667.409 JLINK_IsHalted()
TA260 001:667.928 - 0.519ms returns FALSE
TA260 001:667.937 JLINK_HasError()
TA260 001:669.923 JLINK_IsHalted()
TA260 001:670.435 - 0.512ms returns FALSE
TA260 001:670.453 JLINK_HasError()
TA260 001:671.506 JLINK_IsHalted()
TA260 001:671.979 - 0.472ms returns FALSE
TA260 001:671.985 JLINK_HasError()
TA260 001:673.512 JLINK_IsHalted()
TA260 001:674.044 - 0.531ms returns FALSE
TA260 001:674.054 JLINK_HasError()
TA260 001:675.515 JLINK_IsHalted()
TA260 001:675.995 - 0.480ms returns FALSE
TA260 001:676.004 JLINK_HasError()
TA260 001:677.578 JLINK_IsHalted()
TA260 001:678.032 - 0.454ms returns FALSE
TA260 001:678.039 JLINK_HasError()
TA260 001:680.022 JLINK_IsHalted()
TA260 001:680.536 - 0.513ms returns FALSE
TA260 001:680.547 JLINK_HasError()
TA260 001:682.020 JLINK_IsHalted()
TA260 001:682.534 - 0.513ms returns FALSE
TA260 001:682.544 JLINK_HasError()
TA260 001:684.021 JLINK_IsHalted()
TA260 001:684.476 - 0.454ms returns FALSE
TA260 001:684.485 JLINK_HasError()
TA260 001:686.022 JLINK_IsHalted()
TA260 001:686.536 - 0.513ms returns FALSE
TA260 001:686.544 JLINK_HasError()
TA260 001:688.018 JLINK_IsHalted()
TA260 001:688.535 - 0.517ms returns FALSE
TA260 001:688.542 JLINK_HasError()
TA260 001:690.533 JLINK_IsHalted()
TA260 001:690.993 - 0.459ms returns FALSE
TA260 001:691.009 JLINK_HasError()
TA260 001:692.523 JLINK_IsHalted()
TA260 001:693.029 - 0.506ms returns FALSE
TA260 001:693.035 JLINK_HasError()
TA260 001:694.530 JLINK_IsHalted()
TA260 001:695.067 - 0.535ms returns FALSE
TA260 001:695.099 JLINK_HasError()
TA260 001:696.533 JLINK_IsHalted()
TA260 001:697.041 - 0.507ms returns FALSE
TA260 001:697.055 JLINK_HasError()
TA260 001:699.032 JLINK_IsHalted()
TA260 001:699.536 - 0.504ms returns FALSE
TA260 001:699.543 JLINK_HasError()
TA260 001:701.039 JLINK_IsHalted()
TA260 001:701.567 - 0.528ms returns FALSE
TA260 001:701.577 JLINK_HasError()
TA260 001:703.028 JLINK_IsHalted()
TA260 001:703.534 - 0.505ms returns FALSE
TA260 001:703.542 JLINK_HasError()
TA260 001:705.038 JLINK_IsHalted()
TA260 001:705.558 - 0.519ms returns FALSE
TA260 001:705.575 JLINK_HasError()
TA260 001:707.037 JLINK_IsHalted()
TA260 001:707.566 - 0.528ms returns FALSE
TA260 001:707.573 JLINK_HasError()
TA260 001:709.036 JLINK_IsHalted()
TA260 001:709.547 - 0.511ms returns FALSE
TA260 001:709.562 JLINK_HasError()
TA260 001:711.543 JLINK_IsHalted()
TA260 001:711.976 - 0.433ms returns FALSE
TA260 001:711.986 JLINK_HasError()
TA260 001:713.538 JLINK_IsHalted()
TA260 001:713.996 - 0.458ms returns FALSE
TA260 001:714.003 JLINK_HasError()
TA260 001:715.543 JLINK_IsHalted()
TA260 001:716.039 - 0.495ms returns FALSE
TA260 001:716.059 JLINK_HasError()
TA260 001:717.543 JLINK_IsHalted()
TA260 001:718.069 - 0.525ms returns FALSE
TA260 001:718.081 JLINK_HasError()
TA260 001:720.048 JLINK_IsHalted()
TA260 001:720.539 - 0.490ms returns FALSE
TA260 001:720.554 JLINK_HasError()
TA260 001:722.054 JLINK_IsHalted()
TA260 001:722.536 - 0.481ms returns FALSE
TA260 001:722.544 JLINK_HasError()
TA260 001:724.045 JLINK_IsHalted()
TA260 001:724.536 - 0.490ms returns FALSE
TA260 001:724.545 JLINK_HasError()
TA260 001:726.053 JLINK_IsHalted()
TA260 001:726.655 - 0.602ms returns FALSE
TA260 001:726.672 JLINK_HasError()
TA260 001:728.049 JLINK_IsHalted()
TA260 001:728.555 - 0.506ms returns FALSE
TA260 001:728.562 JLINK_HasError()
TA260 001:730.557 JLINK_IsHalted()
TA260 001:731.022 - 0.465ms returns FALSE
TA260 001:731.030 JLINK_HasError()
TA260 001:732.563 JLINK_IsHalted()
TA260 001:733.040 - 0.477ms returns FALSE
TA260 001:733.049 JLINK_HasError()
TA260 001:734.557 JLINK_IsHalted()
TA260 001:735.073 - 0.515ms returns FALSE
TA260 001:735.098 JLINK_HasError()
TA260 001:737.075 JLINK_IsHalted()
TA260 001:737.631 - 0.555ms returns FALSE
TA260 001:737.640 JLINK_HasError()
TA260 001:741.579 JLINK_IsHalted()
TA260 001:742.069 - 0.489ms returns FALSE
TA260 001:742.075 JLINK_HasError()
TA260 001:743.574 JLINK_IsHalted()
TA260 001:744.032 - 0.458ms returns FALSE
TA260 001:744.042 JLINK_HasError()
TA260 001:746.582 JLINK_IsHalted()
TA260 001:747.089 - 0.506ms returns FALSE
TA260 001:747.102 JLINK_HasError()
TA260 001:750.085 JLINK_IsHalted()
TA260 001:750.631 - 0.545ms returns FALSE
TA260 001:750.647 JLINK_HasError()
TA260 001:753.093 JLINK_IsHalted()
TA260 001:753.582 - 0.488ms returns FALSE
TA260 001:753.600 JLINK_HasError()
TA260 001:756.087 JLINK_IsHalted()
TA260 001:756.606 - 0.518ms returns FALSE
TA260 001:756.623 JLINK_HasError()
TA260 001:759.091 JLINK_IsHalted()
TA260 001:759.632 - 0.540ms returns FALSE
TA260 001:759.650 JLINK_HasError()
TA260 001:762.594 JLINK_IsHalted()
TA260 001:763.158 - 0.563ms returns FALSE
TA260 001:763.165 JLINK_HasError()
TA260 001:767.594 JLINK_IsHalted()
TA260 001:768.183 - 0.588ms returns FALSE
TA260 001:768.190 JLINK_HasError()
TA260 001:771.106 JLINK_IsHalted()
TA260 001:771.625 - 0.518ms returns FALSE
TA260 001:771.638 JLINK_HasError()
TA260 001:776.104 JLINK_IsHalted()
TA260 001:776.663 - 0.558ms returns FALSE
TA260 001:776.672 JLINK_HasError()
TA260 001:780.613 JLINK_IsHalted()
TA260 001:781.210 - 0.595ms returns FALSE
TA260 001:781.222 JLINK_HasError()
TA260 001:784.616 JLINK_IsHalted()
TA260 001:785.101 - 0.485ms returns FALSE
TA260 001:785.125 JLINK_HasError()
TA260 001:789.121 JLINK_IsHalted()
TA260 001:789.664 - 0.542ms returns FALSE
TA260 001:789.671 JLINK_HasError()
TA260 001:792.118 JLINK_IsHalted()
TA260 001:792.603 - 0.484ms returns FALSE
TA260 001:792.610 JLINK_HasError()
TA260 001:796.123 JLINK_IsHalted()
TA260 001:796.687 - 0.564ms returns FALSE
TA260 001:796.710 JLINK_HasError()
TA260 001:802.632 JLINK_IsHalted()
TA260 001:803.160 - 0.527ms returns FALSE
TA260 001:803.167 JLINK_HasError()
TA260 001:804.715 JLINK_IsHalted()
TA260 001:805.218 - 0.502ms returns FALSE
TA260 001:805.232 JLINK_HasError()
TA260 001:810.140 JLINK_IsHalted()
TA260 001:810.717 - 0.575ms returns FALSE
TA260 001:810.724 JLINK_HasError()
TA260 001:816.141 JLINK_IsHalted()
TA260 001:816.686 - 0.544ms returns FALSE
TA260 001:816.701 JLINK_HasError()
TA260 001:822.644 JLINK_IsHalted()
TA260 001:824.995   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:825.552 - 2.907ms returns TRUE
TA260 001:825.574 JLINK_ReadReg(R15 (PC))
TA260 001:825.581 - 0.007ms returns 0x20000000
TA260 001:825.585 JLINK_ClrBPEx(BPHandle = 0x00000007)
TA260 001:825.590 - 0.004ms returns 0x00
TA260 001:825.594 JLINK_ReadReg(R0)
TA260 001:825.598 - 0.003ms returns 0x00000000
TA260 001:825.994 JLINK_HasError()
TA260 001:826.004 JLINK_WriteReg(R0, 0x0800C000)
TA260 001:826.009 - 0.004ms returns 0
TA260 001:826.013 JLINK_WriteReg(R1, 0x00004000)
TA260 001:826.017 - 0.003ms returns 0
TA260 001:826.021 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:826.024 - 0.003ms returns 0
TA260 001:826.028 JLINK_WriteReg(R3, 0x00000000)
TA260 001:826.037 - 0.008ms returns 0
TA260 001:826.043 JLINK_WriteReg(R4, 0x00000000)
TA260 001:826.047 - 0.003ms returns 0
TA260 001:826.051 JLINK_WriteReg(R5, 0x00000000)
TA260 001:826.054 - 0.003ms returns 0
TA260 001:826.059 JLINK_WriteReg(R6, 0x00000000)
TA260 001:826.062 - 0.003ms returns 0
TA260 001:826.066 JLINK_WriteReg(R7, 0x00000000)
TA260 001:826.070 - 0.003ms returns 0
TA260 001:826.074 JLINK_WriteReg(R8, 0x00000000)
TA260 001:826.077 - 0.003ms returns 0
TA260 001:826.082 JLINK_WriteReg(R9, 0x20000180)
TA260 001:826.085 - 0.003ms returns 0
TA260 001:826.089 JLINK_WriteReg(R10, 0x00000000)
TA260 001:826.093 - 0.003ms returns 0
TA260 001:826.097 JLINK_WriteReg(R11, 0x00000000)
TA260 001:826.100 - 0.003ms returns 0
TA260 001:826.104 JLINK_WriteReg(R12, 0x00000000)
TA260 001:826.108 - 0.003ms returns 0
TA260 001:826.112 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:826.115 - 0.003ms returns 0
TA260 001:826.120 JLINK_WriteReg(R14, 0x20000001)
TA260 001:826.123 - 0.003ms returns 0
TA260 001:826.128 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 001:826.131 - 0.003ms returns 0
TA260 001:826.135 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:826.139 - 0.003ms returns 0
TA260 001:826.143 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:826.147 - 0.003ms returns 0
TA260 001:826.151 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:826.155 - 0.003ms returns 0
TA260 001:826.159 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:826.162 - 0.003ms returns 0
TA260 001:826.167 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:826.172 - 0.005ms returns 0x00000008
TA260 001:826.176 JLINK_Go()
TA260 001:826.188   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:828.989 - 2.811ms 
TA260 001:829.015 JLINK_IsHalted()
TA260 001:831.436   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:831.938 - 2.922ms returns TRUE
TA260 001:831.954 JLINK_ReadReg(R15 (PC))
TA260 001:831.960 - 0.006ms returns 0x20000000
TA260 001:831.965 JLINK_ClrBPEx(BPHandle = 0x00000008)
TA260 001:831.969 - 0.004ms returns 0x00
TA260 001:831.974 JLINK_ReadReg(R0)
TA260 001:831.977 - 0.003ms returns 0x00000001
TA260 001:831.982 JLINK_HasError()
TA260 001:831.986 JLINK_WriteReg(R0, 0x0800C000)
TA260 001:831.991 - 0.004ms returns 0
TA260 001:831.995 JLINK_WriteReg(R1, 0x00004000)
TA260 001:831.998 - 0.003ms returns 0
TA260 001:832.002 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:832.006 - 0.004ms returns 0
TA260 001:832.010 JLINK_WriteReg(R3, 0x00000000)
TA260 001:832.014 - 0.003ms returns 0
TA260 001:832.018 JLINK_WriteReg(R4, 0x00000000)
TA260 001:832.021 - 0.003ms returns 0
TA260 001:832.025 JLINK_WriteReg(R5, 0x00000000)
TA260 001:832.029 - 0.003ms returns 0
TA260 001:832.033 JLINK_WriteReg(R6, 0x00000000)
TA260 001:832.036 - 0.003ms returns 0
TA260 001:832.040 JLINK_WriteReg(R7, 0x00000000)
TA260 001:832.044 - 0.003ms returns 0
TA260 001:832.048 JLINK_WriteReg(R8, 0x00000000)
TA260 001:832.051 - 0.003ms returns 0
TA260 001:832.055 JLINK_WriteReg(R9, 0x20000180)
TA260 001:832.059 - 0.003ms returns 0
TA260 001:832.063 JLINK_WriteReg(R10, 0x00000000)
TA260 001:832.066 - 0.003ms returns 0
TA260 001:832.070 JLINK_WriteReg(R11, 0x00000000)
TA260 001:832.073 - 0.003ms returns 0
TA260 001:832.077 JLINK_WriteReg(R12, 0x00000000)
TA260 001:832.081 - 0.003ms returns 0
TA260 001:832.085 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:832.088 - 0.003ms returns 0
TA260 001:832.093 JLINK_WriteReg(R14, 0x20000001)
TA260 001:832.096 - 0.003ms returns 0
TA260 001:832.100 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 001:832.104 - 0.003ms returns 0
TA260 001:832.108 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:832.111 - 0.003ms returns 0
TA260 001:832.115 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:832.119 - 0.003ms returns 0
TA260 001:832.123 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:832.126 - 0.003ms returns 0
TA260 001:832.130 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:832.134 - 0.003ms returns 0
TA260 001:832.138 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:832.142 - 0.004ms returns 0x00000009
TA260 001:832.146 JLINK_Go()
TA260 001:832.161   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:834.997 - 2.849ms 
TA260 001:835.019 JLINK_IsHalted()
TA260 001:835.570 - 0.550ms returns FALSE
TA260 001:835.591 JLINK_HasError()
TA260 001:841.927 JLINK_IsHalted()
TA260 001:842.423 - 0.495ms returns FALSE
TA260 001:842.430 JLINK_HasError()
TA260 001:851.427 JLINK_IsHalted()
TA260 001:851.958 - 0.531ms returns FALSE
TA260 001:851.965 JLINK_HasError()
TA260 001:855.434 JLINK_IsHalted()
TA260 001:856.028 - 0.593ms returns FALSE
TA260 001:856.048 JLINK_HasError()
TA260 001:863.948 JLINK_IsHalted()
TA260 001:864.433 - 0.484ms returns FALSE
TA260 001:864.442 JLINK_HasError()
TA260 001:871.448 JLINK_IsHalted()
TA260 001:871.997 - 0.541ms returns FALSE
TA260 001:872.004 JLINK_HasError()
TA260 001:875.452 JLINK_IsHalted()
TA260 001:875.962 - 0.508ms returns FALSE
TA260 001:875.979 JLINK_HasError()
TA260 001:880.963 JLINK_IsHalted()
TA260 001:881.522 - 0.558ms returns FALSE
TA260 001:881.534 JLINK_HasError()
TA260 001:884.965 JLINK_IsHalted()
TA260 001:885.540 - 0.574ms returns FALSE
TA260 001:885.558 JLINK_HasError()
TA260 001:889.463 JLINK_IsHalted()
TA260 001:890.040 - 0.576ms returns FALSE
TA260 001:890.055 JLINK_HasError()
TA260 001:894.467 JLINK_IsHalted()
TA260 001:895.044 - 0.574ms returns FALSE
TA260 001:895.064 JLINK_HasError()
TA260 001:897.472 JLINK_IsHalted()
TA260 001:897.982 - 0.509ms returns FALSE
TA260 001:898.003 JLINK_HasError()
TA260 001:900.974 JLINK_IsHalted()
TA260 001:901.538 - 0.564ms returns FALSE
TA260 001:901.545 JLINK_HasError()
TA260 001:903.973 JLINK_IsHalted()
TA260 001:904.525 - 0.552ms returns FALSE
TA260 001:904.532 JLINK_HasError()
TA260 001:906.979 JLINK_IsHalted()
TA260 001:907.476 - 0.496ms returns FALSE
TA260 001:907.484 JLINK_HasError()
TA260 001:909.472 JLINK_IsHalted()
TA260 001:910.014 - 0.540ms returns FALSE
TA260 001:910.029 JLINK_HasError()
TA260 001:912.482 JLINK_IsHalted()
TA260 001:912.965 - 0.482ms returns FALSE
TA260 001:912.972 JLINK_HasError()
TA260 001:914.476 JLINK_IsHalted()
TA260 001:915.016 - 0.539ms returns FALSE
TA260 001:915.031 JLINK_HasError()
TA260 001:917.486 JLINK_IsHalted()
TA260 001:917.991 - 0.504ms returns FALSE
TA260 001:917.998 JLINK_HasError()
TA260 001:920.990 JLINK_IsHalted()
TA260 001:921.536 - 0.547ms returns FALSE
TA260 001:921.543 JLINK_HasError()
TA260 001:924.995 JLINK_IsHalted()
TA260 001:925.524 - 0.529ms returns FALSE
TA260 001:925.534 JLINK_HasError()
TA260 001:926.995 JLINK_IsHalted()
TA260 001:927.523 - 0.527ms returns FALSE
TA260 001:927.531 JLINK_HasError()
TA260 001:930.498 JLINK_IsHalted()
TA260 001:930.988 - 0.490ms returns FALSE
TA260 001:930.996 JLINK_HasError()
TA260 001:933.503 JLINK_IsHalted()
TA260 001:934.037 - 0.533ms returns FALSE
TA260 001:934.044 JLINK_HasError()
TA260 001:937.006 JLINK_IsHalted()
TA260 001:937.538 - 0.531ms returns FALSE
TA260 001:937.556 JLINK_HasError()
TA260 001:940.510 JLINK_IsHalted()
TA260 001:941.044 - 0.533ms returns FALSE
TA260 001:941.052 JLINK_HasError()
TA260 001:942.510 JLINK_IsHalted()
TA260 001:942.989 - 0.478ms returns FALSE
TA260 001:942.996 JLINK_HasError()
TA260 001:945.524 JLINK_IsHalted()
TA260 001:946.037 - 0.512ms returns FALSE
TA260 001:946.054 JLINK_HasError()
TA260 001:947.519 JLINK_IsHalted()
TA260 001:948.057 - 0.538ms returns FALSE
TA260 001:948.066 JLINK_HasError()
TA260 001:951.018 JLINK_IsHalted()
TA260 001:951.531 - 0.512ms returns FALSE
TA260 001:951.538 JLINK_HasError()
TA260 001:953.018 JLINK_IsHalted()
TA260 001:953.518 - 0.499ms returns FALSE
TA260 001:953.525 JLINK_HasError()
TA260 001:955.028 JLINK_IsHalted()
TA260 001:955.589 - 0.561ms returns FALSE
TA260 001:955.606 JLINK_HasError()
TA260 001:958.025 JLINK_IsHalted()
TA260 001:958.536 - 0.510ms returns FALSE
TA260 001:958.543 JLINK_HasError()
TA260 001:960.530 JLINK_IsHalted()
TA260 001:961.022 - 0.491ms returns FALSE
TA260 001:961.030 JLINK_HasError()
TA260 001:962.527 JLINK_IsHalted()
TA260 001:963.058 - 0.530ms returns FALSE
TA260 001:963.072 JLINK_HasError()
TA260 001:965.532 JLINK_IsHalted()
TA260 001:966.026 - 0.494ms returns FALSE
TA260 001:966.043 JLINK_HasError()
TA260 001:967.527 JLINK_IsHalted()
TA260 001:968.038 - 0.510ms returns FALSE
TA260 001:968.058 JLINK_HasError()
TA260 001:970.102 JLINK_IsHalted()
TA260 001:970.614 - 0.511ms returns FALSE
TA260 001:970.625 JLINK_HasError()
TA260 001:971.724 JLINK_IsHalted()
TA260 001:972.225 - 0.500ms returns FALSE
TA260 001:972.231 JLINK_HasError()
TA260 001:973.725 JLINK_IsHalted()
TA260 001:974.209 - 0.483ms returns FALSE
TA260 001:974.220 JLINK_HasError()
TA260 001:975.728 JLINK_IsHalted()
TA260 001:976.205 - 0.477ms returns FALSE
TA260 001:976.213 JLINK_HasError()
TA260 001:977.728 JLINK_IsHalted()
TA260 001:978.240 - 0.512ms returns FALSE
TA260 001:978.247 JLINK_HasError()
TA260 001:980.235 JLINK_IsHalted()
TA260 001:980.730 - 0.494ms returns FALSE
TA260 001:980.741 JLINK_HasError()
TA260 001:982.234 JLINK_IsHalted()
TA260 001:982.736 - 0.502ms returns FALSE
TA260 001:982.745 JLINK_HasError()
TA260 001:984.234 JLINK_IsHalted()
TA260 001:984.705 - 0.471ms returns FALSE
TA260 001:984.718 JLINK_HasError()
TA260 001:986.239 JLINK_IsHalted()
TA260 001:986.727 - 0.486ms returns FALSE
TA260 001:986.735 JLINK_HasError()
TA260 001:988.233 JLINK_IsHalted()
TA260 001:988.726 - 0.493ms returns FALSE
TA260 001:988.735 JLINK_HasError()
TA260 001:990.746 JLINK_IsHalted()
TA260 001:991.264 - 0.517ms returns FALSE
TA260 001:991.271 JLINK_HasError()
TA260 001:992.746 JLINK_IsHalted()
TA260 001:993.238 - 0.492ms returns FALSE
TA260 001:993.245 JLINK_HasError()
TA260 001:994.745 JLINK_IsHalted()
TA260 001:995.267 - 0.521ms returns FALSE
TA260 001:995.283 JLINK_HasError()
TA260 001:996.744 JLINK_IsHalted()
TA260 001:997.227 - 0.482ms returns FALSE
TA260 001:997.240 JLINK_HasError()
TA260 001:999.249 JLINK_IsHalted()
TA260 001:999.712 - 0.462ms returns FALSE
TA260 001:999.719 JLINK_HasError()
TA260 002:001.254 JLINK_IsHalted()
TA260 002:001.718 - 0.463ms returns FALSE
TA260 002:001.727 JLINK_HasError()
TA260 002:003.251 JLINK_IsHalted()
TA260 002:003.770 - 0.519ms returns FALSE
TA260 002:003.778 JLINK_HasError()
TA260 002:005.252 JLINK_IsHalted()
TA260 002:005.782 - 0.528ms returns FALSE
TA260 002:005.799 JLINK_HasError()
TA260 002:007.269 JLINK_IsHalted()
TA260 002:007.764 - 0.495ms returns FALSE
TA260 002:007.773 JLINK_HasError()
TA260 002:009.760 JLINK_IsHalted()
TA260 002:010.303 - 0.542ms returns FALSE
TA260 002:010.313 JLINK_HasError()
TA260 002:011.758 JLINK_IsHalted()
TA260 002:012.205 - 0.447ms returns FALSE
TA260 002:012.212 JLINK_HasError()
TA260 002:013.764 JLINK_IsHalted()
TA260 002:014.259 - 0.494ms returns FALSE
TA260 002:014.265 JLINK_HasError()
TA260 002:015.767 JLINK_IsHalted()
TA260 002:016.269 - 0.501ms returns FALSE
TA260 002:016.284 JLINK_HasError()
TA260 002:018.771 JLINK_IsHalted()
TA260 002:019.294 - 0.522ms returns FALSE
TA260 002:019.301 JLINK_HasError()
TA260 002:021.266 JLINK_IsHalted()
TA260 002:021.724 - 0.457ms returns FALSE
TA260 002:021.732 JLINK_HasError()
TA260 002:023.270 JLINK_IsHalted()
TA260 002:023.794 - 0.523ms returns FALSE
TA260 002:023.802 JLINK_HasError()
TA260 002:025.308 JLINK_IsHalted()
TA260 002:025.777 - 0.469ms returns FALSE
TA260 002:025.793 JLINK_HasError()
TA260 002:027.270 JLINK_IsHalted()
TA260 002:027.831 - 0.560ms returns FALSE
TA260 002:027.852 JLINK_HasError()
TA260 002:029.773 JLINK_IsHalted()
TA260 002:030.309 - 0.535ms returns FALSE
TA260 002:030.318 JLINK_HasError()
TA260 002:031.774 JLINK_IsHalted()
TA260 002:032.316 - 0.541ms returns FALSE
TA260 002:032.329 JLINK_HasError()
TA260 002:033.779 JLINK_IsHalted()
TA260 002:034.246 - 0.467ms returns FALSE
TA260 002:034.253 JLINK_HasError()
TA260 002:036.286 JLINK_IsHalted()
TA260 002:036.762 - 0.476ms returns FALSE
TA260 002:036.770 JLINK_HasError()
TA260 002:038.795 JLINK_IsHalted()
TA260 002:039.385 - 0.589ms returns FALSE
TA260 002:039.392 JLINK_HasError()
TA260 002:040.790 JLINK_IsHalted()
TA260 002:041.285 - 0.494ms returns FALSE
TA260 002:041.295 JLINK_HasError()
TA260 002:042.791 JLINK_IsHalted()
TA260 002:043.272 - 0.480ms returns FALSE
TA260 002:043.282 JLINK_HasError()
TA260 002:044.791 JLINK_IsHalted()
TA260 002:045.308 - 0.516ms returns FALSE
TA260 002:045.316 JLINK_HasError()
TA260 002:046.795 JLINK_IsHalted()
TA260 002:047.319 - 0.524ms returns FALSE
TA260 002:047.326 JLINK_HasError()
TA260 002:048.800 JLINK_IsHalted()
TA260 002:049.285 - 0.484ms returns FALSE
TA260 002:049.295 JLINK_HasError()
TA260 002:051.298 JLINK_IsHalted()
TA260 002:051.771 - 0.473ms returns FALSE
TA260 002:051.779 JLINK_HasError()
TA260 002:054.304 JLINK_IsHalted()
TA260 002:054.850 - 0.545ms returns FALSE
TA260 002:054.866 JLINK_HasError()
TA260 002:060.814 JLINK_IsHalted()
TA260 002:061.419 - 0.604ms returns FALSE
TA260 002:061.426 JLINK_HasError()
TA260 002:064.819 JLINK_IsHalted()
TA260 002:065.416 - 0.596ms returns FALSE
TA260 002:065.438 JLINK_HasError()
TA260 002:068.819 JLINK_IsHalted()
TA260 002:069.412 - 0.593ms returns FALSE
TA260 002:069.420 JLINK_HasError()
TA260 002:072.320 JLINK_IsHalted()
TA260 002:072.866 - 0.544ms returns FALSE
TA260 002:072.881 JLINK_HasError()
TA260 002:076.319 JLINK_IsHalted()
TA260 002:076.878 - 0.558ms returns FALSE
TA260 002:076.899 JLINK_HasError()
TA260 002:085.830 JLINK_IsHalted()
TA260 002:086.414 - 0.583ms returns FALSE
TA260 002:086.431 JLINK_HasError()
TA260 002:090.340 JLINK_IsHalted()
TA260 002:090.895 - 0.554ms returns FALSE
TA260 002:090.902 JLINK_HasError()
TA260 002:099.843 JLINK_IsHalted()
TA260 002:100.415 - 0.571ms returns FALSE
TA260 002:100.432 JLINK_HasError()
TA260 002:102.846 JLINK_IsHalted()
TA260 002:103.414 - 0.567ms returns FALSE
TA260 002:103.422 JLINK_HasError()
TA260 002:112.361 JLINK_IsHalted()
TA260 002:112.887 - 0.526ms returns FALSE
TA260 002:112.894 JLINK_HasError()
TA260 002:120.863 JLINK_IsHalted()
TA260 002:121.411 - 0.548ms returns FALSE
TA260 002:121.419 JLINK_HasError()
TA260 002:123.859 JLINK_IsHalted()
TA260 002:124.412 - 0.552ms returns FALSE
TA260 002:124.418 JLINK_HasError()
TA260 002:134.372 JLINK_IsHalted()
TA260 002:134.896 - 0.523ms returns FALSE
TA260 002:134.911 JLINK_HasError()
TA260 002:144.378 JLINK_IsHalted()
TA260 002:144.946 - 0.566ms returns FALSE
TA260 002:144.962 JLINK_HasError()
TA260 002:150.892 JLINK_IsHalted()
TA260 002:151.418 - 0.526ms returns FALSE
TA260 002:151.428 JLINK_HasError()
TA260 002:153.884 JLINK_IsHalted()
TA260 002:154.408 - 0.523ms returns FALSE
TA260 002:154.414 JLINK_HasError()
TA260 002:156.903 JLINK_IsHalted()
TA260 002:157.469 - 0.566ms returns FALSE
TA260 002:157.485 JLINK_HasError()
TA260 002:160.392 JLINK_IsHalted()
TA260 002:160.930 - 0.538ms returns FALSE
TA260 002:160.945 JLINK_HasError()
TA260 002:164.392 JLINK_IsHalted()
TA260 002:164.864 - 0.472ms returns FALSE
TA260 002:164.873 JLINK_HasError()
TA260 002:169.900 JLINK_IsHalted()
TA260 002:170.461 - 0.561ms returns FALSE
TA260 002:170.468 JLINK_HasError()
TA260 002:172.900 JLINK_IsHalted()
TA260 002:173.431 - 0.530ms returns FALSE
TA260 002:173.442 JLINK_HasError()
TA260 002:175.904 JLINK_IsHalted()
TA260 002:176.407 - 0.502ms returns FALSE
TA260 002:176.426 JLINK_HasError()
TA260 002:179.401 JLINK_IsHalted()
TA260 002:179.899 - 0.497ms returns FALSE
TA260 002:179.913 JLINK_HasError()
TA260 002:183.406 JLINK_IsHalted()
TA260 002:183.916 - 0.510ms returns FALSE
TA260 002:183.922 JLINK_HasError()
TA260 002:187.414 JLINK_IsHalted()
TA260 002:187.968 - 0.553ms returns FALSE
TA260 002:187.984 JLINK_HasError()
TA260 002:192.916 JLINK_IsHalted()
TA260 002:193.430 - 0.513ms returns FALSE
TA260 002:193.436 JLINK_HasError()
TA260 002:195.918 JLINK_IsHalted()
TA260 002:196.434 - 0.515ms returns FALSE
TA260 002:196.526 JLINK_HasError()
TA260 002:200.424 JLINK_IsHalted()
TA260 002:200.940 - 0.515ms returns FALSE
TA260 002:200.946 JLINK_HasError()
TA260 002:204.422 JLINK_IsHalted()
TA260 002:204.933 - 0.510ms returns FALSE
TA260 002:204.946 JLINK_HasError()
TA260 002:207.426 JLINK_IsHalted()
TA260 002:207.911 - 0.484ms returns FALSE
TA260 002:207.927 JLINK_HasError()
TA260 002:210.929 JLINK_IsHalted()
TA260 002:211.427 - 0.498ms returns FALSE
TA260 002:211.434 JLINK_HasError()
TA260 002:212.927 JLINK_IsHalted()
TA260 002:213.427 - 0.500ms returns FALSE
TA260 002:213.433 JLINK_HasError()
TA260 002:214.930 JLINK_IsHalted()
TA260 002:215.399 - 0.468ms returns FALSE
TA260 002:215.410 JLINK_HasError()
TA260 002:216.937 JLINK_IsHalted()
TA260 002:217.421 - 0.483ms returns FALSE
TA260 002:217.433 JLINK_HasError()
TA260 002:220.435 JLINK_IsHalted()
TA260 002:221.009 - 0.573ms returns FALSE
TA260 002:221.017 JLINK_HasError()
TA260 002:222.434 JLINK_IsHalted()
TA260 002:222.928 - 0.493ms returns FALSE
TA260 002:222.933 JLINK_HasError()
TA260 002:224.434 JLINK_IsHalted()
TA260 002:224.934 - 0.499ms returns FALSE
TA260 002:224.950 JLINK_HasError()
TA260 002:226.439 JLINK_IsHalted()
TA260 002:226.944 - 0.504ms returns FALSE
TA260 002:226.963 JLINK_HasError()
TA260 002:228.944 JLINK_IsHalted()
TA260 002:229.428 - 0.484ms returns FALSE
TA260 002:229.435 JLINK_HasError()
TA260 002:230.940 JLINK_IsHalted()
TA260 002:231.428 - 0.487ms returns FALSE
TA260 002:231.434 JLINK_HasError()
TA260 002:232.941 JLINK_IsHalted()
TA260 002:233.418 - 0.476ms returns FALSE
TA260 002:233.423 JLINK_HasError()
TA260 002:234.945 JLINK_IsHalted()
TA260 002:235.388 - 0.442ms returns FALSE
TA260 002:235.399 JLINK_HasError()
TA260 002:236.500 JLINK_IsHalted()
TA260 002:237.068 - 0.568ms returns FALSE
TA260 002:237.078 JLINK_HasError()
TA260 002:238.952 JLINK_IsHalted()
TA260 002:239.428 - 0.475ms returns FALSE
TA260 002:239.434 JLINK_HasError()
TA260 002:241.952 JLINK_IsHalted()
TA260 002:242.466 - 0.513ms returns FALSE
TA260 002:242.472 JLINK_HasError()
TA260 002:244.963 JLINK_IsHalted()
TA260 002:245.540 - 0.576ms returns FALSE
TA260 002:245.556 JLINK_HasError()
TA260 002:247.955 JLINK_IsHalted()
TA260 002:248.463 - 0.507ms returns FALSE
TA260 002:248.469 JLINK_HasError()
TA260 002:250.462 JLINK_IsHalted()
TA260 002:252.888   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:253.409 - 2.946ms returns TRUE
TA260 002:253.420 JLINK_ReadReg(R15 (PC))
TA260 002:253.425 - 0.005ms returns 0x20000000
TA260 002:253.455 JLINK_ClrBPEx(BPHandle = 0x00000009)
TA260 002:253.460 - 0.005ms returns 0x00
TA260 002:253.465 JLINK_ReadReg(R0)
TA260 002:253.468 - 0.003ms returns 0x00000000
TA260 002:253.701 JLINK_HasError()
TA260 002:253.710 JLINK_WriteReg(R0, 0x00000001)
TA260 002:253.714 - 0.004ms returns 0
TA260 002:253.718 JLINK_WriteReg(R1, 0x00004000)
TA260 002:253.722 - 0.003ms returns 0
TA260 002:253.726 JLINK_WriteReg(R2, 0x000000FF)
TA260 002:253.729 - 0.003ms returns 0
TA260 002:253.733 JLINK_WriteReg(R3, 0x00000000)
TA260 002:253.737 - 0.003ms returns 0
TA260 002:253.741 JLINK_WriteReg(R4, 0x00000000)
TA260 002:253.744 - 0.003ms returns 0
TA260 002:253.748 JLINK_WriteReg(R5, 0x00000000)
TA260 002:253.752 - 0.003ms returns 0
TA260 002:253.756 JLINK_WriteReg(R6, 0x00000000)
TA260 002:253.761 - 0.005ms returns 0
TA260 002:253.765 JLINK_WriteReg(R7, 0x00000000)
TA260 002:253.768 - 0.003ms returns 0
TA260 002:253.772 JLINK_WriteReg(R8, 0x00000000)
TA260 002:253.776 - 0.003ms returns 0
TA260 002:253.779 JLINK_WriteReg(R9, 0x20000180)
TA260 002:253.783 - 0.003ms returns 0
TA260 002:253.787 JLINK_WriteReg(R10, 0x00000000)
TA260 002:253.790 - 0.003ms returns 0
TA260 002:253.795 JLINK_WriteReg(R11, 0x00000000)
TA260 002:253.798 - 0.003ms returns 0
TA260 002:253.802 JLINK_WriteReg(R12, 0x00000000)
TA260 002:253.806 - 0.003ms returns 0
TA260 002:253.810 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:253.814 - 0.003ms returns 0
TA260 002:253.818 JLINK_WriteReg(R14, 0x20000001)
TA260 002:253.821 - 0.003ms returns 0
TA260 002:253.825 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 002:253.829 - 0.003ms returns 0
TA260 002:253.833 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:253.836 - 0.003ms returns 0
TA260 002:253.840 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:253.847 - 0.006ms returns 0
TA260 002:253.852 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:253.856 - 0.003ms returns 0
TA260 002:253.860 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:253.863 - 0.003ms returns 0
TA260 002:253.867 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:253.872 - 0.004ms returns 0x0000000A
TA260 002:253.876 JLINK_Go()
TA260 002:253.885   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:256.658 - 2.781ms 
TA260 002:256.680 JLINK_IsHalted()
TA260 002:258.966   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:259.420 - 2.738ms returns TRUE
TA260 002:259.427 JLINK_ReadReg(R15 (PC))
TA260 002:259.433 - 0.006ms returns 0x20000000
TA260 002:259.437 JLINK_ClrBPEx(BPHandle = 0x0000000A)
TA260 002:259.441 - 0.003ms returns 0x00
TA260 002:259.445 JLINK_ReadReg(R0)
TA260 002:259.450 - 0.004ms returns 0x00000000
TA260 002:315.024 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 002:315.038   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 002:315.055   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 002:316.992 - 1.967ms returns 0x184
TA260 002:317.042 JLINK_HasError()
TA260 002:317.048 JLINK_WriteReg(R0, 0x08000000)
TA260 002:317.054 - 0.006ms returns 0
TA260 002:317.058 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 002:317.062 - 0.003ms returns 0
TA260 002:317.066 JLINK_WriteReg(R2, 0x00000002)
TA260 002:317.070 - 0.003ms returns 0
TA260 002:317.074 JLINK_WriteReg(R3, 0x00000000)
TA260 002:317.077 - 0.003ms returns 0
TA260 002:317.081 JLINK_WriteReg(R4, 0x00000000)
TA260 002:317.085 - 0.003ms returns 0
TA260 002:317.089 JLINK_WriteReg(R5, 0x00000000)
TA260 002:317.092 - 0.003ms returns 0
TA260 002:317.097 JLINK_WriteReg(R6, 0x00000000)
TA260 002:317.100 - 0.003ms returns 0
TA260 002:317.104 JLINK_WriteReg(R7, 0x00000000)
TA260 002:317.107 - 0.003ms returns 0
TA260 002:317.111 JLINK_WriteReg(R8, 0x00000000)
TA260 002:317.115 - 0.003ms returns 0
TA260 002:317.119 JLINK_WriteReg(R9, 0x20000180)
TA260 002:317.122 - 0.003ms returns 0
TA260 002:317.126 JLINK_WriteReg(R10, 0x00000000)
TA260 002:317.130 - 0.003ms returns 0
TA260 002:317.134 JLINK_WriteReg(R11, 0x00000000)
TA260 002:317.137 - 0.003ms returns 0
TA260 002:317.141 JLINK_WriteReg(R12, 0x00000000)
TA260 002:317.145 - 0.003ms returns 0
TA260 002:317.149 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:317.153 - 0.004ms returns 0
TA260 002:317.157 JLINK_WriteReg(R14, 0x20000001)
TA260 002:317.160 - 0.003ms returns 0
TA260 002:317.164 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 002:317.168 - 0.003ms returns 0
TA260 002:317.172 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:317.176 - 0.003ms returns 0
TA260 002:317.180 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:317.183 - 0.003ms returns 0
TA260 002:317.187 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:317.190 - 0.003ms returns 0
TA260 002:317.194 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:317.198 - 0.003ms returns 0
TA260 002:317.203 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:317.210   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:317.700 - 0.497ms returns 0x0000000B
TA260 002:317.715 JLINK_Go()
TA260 002:317.721   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 002:318.194   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:321.008 - 3.292ms 
TA260 002:321.016 JLINK_IsHalted()
TA260 002:323.399   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:323.883 - 2.866ms returns TRUE
TA260 002:323.889 JLINK_ReadReg(R15 (PC))
TA260 002:323.893 - 0.004ms returns 0x20000000
TA260 002:323.898 JLINK_ClrBPEx(BPHandle = 0x0000000B)
TA260 002:323.902 - 0.004ms returns 0x00
TA260 002:323.906 JLINK_ReadReg(R0)
TA260 002:323.910 - 0.003ms returns 0x00000000
TA260 002:324.179 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:324.186   Data:  18 18 00 20 C1 01 00 08 71 2A 00 08 55 27 00 08 ...
TA260 002:324.197   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:326.804 - 2.624ms returns 0x27C
TA260 002:326.824 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:326.829   Data:  10 B5 13 46 0A 46 04 46 19 46 FF F7 F0 FF 20 46 ...
TA260 002:326.847   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:328.748 - 1.924ms returns 0x184
TA260 002:328.765 JLINK_HasError()
TA260 002:328.800 JLINK_WriteReg(R0, 0x08000000)
TA260 002:328.806 - 0.005ms returns 0
TA260 002:328.810 JLINK_WriteReg(R1, 0x00000400)
TA260 002:328.814 - 0.003ms returns 0
TA260 002:328.818 JLINK_WriteReg(R2, 0x20000184)
TA260 002:328.821 - 0.003ms returns 0
TA260 002:328.825 JLINK_WriteReg(R3, 0x00000000)
TA260 002:328.828 - 0.003ms returns 0
TA260 002:328.833 JLINK_WriteReg(R4, 0x00000000)
TA260 002:328.836 - 0.003ms returns 0
TA260 002:328.840 JLINK_WriteReg(R5, 0x00000000)
TA260 002:328.843 - 0.003ms returns 0
TA260 002:328.848 JLINK_WriteReg(R6, 0x00000000)
TA260 002:328.851 - 0.003ms returns 0
TA260 002:328.855 JLINK_WriteReg(R7, 0x00000000)
TA260 002:328.858 - 0.003ms returns 0
TA260 002:328.863 JLINK_WriteReg(R8, 0x00000000)
TA260 002:328.870 - 0.003ms returns 0
TA260 002:328.874 JLINK_WriteReg(R9, 0x20000180)
TA260 002:328.878 - 0.003ms returns 0
TA260 002:328.882 JLINK_WriteReg(R10, 0x00000000)
TA260 002:328.885 - 0.003ms returns 0
TA260 002:328.889 JLINK_WriteReg(R11, 0x00000000)
TA260 002:328.892 - 0.003ms returns 0
TA260 002:328.896 JLINK_WriteReg(R12, 0x00000000)
TA260 002:328.900 - 0.003ms returns 0
TA260 002:328.904 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:328.908 - 0.003ms returns 0
TA260 002:328.912 JLINK_WriteReg(R14, 0x20000001)
TA260 002:328.915 - 0.003ms returns 0
TA260 002:328.919 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:328.923 - 0.003ms returns 0
TA260 002:328.927 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:328.930 - 0.003ms returns 0
TA260 002:328.934 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:328.937 - 0.003ms returns 0
TA260 002:328.942 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:328.945 - 0.003ms returns 0
TA260 002:328.949 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:328.952 - 0.003ms returns 0
TA260 002:328.957 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:328.962 - 0.004ms returns 0x0000000C
TA260 002:328.966 JLINK_Go()
TA260 002:328.974   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:331.765 - 2.798ms 
TA260 002:331.779 JLINK_IsHalted()
TA260 002:332.239 - 0.459ms returns FALSE
TA260 002:332.250 JLINK_HasError()
TA260 002:335.524 JLINK_IsHalted()
TA260 002:335.980 - 0.455ms returns FALSE
TA260 002:335.994 JLINK_HasError()
TA260 002:338.029 JLINK_IsHalted()
TA260 002:340.408   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:340.905 - 2.875ms returns TRUE
TA260 002:340.912 JLINK_ReadReg(R15 (PC))
TA260 002:340.917 - 0.005ms returns 0x20000000
TA260 002:340.922 JLINK_ClrBPEx(BPHandle = 0x0000000C)
TA260 002:340.926 - 0.004ms returns 0x00
TA260 002:340.930 JLINK_ReadReg(R0)
TA260 002:340.934 - 0.003ms returns 0x00000000
TA260 002:341.279 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:341.286   Data:  5B D0 C3 F3 0A 54 C1 F3 0A 55 2C 44 A4 F2 F3 34 ...
TA260 002:341.297   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:343.893 - 2.614ms returns 0x27C
TA260 002:343.900 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:343.904   Data:  01 02 06 D0 0A 0D A2 F5 60 72 C1 F3 13 01 00 2A ...
TA260 002:343.910   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:345.770 - 1.868ms returns 0x184
TA260 002:345.786 JLINK_HasError()
TA260 002:345.792 JLINK_WriteReg(R0, 0x08000400)
TA260 002:345.797 - 0.005ms returns 0
TA260 002:345.802 JLINK_WriteReg(R1, 0x00000400)
TA260 002:345.805 - 0.003ms returns 0
TA260 002:345.809 JLINK_WriteReg(R2, 0x20000184)
TA260 002:345.812 - 0.003ms returns 0
TA260 002:345.817 JLINK_WriteReg(R3, 0x00000000)
TA260 002:345.820 - 0.003ms returns 0
TA260 002:345.824 JLINK_WriteReg(R4, 0x00000000)
TA260 002:345.828 - 0.003ms returns 0
TA260 002:345.832 JLINK_WriteReg(R5, 0x00000000)
TA260 002:345.835 - 0.003ms returns 0
TA260 002:345.839 JLINK_WriteReg(R6, 0x00000000)
TA260 002:345.843 - 0.003ms returns 0
TA260 002:345.847 JLINK_WriteReg(R7, 0x00000000)
TA260 002:345.850 - 0.003ms returns 0
TA260 002:345.854 JLINK_WriteReg(R8, 0x00000000)
TA260 002:345.862 - 0.007ms returns 0
TA260 002:345.867 JLINK_WriteReg(R9, 0x20000180)
TA260 002:345.871 - 0.004ms returns 0
TA260 002:345.875 JLINK_WriteReg(R10, 0x00000000)
TA260 002:345.879 - 0.003ms returns 0
TA260 002:345.883 JLINK_WriteReg(R11, 0x00000000)
TA260 002:345.886 - 0.003ms returns 0
TA260 002:345.890 JLINK_WriteReg(R12, 0x00000000)
TA260 002:345.894 - 0.003ms returns 0
TA260 002:345.898 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:345.902 - 0.003ms returns 0
TA260 002:345.906 JLINK_WriteReg(R14, 0x20000001)
TA260 002:345.909 - 0.003ms returns 0
TA260 002:345.914 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:345.917 - 0.004ms returns 0
TA260 002:345.921 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:345.925 - 0.003ms returns 0
TA260 002:345.929 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:345.932 - 0.003ms returns 0
TA260 002:345.936 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:345.939 - 0.003ms returns 0
TA260 002:345.943 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:345.947 - 0.003ms returns 0
TA260 002:345.952 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:345.956 - 0.004ms returns 0x0000000D
TA260 002:345.960 JLINK_Go()
TA260 002:345.969   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:348.712 - 2.751ms 
TA260 002:348.725 JLINK_IsHalted()
TA260 002:349.225 - 0.499ms returns FALSE
TA260 002:349.230 JLINK_HasError()
TA260 002:352.038 JLINK_IsHalted()
TA260 002:352.532 - 0.494ms returns FALSE
TA260 002:352.537 JLINK_HasError()
TA260 002:354.037 JLINK_IsHalted()
TA260 002:356.432   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:356.955 - 2.917ms returns TRUE
TA260 002:356.963 JLINK_ReadReg(R15 (PC))
TA260 002:356.969 - 0.006ms returns 0x20000000
TA260 002:356.974 JLINK_ClrBPEx(BPHandle = 0x0000000D)
TA260 002:356.978 - 0.003ms returns 0x00
TA260 002:356.982 JLINK_ReadReg(R0)
TA260 002:356.986 - 0.003ms returns 0x00000000
TA260 002:357.345 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:357.353   Data:  F0 4D 92 46 9B 46 11 B1 B1 FA 81 F2 02 E0 B0 FA ...
TA260 002:357.365   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:359.990 - 2.644ms returns 0x27C
TA260 002:359.999 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:360.003   Data:  C1 FD 20 46 4F F4 00 51 00 22 00 F0 BB FD 01 20 ...
TA260 002:360.012   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:361.942 - 1.942ms returns 0x184
TA260 002:361.956 JLINK_HasError()
TA260 002:361.962 JLINK_WriteReg(R0, 0x08000800)
TA260 002:361.968 - 0.005ms returns 0
TA260 002:361.972 JLINK_WriteReg(R1, 0x00000400)
TA260 002:361.975 - 0.003ms returns 0
TA260 002:361.979 JLINK_WriteReg(R2, 0x20000184)
TA260 002:361.983 - 0.003ms returns 0
TA260 002:361.987 JLINK_WriteReg(R3, 0x00000000)
TA260 002:361.990 - 0.003ms returns 0
TA260 002:361.994 JLINK_WriteReg(R4, 0x00000000)
TA260 002:361.997 - 0.003ms returns 0
TA260 002:362.001 JLINK_WriteReg(R5, 0x00000000)
TA260 002:362.005 - 0.003ms returns 0
TA260 002:362.009 JLINK_WriteReg(R6, 0x00000000)
TA260 002:362.012 - 0.003ms returns 0
TA260 002:362.016 JLINK_WriteReg(R7, 0x00000000)
TA260 002:362.020 - 0.003ms returns 0
TA260 002:362.024 JLINK_WriteReg(R8, 0x00000000)
TA260 002:362.027 - 0.003ms returns 0
TA260 002:362.031 JLINK_WriteReg(R9, 0x20000180)
TA260 002:362.035 - 0.003ms returns 0
TA260 002:362.039 JLINK_WriteReg(R10, 0x00000000)
TA260 002:362.042 - 0.003ms returns 0
TA260 002:362.046 JLINK_WriteReg(R11, 0x00000000)
TA260 002:362.050 - 0.003ms returns 0
TA260 002:362.054 JLINK_WriteReg(R12, 0x00000000)
TA260 002:362.057 - 0.003ms returns 0
TA260 002:362.061 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:362.065 - 0.004ms returns 0
TA260 002:362.069 JLINK_WriteReg(R14, 0x20000001)
TA260 002:362.072 - 0.003ms returns 0
TA260 002:362.076 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:362.080 - 0.003ms returns 0
TA260 002:362.084 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:362.087 - 0.003ms returns 0
TA260 002:362.091 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:362.095 - 0.003ms returns 0
TA260 002:362.099 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:362.105 - 0.006ms returns 0
TA260 002:362.110 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:362.114 - 0.003ms returns 0
TA260 002:362.119 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:362.123 - 0.004ms returns 0x0000000E
TA260 002:362.127 JLINK_Go()
TA260 002:362.135   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:364.868 - 2.739ms 
TA260 002:364.881 JLINK_IsHalted()
TA260 002:365.462 - 0.581ms returns FALSE
TA260 002:365.533 JLINK_HasError()
TA260 002:369.056 JLINK_IsHalted()
TA260 002:371.450   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:371.951 - 2.895ms returns TRUE
TA260 002:371.958 JLINK_ReadReg(R15 (PC))
TA260 002:371.963 - 0.005ms returns 0x20000000
TA260 002:371.967 JLINK_ClrBPEx(BPHandle = 0x0000000E)
TA260 002:371.971 - 0.003ms returns 0x00
TA260 002:371.976 JLINK_ReadReg(R0)
TA260 002:371.979 - 0.003ms returns 0x00000000
TA260 002:372.294 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:372.302   Data:  BD E8 F0 40 00 F0 FC BC 2D E9 F0 4F 81 B0 41 F6 ...
TA260 002:372.312   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:374.841 - 2.546ms returns 0x27C
TA260 002:374.856 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:374.860   Data:  05 F0 01 02 30 46 4F F4 80 51 00 F0 BB FB 20 46 ...
TA260 002:374.870   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:376.768 - 1.911ms returns 0x184
TA260 002:376.786 JLINK_HasError()
TA260 002:376.792 JLINK_WriteReg(R0, 0x08000C00)
TA260 002:376.798 - 0.006ms returns 0
TA260 002:376.802 JLINK_WriteReg(R1, 0x00000400)
TA260 002:376.806 - 0.003ms returns 0
TA260 002:376.810 JLINK_WriteReg(R2, 0x20000184)
TA260 002:376.813 - 0.003ms returns 0
TA260 002:376.817 JLINK_WriteReg(R3, 0x00000000)
TA260 002:376.820 - 0.003ms returns 0
TA260 002:376.824 JLINK_WriteReg(R4, 0x00000000)
TA260 002:376.828 - 0.003ms returns 0
TA260 002:376.832 JLINK_WriteReg(R5, 0x00000000)
TA260 002:376.835 - 0.003ms returns 0
TA260 002:376.839 JLINK_WriteReg(R6, 0x00000000)
TA260 002:376.843 - 0.003ms returns 0
TA260 002:376.847 JLINK_WriteReg(R7, 0x00000000)
TA260 002:376.850 - 0.003ms returns 0
TA260 002:376.854 JLINK_WriteReg(R8, 0x00000000)
TA260 002:376.858 - 0.003ms returns 0
TA260 002:376.862 JLINK_WriteReg(R9, 0x20000180)
TA260 002:376.865 - 0.003ms returns 0
TA260 002:376.869 JLINK_WriteReg(R10, 0x00000000)
TA260 002:376.873 - 0.003ms returns 0
TA260 002:376.877 JLINK_WriteReg(R11, 0x00000000)
TA260 002:376.880 - 0.003ms returns 0
TA260 002:376.884 JLINK_WriteReg(R12, 0x00000000)
TA260 002:376.888 - 0.003ms returns 0
TA260 002:376.892 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:376.896 - 0.004ms returns 0
TA260 002:376.900 JLINK_WriteReg(R14, 0x20000001)
TA260 002:376.903 - 0.003ms returns 0
TA260 002:376.907 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:376.911 - 0.003ms returns 0
TA260 002:376.915 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:376.918 - 0.003ms returns 0
TA260 002:376.922 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:376.926 - 0.003ms returns 0
TA260 002:376.930 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:376.933 - 0.003ms returns 0
TA260 002:376.937 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:376.940 - 0.003ms returns 0
TA260 002:376.945 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:376.971 - 0.026ms returns 0x0000000F
TA260 002:376.976 JLINK_Go()
TA260 002:376.991   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:379.829 - 2.853ms 
TA260 002:379.847 JLINK_IsHalted()
TA260 002:380.411 - 0.563ms returns FALSE
TA260 002:380.421 JLINK_HasError()
TA260 002:382.560 JLINK_IsHalted()
TA260 002:383.054 - 0.493ms returns FALSE
TA260 002:383.060 JLINK_HasError()
TA260 002:385.563 JLINK_IsHalted()
TA260 002:387.878   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:388.408 - 2.845ms returns TRUE
TA260 002:388.416 JLINK_ReadReg(R15 (PC))
TA260 002:388.421 - 0.005ms returns 0x20000000
TA260 002:388.425 JLINK_ClrBPEx(BPHandle = 0x0000000F)
TA260 002:388.429 - 0.003ms returns 0x00
TA260 002:388.434 JLINK_ReadReg(R0)
TA260 002:388.437 - 0.003ms returns 0x00000000
TA260 002:388.783 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:388.793   Data:  00 90 00 98 14 28 E7 DB 02 B0 BD EC 0A 8B BD E8 ...
TA260 002:388.804   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:391.475 - 2.691ms returns 0x27C
TA260 002:391.483 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:391.486   Data:  07 57 45 EA 06 46 37 43 FE 32 27 43 47 EA 02 62 ...
TA260 002:391.494   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:393.409 - 1.926ms returns 0x184
TA260 002:393.415 JLINK_HasError()
TA260 002:393.420 JLINK_WriteReg(R0, 0x08001000)
TA260 002:393.424 - 0.004ms returns 0
TA260 002:393.429 JLINK_WriteReg(R1, 0x00000400)
TA260 002:393.432 - 0.003ms returns 0
TA260 002:393.436 JLINK_WriteReg(R2, 0x20000184)
TA260 002:393.440 - 0.003ms returns 0
TA260 002:393.443 JLINK_WriteReg(R3, 0x00000000)
TA260 002:393.447 - 0.003ms returns 0
TA260 002:393.451 JLINK_WriteReg(R4, 0x00000000)
TA260 002:393.454 - 0.003ms returns 0
TA260 002:393.458 JLINK_WriteReg(R5, 0x00000000)
TA260 002:393.462 - 0.003ms returns 0
TA260 002:393.466 JLINK_WriteReg(R6, 0x00000000)
TA260 002:393.469 - 0.003ms returns 0
TA260 002:393.473 JLINK_WriteReg(R7, 0x00000000)
TA260 002:393.476 - 0.003ms returns 0
TA260 002:393.481 JLINK_WriteReg(R8, 0x00000000)
TA260 002:393.485 - 0.004ms returns 0
TA260 002:393.489 JLINK_WriteReg(R9, 0x20000180)
TA260 002:393.492 - 0.003ms returns 0
TA260 002:393.496 JLINK_WriteReg(R10, 0x00000000)
TA260 002:393.500 - 0.003ms returns 0
TA260 002:393.504 JLINK_WriteReg(R11, 0x00000000)
TA260 002:393.507 - 0.003ms returns 0
TA260 002:393.511 JLINK_WriteReg(R12, 0x00000000)
TA260 002:393.514 - 0.003ms returns 0
TA260 002:393.518 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:393.522 - 0.003ms returns 0
TA260 002:393.526 JLINK_WriteReg(R14, 0x20000001)
TA260 002:393.530 - 0.003ms returns 0
TA260 002:393.534 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:393.537 - 0.003ms returns 0
TA260 002:393.541 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:393.544 - 0.003ms returns 0
TA260 002:393.555 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:393.559 - 0.003ms returns 0
TA260 002:393.563 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:393.566 - 0.003ms returns 0
TA260 002:393.570 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:393.574 - 0.003ms returns 0
TA260 002:393.578 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:393.582 - 0.004ms returns 0x00000010
TA260 002:393.586 JLINK_Go()
TA260 002:393.594   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:396.433 - 2.845ms 
TA260 002:396.456 JLINK_IsHalted()
TA260 002:396.910 - 0.454ms returns FALSE
TA260 002:396.924 JLINK_HasError()
TA260 002:399.071 JLINK_IsHalted()
TA260 002:399.581 - 0.509ms returns FALSE
TA260 002:399.592 JLINK_HasError()
TA260 002:402.574 JLINK_IsHalted()
TA260 002:404.929   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:405.430 - 2.855ms returns TRUE
TA260 002:405.443 JLINK_ReadReg(R15 (PC))
TA260 002:405.449 - 0.005ms returns 0x20000000
TA260 002:405.453 JLINK_ClrBPEx(BPHandle = 0x00000010)
TA260 002:405.457 - 0.004ms returns 0x00
TA260 002:405.462 JLINK_ReadReg(R0)
TA260 002:405.465 - 0.003ms returns 0x00000000
TA260 002:405.792 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:405.799   Data:  3F 21 01 FA 00 F0 B0 60 01 20 84 F8 35 00 00 20 ...
TA260 002:405.810   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:408.414 - 2.621ms returns 0x27C
TA260 002:408.431 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:408.435   Data:  C2 F2 00 01 4F F4 7A 73 09 68 B3 FB F2 F2 04 46 ...
TA260 002:408.445   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:410.408 - 1.977ms returns 0x184
TA260 002:410.418 JLINK_HasError()
TA260 002:410.423 JLINK_WriteReg(R0, 0x08001400)
TA260 002:410.428 - 0.005ms returns 0
TA260 002:410.432 JLINK_WriteReg(R1, 0x00000400)
TA260 002:410.436 - 0.003ms returns 0
TA260 002:410.440 JLINK_WriteReg(R2, 0x20000184)
TA260 002:410.443 - 0.003ms returns 0
TA260 002:410.447 JLINK_WriteReg(R3, 0x00000000)
TA260 002:410.451 - 0.003ms returns 0
TA260 002:410.455 JLINK_WriteReg(R4, 0x00000000)
TA260 002:410.464 - 0.009ms returns 0
TA260 002:410.468 JLINK_WriteReg(R5, 0x00000000)
TA260 002:410.472 - 0.003ms returns 0
TA260 002:410.476 JLINK_WriteReg(R6, 0x00000000)
TA260 002:410.479 - 0.003ms returns 0
TA260 002:410.483 JLINK_WriteReg(R7, 0x00000000)
TA260 002:410.486 - 0.003ms returns 0
TA260 002:410.491 JLINK_WriteReg(R8, 0x00000000)
TA260 002:410.494 - 0.003ms returns 0
TA260 002:410.498 JLINK_WriteReg(R9, 0x20000180)
TA260 002:410.501 - 0.003ms returns 0
TA260 002:410.539 JLINK_WriteReg(R10, 0x00000000)
TA260 002:410.543 - 0.003ms returns 0
TA260 002:410.547 JLINK_WriteReg(R11, 0x00000000)
TA260 002:410.550 - 0.003ms returns 0
TA260 002:410.554 JLINK_WriteReg(R12, 0x00000000)
TA260 002:410.558 - 0.003ms returns 0
TA260 002:410.562 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:410.566 - 0.004ms returns 0
TA260 002:410.570 JLINK_WriteReg(R14, 0x20000001)
TA260 002:410.574 - 0.004ms returns 0
TA260 002:410.578 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:410.581 - 0.003ms returns 0
TA260 002:410.585 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:410.589 - 0.003ms returns 0
TA260 002:410.593 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:410.596 - 0.003ms returns 0
TA260 002:410.600 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:410.604 - 0.003ms returns 0
TA260 002:410.608 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:410.627 - 0.019ms returns 0
TA260 002:410.632 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:410.636 - 0.004ms returns 0x00000011
TA260 002:410.640 JLINK_Go()
TA260 002:410.648   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:413.420 - 2.780ms 
TA260 002:413.435 JLINK_IsHalted()
TA260 002:413.905 - 0.470ms returns FALSE
TA260 002:413.917 JLINK_HasError()
TA260 002:421.138 JLINK_IsHalted()
TA260 002:423.461   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:423.951 - 2.812ms returns TRUE
TA260 002:423.958 JLINK_ReadReg(R15 (PC))
TA260 002:423.963 - 0.005ms returns 0x20000000
TA260 002:423.968 JLINK_ClrBPEx(BPHandle = 0x00000011)
TA260 002:423.972 - 0.004ms returns 0x00
TA260 002:423.976 JLINK_ReadReg(R0)
TA260 002:423.980 - 0.003ms returns 0x00000000
TA260 002:424.287 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:424.296   Data:  02 B0 70 BD 00 20 02 B0 70 BD 00 00 70 47 00 00 ...
TA260 002:424.306   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:426.906 - 2.618ms returns 0x27C
TA260 002:426.929 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:426.933   Data:  30 68 80 03 41 D5 60 68 00 28 3E D1 01 20 02 B0 ...
TA260 002:426.946   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:428.902 - 1.973ms returns 0x184
TA260 002:428.918 JLINK_HasError()
TA260 002:428.924 JLINK_WriteReg(R0, 0x08001800)
TA260 002:428.930 - 0.006ms returns 0
TA260 002:428.934 JLINK_WriteReg(R1, 0x00000400)
TA260 002:428.938 - 0.003ms returns 0
TA260 002:428.942 JLINK_WriteReg(R2, 0x20000184)
TA260 002:428.945 - 0.003ms returns 0
TA260 002:428.949 JLINK_WriteReg(R3, 0x00000000)
TA260 002:428.953 - 0.003ms returns 0
TA260 002:428.957 JLINK_WriteReg(R4, 0x00000000)
TA260 002:428.960 - 0.003ms returns 0
TA260 002:428.965 JLINK_WriteReg(R5, 0x00000000)
TA260 002:428.968 - 0.003ms returns 0
TA260 002:428.972 JLINK_WriteReg(R6, 0x00000000)
TA260 002:428.982 - 0.009ms returns 0
TA260 002:428.987 JLINK_WriteReg(R7, 0x00000000)
TA260 002:428.992 - 0.004ms returns 0
TA260 002:428.996 JLINK_WriteReg(R8, 0x00000000)
TA260 002:428.999 - 0.003ms returns 0
TA260 002:429.003 JLINK_WriteReg(R9, 0x20000180)
TA260 002:429.007 - 0.003ms returns 0
TA260 002:429.011 JLINK_WriteReg(R10, 0x00000000)
TA260 002:429.014 - 0.003ms returns 0
TA260 002:429.018 JLINK_WriteReg(R11, 0x00000000)
TA260 002:429.022 - 0.003ms returns 0
TA260 002:429.026 JLINK_WriteReg(R12, 0x00000000)
TA260 002:429.029 - 0.003ms returns 0
TA260 002:429.033 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:429.037 - 0.004ms returns 0
TA260 002:429.041 JLINK_WriteReg(R14, 0x20000001)
TA260 002:429.045 - 0.003ms returns 0
TA260 002:429.049 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:429.055 - 0.006ms returns 0
TA260 002:429.062 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:429.065 - 0.003ms returns 0
TA260 002:429.069 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:429.073 - 0.003ms returns 0
TA260 002:429.077 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:429.080 - 0.003ms returns 0
TA260 002:429.084 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:429.091 - 0.007ms returns 0
TA260 002:429.096 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:429.100 - 0.004ms returns 0x00000012
TA260 002:429.104 JLINK_Go()
TA260 002:429.113   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:431.996 - 2.891ms 
TA260 002:432.012 JLINK_IsHalted()
TA260 002:432.533 - 0.520ms returns FALSE
TA260 002:432.541 JLINK_HasError()
TA260 002:436.386 JLINK_IsHalted()
TA260 002:438.748   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:439.260 - 2.874ms returns TRUE
TA260 002:439.267 JLINK_ReadReg(R15 (PC))
TA260 002:439.272 - 0.005ms returns 0x20000000
TA260 002:439.277 JLINK_ClrBPEx(BPHandle = 0x00000012)
TA260 002:439.281 - 0.003ms returns 0x00
TA260 002:439.285 JLINK_ReadReg(R0)
TA260 002:439.288 - 0.003ms returns 0x00000000
TA260 002:439.638 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:439.645   Data:  C4 F2 00 07 38 68 C0 05 26 D4 38 68 40 F4 80 70 ...
TA260 002:439.656   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:442.261 - 2.623ms returns 0x27C
TA260 002:442.269 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:442.272   Data:  02 08 04 F5 00 60 29 46 CD E9 04 69 06 97 FF F7 ...
TA260 002:442.280   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:444.164 - 1.894ms returns 0x184
TA260 002:444.179 JLINK_HasError()
TA260 002:444.185 JLINK_WriteReg(R0, 0x08001C00)
TA260 002:444.191 - 0.006ms returns 0
TA260 002:444.195 JLINK_WriteReg(R1, 0x00000400)
TA260 002:444.199 - 0.003ms returns 0
TA260 002:444.203 JLINK_WriteReg(R2, 0x20000184)
TA260 002:444.207 - 0.004ms returns 0
TA260 002:444.211 JLINK_WriteReg(R3, 0x00000000)
TA260 002:444.215 - 0.003ms returns 0
TA260 002:444.219 JLINK_WriteReg(R4, 0x00000000)
TA260 002:444.222 - 0.003ms returns 0
TA260 002:444.226 JLINK_WriteReg(R5, 0x00000000)
TA260 002:444.229 - 0.003ms returns 0
TA260 002:444.233 JLINK_WriteReg(R6, 0x00000000)
TA260 002:444.237 - 0.003ms returns 0
TA260 002:444.241 JLINK_WriteReg(R7, 0x00000000)
TA260 002:444.244 - 0.003ms returns 0
TA260 002:444.248 JLINK_WriteReg(R8, 0x00000000)
TA260 002:444.252 - 0.003ms returns 0
TA260 002:444.256 JLINK_WriteReg(R9, 0x20000180)
TA260 002:444.259 - 0.003ms returns 0
TA260 002:444.263 JLINK_WriteReg(R10, 0x00000000)
TA260 002:444.266 - 0.003ms returns 0
TA260 002:444.270 JLINK_WriteReg(R11, 0x00000000)
TA260 002:444.274 - 0.003ms returns 0
TA260 002:444.278 JLINK_WriteReg(R12, 0x00000000)
TA260 002:444.281 - 0.003ms returns 0
TA260 002:444.286 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:444.290 - 0.003ms returns 0
TA260 002:444.294 JLINK_WriteReg(R14, 0x20000001)
TA260 002:444.297 - 0.003ms returns 0
TA260 002:444.301 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:444.305 - 0.003ms returns 0
TA260 002:444.309 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:444.312 - 0.003ms returns 0
TA260 002:444.316 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:444.320 - 0.003ms returns 0
TA260 002:444.324 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:444.332 - 0.008ms returns 0
TA260 002:444.336 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:444.340 - 0.003ms returns 0
TA260 002:444.344 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:444.348 - 0.004ms returns 0x00000013
TA260 002:444.353 JLINK_Go()
TA260 002:444.361   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:447.131 - 2.777ms 
TA260 002:447.150 JLINK_IsHalted()
TA260 002:447.619 - 0.468ms returns FALSE
TA260 002:447.630 JLINK_HasError()
TA260 002:454.908 JLINK_IsHalted()
TA260 002:457.268   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:457.749 - 2.841ms returns TRUE
TA260 002:457.756 JLINK_ReadReg(R15 (PC))
TA260 002:457.763 - 0.006ms returns 0x20000000
TA260 002:457.768 JLINK_ClrBPEx(BPHandle = 0x00000013)
TA260 002:457.815 - 0.047ms returns 0x00
TA260 002:457.820 JLINK_ReadReg(R0)
TA260 002:457.824 - 0.003ms returns 0x00000000
TA260 002:458.283 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:458.295   Data:  03 1F 02 68 21 F4 80 71 42 E8 03 13 00 2B F5 D1 ...
TA260 002:458.320   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:460.941 - 2.658ms returns 0x27C
TA260 002:460.954 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:460.958   Data:  01 01 88 42 6B D1 00 20 43 F6 30 01 00 90 C4 F2 ...
TA260 002:460.967   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:462.846 - 1.891ms returns 0x184
TA260 002:462.852 JLINK_HasError()
TA260 002:462.858 JLINK_WriteReg(R0, 0x08002000)
TA260 002:462.862 - 0.004ms returns 0
TA260 002:462.866 JLINK_WriteReg(R1, 0x00000400)
TA260 002:462.870 - 0.003ms returns 0
TA260 002:462.874 JLINK_WriteReg(R2, 0x20000184)
TA260 002:462.877 - 0.003ms returns 0
TA260 002:462.881 JLINK_WriteReg(R3, 0x00000000)
TA260 002:462.884 - 0.003ms returns 0
TA260 002:462.889 JLINK_WriteReg(R4, 0x00000000)
TA260 002:462.892 - 0.003ms returns 0
TA260 002:462.896 JLINK_WriteReg(R5, 0x00000000)
TA260 002:462.900 - 0.003ms returns 0
TA260 002:462.904 JLINK_WriteReg(R6, 0x00000000)
TA260 002:462.907 - 0.003ms returns 0
TA260 002:462.911 JLINK_WriteReg(R7, 0x00000000)
TA260 002:462.915 - 0.003ms returns 0
TA260 002:462.919 JLINK_WriteReg(R8, 0x00000000)
TA260 002:462.922 - 0.003ms returns 0
TA260 002:462.926 JLINK_WriteReg(R9, 0x20000180)
TA260 002:462.930 - 0.003ms returns 0
TA260 002:462.934 JLINK_WriteReg(R10, 0x00000000)
TA260 002:462.937 - 0.003ms returns 0
TA260 002:462.941 JLINK_WriteReg(R11, 0x00000000)
TA260 002:462.944 - 0.003ms returns 0
TA260 002:462.948 JLINK_WriteReg(R12, 0x00000000)
TA260 002:462.952 - 0.003ms returns 0
TA260 002:462.956 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:462.959 - 0.003ms returns 0
TA260 002:462.963 JLINK_WriteReg(R14, 0x20000001)
TA260 002:462.967 - 0.003ms returns 0
TA260 002:462.971 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:462.974 - 0.003ms returns 0
TA260 002:462.979 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:462.982 - 0.003ms returns 0
TA260 002:462.986 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:462.990 - 0.003ms returns 0
TA260 002:462.994 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:462.997 - 0.003ms returns 0
TA260 002:463.001 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:463.004 - 0.003ms returns 0
TA260 002:463.009 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:463.013 - 0.004ms returns 0x00000014
TA260 002:463.017 JLINK_Go()
TA260 002:463.024   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:465.764 - 2.746ms 
TA260 002:465.781 JLINK_IsHalted()
TA260 002:466.241 - 0.459ms returns FALSE
TA260 002:466.248 JLINK_HasError()
TA260 002:471.917 JLINK_IsHalted()
TA260 002:474.251   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:474.706 - 2.789ms returns TRUE
TA260 002:474.714 JLINK_ReadReg(R15 (PC))
TA260 002:474.720 - 0.005ms returns 0x20000000
TA260 002:474.724 JLINK_ClrBPEx(BPHandle = 0x00000014)
TA260 002:474.728 - 0.004ms returns 0x00
TA260 002:474.733 JLINK_ReadReg(R0)
TA260 002:474.737 - 0.004ms returns 0x00000000
TA260 002:475.072 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:475.081   Data:  01 21 01 70 00 21 0C F8 0E 10 00 E0 00 21 C1 80 ...
TA260 002:475.093   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:477.698 - 2.625ms returns 0x27C
TA260 002:477.719 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:477.723   Data:  41 E8 05 02 00 2A F5 D1 D0 E7 20 20 84 F8 3D 00 ...
TA260 002:477.734   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:479.611 - 1.891ms returns 0x184
TA260 002:479.618 JLINK_HasError()
TA260 002:479.623 JLINK_WriteReg(R0, 0x08002400)
TA260 002:479.628 - 0.005ms returns 0
TA260 002:479.632 JLINK_WriteReg(R1, 0x00000400)
TA260 002:479.636 - 0.003ms returns 0
TA260 002:479.640 JLINK_WriteReg(R2, 0x20000184)
TA260 002:479.643 - 0.003ms returns 0
TA260 002:479.647 JLINK_WriteReg(R3, 0x00000000)
TA260 002:479.651 - 0.003ms returns 0
TA260 002:479.660 JLINK_WriteReg(R4, 0x00000000)
TA260 002:479.663 - 0.003ms returns 0
TA260 002:479.667 JLINK_WriteReg(R5, 0x00000000)
TA260 002:479.671 - 0.003ms returns 0
TA260 002:479.675 JLINK_WriteReg(R6, 0x00000000)
TA260 002:479.678 - 0.003ms returns 0
TA260 002:479.682 JLINK_WriteReg(R7, 0x00000000)
TA260 002:479.686 - 0.003ms returns 0
TA260 002:479.690 JLINK_WriteReg(R8, 0x00000000)
TA260 002:479.694 - 0.003ms returns 0
TA260 002:479.698 JLINK_WriteReg(R9, 0x20000180)
TA260 002:479.701 - 0.003ms returns 0
TA260 002:479.705 JLINK_WriteReg(R10, 0x00000000)
TA260 002:479.708 - 0.003ms returns 0
TA260 002:479.713 JLINK_WriteReg(R11, 0x00000000)
TA260 002:479.716 - 0.003ms returns 0
TA260 002:479.720 JLINK_WriteReg(R12, 0x00000000)
TA260 002:479.723 - 0.003ms returns 0
TA260 002:479.727 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:479.731 - 0.003ms returns 0
TA260 002:479.735 JLINK_WriteReg(R14, 0x20000001)
TA260 002:479.738 - 0.003ms returns 0
TA260 002:479.743 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:479.746 - 0.003ms returns 0
TA260 002:479.750 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:479.754 - 0.003ms returns 0
TA260 002:479.758 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:479.761 - 0.003ms returns 0
TA260 002:479.765 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:479.768 - 0.003ms returns 0
TA260 002:479.772 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:479.776 - 0.003ms returns 0
TA260 002:479.780 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:479.785 - 0.004ms returns 0x00000015
TA260 002:479.789 JLINK_Go()
TA260 002:479.797   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:482.622 - 2.832ms 
TA260 002:482.630 JLINK_IsHalted()
TA260 002:483.110 - 0.480ms returns FALSE
TA260 002:483.116 JLINK_HasError()
TA260 002:486.433 JLINK_IsHalted()
TA260 002:486.948 - 0.514ms returns FALSE
TA260 002:486.965 JLINK_HasError()
TA260 002:489.933 JLINK_IsHalted()
TA260 002:492.327   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:492.815 - 2.882ms returns TRUE
TA260 002:492.822 JLINK_ReadReg(R15 (PC))
TA260 002:492.827 - 0.005ms returns 0x20000000
TA260 002:492.832 JLINK_ClrBPEx(BPHandle = 0x00000015)
TA260 002:492.836 - 0.003ms returns 0x00
TA260 002:492.840 JLINK_ReadReg(R0)
TA260 002:492.843 - 0.003ms returns 0x00000000
TA260 002:493.158 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:493.165   Data:  C0 E9 0E 11 CD E9 06 21 01 A9 00 22 C0 E9 00 C3 ...
TA260 002:493.176   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:495.806 - 2.647ms returns 0x27C
TA260 002:495.824 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:495.828   Data:  00 21 C2 F2 00 00 C4 F2 40 01 4C F6 CD 42 41 60 ...
TA260 002:495.838   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:497.748 - 1.923ms returns 0x184
TA260 002:497.762 JLINK_HasError()
TA260 002:497.767 JLINK_WriteReg(R0, 0x08002800)
TA260 002:497.773 - 0.005ms returns 0
TA260 002:497.777 JLINK_WriteReg(R1, 0x00000400)
TA260 002:497.781 - 0.003ms returns 0
TA260 002:497.785 JLINK_WriteReg(R2, 0x20000184)
TA260 002:497.788 - 0.003ms returns 0
TA260 002:497.792 JLINK_WriteReg(R3, 0x00000000)
TA260 002:497.796 - 0.003ms returns 0
TA260 002:497.800 JLINK_WriteReg(R4, 0x00000000)
TA260 002:497.803 - 0.003ms returns 0
TA260 002:497.807 JLINK_WriteReg(R5, 0x00000000)
TA260 002:497.811 - 0.003ms returns 0
TA260 002:497.815 JLINK_WriteReg(R6, 0x00000000)
TA260 002:497.818 - 0.003ms returns 0
TA260 002:497.822 JLINK_WriteReg(R7, 0x00000000)
TA260 002:497.826 - 0.003ms returns 0
TA260 002:497.830 JLINK_WriteReg(R8, 0x00000000)
TA260 002:497.834 - 0.003ms returns 0
TA260 002:497.838 JLINK_WriteReg(R9, 0x20000180)
TA260 002:497.841 - 0.003ms returns 0
TA260 002:497.845 JLINK_WriteReg(R10, 0x00000000)
TA260 002:497.848 - 0.003ms returns 0
TA260 002:497.852 JLINK_WriteReg(R11, 0x00000000)
TA260 002:497.856 - 0.003ms returns 0
TA260 002:497.860 JLINK_WriteReg(R12, 0x00000000)
TA260 002:497.863 - 0.003ms returns 0
TA260 002:497.867 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:497.874 - 0.007ms returns 0
TA260 002:497.880 JLINK_WriteReg(R14, 0x20000001)
TA260 002:497.883 - 0.003ms returns 0
TA260 002:497.887 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:497.890 - 0.003ms returns 0
TA260 002:497.894 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:497.898 - 0.003ms returns 0
TA260 002:497.902 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:497.905 - 0.003ms returns 0
TA260 002:497.909 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:497.913 - 0.003ms returns 0
TA260 002:497.917 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:497.920 - 0.003ms returns 0
TA260 002:497.929 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:497.934 - 0.005ms returns 0x00000016
TA260 002:497.939 JLINK_Go()
TA260 002:497.947   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:500.757 - 2.818ms 
TA260 002:500.770 JLINK_IsHalted()
TA260 002:501.270 - 0.500ms returns FALSE
TA260 002:501.276 JLINK_HasError()
TA260 002:504.438 JLINK_IsHalted()
TA260 002:504.941 - 0.502ms returns FALSE
TA260 002:504.949 JLINK_HasError()
TA260 002:506.452 JLINK_IsHalted()
TA260 002:508.889   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:509.429 - 2.976ms returns TRUE
TA260 002:509.440 JLINK_ReadReg(R15 (PC))
TA260 002:509.446 - 0.005ms returns 0x20000000
TA260 002:509.478 JLINK_ClrBPEx(BPHandle = 0x00000016)
TA260 002:509.489 - 0.010ms returns 0x00
TA260 002:509.494 JLINK_ReadReg(R0)
TA260 002:509.497 - 0.003ms returns 0x00000000
TA260 002:509.836 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:509.843   Data:  70 47 00 00 FE F7 08 BD 10 B5 92 B0 06 AC 20 46 ...
TA260 002:509.854   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:512.489 - 2.653ms returns 0x27C
TA260 002:512.499 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:512.503   Data:  02 10 C1 F3 43 11 08 44 21 68 88 60 B0 BD 00 00 ...
TA260 002:512.512   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:514.382 - 1.883ms returns 0x184
TA260 002:514.389 JLINK_HasError()
TA260 002:514.394 JLINK_WriteReg(R0, 0x08002C00)
TA260 002:514.398 - 0.004ms returns 0
TA260 002:514.403 JLINK_WriteReg(R1, 0x00000400)
TA260 002:514.406 - 0.003ms returns 0
TA260 002:514.410 JLINK_WriteReg(R2, 0x20000184)
TA260 002:514.413 - 0.003ms returns 0
TA260 002:514.417 JLINK_WriteReg(R3, 0x00000000)
TA260 002:514.421 - 0.003ms returns 0
TA260 002:514.425 JLINK_WriteReg(R4, 0x00000000)
TA260 002:514.428 - 0.003ms returns 0
TA260 002:514.432 JLINK_WriteReg(R5, 0x00000000)
TA260 002:514.435 - 0.003ms returns 0
TA260 002:514.439 JLINK_WriteReg(R6, 0x00000000)
TA260 002:514.443 - 0.003ms returns 0
TA260 002:514.447 JLINK_WriteReg(R7, 0x00000000)
TA260 002:514.450 - 0.003ms returns 0
TA260 002:514.454 JLINK_WriteReg(R8, 0x00000000)
TA260 002:514.458 - 0.003ms returns 0
TA260 002:514.462 JLINK_WriteReg(R9, 0x20000180)
TA260 002:514.466 - 0.003ms returns 0
TA260 002:514.470 JLINK_WriteReg(R10, 0x00000000)
TA260 002:514.473 - 0.003ms returns 0
TA260 002:514.477 JLINK_WriteReg(R11, 0x00000000)
TA260 002:514.480 - 0.003ms returns 0
TA260 002:514.484 JLINK_WriteReg(R12, 0x00000000)
TA260 002:514.488 - 0.003ms returns 0
TA260 002:514.492 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:514.496 - 0.003ms returns 0
TA260 002:514.500 JLINK_WriteReg(R14, 0x20000001)
TA260 002:514.503 - 0.003ms returns 0
TA260 002:514.507 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:514.510 - 0.003ms returns 0
TA260 002:514.514 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:514.518 - 0.003ms returns 0
TA260 002:514.522 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:514.525 - 0.003ms returns 0
TA260 002:514.529 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:514.533 - 0.003ms returns 0
TA260 002:514.537 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:514.540 - 0.003ms returns 0
TA260 002:514.545 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:514.549 - 0.004ms returns 0x00000017
TA260 002:514.553 JLINK_Go()
TA260 002:514.561   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:517.326 - 2.772ms 
TA260 002:517.347 JLINK_IsHalted()
TA260 002:517.779 - 0.432ms returns FALSE
TA260 002:517.789 JLINK_HasError()
TA260 002:520.451 JLINK_IsHalted()
TA260 002:520.951 - 0.500ms returns FALSE
TA260 002:520.957 JLINK_HasError()
TA260 002:523.451 JLINK_IsHalted()
TA260 002:525.900   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:526.470 - 3.018ms returns TRUE
TA260 002:526.487 JLINK_ReadReg(R15 (PC))
TA260 002:526.493 - 0.006ms returns 0x20000000
TA260 002:526.498 JLINK_ClrBPEx(BPHandle = 0x00000017)
TA260 002:526.502 - 0.004ms returns 0x00
TA260 002:526.506 JLINK_ReadReg(R0)
TA260 002:526.510 - 0.003ms returns 0x00000000
TA260 002:526.882 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:526.889   Data:  C2 F2 00 00 9F ED 11 8A 9F ED 11 9A 05 1F B7 EE ...
TA260 002:526.907   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:529.476 - 2.594ms returns 0x27C
TA260 002:529.489 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:529.493   Data:  9F ED 4E 1A B4 EE 41 0A F1 EE 10 FA 05 DD 9F ED ...
TA260 002:529.502   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:531.413 - 1.924ms returns 0x184
TA260 002:531.422 JLINK_HasError()
TA260 002:531.433 JLINK_WriteReg(R0, 0x08003000)
TA260 002:531.438 - 0.004ms returns 0
TA260 002:531.442 JLINK_WriteReg(R1, 0x00000400)
TA260 002:531.445 - 0.003ms returns 0
TA260 002:531.449 JLINK_WriteReg(R2, 0x20000184)
TA260 002:531.453 - 0.003ms returns 0
TA260 002:531.457 JLINK_WriteReg(R3, 0x00000000)
TA260 002:531.460 - 0.003ms returns 0
TA260 002:531.464 JLINK_WriteReg(R4, 0x00000000)
TA260 002:531.468 - 0.003ms returns 0
TA260 002:531.472 JLINK_WriteReg(R5, 0x00000000)
TA260 002:531.475 - 0.003ms returns 0
TA260 002:531.479 JLINK_WriteReg(R6, 0x00000000)
TA260 002:531.482 - 0.003ms returns 0
TA260 002:531.486 JLINK_WriteReg(R7, 0x00000000)
TA260 002:531.490 - 0.003ms returns 0
TA260 002:531.494 JLINK_WriteReg(R8, 0x00000000)
TA260 002:531.497 - 0.003ms returns 0
TA260 002:531.501 JLINK_WriteReg(R9, 0x20000180)
TA260 002:531.504 - 0.003ms returns 0
TA260 002:531.509 JLINK_WriteReg(R10, 0x00000000)
TA260 002:531.512 - 0.003ms returns 0
TA260 002:531.516 JLINK_WriteReg(R11, 0x00000000)
TA260 002:531.520 - 0.003ms returns 0
TA260 002:531.524 JLINK_WriteReg(R12, 0x00000000)
TA260 002:531.527 - 0.003ms returns 0
TA260 002:531.531 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:531.535 - 0.003ms returns 0
TA260 002:531.539 JLINK_WriteReg(R14, 0x20000001)
TA260 002:531.542 - 0.003ms returns 0
TA260 002:531.547 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:531.550 - 0.003ms returns 0
TA260 002:531.554 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:531.558 - 0.003ms returns 0
TA260 002:531.562 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:531.565 - 0.003ms returns 0
TA260 002:531.569 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:531.572 - 0.003ms returns 0
TA260 002:531.576 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:531.580 - 0.003ms returns 0
TA260 002:531.584 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:531.588 - 0.004ms returns 0x00000018
TA260 002:531.592 JLINK_Go()
TA260 002:531.600   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:534.314 - 2.721ms 
TA260 002:534.325 JLINK_IsHalted()
TA260 002:534.806 - 0.480ms returns FALSE
TA260 002:534.822 JLINK_HasError()
TA260 002:537.467 JLINK_IsHalted()
TA260 002:538.050 - 0.583ms returns FALSE
TA260 002:538.066 JLINK_HasError()
TA260 002:542.972 JLINK_IsHalted()
TA260 002:545.431   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:545.902 - 2.929ms returns TRUE
TA260 002:545.917 JLINK_ReadReg(R15 (PC))
TA260 002:545.923 - 0.006ms returns 0x20000000
TA260 002:545.928 JLINK_ClrBPEx(BPHandle = 0x00000018)
TA260 002:545.932 - 0.004ms returns 0x00
TA260 002:545.936 JLINK_ReadReg(R0)
TA260 002:545.940 - 0.003ms returns 0x00000000
TA260 002:546.289 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:546.297   Data:  12 00 68 60 FD F7 DC F8 9F ED C7 0B 55 EC 10 4B ...
TA260 002:546.307   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:548.889 - 2.599ms returns 0x27C
TA260 002:548.903 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:548.907   Data:  04 2B 41 F8 04 2B 50 F8 04 2B 41 F8 04 2B 50 F8 ...
TA260 002:548.922   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:550.759 - 1.856ms returns 0x184
TA260 002:550.769 JLINK_HasError()
TA260 002:550.774 JLINK_WriteReg(R0, 0x08003400)
TA260 002:550.780 - 0.005ms returns 0
TA260 002:550.784 JLINK_WriteReg(R1, 0x00000400)
TA260 002:550.788 - 0.003ms returns 0
TA260 002:550.792 JLINK_WriteReg(R2, 0x20000184)
TA260 002:550.795 - 0.003ms returns 0
TA260 002:550.799 JLINK_WriteReg(R3, 0x00000000)
TA260 002:550.802 - 0.003ms returns 0
TA260 002:550.806 JLINK_WriteReg(R4, 0x00000000)
TA260 002:550.810 - 0.003ms returns 0
TA260 002:550.814 JLINK_WriteReg(R5, 0x00000000)
TA260 002:550.817 - 0.003ms returns 0
TA260 002:550.821 JLINK_WriteReg(R6, 0x00000000)
TA260 002:550.824 - 0.003ms returns 0
TA260 002:550.828 JLINK_WriteReg(R7, 0x00000000)
TA260 002:550.832 - 0.003ms returns 0
TA260 002:550.836 JLINK_WriteReg(R8, 0x00000000)
TA260 002:550.839 - 0.003ms returns 0
TA260 002:550.843 JLINK_WriteReg(R9, 0x20000180)
TA260 002:550.847 - 0.003ms returns 0
TA260 002:550.851 JLINK_WriteReg(R10, 0x00000000)
TA260 002:550.854 - 0.003ms returns 0
TA260 002:550.859 JLINK_WriteReg(R11, 0x00000000)
TA260 002:550.862 - 0.003ms returns 0
TA260 002:550.866 JLINK_WriteReg(R12, 0x00000000)
TA260 002:550.870 - 0.003ms returns 0
TA260 002:550.874 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:550.878 - 0.003ms returns 0
TA260 002:550.882 JLINK_WriteReg(R14, 0x20000001)
TA260 002:550.885 - 0.003ms returns 0
TA260 002:550.889 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:550.892 - 0.003ms returns 0
TA260 002:550.897 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:550.900 - 0.003ms returns 0
TA260 002:550.904 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:550.907 - 0.003ms returns 0
TA260 002:550.911 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:550.915 - 0.003ms returns 0
TA260 002:550.919 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:550.922 - 0.003ms returns 0
TA260 002:550.927 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:550.931 - 0.004ms returns 0x00000019
TA260 002:550.935 JLINK_Go()
TA260 002:550.943   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:553.669 - 2.733ms 
TA260 002:553.677 JLINK_IsHalted()
TA260 002:554.142 - 0.465ms returns FALSE
TA260 002:554.149 JLINK_HasError()
TA260 002:557.481 JLINK_IsHalted()
TA260 002:557.988 - 0.507ms returns FALSE
TA260 002:557.995 JLINK_HasError()
TA260 002:563.985 JLINK_IsHalted()
TA260 002:566.471   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:567.009 - 3.023ms returns TRUE
TA260 002:567.020 JLINK_ReadReg(R15 (PC))
TA260 002:567.026 - 0.006ms returns 0x20000000
TA260 002:567.030 JLINK_ClrBPEx(BPHandle = 0x00000019)
TA260 002:567.034 - 0.003ms returns 0x00
TA260 002:567.039 JLINK_ReadReg(R0)
TA260 002:567.042 - 0.003ms returns 0x00000000
TA260 002:567.399 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:567.417   Data:  81 07 DE D5 FF F7 28 FC A0 78 40 07 DB D5 BD E8 ...
TA260 002:567.429   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:570.072 - 2.672ms returns 0x27C
TA260 002:570.084 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:570.088   Data:  03 D1 BA F1 00 0F 05 D1 88 E0 BA F1 00 0F 08 BF ...
TA260 002:570.098   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:571.985 - 1.901ms returns 0x184
TA260 002:571.992 JLINK_HasError()
TA260 002:571.997 JLINK_WriteReg(R0, 0x08003800)
TA260 002:572.002 - 0.004ms returns 0
TA260 002:572.006 JLINK_WriteReg(R1, 0x00000400)
TA260 002:572.009 - 0.003ms returns 0
TA260 002:572.013 JLINK_WriteReg(R2, 0x20000184)
TA260 002:572.017 - 0.003ms returns 0
TA260 002:572.021 JLINK_WriteReg(R3, 0x00000000)
TA260 002:572.024 - 0.003ms returns 0
TA260 002:572.028 JLINK_WriteReg(R4, 0x00000000)
TA260 002:572.031 - 0.003ms returns 0
TA260 002:572.035 JLINK_WriteReg(R5, 0x00000000)
TA260 002:572.039 - 0.003ms returns 0
TA260 002:572.043 JLINK_WriteReg(R6, 0x00000000)
TA260 002:572.046 - 0.003ms returns 0
TA260 002:572.050 JLINK_WriteReg(R7, 0x00000000)
TA260 002:572.054 - 0.003ms returns 0
TA260 002:572.062 JLINK_WriteReg(R8, 0x00000000)
TA260 002:572.067 - 0.005ms returns 0
TA260 002:572.071 JLINK_WriteReg(R9, 0x20000180)
TA260 002:572.074 - 0.003ms returns 0
TA260 002:572.078 JLINK_WriteReg(R10, 0x00000000)
TA260 002:572.082 - 0.003ms returns 0
TA260 002:572.086 JLINK_WriteReg(R11, 0x00000000)
TA260 002:572.089 - 0.003ms returns 0
TA260 002:572.093 JLINK_WriteReg(R12, 0x00000000)
TA260 002:572.097 - 0.003ms returns 0
TA260 002:572.101 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:572.105 - 0.003ms returns 0
TA260 002:572.109 JLINK_WriteReg(R14, 0x20000001)
TA260 002:572.112 - 0.003ms returns 0
TA260 002:572.116 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:572.120 - 0.003ms returns 0
TA260 002:572.124 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:572.127 - 0.003ms returns 0
TA260 002:572.132 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:572.135 - 0.003ms returns 0
TA260 002:572.139 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:572.142 - 0.003ms returns 0
TA260 002:572.146 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:572.150 - 0.003ms returns 0
TA260 002:572.154 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:572.158 - 0.004ms returns 0x0000001A
TA260 002:572.162 JLINK_Go()
TA260 002:572.170   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:574.898 - 2.735ms 
TA260 002:574.912 JLINK_IsHalted()
TA260 002:575.410 - 0.497ms returns FALSE
TA260 002:575.423 JLINK_HasError()
TA260 002:579.002 JLINK_IsHalted()
TA260 002:581.464   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:581.940 - 2.937ms returns TRUE
TA260 002:581.947 JLINK_ReadReg(R15 (PC))
TA260 002:581.952 - 0.005ms returns 0x20000000
TA260 002:581.956 JLINK_ClrBPEx(BPHandle = 0x0000001A)
TA260 002:581.960 - 0.003ms returns 0x00
TA260 002:581.965 JLINK_ReadReg(R0)
TA260 002:581.968 - 0.003ms returns 0x00000000
TA260 002:582.318 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:582.326   Data:  AA E7 8D ED 02 9B 9D ED 22 0B 19 EE 90 1A 10 EE ...
TA260 002:582.337   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:584.935 - 2.616ms returns 0x27C
TA260 002:584.949 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:584.954   Data:  00 00 00 80 00 00 00 00 00 00 F0 FF 00 00 00 00 ...
TA260 002:584.963   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:586.994 - 2.044ms returns 0x184
TA260 002:587.018 JLINK_HasError()
TA260 002:587.055 JLINK_WriteReg(R0, 0x08003C00)
TA260 002:587.071 - 0.016ms returns 0
TA260 002:587.076 JLINK_WriteReg(R1, 0x00000400)
TA260 002:587.079 - 0.003ms returns 0
TA260 002:587.084 JLINK_WriteReg(R2, 0x20000184)
TA260 002:587.088 - 0.003ms returns 0
TA260 002:587.092 JLINK_WriteReg(R3, 0x00000000)
TA260 002:587.095 - 0.003ms returns 0
TA260 002:587.100 JLINK_WriteReg(R4, 0x00000000)
TA260 002:587.103 - 0.003ms returns 0
TA260 002:587.107 JLINK_WriteReg(R5, 0x00000000)
TA260 002:587.110 - 0.003ms returns 0
TA260 002:587.114 JLINK_WriteReg(R6, 0x00000000)
TA260 002:587.118 - 0.003ms returns 0
TA260 002:587.122 JLINK_WriteReg(R7, 0x00000000)
TA260 002:587.125 - 0.003ms returns 0
TA260 002:587.129 JLINK_WriteReg(R8, 0x00000000)
TA260 002:587.132 - 0.003ms returns 0
TA260 002:587.137 JLINK_WriteReg(R9, 0x20000180)
TA260 002:587.140 - 0.003ms returns 0
TA260 002:587.144 JLINK_WriteReg(R10, 0x00000000)
TA260 002:587.148 - 0.003ms returns 0
TA260 002:587.152 JLINK_WriteReg(R11, 0x00000000)
TA260 002:587.155 - 0.003ms returns 0
TA260 002:587.159 JLINK_WriteReg(R12, 0x00000000)
TA260 002:587.162 - 0.003ms returns 0
TA260 002:587.166 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:587.170 - 0.003ms returns 0
TA260 002:587.174 JLINK_WriteReg(R14, 0x20000001)
TA260 002:587.178 - 0.003ms returns 0
TA260 002:587.182 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:587.186 - 0.003ms returns 0
TA260 002:587.190 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:587.193 - 0.003ms returns 0
TA260 002:587.197 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:587.200 - 0.003ms returns 0
TA260 002:587.204 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:587.208 - 0.003ms returns 0
TA260 002:587.212 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:587.221 - 0.008ms returns 0
TA260 002:587.225 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:587.230 - 0.004ms returns 0x0000001B
TA260 002:587.235 JLINK_Go()
TA260 002:587.244   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:590.019 - 2.784ms 
TA260 002:590.031 JLINK_IsHalted()
TA260 002:590.530 - 0.499ms returns FALSE
TA260 002:590.536 JLINK_HasError()
TA260 002:595.510 JLINK_IsHalted()
TA260 002:597.879   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:598.415 - 2.905ms returns TRUE
TA260 002:598.423 JLINK_ReadReg(R15 (PC))
TA260 002:598.428 - 0.005ms returns 0x20000000
TA260 002:598.433 JLINK_ClrBPEx(BPHandle = 0x0000001B)
TA260 002:598.437 - 0.003ms returns 0x00
TA260 002:598.442 JLINK_ReadReg(R0)
TA260 002:598.445 - 0.003ms returns 0x00000000
TA260 002:598.789 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:598.796   Data:  1C 2B FC F7 EB F9 41 EC 19 0B C0 48 8D ED 0E 9B ...
TA260 002:598.807   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:601.482 - 2.692ms returns 0x27C
TA260 002:601.494 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:601.498   Data:  53 EC 10 2B 9D ED 04 0B 51 EC 10 0B FC F7 A2 F8 ...
TA260 002:601.508   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:603.412 - 1.918ms returns 0x184
TA260 002:603.427 JLINK_HasError()
TA260 002:603.432 JLINK_WriteReg(R0, 0x08004000)
TA260 002:603.437 - 0.005ms returns 0
TA260 002:603.441 JLINK_WriteReg(R1, 0x00000400)
TA260 002:603.445 - 0.003ms returns 0
TA260 002:603.449 JLINK_WriteReg(R2, 0x20000184)
TA260 002:603.452 - 0.003ms returns 0
TA260 002:603.456 JLINK_WriteReg(R3, 0x00000000)
TA260 002:603.460 - 0.003ms returns 0
TA260 002:603.464 JLINK_WriteReg(R4, 0x00000000)
TA260 002:603.468 - 0.003ms returns 0
TA260 002:603.472 JLINK_WriteReg(R5, 0x00000000)
TA260 002:603.475 - 0.003ms returns 0
TA260 002:603.479 JLINK_WriteReg(R6, 0x00000000)
TA260 002:603.483 - 0.003ms returns 0
TA260 002:603.487 JLINK_WriteReg(R7, 0x00000000)
TA260 002:603.490 - 0.003ms returns 0
TA260 002:603.494 JLINK_WriteReg(R8, 0x00000000)
TA260 002:603.498 - 0.003ms returns 0
TA260 002:603.502 JLINK_WriteReg(R9, 0x20000180)
TA260 002:603.505 - 0.003ms returns 0
TA260 002:603.509 JLINK_WriteReg(R10, 0x00000000)
TA260 002:603.512 - 0.003ms returns 0
TA260 002:603.516 JLINK_WriteReg(R11, 0x00000000)
TA260 002:603.520 - 0.003ms returns 0
TA260 002:603.524 JLINK_WriteReg(R12, 0x00000000)
TA260 002:603.527 - 0.003ms returns 0
TA260 002:603.531 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:603.535 - 0.003ms returns 0
TA260 002:603.539 JLINK_WriteReg(R14, 0x20000001)
TA260 002:603.542 - 0.003ms returns 0
TA260 002:603.546 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:603.550 - 0.003ms returns 0
TA260 002:603.554 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:603.557 - 0.003ms returns 0
TA260 002:603.561 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:603.565 - 0.003ms returns 0
TA260 002:603.569 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:603.572 - 0.003ms returns 0
TA260 002:603.576 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:603.580 - 0.003ms returns 0
TA260 002:603.584 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:603.588 - 0.004ms returns 0x0000001C
TA260 002:603.592 JLINK_Go()
TA260 002:603.601   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:606.417 - 2.819ms 
TA260 002:606.434 JLINK_IsHalted()
TA260 002:606.938 - 0.503ms returns FALSE
TA260 002:606.951 JLINK_HasError()
TA260 002:611.520 JLINK_IsHalted()
TA260 002:613.902   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:614.407 - 2.887ms returns TRUE
TA260 002:614.418 JLINK_ReadReg(R15 (PC))
TA260 002:614.424 - 0.006ms returns 0x20000000
TA260 002:614.455 JLINK_ClrBPEx(BPHandle = 0x0000001C)
TA260 002:614.460 - 0.005ms returns 0x00
TA260 002:614.465 JLINK_ReadReg(R0)
TA260 002:614.468 - 0.003ms returns 0x00000000
TA260 002:614.894 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:614.904   Data:  9D ED 04 0B 53 EC 10 2B 9D ED 02 0B 51 EC 10 0B ...
TA260 002:614.916   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:617.486 - 2.591ms returns 0x27C
TA260 002:617.504 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:617.508   Data:  00 00 00 40 10 B5 2D ED 02 8B B0 EE 40 8A 18 EE ...
TA260 002:617.520   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:619.402 - 1.897ms returns 0x184
TA260 002:619.408 JLINK_HasError()
TA260 002:619.414 JLINK_WriteReg(R0, 0x08004400)
TA260 002:619.418 - 0.004ms returns 0
TA260 002:619.423 JLINK_WriteReg(R1, 0x00000400)
TA260 002:619.426 - 0.003ms returns 0
TA260 002:619.430 JLINK_WriteReg(R2, 0x20000184)
TA260 002:619.434 - 0.003ms returns 0
TA260 002:619.438 JLINK_WriteReg(R3, 0x00000000)
TA260 002:619.441 - 0.003ms returns 0
TA260 002:619.445 JLINK_WriteReg(R4, 0x00000000)
TA260 002:619.448 - 0.003ms returns 0
TA260 002:619.452 JLINK_WriteReg(R5, 0x00000000)
TA260 002:619.456 - 0.003ms returns 0
TA260 002:619.460 JLINK_WriteReg(R6, 0x00000000)
TA260 002:619.463 - 0.003ms returns 0
TA260 002:619.467 JLINK_WriteReg(R7, 0x00000000)
TA260 002:619.471 - 0.003ms returns 0
TA260 002:619.475 JLINK_WriteReg(R8, 0x00000000)
TA260 002:619.479 - 0.004ms returns 0
TA260 002:619.483 JLINK_WriteReg(R9, 0x20000180)
TA260 002:619.486 - 0.003ms returns 0
TA260 002:619.490 JLINK_WriteReg(R10, 0x00000000)
TA260 002:619.494 - 0.003ms returns 0
TA260 002:619.498 JLINK_WriteReg(R11, 0x00000000)
TA260 002:619.501 - 0.003ms returns 0
TA260 002:619.505 JLINK_WriteReg(R12, 0x00000000)
TA260 002:619.508 - 0.003ms returns 0
TA260 002:619.512 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:619.516 - 0.004ms returns 0
TA260 002:619.520 JLINK_WriteReg(R14, 0x20000001)
TA260 002:619.524 - 0.003ms returns 0
TA260 002:619.528 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:619.531 - 0.003ms returns 0
TA260 002:619.535 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:619.539 - 0.003ms returns 0
TA260 002:619.543 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:619.546 - 0.003ms returns 0
TA260 002:619.550 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:619.554 - 0.003ms returns 0
TA260 002:619.558 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:619.561 - 0.003ms returns 0
TA260 002:619.566 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:619.570 - 0.004ms returns 0x0000001D
TA260 002:619.574 JLINK_Go()
TA260 002:619.582   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:622.411 - 2.836ms 
TA260 002:622.420 JLINK_IsHalted()
TA260 002:622.905 - 0.485ms returns FALSE
TA260 002:622.911 JLINK_HasError()
TA260 002:627.035 JLINK_IsHalted()
TA260 002:629.430   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:629.935 - 2.899ms returns TRUE
TA260 002:629.948 JLINK_ReadReg(R15 (PC))
TA260 002:629.954 - 0.006ms returns 0x20000000
TA260 002:629.959 JLINK_ClrBPEx(BPHandle = 0x0000001D)
TA260 002:629.963 - 0.004ms returns 0x00
TA260 002:629.967 JLINK_ReadReg(R0)
TA260 002:629.971 - 0.003ms returns 0x00000000
TA260 002:630.277 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:630.284   Data:  53 EC 11 2B FB F7 43 FD BD EC 02 8B 41 EC 10 0B ...
TA260 002:630.294   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:632.886 - 2.608ms returns 0x27C
TA260 002:632.896 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:632.900   Data:  00 25 0F E2 25 28 77 D1 00 24 27 46 F8 4A 01 21 ...
TA260 002:632.910   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:634.762 - 1.865ms returns 0x184
TA260 002:634.776 JLINK_HasError()
TA260 002:634.781 JLINK_WriteReg(R0, 0x08004800)
TA260 002:634.786 - 0.005ms returns 0
TA260 002:634.791 JLINK_WriteReg(R1, 0x00000400)
TA260 002:634.795 - 0.003ms returns 0
TA260 002:634.799 JLINK_WriteReg(R2, 0x20000184)
TA260 002:634.802 - 0.003ms returns 0
TA260 002:634.807 JLINK_WriteReg(R3, 0x00000000)
TA260 002:634.810 - 0.003ms returns 0
TA260 002:634.814 JLINK_WriteReg(R4, 0x00000000)
TA260 002:634.818 - 0.003ms returns 0
TA260 002:634.822 JLINK_WriteReg(R5, 0x00000000)
TA260 002:634.825 - 0.003ms returns 0
TA260 002:634.829 JLINK_WriteReg(R6, 0x00000000)
TA260 002:634.833 - 0.003ms returns 0
TA260 002:634.837 JLINK_WriteReg(R7, 0x00000000)
TA260 002:634.844 - 0.007ms returns 0
TA260 002:634.849 JLINK_WriteReg(R8, 0x00000000)
TA260 002:634.854 - 0.004ms returns 0
TA260 002:634.859 JLINK_WriteReg(R9, 0x20000180)
TA260 002:634.863 - 0.003ms returns 0
TA260 002:634.867 JLINK_WriteReg(R10, 0x00000000)
TA260 002:634.882 - 0.015ms returns 0
TA260 002:634.887 JLINK_WriteReg(R11, 0x00000000)
TA260 002:634.890 - 0.003ms returns 0
TA260 002:634.895 JLINK_WriteReg(R12, 0x00000000)
TA260 002:634.898 - 0.003ms returns 0
TA260 002:634.902 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:634.906 - 0.003ms returns 0
TA260 002:634.910 JLINK_WriteReg(R14, 0x20000001)
TA260 002:634.913 - 0.003ms returns 0
TA260 002:634.917 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:634.921 - 0.003ms returns 0
TA260 002:634.925 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:634.929 - 0.003ms returns 0
TA260 002:634.933 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:634.936 - 0.003ms returns 0
TA260 002:634.940 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:634.944 - 0.003ms returns 0
TA260 002:634.948 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:634.951 - 0.003ms returns 0
TA260 002:634.956 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:634.960 - 0.004ms returns 0x0000001E
TA260 002:634.964 JLINK_Go()
TA260 002:634.973   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:637.760 - 2.794ms 
TA260 002:637.780 JLINK_IsHalted()
TA260 002:638.409 - 0.629ms returns FALSE
TA260 002:638.424 JLINK_HasError()
TA260 002:644.730 JLINK_IsHalted()
TA260 002:647.074   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:647.579 - 2.849ms returns TRUE
TA260 002:647.587 JLINK_ReadReg(R15 (PC))
TA260 002:647.592 - 0.005ms returns 0x20000000
TA260 002:647.597 JLINK_ClrBPEx(BPHandle = 0x0000001E)
TA260 002:647.600 - 0.003ms returns 0x00
TA260 002:647.605 JLINK_ReadReg(R0)
TA260 002:647.609 - 0.003ms returns 0x00000000
TA260 002:647.962 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:647.970   Data:  FF 30 61 07 4F F0 00 01 02 D4 0D E0 08 F1 01 01 ...
TA260 002:647.980   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:650.566 - 2.604ms returns 0x27C
TA260 002:650.575 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:650.579   Data:  63 64 65 66 00 00 00 00 30 31 32 33 34 35 36 37 ...
TA260 002:650.587   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:652.427 - 1.852ms returns 0x184
TA260 002:652.433 JLINK_HasError()
TA260 002:652.438 JLINK_WriteReg(R0, 0x08004C00)
TA260 002:652.442 - 0.004ms returns 0
TA260 002:652.446 JLINK_WriteReg(R1, 0x00000400)
TA260 002:652.450 - 0.003ms returns 0
TA260 002:652.454 JLINK_WriteReg(R2, 0x20000184)
TA260 002:652.458 - 0.003ms returns 0
TA260 002:652.462 JLINK_WriteReg(R3, 0x00000000)
TA260 002:652.465 - 0.003ms returns 0
TA260 002:652.469 JLINK_WriteReg(R4, 0x00000000)
TA260 002:652.472 - 0.003ms returns 0
TA260 002:652.477 JLINK_WriteReg(R5, 0x00000000)
TA260 002:652.480 - 0.003ms returns 0
TA260 002:652.484 JLINK_WriteReg(R6, 0x00000000)
TA260 002:652.487 - 0.003ms returns 0
TA260 002:652.491 JLINK_WriteReg(R7, 0x00000000)
TA260 002:652.495 - 0.003ms returns 0
TA260 002:652.499 JLINK_WriteReg(R8, 0x00000000)
TA260 002:652.502 - 0.003ms returns 0
TA260 002:652.506 JLINK_WriteReg(R9, 0x20000180)
TA260 002:652.510 - 0.003ms returns 0
TA260 002:652.514 JLINK_WriteReg(R10, 0x00000000)
TA260 002:652.517 - 0.003ms returns 0
TA260 002:652.521 JLINK_WriteReg(R11, 0x00000000)
TA260 002:652.525 - 0.003ms returns 0
TA260 002:652.529 JLINK_WriteReg(R12, 0x00000000)
TA260 002:652.532 - 0.003ms returns 0
TA260 002:652.536 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:652.540 - 0.003ms returns 0
TA260 002:652.544 JLINK_WriteReg(R14, 0x20000001)
TA260 002:652.548 - 0.003ms returns 0
TA260 002:652.552 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:652.555 - 0.003ms returns 0
TA260 002:652.559 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:652.562 - 0.003ms returns 0
TA260 002:652.566 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:652.570 - 0.003ms returns 0
TA260 002:652.574 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:652.580 - 0.006ms returns 0
TA260 002:652.585 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:652.589 - 0.003ms returns 0
TA260 002:652.593 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:652.597 - 0.004ms returns 0x0000001F
TA260 002:652.602 JLINK_Go()
TA260 002:652.609   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:655.409 - 2.806ms 
TA260 002:655.425 JLINK_IsHalted()
TA260 002:655.957 - 0.531ms returns FALSE
TA260 002:655.971 JLINK_HasError()
TA260 002:659.239 JLINK_IsHalted()
TA260 002:661.603   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:662.136 - 2.896ms returns TRUE
TA260 002:662.146 JLINK_ReadReg(R15 (PC))
TA260 002:662.152 - 0.005ms returns 0x20000000
TA260 002:662.156 JLINK_ClrBPEx(BPHandle = 0x0000001F)
TA260 002:662.160 - 0.003ms returns 0x00
TA260 002:662.164 JLINK_ReadReg(R0)
TA260 002:662.168 - 0.003ms returns 0x00000000
TA260 002:662.476 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:662.483   Data:  41 1C 51 45 00 DD 8A 46 04 99 40 1A 40 1C 01 90 ...
TA260 002:662.494   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:665.084 - 2.607ms returns 0x27C
TA260 002:665.100 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:665.104   Data:  B6 6D DB 3F 4D 26 8F 51 55 55 D5 3F 01 41 1D A9 ...
TA260 002:665.113   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:667.025 - 1.925ms returns 0x184
TA260 002:667.048 JLINK_HasError()
TA260 002:667.088 JLINK_WriteReg(R0, 0x08005000)
TA260 002:667.103 - 0.015ms returns 0
TA260 002:667.108 JLINK_WriteReg(R1, 0x00000400)
TA260 002:667.112 - 0.004ms returns 0
TA260 002:667.116 JLINK_WriteReg(R2, 0x20000184)
TA260 002:667.119 - 0.003ms returns 0
TA260 002:667.123 JLINK_WriteReg(R3, 0x00000000)
TA260 002:667.127 - 0.003ms returns 0
TA260 002:667.131 JLINK_WriteReg(R4, 0x00000000)
TA260 002:667.134 - 0.003ms returns 0
TA260 002:667.138 JLINK_WriteReg(R5, 0x00000000)
TA260 002:667.142 - 0.003ms returns 0
TA260 002:667.146 JLINK_WriteReg(R6, 0x00000000)
TA260 002:667.149 - 0.003ms returns 0
TA260 002:667.153 JLINK_WriteReg(R7, 0x00000000)
TA260 002:667.156 - 0.003ms returns 0
TA260 002:667.160 JLINK_WriteReg(R8, 0x00000000)
TA260 002:667.164 - 0.003ms returns 0
TA260 002:667.168 JLINK_WriteReg(R9, 0x20000180)
TA260 002:667.171 - 0.003ms returns 0
TA260 002:667.175 JLINK_WriteReg(R10, 0x00000000)
TA260 002:667.178 - 0.003ms returns 0
TA260 002:667.182 JLINK_WriteReg(R11, 0x00000000)
TA260 002:667.186 - 0.003ms returns 0
TA260 002:667.190 JLINK_WriteReg(R12, 0x00000000)
TA260 002:667.193 - 0.003ms returns 0
TA260 002:667.197 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:667.201 - 0.004ms returns 0
TA260 002:667.206 JLINK_WriteReg(R14, 0x20000001)
TA260 002:667.209 - 0.003ms returns 0
TA260 002:667.234 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:667.238 - 0.003ms returns 0
TA260 002:667.242 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:667.245 - 0.003ms returns 0
TA260 002:667.250 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:667.253 - 0.003ms returns 0
TA260 002:667.257 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:667.260 - 0.003ms returns 0
TA260 002:667.264 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:667.268 - 0.003ms returns 0
TA260 002:667.273 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:667.277 - 0.005ms returns 0x00000020
TA260 002:667.282 JLINK_Go()
TA260 002:667.292   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:669.989 - 2.707ms 
TA260 002:669.996 JLINK_IsHalted()
TA260 002:670.473 - 0.476ms returns FALSE
TA260 002:670.479 JLINK_HasError()
TA260 002:674.247 JLINK_IsHalted()
TA260 002:676.610   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:677.087 - 2.838ms returns TRUE
TA260 002:677.103 JLINK_ReadReg(R15 (PC))
TA260 002:677.109 - 0.006ms returns 0x20000000
TA260 002:677.114 JLINK_ClrBPEx(BPHandle = 0x00000020)
TA260 002:677.118 - 0.004ms returns 0x00
TA260 002:677.122 JLINK_ReadReg(R0)
TA260 002:677.126 - 0.003ms returns 0x00000000
TA260 002:677.514 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:677.522   Data:  08 00 08 04 08 02 08 06 08 01 08 05 08 03 08 07 ...
TA260 002:677.538   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:680.068 - 2.553ms returns 0x27C
TA260 002:680.080 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:680.084   Data:  EC 03 EC 07 1C 00 1C 04 1C 02 1C 06 1C 01 1C 05 ...
TA260 002:680.093   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:681.973 - 1.893ms returns 0x184
TA260 002:681.979 JLINK_HasError()
TA260 002:681.985 JLINK_WriteReg(R0, 0x08005400)
TA260 002:681.989 - 0.004ms returns 0
TA260 002:681.994 JLINK_WriteReg(R1, 0x00000400)
TA260 002:681.997 - 0.003ms returns 0
TA260 002:682.001 JLINK_WriteReg(R2, 0x20000184)
TA260 002:682.005 - 0.003ms returns 0
TA260 002:682.009 JLINK_WriteReg(R3, 0x00000000)
TA260 002:682.012 - 0.003ms returns 0
TA260 002:682.016 JLINK_WriteReg(R4, 0x00000000)
TA260 002:682.020 - 0.003ms returns 0
TA260 002:682.024 JLINK_WriteReg(R5, 0x00000000)
TA260 002:682.027 - 0.003ms returns 0
TA260 002:682.031 JLINK_WriteReg(R6, 0x00000000)
TA260 002:682.034 - 0.003ms returns 0
TA260 002:682.039 JLINK_WriteReg(R7, 0x00000000)
TA260 002:682.042 - 0.003ms returns 0
TA260 002:682.046 JLINK_WriteReg(R8, 0x00000000)
TA260 002:682.050 - 0.003ms returns 0
TA260 002:682.054 JLINK_WriteReg(R9, 0x20000180)
TA260 002:682.057 - 0.003ms returns 0
TA260 002:682.061 JLINK_WriteReg(R10, 0x00000000)
TA260 002:682.065 - 0.003ms returns 0
TA260 002:682.069 JLINK_WriteReg(R11, 0x00000000)
TA260 002:682.072 - 0.003ms returns 0
TA260 002:682.076 JLINK_WriteReg(R12, 0x00000000)
TA260 002:682.079 - 0.003ms returns 0
TA260 002:682.084 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:682.087 - 0.003ms returns 0
TA260 002:682.091 JLINK_WriteReg(R14, 0x20000001)
TA260 002:682.095 - 0.003ms returns 0
TA260 002:682.099 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:682.102 - 0.003ms returns 0
TA260 002:682.106 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:682.110 - 0.003ms returns 0
TA260 002:682.114 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:682.117 - 0.003ms returns 0
TA260 002:682.121 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:682.124 - 0.003ms returns 0
TA260 002:682.128 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:682.132 - 0.003ms returns 0
TA260 002:682.136 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:682.140 - 0.004ms returns 0x00000021
TA260 002:682.145 JLINK_Go()
TA260 002:682.152   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:684.817 - 2.671ms 
TA260 002:684.825 JLINK_IsHalted()
TA260 002:685.421 - 0.595ms returns FALSE
TA260 002:685.435 JLINK_HasError()
TA260 002:688.760 JLINK_IsHalted()
TA260 002:691.101   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:691.579 - 2.818ms returns TRUE
TA260 002:691.585 JLINK_ReadReg(R15 (PC))
TA260 002:691.590 - 0.004ms returns 0x20000000
TA260 002:691.595 JLINK_ClrBPEx(BPHandle = 0x00000021)
TA260 002:691.599 - 0.003ms returns 0x00
TA260 002:691.604 JLINK_ReadReg(R0)
TA260 002:691.607 - 0.003ms returns 0x00000000
TA260 002:691.920 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:691.927   Data:  0A 00 0A 04 0A 02 0A 06 0A 01 0A 05 0A 03 0A 07 ...
TA260 002:691.938   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:694.477 - 2.556ms returns 0x27C
TA260 002:694.493 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:694.497   Data:  EE 03 EE 07 1E 00 1E 04 1E 02 1E 06 1E 01 1E 05 ...
TA260 002:694.507   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:696.426 - 1.932ms returns 0x184
TA260 002:696.447 JLINK_HasError()
TA260 002:696.486 JLINK_WriteReg(R0, 0x08005800)
TA260 002:696.507 - 0.020ms returns 0
TA260 002:696.512 JLINK_WriteReg(R1, 0x00000400)
TA260 002:696.516 - 0.003ms returns 0
TA260 002:696.520 JLINK_WriteReg(R2, 0x20000184)
TA260 002:696.523 - 0.003ms returns 0
TA260 002:696.528 JLINK_WriteReg(R3, 0x00000000)
TA260 002:696.531 - 0.003ms returns 0
TA260 002:696.535 JLINK_WriteReg(R4, 0x00000000)
TA260 002:696.538 - 0.003ms returns 0
TA260 002:696.543 JLINK_WriteReg(R5, 0x00000000)
TA260 002:696.546 - 0.003ms returns 0
TA260 002:696.550 JLINK_WriteReg(R6, 0x00000000)
TA260 002:696.553 - 0.003ms returns 0
TA260 002:696.563 JLINK_WriteReg(R7, 0x00000000)
TA260 002:696.567 - 0.004ms returns 0
TA260 002:696.572 JLINK_WriteReg(R8, 0x00000000)
TA260 002:696.575 - 0.003ms returns 0
TA260 002:696.579 JLINK_WriteReg(R9, 0x20000180)
TA260 002:696.582 - 0.003ms returns 0
TA260 002:696.587 JLINK_WriteReg(R10, 0x00000000)
TA260 002:696.590 - 0.003ms returns 0
TA260 002:696.594 JLINK_WriteReg(R11, 0x00000000)
TA260 002:696.598 - 0.003ms returns 0
TA260 002:696.602 JLINK_WriteReg(R12, 0x00000000)
TA260 002:696.605 - 0.003ms returns 0
TA260 002:696.609 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:696.614 - 0.004ms returns 0
TA260 002:696.618 JLINK_WriteReg(R14, 0x20000001)
TA260 002:696.621 - 0.003ms returns 0
TA260 002:696.626 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:696.629 - 0.003ms returns 0
TA260 002:696.633 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:696.636 - 0.003ms returns 0
TA260 002:696.641 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:696.644 - 0.003ms returns 0
TA260 002:696.648 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:696.652 - 0.003ms returns 0
TA260 002:696.656 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:696.659 - 0.003ms returns 0
TA260 002:696.664 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:696.673 - 0.009ms returns 0x00000022
TA260 002:696.677 JLINK_Go()
TA260 002:696.688   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:699.508 - 2.831ms 
TA260 002:699.522 JLINK_IsHalted()
TA260 002:699.999 - 0.476ms returns FALSE
TA260 002:700.008 JLINK_HasError()
TA260 002:702.768 JLINK_IsHalted()
TA260 002:703.281 - 0.512ms returns FALSE
TA260 002:703.287 JLINK_HasError()
TA260 002:704.767 JLINK_IsHalted()
TA260 002:707.017   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:707.538 - 2.770ms returns TRUE
TA260 002:707.554 JLINK_ReadReg(R15 (PC))
TA260 002:707.559 - 0.005ms returns 0x20000000
TA260 002:707.564 JLINK_ClrBPEx(BPHandle = 0x00000022)
TA260 002:707.568 - 0.003ms returns 0x00
TA260 002:707.572 JLINK_ReadReg(R0)
TA260 002:707.576 - 0.003ms returns 0x00000000
TA260 002:707.931 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:707.938   Data:  C9 C8 32 3F F3 04 35 3F 23 3A 37 3F 42 68 39 3F ...
TA260 002:707.948   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:710.569 - 2.638ms returns 0x27C
TA260 002:710.581 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:710.584   Data:  CA 7B CF 3E 53 B9 C9 3E 15 EF C3 3E 49 1D BE 3E ...
TA260 002:710.594   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:712.486 - 1.905ms returns 0x184
TA260 002:712.492 JLINK_HasError()
TA260 002:712.497 JLINK_WriteReg(R0, 0x08005C00)
TA260 002:712.501 - 0.004ms returns 0
TA260 002:712.505 JLINK_WriteReg(R1, 0x00000400)
TA260 002:712.509 - 0.003ms returns 0
TA260 002:712.513 JLINK_WriteReg(R2, 0x20000184)
TA260 002:712.516 - 0.003ms returns 0
TA260 002:712.520 JLINK_WriteReg(R3, 0x00000000)
TA260 002:712.524 - 0.003ms returns 0
TA260 002:712.527 JLINK_WriteReg(R4, 0x00000000)
TA260 002:712.531 - 0.003ms returns 0
TA260 002:712.535 JLINK_WriteReg(R5, 0x00000000)
TA260 002:712.538 - 0.003ms returns 0
TA260 002:712.542 JLINK_WriteReg(R6, 0x00000000)
TA260 002:712.546 - 0.003ms returns 0
TA260 002:712.550 JLINK_WriteReg(R7, 0x00000000)
TA260 002:712.554 - 0.003ms returns 0
TA260 002:712.558 JLINK_WriteReg(R8, 0x00000000)
TA260 002:712.561 - 0.003ms returns 0
TA260 002:712.565 JLINK_WriteReg(R9, 0x20000180)
TA260 002:712.568 - 0.003ms returns 0
TA260 002:712.572 JLINK_WriteReg(R10, 0x00000000)
TA260 002:712.576 - 0.003ms returns 0
TA260 002:712.580 JLINK_WriteReg(R11, 0x00000000)
TA260 002:712.583 - 0.003ms returns 0
TA260 002:712.587 JLINK_WriteReg(R12, 0x00000000)
TA260 002:712.591 - 0.003ms returns 0
TA260 002:712.595 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:712.598 - 0.003ms returns 0
TA260 002:712.602 JLINK_WriteReg(R14, 0x20000001)
TA260 002:712.606 - 0.003ms returns 0
TA260 002:712.610 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:712.613 - 0.003ms returns 0
TA260 002:712.617 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:712.621 - 0.003ms returns 0
TA260 002:712.629 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:712.632 - 0.003ms returns 0
TA260 002:712.636 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:712.640 - 0.003ms returns 0
TA260 002:712.644 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:712.647 - 0.003ms returns 0
TA260 002:712.652 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:712.656 - 0.004ms returns 0x00000023
TA260 002:712.660 JLINK_Go()
TA260 002:712.667   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:715.390 - 2.729ms 
TA260 002:715.403 JLINK_IsHalted()
TA260 002:715.898 - 0.494ms returns FALSE
TA260 002:715.911 JLINK_HasError()
TA260 002:717.277 JLINK_IsHalted()
TA260 002:717.769 - 0.492ms returns FALSE
TA260 002:717.775 JLINK_HasError()
TA260 002:720.782 JLINK_IsHalted()
TA260 002:723.148   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:723.623 - 2.841ms returns TRUE
TA260 002:723.634 JLINK_ReadReg(R15 (PC))
TA260 002:723.641 - 0.006ms returns 0x20000000
TA260 002:723.676 JLINK_ClrBPEx(BPHandle = 0x00000023)
TA260 002:723.683 - 0.006ms returns 0x00
TA260 002:723.688 JLINK_ReadReg(R0)
TA260 002:723.693 - 0.004ms returns 0x00000000
TA260 002:724.014 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:724.022   Data:  C9 C8 32 BF F3 04 35 BF 23 3A 37 BF 42 68 39 BF ...
TA260 002:724.033   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:726.674 - 2.659ms returns 0x27C
TA260 002:726.695 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:726.700   Data:  CA 7B CF BE 53 B9 C9 BE 15 EF C3 BE 49 1D BE BE ...
TA260 002:726.713   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:728.571 - 1.875ms returns 0x184
TA260 002:728.586 JLINK_HasError()
TA260 002:728.592 JLINK_WriteReg(R0, 0x08006000)
TA260 002:728.598 - 0.005ms returns 0
TA260 002:728.602 JLINK_WriteReg(R1, 0x00000400)
TA260 002:728.605 - 0.003ms returns 0
TA260 002:728.609 JLINK_WriteReg(R2, 0x20000184)
TA260 002:728.613 - 0.003ms returns 0
TA260 002:728.617 JLINK_WriteReg(R3, 0x00000000)
TA260 002:728.620 - 0.003ms returns 0
TA260 002:728.624 JLINK_WriteReg(R4, 0x00000000)
TA260 002:728.627 - 0.003ms returns 0
TA260 002:728.631 JLINK_WriteReg(R5, 0x00000000)
TA260 002:728.635 - 0.003ms returns 0
TA260 002:728.639 JLINK_WriteReg(R6, 0x00000000)
TA260 002:728.642 - 0.003ms returns 0
TA260 002:728.646 JLINK_WriteReg(R7, 0x00000000)
TA260 002:728.650 - 0.003ms returns 0
TA260 002:728.654 JLINK_WriteReg(R8, 0x00000000)
TA260 002:728.657 - 0.003ms returns 0
TA260 002:728.661 JLINK_WriteReg(R9, 0x20000180)
TA260 002:728.664 - 0.003ms returns 0
TA260 002:728.669 JLINK_WriteReg(R10, 0x00000000)
TA260 002:728.672 - 0.003ms returns 0
TA260 002:728.676 JLINK_WriteReg(R11, 0x00000000)
TA260 002:728.679 - 0.003ms returns 0
TA260 002:728.683 JLINK_WriteReg(R12, 0x00000000)
TA260 002:728.687 - 0.003ms returns 0
TA260 002:728.691 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:728.695 - 0.004ms returns 0
TA260 002:728.699 JLINK_WriteReg(R14, 0x20000001)
TA260 002:728.702 - 0.003ms returns 0
TA260 002:728.706 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:728.710 - 0.003ms returns 0
TA260 002:728.714 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:728.717 - 0.003ms returns 0
TA260 002:728.721 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:728.725 - 0.003ms returns 0
TA260 002:728.729 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:728.732 - 0.003ms returns 0
TA260 002:728.736 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:728.739 - 0.003ms returns 0
TA260 002:728.744 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:728.748 - 0.004ms returns 0x00000024
TA260 002:728.753 JLINK_Go()
TA260 002:728.761   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:731.486 - 2.733ms 
TA260 002:731.493 JLINK_IsHalted()
TA260 002:732.007 - 0.513ms returns FALSE
TA260 002:732.012 JLINK_HasError()
TA260 002:733.285 JLINK_IsHalted()
TA260 002:733.768 - 0.483ms returns FALSE
TA260 002:733.774 JLINK_HasError()
TA260 002:735.299 JLINK_IsHalted()
TA260 002:737.656   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:738.158 - 2.859ms returns TRUE
TA260 002:738.165 JLINK_ReadReg(R15 (PC))
TA260 002:738.175 - 0.010ms returns 0x20000000
TA260 002:738.180 JLINK_ClrBPEx(BPHandle = 0x00000024)
TA260 002:738.183 - 0.003ms returns 0x00
TA260 002:738.188 JLINK_ReadReg(R0)
TA260 002:738.191 - 0.003ms returns 0x00000000
TA260 002:738.591 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:738.598   Data:  E6 F4 7F 3F B6 C9 96 3C F8 F2 7F 3F 1C 5A A3 3C ...
TA260 002:738.609   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:741.214 - 2.623ms returns 0x27C
TA260 002:741.225 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:741.229   Data:  8B 7A 0E 3E 74 74 7D 3F B7 08 10 3E 3D 66 7D 3F ...
TA260 002:741.238   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:743.120 - 1.895ms returns 0x184
TA260 002:743.127 JLINK_HasError()
TA260 002:743.132 JLINK_WriteReg(R0, 0x08006400)
TA260 002:743.137 - 0.004ms returns 0
TA260 002:743.141 JLINK_WriteReg(R1, 0x00000400)
TA260 002:743.145 - 0.003ms returns 0
TA260 002:743.149 JLINK_WriteReg(R2, 0x20000184)
TA260 002:743.152 - 0.003ms returns 0
TA260 002:743.156 JLINK_WriteReg(R3, 0x00000000)
TA260 002:743.160 - 0.003ms returns 0
TA260 002:743.164 JLINK_WriteReg(R4, 0x00000000)
TA260 002:743.167 - 0.003ms returns 0
TA260 002:743.171 JLINK_WriteReg(R5, 0x00000000)
TA260 002:743.175 - 0.003ms returns 0
TA260 002:743.179 JLINK_WriteReg(R6, 0x00000000)
TA260 002:743.182 - 0.003ms returns 0
TA260 002:743.186 JLINK_WriteReg(R7, 0x00000000)
TA260 002:743.189 - 0.003ms returns 0
TA260 002:743.193 JLINK_WriteReg(R8, 0x00000000)
TA260 002:743.197 - 0.003ms returns 0
TA260 002:743.201 JLINK_WriteReg(R9, 0x20000180)
TA260 002:743.204 - 0.003ms returns 0
TA260 002:743.208 JLINK_WriteReg(R10, 0x00000000)
TA260 002:743.212 - 0.003ms returns 0
TA260 002:743.216 JLINK_WriteReg(R11, 0x00000000)
TA260 002:743.219 - 0.003ms returns 0
TA260 002:743.223 JLINK_WriteReg(R12, 0x00000000)
TA260 002:743.226 - 0.003ms returns 0
TA260 002:743.230 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:743.234 - 0.003ms returns 0
TA260 002:743.238 JLINK_WriteReg(R14, 0x20000001)
TA260 002:743.242 - 0.003ms returns 0
TA260 002:743.246 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:743.249 - 0.003ms returns 0
TA260 002:743.253 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:743.257 - 0.003ms returns 0
TA260 002:743.261 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:743.264 - 0.003ms returns 0
TA260 002:743.268 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:743.272 - 0.003ms returns 0
TA260 002:743.276 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:743.279 - 0.003ms returns 0
TA260 002:743.283 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:743.288 - 0.004ms returns 0x00000025
TA260 002:743.292 JLINK_Go()
TA260 002:743.306   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:746.150 - 2.858ms 
TA260 002:746.165 JLINK_IsHalted()
TA260 002:746.715 - 0.549ms returns FALSE
TA260 002:746.723 JLINK_HasError()
TA260 002:748.809 JLINK_IsHalted()
TA260 002:749.304 - 0.494ms returns FALSE
TA260 002:749.314 JLINK_HasError()
TA260 002:750.806 JLINK_IsHalted()
TA260 002:753.149   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:753.615 - 2.808ms returns TRUE
TA260 002:753.621 JLINK_ReadReg(R15 (PC))
TA260 002:753.626 - 0.005ms returns 0x20000000
TA260 002:753.631 JLINK_ClrBPEx(BPHandle = 0x00000025)
TA260 002:753.634 - 0.003ms returns 0x00
TA260 002:753.639 JLINK_ReadReg(R0)
TA260 002:753.642 - 0.003ms returns 0x00000000
TA260 002:754.260 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:754.271   Data:  84 1E 7A 3F 97 39 5A 3E 04 09 7A 3F 6A C2 5B 3E ...
TA260 002:754.282   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:756.822 - 2.561ms returns 0x27C
TA260 002:756.841 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:756.845   Data:  FE C8 A8 3E 57 8F 71 3F C4 86 A9 3E FB 6D 71 3F ...
TA260 002:756.855   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:758.765 - 1.924ms returns 0x184
TA260 002:758.779 JLINK_HasError()
TA260 002:758.785 JLINK_WriteReg(R0, 0x08006800)
TA260 002:758.790 - 0.005ms returns 0
TA260 002:758.795 JLINK_WriteReg(R1, 0x00000400)
TA260 002:758.805 - 0.009ms returns 0
TA260 002:758.809 JLINK_WriteReg(R2, 0x20000184)
TA260 002:758.812 - 0.003ms returns 0
TA260 002:758.816 JLINK_WriteReg(R3, 0x00000000)
TA260 002:758.820 - 0.004ms returns 0
TA260 002:758.824 JLINK_WriteReg(R4, 0x00000000)
TA260 002:758.828 - 0.003ms returns 0
TA260 002:758.832 JLINK_WriteReg(R5, 0x00000000)
TA260 002:758.835 - 0.003ms returns 0
TA260 002:758.839 JLINK_WriteReg(R6, 0x00000000)
TA260 002:758.843 - 0.003ms returns 0
TA260 002:758.847 JLINK_WriteReg(R7, 0x00000000)
TA260 002:758.850 - 0.003ms returns 0
TA260 002:758.854 JLINK_WriteReg(R8, 0x00000000)
TA260 002:758.858 - 0.003ms returns 0
TA260 002:758.862 JLINK_WriteReg(R9, 0x20000180)
TA260 002:758.865 - 0.003ms returns 0
TA260 002:758.869 JLINK_WriteReg(R10, 0x00000000)
TA260 002:758.873 - 0.003ms returns 0
TA260 002:758.877 JLINK_WriteReg(R11, 0x00000000)
TA260 002:758.880 - 0.003ms returns 0
TA260 002:758.884 JLINK_WriteReg(R12, 0x00000000)
TA260 002:758.887 - 0.003ms returns 0
TA260 002:758.892 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:758.895 - 0.003ms returns 0
TA260 002:758.900 JLINK_WriteReg(R14, 0x20000001)
TA260 002:758.903 - 0.003ms returns 0
TA260 002:758.907 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:758.910 - 0.003ms returns 0
TA260 002:758.915 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:758.918 - 0.003ms returns 0
TA260 002:758.922 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:758.925 - 0.003ms returns 0
TA260 002:758.929 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:758.933 - 0.003ms returns 0
TA260 002:758.937 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:758.940 - 0.003ms returns 0
TA260 002:758.945 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:758.949 - 0.004ms returns 0x00000026
TA260 002:758.953 JLINK_Go()
TA260 002:758.961   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:761.678 - 2.724ms 
TA260 002:761.686 JLINK_IsHalted()
TA260 002:762.178 - 0.492ms returns FALSE
TA260 002:762.184 JLINK_HasError()
TA260 002:768.827 JLINK_IsHalted()
TA260 002:771.251   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:771.748 - 2.920ms returns TRUE
TA260 002:771.756 JLINK_ReadReg(R15 (PC))
TA260 002:771.761 - 0.005ms returns 0x20000000
TA260 002:771.766 JLINK_ClrBPEx(BPHandle = 0x00000026)
TA260 002:771.769 - 0.003ms returns 0x00
TA260 002:771.774 JLINK_ReadReg(R0)
TA260 002:771.778 - 0.003ms returns 0x00000000
TA260 002:772.108 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:772.117   Data:  7B AB 6A 3F 8B 9B CC 3E 3C 83 6A 3F CA 53 CD 3E ...
TA260 002:772.127   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:774.683 - 2.574ms returns 0x27C
TA260 002:774.701 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:774.705   Data:  1C EC 01 3F C7 61 5C 3F B1 42 02 3F 8E 2E 5C 3F ...
TA260 002:774.715   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:776.541 - 1.839ms returns 0x184
TA260 002:776.566 JLINK_HasError()
TA260 002:776.600 JLINK_WriteReg(R0, 0x08006C00)
TA260 002:776.607 - 0.006ms returns 0
TA260 002:776.612 JLINK_WriteReg(R1, 0x00000400)
TA260 002:776.615 - 0.003ms returns 0
TA260 002:776.619 JLINK_WriteReg(R2, 0x20000184)
TA260 002:776.622 - 0.003ms returns 0
TA260 002:776.627 JLINK_WriteReg(R3, 0x00000000)
TA260 002:776.630 - 0.003ms returns 0
TA260 002:776.634 JLINK_WriteReg(R4, 0x00000000)
TA260 002:776.638 - 0.003ms returns 0
TA260 002:776.642 JLINK_WriteReg(R5, 0x00000000)
TA260 002:776.645 - 0.003ms returns 0
TA260 002:776.649 JLINK_WriteReg(R6, 0x00000000)
TA260 002:776.653 - 0.003ms returns 0
TA260 002:776.657 JLINK_WriteReg(R7, 0x00000000)
TA260 002:776.660 - 0.003ms returns 0
TA260 002:776.664 JLINK_WriteReg(R8, 0x00000000)
TA260 002:776.668 - 0.003ms returns 0
TA260 002:776.672 JLINK_WriteReg(R9, 0x20000180)
TA260 002:776.675 - 0.003ms returns 0
TA260 002:776.679 JLINK_WriteReg(R10, 0x00000000)
TA260 002:776.683 - 0.003ms returns 0
TA260 002:776.687 JLINK_WriteReg(R11, 0x00000000)
TA260 002:776.690 - 0.003ms returns 0
TA260 002:776.694 JLINK_WriteReg(R12, 0x00000000)
TA260 002:776.701 - 0.007ms returns 0
TA260 002:776.708 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:776.712 - 0.004ms returns 0
TA260 002:776.716 JLINK_WriteReg(R14, 0x20000001)
TA260 002:776.719 - 0.003ms returns 0
TA260 002:776.723 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:776.727 - 0.003ms returns 0
TA260 002:776.731 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:776.734 - 0.003ms returns 0
TA260 002:776.738 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:776.742 - 0.003ms returns 0
TA260 002:776.746 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:776.749 - 0.003ms returns 0
TA260 002:776.753 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:776.757 - 0.003ms returns 0
TA260 002:776.761 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:776.766 - 0.004ms returns 0x00000027
TA260 002:776.770 JLINK_Go()
TA260 002:776.779   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:779.531 - 2.760ms 
TA260 002:779.547 JLINK_IsHalted()
TA260 002:780.018 - 0.471ms returns FALSE
TA260 002:780.025 JLINK_HasError()
TA260 002:784.329 JLINK_IsHalted()
TA260 002:786.722   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:787.260 - 2.929ms returns TRUE
TA260 002:787.277 JLINK_ReadReg(R15 (PC))
TA260 002:787.283 - 0.006ms returns 0x20000000
TA260 002:787.287 JLINK_ClrBPEx(BPHandle = 0x00000027)
TA260 002:787.291 - 0.003ms returns 0x00
TA260 002:787.296 JLINK_ReadReg(R0)
TA260 002:787.299 - 0.003ms returns 0x00000000
TA260 002:787.680 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:787.687   Data:  C6 33 52 3F B0 1E 12 3F 54 FA 51 3F 30 71 12 3F ...
TA260 002:787.698   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:790.307 - 2.627ms returns 0x27C
TA260 002:790.318 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:790.322   Data:  8E 75 2A 3F 1B BC 3E 3F 82 C0 2A 3F FF 78 3E 3F ...
TA260 002:790.330   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:792.209 - 1.891ms returns 0x184
TA260 002:792.216 JLINK_HasError()
TA260 002:792.221 JLINK_WriteReg(R0, 0x08007000)
TA260 002:792.225 - 0.004ms returns 0
TA260 002:792.230 JLINK_WriteReg(R1, 0x00000400)
TA260 002:792.233 - 0.003ms returns 0
TA260 002:792.237 JLINK_WriteReg(R2, 0x20000184)
TA260 002:792.240 - 0.003ms returns 0
TA260 002:792.244 JLINK_WriteReg(R3, 0x00000000)
TA260 002:792.248 - 0.003ms returns 0
TA260 002:792.252 JLINK_WriteReg(R4, 0x00000000)
TA260 002:792.255 - 0.003ms returns 0
TA260 002:792.259 JLINK_WriteReg(R5, 0x00000000)
TA260 002:792.262 - 0.003ms returns 0
TA260 002:792.266 JLINK_WriteReg(R6, 0x00000000)
TA260 002:792.270 - 0.003ms returns 0
TA260 002:792.274 JLINK_WriteReg(R7, 0x00000000)
TA260 002:792.278 - 0.003ms returns 0
TA260 002:792.282 JLINK_WriteReg(R8, 0x00000000)
TA260 002:792.285 - 0.003ms returns 0
TA260 002:792.289 JLINK_WriteReg(R9, 0x20000180)
TA260 002:792.292 - 0.003ms returns 0
TA260 002:792.296 JLINK_WriteReg(R10, 0x00000000)
TA260 002:792.300 - 0.003ms returns 0
TA260 002:792.304 JLINK_WriteReg(R11, 0x00000000)
TA260 002:792.307 - 0.003ms returns 0
TA260 002:792.311 JLINK_WriteReg(R12, 0x00000000)
TA260 002:792.315 - 0.003ms returns 0
TA260 002:792.319 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:792.322 - 0.003ms returns 0
TA260 002:792.326 JLINK_WriteReg(R14, 0x20000001)
TA260 002:792.330 - 0.003ms returns 0
TA260 002:792.334 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:792.337 - 0.003ms returns 0
TA260 002:792.341 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:792.345 - 0.003ms returns 0
TA260 002:792.349 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:792.352 - 0.003ms returns 0
TA260 002:792.356 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:792.360 - 0.003ms returns 0
TA260 002:792.366 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:792.370 - 0.003ms returns 0
TA260 002:792.374 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:792.378 - 0.004ms returns 0x00000028
TA260 002:792.383 JLINK_Go()
TA260 002:792.390   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:795.243 - 2.859ms 
TA260 002:795.258 JLINK_IsHalted()
TA260 002:795.731 - 0.473ms returns FALSE
TA260 002:795.744 JLINK_HasError()
TA260 002:799.338 JLINK_IsHalted()
TA260 002:801.694   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:802.192 - 2.853ms returns TRUE
TA260 002:802.203 JLINK_ReadReg(R15 (PC))
TA260 002:802.208 - 0.005ms returns 0x20000000
TA260 002:802.240 JLINK_ClrBPEx(BPHandle = 0x00000028)
TA260 002:802.255 - 0.014ms returns 0x00
TA260 002:802.260 JLINK_ReadReg(R0)
TA260 002:802.263 - 0.003ms returns 0x00000000
TA260 002:802.571 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:802.579   Data:  1D A8 31 3F 16 52 38 3F AD 5F 31 3F CB 97 38 3F ...
TA260 002:802.590   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:805.211 - 2.640ms returns 0x27C
TA260 002:805.225 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:805.229   Data:  08 72 4C 3F 00 C2 19 3F 79 AE 4C 3F 94 71 19 3F ...
TA260 002:805.240   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:807.167 - 1.942ms returns 0x184
TA260 002:807.217 JLINK_HasError()
TA260 002:807.223 JLINK_WriteReg(R0, 0x08007400)
TA260 002:807.228 - 0.005ms returns 0
TA260 002:807.233 JLINK_WriteReg(R1, 0x00000400)
TA260 002:807.236 - 0.003ms returns 0
TA260 002:807.240 JLINK_WriteReg(R2, 0x20000184)
TA260 002:807.244 - 0.003ms returns 0
TA260 002:807.248 JLINK_WriteReg(R3, 0x00000000)
TA260 002:807.269 - 0.021ms returns 0
TA260 002:807.274 JLINK_WriteReg(R4, 0x00000000)
TA260 002:807.277 - 0.003ms returns 0
TA260 002:807.281 JLINK_WriteReg(R5, 0x00000000)
TA260 002:807.284 - 0.003ms returns 0
TA260 002:807.288 JLINK_WriteReg(R6, 0x00000000)
TA260 002:807.292 - 0.003ms returns 0
TA260 002:807.296 JLINK_WriteReg(R7, 0x00000000)
TA260 002:807.299 - 0.003ms returns 0
TA260 002:807.303 JLINK_WriteReg(R8, 0x00000000)
TA260 002:807.306 - 0.003ms returns 0
TA260 002:807.310 JLINK_WriteReg(R9, 0x20000180)
TA260 002:807.314 - 0.003ms returns 0
TA260 002:807.318 JLINK_WriteReg(R10, 0x00000000)
TA260 002:807.322 - 0.004ms returns 0
TA260 002:807.326 JLINK_WriteReg(R11, 0x00000000)
TA260 002:807.330 - 0.003ms returns 0
TA260 002:807.334 JLINK_WriteReg(R12, 0x00000000)
TA260 002:807.337 - 0.003ms returns 0
TA260 002:807.341 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:807.346 - 0.004ms returns 0
TA260 002:807.350 JLINK_WriteReg(R14, 0x20000001)
TA260 002:807.353 - 0.003ms returns 0
TA260 002:807.357 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:807.360 - 0.003ms returns 0
TA260 002:807.364 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:807.368 - 0.003ms returns 0
TA260 002:807.374 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:807.378 - 0.004ms returns 0
TA260 002:807.382 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:807.386 - 0.003ms returns 0
TA260 002:807.390 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:807.393 - 0.003ms returns 0
TA260 002:807.398 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:807.402 - 0.004ms returns 0x00000029
TA260 002:807.406 JLINK_Go()
TA260 002:807.422   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:810.240 - 2.832ms 
TA260 002:810.250 JLINK_IsHalted()
TA260 002:810.744 - 0.494ms returns FALSE
TA260 002:810.750 JLINK_HasError()
TA260 002:814.852 JLINK_IsHalted()
TA260 002:817.250   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:817.747 - 2.894ms returns TRUE
TA260 002:817.754 JLINK_ReadReg(R15 (PC))
TA260 002:817.760 - 0.005ms returns 0x20000000
TA260 002:817.764 JLINK_ClrBPEx(BPHandle = 0x00000029)
TA260 002:817.768 - 0.003ms returns 0x00
TA260 002:817.773 JLINK_ReadReg(R0)
TA260 002:817.776 - 0.003ms returns 0x00000000
TA260 002:818.150 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:818.158   Data:  AD 48 0A 3F 26 70 57 3F 09 F4 09 3F 63 A6 57 3F ...
TA260 002:818.168   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:820.805 - 2.655ms returns 0x27C
TA260 002:820.815 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:820.819   Data:  30 93 66 3F 79 BE DD 3E CC BE 66 3F 2E 09 DD 3E ...
TA260 002:820.827   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:822.746 - 1.930ms returns 0x184
TA260 002:822.754 JLINK_HasError()
TA260 002:822.759 JLINK_WriteReg(R0, 0x08007800)
TA260 002:822.764 - 0.004ms returns 0
TA260 002:822.774 JLINK_WriteReg(R1, 0x00000400)
TA260 002:822.777 - 0.003ms returns 0
TA260 002:822.781 JLINK_WriteReg(R2, 0x20000184)
TA260 002:822.785 - 0.003ms returns 0
TA260 002:822.789 JLINK_WriteReg(R3, 0x00000000)
TA260 002:822.792 - 0.003ms returns 0
TA260 002:822.796 JLINK_WriteReg(R4, 0x00000000)
TA260 002:822.800 - 0.003ms returns 0
TA260 002:822.804 JLINK_WriteReg(R5, 0x00000000)
TA260 002:822.807 - 0.003ms returns 0
TA260 002:822.811 JLINK_WriteReg(R6, 0x00000000)
TA260 002:822.814 - 0.003ms returns 0
TA260 002:822.818 JLINK_WriteReg(R7, 0x00000000)
TA260 002:822.822 - 0.003ms returns 0
TA260 002:822.826 JLINK_WriteReg(R8, 0x00000000)
TA260 002:822.829 - 0.003ms returns 0
TA260 002:822.833 JLINK_WriteReg(R9, 0x20000180)
TA260 002:822.836 - 0.003ms returns 0
TA260 002:822.840 JLINK_WriteReg(R10, 0x00000000)
TA260 002:822.844 - 0.003ms returns 0
TA260 002:822.848 JLINK_WriteReg(R11, 0x00000000)
TA260 002:822.851 - 0.003ms returns 0
TA260 002:822.856 JLINK_WriteReg(R12, 0x00000000)
TA260 002:822.859 - 0.003ms returns 0
TA260 002:822.863 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:822.867 - 0.004ms returns 0
TA260 002:822.871 JLINK_WriteReg(R14, 0x20000001)
TA260 002:822.874 - 0.003ms returns 0
TA260 002:822.879 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:822.882 - 0.003ms returns 0
TA260 002:822.886 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:822.890 - 0.003ms returns 0
TA260 002:822.894 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:822.897 - 0.003ms returns 0
TA260 002:822.901 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:822.904 - 0.003ms returns 0
TA260 002:822.908 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:822.912 - 0.003ms returns 0
TA260 002:822.916 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:822.920 - 0.004ms returns 0x0000002A
TA260 002:822.925 JLINK_Go()
TA260 002:822.933   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:825.714 - 2.787ms 
TA260 002:825.732 JLINK_IsHalted()
TA260 002:826.206 - 0.474ms returns FALSE
TA260 002:826.222 JLINK_HasError()
TA260 002:830.863 JLINK_IsHalted()
TA260 002:833.304   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:833.792 - 2.927ms returns TRUE
TA260 002:833.800 JLINK_ReadReg(R15 (PC))
TA260 002:833.805 - 0.005ms returns 0x20000000
TA260 002:833.809 JLINK_ClrBPEx(BPHandle = 0x0000002A)
TA260 002:833.813 - 0.003ms returns 0x00
TA260 002:833.818 JLINK_ReadReg(R0)
TA260 002:833.821 - 0.003ms returns 0x00000000
TA260 002:834.126 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:834.133   Data:  A0 31 BB 3E BE 46 6E 3F 6E 76 BA 3E 6D 6B 6E 3F ...
TA260 002:834.144   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:836.677 - 2.551ms returns 0x27C
TA260 002:836.699 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:836.703   Data:  F7 D7 77 3F E1 E6 7E 3E 10 F1 77 3F 56 61 7D 3E ...
TA260 002:836.716   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:838.572 - 1.872ms returns 0x184
TA260 002:838.588 JLINK_HasError()
TA260 002:838.593 JLINK_WriteReg(R0, 0x08007C00)
TA260 002:838.599 - 0.006ms returns 0
TA260 002:838.604 JLINK_WriteReg(R1, 0x00000400)
TA260 002:838.607 - 0.003ms returns 0
TA260 002:838.611 JLINK_WriteReg(R2, 0x20000184)
TA260 002:838.615 - 0.003ms returns 0
TA260 002:838.619 JLINK_WriteReg(R3, 0x00000000)
TA260 002:838.623 - 0.004ms returns 0
TA260 002:838.627 JLINK_WriteReg(R4, 0x00000000)
TA260 002:838.630 - 0.003ms returns 0
TA260 002:838.634 JLINK_WriteReg(R5, 0x00000000)
TA260 002:838.638 - 0.003ms returns 0
TA260 002:838.642 JLINK_WriteReg(R6, 0x00000000)
TA260 002:838.645 - 0.003ms returns 0
TA260 002:838.649 JLINK_WriteReg(R7, 0x00000000)
TA260 002:838.652 - 0.003ms returns 0
TA260 002:838.656 JLINK_WriteReg(R8, 0x00000000)
TA260 002:838.660 - 0.003ms returns 0
TA260 002:838.664 JLINK_WriteReg(R9, 0x20000180)
TA260 002:838.667 - 0.003ms returns 0
TA260 002:838.671 JLINK_WriteReg(R10, 0x00000000)
TA260 002:838.674 - 0.003ms returns 0
TA260 002:838.678 JLINK_WriteReg(R11, 0x00000000)
TA260 002:838.682 - 0.003ms returns 0
TA260 002:838.690 JLINK_WriteReg(R12, 0x00000000)
TA260 002:838.695 - 0.005ms returns 0
TA260 002:838.700 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:838.703 - 0.003ms returns 0
TA260 002:838.707 JLINK_WriteReg(R14, 0x20000001)
TA260 002:838.711 - 0.003ms returns 0
TA260 002:838.715 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:838.718 - 0.003ms returns 0
TA260 002:838.723 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:838.726 - 0.003ms returns 0
TA260 002:838.730 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:838.734 - 0.003ms returns 0
TA260 002:838.738 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:838.741 - 0.003ms returns 0
TA260 002:838.745 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:838.748 - 0.003ms returns 0
TA260 002:838.753 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:838.757 - 0.004ms returns 0x0000002B
TA260 002:838.762 JLINK_Go()
TA260 002:838.770   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:841.472 - 2.710ms 
TA260 002:841.480 JLINK_IsHalted()
TA260 002:841.951 - 0.471ms returns FALSE
TA260 002:841.956 JLINK_HasError()
TA260 002:847.526 JLINK_IsHalted()
TA260 002:849.899   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:850.420 - 2.893ms returns TRUE
TA260 002:850.431 JLINK_ReadReg(R15 (PC))
TA260 002:850.437 - 0.006ms returns 0x20000000
TA260 002:850.442 JLINK_ClrBPEx(BPHandle = 0x0000002B)
TA260 002:850.446 - 0.003ms returns 0x00
TA260 002:850.451 JLINK_ReadReg(R0)
TA260 002:850.455 - 0.003ms returns 0x00000000
TA260 002:850.801 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:850.809   Data:  98 40 35 3E 31 F5 7B 3F C4 B4 33 3E E9 06 7C 3F ...
TA260 002:850.820   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:853.473 - 2.671ms returns 0x27C
TA260 002:853.484 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:853.488   Data:  78 96 7F 3F 68 14 62 3D 18 9C 7F 3F 4C CE 5B 3D ...
TA260 002:853.494   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:855.412 - 1.928ms returns 0x184
TA260 002:855.432 JLINK_HasError()
TA260 002:855.438 JLINK_WriteReg(R0, 0x08008000)
TA260 002:855.445 - 0.006ms returns 0
TA260 002:855.449 JLINK_WriteReg(R1, 0x00000400)
TA260 002:855.453 - 0.003ms returns 0
TA260 002:855.457 JLINK_WriteReg(R2, 0x20000184)
TA260 002:855.460 - 0.003ms returns 0
TA260 002:855.464 JLINK_WriteReg(R3, 0x00000000)
TA260 002:855.467 - 0.003ms returns 0
TA260 002:855.472 JLINK_WriteReg(R4, 0x00000000)
TA260 002:855.475 - 0.003ms returns 0
TA260 002:855.480 JLINK_WriteReg(R5, 0x00000000)
TA260 002:855.484 - 0.003ms returns 0
TA260 002:855.526 JLINK_WriteReg(R6, 0x00000000)
TA260 002:855.574 - 0.048ms returns 0
TA260 002:855.580 JLINK_WriteReg(R7, 0x00000000)
TA260 002:855.583 - 0.003ms returns 0
TA260 002:855.588 JLINK_WriteReg(R8, 0x00000000)
TA260 002:855.591 - 0.003ms returns 0
TA260 002:855.595 JLINK_WriteReg(R9, 0x20000180)
TA260 002:855.598 - 0.003ms returns 0
TA260 002:855.602 JLINK_WriteReg(R10, 0x00000000)
TA260 002:855.606 - 0.003ms returns 0
TA260 002:855.610 JLINK_WriteReg(R11, 0x00000000)
TA260 002:855.613 - 0.003ms returns 0
TA260 002:855.617 JLINK_WriteReg(R12, 0x00000000)
TA260 002:855.620 - 0.003ms returns 0
TA260 002:855.625 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:855.629 - 0.004ms returns 0
TA260 002:855.633 JLINK_WriteReg(R14, 0x20000001)
TA260 002:855.636 - 0.003ms returns 0
TA260 002:855.640 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:855.644 - 0.003ms returns 0
TA260 002:855.648 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:855.651 - 0.003ms returns 0
TA260 002:855.655 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:855.659 - 0.003ms returns 0
TA260 002:855.663 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:855.666 - 0.003ms returns 0
TA260 002:855.671 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:855.674 - 0.003ms returns 0
TA260 002:855.679 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:855.683 - 0.005ms returns 0x0000002C
TA260 002:855.688 JLINK_Go()
TA260 002:855.721   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:858.313 - 2.624ms 
TA260 002:858.326 JLINK_IsHalted()
TA260 002:858.825 - 0.499ms returns FALSE
TA260 002:858.836 JLINK_HasError()
TA260 002:862.535 JLINK_IsHalted()
TA260 002:864.889   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:865.414 - 2.878ms returns TRUE
TA260 002:865.428 JLINK_ReadReg(R15 (PC))
TA260 002:865.434 - 0.006ms returns 0x20000000
TA260 002:865.438 JLINK_ClrBPEx(BPHandle = 0x0000002C)
TA260 002:865.443 - 0.004ms returns 0x00
TA260 002:865.447 JLINK_ReadReg(R0)
TA260 002:865.451 - 0.003ms returns 0x00000000
TA260 002:865.815 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:865.827   Data:  B6 C9 96 BC E6 F4 7F 3F 1C 5A A3 BC F8 F2 7F 3F ...
TA260 002:865.840   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:868.412 - 2.596ms returns 0x27C
TA260 002:868.426 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:868.430   Data:  85 82 7D 3F B7 08 10 BE 74 74 7D 3F CC 96 11 BE ...
TA260 002:868.440   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:870.414 - 1.988ms returns 0x184
TA260 002:870.432 JLINK_HasError()
TA260 002:870.438 JLINK_WriteReg(R0, 0x08008400)
TA260 002:870.443 - 0.005ms returns 0
TA260 002:870.447 JLINK_WriteReg(R1, 0x00000400)
TA260 002:870.451 - 0.003ms returns 0
TA260 002:870.455 JLINK_WriteReg(R2, 0x20000184)
TA260 002:870.464 - 0.008ms returns 0
TA260 002:870.468 JLINK_WriteReg(R3, 0x00000000)
TA260 002:870.472 - 0.003ms returns 0
TA260 002:870.476 JLINK_WriteReg(R4, 0x00000000)
TA260 002:870.479 - 0.003ms returns 0
TA260 002:870.483 JLINK_WriteReg(R5, 0x00000000)
TA260 002:870.486 - 0.003ms returns 0
TA260 002:870.490 JLINK_WriteReg(R6, 0x00000000)
TA260 002:870.494 - 0.003ms returns 0
TA260 002:870.498 JLINK_WriteReg(R7, 0x00000000)
TA260 002:870.501 - 0.003ms returns 0
TA260 002:870.506 JLINK_WriteReg(R8, 0x00000000)
TA260 002:870.509 - 0.003ms returns 0
TA260 002:870.514 JLINK_WriteReg(R9, 0x20000180)
TA260 002:870.517 - 0.003ms returns 0
TA260 002:870.521 JLINK_WriteReg(R10, 0x00000000)
TA260 002:870.524 - 0.003ms returns 0
TA260 002:870.528 JLINK_WriteReg(R11, 0x00000000)
TA260 002:870.532 - 0.003ms returns 0
TA260 002:870.536 JLINK_WriteReg(R12, 0x00000000)
TA260 002:870.539 - 0.003ms returns 0
TA260 002:870.543 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:870.547 - 0.003ms returns 0
TA260 002:870.551 JLINK_WriteReg(R14, 0x20000001)
TA260 002:870.554 - 0.003ms returns 0
TA260 002:870.558 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:870.562 - 0.003ms returns 0
TA260 002:870.566 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:870.569 - 0.003ms returns 0
TA260 002:870.573 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:870.576 - 0.003ms returns 0
TA260 002:870.580 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:870.584 - 0.003ms returns 0
TA260 002:870.588 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:870.591 - 0.003ms returns 0
TA260 002:870.596 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:870.600 - 0.004ms returns 0x0000002D
TA260 002:870.604 JLINK_Go()
TA260 002:870.613   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:873.280 - 2.675ms 
TA260 002:873.286 JLINK_IsHalted()
TA260 002:873.767 - 0.481ms returns FALSE
TA260 002:873.772 JLINK_HasError()
TA260 002:877.046 JLINK_IsHalted()
TA260 002:877.544 - 0.498ms returns FALSE
TA260 002:877.551 JLINK_HasError()
TA260 002:880.550 JLINK_IsHalted()
TA260 002:882.884   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:883.407 - 2.856ms returns TRUE
TA260 002:883.418 JLINK_ReadReg(R15 (PC))
TA260 002:883.423 - 0.005ms returns 0x20000000
TA260 002:883.450 JLINK_ClrBPEx(BPHandle = 0x0000002D)
TA260 002:883.454 - 0.005ms returns 0x00
TA260 002:883.459 JLINK_ReadReg(R0)
TA260 002:883.462 - 0.003ms returns 0x00000000
TA260 002:883.763 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:883.770   Data:  97 39 5A BE 84 1E 7A 3F 6A C2 5B BE 04 09 7A 3F ...
TA260 002:883.781   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:886.416 - 2.652ms returns 0x27C
TA260 002:886.437 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:886.446   Data:  8E B0 71 3F C4 86 A9 BE 57 8F 71 3F 6F 44 AA BE ...
TA260 002:886.458   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:888.414 - 1.976ms returns 0x184
TA260 002:888.430 JLINK_HasError()
TA260 002:888.435 JLINK_WriteReg(R0, 0x08008800)
TA260 002:888.440 - 0.005ms returns 0
TA260 002:888.444 JLINK_WriteReg(R1, 0x00000400)
TA260 002:888.448 - 0.003ms returns 0
TA260 002:888.452 JLINK_WriteReg(R2, 0x20000184)
TA260 002:888.455 - 0.003ms returns 0
TA260 002:888.459 JLINK_WriteReg(R3, 0x00000000)
TA260 002:888.463 - 0.003ms returns 0
TA260 002:888.467 JLINK_WriteReg(R4, 0x00000000)
TA260 002:888.470 - 0.003ms returns 0
TA260 002:888.474 JLINK_WriteReg(R5, 0x00000000)
TA260 002:888.478 - 0.003ms returns 0
TA260 002:888.482 JLINK_WriteReg(R6, 0x00000000)
TA260 002:888.485 - 0.003ms returns 0
TA260 002:888.489 JLINK_WriteReg(R7, 0x00000000)
TA260 002:888.492 - 0.003ms returns 0
TA260 002:888.497 JLINK_WriteReg(R8, 0x00000000)
TA260 002:888.501 - 0.003ms returns 0
TA260 002:888.505 JLINK_WriteReg(R9, 0x20000180)
TA260 002:888.508 - 0.003ms returns 0
TA260 002:888.512 JLINK_WriteReg(R10, 0x00000000)
TA260 002:888.515 - 0.003ms returns 0
TA260 002:888.519 JLINK_WriteReg(R11, 0x00000000)
TA260 002:888.523 - 0.003ms returns 0
TA260 002:888.527 JLINK_WriteReg(R12, 0x00000000)
TA260 002:888.530 - 0.003ms returns 0
TA260 002:888.534 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:888.538 - 0.004ms returns 0
TA260 002:888.542 JLINK_WriteReg(R14, 0x20000001)
TA260 002:888.545 - 0.003ms returns 0
TA260 002:888.550 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:888.553 - 0.003ms returns 0
TA260 002:888.557 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:888.561 - 0.003ms returns 0
TA260 002:888.565 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:888.568 - 0.003ms returns 0
TA260 002:888.572 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:888.575 - 0.003ms returns 0
TA260 002:888.579 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:888.583 - 0.003ms returns 0
TA260 002:888.588 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:888.592 - 0.005ms returns 0x0000002E
TA260 002:888.596 JLINK_Go()
TA260 002:888.605   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:891.290 - 2.693ms 
TA260 002:891.299 JLINK_IsHalted()
TA260 002:891.792 - 0.493ms returns FALSE
TA260 002:891.798 JLINK_HasError()
TA260 002:896.063 JLINK_IsHalted()
TA260 002:898.420   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:898.907 - 2.844ms returns TRUE
TA260 002:898.914 JLINK_ReadReg(R15 (PC))
TA260 002:898.920 - 0.005ms returns 0x20000000
TA260 002:898.925 JLINK_ClrBPEx(BPHandle = 0x0000002E)
TA260 002:898.928 - 0.003ms returns 0x00
TA260 002:898.933 JLINK_ReadReg(R0)
TA260 002:898.936 - 0.003ms returns 0x00000000
TA260 002:899.287 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:899.295   Data:  8B 9B CC BE 7B AB 6A 3F CA 53 CD BE 3C 83 6A 3F ...
TA260 002:899.306   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:901.894 - 2.606ms returns 0x27C
TA260 002:901.902 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:901.906   Data:  DD 94 5C 3F B1 42 02 BF C7 61 5C 3F 32 99 02 BF ...
TA260 002:901.914   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:903.790 - 1.888ms returns 0x184
TA260 002:903.796 JLINK_HasError()
TA260 002:903.802 JLINK_WriteReg(R0, 0x08008C00)
TA260 002:903.806 - 0.004ms returns 0
TA260 002:903.810 JLINK_WriteReg(R1, 0x00000400)
TA260 002:903.813 - 0.003ms returns 0
TA260 002:903.818 JLINK_WriteReg(R2, 0x20000184)
TA260 002:903.821 - 0.003ms returns 0
TA260 002:903.825 JLINK_WriteReg(R3, 0x00000000)
TA260 002:903.828 - 0.003ms returns 0
TA260 002:903.832 JLINK_WriteReg(R4, 0x00000000)
TA260 002:903.836 - 0.003ms returns 0
TA260 002:903.840 JLINK_WriteReg(R5, 0x00000000)
TA260 002:903.843 - 0.003ms returns 0
TA260 002:903.847 JLINK_WriteReg(R6, 0x00000000)
TA260 002:903.850 - 0.003ms returns 0
TA260 002:903.854 JLINK_WriteReg(R7, 0x00000000)
TA260 002:903.858 - 0.003ms returns 0
TA260 002:903.862 JLINK_WriteReg(R8, 0x00000000)
TA260 002:903.866 - 0.003ms returns 0
TA260 002:903.870 JLINK_WriteReg(R9, 0x20000180)
TA260 002:903.873 - 0.003ms returns 0
TA260 002:903.877 JLINK_WriteReg(R10, 0x00000000)
TA260 002:903.885 - 0.007ms returns 0
TA260 002:903.889 JLINK_WriteReg(R11, 0x00000000)
TA260 002:903.892 - 0.003ms returns 0
TA260 002:903.896 JLINK_WriteReg(R12, 0x00000000)
TA260 002:903.900 - 0.003ms returns 0
TA260 002:903.904 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:903.908 - 0.003ms returns 0
TA260 002:903.913 JLINK_WriteReg(R14, 0x20000001)
TA260 002:903.917 - 0.003ms returns 0
TA260 002:903.921 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:903.924 - 0.003ms returns 0
TA260 002:903.929 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:903.932 - 0.003ms returns 0
TA260 002:903.936 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:903.940 - 0.003ms returns 0
TA260 002:903.944 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:903.947 - 0.003ms returns 0
TA260 002:903.951 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:903.954 - 0.003ms returns 0
TA260 002:903.959 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:903.963 - 0.004ms returns 0x0000002F
TA260 002:903.967 JLINK_Go()
TA260 002:903.974   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:906.810 - 2.842ms 
TA260 002:906.831 JLINK_IsHalted()
TA260 002:907.390 - 0.559ms returns FALSE
TA260 002:907.398 JLINK_HasError()
TA260 002:910.074 JLINK_IsHalted()
TA260 002:910.565 - 0.490ms returns FALSE
TA260 002:910.572 JLINK_HasError()
TA260 002:914.069 JLINK_IsHalted()
TA260 002:916.517   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:917.016 - 2.946ms returns TRUE
TA260 002:917.032 JLINK_ReadReg(R15 (PC))
TA260 002:917.038 - 0.006ms returns 0x20000000
TA260 002:917.042 JLINK_ClrBPEx(BPHandle = 0x0000002F)
TA260 002:917.046 - 0.004ms returns 0x00
TA260 002:917.051 JLINK_ReadReg(R0)
TA260 002:917.055 - 0.004ms returns 0x00000000
TA260 002:917.401 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:917.408   Data:  B0 1E 12 BF C6 33 52 3F 30 71 12 BF 54 FA 51 3F ...
TA260 002:917.419   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:920.020 - 2.618ms returns 0x27C
TA260 002:920.029 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:920.033   Data:  1B FF 3E 3F 82 C0 2A BF 1B BC 3E 3F 5B 0B 2B BF ...
TA260 002:920.042   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:921.938 - 1.908ms returns 0x184
TA260 002:921.944 JLINK_HasError()
TA260 002:921.949 JLINK_WriteReg(R0, 0x08009000)
TA260 002:921.954 - 0.004ms returns 0
TA260 002:921.958 JLINK_WriteReg(R1, 0x00000400)
TA260 002:921.961 - 0.003ms returns 0
TA260 002:921.965 JLINK_WriteReg(R2, 0x20000184)
TA260 002:921.969 - 0.003ms returns 0
TA260 002:921.973 JLINK_WriteReg(R3, 0x00000000)
TA260 002:921.976 - 0.003ms returns 0
TA260 002:921.980 JLINK_WriteReg(R4, 0x00000000)
TA260 002:921.983 - 0.003ms returns 0
TA260 002:921.987 JLINK_WriteReg(R5, 0x00000000)
TA260 002:921.991 - 0.003ms returns 0
TA260 002:921.995 JLINK_WriteReg(R6, 0x00000000)
TA260 002:921.998 - 0.003ms returns 0
TA260 002:922.002 JLINK_WriteReg(R7, 0x00000000)
TA260 002:922.006 - 0.003ms returns 0
TA260 002:922.010 JLINK_WriteReg(R8, 0x00000000)
TA260 002:922.013 - 0.003ms returns 0
TA260 002:922.017 JLINK_WriteReg(R9, 0x20000180)
TA260 002:922.020 - 0.003ms returns 0
TA260 002:922.024 JLINK_WriteReg(R10, 0x00000000)
TA260 002:922.028 - 0.003ms returns 0
TA260 002:922.032 JLINK_WriteReg(R11, 0x00000000)
TA260 002:922.035 - 0.003ms returns 0
TA260 002:922.039 JLINK_WriteReg(R12, 0x00000000)
TA260 002:922.043 - 0.003ms returns 0
TA260 002:922.047 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:922.051 - 0.003ms returns 0
TA260 002:922.055 JLINK_WriteReg(R14, 0x20000001)
TA260 002:922.058 - 0.003ms returns 0
TA260 002:922.062 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:922.066 - 0.003ms returns 0
TA260 002:922.070 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:922.074 - 0.003ms returns 0
TA260 002:922.078 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:922.081 - 0.003ms returns 0
TA260 002:922.085 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:922.089 - 0.003ms returns 0
TA260 002:922.092 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:922.096 - 0.003ms returns 0
TA260 002:922.105 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:922.111 - 0.006ms returns 0x00000030
TA260 002:922.115 JLINK_Go()
TA260 002:922.122   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:924.856 - 2.740ms 
TA260 002:924.870 JLINK_IsHalted()
TA260 002:925.413 - 0.543ms returns FALSE
TA260 002:925.426 JLINK_HasError()
TA260 002:929.089 JLINK_IsHalted()
TA260 002:931.471   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:931.951 - 2.862ms returns TRUE
TA260 002:931.958 JLINK_ReadReg(R15 (PC))
TA260 002:931.963 - 0.005ms returns 0x20000000
TA260 002:931.968 JLINK_ClrBPEx(BPHandle = 0x00000030)
TA260 002:931.972 - 0.003ms returns 0x00
TA260 002:931.976 JLINK_ReadReg(R0)
TA260 002:931.979 - 0.003ms returns 0x00000000
TA260 002:932.282 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:932.290   Data:  16 52 38 BF 1D A8 31 3F CB 97 38 BF AD 5F 31 3F ...
TA260 002:932.301   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:934.903 - 2.620ms returns 0x27C
TA260 002:934.921 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:934.926   Data:  55 12 1A 3F 79 AE 4C BF 00 C2 19 3F CB EA 4C BF ...
TA260 002:934.937   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:936.841 - 1.920ms returns 0x184
TA260 002:936.861 JLINK_HasError()
TA260 002:936.944 JLINK_WriteReg(R0, 0x08009400)
TA260 002:936.951 - 0.006ms returns 0
TA260 002:936.984 JLINK_WriteReg(R1, 0x00000400)
TA260 002:936.989 - 0.005ms returns 0
TA260 002:936.994 JLINK_WriteReg(R2, 0x20000184)
TA260 002:936.997 - 0.003ms returns 0
TA260 002:937.001 JLINK_WriteReg(R3, 0x00000000)
TA260 002:937.005 - 0.003ms returns 0
TA260 002:937.009 JLINK_WriteReg(R4, 0x00000000)
TA260 002:937.012 - 0.003ms returns 0
TA260 002:937.016 JLINK_WriteReg(R5, 0x00000000)
TA260 002:937.020 - 0.003ms returns 0
TA260 002:937.024 JLINK_WriteReg(R6, 0x00000000)
TA260 002:937.027 - 0.003ms returns 0
TA260 002:937.031 JLINK_WriteReg(R7, 0x00000000)
TA260 002:937.034 - 0.003ms returns 0
TA260 002:937.038 JLINK_WriteReg(R8, 0x00000000)
TA260 002:937.042 - 0.003ms returns 0
TA260 002:937.046 JLINK_WriteReg(R9, 0x20000180)
TA260 002:937.049 - 0.003ms returns 0
TA260 002:937.053 JLINK_WriteReg(R10, 0x00000000)
TA260 002:937.057 - 0.003ms returns 0
TA260 002:937.061 JLINK_WriteReg(R11, 0x00000000)
TA260 002:937.064 - 0.003ms returns 0
TA260 002:937.068 JLINK_WriteReg(R12, 0x00000000)
TA260 002:937.071 - 0.003ms returns 0
TA260 002:937.075 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:937.079 - 0.004ms returns 0
TA260 002:937.084 JLINK_WriteReg(R14, 0x20000001)
TA260 002:937.087 - 0.003ms returns 0
TA260 002:937.091 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:937.094 - 0.003ms returns 0
TA260 002:937.099 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:937.102 - 0.003ms returns 0
TA260 002:937.107 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:937.110 - 0.003ms returns 0
TA260 002:937.114 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:937.117 - 0.003ms returns 0
TA260 002:937.121 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:937.125 - 0.003ms returns 0
TA260 002:937.129 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:937.134 - 0.004ms returns 0x00000031
TA260 002:937.138 JLINK_Go()
TA260 002:937.154   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:939.847 - 2.708ms 
TA260 002:939.857 JLINK_IsHalted()
TA260 002:940.414 - 0.557ms returns FALSE
TA260 002:940.424 JLINK_HasError()
TA260 002:945.108 JLINK_IsHalted()
TA260 002:947.468   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:947.998 - 2.890ms returns TRUE
TA260 002:948.006 JLINK_ReadReg(R15 (PC))
TA260 002:948.011 - 0.005ms returns 0x20000000
TA260 002:948.016 JLINK_ClrBPEx(BPHandle = 0x00000031)
TA260 002:948.019 - 0.003ms returns 0x00
TA260 002:948.024 JLINK_ReadReg(R0)
TA260 002:948.028 - 0.003ms returns 0x00000000
TA260 002:948.385 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:948.393   Data:  26 70 57 BF AD 48 0A 3F 63 A6 57 BF 09 F4 09 3F ...
TA260 002:948.404   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:951.020 - 2.634ms returns 0x27C
TA260 002:951.028 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:951.036   Data:  A2 73 DE 3E CC BE 66 BF 79 BE DD 3E 45 EA 66 BF ...
TA260 002:951.044   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:952.928 - 1.899ms returns 0x184
TA260 002:952.934 JLINK_HasError()
TA260 002:952.939 JLINK_WriteReg(R0, 0x08009800)
TA260 002:952.943 - 0.004ms returns 0
TA260 002:952.947 JLINK_WriteReg(R1, 0x00000400)
TA260 002:952.950 - 0.003ms returns 0
TA260 002:952.954 JLINK_WriteReg(R2, 0x20000184)
TA260 002:952.958 - 0.003ms returns 0
TA260 002:952.962 JLINK_WriteReg(R3, 0x00000000)
TA260 002:952.965 - 0.003ms returns 0
TA260 002:952.970 JLINK_WriteReg(R4, 0x00000000)
TA260 002:952.973 - 0.003ms returns 0
TA260 002:952.977 JLINK_WriteReg(R5, 0x00000000)
TA260 002:952.980 - 0.003ms returns 0
TA260 002:952.985 JLINK_WriteReg(R6, 0x00000000)
TA260 002:952.988 - 0.003ms returns 0
TA260 002:952.992 JLINK_WriteReg(R7, 0x00000000)
TA260 002:952.996 - 0.003ms returns 0
TA260 002:953.000 JLINK_WriteReg(R8, 0x00000000)
TA260 002:953.003 - 0.003ms returns 0
TA260 002:953.007 JLINK_WriteReg(R9, 0x20000180)
TA260 002:953.010 - 0.003ms returns 0
TA260 002:953.014 JLINK_WriteReg(R10, 0x00000000)
TA260 002:953.018 - 0.003ms returns 0
TA260 002:953.022 JLINK_WriteReg(R11, 0x00000000)
TA260 002:953.025 - 0.003ms returns 0
TA260 002:953.029 JLINK_WriteReg(R12, 0x00000000)
TA260 002:953.032 - 0.003ms returns 0
TA260 002:953.036 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:953.040 - 0.003ms returns 0
TA260 002:953.044 JLINK_WriteReg(R14, 0x20000001)
TA260 002:953.048 - 0.003ms returns 0
TA260 002:953.052 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:953.055 - 0.003ms returns 0
TA260 002:953.059 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:953.062 - 0.003ms returns 0
TA260 002:953.067 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:953.070 - 0.003ms returns 0
TA260 002:953.074 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:953.077 - 0.003ms returns 0
TA260 002:953.082 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:953.085 - 0.003ms returns 0
TA260 002:953.090 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:953.094 - 0.004ms returns 0x00000032
TA260 002:953.098 JLINK_Go()
TA260 002:953.105   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:955.838 - 2.740ms 
TA260 002:955.854 JLINK_IsHalted()
TA260 002:956.316 - 0.461ms returns FALSE
TA260 002:956.322 JLINK_HasError()
TA260 002:960.110 JLINK_IsHalted()
TA260 002:962.465   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:962.941 - 2.830ms returns TRUE
TA260 002:962.952 JLINK_ReadReg(R15 (PC))
TA260 002:962.957 - 0.005ms returns 0x20000000
TA260 002:962.990 JLINK_ClrBPEx(BPHandle = 0x00000032)
TA260 002:962.995 - 0.005ms returns 0x00
TA260 002:963.000 JLINK_ReadReg(R0)
TA260 002:963.004 - 0.003ms returns 0x00000000
TA260 002:963.326 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:963.333   Data:  BE 46 6E BF A0 31 BB 3E 6D 6B 6E BF 6E 76 BA 3E ...
TA260 002:963.344   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:965.936 - 2.610ms returns 0x27C
TA260 002:965.954 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:965.958   Data:  22 36 80 3E 10 F1 77 BF E1 E6 7E 3E 04 0A 78 BF ...
TA260 002:965.970   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:967.943 - 1.988ms returns 0x184
TA260 002:967.958 JLINK_HasError()
TA260 002:967.964 JLINK_WriteReg(R0, 0x08009C00)
TA260 002:967.969 - 0.005ms returns 0
TA260 002:967.973 JLINK_WriteReg(R1, 0x00000400)
TA260 002:967.977 - 0.003ms returns 0
TA260 002:967.981 JLINK_WriteReg(R2, 0x20000184)
TA260 002:967.984 - 0.003ms returns 0
TA260 002:967.988 JLINK_WriteReg(R3, 0x00000000)
TA260 002:967.991 - 0.003ms returns 0
TA260 002:967.996 JLINK_WriteReg(R4, 0x00000000)
TA260 002:967.999 - 0.003ms returns 0
TA260 002:968.003 JLINK_WriteReg(R5, 0x00000000)
TA260 002:968.006 - 0.003ms returns 0
TA260 002:968.010 JLINK_WriteReg(R6, 0x00000000)
TA260 002:968.014 - 0.003ms returns 0
TA260 002:968.018 JLINK_WriteReg(R7, 0x00000000)
TA260 002:968.021 - 0.003ms returns 0
TA260 002:968.026 JLINK_WriteReg(R8, 0x00000000)
TA260 002:968.033 - 0.007ms returns 0
TA260 002:968.038 JLINK_WriteReg(R9, 0x20000180)
TA260 002:968.042 - 0.003ms returns 0
TA260 002:968.046 JLINK_WriteReg(R10, 0x00000000)
TA260 002:968.049 - 0.003ms returns 0
TA260 002:968.053 JLINK_WriteReg(R11, 0x00000000)
TA260 002:968.057 - 0.003ms returns 0
TA260 002:968.071 JLINK_WriteReg(R12, 0x00000000)
TA260 002:968.075 - 0.003ms returns 0
TA260 002:968.079 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:968.083 - 0.003ms returns 0
TA260 002:968.087 JLINK_WriteReg(R14, 0x20000001)
TA260 002:968.090 - 0.003ms returns 0
TA260 002:968.094 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:968.098 - 0.003ms returns 0
TA260 002:968.102 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:968.105 - 0.003ms returns 0
TA260 002:968.109 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:968.113 - 0.003ms returns 0
TA260 002:968.117 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:968.120 - 0.003ms returns 0
TA260 002:968.149 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:968.153 - 0.004ms returns 0
TA260 002:968.158 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:968.162 - 0.004ms returns 0x00000033
TA260 002:968.166 JLINK_Go()
TA260 002:968.176   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:970.972 - 2.805ms 
TA260 002:970.980 JLINK_IsHalted()
TA260 002:971.426 - 0.445ms returns FALSE
TA260 002:971.431 JLINK_HasError()
TA260 002:974.619 JLINK_IsHalted()
TA260 002:975.084 - 0.465ms returns FALSE
TA260 002:975.098 JLINK_HasError()
TA260 002:979.124 JLINK_IsHalted()
TA260 002:981.411   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:981.930 - 2.805ms returns TRUE
TA260 002:981.937 JLINK_ReadReg(R15 (PC))
TA260 002:981.943 - 0.005ms returns 0x20000000
TA260 002:981.947 JLINK_ClrBPEx(BPHandle = 0x00000033)
TA260 002:981.951 - 0.003ms returns 0x00
TA260 002:981.955 JLINK_ReadReg(R0)
TA260 002:981.959 - 0.003ms returns 0x00000000
TA260 002:982.265 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:982.272   Data:  31 F5 7B BF 98 40 35 3E E9 06 7C BF C4 B4 33 3E ...
TA260 002:982.283   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:984.892 - 2.626ms returns 0x27C
TA260 002:984.906 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:984.910   Data:  62 5A 68 3D 18 9C 7F BF 68 14 62 3D 91 A1 7F BF ...
TA260 002:984.919   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:986.770 - 1.863ms returns 0x184
TA260 002:986.786 JLINK_HasError()
TA260 002:986.792 JLINK_WriteReg(R0, 0x0800A000)
TA260 002:986.798 - 0.005ms returns 0
TA260 002:986.802 JLINK_WriteReg(R1, 0x00000400)
TA260 002:986.806 - 0.003ms returns 0
TA260 002:986.810 JLINK_WriteReg(R2, 0x20000184)
TA260 002:986.813 - 0.003ms returns 0
TA260 002:986.817 JLINK_WriteReg(R3, 0x00000000)
TA260 002:986.820 - 0.003ms returns 0
TA260 002:986.824 JLINK_WriteReg(R4, 0x00000000)
TA260 002:986.828 - 0.003ms returns 0
TA260 002:986.832 JLINK_WriteReg(R5, 0x00000000)
TA260 002:986.835 - 0.003ms returns 0
TA260 002:986.839 JLINK_WriteReg(R6, 0x00000000)
TA260 002:986.843 - 0.003ms returns 0
TA260 002:986.847 JLINK_WriteReg(R7, 0x00000000)
TA260 002:986.850 - 0.003ms returns 0
TA260 002:986.854 JLINK_WriteReg(R8, 0x00000000)
TA260 002:986.858 - 0.003ms returns 0
TA260 002:986.862 JLINK_WriteReg(R9, 0x20000180)
TA260 002:986.865 - 0.003ms returns 0
TA260 002:986.869 JLINK_WriteReg(R10, 0x00000000)
TA260 002:986.873 - 0.003ms returns 0
TA260 002:986.877 JLINK_WriteReg(R11, 0x00000000)
TA260 002:986.880 - 0.003ms returns 0
TA260 002:986.884 JLINK_WriteReg(R12, 0x00000000)
TA260 002:986.888 - 0.003ms returns 0
TA260 002:986.892 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:986.896 - 0.004ms returns 0
TA260 002:986.900 JLINK_WriteReg(R14, 0x20000001)
TA260 002:986.903 - 0.003ms returns 0
TA260 002:986.907 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:986.911 - 0.003ms returns 0
TA260 002:986.915 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:986.922 - 0.007ms returns 0
TA260 002:986.927 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:986.930 - 0.003ms returns 0
TA260 002:986.934 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:986.943 - 0.009ms returns 0
TA260 002:986.947 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:986.951 - 0.003ms returns 0
TA260 002:986.955 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:986.960 - 0.004ms returns 0x00000034
TA260 002:986.964 JLINK_Go()
TA260 002:986.972   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:989.739 - 2.774ms 
TA260 002:989.755 JLINK_IsHalted()
TA260 002:990.223 - 0.467ms returns FALSE
TA260 002:990.232 JLINK_HasError()
TA260 002:992.627 JLINK_IsHalted()
TA260 002:993.114 - 0.486ms returns FALSE
TA260 002:993.120 JLINK_HasError()
TA260 002:995.630 JLINK_IsHalted()
TA260 002:997.972   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:998.565 - 2.934ms returns TRUE
TA260 002:998.572 JLINK_ReadReg(R15 (PC))
TA260 002:998.577 - 0.005ms returns 0x20000000
TA260 002:998.582 JLINK_ClrBPEx(BPHandle = 0x00000034)
TA260 002:998.586 - 0.004ms returns 0x00
TA260 002:998.590 JLINK_ReadReg(R0)
TA260 002:998.594 - 0.003ms returns 0x00000000
TA260 002:998.950 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:998.959   Data:  E6 F4 7F BF B6 C9 96 BC F8 F2 7F BF 1C 5A A3 BC ...
TA260 002:998.970   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:001.577 - 2.627ms returns 0x27C
TA260 003:001.588 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:001.592   Data:  8B 7A 0E BE 74 74 7D BF B7 08 10 BE 3D 66 7D BF ...
TA260 003:001.601   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:003.485 - 1.896ms returns 0x184
TA260 003:003.491 JLINK_HasError()
TA260 003:003.496 JLINK_WriteReg(R0, 0x0800A400)
TA260 003:003.501 - 0.004ms returns 0
TA260 003:003.505 JLINK_WriteReg(R1, 0x00000400)
TA260 003:003.508 - 0.003ms returns 0
TA260 003:003.512 JLINK_WriteReg(R2, 0x20000184)
TA260 003:003.516 - 0.003ms returns 0
TA260 003:003.520 JLINK_WriteReg(R3, 0x00000000)
TA260 003:003.523 - 0.003ms returns 0
TA260 003:003.527 JLINK_WriteReg(R4, 0x00000000)
TA260 003:003.530 - 0.003ms returns 0
TA260 003:003.534 JLINK_WriteReg(R5, 0x00000000)
TA260 003:003.538 - 0.003ms returns 0
TA260 003:003.542 JLINK_WriteReg(R6, 0x00000000)
TA260 003:003.545 - 0.003ms returns 0
TA260 003:003.549 JLINK_WriteReg(R7, 0x00000000)
TA260 003:003.552 - 0.003ms returns 0
TA260 003:003.556 JLINK_WriteReg(R8, 0x00000000)
TA260 003:003.560 - 0.004ms returns 0
TA260 003:003.564 JLINK_WriteReg(R9, 0x20000180)
TA260 003:003.568 - 0.003ms returns 0
TA260 003:003.572 JLINK_WriteReg(R10, 0x00000000)
TA260 003:003.575 - 0.003ms returns 0
TA260 003:003.579 JLINK_WriteReg(R11, 0x00000000)
TA260 003:003.582 - 0.003ms returns 0
TA260 003:003.587 JLINK_WriteReg(R12, 0x00000000)
TA260 003:003.590 - 0.003ms returns 0
TA260 003:003.594 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:003.598 - 0.003ms returns 0
TA260 003:003.602 JLINK_WriteReg(R14, 0x20000001)
TA260 003:003.605 - 0.003ms returns 0
TA260 003:003.609 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:003.613 - 0.003ms returns 0
TA260 003:003.617 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:003.620 - 0.003ms returns 0
TA260 003:003.624 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:003.628 - 0.003ms returns 0
TA260 003:003.632 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:003.635 - 0.003ms returns 0
TA260 003:003.639 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:003.642 - 0.003ms returns 0
TA260 003:003.647 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:003.651 - 0.004ms returns 0x00000035
TA260 003:003.655 JLINK_Go()
TA260 003:003.663   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:006.415 - 2.758ms 
TA260 003:006.436 JLINK_IsHalted()
TA260 003:006.922 - 0.485ms returns FALSE
TA260 003:006.938 JLINK_HasError()
TA260 003:009.142 JLINK_IsHalted()
TA260 003:009.626 - 0.483ms returns FALSE
TA260 003:009.633 JLINK_HasError()
TA260 003:011.645 JLINK_IsHalted()
TA260 003:014.029   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:014.556 - 2.910ms returns TRUE
TA260 003:014.563 JLINK_ReadReg(R15 (PC))
TA260 003:014.683 - 0.005ms returns 0x20000000
TA260 003:014.693 JLINK_ClrBPEx(BPHandle = 0x00000035)
TA260 003:014.702 - 0.009ms returns 0x00
TA260 003:014.744 JLINK_ReadReg(R0)
TA260 003:014.758 - 0.013ms returns 0x00000000
TA260 003:015.194 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:015.203   Data:  84 1E 7A BF 97 39 5A BE 04 09 7A BF 6A C2 5B BE ...
TA260 003:015.216   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:017.768 - 2.573ms returns 0x27C
TA260 003:017.790 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:017.795   Data:  FE C8 A8 BE 57 8F 71 BF C4 86 A9 BE FB 6D 71 BF ...
TA260 003:017.806   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:019.763 - 1.972ms returns 0x184
TA260 003:019.779 JLINK_HasError()
TA260 003:019.784 JLINK_WriteReg(R0, 0x0800A800)
TA260 003:019.789 - 0.005ms returns 0
TA260 003:019.794 JLINK_WriteReg(R1, 0x00000400)
TA260 003:019.798 - 0.003ms returns 0
TA260 003:019.802 JLINK_WriteReg(R2, 0x20000184)
TA260 003:019.805 - 0.003ms returns 0
TA260 003:019.809 JLINK_WriteReg(R3, 0x00000000)
TA260 003:019.813 - 0.003ms returns 0
TA260 003:019.816 JLINK_WriteReg(R4, 0x00000000)
TA260 003:019.820 - 0.003ms returns 0
TA260 003:019.824 JLINK_WriteReg(R5, 0x00000000)
TA260 003:019.827 - 0.003ms returns 0
TA260 003:019.831 JLINK_WriteReg(R6, 0x00000000)
TA260 003:019.836 - 0.004ms returns 0
TA260 003:019.840 JLINK_WriteReg(R7, 0x00000000)
TA260 003:019.844 - 0.003ms returns 0
TA260 003:019.848 JLINK_WriteReg(R8, 0x00000000)
TA260 003:019.851 - 0.003ms returns 0
TA260 003:019.855 JLINK_WriteReg(R9, 0x20000180)
TA260 003:019.858 - 0.003ms returns 0
TA260 003:019.863 JLINK_WriteReg(R10, 0x00000000)
TA260 003:019.866 - 0.003ms returns 0
TA260 003:019.870 JLINK_WriteReg(R11, 0x00000000)
TA260 003:019.874 - 0.003ms returns 0
TA260 003:019.878 JLINK_WriteReg(R12, 0x00000000)
TA260 003:019.881 - 0.003ms returns 0
TA260 003:019.885 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:019.889 - 0.004ms returns 0
TA260 003:019.893 JLINK_WriteReg(R14, 0x20000001)
TA260 003:019.897 - 0.003ms returns 0
TA260 003:019.901 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:019.920 - 0.019ms returns 0
TA260 003:019.925 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:019.928 - 0.003ms returns 0
TA260 003:019.932 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:019.936 - 0.003ms returns 0
TA260 003:019.940 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:019.943 - 0.003ms returns 0
TA260 003:019.947 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:019.951 - 0.003ms returns 0
TA260 003:019.956 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:019.960 - 0.004ms returns 0x00000036
TA260 003:019.964 JLINK_Go()
TA260 003:019.973   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:022.746 - 2.782ms 
TA260 003:022.760 JLINK_IsHalted()
TA260 003:023.258 - 0.497ms returns FALSE
TA260 003:023.263 JLINK_HasError()
TA260 003:025.150 JLINK_IsHalted()
TA260 003:025.662 - 0.510ms returns FALSE
TA260 003:025.675 JLINK_HasError()
TA260 003:027.151 JLINK_IsHalted()
TA260 003:029.502   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:029.980 - 2.829ms returns TRUE
TA260 003:029.990 JLINK_ReadReg(R15 (PC))
TA260 003:029.996 - 0.005ms returns 0x20000000
TA260 003:030.000 JLINK_ClrBPEx(BPHandle = 0x00000036)
TA260 003:030.004 - 0.004ms returns 0x00
TA260 003:030.008 JLINK_ReadReg(R0)
TA260 003:030.012 - 0.003ms returns 0x00000000
TA260 003:030.355 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:030.362   Data:  7B AB 6A BF 8B 9B CC BE 3C 83 6A BF CA 53 CD BE ...
TA260 003:030.372   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:033.023 - 2.668ms returns 0x27C
TA260 003:033.036 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:033.040   Data:  1C EC 01 BF C7 61 5C BF B1 42 02 BF 8E 2E 5C BF ...
TA260 003:033.049   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:034.932 - 1.896ms returns 0x184
TA260 003:034.941 JLINK_HasError()
TA260 003:034.946 JLINK_WriteReg(R0, 0x0800AC00)
TA260 003:034.951 - 0.004ms returns 0
TA260 003:034.956 JLINK_WriteReg(R1, 0x00000400)
TA260 003:034.959 - 0.003ms returns 0
TA260 003:034.964 JLINK_WriteReg(R2, 0x20000184)
TA260 003:034.967 - 0.003ms returns 0
TA260 003:034.977 JLINK_WriteReg(R3, 0x00000000)
TA260 003:034.980 - 0.003ms returns 0
TA260 003:034.984 JLINK_WriteReg(R4, 0x00000000)
TA260 003:034.988 - 0.003ms returns 0
TA260 003:034.992 JLINK_WriteReg(R5, 0x00000000)
TA260 003:034.995 - 0.003ms returns 0
TA260 003:034.999 JLINK_WriteReg(R6, 0x00000000)
TA260 003:035.002 - 0.003ms returns 0
TA260 003:035.007 JLINK_WriteReg(R7, 0x00000000)
TA260 003:035.010 - 0.003ms returns 0
TA260 003:035.014 JLINK_WriteReg(R8, 0x00000000)
TA260 003:035.018 - 0.003ms returns 0
TA260 003:035.022 JLINK_WriteReg(R9, 0x20000180)
TA260 003:035.025 - 0.003ms returns 0
TA260 003:035.029 JLINK_WriteReg(R10, 0x00000000)
TA260 003:035.033 - 0.003ms returns 0
TA260 003:035.037 JLINK_WriteReg(R11, 0x00000000)
TA260 003:035.041 - 0.003ms returns 0
TA260 003:035.045 JLINK_WriteReg(R12, 0x00000000)
TA260 003:035.048 - 0.003ms returns 0
TA260 003:035.053 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:035.058 - 0.005ms returns 0
TA260 003:035.062 JLINK_WriteReg(R14, 0x20000001)
TA260 003:035.065 - 0.003ms returns 0
TA260 003:035.069 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:035.073 - 0.003ms returns 0
TA260 003:035.077 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:035.080 - 0.003ms returns 0
TA260 003:035.085 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:035.088 - 0.003ms returns 0
TA260 003:035.092 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:035.096 - 0.003ms returns 0
TA260 003:035.100 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:035.103 - 0.003ms returns 0
TA260 003:035.108 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:035.112 - 0.004ms returns 0x00000037
TA260 003:035.124 JLINK_Go()
TA260 003:035.132   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:037.877 - 2.752ms 
TA260 003:037.896 JLINK_IsHalted()
TA260 003:038.382 - 0.486ms returns FALSE
TA260 003:038.388 JLINK_HasError()
TA260 003:039.662 JLINK_IsHalted()
TA260 003:040.180 - 0.517ms returns FALSE
TA260 003:040.192 JLINK_HasError()
TA260 003:041.663 JLINK_IsHalted()
TA260 003:042.142 - 0.479ms returns FALSE
TA260 003:042.148 JLINK_HasError()
TA260 003:043.664 JLINK_IsHalted()
TA260 003:046.025   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:046.542 - 2.877ms returns TRUE
TA260 003:046.653 JLINK_ReadReg(R15 (PC))
TA260 003:046.659 - 0.006ms returns 0x20000000
TA260 003:046.664 JLINK_ClrBPEx(BPHandle = 0x00000037)
TA260 003:046.703 - 0.039ms returns 0x00
TA260 003:046.708 JLINK_ReadReg(R0)
TA260 003:046.712 - 0.003ms returns 0x00000000
TA260 003:047.099 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:047.107   Data:  C6 33 52 BF B0 1E 12 BF 54 FA 51 BF 30 71 12 BF ...
TA260 003:047.118   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:049.702 - 2.603ms returns 0x27C
TA260 003:049.709 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:049.712   Data:  8E 75 2A BF 1B BC 3E BF 82 C0 2A BF FF 78 3E BF ...
TA260 003:049.719   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:051.575 - 1.866ms returns 0x184
TA260 003:051.582 JLINK_HasError()
TA260 003:051.587 JLINK_WriteReg(R0, 0x0800B000)
TA260 003:051.591 - 0.004ms returns 0
TA260 003:051.596 JLINK_WriteReg(R1, 0x00000400)
TA260 003:051.599 - 0.003ms returns 0
TA260 003:051.603 JLINK_WriteReg(R2, 0x20000184)
TA260 003:051.607 - 0.003ms returns 0
TA260 003:051.611 JLINK_WriteReg(R3, 0x00000000)
TA260 003:051.614 - 0.003ms returns 0
TA260 003:051.618 JLINK_WriteReg(R4, 0x00000000)
TA260 003:051.622 - 0.003ms returns 0
TA260 003:051.626 JLINK_WriteReg(R5, 0x00000000)
TA260 003:051.629 - 0.003ms returns 0
TA260 003:051.633 JLINK_WriteReg(R6, 0x00000000)
TA260 003:051.636 - 0.003ms returns 0
TA260 003:051.640 JLINK_WriteReg(R7, 0x00000000)
TA260 003:051.644 - 0.003ms returns 0
TA260 003:051.648 JLINK_WriteReg(R8, 0x00000000)
TA260 003:051.651 - 0.003ms returns 0
TA260 003:051.655 JLINK_WriteReg(R9, 0x20000180)
TA260 003:051.658 - 0.003ms returns 0
TA260 003:051.662 JLINK_WriteReg(R10, 0x00000000)
TA260 003:051.666 - 0.003ms returns 0
TA260 003:051.670 JLINK_WriteReg(R11, 0x00000000)
TA260 003:051.677 - 0.006ms returns 0
TA260 003:051.682 JLINK_WriteReg(R12, 0x00000000)
TA260 003:051.685 - 0.003ms returns 0
TA260 003:051.689 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:051.693 - 0.003ms returns 0
TA260 003:051.697 JLINK_WriteReg(R14, 0x20000001)
TA260 003:051.701 - 0.003ms returns 0
TA260 003:051.705 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:051.708 - 0.003ms returns 0
TA260 003:051.712 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:051.716 - 0.003ms returns 0
TA260 003:051.720 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:051.723 - 0.003ms returns 0
TA260 003:051.727 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:051.731 - 0.003ms returns 0
TA260 003:051.735 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:051.738 - 0.003ms returns 0
TA260 003:051.743 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:051.747 - 0.004ms returns 0x00000038
TA260 003:051.751 JLINK_Go()
TA260 003:051.758   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:054.485 - 2.734ms 
TA260 003:054.494 JLINK_IsHalted()
TA260 003:054.966 - 0.471ms returns FALSE
TA260 003:054.979 JLINK_HasError()
TA260 003:056.175 JLINK_IsHalted()
TA260 003:056.640 - 0.464ms returns FALSE
TA260 003:056.654 JLINK_HasError()
TA260 003:058.170 JLINK_IsHalted()
TA260 003:058.678 - 0.507ms returns FALSE
TA260 003:058.684 JLINK_HasError()
TA260 003:060.680 JLINK_IsHalted()
TA260 003:063.005   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:063.519 - 2.839ms returns TRUE
TA260 003:063.525 JLINK_ReadReg(R15 (PC))
TA260 003:063.530 - 0.005ms returns 0x20000000
TA260 003:063.535 JLINK_ClrBPEx(BPHandle = 0x00000038)
TA260 003:063.538 - 0.003ms returns 0x00
TA260 003:063.543 JLINK_ReadReg(R0)
TA260 003:063.546 - 0.003ms returns 0x00000000
TA260 003:063.931 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:063.938   Data:  1D A8 31 BF 16 52 38 BF AD 5F 31 BF CB 97 38 BF ...
TA260 003:063.950   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:066.573 - 2.642ms returns 0x27C
TA260 003:066.595 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:066.599   Data:  08 72 4C BF 00 C2 19 BF 79 AE 4C BF 94 71 19 BF ...
TA260 003:066.611   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:068.491 - 1.895ms returns 0x184
TA260 003:068.507 JLINK_HasError()
TA260 003:068.542 JLINK_WriteReg(R0, 0x0800B400)
TA260 003:068.547 - 0.006ms returns 0
TA260 003:068.552 JLINK_WriteReg(R1, 0x00000400)
TA260 003:068.555 - 0.003ms returns 0
TA260 003:068.560 JLINK_WriteReg(R2, 0x20000184)
TA260 003:068.563 - 0.003ms returns 0
TA260 003:068.567 JLINK_WriteReg(R3, 0x00000000)
TA260 003:068.570 - 0.003ms returns 0
TA260 003:068.574 JLINK_WriteReg(R4, 0x00000000)
TA260 003:068.578 - 0.003ms returns 0
TA260 003:068.582 JLINK_WriteReg(R5, 0x00000000)
TA260 003:068.585 - 0.003ms returns 0
TA260 003:068.589 JLINK_WriteReg(R6, 0x00000000)
TA260 003:068.592 - 0.003ms returns 0
TA260 003:068.596 JLINK_WriteReg(R7, 0x00000000)
TA260 003:068.600 - 0.003ms returns 0
TA260 003:068.604 JLINK_WriteReg(R8, 0x00000000)
TA260 003:068.607 - 0.003ms returns 0
TA260 003:068.611 JLINK_WriteReg(R9, 0x20000180)
TA260 003:068.615 - 0.003ms returns 0
TA260 003:068.619 JLINK_WriteReg(R10, 0x00000000)
TA260 003:068.622 - 0.003ms returns 0
TA260 003:068.626 JLINK_WriteReg(R11, 0x00000000)
TA260 003:068.630 - 0.003ms returns 0
TA260 003:068.634 JLINK_WriteReg(R12, 0x00000000)
TA260 003:068.637 - 0.003ms returns 0
TA260 003:068.641 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:068.645 - 0.003ms returns 0
TA260 003:068.649 JLINK_WriteReg(R14, 0x20000001)
TA260 003:068.653 - 0.003ms returns 0
TA260 003:068.657 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:068.660 - 0.003ms returns 0
TA260 003:068.664 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:068.668 - 0.003ms returns 0
TA260 003:068.672 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:068.675 - 0.003ms returns 0
TA260 003:068.679 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:068.682 - 0.003ms returns 0
TA260 003:068.686 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:068.690 - 0.003ms returns 0
TA260 003:068.698 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:068.704 - 0.006ms returns 0x00000039
TA260 003:068.708 JLINK_Go()
TA260 003:068.716   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:071.472 - 2.763ms 
TA260 003:071.482 JLINK_IsHalted()
TA260 003:071.950 - 0.467ms returns FALSE
TA260 003:071.956 JLINK_HasError()
TA260 003:073.184 JLINK_IsHalted()
TA260 003:073.701 - 0.516ms returns FALSE
TA260 003:073.707 JLINK_HasError()
TA260 003:075.189 JLINK_IsHalted()
TA260 003:075.638 - 0.448ms returns FALSE
TA260 003:075.652 JLINK_HasError()
TA260 003:077.187 JLINK_IsHalted()
TA260 003:079.456   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:079.938 - 2.750ms returns TRUE
TA260 003:079.948 JLINK_ReadReg(R15 (PC))
TA260 003:079.953 - 0.005ms returns 0x20000000
TA260 003:079.958 JLINK_ClrBPEx(BPHandle = 0x00000039)
TA260 003:079.962 - 0.004ms returns 0x00
TA260 003:079.966 JLINK_ReadReg(R0)
TA260 003:079.970 - 0.003ms returns 0x00000000
TA260 003:080.308 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:080.315   Data:  AD 48 0A BF 26 70 57 BF 09 F4 09 BF 63 A6 57 BF ...
TA260 003:080.325   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:082.941 - 2.632ms returns 0x27C
TA260 003:082.950 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:082.954   Data:  30 93 66 BF 79 BE DD BE CC BE 66 BF 2E 09 DD BE ...
TA260 003:082.963   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:084.764 - 1.813ms returns 0x184
TA260 003:084.778 JLINK_HasError()
TA260 003:084.783 JLINK_WriteReg(R0, 0x0800B800)
TA260 003:084.788 - 0.005ms returns 0
TA260 003:084.792 JLINK_WriteReg(R1, 0x00000400)
TA260 003:084.795 - 0.003ms returns 0
TA260 003:084.800 JLINK_WriteReg(R2, 0x20000184)
TA260 003:084.803 - 0.003ms returns 0
TA260 003:084.807 JLINK_WriteReg(R3, 0x00000000)
TA260 003:084.811 - 0.003ms returns 0
TA260 003:084.815 JLINK_WriteReg(R4, 0x00000000)
TA260 003:084.818 - 0.003ms returns 0
TA260 003:084.822 JLINK_WriteReg(R5, 0x00000000)
TA260 003:084.825 - 0.003ms returns 0
TA260 003:084.829 JLINK_WriteReg(R6, 0x00000000)
TA260 003:084.833 - 0.003ms returns 0
TA260 003:084.837 JLINK_WriteReg(R7, 0x00000000)
TA260 003:084.840 - 0.003ms returns 0
TA260 003:084.845 JLINK_WriteReg(R8, 0x00000000)
TA260 003:084.848 - 0.004ms returns 0
TA260 003:084.852 JLINK_WriteReg(R9, 0x20000180)
TA260 003:084.856 - 0.003ms returns 0
TA260 003:084.860 JLINK_WriteReg(R10, 0x00000000)
TA260 003:084.863 - 0.003ms returns 0
TA260 003:084.867 JLINK_WriteReg(R11, 0x00000000)
TA260 003:084.871 - 0.003ms returns 0
TA260 003:084.875 JLINK_WriteReg(R12, 0x00000000)
TA260 003:084.878 - 0.003ms returns 0
TA260 003:084.882 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:084.886 - 0.003ms returns 0
TA260 003:084.890 JLINK_WriteReg(R14, 0x20000001)
TA260 003:084.894 - 0.003ms returns 0
TA260 003:084.905 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:084.909 - 0.003ms returns 0
TA260 003:084.913 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:084.916 - 0.003ms returns 0
TA260 003:084.920 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:084.924 - 0.003ms returns 0
TA260 003:084.928 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:084.931 - 0.003ms returns 0
TA260 003:084.935 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:084.938 - 0.003ms returns 0
TA260 003:084.943 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:084.947 - 0.004ms returns 0x0000003A
TA260 003:084.952 JLINK_Go()
TA260 003:084.960   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:087.669 - 2.717ms 
TA260 003:087.689 JLINK_IsHalted()
TA260 003:088.190 - 0.500ms returns FALSE
TA260 003:088.196 JLINK_HasError()
TA260 003:090.197 JLINK_IsHalted()
TA260 003:090.665 - 0.467ms returns FALSE
TA260 003:090.674 JLINK_HasError()
TA260 003:092.194 JLINK_IsHalted()
TA260 003:094.462   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:094.997 - 2.801ms returns TRUE
TA260 003:095.016 JLINK_ReadReg(R15 (PC))
TA260 003:095.022 - 0.006ms returns 0x20000000
TA260 003:095.079 JLINK_ClrBPEx(BPHandle = 0x0000003A)
TA260 003:095.085 - 0.006ms returns 0x00
TA260 003:095.093 JLINK_ReadReg(R0)
TA260 003:095.099 - 0.005ms returns 0x00000000
TA260 003:095.491 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:095.500   Data:  A0 31 BB BE BE 46 6E BF 6E 76 BA BE 6D 6B 6E BF ...
TA260 003:095.512   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:098.118 - 2.626ms returns 0x27C
TA260 003:098.131 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:098.135   Data:  F7 D7 77 BF E1 E6 7E BE 10 F1 77 BF 56 61 7D BE ...
TA260 003:098.145   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:100.025 - 1.893ms returns 0x184
TA260 003:100.039 JLINK_HasError()
TA260 003:100.044 JLINK_WriteReg(R0, 0x0800BC00)
TA260 003:100.049 - 0.005ms returns 0
TA260 003:100.054 JLINK_WriteReg(R1, 0x00000400)
TA260 003:100.057 - 0.003ms returns 0
TA260 003:100.061 JLINK_WriteReg(R2, 0x20000184)
TA260 003:100.064 - 0.003ms returns 0
TA260 003:100.068 JLINK_WriteReg(R3, 0x00000000)
TA260 003:100.072 - 0.003ms returns 0
TA260 003:100.076 JLINK_WriteReg(R4, 0x00000000)
TA260 003:100.080 - 0.003ms returns 0
TA260 003:100.084 JLINK_WriteReg(R5, 0x00000000)
TA260 003:100.087 - 0.003ms returns 0
TA260 003:100.091 JLINK_WriteReg(R6, 0x00000000)
TA260 003:100.094 - 0.003ms returns 0
TA260 003:100.098 JLINK_WriteReg(R7, 0x00000000)
TA260 003:100.102 - 0.003ms returns 0
TA260 003:100.106 JLINK_WriteReg(R8, 0x00000000)
TA260 003:100.109 - 0.003ms returns 0
TA260 003:100.113 JLINK_WriteReg(R9, 0x20000180)
TA260 003:100.117 - 0.003ms returns 0
TA260 003:100.121 JLINK_WriteReg(R10, 0x00000000)
TA260 003:100.124 - 0.003ms returns 0
TA260 003:100.129 JLINK_WriteReg(R11, 0x00000000)
TA260 003:100.132 - 0.003ms returns 0
TA260 003:100.136 JLINK_WriteReg(R12, 0x00000000)
TA260 003:100.139 - 0.003ms returns 0
TA260 003:100.143 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:100.147 - 0.003ms returns 0
TA260 003:100.151 JLINK_WriteReg(R14, 0x20000001)
TA260 003:100.154 - 0.003ms returns 0
TA260 003:100.158 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:100.162 - 0.003ms returns 0
TA260 003:100.166 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:100.170 - 0.003ms returns 0
TA260 003:100.174 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:100.177 - 0.003ms returns 0
TA260 003:100.181 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:100.184 - 0.003ms returns 0
TA260 003:100.188 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:100.192 - 0.003ms returns 0
TA260 003:100.197 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:100.201 - 0.004ms returns 0x0000003B
TA260 003:100.205 JLINK_Go()
TA260 003:100.213   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:102.950 - 2.744ms 
TA260 003:102.963 JLINK_IsHalted()
TA260 003:103.414 - 0.450ms returns FALSE
TA260 003:103.419 JLINK_HasError()
TA260 003:104.706 JLINK_IsHalted()
TA260 003:105.213 - 0.507ms returns FALSE
TA260 003:105.225 JLINK_HasError()
TA260 003:106.706 JLINK_IsHalted()
TA260 003:107.226 - 0.519ms returns FALSE
TA260 003:107.232 JLINK_HasError()
TA260 003:109.208 JLINK_IsHalted()
TA260 003:111.546   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:112.019 - 2.810ms returns TRUE
TA260 003:112.025 JLINK_ReadReg(R15 (PC))
TA260 003:112.030 - 0.004ms returns 0x20000000
TA260 003:112.034 JLINK_ClrBPEx(BPHandle = 0x0000003B)
TA260 003:112.038 - 0.003ms returns 0x00
TA260 003:112.042 JLINK_ReadReg(R0)
TA260 003:112.046 - 0.003ms returns 0x00000000
TA260 003:112.392 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:112.400   Data:  98 40 35 BE 31 F5 7B BF C4 B4 33 BE E9 06 7C BF ...
TA260 003:112.410   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:114.993 - 2.601ms returns 0x27C
TA260 003:115.012 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:115.016   Data:  78 96 7F BF 68 14 62 BD 18 9C 7F BF 4C CE 5B BD ...
TA260 003:115.027   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:116.939 - 1.926ms returns 0x184
TA260 003:116.956 JLINK_HasError()
TA260 003:116.962 JLINK_WriteReg(R0, 0x0800C000)
TA260 003:116.968 - 0.005ms returns 0
TA260 003:116.972 JLINK_WriteReg(R1, 0x00000400)
TA260 003:116.975 - 0.003ms returns 0
TA260 003:116.984 JLINK_WriteReg(R2, 0x20000184)
TA260 003:116.989 - 0.005ms returns 0
TA260 003:116.994 JLINK_WriteReg(R3, 0x00000000)
TA260 003:116.997 - 0.003ms returns 0
TA260 003:117.001 JLINK_WriteReg(R4, 0x00000000)
TA260 003:117.004 - 0.003ms returns 0
TA260 003:117.008 JLINK_WriteReg(R5, 0x00000000)
TA260 003:117.012 - 0.003ms returns 0
TA260 003:117.016 JLINK_WriteReg(R6, 0x00000000)
TA260 003:117.019 - 0.003ms returns 0
TA260 003:117.023 JLINK_WriteReg(R7, 0x00000000)
TA260 003:117.026 - 0.003ms returns 0
TA260 003:117.031 JLINK_WriteReg(R8, 0x00000000)
TA260 003:117.034 - 0.003ms returns 0
TA260 003:117.038 JLINK_WriteReg(R9, 0x20000180)
TA260 003:117.042 - 0.003ms returns 0
TA260 003:117.046 JLINK_WriteReg(R10, 0x00000000)
TA260 003:117.049 - 0.003ms returns 0
TA260 003:117.053 JLINK_WriteReg(R11, 0x00000000)
TA260 003:117.056 - 0.003ms returns 0
TA260 003:117.060 JLINK_WriteReg(R12, 0x00000000)
TA260 003:117.064 - 0.003ms returns 0
TA260 003:117.068 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:117.072 - 0.004ms returns 0
TA260 003:117.076 JLINK_WriteReg(R14, 0x20000001)
TA260 003:117.079 - 0.003ms returns 0
TA260 003:117.083 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:117.087 - 0.003ms returns 0
TA260 003:117.091 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:117.094 - 0.003ms returns 0
TA260 003:117.098 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:117.102 - 0.003ms returns 0
TA260 003:117.106 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:117.109 - 0.003ms returns 0
TA260 003:117.113 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:117.117 - 0.003ms returns 0
TA260 003:117.121 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:117.126 - 0.004ms returns 0x0000003C
TA260 003:117.130 JLINK_Go()
TA260 003:117.139   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:119.930 - 2.799ms 
TA260 003:119.950 JLINK_IsHalted()
TA260 003:120.414 - 0.463ms returns FALSE
TA260 003:120.429 JLINK_HasError()
TA260 003:122.716 JLINK_IsHalted()
TA260 003:123.210 - 0.493ms returns FALSE
TA260 003:123.216 JLINK_HasError()
TA260 003:124.717 JLINK_IsHalted()
TA260 003:127.161   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:127.684 - 2.967ms returns TRUE
TA260 003:127.699 JLINK_ReadReg(R15 (PC))
TA260 003:127.705 - 0.005ms returns 0x20000000
TA260 003:127.710 JLINK_ClrBPEx(BPHandle = 0x0000003C)
TA260 003:127.714 - 0.004ms returns 0x00
TA260 003:127.718 JLINK_ReadReg(R0)
TA260 003:127.722 - 0.003ms returns 0x00000000
TA260 003:128.062 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:128.069   Data:  B6 C9 96 3C E6 F4 7F BF 1C 5A A3 3C F8 F2 7F BF ...
TA260 003:128.079   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:130.714 - 2.651ms returns 0x27C
TA260 003:130.725 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:130.729   Data:  85 82 7D BF B7 08 10 3E 74 74 7D BF CC 96 11 3E ...
TA260 003:130.738   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:132.620 - 1.893ms returns 0x184
TA260 003:132.630 JLINK_HasError()
TA260 003:132.636 JLINK_WriteReg(R0, 0x0800C400)
TA260 003:132.642 - 0.005ms returns 0
TA260 003:132.646 JLINK_WriteReg(R1, 0x00000400)
TA260 003:132.649 - 0.003ms returns 0
TA260 003:132.654 JLINK_WriteReg(R2, 0x20000184)
TA260 003:132.657 - 0.003ms returns 0
TA260 003:132.661 JLINK_WriteReg(R3, 0x00000000)
TA260 003:132.664 - 0.003ms returns 0
TA260 003:132.669 JLINK_WriteReg(R4, 0x00000000)
TA260 003:132.672 - 0.003ms returns 0
TA260 003:132.676 JLINK_WriteReg(R5, 0x00000000)
TA260 003:132.680 - 0.003ms returns 0
TA260 003:132.684 JLINK_WriteReg(R6, 0x00000000)
TA260 003:132.687 - 0.003ms returns 0
TA260 003:132.692 JLINK_WriteReg(R7, 0x00000000)
TA260 003:132.697 - 0.004ms returns 0
TA260 003:132.701 JLINK_WriteReg(R8, 0x00000000)
TA260 003:132.704 - 0.003ms returns 0
TA260 003:132.708 JLINK_WriteReg(R9, 0x20000180)
TA260 003:132.712 - 0.003ms returns 0
TA260 003:132.716 JLINK_WriteReg(R10, 0x00000000)
TA260 003:132.720 - 0.003ms returns 0
TA260 003:132.724 JLINK_WriteReg(R11, 0x00000000)
TA260 003:132.727 - 0.003ms returns 0
TA260 003:132.794 JLINK_WriteReg(R12, 0x00000000)
TA260 003:132.798 - 0.003ms returns 0
TA260 003:132.802 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:132.806 - 0.004ms returns 0
TA260 003:132.810 JLINK_WriteReg(R14, 0x20000001)
TA260 003:132.813 - 0.003ms returns 0
TA260 003:132.817 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:132.821 - 0.003ms returns 0
TA260 003:132.835 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:132.839 - 0.004ms returns 0
TA260 003:132.843 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:132.846 - 0.003ms returns 0
TA260 003:132.850 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:132.853 - 0.003ms returns 0
TA260 003:132.857 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:132.861 - 0.003ms returns 0
TA260 003:132.866 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:132.870 - 0.004ms returns 0x0000003D
TA260 003:132.874 JLINK_Go()
TA260 003:132.883   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:135.752 - 2.877ms 
TA260 003:135.772 JLINK_IsHalted()
TA260 003:136.283 - 0.511ms returns FALSE
TA260 003:136.298 JLINK_HasError()
TA260 003:137.726 JLINK_IsHalted()
TA260 003:138.220 - 0.493ms returns FALSE
TA260 003:138.240 JLINK_HasError()
TA260 003:140.238 JLINK_IsHalted()
TA260 003:142.558   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:143.058 - 2.819ms returns TRUE
TA260 003:143.068 JLINK_ReadReg(R15 (PC))
TA260 003:143.073 - 0.005ms returns 0x20000000
TA260 003:143.078 JLINK_ClrBPEx(BPHandle = 0x0000003D)
TA260 003:143.083 - 0.005ms returns 0x00
TA260 003:143.087 JLINK_ReadReg(R0)
TA260 003:143.091 - 0.003ms returns 0x00000000
TA260 003:143.487 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:143.498   Data:  97 39 5A 3E 84 1E 7A BF 6A C2 5B 3E 04 09 7A BF ...
TA260 003:143.510   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:146.081 - 2.593ms returns 0x27C
TA260 003:146.110 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:146.114   Data:  8E B0 71 BF C4 86 A9 3E 57 8F 71 BF 6F 44 AA 3E ...
TA260 003:146.129   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:147.987 - 1.877ms returns 0x184
TA260 003:148.011 JLINK_HasError()
TA260 003:148.050 JLINK_WriteReg(R0, 0x0800C800)
TA260 003:148.066 - 0.015ms returns 0
TA260 003:148.071 JLINK_WriteReg(R1, 0x00000400)
TA260 003:148.074 - 0.003ms returns 0
TA260 003:148.078 JLINK_WriteReg(R2, 0x20000184)
TA260 003:148.082 - 0.003ms returns 0
TA260 003:148.086 JLINK_WriteReg(R3, 0x00000000)
TA260 003:148.089 - 0.003ms returns 0
TA260 003:148.093 JLINK_WriteReg(R4, 0x00000000)
TA260 003:148.097 - 0.003ms returns 0
TA260 003:148.101 JLINK_WriteReg(R5, 0x00000000)
TA260 003:148.105 - 0.003ms returns 0
TA260 003:148.109 JLINK_WriteReg(R6, 0x00000000)
TA260 003:148.112 - 0.003ms returns 0
TA260 003:148.116 JLINK_WriteReg(R7, 0x00000000)
TA260 003:148.120 - 0.003ms returns 0
TA260 003:148.124 JLINK_WriteReg(R8, 0x00000000)
TA260 003:148.128 - 0.003ms returns 0
TA260 003:148.132 JLINK_WriteReg(R9, 0x20000180)
TA260 003:148.135 - 0.003ms returns 0
TA260 003:148.139 JLINK_WriteReg(R10, 0x00000000)
TA260 003:148.143 - 0.003ms returns 0
TA260 003:148.147 JLINK_WriteReg(R11, 0x00000000)
TA260 003:148.150 - 0.003ms returns 0
TA260 003:148.154 JLINK_WriteReg(R12, 0x00000000)
TA260 003:148.158 - 0.003ms returns 0
TA260 003:148.162 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:148.166 - 0.004ms returns 0
TA260 003:148.170 JLINK_WriteReg(R14, 0x20000001)
TA260 003:148.173 - 0.003ms returns 0
TA260 003:148.177 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:148.181 - 0.003ms returns 0
TA260 003:148.185 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:148.188 - 0.003ms returns 0
TA260 003:148.192 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:148.196 - 0.003ms returns 0
TA260 003:148.200 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:148.203 - 0.003ms returns 0
TA260 003:148.207 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:148.211 - 0.003ms returns 0
TA260 003:148.215 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:148.220 - 0.005ms returns 0x0000003E
TA260 003:148.224 JLINK_Go()
TA260 003:148.235   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:151.004 - 2.778ms 
TA260 003:151.018 JLINK_IsHalted()
TA260 003:151.534 - 0.515ms returns FALSE
TA260 003:151.540 JLINK_HasError()
TA260 003:158.753 JLINK_IsHalted()
TA260 003:161.174   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:161.680 - 2.927ms returns TRUE
TA260 003:161.689 JLINK_ReadReg(R15 (PC))
TA260 003:161.695 - 0.006ms returns 0x20000000
TA260 003:161.699 JLINK_ClrBPEx(BPHandle = 0x0000003E)
TA260 003:161.703 - 0.003ms returns 0x00
TA260 003:161.708 JLINK_ReadReg(R0)
TA260 003:161.712 - 0.003ms returns 0x00000000
TA260 003:162.070 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:162.078   Data:  8B 9B CC 3E 7B AB 6A BF CA 53 CD 3E 3C 83 6A BF ...
TA260 003:162.088   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:164.740 - 2.669ms returns 0x27C
TA260 003:164.760 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:164.766   Data:  DD 94 5C BF B1 42 02 3F C7 61 5C BF 32 99 02 3F ...
TA260 003:164.779   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:166.681 - 1.920ms returns 0x184
TA260 003:166.709 JLINK_HasError()
TA260 003:166.717 JLINK_WriteReg(R0, 0x0800CC00)
TA260 003:166.724 - 0.006ms returns 0
TA260 003:166.728 JLINK_WriteReg(R1, 0x00000400)
TA260 003:166.731 - 0.003ms returns 0
TA260 003:166.736 JLINK_WriteReg(R2, 0x20000184)
TA260 003:166.739 - 0.003ms returns 0
TA260 003:166.743 JLINK_WriteReg(R3, 0x00000000)
TA260 003:166.747 - 0.003ms returns 0
TA260 003:166.751 JLINK_WriteReg(R4, 0x00000000)
TA260 003:166.754 - 0.003ms returns 0
TA260 003:166.758 JLINK_WriteReg(R5, 0x00000000)
TA260 003:166.762 - 0.003ms returns 0
TA260 003:166.766 JLINK_WriteReg(R6, 0x00000000)
TA260 003:166.770 - 0.003ms returns 0
TA260 003:166.774 JLINK_WriteReg(R7, 0x00000000)
TA260 003:166.777 - 0.003ms returns 0
TA260 003:166.782 JLINK_WriteReg(R8, 0x00000000)
TA260 003:166.785 - 0.003ms returns 0
TA260 003:166.789 JLINK_WriteReg(R9, 0x20000180)
TA260 003:166.792 - 0.003ms returns 0
TA260 003:166.796 JLINK_WriteReg(R10, 0x00000000)
TA260 003:166.800 - 0.003ms returns 0
TA260 003:166.804 JLINK_WriteReg(R11, 0x00000000)
TA260 003:166.808 - 0.003ms returns 0
TA260 003:166.812 JLINK_WriteReg(R12, 0x00000000)
TA260 003:166.815 - 0.003ms returns 0
TA260 003:166.820 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:166.824 - 0.004ms returns 0
TA260 003:166.828 JLINK_WriteReg(R14, 0x20000001)
TA260 003:166.831 - 0.003ms returns 0
TA260 003:166.835 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:166.839 - 0.003ms returns 0
TA260 003:166.843 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:166.846 - 0.003ms returns 0
TA260 003:166.850 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:166.853 - 0.003ms returns 0
TA260 003:166.857 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:166.861 - 0.003ms returns 0
TA260 003:166.865 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:166.868 - 0.003ms returns 0
TA260 003:166.873 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:166.877 - 0.004ms returns 0x0000003F
TA260 003:166.882 JLINK_Go()
TA260 003:166.891   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:169.668 - 2.785ms 
TA260 003:169.690 JLINK_IsHalted()
TA260 003:170.194 - 0.503ms returns FALSE
TA260 003:170.212 JLINK_HasError()
TA260 003:173.763 JLINK_IsHalted()
TA260 003:176.188   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:176.675 - 2.911ms returns TRUE
TA260 003:176.685 JLINK_ReadReg(R15 (PC))
TA260 003:176.691 - 0.006ms returns 0x20000000
TA260 003:176.696 JLINK_ClrBPEx(BPHandle = 0x0000003F)
TA260 003:176.700 - 0.003ms returns 0x00
TA260 003:176.704 JLINK_ReadReg(R0)
TA260 003:176.708 - 0.003ms returns 0x00000000
TA260 003:177.170 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:177.186   Data:  B0 1E 12 3F C6 33 52 BF 30 71 12 3F 54 FA 51 BF ...
TA260 003:177.199   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:179.863 - 2.693ms returns 0x27C
TA260 003:179.888 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:179.892   Data:  1B FF 3E BF 82 C0 2A 3F 1B BC 3E BF 5B 0B 2B 3F ...
TA260 003:179.918   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:181.752 - 1.864ms returns 0x184
TA260 003:181.763 JLINK_HasError()
TA260 003:181.769 JLINK_WriteReg(R0, 0x0800D000)
TA260 003:181.776 - 0.007ms returns 0
TA260 003:181.780 JLINK_WriteReg(R1, 0x00000400)
TA260 003:181.784 - 0.004ms returns 0
TA260 003:181.788 JLINK_WriteReg(R2, 0x20000184)
TA260 003:181.791 - 0.003ms returns 0
TA260 003:181.796 JLINK_WriteReg(R3, 0x00000000)
TA260 003:181.799 - 0.003ms returns 0
TA260 003:181.803 JLINK_WriteReg(R4, 0x00000000)
TA260 003:181.807 - 0.003ms returns 0
TA260 003:181.811 JLINK_WriteReg(R5, 0x00000000)
TA260 003:181.814 - 0.003ms returns 0
TA260 003:181.818 JLINK_WriteReg(R6, 0x00000000)
TA260 003:181.822 - 0.003ms returns 0
TA260 003:181.826 JLINK_WriteReg(R7, 0x00000000)
TA260 003:181.829 - 0.003ms returns 0
TA260 003:181.833 JLINK_WriteReg(R8, 0x00000000)
TA260 003:181.836 - 0.003ms returns 0
TA260 003:181.840 JLINK_WriteReg(R9, 0x20000180)
TA260 003:181.844 - 0.003ms returns 0
TA260 003:181.848 JLINK_WriteReg(R10, 0x00000000)
TA260 003:181.851 - 0.003ms returns 0
TA260 003:181.855 JLINK_WriteReg(R11, 0x00000000)
TA260 003:181.859 - 0.003ms returns 0
TA260 003:181.863 JLINK_WriteReg(R12, 0x00000000)
TA260 003:181.866 - 0.003ms returns 0
TA260 003:181.870 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:181.874 - 0.004ms returns 0
TA260 003:181.878 JLINK_WriteReg(R14, 0x20000001)
TA260 003:181.882 - 0.003ms returns 0
TA260 003:181.887 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:181.890 - 0.003ms returns 0
TA260 003:181.894 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:181.898 - 0.003ms returns 0
TA260 003:181.902 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:181.906 - 0.004ms returns 0
TA260 003:181.911 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:181.914 - 0.003ms returns 0
TA260 003:181.918 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:181.922 - 0.003ms returns 0
TA260 003:181.926 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:181.930 - 0.004ms returns 0x00000040
TA260 003:181.934 JLINK_Go()
TA260 003:181.943   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:184.675 - 2.739ms 
TA260 003:184.707 JLINK_IsHalted()
TA260 003:185.199 - 0.491ms returns FALSE
TA260 003:185.217 JLINK_HasError()
TA260 003:188.783 JLINK_IsHalted()
TA260 003:191.115   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:191.626 - 2.843ms returns TRUE
TA260 003:191.636 JLINK_ReadReg(R15 (PC))
TA260 003:191.643 - 0.006ms returns 0x20000000
TA260 003:191.647 JLINK_ClrBPEx(BPHandle = 0x00000040)
TA260 003:191.652 - 0.004ms returns 0x00
TA260 003:191.656 JLINK_ReadReg(R0)
TA260 003:191.660 - 0.003ms returns 0x00000000
TA260 003:192.040 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:192.050   Data:  16 52 38 3F 1D A8 31 BF CB 97 38 3F AD 5F 31 BF ...
TA260 003:192.062   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:194.675 - 2.634ms returns 0x27C
TA260 003:194.696 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:194.700   Data:  55 12 1A BF 79 AE 4C 3F 00 C2 19 BF CB EA 4C 3F ...
TA260 003:194.718   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:196.628 - 1.932ms returns 0x184
TA260 003:196.659 JLINK_HasError()
TA260 003:196.667 JLINK_WriteReg(R0, 0x0800D400)
TA260 003:196.674 - 0.007ms returns 0
TA260 003:196.678 JLINK_WriteReg(R1, 0x00000400)
TA260 003:196.682 - 0.003ms returns 0
TA260 003:196.686 JLINK_WriteReg(R2, 0x20000184)
TA260 003:196.689 - 0.003ms returns 0
TA260 003:196.693 JLINK_WriteReg(R3, 0x00000000)
TA260 003:196.698 - 0.004ms returns 0
TA260 003:196.702 JLINK_WriteReg(R4, 0x00000000)
TA260 003:196.705 - 0.003ms returns 0
TA260 003:196.709 JLINK_WriteReg(R5, 0x00000000)
TA260 003:196.712 - 0.003ms returns 0
TA260 003:196.716 JLINK_WriteReg(R6, 0x00000000)
TA260 003:196.720 - 0.003ms returns 0
TA260 003:196.724 JLINK_WriteReg(R7, 0x00000000)
TA260 003:196.727 - 0.003ms returns 0
TA260 003:196.732 JLINK_WriteReg(R8, 0x00000000)
TA260 003:196.735 - 0.003ms returns 0
TA260 003:196.739 JLINK_WriteReg(R9, 0x20000180)
TA260 003:196.742 - 0.003ms returns 0
TA260 003:196.746 JLINK_WriteReg(R10, 0x00000000)
TA260 003:196.791 - 0.044ms returns 0
TA260 003:196.795 JLINK_WriteReg(R11, 0x00000000)
TA260 003:196.799 - 0.003ms returns 0
TA260 003:196.803 JLINK_WriteReg(R12, 0x00000000)
TA260 003:196.806 - 0.003ms returns 0
TA260 003:196.810 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:196.814 - 0.004ms returns 0
TA260 003:196.818 JLINK_WriteReg(R14, 0x20000001)
TA260 003:196.822 - 0.003ms returns 0
TA260 003:196.826 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:196.830 - 0.003ms returns 0
TA260 003:196.834 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:196.838 - 0.003ms returns 0
TA260 003:196.842 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:196.845 - 0.003ms returns 0
TA260 003:196.849 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:196.852 - 0.003ms returns 0
TA260 003:196.856 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:196.860 - 0.003ms returns 0
TA260 003:196.864 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:196.869 - 0.004ms returns 0x00000041
TA260 003:196.873 JLINK_Go()
TA260 003:196.883   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:199.658 - 2.784ms 
TA260 003:199.691 JLINK_IsHalted()
TA260 003:200.169 - 0.477ms returns FALSE
TA260 003:200.181 JLINK_HasError()
TA260 003:204.296 JLINK_IsHalted()
TA260 003:206.690   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:207.216 - 2.920ms returns TRUE
TA260 003:207.226 JLINK_ReadReg(R15 (PC))
TA260 003:207.232 - 0.006ms returns 0x20000000
TA260 003:207.236 JLINK_ClrBPEx(BPHandle = 0x00000041)
TA260 003:207.240 - 0.003ms returns 0x00
TA260 003:207.244 JLINK_ReadReg(R0)
TA260 003:207.248 - 0.004ms returns 0x00000000
TA260 003:207.628 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:207.635   Data:  26 70 57 3F AD 48 0A BF 63 A6 57 3F 09 F4 09 BF ...
TA260 003:207.645   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:210.264 - 2.635ms returns 0x27C
TA260 003:210.282 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:210.286   Data:  A2 73 DE BE CC BE 66 3F 79 BE DD BE 45 EA 66 3F ...
TA260 003:210.298   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:212.215 - 1.932ms returns 0x184
TA260 003:212.234 JLINK_HasError()
TA260 003:212.240 JLINK_WriteReg(R0, 0x0800D800)
TA260 003:212.246 - 0.006ms returns 0
TA260 003:212.250 JLINK_WriteReg(R1, 0x00000400)
TA260 003:212.254 - 0.003ms returns 0
TA260 003:212.258 JLINK_WriteReg(R2, 0x20000184)
TA260 003:212.261 - 0.003ms returns 0
TA260 003:212.265 JLINK_WriteReg(R3, 0x00000000)
TA260 003:212.269 - 0.003ms returns 0
TA260 003:212.274 JLINK_WriteReg(R4, 0x00000000)
TA260 003:212.278 - 0.004ms returns 0
TA260 003:212.282 JLINK_WriteReg(R5, 0x00000000)
TA260 003:212.286 - 0.003ms returns 0
TA260 003:212.290 JLINK_WriteReg(R6, 0x00000000)
TA260 003:212.294 - 0.004ms returns 0
TA260 003:212.298 JLINK_WriteReg(R7, 0x00000000)
TA260 003:212.301 - 0.003ms returns 0
TA260 003:212.306 JLINK_WriteReg(R8, 0x00000000)
TA260 003:212.309 - 0.003ms returns 0
TA260 003:212.313 JLINK_WriteReg(R9, 0x20000180)
TA260 003:212.316 - 0.003ms returns 0
TA260 003:212.320 JLINK_WriteReg(R10, 0x00000000)
TA260 003:212.324 - 0.003ms returns 0
TA260 003:212.328 JLINK_WriteReg(R11, 0x00000000)
TA260 003:212.331 - 0.003ms returns 0
TA260 003:212.335 JLINK_WriteReg(R12, 0x00000000)
TA260 003:212.339 - 0.003ms returns 0
TA260 003:212.343 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:212.347 - 0.004ms returns 0
TA260 003:212.351 JLINK_WriteReg(R14, 0x20000001)
TA260 003:212.354 - 0.003ms returns 0
TA260 003:212.359 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:212.362 - 0.003ms returns 0
TA260 003:212.366 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:212.370 - 0.003ms returns 0
TA260 003:212.374 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:212.378 - 0.003ms returns 0
TA260 003:212.382 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:212.385 - 0.003ms returns 0
TA260 003:212.389 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:212.393 - 0.003ms returns 0
TA260 003:212.397 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:212.402 - 0.004ms returns 0x00000042
TA260 003:212.448 JLINK_Go()
TA260 003:212.457   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:215.236 - 2.784ms 
TA260 003:215.252 JLINK_IsHalted()
TA260 003:215.785 - 0.532ms returns FALSE
TA260 003:215.803 JLINK_HasError()
TA260 003:218.806 JLINK_IsHalted()
TA260 003:219.286 - 0.480ms returns FALSE
TA260 003:219.295 JLINK_HasError()
TA260 003:221.304 JLINK_IsHalted()
TA260 003:223.666   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:224.182 - 2.877ms returns TRUE
TA260 003:224.192 JLINK_ReadReg(R15 (PC))
TA260 003:224.197 - 0.005ms returns 0x20000000
TA260 003:224.202 JLINK_ClrBPEx(BPHandle = 0x00000042)
TA260 003:224.206 - 0.003ms returns 0x00
TA260 003:224.210 JLINK_ReadReg(R0)
TA260 003:224.214 - 0.003ms returns 0x00000000
TA260 003:224.769 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:224.784   Data:  BE 46 6E 3F A0 31 BB BE 6D 6B 6E 3F 6E 76 BA BE ...
TA260 003:224.843   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:227.409 - 2.639ms returns 0x27C
TA260 003:227.433 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:227.437   Data:  22 36 80 BE 10 F1 77 3F E1 E6 7E BE 04 0A 78 3F ...
TA260 003:227.452   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:229.317 - 1.883ms returns 0x184
TA260 003:229.337 JLINK_HasError()
TA260 003:229.343 JLINK_WriteReg(R0, 0x0800DC00)
TA260 003:229.349 - 0.005ms returns 0
TA260 003:229.353 JLINK_WriteReg(R1, 0x00000400)
TA260 003:229.357 - 0.003ms returns 0
TA260 003:229.361 JLINK_WriteReg(R2, 0x20000184)
TA260 003:229.364 - 0.003ms returns 0
TA260 003:229.368 JLINK_WriteReg(R3, 0x00000000)
TA260 003:229.372 - 0.003ms returns 0
TA260 003:229.376 JLINK_WriteReg(R4, 0x00000000)
TA260 003:229.380 - 0.004ms returns 0
TA260 003:229.384 JLINK_WriteReg(R5, 0x00000000)
TA260 003:229.388 - 0.003ms returns 0
TA260 003:229.392 JLINK_WriteReg(R6, 0x00000000)
TA260 003:229.395 - 0.003ms returns 0
TA260 003:229.399 JLINK_WriteReg(R7, 0x00000000)
TA260 003:229.403 - 0.004ms returns 0
TA260 003:229.408 JLINK_WriteReg(R8, 0x00000000)
TA260 003:229.411 - 0.003ms returns 0
TA260 003:229.415 JLINK_WriteReg(R9, 0x20000180)
TA260 003:229.419 - 0.003ms returns 0
TA260 003:229.423 JLINK_WriteReg(R10, 0x00000000)
TA260 003:229.426 - 0.003ms returns 0
TA260 003:229.430 JLINK_WriteReg(R11, 0x00000000)
TA260 003:229.434 - 0.003ms returns 0
TA260 003:229.438 JLINK_WriteReg(R12, 0x00000000)
TA260 003:229.441 - 0.003ms returns 0
TA260 003:229.445 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:229.449 - 0.004ms returns 0
TA260 003:229.453 JLINK_WriteReg(R14, 0x20000001)
TA260 003:229.457 - 0.003ms returns 0
TA260 003:229.461 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:229.464 - 0.003ms returns 0
TA260 003:229.468 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:229.472 - 0.003ms returns 0
TA260 003:229.476 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:229.479 - 0.003ms returns 0
TA260 003:229.483 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:229.487 - 0.003ms returns 0
TA260 003:229.491 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:229.494 - 0.003ms returns 0
TA260 003:229.499 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:229.503 - 0.004ms returns 0x00000043
TA260 003:229.507 JLINK_Go()
TA260 003:229.516   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:232.288 - 2.779ms 
TA260 003:232.305 JLINK_IsHalted()
TA260 003:232.783 - 0.478ms returns FALSE
TA260 003:232.791 JLINK_HasError()
TA260 003:235.822 JLINK_IsHalted()
TA260 003:236.402 - 0.580ms returns FALSE
TA260 003:236.419 JLINK_HasError()
TA260 003:238.844 JLINK_IsHalted()
TA260 003:241.257   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:241.795 - 2.951ms returns TRUE
TA260 003:241.804 JLINK_ReadReg(R15 (PC))
TA260 003:241.810 - 0.005ms returns 0x20000000
TA260 003:241.815 JLINK_ClrBPEx(BPHandle = 0x00000043)
TA260 003:241.819 - 0.003ms returns 0x00
TA260 003:241.824 JLINK_ReadReg(R0)
TA260 003:241.827 - 0.003ms returns 0x00000000
TA260 003:242.199 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:242.208   Data:  31 F5 7B 3F 98 40 35 BE E9 06 7C 3F C4 B4 33 BE ...
TA260 003:242.259   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:244.870 - 2.680ms returns 0x27C
TA260 003:244.902 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:244.906   Data:  62 5A 68 BD 18 9C 7F 3F 68 14 62 BD 91 A1 7F 3F ...
TA260 003:244.928   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:246.785 - 1.882ms returns 0x184
TA260 003:246.803 JLINK_HasError()
TA260 003:246.809 JLINK_WriteReg(R0, 0x0800E000)
TA260 003:246.815 - 0.006ms returns 0
TA260 003:246.820 JLINK_WriteReg(R1, 0x00000400)
TA260 003:246.823 - 0.003ms returns 0
TA260 003:246.828 JLINK_WriteReg(R2, 0x20000184)
TA260 003:246.831 - 0.003ms returns 0
TA260 003:246.835 JLINK_WriteReg(R3, 0x00000000)
TA260 003:246.838 - 0.003ms returns 0
TA260 003:246.843 JLINK_WriteReg(R4, 0x00000000)
TA260 003:246.846 - 0.003ms returns 0
TA260 003:246.850 JLINK_WriteReg(R5, 0x00000000)
TA260 003:246.854 - 0.003ms returns 0
TA260 003:246.858 JLINK_WriteReg(R6, 0x00000000)
TA260 003:246.861 - 0.003ms returns 0
TA260 003:246.865 JLINK_WriteReg(R7, 0x00000000)
TA260 003:246.868 - 0.003ms returns 0
TA260 003:246.873 JLINK_WriteReg(R8, 0x00000000)
TA260 003:246.876 - 0.003ms returns 0
TA260 003:246.880 JLINK_WriteReg(R9, 0x20000180)
TA260 003:246.884 - 0.003ms returns 0
TA260 003:246.888 JLINK_WriteReg(R10, 0x00000000)
TA260 003:246.891 - 0.003ms returns 0
TA260 003:246.895 JLINK_WriteReg(R11, 0x00000000)
TA260 003:246.899 - 0.003ms returns 0
TA260 003:246.903 JLINK_WriteReg(R12, 0x00000000)
TA260 003:246.906 - 0.003ms returns 0
TA260 003:246.910 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:246.914 - 0.004ms returns 0
TA260 003:246.918 JLINK_WriteReg(R14, 0x20000001)
TA260 003:246.922 - 0.004ms returns 0
TA260 003:246.928 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:246.933 - 0.005ms returns 0
TA260 003:246.938 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:246.941 - 0.003ms returns 0
TA260 003:246.946 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:246.949 - 0.003ms returns 0
TA260 003:246.953 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:246.956 - 0.003ms returns 0
TA260 003:246.960 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:246.964 - 0.003ms returns 0
TA260 003:246.969 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:246.973 - 0.005ms returns 0x00000044
TA260 003:246.977 JLINK_Go()
TA260 003:246.987   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:249.817 - 2.839ms 
TA260 003:249.850 JLINK_IsHalted()
TA260 003:250.316 - 0.466ms returns FALSE
TA260 003:250.327 JLINK_HasError()
TA260 003:253.353 JLINK_IsHalted()
TA260 003:253.850 - 0.496ms returns FALSE
TA260 003:253.857 JLINK_HasError()
TA260 003:256.354 JLINK_IsHalted()
TA260 003:258.741   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:259.257 - 2.902ms returns TRUE
TA260 003:259.273 JLINK_ReadReg(R15 (PC))
TA260 003:259.279 - 0.005ms returns 0x20000000
TA260 003:259.289 JLINK_ClrBPEx(BPHandle = 0x00000044)
TA260 003:259.294 - 0.004ms returns 0x00
TA260 003:259.298 JLINK_ReadReg(R0)
TA260 003:259.302 - 0.003ms returns 0x00000000
TA260 003:259.791 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:259.799   Data:  E1 7A 14 40 71 3D 0A 40 00 00 00 40 D7 A3 F0 3F ...
TA260 003:259.811   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:262.425 - 2.631ms returns 0x27C
TA260 003:262.458 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:262.462   Data:  FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF ...
TA260 003:262.477   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:264.405 - 1.946ms returns 0x184
TA260 003:264.413 JLINK_HasError()
TA260 003:264.418 JLINK_WriteReg(R0, 0x0800E400)
TA260 003:264.424 - 0.006ms returns 0
TA260 003:264.428 JLINK_WriteReg(R1, 0x00000070)
TA260 003:264.432 - 0.003ms returns 0
TA260 003:264.436 JLINK_WriteReg(R2, 0x20000184)
TA260 003:264.439 - 0.003ms returns 0
TA260 003:264.443 JLINK_WriteReg(R3, 0x00000000)
TA260 003:264.447 - 0.003ms returns 0
TA260 003:264.451 JLINK_WriteReg(R4, 0x00000000)
TA260 003:264.455 - 0.003ms returns 0
TA260 003:264.459 JLINK_WriteReg(R5, 0x00000000)
TA260 003:264.462 - 0.003ms returns 0
TA260 003:264.508 JLINK_WriteReg(R6, 0x00000000)
TA260 003:264.512 - 0.003ms returns 0
TA260 003:264.516 JLINK_WriteReg(R7, 0x00000000)
TA260 003:264.520 - 0.003ms returns 0
TA260 003:264.524 JLINK_WriteReg(R8, 0x00000000)
TA260 003:264.528 - 0.004ms returns 0
TA260 003:264.532 JLINK_WriteReg(R9, 0x20000180)
TA260 003:264.535 - 0.003ms returns 0
TA260 003:264.539 JLINK_WriteReg(R10, 0x00000000)
TA260 003:264.542 - 0.003ms returns 0
TA260 003:264.547 JLINK_WriteReg(R11, 0x00000000)
TA260 003:264.550 - 0.003ms returns 0
TA260 003:264.554 JLINK_WriteReg(R12, 0x00000000)
TA260 003:264.558 - 0.003ms returns 0
TA260 003:264.678 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:264.682 - 0.004ms returns 0
TA260 003:264.686 JLINK_WriteReg(R14, 0x20000001)
TA260 003:264.690 - 0.003ms returns 0
TA260 003:264.694 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:264.698 - 0.003ms returns 0
TA260 003:264.702 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:264.706 - 0.003ms returns 0
TA260 003:264.710 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:264.713 - 0.003ms returns 0
TA260 003:264.717 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:264.721 - 0.003ms returns 0
TA260 003:264.725 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:264.728 - 0.003ms returns 0
TA260 003:264.733 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:264.738 - 0.005ms returns 0x00000045
TA260 003:264.742 JLINK_Go()
TA260 003:264.752   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:267.532 - 2.789ms 
TA260 003:267.557 JLINK_IsHalted()
TA260 003:269.927   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:270.432 - 2.875ms returns TRUE
TA260 003:270.439 JLINK_ReadReg(R15 (PC))
TA260 003:270.444 - 0.005ms returns 0x20000000
TA260 003:270.450 JLINK_ClrBPEx(BPHandle = 0x00000045)
TA260 003:270.454 - 0.003ms returns 0x00
TA260 003:270.458 JLINK_ReadReg(R0)
TA260 003:270.461 - 0.003ms returns 0x00000000
TA260 003:270.466 JLINK_HasError()
TA260 003:270.471 JLINK_WriteReg(R0, 0x00000002)
TA260 003:270.476 - 0.004ms returns 0
TA260 003:270.480 JLINK_WriteReg(R1, 0x00000070)
TA260 003:270.484 - 0.003ms returns 0
TA260 003:270.488 JLINK_WriteReg(R2, 0x20000184)
TA260 003:270.491 - 0.003ms returns 0
TA260 003:270.496 JLINK_WriteReg(R3, 0x00000000)
TA260 003:270.499 - 0.003ms returns 0
TA260 003:270.503 JLINK_WriteReg(R4, 0x00000000)
TA260 003:270.507 - 0.003ms returns 0
TA260 003:270.511 JLINK_WriteReg(R5, 0x00000000)
TA260 003:270.514 - 0.003ms returns 0
TA260 003:270.518 JLINK_WriteReg(R6, 0x00000000)
TA260 003:270.522 - 0.003ms returns 0
TA260 003:270.526 JLINK_WriteReg(R7, 0x00000000)
TA260 003:270.529 - 0.003ms returns 0
TA260 003:270.533 JLINK_WriteReg(R8, 0x00000000)
TA260 003:270.536 - 0.003ms returns 0
TA260 003:270.540 JLINK_WriteReg(R9, 0x20000180)
TA260 003:270.544 - 0.003ms returns 0
TA260 003:270.548 JLINK_WriteReg(R10, 0x00000000)
TA260 003:270.551 - 0.003ms returns 0
TA260 003:270.555 JLINK_WriteReg(R11, 0x00000000)
TA260 003:270.559 - 0.003ms returns 0
TA260 003:270.563 JLINK_WriteReg(R12, 0x00000000)
TA260 003:270.566 - 0.003ms returns 0
TA260 003:270.571 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:270.574 - 0.003ms returns 0
TA260 003:270.578 JLINK_WriteReg(R14, 0x20000001)
TA260 003:270.582 - 0.003ms returns 0
TA260 003:270.586 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 003:270.590 - 0.003ms returns 0
TA260 003:270.594 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:270.597 - 0.003ms returns 0
TA260 003:270.601 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:270.604 - 0.003ms returns 0
TA260 003:270.608 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:270.612 - 0.003ms returns 0
TA260 003:270.616 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:270.619 - 0.003ms returns 0
TA260 003:270.624 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:270.628 - 0.004ms returns 0x00000046
TA260 003:270.632 JLINK_Go()
TA260 003:270.640   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:273.317 - 2.684ms 
TA260 003:273.340 JLINK_IsHalted()
TA260 003:275.691   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:276.276 - 2.937ms returns TRUE
TA260 003:276.294 JLINK_ReadReg(R15 (PC))
TA260 003:276.301 - 0.006ms returns 0x20000000
TA260 003:276.308 JLINK_ClrBPEx(BPHandle = 0x00000046)
TA260 003:276.312 - 0.004ms returns 0x00
TA260 003:276.355 JLINK_ReadReg(R0)
TA260 003:276.361 - 0.005ms returns 0x00000000
TA260 003:332.083 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 003:332.096   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 003:332.112   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 003:334.006 - 1.922ms returns 0x184
TA260 003:334.042 JLINK_HasError()
TA260 003:334.048 JLINK_WriteReg(R0, 0x08000000)
TA260 003:334.054 - 0.006ms returns 0
TA260 003:334.058 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 003:334.062 - 0.003ms returns 0
TA260 003:334.066 JLINK_WriteReg(R2, 0x00000003)
TA260 003:334.069 - 0.003ms returns 0
TA260 003:334.073 JLINK_WriteReg(R3, 0x00000000)
TA260 003:334.077 - 0.003ms returns 0
TA260 003:334.081 JLINK_WriteReg(R4, 0x00000000)
TA260 003:334.085 - 0.003ms returns 0
TA260 003:334.089 JLINK_WriteReg(R5, 0x00000000)
TA260 003:334.092 - 0.003ms returns 0
TA260 003:334.096 JLINK_WriteReg(R6, 0x00000000)
TA260 003:334.099 - 0.003ms returns 0
TA260 003:334.103 JLINK_WriteReg(R7, 0x00000000)
TA260 003:334.107 - 0.003ms returns 0
TA260 003:334.112 JLINK_WriteReg(R8, 0x00000000)
TA260 003:334.115 - 0.003ms returns 0
TA260 003:334.119 JLINK_WriteReg(R9, 0x20000180)
TA260 003:334.123 - 0.003ms returns 0
TA260 003:334.127 JLINK_WriteReg(R10, 0x00000000)
TA260 003:334.130 - 0.003ms returns 0
TA260 003:334.134 JLINK_WriteReg(R11, 0x00000000)
TA260 003:334.138 - 0.003ms returns 0
TA260 003:334.142 JLINK_WriteReg(R12, 0x00000000)
TA260 003:334.145 - 0.003ms returns 0
TA260 003:334.149 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:334.153 - 0.004ms returns 0
TA260 003:334.157 JLINK_WriteReg(R14, 0x20000001)
TA260 003:334.161 - 0.003ms returns 0
TA260 003:334.165 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 003:334.169 - 0.003ms returns 0
TA260 003:334.173 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:334.176 - 0.003ms returns 0
TA260 003:334.180 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:334.184 - 0.004ms returns 0
TA260 003:334.188 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:334.192 - 0.003ms returns 0
TA260 003:334.196 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:334.199 - 0.003ms returns 0
TA260 003:334.204 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:334.211   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:334.707 - 0.502ms returns 0x00000047
TA260 003:334.727 JLINK_Go()
TA260 003:334.734   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 003:335.227   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:338.045 - 3.316ms 
TA260 003:338.069 JLINK_IsHalted()
TA260 003:340.408   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:340.897 - 2.827ms returns TRUE
TA260 003:340.904 JLINK_ReadReg(R15 (PC))
TA260 003:340.910 - 0.006ms returns 0x20000000
TA260 003:340.915 JLINK_ClrBPEx(BPHandle = 0x00000047)
TA260 003:340.918 - 0.003ms returns 0x00
TA260 003:340.924 JLINK_ReadReg(R0)
TA260 003:340.928 - 0.004ms returns 0x00000000
TA260 003:340.933 JLINK_HasError()
TA260 003:340.939 JLINK_WriteReg(R0, 0xFFFFFFFF)
TA260 003:340.943 - 0.004ms returns 0
TA260 003:340.947 JLINK_WriteReg(R1, 0x08000000)
TA260 003:340.950 - 0.003ms returns 0
TA260 003:340.954 JLINK_WriteReg(R2, 0x0000E470)
TA260 003:340.958 - 0.003ms returns 0
TA260 003:340.962 JLINK_WriteReg(R3, 0x04C11DB7)
TA260 003:340.965 - 0.003ms returns 0
TA260 003:340.969 JLINK_WriteReg(R4, 0x00000000)
TA260 003:340.973 - 0.003ms returns 0
TA260 003:340.977 JLINK_WriteReg(R5, 0x00000000)
TA260 003:340.980 - 0.003ms returns 0
TA260 003:340.984 JLINK_WriteReg(R6, 0x00000000)
TA260 003:340.988 - 0.003ms returns 0
TA260 003:340.992 JLINK_WriteReg(R7, 0x00000000)
TA260 003:340.995 - 0.003ms returns 0
TA260 003:340.999 JLINK_WriteReg(R8, 0x00000000)
TA260 003:341.002 - 0.003ms returns 0
TA260 003:341.006 JLINK_WriteReg(R9, 0x20000180)
TA260 003:341.010 - 0.003ms returns 0
TA260 003:341.014 JLINK_WriteReg(R10, 0x00000000)
TA260 003:341.059 - 0.045ms returns 0
TA260 003:341.063 JLINK_WriteReg(R11, 0x00000000)
TA260 003:341.066 - 0.003ms returns 0
TA260 003:341.070 JLINK_WriteReg(R12, 0x00000000)
TA260 003:341.074 - 0.003ms returns 0
TA260 003:341.079 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:341.082 - 0.003ms returns 0
TA260 003:341.086 JLINK_WriteReg(R14, 0x20000001)
TA260 003:341.101 - 0.014ms returns 0
TA260 003:341.105 JLINK_WriteReg(R15 (PC), 0x20000002)
TA260 003:341.109 - 0.003ms returns 0
TA260 003:341.113 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:341.116 - 0.003ms returns 0
TA260 003:341.120 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:341.124 - 0.003ms returns 0
TA260 003:341.128 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:341.131 - 0.003ms returns 0
TA260 003:341.135 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:341.138 - 0.003ms returns 0
TA260 003:341.143 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:341.147 - 0.004ms returns 0x00000048
TA260 003:341.151 JLINK_Go()
TA260 003:341.159   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:343.887 - 2.735ms 
TA260 003:343.900 JLINK_IsHalted()
TA260 003:344.440 - 0.538ms returns FALSE
TA260 003:344.450 JLINK_HasError()
TA260 003:350.439 JLINK_IsHalted()
TA260 003:351.011 - 0.572ms returns FALSE
TA260 003:351.018 JLINK_HasError()
TA260 003:354.434 JLINK_IsHalted()
TA260 003:354.998 - 0.563ms returns FALSE
TA260 003:355.011 JLINK_HasError()
TA260 003:356.432 JLINK_IsHalted()
TA260 003:356.992 - 0.560ms returns FALSE
TA260 003:357.007 JLINK_HasError()
TA260 003:358.945 JLINK_IsHalted()
TA260 003:359.469 - 0.523ms returns FALSE
TA260 003:359.484 JLINK_HasError()
TA260 003:360.936 JLINK_IsHalted()
TA260 003:361.431 - 0.494ms returns FALSE
TA260 003:361.440 JLINK_HasError()
TA260 003:365.771 JLINK_IsHalted()
TA260 003:366.395 - 0.623ms returns FALSE
TA260 003:366.412 JLINK_HasError()
TA260 003:367.765 JLINK_IsHalted()
TA260 003:368.206 - 0.441ms returns FALSE
TA260 003:368.213 JLINK_HasError()
TA260 003:369.274 JLINK_IsHalted()
TA260 003:369.797 - 0.522ms returns FALSE
TA260 003:369.804 JLINK_HasError()
TA260 003:372.276 JLINK_IsHalted()
TA260 003:372.775 - 0.498ms returns FALSE
TA260 003:372.782 JLINK_HasError()
TA260 003:374.276 JLINK_IsHalted()
TA260 003:374.769 - 0.492ms returns FALSE
TA260 003:374.790 JLINK_HasError()
TA260 003:377.284 JLINK_IsHalted()
TA260 003:377.773 - 0.488ms returns FALSE
TA260 003:377.795 JLINK_HasError()
TA260 003:379.783 JLINK_IsHalted()
TA260 003:380.309 - 0.525ms returns FALSE
TA260 003:380.320 JLINK_HasError()
TA260 003:381.780 JLINK_IsHalted()
TA260 003:382.310 - 0.530ms returns FALSE
TA260 003:382.318 JLINK_HasError()
TA260 003:383.782 JLINK_IsHalted()
TA260 003:384.250 - 0.468ms returns FALSE
TA260 003:384.260 JLINK_HasError()
TA260 003:385.788 JLINK_IsHalted()
TA260 003:386.279 - 0.490ms returns FALSE
TA260 003:386.294 JLINK_HasError()
TA260 003:388.790 JLINK_IsHalted()
TA260 003:389.264 - 0.474ms returns FALSE
TA260 003:389.272 JLINK_HasError()
TA260 003:391.290 JLINK_IsHalted()
TA260 003:391.772 - 0.481ms returns FALSE
TA260 003:391.778 JLINK_HasError()
TA260 003:393.290 JLINK_IsHalted()
TA260 003:393.803 - 0.513ms returns FALSE
TA260 003:393.811 JLINK_HasError()
TA260 003:395.293 JLINK_IsHalted()
TA260 003:395.802 - 0.508ms returns FALSE
TA260 003:395.818 JLINK_HasError()
TA260 003:397.292 JLINK_IsHalted()
TA260 003:397.798 - 0.505ms returns FALSE
TA260 003:397.804 JLINK_HasError()
TA260 003:399.797 JLINK_IsHalted()
TA260 003:400.263 - 0.465ms returns FALSE
TA260 003:400.275 JLINK_HasError()
TA260 003:402.799 JLINK_IsHalted()
TA260 003:403.282 - 0.482ms returns FALSE
TA260 003:403.293 JLINK_HasError()
TA260 003:404.800 JLINK_IsHalted()
TA260 003:405.288 - 0.487ms returns FALSE
TA260 003:405.305 JLINK_HasError()
TA260 003:406.800 JLINK_IsHalted()
TA260 003:407.318 - 0.517ms returns FALSE
TA260 003:407.328 JLINK_HasError()
TA260 003:408.802 JLINK_IsHalted()
TA260 003:409.320 - 0.517ms returns FALSE
TA260 003:409.326 JLINK_HasError()
TA260 003:411.308 JLINK_IsHalted()
TA260 003:411.782 - 0.473ms returns FALSE
TA260 003:411.791 JLINK_HasError()
TA260 003:413.312 JLINK_IsHalted()
TA260 003:413.830 - 0.517ms returns FALSE
TA260 003:413.840 JLINK_HasError()
TA260 003:416.316 JLINK_IsHalted()
TA260 003:416.820 - 0.503ms returns FALSE
TA260 003:416.828 JLINK_HasError()
TA260 003:418.811 JLINK_IsHalted()
TA260 003:419.396 - 0.584ms returns FALSE
TA260 003:419.410 JLINK_HasError()
TA260 003:421.822 JLINK_IsHalted()
TA260 003:422.324 - 0.501ms returns FALSE
TA260 003:422.341 JLINK_HasError()
TA260 003:423.812 JLINK_IsHalted()
TA260 003:424.310 - 0.497ms returns FALSE
TA260 003:424.320 JLINK_HasError()
TA260 003:425.819 JLINK_IsHalted()
TA260 003:426.288 - 0.468ms returns FALSE
TA260 003:426.294 JLINK_HasError()
TA260 003:427.819 JLINK_IsHalted()
TA260 003:428.398 - 0.578ms returns FALSE
TA260 003:428.413 JLINK_HasError()
TA260 003:430.112 JLINK_IsHalted()
TA260 003:430.604 - 0.490ms returns FALSE
TA260 003:430.613 JLINK_HasError()
TA260 003:433.114 JLINK_IsHalted()
TA260 003:433.614 - 0.499ms returns FALSE
TA260 003:433.620 JLINK_HasError()
TA260 003:435.117 JLINK_IsHalted()
TA260 003:435.585 - 0.467ms returns FALSE
TA260 003:435.595 JLINK_HasError()
TA260 003:437.626 JLINK_IsHalted()
TA260 003:438.112 - 0.486ms returns FALSE
TA260 003:438.121 JLINK_HasError()
TA260 003:440.138 JLINK_IsHalted()
TA260 003:440.613 - 0.475ms returns FALSE
TA260 003:440.620 JLINK_HasError()
TA260 003:444.071 JLINK_IsHalted()
TA260 003:444.626 - 0.555ms returns FALSE
TA260 003:444.644 JLINK_HasError()
TA260 003:446.071 JLINK_IsHalted()
TA260 003:446.605 - 0.532ms returns FALSE
TA260 003:446.621 JLINK_HasError()
TA260 003:448.070 JLINK_IsHalted()
TA260 003:448.540 - 0.469ms returns FALSE
TA260 003:448.554 JLINK_HasError()
TA260 003:450.574 JLINK_IsHalted()
TA260 003:451.065 - 0.490ms returns FALSE
TA260 003:451.072 JLINK_HasError()
TA260 003:452.576 JLINK_IsHalted()
TA260 003:453.066 - 0.489ms returns FALSE
TA260 003:453.077 JLINK_HasError()
TA260 003:455.576 JLINK_IsHalted()
TA260 003:456.087 - 0.510ms returns FALSE
TA260 003:456.094 JLINK_HasError()
TA260 003:457.575 JLINK_IsHalted()
TA260 003:458.081 - 0.506ms returns FALSE
TA260 003:458.088 JLINK_HasError()
TA260 003:462.087 JLINK_IsHalted()
TA260 003:462.629 - 0.541ms returns FALSE
TA260 003:462.636 JLINK_HasError()
TA260 003:464.087 JLINK_IsHalted()
TA260 003:464.668 - 0.581ms returns FALSE
TA260 003:464.683 JLINK_HasError()
TA260 003:468.087 JLINK_IsHalted()
TA260 003:468.614 - 0.526ms returns FALSE
TA260 003:468.622 JLINK_HasError()
TA260 003:470.594 JLINK_IsHalted()
TA260 003:471.037 - 0.443ms returns FALSE
TA260 003:471.044 JLINK_HasError()
TA260 003:472.594 JLINK_IsHalted()
TA260 003:473.078 - 0.484ms returns FALSE
TA260 003:473.086 JLINK_HasError()
TA260 003:475.598 JLINK_IsHalted()
TA260 003:476.108 - 0.509ms returns FALSE
TA260 003:476.123 JLINK_HasError()
TA260 003:477.596 JLINK_IsHalted()
TA260 003:478.088 - 0.491ms returns FALSE
TA260 003:478.097 JLINK_HasError()
TA260 003:480.104 JLINK_IsHalted()
TA260 003:480.618 - 0.513ms returns FALSE
TA260 003:480.638 JLINK_HasError()
TA260 003:484.105 JLINK_IsHalted()
TA260 003:484.694 - 0.588ms returns FALSE
TA260 003:484.713 JLINK_HasError()
TA260 003:487.108 JLINK_IsHalted()
TA260 003:487.581 - 0.473ms returns FALSE
TA260 003:487.588 JLINK_HasError()
TA260 003:489.104 JLINK_IsHalted()
TA260 003:489.520 - 0.415ms returns FALSE
TA260 003:489.527 JLINK_HasError()
TA260 003:490.610 JLINK_IsHalted()
TA260 003:491.124 - 0.514ms returns FALSE
TA260 003:491.135 JLINK_HasError()
TA260 003:492.607 JLINK_IsHalted()
TA260 003:493.113 - 0.505ms returns FALSE
TA260 003:493.122 JLINK_HasError()
TA260 003:495.612 JLINK_IsHalted()
TA260 003:496.140 - 0.527ms returns FALSE
TA260 003:496.148 JLINK_HasError()
TA260 003:497.608 JLINK_IsHalted()
TA260 003:498.079 - 0.471ms returns FALSE
TA260 003:498.087 JLINK_HasError()
TA260 003:500.115 JLINK_IsHalted()
TA260 003:500.626 - 0.511ms returns FALSE
TA260 003:500.680 JLINK_HasError()
TA260 003:502.112 JLINK_IsHalted()
TA260 003:502.563 - 0.450ms returns FALSE
TA260 003:502.570 JLINK_HasError()
TA260 003:505.133 JLINK_IsHalted()
TA260 003:505.651 - 0.517ms returns FALSE
TA260 003:505.674 JLINK_HasError()
TA260 003:507.016 JLINK_IsHalted()
TA260 003:507.528 - 0.511ms returns FALSE
TA260 003:507.542 JLINK_HasError()
TA260 003:509.028 JLINK_IsHalted()
TA260 003:509.469 - 0.441ms returns FALSE
TA260 003:509.480 JLINK_HasError()
TA260 003:511.036 JLINK_IsHalted()
TA260 003:511.522 - 0.485ms returns FALSE
TA260 003:511.529 JLINK_HasError()
TA260 003:513.029 JLINK_IsHalted()
TA260 003:513.518 - 0.488ms returns FALSE
TA260 003:513.527 JLINK_HasError()
TA260 003:517.030 JLINK_IsHalted()
TA260 003:517.564 - 0.534ms returns FALSE
TA260 003:517.572 JLINK_HasError()
TA260 003:519.037 JLINK_IsHalted()
TA260 003:519.521 - 0.484ms returns FALSE
TA260 003:519.528 JLINK_HasError()
TA260 003:522.542 JLINK_IsHalted()
TA260 003:523.082 - 0.539ms returns FALSE
TA260 003:523.088 JLINK_HasError()
TA260 003:524.540 JLINK_IsHalted()
TA260 003:525.156 - 0.616ms returns FALSE
TA260 003:525.176 JLINK_HasError()
TA260 003:529.052 JLINK_IsHalted()
TA260 003:529.629 - 0.576ms returns FALSE
TA260 003:529.646 JLINK_HasError()
TA260 003:532.047 JLINK_IsHalted()
TA260 003:532.536 - 0.489ms returns FALSE
TA260 003:532.544 JLINK_HasError()
TA260 003:534.046 JLINK_IsHalted()
TA260 003:534.536 - 0.490ms returns FALSE
TA260 003:534.544 JLINK_HasError()
TA260 003:536.052 JLINK_IsHalted()
TA260 003:536.523 - 0.470ms returns FALSE
TA260 003:536.531 JLINK_HasError()
TA260 003:540.192 JLINK_IsHalted()
TA260 003:540.759 - 0.566ms returns FALSE
TA260 003:540.766 JLINK_HasError()
TA260 003:544.197 JLINK_IsHalted()
TA260 003:544.724 - 0.525ms returns FALSE
TA260 003:544.746 JLINK_HasError()
TA260 003:549.202 JLINK_IsHalted()
TA260 003:549.739 - 0.536ms returns FALSE
TA260 003:549.748 JLINK_HasError()
TA260 003:552.702 JLINK_IsHalted()
TA260 003:553.164 - 0.462ms returns FALSE
TA260 003:553.176 JLINK_HasError()
TA260 003:556.706 JLINK_IsHalted()
TA260 003:557.240 - 0.533ms returns FALSE
TA260 003:557.260 JLINK_HasError()
TA260 003:562.134 JLINK_IsHalted()
TA260 003:562.616 - 0.482ms returns FALSE
TA260 003:562.623 JLINK_HasError()
TA260 003:565.142 JLINK_IsHalted()
TA260 003:565.684 - 0.541ms returns FALSE
TA260 003:565.703 JLINK_HasError()
TA260 003:568.140 JLINK_IsHalted()
TA260 003:568.774 - 0.634ms returns FALSE
TA260 003:568.786 JLINK_HasError()
TA260 003:571.640 JLINK_IsHalted()
TA260 003:572.159 - 0.517ms returns FALSE
TA260 003:572.167 JLINK_HasError()
TA260 003:574.700 JLINK_IsHalted()
TA260 003:575.266 - 0.565ms returns FALSE
TA260 003:575.282 JLINK_HasError()
TA260 003:580.152 JLINK_IsHalted()
TA260 003:580.630 - 0.477ms returns FALSE
TA260 003:580.639 JLINK_HasError()
TA260 003:583.150 JLINK_IsHalted()
TA260 003:583.701 - 0.551ms returns FALSE
TA260 003:583.708 JLINK_HasError()
TA260 003:586.156 JLINK_IsHalted()
TA260 003:586.751 - 0.594ms returns FALSE
TA260 003:586.764 JLINK_HasError()
TA260 003:589.164 JLINK_IsHalted()
TA260 003:589.683 - 0.519ms returns FALSE
TA260 003:589.700 JLINK_HasError()
TA260 003:593.666 JLINK_IsHalted()
TA260 003:594.194 - 0.527ms returns FALSE
TA260 003:594.206 JLINK_HasError()
TA260 003:596.685 JLINK_IsHalted()
TA260 003:597.206 - 0.520ms returns FALSE
TA260 003:597.222 JLINK_HasError()
TA260 003:600.189 JLINK_IsHalted()
TA260 003:600.743 - 0.552ms returns FALSE
TA260 003:600.752 JLINK_HasError()
TA260 003:603.188 JLINK_IsHalted()
TA260 003:603.760 - 0.572ms returns FALSE
TA260 003:603.768 JLINK_HasError()
TA260 003:607.196 JLINK_IsHalted()
TA260 003:607.722 - 0.525ms returns FALSE
TA260 003:607.745 JLINK_HasError()
TA260 003:610.699 JLINK_IsHalted()
TA260 003:611.266 - 0.566ms returns FALSE
TA260 003:611.274 JLINK_HasError()
TA260 003:614.697 JLINK_IsHalted()
TA260 003:615.220 - 0.522ms returns FALSE
TA260 003:615.235 JLINK_HasError()
TA260 003:616.700 JLINK_IsHalted()
TA260 003:617.260 - 0.560ms returns FALSE
TA260 003:617.268 JLINK_HasError()
TA260 003:620.204 JLINK_IsHalted()
TA260 003:620.763 - 0.558ms returns FALSE
TA260 003:620.770 JLINK_HasError()
TA260 003:623.206 JLINK_IsHalted()
TA260 003:623.760 - 0.554ms returns FALSE
TA260 003:623.767 JLINK_HasError()
TA260 003:625.207 JLINK_IsHalted()
TA260 003:625.763 - 0.555ms returns FALSE
TA260 003:625.774 JLINK_HasError()
TA260 003:629.709 JLINK_IsHalted()
TA260 003:630.265 - 0.555ms returns FALSE
TA260 003:630.278 JLINK_HasError()
TA260 003:631.710 JLINK_IsHalted()
TA260 003:632.261 - 0.551ms returns FALSE
TA260 003:632.269 JLINK_HasError()
TA260 003:633.711 JLINK_IsHalted()
TA260 003:634.202 - 0.490ms returns FALSE
TA260 003:634.210 JLINK_HasError()
TA260 003:635.718 JLINK_IsHalted()
TA260 003:636.190 - 0.471ms returns FALSE
TA260 003:636.206 JLINK_HasError()
TA260 003:641.226 JLINK_IsHalted()
TA260 003:641.808 - 0.581ms returns FALSE
TA260 003:641.816 JLINK_HasError()
TA260 003:643.220 JLINK_IsHalted()
TA260 003:643.705 - 0.484ms returns FALSE
TA260 003:643.714 JLINK_HasError()
TA260 003:645.222 JLINK_IsHalted()
TA260 003:645.719 - 0.497ms returns FALSE
TA260 003:645.735 JLINK_HasError()
TA260 003:646.911 JLINK_IsHalted()
TA260 003:647.386 - 0.475ms returns FALSE
TA260 003:647.401 JLINK_HasError()
TA260 003:650.420 JLINK_IsHalted()
TA260 003:650.910 - 0.490ms returns FALSE
TA260 003:650.917 JLINK_HasError()
TA260 003:652.418 JLINK_IsHalted()
TA260 003:652.908 - 0.489ms returns FALSE
TA260 003:652.916 JLINK_HasError()
TA260 003:654.419 JLINK_IsHalted()
TA260 003:654.938 - 0.519ms returns FALSE
TA260 003:654.956 JLINK_HasError()
TA260 003:656.426 JLINK_IsHalted()
TA260 003:656.920 - 0.493ms returns FALSE
TA260 003:656.928 JLINK_HasError()
TA260 003:658.986 JLINK_IsHalted()
TA260 003:659.536 - 0.550ms returns FALSE
TA260 003:659.552 JLINK_HasError()
TA260 003:660.926 JLINK_IsHalted()
TA260 003:661.394 - 0.467ms returns FALSE
TA260 003:661.401 JLINK_HasError()
TA260 003:662.933 JLINK_IsHalted()
TA260 003:663.418 - 0.484ms returns FALSE
TA260 003:663.429 JLINK_HasError()
TA260 003:665.933 JLINK_IsHalted()
TA260 003:668.316   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:668.786 - 2.852ms returns TRUE
TA260 003:668.795 JLINK_ReadReg(R15 (PC))
TA260 003:668.800 - 0.006ms returns 0x20000000
TA260 003:668.805 JLINK_ClrBPEx(BPHandle = 0x00000048)
TA260 003:668.809 - 0.003ms returns 0x00
TA260 003:668.813 JLINK_ReadReg(R0)
TA260 003:668.817 - 0.003ms returns 0x4F905EB9
TA260 003:670.593 JLINK_HasError()
TA260 003:670.607 JLINK_WriteReg(R0, 0x00000003)
TA260 003:670.612 - 0.005ms returns 0
TA260 003:670.617 JLINK_WriteReg(R1, 0x08000000)
TA260 003:670.620 - 0.003ms returns 0
TA260 003:670.624 JLINK_WriteReg(R2, 0x0000E470)
TA260 003:670.628 - 0.003ms returns 0
TA260 003:670.632 JLINK_WriteReg(R3, 0x04C11DB7)
TA260 003:670.636 - 0.003ms returns 0
TA260 003:670.640 JLINK_WriteReg(R4, 0x00000000)
TA260 003:670.643 - 0.003ms returns 0
TA260 003:670.647 JLINK_WriteReg(R5, 0x00000000)
TA260 003:670.651 - 0.003ms returns 0
TA260 003:670.655 JLINK_WriteReg(R6, 0x00000000)
TA260 003:670.658 - 0.003ms returns 0
TA260 003:670.663 JLINK_WriteReg(R7, 0x00000000)
TA260 003:670.666 - 0.003ms returns 0
TA260 003:670.670 JLINK_WriteReg(R8, 0x00000000)
TA260 003:670.673 - 0.003ms returns 0
TA260 003:670.677 JLINK_WriteReg(R9, 0x20000180)
TA260 003:670.681 - 0.003ms returns 0
TA260 003:670.685 JLINK_WriteReg(R10, 0x00000000)
TA260 003:670.688 - 0.003ms returns 0
TA260 003:670.692 JLINK_WriteReg(R11, 0x00000000)
TA260 003:670.696 - 0.003ms returns 0
TA260 003:670.700 JLINK_WriteReg(R12, 0x00000000)
TA260 003:670.704 - 0.003ms returns 0
TA260 003:670.708 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:670.712 - 0.004ms returns 0
TA260 003:670.716 JLINK_WriteReg(R14, 0x20000001)
TA260 003:670.719 - 0.003ms returns 0
TA260 003:670.723 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 003:670.726 - 0.003ms returns 0
TA260 003:670.731 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:670.777 - 0.046ms returns 0
TA260 003:670.782 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:670.785 - 0.003ms returns 0
TA260 003:670.789 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:670.793 - 0.003ms returns 0
TA260 003:670.797 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:670.804 - 0.007ms returns 0
TA260 003:670.810 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:670.815 - 0.005ms returns 0x00000049
TA260 003:670.819 JLINK_Go()
TA260 003:670.828   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:673.615 - 2.795ms 
TA260 003:673.627 JLINK_IsHalted()
TA260 003:675.989   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:676.538 - 2.910ms returns TRUE
TA260 003:676.547 JLINK_ReadReg(R15 (PC))
TA260 003:676.554 - 0.006ms returns 0x20000000
TA260 003:676.558 JLINK_ClrBPEx(BPHandle = 0x00000049)
TA260 003:676.562 - 0.003ms returns 0x00
TA260 003:676.567 JLINK_ReadReg(R0)
TA260 003:676.571 - 0.003ms returns 0x00000000
TA260 003:729.700 JLINK_WriteMemEx(0x20000000, 0x00000002 Bytes, Flags = 0x02000000)
TA260 003:729.719   Data:  FE E7
TA260 003:729.738   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 003:730.196 - 0.495ms returns 0x2
TA260 003:730.206 JLINK_HasError()
TA260 003:733.620 JLINK_Close()
TA260 003:736.260   OnDisconnectTarget() start
TA260 003:736.285    J-Link Script File: Executing OnDisconnectTarget()
TA260 003:736.302   CPU_WriteMem(4 bytes @ 0xE0042004)
TA260 003:736.779   CPU_WriteMem(4 bytes @ 0xE0042008)
TA260 003:739.016   OnDisconnectTarget() end - Took 1.10ms
TA260 003:739.043   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:756.394 - 22.774ms
TA260 003:756.418   
TA260 003:756.422   Closed
