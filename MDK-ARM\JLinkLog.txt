TA260 000:004.322   SEGGER J-Link V8.16 Log File
TA260 000:004.436   DLL Compiled: Feb 26 2025 12:07:26
TA260 000:004.441   Logging started @ 2025-08-02 07:58
TA260 000:004.446   Process: G:\keil\keil arm\UV4\UV4.exe
TA260 000:004.469 - 4.462ms 
TA260 000:004.510 JLINK_SetWarnOutHandler(...)
TA260 000:004.516 - 0.008ms 
TA260 000:004.528 JLINK_OpenEx(...)
TA260 000:008.528   Firmware: J-Link V9 compiled May  7 2021 16:26:12
TA260 000:010.019   Firmware: J-Link V9 compiled May  7 2021 16:26:12
TA260 000:010.162   Decompressing FW timestamp took 98 us
TA260 000:017.697   Hardware: V9.60
TA260 000:017.721   S/N: 69655018
TA260 000:017.726   OEM: SEGGER
TA260 000:017.731   Feature(s): R<PERSON>, G<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>B<PERSON>, JFlash
TA260 000:019.090   Bootloader: (FW returned invalid version)
TA260 000:020.594   TELNET listener socket opened on port 19021
TA260 000:020.677   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
TA260 000:020.812   WEBSRV Webserver running on local port 19080
TA260 000:020.877   Looking for J-Link GUI Server exe at: G:\keil\keil arm\ARM\Segger\JLinkGUIServer.exe
TA260 000:020.957   Looking for J-Link GUI Server exe at: C:\Program Files (x86)\SEGGER\JLink_V630d\\JLinkGUIServer.exe
TA260 000:328.854   Failed to connect to J-Link GUI Server.
TA260 000:328.877 - 324.342ms returns "O.K."
TA260 000:328.891 JLINK_GetEmuCaps()
TA260 000:328.897 - 0.004ms returns 0xB9FF7BBF
TA260 000:328.907 JLINK_TIF_GetAvailable(...)
TA260 000:329.300 - 0.393ms 
TA260 000:329.312 JLINK_SetErrorOutHandler(...)
TA260 000:329.316 - 0.003ms 
TA260 000:329.336 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\2025ele_ori\zuolan_stm32\MDK-ARM\JLinkSettings.ini"", ...). 
TA260 000:340.034 - 10.699ms returns 0x00
TA260 000:342.904 JLINK_ExecCommand("Device = STM32F429IGTx", ...). 
TA260 000:344.095   Flash bank @ 0x90000000: SFL: Parsing sectorization info from ELF file
TA260 000:344.107     FlashDevice.SectorInfo[0]: .SectorSize = 0x00010000, .SectorStartAddr = 0x00000000
TA260 000:348.641   Device "STM32F429IG" selected.
TA260 000:348.863 - 5.943ms returns 0x00
TA260 000:348.873 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
TA260 000:348.893   ERROR: Unknown command
TA260 000:348.899 - 0.012ms returns 0x01
TA260 000:348.904 JLINK_GetHardwareVersion()
TA260 000:348.908 - 0.003ms returns 96000
TA260 000:348.912 JLINK_GetDLLVersion()
TA260 000:348.915 - 0.003ms returns 81600
TA260 000:348.920 JLINK_GetOEMString(...)
TA260 000:348.924 JLINK_GetFirmwareString(...)
TA260 000:348.928 - 0.003ms 
TA260 000:353.112 JLINK_GetDLLVersion()
TA260 000:353.125 - 0.013ms returns 81600
TA260 000:353.129 JLINK_GetCompileDateTime()
TA260 000:353.133 - 0.003ms 
TA260 000:354.451 JLINK_GetFirmwareString(...)
TA260 000:354.461 - 0.010ms 
TA260 000:355.841 JLINK_GetHardwareVersion()
TA260 000:355.853 - 0.011ms returns 96000
TA260 000:357.562 JLINK_GetSN()
TA260 000:357.573 - 0.010ms returns 69655018
TA260 000:359.011 JLINK_GetOEMString(...)
TA260 000:361.677 JLINK_TIF_Select(JLINKARM_TIF_SWD)
TA260 000:363.160 - 1.484ms returns 0x00
TA260 000:363.175 JLINK_HasError()
TA260 000:363.188 JLINK_SetSpeed(5000)
TA260 000:363.496 - 0.310ms 
TA260 000:363.503 JLINK_GetId()
TA260 000:365.264   InitTarget() start
TA260 000:365.283    J-Link Script File: Executing InitTarget()
TA260 000:367.255   SWD selected. Executing JTAG -> SWD switching sequence.
TA260 000:371.776   DAP initialized successfully.
TA260 000:384.123   InitTarget() end - Took 17.2ms
TA260 000:386.526   Found SW-DP with ID 0x2BA01477
TA260 000:391.724   DPIDR: 0x2BA01477
TA260 000:393.158   CoreSight SoC-400 or earlier
TA260 000:394.510   Scanning AP map to find all available APs
TA260 000:396.952   AP[1]: Stopped AP scan as end of AP map has been reached
TA260 000:398.593   AP[0]: AHB-AP (IDR: 0x24770011, ADDR: 0x00000000)
TA260 000:400.057   Iterating through AP map to find AHB-AP to use
TA260 000:402.714   AP[0]: Core found
TA260 000:404.050   AP[0]: AHB-AP ROM base: 0xE00FF000
TA260 000:406.399   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
TA260 000:407.897   Found Cortex-M4 r0p1, Little endian.
TA260 000:408.680   -- Max. mem block: 0x00010C40
TA260 000:409.583   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:410.066   CPU_ReadMem(4 bytes @ 0x********)
TA260 000:412.009   FPUnit: 6 code (BP) slots and 2 literal slots
TA260 000:412.023   CPU_ReadMem(4 bytes @ 0xE000EDFC)
TA260 000:412.532   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:413.023   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:413.503   CPU_WriteMem(4 bytes @ 0xE0001000)
TA260 000:413.979   CPU_ReadMem(4 bytes @ 0xE000ED88)
TA260 000:414.459   CPU_WriteMem(4 bytes @ 0xE000ED88)
TA260 000:414.984   CPU_ReadMem(4 bytes @ 0xE000ED88)
TA260 000:415.481   CPU_WriteMem(4 bytes @ 0xE000ED88)
TA260 000:417.891   CoreSight components:
TA260 000:419.384   ROMTbl[0] @ E00FF000
TA260 000:419.400   CPU_ReadMem(64 bytes @ 0xE00FF000)
TA260 000:420.121   CPU_ReadMem(32 bytes @ 0xE000EFE0)
TA260 000:422.367   [0][0]: E000E000 CID B105E00D PID 000BB00C SCS-M7
TA260 000:422.384   CPU_ReadMem(32 bytes @ 0xE0001FE0)
TA260 000:424.420   [0][1]: E0001000 CID B105E00D PID 003BB002 DWT
TA260 000:424.435   CPU_ReadMem(32 bytes @ 0xE0002FE0)
TA260 000:426.418   [0][2]: ******** CID B105E00D PID 002BB003 FPB
TA260 000:426.433   CPU_ReadMem(32 bytes @ 0xE0000FE0)
TA260 000:428.776   [0][3]: ******** CID B105E00D PID 003BB001 ITM
TA260 000:428.792   CPU_ReadMem(32 bytes @ 0xE0040FE0)
TA260 000:430.864   [0][4]: ******** CID B105900D PID 000BB9A1 TPIU
TA260 000:430.879   CPU_ReadMem(32 bytes @ 0xE0041FE0)
TA260 000:432.817   [0][5]: ******** CID B105900D PID 000BB925 ETM
TA260 000:433.314 - 69.810ms returns 0x2BA01477
TA260 000:433.359 JLINK_GetDLLVersion()
TA260 000:433.363 - 0.004ms returns 81600
TA260 000:433.372 JLINK_CORE_GetFound()
TA260 000:433.377 - 0.004ms returns 0xE0000FF
TA260 000:433.381 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
TA260 000:433.386   Value=0xE00FF000
TA260 000:433.391 - 0.010ms returns 0
TA260 000:434.742 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
TA260 000:434.752   Value=0xE00FF000
TA260 000:434.758 - 0.016ms returns 0
TA260 000:434.762 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
TA260 000:434.766   Value=0x********
TA260 000:434.771 - 0.008ms returns 0
TA260 000:434.776 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
TA260 000:434.799   CPU_ReadMem(32 bytes @ 0xE0041FD0)
TA260 000:435.403   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
TA260 000:435.409 - 0.633ms returns 32 (0x20)
TA260 000:435.415 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
TA260 000:435.418   Value=0x00000000
TA260 000:435.425 - 0.010ms returns 0
TA260 000:435.429 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
TA260 000:435.433   Value=0x********
TA260 000:435.438 - 0.009ms returns 0
TA260 000:435.443 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
TA260 000:435.446   Value=0x********
TA260 000:435.451 - 0.008ms returns 0
TA260 000:435.455 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
TA260 000:435.458   Value=0xE0001000
TA260 000:435.463 - 0.008ms returns 0
TA260 000:435.467 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
TA260 000:435.471   Value=0x********
TA260 000:435.475 - 0.008ms returns 0
TA260 000:435.480 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
TA260 000:435.483   Value=0xE000E000
TA260 000:435.488 - 0.008ms returns 0
TA260 000:435.492 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
TA260 000:435.496   Value=0xE000EDF0
TA260 000:435.501 - 0.008ms returns 0
TA260 000:435.505 JLINK_GetDebugInfo(0x01 = Unknown)
TA260 000:435.508   Value=0x00000001
TA260 000:435.513 - 0.008ms returns 0
TA260 000:435.518 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
TA260 000:435.523   CPU_ReadMem(4 bytes @ 0xE000ED00)
TA260 000:436.024   Data:  41 C2 0F 41
TA260 000:436.033   Debug reg: CPUID
TA260 000:436.038 - 0.520ms returns 1 (0x1)
TA260 000:436.044 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
TA260 000:436.050   Value=0x00000000
TA260 000:436.055 - 0.011ms returns 0
TA260 000:436.060 JLINK_HasError()
TA260 000:436.065 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
TA260 000:436.069 - 0.004ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
TA260 000:436.073 JLINK_Reset()
TA260 000:436.079   JLINK_GetResetTypeDesc
TA260 000:436.082   - 0.003ms 
TA260 000:437.769   Reset type: NORMAL (https://wiki.segger.com/J-Link_Reset_Strategies)
TA260 000:437.795   CPU is running
TA260 000:437.802   CPU_WriteMem(4 bytes @ 0xE000EDF0)
TA260 000:438.267   CPU is running
TA260 000:438.280   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:440.376   Reset: Halt core after reset via DEMCR.VC_CORERESET.
TA260 000:442.512   Reset: Reset device via AIRCR.SYSRESETREQ.
TA260 000:442.525   CPU is running
TA260 000:442.531   CPU_WriteMem(4 bytes @ 0xE000ED0C)
TA260 000:497.191   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:497.686   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:500.723   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:506.959   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:510.459   CPU_WriteMem(4 bytes @ 0x********)
TA260 000:510.974   CPU_ReadMem(4 bytes @ 0xE000EDFC)
TA260 000:511.492   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:511.984 - 75.910ms 
TA260 000:512.000 JLINK_Halt()
TA260 000:512.004 - 0.003ms returns 0x00
TA260 000:512.009 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
TA260 000:512.016   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:512.502   Data:  03 00 03 00
TA260 000:512.510   Debug reg: DHCSR
TA260 000:512.515 - 0.506ms returns 1 (0x1)
TA260 000:512.522 JLINK_WriteU32(0xE000EDF0, 0xA05F0003)
TA260 000:512.526   Debug reg: DHCSR
TA260 000:512.730   CPU_WriteMem(4 bytes @ 0xE000EDF0)
TA260 000:513.245 - 0.721ms returns 0 (0x00000000)
TA260 000:513.261 JLINK_WriteU32(0xE000EDFC, 0x01000000)
TA260 000:513.265   Debug reg: DEMCR
TA260 000:513.275   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:513.788 - 0.526ms returns 0 (0x00000000)
TA260 000:520.605 JLINK_GetHWStatus(...)
TA260 000:521.017 - 0.411ms returns 0
TA260 000:525.258 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
TA260 000:525.270 - 0.012ms returns 0x06
TA260 000:525.275 JLINK_GetNumBPUnits(Type = 0xF0)
TA260 000:525.279 - 0.003ms returns 0x2000
TA260 000:525.283 JLINK_GetNumWPUnits()
TA260 000:525.287 - 0.003ms returns 4
TA260 000:529.363 JLINK_GetSpeed()
TA260 000:529.379 - 0.015ms returns 4000
TA260 000:532.450 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
TA260 000:532.471   CPU_ReadMem(4 bytes @ 0xE000E004)
TA260 000:533.008   Data:  02 00 00 00
TA260 000:533.016 - 0.566ms returns 1 (0x1)
TA260 000:533.022 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
TA260 000:533.028   CPU_ReadMem(4 bytes @ 0xE000E004)
TA260 000:533.493   Data:  02 00 00 00
TA260 000:533.499 - 0.476ms returns 1 (0x1)
TA260 000:533.504 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
TA260 000:533.508   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
TA260 000:533.516   CPU_WriteMem(28 bytes @ 0xE0001000)
TA260 000:534.116 - 0.612ms returns 0x1C
TA260 000:534.125 JLINK_Halt()
TA260 000:534.128 - 0.003ms returns 0x00
TA260 000:534.132 JLINK_IsHalted()
TA260 000:534.136 - 0.004ms returns TRUE
TA260 000:536.148 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 000:536.156   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 000:536.352   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 000:538.232 - 2.084ms returns 0x184
TA260 000:538.274 JLINK_HasError()
TA260 000:538.280 JLINK_WriteReg(R0, 0x08000000)
TA260 000:538.287 - 0.006ms returns 0
TA260 000:538.291 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 000:538.295 - 0.003ms returns 0
TA260 000:538.299 JLINK_WriteReg(R2, 0x00000001)
TA260 000:538.302 - 0.003ms returns 0
TA260 000:538.306 JLINK_WriteReg(R3, 0x00000000)
TA260 000:538.310 - 0.003ms returns 0
TA260 000:538.314 JLINK_WriteReg(R4, 0x00000000)
TA260 000:538.317 - 0.003ms returns 0
TA260 000:538.321 JLINK_WriteReg(R5, 0x00000000)
TA260 000:538.328 - 0.006ms returns 0
TA260 000:538.333 JLINK_WriteReg(R6, 0x00000000)
TA260 000:538.337 - 0.003ms returns 0
TA260 000:538.341 JLINK_WriteReg(R7, 0x00000000)
TA260 000:538.344 - 0.003ms returns 0
TA260 000:538.360 JLINK_WriteReg(R8, 0x00000000)
TA260 000:538.363 - 0.014ms returns 0
TA260 000:538.367 JLINK_WriteReg(R9, 0x20000180)
TA260 000:538.371 - 0.003ms returns 0
TA260 000:538.375 JLINK_WriteReg(R10, 0x00000000)
TA260 000:538.378 - 0.003ms returns 0
TA260 000:538.382 JLINK_WriteReg(R11, 0x00000000)
TA260 000:538.385 - 0.003ms returns 0
TA260 000:538.389 JLINK_WriteReg(R12, 0x00000000)
TA260 000:538.393 - 0.003ms returns 0
TA260 000:538.397 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:538.401 - 0.004ms returns 0
TA260 000:538.405 JLINK_WriteReg(R14, 0x20000001)
TA260 000:538.409 - 0.003ms returns 0
TA260 000:538.416 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 000:538.420 - 0.007ms returns 0
TA260 000:538.424 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:538.427 - 0.003ms returns 0
TA260 000:538.431 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:538.435 - 0.003ms returns 0
TA260 000:538.439 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:538.442 - 0.003ms returns 0
TA260 000:538.446 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:538.450 - 0.003ms returns 0
TA260 000:538.455 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:538.461   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:538.936 - 0.481ms returns 0x00000001
TA260 000:538.943 JLINK_Go()
TA260 000:538.948   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 000:539.469   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:539.922   CPU_WriteMem(4 bytes @ 0xE0002008)
TA260 000:539.928   CPU_WriteMem(4 bytes @ 0xE000200C)
TA260 000:539.933   CPU_WriteMem(4 bytes @ 0xE0002010)
TA260 000:539.938   CPU_WriteMem(4 bytes @ 0xE0002014)
TA260 000:539.943   CPU_WriteMem(4 bytes @ 0xE0002018)
TA260 000:539.948   CPU_WriteMem(4 bytes @ 0xE000201C)
TA260 000:541.163   CPU_WriteMem(4 bytes @ 0xE0001004)
TA260 000:545.483   Memory map 'after startup completion point' is active
TA260 000:545.497 - 6.553ms 
TA260 000:545.508 JLINK_IsHalted()
TA260 000:547.823   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:548.341 - 2.832ms returns TRUE
TA260 000:548.362 JLINK_ReadReg(R15 (PC))
TA260 000:548.368 - 0.006ms returns 0x20000000
TA260 000:548.400 JLINK_ClrBPEx(BPHandle = 0x00000001)
TA260 000:548.406 - 0.005ms returns 0x00
TA260 000:548.410 JLINK_ReadReg(R0)
TA260 000:548.414 - 0.003ms returns 0x00000000
TA260 000:548.700 JLINK_HasError()
TA260 000:548.708 JLINK_WriteReg(R0, 0x08000000)
TA260 000:548.713 - 0.004ms returns 0
TA260 000:548.717 JLINK_WriteReg(R1, 0x00004000)
TA260 000:548.720 - 0.003ms returns 0
TA260 000:548.724 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:548.728 - 0.003ms returns 0
TA260 000:548.732 JLINK_WriteReg(R3, 0x00000000)
TA260 000:548.735 - 0.003ms returns 0
TA260 000:548.739 JLINK_WriteReg(R4, 0x00000000)
TA260 000:548.743 - 0.003ms returns 0
TA260 000:548.747 JLINK_WriteReg(R5, 0x00000000)
TA260 000:548.750 - 0.003ms returns 0
TA260 000:548.754 JLINK_WriteReg(R6, 0x00000000)
TA260 000:548.758 - 0.003ms returns 0
TA260 000:548.762 JLINK_WriteReg(R7, 0x00000000)
TA260 000:548.765 - 0.003ms returns 0
TA260 000:548.769 JLINK_WriteReg(R8, 0x00000000)
TA260 000:548.773 - 0.003ms returns 0
TA260 000:548.777 JLINK_WriteReg(R9, 0x20000180)
TA260 000:548.780 - 0.003ms returns 0
TA260 000:548.784 JLINK_WriteReg(R10, 0x00000000)
TA260 000:548.788 - 0.003ms returns 0
TA260 000:548.792 JLINK_WriteReg(R11, 0x00000000)
TA260 000:548.795 - 0.003ms returns 0
TA260 000:548.799 JLINK_WriteReg(R12, 0x00000000)
TA260 000:548.802 - 0.003ms returns 0
TA260 000:548.806 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:548.810 - 0.003ms returns 0
TA260 000:548.814 JLINK_WriteReg(R14, 0x20000001)
TA260 000:548.817 - 0.003ms returns 0
TA260 000:548.822 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 000:548.825 - 0.003ms returns 0
TA260 000:548.829 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:548.832 - 0.003ms returns 0
TA260 000:548.868 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:548.873 - 0.005ms returns 0
TA260 000:548.877 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:548.881 - 0.003ms returns 0
TA260 000:548.885 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:548.888 - 0.003ms returns 0
TA260 000:548.893 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:548.897 - 0.004ms returns 0x00000002
TA260 000:548.901 JLINK_Go()
TA260 000:548.909   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:551.773 - 2.871ms 
TA260 000:551.782 JLINK_IsHalted()
TA260 000:554.136   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:554.638 - 2.856ms returns TRUE
TA260 000:554.644 JLINK_ReadReg(R15 (PC))
TA260 000:554.649 - 0.004ms returns 0x20000000
TA260 000:554.653 JLINK_ClrBPEx(BPHandle = 0x00000002)
TA260 000:554.657 - 0.003ms returns 0x00
TA260 000:554.661 JLINK_ReadReg(R0)
TA260 000:554.664 - 0.003ms returns 0x00000001
TA260 000:554.669 JLINK_HasError()
TA260 000:554.673 JLINK_WriteReg(R0, 0x08000000)
TA260 000:554.677 - 0.003ms returns 0
TA260 000:554.681 JLINK_WriteReg(R1, 0x00004000)
TA260 000:554.684 - 0.003ms returns 0
TA260 000:554.689 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:554.692 - 0.003ms returns 0
TA260 000:554.696 JLINK_WriteReg(R3, 0x00000000)
TA260 000:554.699 - 0.003ms returns 0
TA260 000:554.703 JLINK_WriteReg(R4, 0x00000000)
TA260 000:554.707 - 0.003ms returns 0
TA260 000:554.711 JLINK_WriteReg(R5, 0x00000000)
TA260 000:554.714 - 0.003ms returns 0
TA260 000:554.718 JLINK_WriteReg(R6, 0x00000000)
TA260 000:554.721 - 0.003ms returns 0
TA260 000:554.725 JLINK_WriteReg(R7, 0x00000000)
TA260 000:554.729 - 0.003ms returns 0
TA260 000:554.733 JLINK_WriteReg(R8, 0x00000000)
TA260 000:554.736 - 0.003ms returns 0
TA260 000:554.740 JLINK_WriteReg(R9, 0x20000180)
TA260 000:554.743 - 0.003ms returns 0
TA260 000:554.747 JLINK_WriteReg(R10, 0x00000000)
TA260 000:554.751 - 0.003ms returns 0
TA260 000:554.755 JLINK_WriteReg(R11, 0x00000000)
TA260 000:554.758 - 0.003ms returns 0
TA260 000:554.762 JLINK_WriteReg(R12, 0x00000000)
TA260 000:554.766 - 0.003ms returns 0
TA260 000:554.770 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:554.773 - 0.003ms returns 0
TA260 000:554.777 JLINK_WriteReg(R14, 0x20000001)
TA260 000:554.781 - 0.003ms returns 0
TA260 000:554.785 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 000:554.788 - 0.003ms returns 0
TA260 000:554.792 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:554.796 - 0.003ms returns 0
TA260 000:554.800 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:554.803 - 0.003ms returns 0
TA260 000:554.807 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:554.811 - 0.003ms returns 0
TA260 000:554.815 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:554.818 - 0.003ms returns 0
TA260 000:554.822 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:554.826 - 0.003ms returns 0x00000003
TA260 000:554.830 JLINK_Go()
TA260 000:554.836   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:557.569 - 2.738ms 
TA260 000:557.582 JLINK_IsHalted()
TA260 000:558.071 - 0.489ms returns FALSE
TA260 000:558.086 JLINK_HasError()
TA260 000:572.424 JLINK_IsHalted()
TA260 000:572.875 - 0.450ms returns FALSE
TA260 000:572.891 JLINK_HasError()
TA260 000:574.429 JLINK_IsHalted()
TA260 000:574.935 - 0.505ms returns FALSE
TA260 000:574.943 JLINK_HasError()
TA260 000:576.424 JLINK_IsHalted()
TA260 000:576.979 - 0.555ms returns FALSE
TA260 000:576.987 JLINK_HasError()
TA260 000:578.428 JLINK_IsHalted()
TA260 000:578.869 - 0.441ms returns FALSE
TA260 000:578.883 JLINK_HasError()
TA260 000:580.424 JLINK_IsHalted()
TA260 000:580.922 - 0.498ms returns FALSE
TA260 000:580.928 JLINK_HasError()
TA260 000:582.930 JLINK_IsHalted()
TA260 000:583.410 - 0.479ms returns FALSE
TA260 000:583.416 JLINK_HasError()
TA260 000:584.936 JLINK_IsHalted()
TA260 000:585.422 - 0.487ms returns FALSE
TA260 000:585.429 JLINK_HasError()
TA260 000:586.931 JLINK_IsHalted()
TA260 000:587.433 - 0.501ms returns FALSE
TA260 000:587.440 JLINK_HasError()
TA260 000:588.936 JLINK_IsHalted()
TA260 000:589.448 - 0.511ms returns FALSE
TA260 000:589.494 JLINK_HasError()
TA260 000:590.930 JLINK_IsHalted()
TA260 000:591.365 - 0.434ms returns FALSE
TA260 000:591.371 JLINK_HasError()
TA260 000:592.432 JLINK_IsHalted()
TA260 000:592.922 - 0.489ms returns FALSE
TA260 000:592.928 JLINK_HasError()
TA260 000:594.437 JLINK_IsHalted()
TA260 000:594.914 - 0.477ms returns FALSE
TA260 000:594.920 JLINK_HasError()
TA260 000:597.437 JLINK_IsHalted()
TA260 000:597.928 - 0.491ms returns FALSE
TA260 000:597.934 JLINK_HasError()
TA260 000:599.434 JLINK_IsHalted()
TA260 000:599.932 - 0.497ms returns FALSE
TA260 000:599.938 JLINK_HasError()
TA260 000:601.937 JLINK_IsHalted()
TA260 000:602.434 - 0.496ms returns FALSE
TA260 000:602.439 JLINK_HasError()
TA260 000:603.942 JLINK_IsHalted()
TA260 000:604.451 - 0.509ms returns FALSE
TA260 000:604.457 JLINK_HasError()
TA260 000:605.947 JLINK_IsHalted()
TA260 000:606.388 - 0.441ms returns FALSE
TA260 000:606.395 JLINK_HasError()
TA260 000:607.944 JLINK_IsHalted()
TA260 000:608.391 - 0.446ms returns FALSE
TA260 000:608.397 JLINK_HasError()
TA260 000:609.942 JLINK_IsHalted()
TA260 000:610.434 - 0.492ms returns FALSE
TA260 000:610.441 JLINK_HasError()
TA260 000:611.941 JLINK_IsHalted()
TA260 000:612.434 - 0.492ms returns FALSE
TA260 000:612.440 JLINK_HasError()
TA260 000:614.445 JLINK_IsHalted()
TA260 000:614.931 - 0.486ms returns FALSE
TA260 000:614.937 JLINK_HasError()
TA260 000:616.449 JLINK_IsHalted()
TA260 000:616.933 - 0.483ms returns FALSE
TA260 000:616.942 JLINK_HasError()
TA260 000:618.446 JLINK_IsHalted()
TA260 000:618.923 - 0.476ms returns FALSE
TA260 000:618.928 JLINK_HasError()
TA260 000:620.447 JLINK_IsHalted()
TA260 000:620.913 - 0.466ms returns FALSE
TA260 000:620.918 JLINK_HasError()
TA260 000:621.948 JLINK_IsHalted()
TA260 000:622.408 - 0.460ms returns FALSE
TA260 000:622.414 JLINK_HasError()
TA260 000:623.953 JLINK_IsHalted()
TA260 000:624.479 - 0.526ms returns FALSE
TA260 000:624.489 JLINK_HasError()
TA260 000:625.955 JLINK_IsHalted()
TA260 000:626.413 - 0.457ms returns FALSE
TA260 000:626.425 JLINK_HasError()
TA260 000:627.955 JLINK_IsHalted()
TA260 000:628.433 - 0.477ms returns FALSE
TA260 000:628.439 JLINK_HasError()
TA260 000:629.951 JLINK_IsHalted()
TA260 000:630.466 - 0.515ms returns FALSE
TA260 000:630.478 JLINK_HasError()
TA260 000:631.953 JLINK_IsHalted()
TA260 000:632.422 - 0.469ms returns FALSE
TA260 000:632.428 JLINK_HasError()
TA260 000:634.455 JLINK_IsHalted()
TA260 000:634.956 - 0.501ms returns FALSE
TA260 000:634.962 JLINK_HasError()
TA260 000:636.455 JLINK_IsHalted()
TA260 000:636.936 - 0.480ms returns FALSE
TA260 000:636.945 JLINK_HasError()
TA260 000:638.459 JLINK_IsHalted()
TA260 000:638.961 - 0.501ms returns FALSE
TA260 000:638.967 JLINK_HasError()
TA260 000:640.456 JLINK_IsHalted()
TA260 000:641.017 - 0.559ms returns FALSE
TA260 000:641.028 JLINK_HasError()
TA260 000:642.961 JLINK_IsHalted()
TA260 000:643.457 - 0.495ms returns FALSE
TA260 000:643.463 JLINK_HasError()
TA260 000:644.961 JLINK_IsHalted()
TA260 000:645.434 - 0.472ms returns FALSE
TA260 000:645.440 JLINK_HasError()
TA260 000:646.964 JLINK_IsHalted()
TA260 000:647.401 - 0.436ms returns FALSE
TA260 000:647.407 JLINK_HasError()
TA260 000:648.965 JLINK_IsHalted()
TA260 000:649.415 - 0.450ms returns FALSE
TA260 000:649.427 JLINK_HasError()
TA260 000:650.961 JLINK_IsHalted()
TA260 000:651.457 - 0.496ms returns FALSE
TA260 000:651.463 JLINK_HasError()
TA260 000:653.469 JLINK_IsHalted()
TA260 000:653.953 - 0.484ms returns FALSE
TA260 000:653.959 JLINK_HasError()
TA260 000:655.468 JLINK_IsHalted()
TA260 000:655.958 - 0.490ms returns FALSE
TA260 000:655.965 JLINK_HasError()
TA260 000:657.470 JLINK_IsHalted()
TA260 000:657.915 - 0.444ms returns FALSE
TA260 000:657.926 JLINK_HasError()
TA260 000:659.469 JLINK_IsHalted()
TA260 000:659.977 - 0.507ms returns FALSE
TA260 000:659.983 JLINK_HasError()
TA260 000:661.970 JLINK_IsHalted()
TA260 000:662.459 - 0.488ms returns FALSE
TA260 000:662.466 JLINK_HasError()
TA260 000:663.974 JLINK_IsHalted()
TA260 000:664.491 - 0.516ms returns FALSE
TA260 000:664.497 JLINK_HasError()
TA260 000:665.973 JLINK_IsHalted()
TA260 000:666.463 - 0.489ms returns FALSE
TA260 000:666.469 JLINK_HasError()
TA260 000:667.977 JLINK_IsHalted()
TA260 000:668.462 - 0.484ms returns FALSE
TA260 000:668.469 JLINK_HasError()
TA260 000:669.974 JLINK_IsHalted()
TA260 000:670.467 - 0.493ms returns FALSE
TA260 000:670.475 JLINK_HasError()
TA260 000:671.974 JLINK_IsHalted()
TA260 000:672.519 - 0.545ms returns FALSE
TA260 000:672.530 JLINK_HasError()
TA260 000:675.482 JLINK_IsHalted()
TA260 000:675.979 - 0.496ms returns FALSE
TA260 000:675.991 JLINK_HasError()
TA260 000:677.480 JLINK_IsHalted()
TA260 000:677.962 - 0.482ms returns FALSE
TA260 000:677.975 JLINK_HasError()
TA260 000:679.483 JLINK_IsHalted()
TA260 000:679.956 - 0.473ms returns FALSE
TA260 000:679.962 JLINK_HasError()
TA260 000:681.480 JLINK_IsHalted()
TA260 000:681.956 - 0.476ms returns FALSE
TA260 000:681.962 JLINK_HasError()
TA260 000:683.985 JLINK_IsHalted()
TA260 000:684.465 - 0.480ms returns FALSE
TA260 000:684.471 JLINK_HasError()
TA260 000:685.984 JLINK_IsHalted()
TA260 000:686.467 - 0.482ms returns FALSE
TA260 000:686.473 JLINK_HasError()
TA260 000:687.985 JLINK_IsHalted()
TA260 000:688.424 - 0.439ms returns FALSE
TA260 000:688.430 JLINK_HasError()
TA260 000:689.987 JLINK_IsHalted()
TA260 000:690.496 - 0.509ms returns FALSE
TA260 000:690.505 JLINK_HasError()
TA260 000:692.002 JLINK_IsHalted()
TA260 000:692.641 - 0.639ms returns FALSE
TA260 000:692.648 JLINK_HasError()
TA260 000:694.491 JLINK_IsHalted()
TA260 000:694.970 - 0.478ms returns FALSE
TA260 000:694.977 JLINK_HasError()
TA260 000:696.490 JLINK_IsHalted()
TA260 000:696.936 - 0.445ms returns FALSE
TA260 000:696.944 JLINK_HasError()
TA260 000:698.491 JLINK_IsHalted()
TA260 000:698.910 - 0.419ms returns FALSE
TA260 000:698.916 JLINK_HasError()
TA260 000:700.491 JLINK_IsHalted()
TA260 000:701.001 - 0.509ms returns FALSE
TA260 000:701.006 JLINK_HasError()
TA260 000:702.993 JLINK_IsHalted()
TA260 000:703.639 - 0.645ms returns FALSE
TA260 000:703.651 JLINK_HasError()
TA260 000:705.999 JLINK_IsHalted()
TA260 000:706.753 - 0.753ms returns FALSE
TA260 000:706.764 JLINK_HasError()
TA260 000:707.997 JLINK_IsHalted()
TA260 000:708.513 - 0.515ms returns FALSE
TA260 000:708.520 JLINK_HasError()
TA260 000:709.996 JLINK_IsHalted()
TA260 000:710.513 - 0.516ms returns FALSE
TA260 000:710.518 JLINK_HasError()
TA260 000:711.996 JLINK_IsHalted()
TA260 000:712.467 - 0.471ms returns FALSE
TA260 000:712.473 JLINK_HasError()
TA260 000:714.500 JLINK_IsHalted()
TA260 000:714.967 - 0.466ms returns FALSE
TA260 000:714.973 JLINK_HasError()
TA260 000:716.499 JLINK_IsHalted()
TA260 000:717.163 - 0.664ms returns FALSE
TA260 000:717.170 JLINK_HasError()
TA260 000:718.501 JLINK_IsHalted()
TA260 000:719.027 - 0.525ms returns FALSE
TA260 000:719.038 JLINK_HasError()
TA260 000:720.503 JLINK_IsHalted()
TA260 000:721.012 - 0.509ms returns FALSE
TA260 000:721.020 JLINK_HasError()
TA260 000:723.004 JLINK_IsHalted()
TA260 000:723.459 - 0.455ms returns FALSE
TA260 000:723.466 JLINK_HasError()
TA260 000:725.005 JLINK_IsHalted()
TA260 000:725.480 - 0.474ms returns FALSE
TA260 000:725.490 JLINK_HasError()
TA260 000:727.006 JLINK_IsHalted()
TA260 000:727.501 - 0.494ms returns FALSE
TA260 000:727.508 JLINK_HasError()
TA260 000:729.009 JLINK_IsHalted()
TA260 000:729.538 - 0.528ms returns FALSE
TA260 000:729.544 JLINK_HasError()
TA260 000:731.004 JLINK_IsHalted()
TA260 000:731.502 - 0.497ms returns FALSE
TA260 000:731.508 JLINK_HasError()
TA260 000:733.511 JLINK_IsHalted()
TA260 000:734.106 - 0.594ms returns FALSE
TA260 000:734.115 JLINK_HasError()
TA260 000:735.513 JLINK_IsHalted()
TA260 000:736.012 - 0.499ms returns FALSE
TA260 000:736.018 JLINK_HasError()
TA260 000:737.513 JLINK_IsHalted()
TA260 000:737.973 - 0.459ms returns FALSE
TA260 000:737.986 JLINK_HasError()
TA260 000:739.511 JLINK_IsHalted()
TA260 000:739.969 - 0.456ms returns FALSE
TA260 000:739.976 JLINK_HasError()
TA260 000:742.014 JLINK_IsHalted()
TA260 000:742.491 - 0.477ms returns FALSE
TA260 000:742.497 JLINK_HasError()
TA260 000:744.017 JLINK_IsHalted()
TA260 000:744.536 - 0.519ms returns FALSE
TA260 000:744.542 JLINK_HasError()
TA260 000:746.018 JLINK_IsHalted()
TA260 000:746.535 - 0.516ms returns FALSE
TA260 000:746.541 JLINK_HasError()
TA260 000:748.019 JLINK_IsHalted()
TA260 000:748.517 - 0.498ms returns FALSE
TA260 000:748.523 JLINK_HasError()
TA260 000:750.019 JLINK_IsHalted()
TA260 000:750.459 - 0.439ms returns FALSE
TA260 000:750.475 JLINK_HasError()
TA260 000:752.017 JLINK_IsHalted()
TA260 000:752.491 - 0.473ms returns FALSE
TA260 000:752.500 JLINK_HasError()
TA260 000:754.522 JLINK_IsHalted()
TA260 000:755.005 - 0.482ms returns FALSE
TA260 000:755.011 JLINK_HasError()
TA260 000:756.523 JLINK_IsHalted()
TA260 000:757.003 - 0.479ms returns FALSE
TA260 000:757.015 JLINK_HasError()
TA260 000:758.525 JLINK_IsHalted()
TA260 000:759.026 - 0.500ms returns FALSE
TA260 000:759.032 JLINK_HasError()
TA260 000:760.522 JLINK_IsHalted()
TA260 000:761.023 - 0.501ms returns FALSE
TA260 000:761.028 JLINK_HasError()
TA260 000:763.026 JLINK_IsHalted()
TA260 000:763.523 - 0.497ms returns FALSE
TA260 000:763.529 JLINK_HasError()
TA260 000:765.026 JLINK_IsHalted()
TA260 000:765.538 - 0.511ms returns FALSE
TA260 000:765.547 JLINK_HasError()
TA260 000:767.029 JLINK_IsHalted()
TA260 000:767.536 - 0.506ms returns FALSE
TA260 000:767.542 JLINK_HasError()
TA260 000:769.031 JLINK_IsHalted()
TA260 000:769.516 - 0.484ms returns FALSE
TA260 000:769.522 JLINK_HasError()
TA260 000:771.026 JLINK_IsHalted()
TA260 000:771.513 - 0.486ms returns FALSE
TA260 000:771.519 JLINK_HasError()
TA260 000:773.534 JLINK_IsHalted()
TA260 000:774.037 - 0.502ms returns FALSE
TA260 000:774.042 JLINK_HasError()
TA260 000:775.535 JLINK_IsHalted()
TA260 000:776.023 - 0.487ms returns FALSE
TA260 000:776.033 JLINK_HasError()
TA260 000:777.535 JLINK_IsHalted()
TA260 000:777.981 - 0.445ms returns FALSE
TA260 000:777.988 JLINK_HasError()
TA260 000:779.533 JLINK_IsHalted()
TA260 000:780.024 - 0.490ms returns FALSE
TA260 000:780.030 JLINK_HasError()
TA260 000:781.536 JLINK_IsHalted()
TA260 000:781.976 - 0.440ms returns FALSE
TA260 000:781.982 JLINK_HasError()
TA260 000:784.041 JLINK_IsHalted()
TA260 000:784.524 - 0.482ms returns FALSE
TA260 000:784.530 JLINK_HasError()
TA260 000:786.039 JLINK_IsHalted()
TA260 000:786.559 - 0.519ms returns FALSE
TA260 000:786.565 JLINK_HasError()
TA260 000:788.040 JLINK_IsHalted()
TA260 000:788.479 - 0.439ms returns FALSE
TA260 000:788.485 JLINK_HasError()
TA260 000:790.041 JLINK_IsHalted()
TA260 000:790.649 - 0.607ms returns FALSE
TA260 000:790.659 JLINK_HasError()
TA260 000:792.063 JLINK_IsHalted()
TA260 000:792.669 - 0.605ms returns FALSE
TA260 000:792.675 JLINK_HasError()
TA260 000:794.545 JLINK_IsHalted()
TA260 000:795.023 - 0.477ms returns FALSE
TA260 000:795.028 JLINK_HasError()
TA260 000:796.545 JLINK_IsHalted()
TA260 000:797.129 - 0.584ms returns FALSE
TA260 000:797.141 JLINK_HasError()
TA260 000:798.548 JLINK_IsHalted()
TA260 000:799.061 - 0.512ms returns FALSE
TA260 000:799.067 JLINK_HasError()
TA260 000:800.547 JLINK_IsHalted()
TA260 000:801.035 - 0.487ms returns FALSE
TA260 000:801.044 JLINK_HasError()
TA260 000:803.048 JLINK_IsHalted()
TA260 000:803.513 - 0.464ms returns FALSE
TA260 000:803.519 JLINK_HasError()
TA260 000:805.051 JLINK_IsHalted()
TA260 000:805.547 - 0.496ms returns FALSE
TA260 000:805.553 JLINK_HasError()
TA260 000:807.052 JLINK_IsHalted()
TA260 000:807.523 - 0.470ms returns FALSE
TA260 000:807.536 JLINK_HasError()
TA260 000:809.051 JLINK_IsHalted()
TA260 000:809.548 - 0.496ms returns FALSE
TA260 000:809.554 JLINK_HasError()
TA260 000:811.049 JLINK_IsHalted()
TA260 000:811.537 - 0.488ms returns FALSE
TA260 000:811.543 JLINK_HasError()
TA260 000:813.557 JLINK_IsHalted()
TA260 000:814.024 - 0.466ms returns FALSE
TA260 000:814.032 JLINK_HasError()
TA260 000:816.556 JLINK_IsHalted()
TA260 000:817.013 - 0.455ms returns FALSE
TA260 000:817.026 JLINK_HasError()
TA260 000:818.559 JLINK_IsHalted()
TA260 000:819.069 - 0.509ms returns FALSE
TA260 000:819.076 JLINK_HasError()
TA260 000:820.558 JLINK_IsHalted()
TA260 000:821.057 - 0.499ms returns FALSE
TA260 000:821.064 JLINK_HasError()
TA260 000:823.059 JLINK_IsHalted()
TA260 000:823.561 - 0.502ms returns FALSE
TA260 000:823.568 JLINK_HasError()
TA260 000:825.061 JLINK_IsHalted()
TA260 000:825.522 - 0.461ms returns FALSE
TA260 000:825.528 JLINK_HasError()
TA260 000:827.062 JLINK_IsHalted()
TA260 000:827.548 - 0.486ms returns FALSE
TA260 000:827.555 JLINK_HasError()
TA260 000:829.064 JLINK_IsHalted()
TA260 000:829.496 - 0.432ms returns FALSE
TA260 000:829.503 JLINK_HasError()
TA260 000:831.060 JLINK_IsHalted()
TA260 000:831.558 - 0.497ms returns FALSE
TA260 000:831.564 JLINK_HasError()
TA260 000:833.566 JLINK_IsHalted()
TA260 000:834.057 - 0.491ms returns FALSE
TA260 000:834.063 JLINK_HasError()
TA260 000:835.568 JLINK_IsHalted()
TA260 000:836.076 - 0.507ms returns FALSE
TA260 000:836.085 JLINK_HasError()
TA260 000:837.568 JLINK_IsHalted()
TA260 000:838.048 - 0.479ms returns FALSE
TA260 000:838.054 JLINK_HasError()
TA260 000:839.567 JLINK_IsHalted()
TA260 000:840.026 - 0.459ms returns FALSE
TA260 000:840.033 JLINK_HasError()
TA260 000:842.069 JLINK_IsHalted()
TA260 000:842.637 - 0.567ms returns FALSE
TA260 000:842.643 JLINK_HasError()
TA260 000:844.073 JLINK_IsHalted()
TA260 000:844.650 - 0.576ms returns FALSE
TA260 000:844.656 JLINK_HasError()
TA260 000:846.072 JLINK_IsHalted()
TA260 000:846.742 - 0.670ms returns FALSE
TA260 000:846.749 JLINK_HasError()
TA260 000:848.074 JLINK_IsHalted()
TA260 000:848.646 - 0.571ms returns FALSE
TA260 000:848.652 JLINK_HasError()
TA260 000:850.071 JLINK_IsHalted()
TA260 000:850.547 - 0.475ms returns FALSE
TA260 000:850.552 JLINK_HasError()
TA260 000:852.076 JLINK_IsHalted()
TA260 000:852.715 - 0.638ms returns FALSE
TA260 000:852.721 JLINK_HasError()
TA260 000:854.576 JLINK_IsHalted()
TA260 000:855.066 - 0.489ms returns FALSE
TA260 000:855.071 JLINK_HasError()
TA260 000:856.578 JLINK_IsHalted()
TA260 000:857.158 - 0.579ms returns FALSE
TA260 000:857.170 JLINK_HasError()
TA260 000:858.579 JLINK_IsHalted()
TA260 000:859.248 - 0.669ms returns FALSE
TA260 000:859.259 JLINK_HasError()
TA260 000:860.579 JLINK_IsHalted()
TA260 000:861.069 - 0.489ms returns FALSE
TA260 000:861.077 JLINK_HasError()
TA260 000:863.083 JLINK_IsHalted()
TA260 000:863.637 - 0.553ms returns FALSE
TA260 000:863.643 JLINK_HasError()
TA260 000:865.084 JLINK_IsHalted()
TA260 000:865.645 - 0.561ms returns FALSE
TA260 000:865.651 JLINK_HasError()
TA260 000:867.085 JLINK_IsHalted()
TA260 000:867.568 - 0.482ms returns FALSE
TA260 000:867.575 JLINK_HasError()
TA260 000:869.085 JLINK_IsHalted()
TA260 000:869.645 - 0.560ms returns FALSE
TA260 000:869.652 JLINK_HasError()
TA260 000:871.083 JLINK_IsHalted()
TA260 000:871.558 - 0.474ms returns FALSE
TA260 000:871.563 JLINK_HasError()
TA260 000:873.593 JLINK_IsHalted()
TA260 000:874.104 - 0.510ms returns FALSE
TA260 000:874.111 JLINK_HasError()
TA260 000:875.593 JLINK_IsHalted()
TA260 000:876.060 - 0.466ms returns FALSE
TA260 000:876.070 JLINK_HasError()
TA260 000:877.593 JLINK_IsHalted()
TA260 000:878.107 - 0.513ms returns FALSE
TA260 000:878.120 JLINK_HasError()
TA260 000:879.592 JLINK_IsHalted()
TA260 000:880.078 - 0.485ms returns FALSE
TA260 000:880.084 JLINK_HasError()
TA260 000:881.591 JLINK_IsHalted()
TA260 000:882.105 - 0.513ms returns FALSE
TA260 000:882.111 JLINK_HasError()
TA260 000:884.096 JLINK_IsHalted()
TA260 000:884.637 - 0.541ms returns FALSE
TA260 000:884.643 JLINK_HasError()
TA260 000:886.095 JLINK_IsHalted()
TA260 000:886.743 - 0.647ms returns FALSE
TA260 000:886.749 JLINK_HasError()
TA260 000:888.098 JLINK_IsHalted()
TA260 000:888.648 - 0.550ms returns FALSE
TA260 000:888.654 JLINK_HasError()
TA260 000:890.097 JLINK_IsHalted()
TA260 000:890.779 - 0.681ms returns FALSE
TA260 000:890.790 JLINK_HasError()
TA260 000:892.102 JLINK_IsHalted()
TA260 000:892.647 - 0.545ms returns FALSE
TA260 000:892.655 JLINK_HasError()
TA260 000:894.601 JLINK_IsHalted()
TA260 000:895.085 - 0.483ms returns FALSE
TA260 000:895.091 JLINK_HasError()
TA260 000:896.601 JLINK_IsHalted()
TA260 000:897.072 - 0.471ms returns FALSE
TA260 000:897.081 JLINK_HasError()
TA260 000:898.605 JLINK_IsHalted()
TA260 000:899.106 - 0.501ms returns FALSE
TA260 000:899.112 JLINK_HasError()
TA260 000:900.602 JLINK_IsHalted()
TA260 000:901.081 - 0.479ms returns FALSE
TA260 000:901.087 JLINK_HasError()
TA260 000:903.105 JLINK_IsHalted()
TA260 000:903.653 - 0.548ms returns FALSE
TA260 000:903.660 JLINK_HasError()
TA260 000:905.105 JLINK_IsHalted()
TA260 000:905.652 - 0.546ms returns FALSE
TA260 000:905.663 JLINK_HasError()
TA260 000:907.107 JLINK_IsHalted()
TA260 000:907.554 - 0.446ms returns FALSE
TA260 000:907.569 JLINK_HasError()
TA260 000:909.108 JLINK_IsHalted()
TA260 000:909.570 - 0.460ms returns FALSE
TA260 000:909.577 JLINK_HasError()
TA260 000:911.105 JLINK_IsHalted()
TA260 000:911.646 - 0.541ms returns FALSE
TA260 000:911.652 JLINK_HasError()
TA260 000:913.612 JLINK_IsHalted()
TA260 000:914.106 - 0.494ms returns FALSE
TA260 000:914.112 JLINK_HasError()
TA260 000:915.613 JLINK_IsHalted()
TA260 000:916.070 - 0.457ms returns FALSE
TA260 000:916.079 JLINK_HasError()
TA260 000:917.614 JLINK_IsHalted()
TA260 000:918.069 - 0.454ms returns FALSE
TA260 000:918.076 JLINK_HasError()
TA260 000:919.612 JLINK_IsHalted()
TA260 000:920.104 - 0.492ms returns FALSE
TA260 000:920.110 JLINK_HasError()
TA260 000:922.116 JLINK_IsHalted()
TA260 000:922.638 - 0.522ms returns FALSE
TA260 000:922.644 JLINK_HasError()
TA260 000:925.116 JLINK_IsHalted()
TA260 000:925.649 - 0.532ms returns FALSE
TA260 000:925.655 JLINK_HasError()
TA260 000:927.120 JLINK_IsHalted()
TA260 000:927.649 - 0.528ms returns FALSE
TA260 000:927.666 JLINK_HasError()
TA260 000:929.118 JLINK_IsHalted()
TA260 000:929.649 - 0.530ms returns FALSE
TA260 000:929.655 JLINK_HasError()
TA260 000:931.115 JLINK_IsHalted()
TA260 000:931.559 - 0.443ms returns FALSE
TA260 000:931.565 JLINK_HasError()
TA260 000:932.619 JLINK_IsHalted()
TA260 000:933.069 - 0.450ms returns FALSE
TA260 000:933.075 JLINK_HasError()
TA260 000:934.621 JLINK_IsHalted()
TA260 000:935.091 - 0.470ms returns FALSE
TA260 000:935.096 JLINK_HasError()
TA260 000:936.777 JLINK_IsHalted()
TA260 000:937.549 - 0.772ms returns FALSE
TA260 000:937.560 JLINK_HasError()
TA260 000:938.623 JLINK_IsHalted()
TA260 000:939.105 - 0.482ms returns FALSE
TA260 000:939.111 JLINK_HasError()
TA260 000:940.622 JLINK_IsHalted()
TA260 000:941.082 - 0.459ms returns FALSE
TA260 000:941.088 JLINK_HasError()
TA260 000:942.125 JLINK_IsHalted()
TA260 000:942.645 - 0.519ms returns FALSE
TA260 000:942.651 JLINK_HasError()
TA260 000:944.128 JLINK_IsHalted()
TA260 000:944.638 - 0.509ms returns FALSE
TA260 000:944.644 JLINK_HasError()
TA260 000:946.128 JLINK_IsHalted()
TA260 000:946.737 - 0.607ms returns FALSE
TA260 000:946.749 JLINK_HasError()
TA260 000:948.128 JLINK_IsHalted()
TA260 000:948.648 - 0.519ms returns FALSE
TA260 000:948.653 JLINK_HasError()
TA260 000:950.128 JLINK_IsHalted()
TA260 000:950.647 - 0.519ms returns FALSE
TA260 000:950.652 JLINK_HasError()
TA260 000:952.130 JLINK_IsHalted()
TA260 000:952.791 - 0.660ms returns FALSE
TA260 000:952.803 JLINK_HasError()
TA260 000:954.637 JLINK_IsHalted()
TA260 000:955.108 - 0.471ms returns FALSE
TA260 000:955.115 JLINK_HasError()
TA260 000:956.685 JLINK_IsHalted()
TA260 000:957.175 - 0.489ms returns FALSE
TA260 000:957.187 JLINK_HasError()
TA260 000:958.636 JLINK_IsHalted()
TA260 000:959.105 - 0.468ms returns FALSE
TA260 000:959.110 JLINK_HasError()
TA260 000:960.635 JLINK_IsHalted()
TA260 000:961.116 - 0.480ms returns FALSE
TA260 000:961.121 JLINK_HasError()
TA260 000:963.140 JLINK_IsHalted()
TA260 000:963.647 - 0.506ms returns FALSE
TA260 000:963.653 JLINK_HasError()
TA260 000:965.141 JLINK_IsHalted()
TA260 000:965.648 - 0.507ms returns FALSE
TA260 000:965.657 JLINK_HasError()
TA260 000:967.143 JLINK_IsHalted()
TA260 000:967.644 - 0.500ms returns FALSE
TA260 000:967.654 JLINK_HasError()
TA260 000:969.143 JLINK_IsHalted()
TA260 000:969.637 - 0.493ms returns FALSE
TA260 000:969.644 JLINK_HasError()
TA260 000:971.141 JLINK_IsHalted()
TA260 000:973.468   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:973.957 - 2.816ms returns TRUE
TA260 000:973.965 JLINK_ReadReg(R15 (PC))
TA260 000:973.971 - 0.006ms returns 0x20000000
TA260 000:973.975 JLINK_ClrBPEx(BPHandle = 0x00000003)
TA260 000:973.979 - 0.004ms returns 0x00
TA260 000:973.984 JLINK_ReadReg(R0)
TA260 000:973.987 - 0.003ms returns 0x00000000
TA260 000:974.366 JLINK_HasError()
TA260 000:974.376 JLINK_WriteReg(R0, 0x08004000)
TA260 000:974.381 - 0.005ms returns 0
TA260 000:974.386 JLINK_WriteReg(R1, 0x00004000)
TA260 000:974.389 - 0.003ms returns 0
TA260 000:974.393 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:974.397 - 0.003ms returns 0
TA260 000:974.401 JLINK_WriteReg(R3, 0x00000000)
TA260 000:974.404 - 0.003ms returns 0
TA260 000:974.409 JLINK_WriteReg(R4, 0x00000000)
TA260 000:974.412 - 0.003ms returns 0
TA260 000:974.416 JLINK_WriteReg(R5, 0x00000000)
TA260 000:974.419 - 0.003ms returns 0
TA260 000:974.423 JLINK_WriteReg(R6, 0x00000000)
TA260 000:974.427 - 0.003ms returns 0
TA260 000:974.431 JLINK_WriteReg(R7, 0x00000000)
TA260 000:974.434 - 0.003ms returns 0
TA260 000:974.438 JLINK_WriteReg(R8, 0x00000000)
TA260 000:974.442 - 0.003ms returns 0
TA260 000:974.445 JLINK_WriteReg(R9, 0x20000180)
TA260 000:974.449 - 0.003ms returns 0
TA260 000:974.453 JLINK_WriteReg(R10, 0x00000000)
TA260 000:974.456 - 0.003ms returns 0
TA260 000:974.460 JLINK_WriteReg(R11, 0x00000000)
TA260 000:974.464 - 0.003ms returns 0
TA260 000:974.468 JLINK_WriteReg(R12, 0x00000000)
TA260 000:974.471 - 0.003ms returns 0
TA260 000:974.475 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:974.479 - 0.004ms returns 0
TA260 000:974.483 JLINK_WriteReg(R14, 0x20000001)
TA260 000:974.487 - 0.003ms returns 0
TA260 000:974.491 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 000:974.494 - 0.003ms returns 0
TA260 000:974.499 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:974.502 - 0.003ms returns 0
TA260 000:974.506 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:974.509 - 0.003ms returns 0
TA260 000:974.513 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:974.517 - 0.003ms returns 0
TA260 000:974.521 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:974.524 - 0.003ms returns 0
TA260 000:974.528 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:974.533 - 0.004ms returns 0x00000004
TA260 000:974.537 JLINK_Go()
TA260 000:974.546   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:977.268 - 2.731ms 
TA260 000:977.288 JLINK_IsHalted()
TA260 000:979.558   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:980.069 - 2.781ms returns TRUE
TA260 000:980.180 JLINK_ReadReg(R15 (PC))
TA260 000:980.186 - 0.006ms returns 0x20000000
TA260 000:980.218 JLINK_ClrBPEx(BPHandle = 0x00000004)
TA260 000:980.224 - 0.005ms returns 0x00
TA260 000:980.229 JLINK_ReadReg(R0)
TA260 000:980.232 - 0.003ms returns 0x00000001
TA260 000:980.237 JLINK_HasError()
TA260 000:980.241 JLINK_WriteReg(R0, 0x08004000)
TA260 000:980.245 - 0.003ms returns 0
TA260 000:980.250 JLINK_WriteReg(R1, 0x00004000)
TA260 000:980.253 - 0.003ms returns 0
TA260 000:980.257 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:980.261 - 0.003ms returns 0
TA260 000:980.265 JLINK_WriteReg(R3, 0x00000000)
TA260 000:980.268 - 0.003ms returns 0
TA260 000:980.272 JLINK_WriteReg(R4, 0x00000000)
TA260 000:980.275 - 0.003ms returns 0
TA260 000:980.279 JLINK_WriteReg(R5, 0x00000000)
TA260 000:980.283 - 0.003ms returns 0
TA260 000:980.287 JLINK_WriteReg(R6, 0x00000000)
TA260 000:980.290 - 0.003ms returns 0
TA260 000:980.294 JLINK_WriteReg(R7, 0x00000000)
TA260 000:980.297 - 0.003ms returns 0
TA260 000:980.301 JLINK_WriteReg(R8, 0x00000000)
TA260 000:980.305 - 0.003ms returns 0
TA260 000:980.309 JLINK_WriteReg(R9, 0x20000180)
TA260 000:980.312 - 0.003ms returns 0
TA260 000:980.316 JLINK_WriteReg(R10, 0x00000000)
TA260 000:980.325 - 0.008ms returns 0
TA260 000:980.329 JLINK_WriteReg(R11, 0x00000000)
TA260 000:980.332 - 0.003ms returns 0
TA260 000:980.336 JLINK_WriteReg(R12, 0x00000000)
TA260 000:980.340 - 0.003ms returns 0
TA260 000:980.344 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:980.347 - 0.003ms returns 0
TA260 000:980.351 JLINK_WriteReg(R14, 0x20000001)
TA260 000:980.355 - 0.003ms returns 0
TA260 000:980.359 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 000:980.362 - 0.003ms returns 0
TA260 000:980.366 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:980.370 - 0.003ms returns 0
TA260 000:980.374 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:980.377 - 0.003ms returns 0
TA260 000:980.381 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:980.384 - 0.003ms returns 0
TA260 000:980.388 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:980.392 - 0.003ms returns 0
TA260 000:980.396 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:980.400 - 0.004ms returns 0x00000005
TA260 000:980.404 JLINK_Go()
TA260 000:980.412   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 000:983.209 - 2.804ms 
TA260 000:983.220 JLINK_IsHalted()
TA260 000:983.716 - 0.495ms returns FALSE
TA260 000:983.721 JLINK_HasError()
TA260 000:985.155 JLINK_IsHalted()
TA260 000:985.637 - 0.482ms returns FALSE
TA260 000:985.643 JLINK_HasError()
TA260 000:987.156 JLINK_IsHalted()
TA260 000:987.683 - 0.527ms returns FALSE
TA260 000:987.689 JLINK_HasError()
TA260 000:989.156 JLINK_IsHalted()
TA260 000:989.637 - 0.481ms returns FALSE
TA260 000:989.643 JLINK_HasError()
TA260 000:991.154 JLINK_IsHalted()
TA260 000:991.637 - 0.483ms returns FALSE
TA260 000:991.643 JLINK_HasError()
TA260 000:993.658 JLINK_IsHalted()
TA260 000:994.161 - 0.502ms returns FALSE
TA260 000:994.167 JLINK_HasError()
TA260 000:995.660 JLINK_IsHalted()
TA260 000:996.182 - 0.522ms returns FALSE
TA260 000:996.189 JLINK_HasError()
TA260 000:997.660 JLINK_IsHalted()
TA260 000:998.170 - 0.509ms returns FALSE
TA260 000:998.185 JLINK_HasError()
TA260 001:002.164 JLINK_IsHalted()
TA260 001:002.640 - 0.475ms returns FALSE
TA260 001:002.653 JLINK_HasError()
TA260 001:004.168 JLINK_IsHalted()
TA260 001:004.683 - 0.514ms returns FALSE
TA260 001:004.690 JLINK_HasError()
TA260 001:006.165 JLINK_IsHalted()
TA260 001:006.731 - 0.565ms returns FALSE
TA260 001:006.738 JLINK_HasError()
TA260 001:008.168 JLINK_IsHalted()
TA260 001:008.685 - 0.516ms returns FALSE
TA260 001:008.691 JLINK_HasError()
TA260 001:010.166 JLINK_IsHalted()
TA260 001:010.638 - 0.471ms returns FALSE
TA260 001:010.644 JLINK_HasError()
TA260 001:012.667 JLINK_IsHalted()
TA260 001:013.149 - 0.482ms returns FALSE
TA260 001:013.156 JLINK_HasError()
TA260 001:014.671 JLINK_IsHalted()
TA260 001:015.088 - 0.416ms returns FALSE
TA260 001:015.094 JLINK_HasError()
TA260 001:016.669 JLINK_IsHalted()
TA260 001:017.153 - 0.483ms returns FALSE
TA260 001:017.165 JLINK_HasError()
TA260 001:018.673 JLINK_IsHalted()
TA260 001:019.141 - 0.468ms returns FALSE
TA260 001:019.153 JLINK_HasError()
TA260 001:021.672 JLINK_IsHalted()
TA260 001:022.088 - 0.415ms returns FALSE
TA260 001:022.094 JLINK_HasError()
TA260 001:023.179 JLINK_IsHalted()
TA260 001:023.732 - 0.552ms returns FALSE
TA260 001:023.742 JLINK_HasError()
TA260 001:025.178 JLINK_IsHalted()
TA260 001:025.647 - 0.468ms returns FALSE
TA260 001:025.654 JLINK_HasError()
TA260 001:027.178 JLINK_IsHalted()
TA260 001:027.683 - 0.505ms returns FALSE
TA260 001:027.689 JLINK_HasError()
TA260 001:029.180 JLINK_IsHalted()
TA260 001:029.644 - 0.464ms returns FALSE
TA260 001:029.654 JLINK_HasError()
TA260 001:031.680 JLINK_IsHalted()
TA260 001:032.206 - 0.525ms returns FALSE
TA260 001:032.217 JLINK_HasError()
TA260 001:033.687 JLINK_IsHalted()
TA260 001:034.183 - 0.496ms returns FALSE
TA260 001:034.189 JLINK_HasError()
TA260 001:035.683 JLINK_IsHalted()
TA260 001:036.183 - 0.499ms returns FALSE
TA260 001:036.190 JLINK_HasError()
TA260 001:037.682 JLINK_IsHalted()
TA260 001:038.183 - 0.500ms returns FALSE
TA260 001:038.189 JLINK_HasError()
TA260 001:039.683 JLINK_IsHalted()
TA260 001:040.182 - 0.499ms returns FALSE
TA260 001:040.187 JLINK_HasError()
TA260 001:041.683 JLINK_IsHalted()
TA260 001:042.183 - 0.500ms returns FALSE
TA260 001:042.188 JLINK_HasError()
TA260 001:044.187 JLINK_IsHalted()
TA260 001:044.683 - 0.496ms returns FALSE
TA260 001:044.689 JLINK_HasError()
TA260 001:046.189 JLINK_IsHalted()
TA260 001:046.680 - 0.490ms returns FALSE
TA260 001:046.689 JLINK_HasError()
TA260 001:048.189 JLINK_IsHalted()
TA260 001:048.683 - 0.494ms returns FALSE
TA260 001:048.691 JLINK_HasError()
TA260 001:050.187 JLINK_IsHalted()
TA260 001:050.639 - 0.452ms returns FALSE
TA260 001:050.645 JLINK_HasError()
TA260 001:051.689 JLINK_IsHalted()
TA260 001:052.149 - 0.459ms returns FALSE
TA260 001:052.155 JLINK_HasError()
TA260 001:053.692 JLINK_IsHalted()
TA260 001:054.158 - 0.466ms returns FALSE
TA260 001:054.164 JLINK_HasError()
TA260 001:055.694 JLINK_IsHalted()
TA260 001:056.184 - 0.490ms returns FALSE
TA260 001:056.197 JLINK_HasError()
TA260 001:057.695 JLINK_IsHalted()
TA260 001:058.163 - 0.467ms returns FALSE
TA260 001:058.169 JLINK_HasError()
TA260 001:059.693 JLINK_IsHalted()
TA260 001:060.161 - 0.468ms returns FALSE
TA260 001:060.167 JLINK_HasError()
TA260 001:061.695 JLINK_IsHalted()
TA260 001:062.169 - 0.474ms returns FALSE
TA260 001:062.182 JLINK_HasError()
TA260 001:064.202 JLINK_IsHalted()
TA260 001:064.649 - 0.446ms returns FALSE
TA260 001:064.659 JLINK_HasError()
TA260 001:066.202 JLINK_IsHalted()
TA260 001:066.790 - 0.587ms returns FALSE
TA260 001:066.801 JLINK_HasError()
TA260 001:068.203 JLINK_IsHalted()
TA260 001:068.737 - 0.533ms returns FALSE
TA260 001:068.748 JLINK_HasError()
TA260 001:070.201 JLINK_IsHalted()
TA260 001:070.718 - 0.516ms returns FALSE
TA260 001:070.727 JLINK_HasError()
TA260 001:072.703 JLINK_IsHalted()
TA260 001:073.219 - 0.515ms returns FALSE
TA260 001:073.232 JLINK_HasError()
TA260 001:074.706 JLINK_IsHalted()
TA260 001:075.164 - 0.457ms returns FALSE
TA260 001:075.173 JLINK_HasError()
TA260 001:076.707 JLINK_IsHalted()
TA260 001:077.200 - 0.493ms returns FALSE
TA260 001:077.213 JLINK_HasError()
TA260 001:078.707 JLINK_IsHalted()
TA260 001:079.180 - 0.472ms returns FALSE
TA260 001:079.187 JLINK_HasError()
TA260 001:080.706 JLINK_IsHalted()
TA260 001:081.195 - 0.488ms returns FALSE
TA260 001:081.204 JLINK_HasError()
TA260 001:083.212 JLINK_IsHalted()
TA260 001:083.689 - 0.476ms returns FALSE
TA260 001:083.696 JLINK_HasError()
TA260 001:085.211 JLINK_IsHalted()
TA260 001:085.713 - 0.502ms returns FALSE
TA260 001:085.719 JLINK_HasError()
TA260 001:087.212 JLINK_IsHalted()
TA260 001:087.685 - 0.472ms returns FALSE
TA260 001:087.693 JLINK_HasError()
TA260 001:089.214 JLINK_IsHalted()
TA260 001:089.686 - 0.471ms returns FALSE
TA260 001:089.692 JLINK_HasError()
TA260 001:091.713 JLINK_IsHalted()
TA260 001:092.196 - 0.482ms returns FALSE
TA260 001:092.205 JLINK_HasError()
TA260 001:093.716 JLINK_IsHalted()
TA260 001:094.209 - 0.492ms returns FALSE
TA260 001:094.215 JLINK_HasError()
TA260 001:095.718 JLINK_IsHalted()
TA260 001:096.229 - 0.511ms returns FALSE
TA260 001:096.236 JLINK_HasError()
TA260 001:097.717 JLINK_IsHalted()
TA260 001:098.171 - 0.454ms returns FALSE
TA260 001:098.177 JLINK_HasError()
TA260 001:099.716 JLINK_IsHalted()
TA260 001:100.184 - 0.468ms returns FALSE
TA260 001:100.189 JLINK_HasError()
TA260 001:101.717 JLINK_IsHalted()
TA260 001:102.229 - 0.512ms returns FALSE
TA260 001:102.235 JLINK_HasError()
TA260 001:104.221 JLINK_IsHalted()
TA260 001:104.675 - 0.453ms returns FALSE
TA260 001:104.681 JLINK_HasError()
TA260 001:106.223 JLINK_IsHalted()
TA260 001:106.684 - 0.460ms returns FALSE
TA260 001:106.698 JLINK_HasError()
TA260 001:108.223 JLINK_IsHalted()
TA260 001:108.789 - 0.565ms returns FALSE
TA260 001:108.800 JLINK_HasError()
TA260 001:110.221 JLINK_IsHalted()
TA260 001:110.714 - 0.493ms returns FALSE
TA260 001:110.721 JLINK_HasError()
TA260 001:112.723 JLINK_IsHalted()
TA260 001:113.174 - 0.450ms returns FALSE
TA260 001:113.185 JLINK_HasError()
TA260 001:114.727 JLINK_IsHalted()
TA260 001:115.207 - 0.479ms returns FALSE
TA260 001:115.212 JLINK_HasError()
TA260 001:116.726 JLINK_IsHalted()
TA260 001:117.207 - 0.480ms returns FALSE
TA260 001:117.215 JLINK_HasError()
TA260 001:118.730 JLINK_IsHalted()
TA260 001:119.197 - 0.467ms returns FALSE
TA260 001:119.203 JLINK_HasError()
TA260 001:120.727 JLINK_IsHalted()
TA260 001:121.206 - 0.479ms returns FALSE
TA260 001:121.213 JLINK_HasError()
TA260 001:123.230 JLINK_IsHalted()
TA260 001:123.881 - 0.651ms returns FALSE
TA260 001:123.893 JLINK_HasError()
TA260 001:125.181 JLINK_IsHalted()
TA260 001:125.676 - 0.494ms returns FALSE
TA260 001:125.682 JLINK_HasError()
TA260 001:127.181 JLINK_IsHalted()
TA260 001:127.645 - 0.462ms returns FALSE
TA260 001:127.660 JLINK_HasError()
TA260 001:129.183 JLINK_IsHalted()
TA260 001:129.681 - 0.497ms returns FALSE
TA260 001:129.687 JLINK_HasError()
TA260 001:131.687 JLINK_IsHalted()
TA260 001:132.183 - 0.495ms returns FALSE
TA260 001:132.193 JLINK_HasError()
TA260 001:133.686 JLINK_IsHalted()
TA260 001:134.183 - 0.497ms returns FALSE
TA260 001:134.189 JLINK_HasError()
TA260 001:135.688 JLINK_IsHalted()
TA260 001:136.186 - 0.498ms returns FALSE
TA260 001:136.193 JLINK_HasError()
TA260 001:137.689 JLINK_IsHalted()
TA260 001:138.105 - 0.416ms returns FALSE
TA260 001:138.112 JLINK_HasError()
TA260 001:139.688 JLINK_IsHalted()
TA260 001:140.166 - 0.477ms returns FALSE
TA260 001:140.173 JLINK_HasError()
TA260 001:142.189 JLINK_IsHalted()
TA260 001:142.684 - 0.495ms returns FALSE
TA260 001:142.690 JLINK_HasError()
TA260 001:144.191 JLINK_IsHalted()
TA260 001:144.682 - 0.491ms returns FALSE
TA260 001:144.688 JLINK_HasError()
TA260 001:146.192 JLINK_IsHalted()
TA260 001:146.685 - 0.492ms returns FALSE
TA260 001:146.697 JLINK_HasError()
TA260 001:148.195 JLINK_IsHalted()
TA260 001:148.648 - 0.452ms returns FALSE
TA260 001:148.661 JLINK_HasError()
TA260 001:150.191 JLINK_IsHalted()
TA260 001:150.669 - 0.477ms returns FALSE
TA260 001:150.675 JLINK_HasError()
TA260 001:152.694 JLINK_IsHalted()
TA260 001:153.165 - 0.471ms returns FALSE
TA260 001:153.173 JLINK_HasError()
TA260 001:154.699 JLINK_IsHalted()
TA260 001:155.339 - 0.639ms returns FALSE
TA260 001:155.347 JLINK_HasError()
TA260 001:156.699 JLINK_IsHalted()
TA260 001:157.210 - 0.510ms returns FALSE
TA260 001:157.223 JLINK_HasError()
TA260 001:158.702 JLINK_IsHalted()
TA260 001:159.196 - 0.492ms returns FALSE
TA260 001:159.202 JLINK_HasError()
TA260 001:160.696 JLINK_IsHalted()
TA260 001:161.180 - 0.484ms returns FALSE
TA260 001:161.186 JLINK_HasError()
TA260 001:163.204 JLINK_IsHalted()
TA260 001:163.715 - 0.511ms returns FALSE
TA260 001:163.722 JLINK_HasError()
TA260 001:165.203 JLINK_IsHalted()
TA260 001:165.683 - 0.480ms returns FALSE
TA260 001:165.689 JLINK_HasError()
TA260 001:167.203 JLINK_IsHalted()
TA260 001:167.691 - 0.488ms returns FALSE
TA260 001:167.704 JLINK_HasError()
TA260 001:169.204 JLINK_IsHalted()
TA260 001:169.797 - 0.592ms returns FALSE
TA260 001:169.806 JLINK_HasError()
TA260 001:171.707 JLINK_IsHalted()
TA260 001:172.196 - 0.488ms returns FALSE
TA260 001:172.202 JLINK_HasError()
TA260 001:173.709 JLINK_IsHalted()
TA260 001:174.207 - 0.497ms returns FALSE
TA260 001:174.213 JLINK_HasError()
TA260 001:175.712 JLINK_IsHalted()
TA260 001:176.192 - 0.480ms returns FALSE
TA260 001:176.199 JLINK_HasError()
TA260 001:177.709 JLINK_IsHalted()
TA260 001:178.183 - 0.473ms returns FALSE
TA260 001:178.189 JLINK_HasError()
TA260 001:179.710 JLINK_IsHalted()
TA260 001:180.206 - 0.496ms returns FALSE
TA260 001:180.212 JLINK_HasError()
TA260 001:181.711 JLINK_IsHalted()
TA260 001:182.193 - 0.481ms returns FALSE
TA260 001:182.202 JLINK_HasError()
TA260 001:184.213 JLINK_IsHalted()
TA260 001:184.724 - 0.510ms returns FALSE
TA260 001:184.733 JLINK_HasError()
TA260 001:186.216 JLINK_IsHalted()
TA260 001:186.788 - 0.572ms returns FALSE
TA260 001:186.794 JLINK_HasError()
TA260 001:188.218 JLINK_IsHalted()
TA260 001:188.687 - 0.469ms returns FALSE
TA260 001:188.699 JLINK_HasError()
TA260 001:190.215 JLINK_IsHalted()
TA260 001:190.646 - 0.430ms returns FALSE
TA260 001:190.651 JLINK_HasError()
TA260 001:191.720 JLINK_IsHalted()
TA260 001:192.314 - 0.593ms returns FALSE
TA260 001:192.326 JLINK_HasError()
TA260 001:193.720 JLINK_IsHalted()
TA260 001:194.174 - 0.453ms returns FALSE
TA260 001:194.180 JLINK_HasError()
TA260 001:195.722 JLINK_IsHalted()
TA260 001:196.207 - 0.485ms returns FALSE
TA260 001:196.215 JLINK_HasError()
TA260 001:197.723 JLINK_IsHalted()
TA260 001:198.232 - 0.509ms returns FALSE
TA260 001:198.238 JLINK_HasError()
TA260 001:199.720 JLINK_IsHalted()
TA260 001:200.266 - 0.545ms returns FALSE
TA260 001:200.276 JLINK_HasError()
TA260 001:201.721 JLINK_IsHalted()
TA260 001:202.205 - 0.483ms returns FALSE
TA260 001:202.211 JLINK_HasError()
TA260 001:204.226 JLINK_IsHalted()
TA260 001:204.730 - 0.503ms returns FALSE
TA260 001:204.736 JLINK_HasError()
TA260 001:206.226 JLINK_IsHalted()
TA260 001:206.742 - 0.514ms returns FALSE
TA260 001:206.750 JLINK_HasError()
TA260 001:208.236 JLINK_IsHalted()
TA260 001:208.744 - 0.508ms returns FALSE
TA260 001:208.751 JLINK_HasError()
TA260 001:210.225 JLINK_IsHalted()
TA260 001:210.672 - 0.447ms returns FALSE
TA260 001:210.678 JLINK_HasError()
TA260 001:211.728 JLINK_IsHalted()
TA260 001:212.205 - 0.477ms returns FALSE
TA260 001:212.211 JLINK_HasError()
TA260 001:213.730 JLINK_IsHalted()
TA260 001:214.216 - 0.485ms returns FALSE
TA260 001:214.222 JLINK_HasError()
TA260 001:215.732 JLINK_IsHalted()
TA260 001:216.506 - 0.773ms returns FALSE
TA260 001:216.517 JLINK_HasError()
TA260 001:217.733 JLINK_IsHalted()
TA260 001:218.174 - 0.441ms returns FALSE
TA260 001:218.185 JLINK_HasError()
TA260 001:219.730 JLINK_IsHalted()
TA260 001:220.206 - 0.475ms returns FALSE
TA260 001:220.211 JLINK_HasError()
TA260 001:221.731 JLINK_IsHalted()
TA260 001:222.207 - 0.475ms returns FALSE
TA260 001:222.212 JLINK_HasError()
TA260 001:224.235 JLINK_IsHalted()
TA260 001:224.787 - 0.552ms returns FALSE
TA260 001:224.793 JLINK_HasError()
TA260 001:226.235 JLINK_IsHalted()
TA260 001:226.685 - 0.449ms returns FALSE
TA260 001:226.698 JLINK_HasError()
TA260 001:228.239 JLINK_IsHalted()
TA260 001:228.878 - 0.639ms returns FALSE
TA260 001:228.885 JLINK_HasError()
TA260 001:230.235 JLINK_IsHalted()
TA260 001:230.683 - 0.447ms returns FALSE
TA260 001:230.688 JLINK_HasError()
TA260 001:231.739 JLINK_IsHalted()
TA260 001:232.272 - 0.533ms returns FALSE
TA260 001:232.282 JLINK_HasError()
TA260 001:233.741 JLINK_IsHalted()
TA260 001:234.214 - 0.473ms returns FALSE
TA260 001:234.220 JLINK_HasError()
TA260 001:235.743 JLINK_IsHalted()
TA260 001:236.216 - 0.473ms returns FALSE
TA260 001:236.223 JLINK_HasError()
TA260 001:237.742 JLINK_IsHalted()
TA260 001:238.221 - 0.479ms returns FALSE
TA260 001:238.235 JLINK_HasError()
TA260 001:239.741 JLINK_IsHalted()
TA260 001:240.195 - 0.453ms returns FALSE
TA260 001:240.202 JLINK_HasError()
TA260 001:241.742 JLINK_IsHalted()
TA260 001:242.216 - 0.473ms returns FALSE
TA260 001:242.222 JLINK_HasError()
TA260 001:244.245 JLINK_IsHalted()
TA260 001:244.714 - 0.468ms returns FALSE
TA260 001:244.720 JLINK_HasError()
TA260 001:246.258 JLINK_IsHalted()
TA260 001:246.787 - 0.528ms returns FALSE
TA260 001:246.794 JLINK_HasError()
TA260 001:248.251 JLINK_IsHalted()
TA260 001:248.689 - 0.438ms returns FALSE
TA260 001:248.695 JLINK_HasError()
TA260 001:251.751 JLINK_IsHalted()
TA260 001:252.244 - 0.493ms returns FALSE
TA260 001:252.250 JLINK_HasError()
TA260 001:253.751 JLINK_IsHalted()
TA260 001:254.252 - 0.501ms returns FALSE
TA260 001:254.258 JLINK_HasError()
TA260 001:255.751 JLINK_IsHalted()
TA260 001:256.437 - 0.686ms returns FALSE
TA260 001:256.448 JLINK_HasError()
TA260 001:257.754 JLINK_IsHalted()
TA260 001:258.241 - 0.486ms returns FALSE
TA260 001:258.251 JLINK_HasError()
TA260 001:259.752 JLINK_IsHalted()
TA260 001:260.213 - 0.461ms returns FALSE
TA260 001:260.222 JLINK_HasError()
TA260 001:261.753 JLINK_IsHalted()
TA260 001:262.212 - 0.459ms returns FALSE
TA260 001:262.218 JLINK_HasError()
TA260 001:263.257 JLINK_IsHalted()
TA260 001:263.792 - 0.535ms returns FALSE
TA260 001:263.804 JLINK_HasError()
TA260 001:265.257 JLINK_IsHalted()
TA260 001:265.773 - 0.516ms returns FALSE
TA260 001:265.779 JLINK_HasError()
TA260 001:267.258 JLINK_IsHalted()
TA260 001:267.792 - 0.533ms returns FALSE
TA260 001:267.805 JLINK_HasError()
TA260 001:269.257 JLINK_IsHalted()
TA260 001:269.788 - 0.531ms returns FALSE
TA260 001:269.794 JLINK_HasError()
TA260 001:271.759 JLINK_IsHalted()
TA260 001:272.240 - 0.480ms returns FALSE
TA260 001:272.245 JLINK_HasError()
TA260 001:273.762 JLINK_IsHalted()
TA260 001:274.241 - 0.479ms returns FALSE
TA260 001:274.248 JLINK_HasError()
TA260 001:275.764 JLINK_IsHalted()
TA260 001:276.241 - 0.477ms returns FALSE
TA260 001:276.248 JLINK_HasError()
TA260 001:277.765 JLINK_IsHalted()
TA260 001:278.264 - 0.499ms returns FALSE
TA260 001:278.270 JLINK_HasError()
TA260 001:279.766 JLINK_IsHalted()
TA260 001:280.196 - 0.429ms returns FALSE
TA260 001:280.203 JLINK_HasError()
TA260 001:282.267 JLINK_IsHalted()
TA260 001:282.822 - 0.555ms returns FALSE
TA260 001:282.830 JLINK_HasError()
TA260 001:284.273 JLINK_IsHalted()
TA260 001:284.788 - 0.515ms returns FALSE
TA260 001:284.796 JLINK_HasError()
TA260 001:286.274 JLINK_IsHalted()
TA260 001:287.007 - 0.732ms returns FALSE
TA260 001:287.018 JLINK_HasError()
TA260 001:288.275 JLINK_IsHalted()
TA260 001:288.786 - 0.510ms returns FALSE
TA260 001:288.795 JLINK_HasError()
TA260 001:290.271 JLINK_IsHalted()
TA260 001:290.790 - 0.518ms returns FALSE
TA260 001:290.803 JLINK_HasError()
TA260 001:292.776 JLINK_IsHalted()
TA260 001:293.286 - 0.509ms returns FALSE
TA260 001:293.294 JLINK_HasError()
TA260 001:294.778 JLINK_IsHalted()
TA260 001:295.314 - 0.535ms returns FALSE
TA260 001:295.323 JLINK_HasError()
TA260 001:296.824 JLINK_IsHalted()
TA260 001:297.294 - 0.470ms returns FALSE
TA260 001:297.305 JLINK_HasError()
TA260 001:298.785 JLINK_IsHalted()
TA260 001:299.255 - 0.469ms returns FALSE
TA260 001:299.262 JLINK_HasError()
TA260 001:300.784 JLINK_IsHalted()
TA260 001:301.244 - 0.460ms returns FALSE
TA260 001:301.251 JLINK_HasError()
TA260 001:303.292 JLINK_IsHalted()
TA260 001:303.836 - 0.544ms returns FALSE
TA260 001:303.843 JLINK_HasError()
TA260 001:305.286 JLINK_IsHalted()
TA260 001:305.734 - 0.446ms returns FALSE
TA260 001:305.744 JLINK_HasError()
TA260 001:307.293 JLINK_IsHalted()
TA260 001:307.795 - 0.503ms returns FALSE
TA260 001:307.804 JLINK_HasError()
TA260 001:309.288 JLINK_IsHalted()
TA260 001:309.774 - 0.485ms returns FALSE
TA260 001:309.783 JLINK_HasError()
TA260 001:311.794 JLINK_IsHalted()
TA260 001:312.245 - 0.450ms returns FALSE
TA260 001:312.251 JLINK_HasError()
TA260 001:313.796 JLINK_IsHalted()
TA260 001:314.310 - 0.513ms returns FALSE
TA260 001:314.318 JLINK_HasError()
TA260 001:315.799 JLINK_IsHalted()
TA260 001:316.274 - 0.474ms returns FALSE
TA260 001:316.280 JLINK_HasError()
TA260 001:317.796 JLINK_IsHalted()
TA260 001:318.289 - 0.492ms returns FALSE
TA260 001:318.295 JLINK_HasError()
TA260 001:319.797 JLINK_IsHalted()
TA260 001:320.268 - 0.470ms returns FALSE
TA260 001:320.274 JLINK_HasError()
TA260 001:321.805 JLINK_IsHalted()
TA260 001:322.325 - 0.520ms returns FALSE
TA260 001:322.334 JLINK_HasError()
TA260 001:324.304 JLINK_IsHalted()
TA260 001:324.792 - 0.487ms returns FALSE
TA260 001:324.802 JLINK_HasError()
TA260 001:326.306 JLINK_IsHalted()
TA260 001:326.795 - 0.489ms returns FALSE
TA260 001:326.806 JLINK_HasError()
TA260 001:328.303 JLINK_IsHalted()
TA260 001:328.866 - 0.562ms returns FALSE
TA260 001:328.883 JLINK_HasError()
TA260 001:330.304 JLINK_IsHalted()
TA260 001:330.822 - 0.518ms returns FALSE
TA260 001:330.886 JLINK_HasError()
TA260 001:332.811 JLINK_IsHalted()
TA260 001:333.244 - 0.433ms returns FALSE
TA260 001:333.252 JLINK_HasError()
TA260 001:334.809 JLINK_IsHalted()
TA260 001:335.252 - 0.443ms returns FALSE
TA260 001:335.261 JLINK_HasError()
TA260 001:336.813 JLINK_IsHalted()
TA260 001:337.281 - 0.468ms returns FALSE
TA260 001:337.288 JLINK_HasError()
TA260 001:338.811 JLINK_IsHalted()
TA260 001:339.329 - 0.517ms returns FALSE
TA260 001:339.336 JLINK_HasError()
TA260 001:340.812 JLINK_IsHalted()
TA260 001:341.279 - 0.467ms returns FALSE
TA260 001:341.286 JLINK_HasError()
TA260 001:343.319 JLINK_IsHalted()
TA260 001:343.790 - 0.471ms returns FALSE
TA260 001:343.797 JLINK_HasError()
TA260 001:345.328 JLINK_IsHalted()
TA260 001:345.823 - 0.496ms returns FALSE
TA260 001:345.832 JLINK_HasError()
TA260 001:347.327 JLINK_IsHalted()
TA260 001:347.809 - 0.482ms returns FALSE
TA260 001:347.817 JLINK_HasError()
TA260 001:349.322 JLINK_IsHalted()
TA260 001:349.812 - 0.492ms returns FALSE
TA260 001:349.821 JLINK_HasError()
TA260 001:351.826 JLINK_IsHalted()
TA260 001:352.376 - 0.550ms returns FALSE
TA260 001:352.384 JLINK_HasError()
TA260 001:353.826 JLINK_IsHalted()
TA260 001:354.322 - 0.496ms returns FALSE
TA260 001:354.329 JLINK_HasError()
TA260 001:355.836 JLINK_IsHalted()
TA260 001:356.291 - 0.454ms returns FALSE
TA260 001:356.298 JLINK_HasError()
TA260 001:357.826 JLINK_IsHalted()
TA260 001:358.359 - 0.532ms returns FALSE
TA260 001:358.372 JLINK_HasError()
TA260 001:360.830 JLINK_IsHalted()
TA260 001:361.291 - 0.460ms returns FALSE
TA260 001:361.298 JLINK_HasError()
TA260 001:363.336 JLINK_IsHalted()
TA260 001:363.835 - 0.499ms returns FALSE
TA260 001:363.844 JLINK_HasError()
TA260 001:365.339 JLINK_IsHalted()
TA260 001:365.816 - 0.475ms returns FALSE
TA260 001:365.827 JLINK_HasError()
TA260 001:367.337 JLINK_IsHalted()
TA260 001:367.867 - 0.530ms returns FALSE
TA260 001:367.875 JLINK_HasError()
TA260 001:369.334 JLINK_IsHalted()
TA260 001:369.821 - 0.486ms returns FALSE
TA260 001:369.831 JLINK_HasError()
TA260 001:371.841 JLINK_IsHalted()
TA260 001:372.288 - 0.446ms returns FALSE
TA260 001:372.294 JLINK_HasError()
TA260 001:373.840 JLINK_IsHalted()
TA260 001:374.388 - 0.547ms returns FALSE
TA260 001:374.399 JLINK_HasError()
TA260 001:375.846 JLINK_IsHalted()
TA260 001:376.382 - 0.535ms returns FALSE
TA260 001:376.390 JLINK_HasError()
TA260 001:377.844 JLINK_IsHalted()
TA260 001:378.309 - 0.465ms returns FALSE
TA260 001:378.317 JLINK_HasError()
TA260 001:379.843 JLINK_IsHalted()
TA260 001:380.324 - 0.480ms returns FALSE
TA260 001:380.330 JLINK_HasError()
TA260 001:381.846 JLINK_IsHalted()
TA260 001:382.286 - 0.440ms returns FALSE
TA260 001:382.294 JLINK_HasError()
TA260 001:383.348 JLINK_IsHalted()
TA260 001:383.836 - 0.488ms returns FALSE
TA260 001:383.844 JLINK_HasError()
TA260 001:385.351 JLINK_IsHalted()
TA260 001:385.871 - 0.520ms returns FALSE
TA260 001:385.881 JLINK_HasError()
TA260 001:387.352 JLINK_IsHalted()
TA260 001:387.834 - 0.481ms returns FALSE
TA260 001:387.843 JLINK_HasError()
TA260 001:389.351 JLINK_IsHalted()
TA260 001:389.870 - 0.519ms returns FALSE
TA260 001:389.881 JLINK_HasError()
TA260 001:391.857 JLINK_IsHalted()
TA260 001:392.392 - 0.535ms returns FALSE
TA260 001:392.400 JLINK_HasError()
TA260 001:393.856 JLINK_IsHalted()
TA260 001:394.358 - 0.502ms returns FALSE
TA260 001:394.366 JLINK_HasError()
TA260 001:395.860 JLINK_IsHalted()
TA260 001:396.333 - 0.472ms returns FALSE
TA260 001:396.339 JLINK_HasError()
TA260 001:397.858 JLINK_IsHalted()
TA260 001:400.188   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:400.673 - 2.815ms returns TRUE
TA260 001:400.682 JLINK_ReadReg(R15 (PC))
TA260 001:400.687 - 0.005ms returns 0x20000000
TA260 001:400.692 JLINK_ClrBPEx(BPHandle = 0x00000005)
TA260 001:400.695 - 0.003ms returns 0x00
TA260 001:400.700 JLINK_ReadReg(R0)
TA260 001:400.703 - 0.003ms returns 0x00000000
TA260 001:401.137 JLINK_HasError()
TA260 001:401.149 JLINK_WriteReg(R0, 0x08008000)
TA260 001:401.154 - 0.005ms returns 0
TA260 001:401.159 JLINK_WriteReg(R1, 0x00004000)
TA260 001:401.162 - 0.003ms returns 0
TA260 001:401.166 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:401.174 - 0.007ms returns 0
TA260 001:401.179 JLINK_WriteReg(R3, 0x00000000)
TA260 001:401.183 - 0.003ms returns 0
TA260 001:401.187 JLINK_WriteReg(R4, 0x00000000)
TA260 001:401.190 - 0.003ms returns 0
TA260 001:401.194 JLINK_WriteReg(R5, 0x00000000)
TA260 001:401.198 - 0.004ms returns 0
TA260 001:401.202 JLINK_WriteReg(R6, 0x00000000)
TA260 001:401.206 - 0.003ms returns 0
TA260 001:401.210 JLINK_WriteReg(R7, 0x00000000)
TA260 001:401.213 - 0.003ms returns 0
TA260 001:401.217 JLINK_WriteReg(R8, 0x00000000)
TA260 001:401.220 - 0.003ms returns 0
TA260 001:401.224 JLINK_WriteReg(R9, 0x20000180)
TA260 001:401.228 - 0.003ms returns 0
TA260 001:401.232 JLINK_WriteReg(R10, 0x00000000)
TA260 001:401.235 - 0.003ms returns 0
TA260 001:401.239 JLINK_WriteReg(R11, 0x00000000)
TA260 001:401.243 - 0.003ms returns 0
TA260 001:401.247 JLINK_WriteReg(R12, 0x00000000)
TA260 001:401.250 - 0.003ms returns 0
TA260 001:401.254 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:401.258 - 0.003ms returns 0
TA260 001:401.262 JLINK_WriteReg(R14, 0x20000001)
TA260 001:401.265 - 0.003ms returns 0
TA260 001:401.270 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 001:401.273 - 0.004ms returns 0
TA260 001:401.277 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:401.281 - 0.003ms returns 0
TA260 001:401.285 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:401.288 - 0.003ms returns 0
TA260 001:401.292 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:401.296 - 0.003ms returns 0
TA260 001:401.300 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:401.303 - 0.003ms returns 0
TA260 001:401.308 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:401.312 - 0.004ms returns 0x00000006
TA260 001:401.316 JLINK_Go()
TA260 001:401.325   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:404.072 - 2.755ms 
TA260 001:404.084 JLINK_IsHalted()
TA260 001:406.511   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:407.030 - 2.945ms returns TRUE
TA260 001:407.039 JLINK_ReadReg(R15 (PC))
TA260 001:407.045 - 0.005ms returns 0x20000000
TA260 001:407.050 JLINK_ClrBPEx(BPHandle = 0x00000006)
TA260 001:407.054 - 0.003ms returns 0x00
TA260 001:407.059 JLINK_ReadReg(R0)
TA260 001:407.062 - 0.003ms returns 0x00000001
TA260 001:407.067 JLINK_HasError()
TA260 001:407.072 JLINK_WriteReg(R0, 0x08008000)
TA260 001:407.076 - 0.004ms returns 0
TA260 001:407.080 JLINK_WriteReg(R1, 0x00004000)
TA260 001:407.084 - 0.003ms returns 0
TA260 001:407.088 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:407.092 - 0.004ms returns 0
TA260 001:407.096 JLINK_WriteReg(R3, 0x00000000)
TA260 001:407.099 - 0.003ms returns 0
TA260 001:407.103 JLINK_WriteReg(R4, 0x00000000)
TA260 001:407.107 - 0.003ms returns 0
TA260 001:407.111 JLINK_WriteReg(R5, 0x00000000)
TA260 001:407.114 - 0.003ms returns 0
TA260 001:407.118 JLINK_WriteReg(R6, 0x00000000)
TA260 001:407.121 - 0.003ms returns 0
TA260 001:407.125 JLINK_WriteReg(R7, 0x00000000)
TA260 001:407.129 - 0.003ms returns 0
TA260 001:407.133 JLINK_WriteReg(R8, 0x00000000)
TA260 001:407.136 - 0.003ms returns 0
TA260 001:407.140 JLINK_WriteReg(R9, 0x20000180)
TA260 001:407.144 - 0.003ms returns 0
TA260 001:407.148 JLINK_WriteReg(R10, 0x00000000)
TA260 001:407.151 - 0.003ms returns 0
TA260 001:407.155 JLINK_WriteReg(R11, 0x00000000)
TA260 001:407.159 - 0.003ms returns 0
TA260 001:407.163 JLINK_WriteReg(R12, 0x00000000)
TA260 001:407.166 - 0.003ms returns 0
TA260 001:407.170 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:407.174 - 0.003ms returns 0
TA260 001:407.178 JLINK_WriteReg(R14, 0x20000001)
TA260 001:407.181 - 0.003ms returns 0
TA260 001:407.185 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 001:407.189 - 0.003ms returns 0
TA260 001:407.193 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:407.196 - 0.003ms returns 0
TA260 001:407.200 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:407.204 - 0.003ms returns 0
TA260 001:407.208 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:407.211 - 0.003ms returns 0
TA260 001:407.215 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:407.219 - 0.003ms returns 0
TA260 001:407.223 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:407.233 - 0.009ms returns 0x00000007
TA260 001:407.239 JLINK_Go()
TA260 001:407.247   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:410.009 - 2.769ms 
TA260 001:410.034 JLINK_IsHalted()
TA260 001:410.537 - 0.503ms returns FALSE
TA260 001:410.547 JLINK_HasError()
TA260 001:411.870 JLINK_IsHalted()
TA260 001:412.336 - 0.466ms returns FALSE
TA260 001:412.343 JLINK_HasError()
TA260 001:413.872 JLINK_IsHalted()
TA260 001:414.368 - 0.495ms returns FALSE
TA260 001:414.375 JLINK_HasError()
TA260 001:415.874 JLINK_IsHalted()
TA260 001:416.356 - 0.481ms returns FALSE
TA260 001:416.363 JLINK_HasError()
TA260 001:417.871 JLINK_IsHalted()
TA260 001:418.356 - 0.485ms returns FALSE
TA260 001:418.363 JLINK_HasError()
TA260 001:419.871 JLINK_IsHalted()
TA260 001:420.382 - 0.510ms returns FALSE
TA260 001:420.393 JLINK_HasError()
TA260 001:421.873 JLINK_IsHalted()
TA260 001:422.367 - 0.494ms returns FALSE
TA260 001:422.374 JLINK_HasError()
TA260 001:424.379 JLINK_IsHalted()
TA260 001:424.888 - 0.509ms returns FALSE
TA260 001:424.896 JLINK_HasError()
TA260 001:426.380 JLINK_IsHalted()
TA260 001:426.872 - 0.491ms returns FALSE
TA260 001:426.879 JLINK_HasError()
TA260 001:428.378 JLINK_IsHalted()
TA260 001:428.881 - 0.503ms returns FALSE
TA260 001:428.890 JLINK_HasError()
TA260 001:430.378 JLINK_IsHalted()
TA260 001:430.868 - 0.490ms returns FALSE
TA260 001:430.876 JLINK_HasError()
TA260 001:432.883 JLINK_IsHalted()
TA260 001:433.368 - 0.485ms returns FALSE
TA260 001:433.377 JLINK_HasError()
TA260 001:434.886 JLINK_IsHalted()
TA260 001:435.466 - 0.578ms returns FALSE
TA260 001:435.479 JLINK_HasError()
TA260 001:436.885 JLINK_IsHalted()
TA260 001:437.380 - 0.494ms returns FALSE
TA260 001:437.387 JLINK_HasError()
TA260 001:438.887 JLINK_IsHalted()
TA260 001:439.356 - 0.469ms returns FALSE
TA260 001:439.363 JLINK_HasError()
TA260 001:440.889 JLINK_IsHalted()
TA260 001:441.368 - 0.479ms returns FALSE
TA260 001:441.376 JLINK_HasError()
TA260 001:443.392 JLINK_IsHalted()
TA260 001:443.892 - 0.500ms returns FALSE
TA260 001:443.900 JLINK_HasError()
TA260 001:445.396 JLINK_IsHalted()
TA260 001:445.871 - 0.474ms returns FALSE
TA260 001:445.882 JLINK_HasError()
TA260 001:447.394 JLINK_IsHalted()
TA260 001:447.938 - 0.543ms returns FALSE
TA260 001:447.946 JLINK_HasError()
TA260 001:449.395 JLINK_IsHalted()
TA260 001:449.869 - 0.473ms returns FALSE
TA260 001:449.878 JLINK_HasError()
TA260 001:451.900 JLINK_IsHalted()
TA260 001:452.366 - 0.466ms returns FALSE
TA260 001:452.376 JLINK_HasError()
TA260 001:453.901 JLINK_IsHalted()
TA260 001:454.413 - 0.511ms returns FALSE
TA260 001:454.421 JLINK_HasError()
TA260 001:455.909 JLINK_IsHalted()
TA260 001:456.418 - 0.508ms returns FALSE
TA260 001:456.427 JLINK_HasError()
TA260 001:457.902 JLINK_IsHalted()
TA260 001:458.439 - 0.537ms returns FALSE
TA260 001:458.450 JLINK_HasError()
TA260 001:459.904 JLINK_IsHalted()
TA260 001:460.438 - 0.533ms returns FALSE
TA260 001:460.446 JLINK_HasError()
TA260 001:461.904 JLINK_IsHalted()
TA260 001:462.371 - 0.466ms returns FALSE
TA260 001:462.377 JLINK_HasError()
TA260 001:464.410 JLINK_IsHalted()
TA260 001:464.883 - 0.472ms returns FALSE
TA260 001:464.893 JLINK_HasError()
TA260 001:466.409 JLINK_IsHalted()
TA260 001:466.957 - 0.546ms returns FALSE
TA260 001:466.974 JLINK_HasError()
TA260 001:469.409 JLINK_IsHalted()
TA260 001:469.870 - 0.461ms returns FALSE
TA260 001:469.877 JLINK_HasError()
TA260 001:471.917 JLINK_IsHalted()
TA260 001:472.466 - 0.548ms returns FALSE
TA260 001:472.530 JLINK_HasError()
TA260 001:473.917 JLINK_IsHalted()
TA260 001:474.394 - 0.476ms returns FALSE
TA260 001:474.425 JLINK_HasError()
TA260 001:475.920 JLINK_IsHalted()
TA260 001:476.391 - 0.471ms returns FALSE
TA260 001:476.398 JLINK_HasError()
TA260 001:477.915 JLINK_IsHalted()
TA260 001:478.423 - 0.508ms returns FALSE
TA260 001:478.432 JLINK_HasError()
TA260 001:479.931 JLINK_IsHalted()
TA260 001:480.427 - 0.496ms returns FALSE
TA260 001:480.435 JLINK_HasError()
TA260 001:481.919 JLINK_IsHalted()
TA260 001:482.449 - 0.530ms returns FALSE
TA260 001:482.461 JLINK_HasError()
TA260 001:484.420 JLINK_IsHalted()
TA260 001:484.870 - 0.449ms returns FALSE
TA260 001:484.878 JLINK_HasError()
TA260 001:486.428 JLINK_IsHalted()
TA260 001:486.843 - 0.414ms returns FALSE
TA260 001:486.857 JLINK_HasError()
TA260 001:488.424 JLINK_IsHalted()
TA260 001:488.841 - 0.415ms returns FALSE
TA260 001:488.847 JLINK_HasError()
TA260 001:490.426 JLINK_IsHalted()
TA260 001:490.870 - 0.444ms returns FALSE
TA260 001:490.878 JLINK_HasError()
TA260 001:491.928 JLINK_IsHalted()
TA260 001:492.539 - 0.611ms returns FALSE
TA260 001:492.559 JLINK_HasError()
TA260 001:493.931 JLINK_IsHalted()
TA260 001:494.392 - 0.461ms returns FALSE
TA260 001:494.401 JLINK_HasError()
TA260 001:495.937 JLINK_IsHalted()
TA260 001:496.404 - 0.466ms returns FALSE
TA260 001:496.411 JLINK_HasError()
TA260 001:497.931 JLINK_IsHalted()
TA260 001:498.403 - 0.471ms returns FALSE
TA260 001:498.412 JLINK_HasError()
TA260 001:501.936 JLINK_IsHalted()
TA260 001:502.415 - 0.478ms returns FALSE
TA260 001:502.423 JLINK_HasError()
TA260 001:504.435 JLINK_IsHalted()
TA260 001:504.926 - 0.491ms returns FALSE
TA260 001:504.933 JLINK_HasError()
TA260 001:506.437 JLINK_IsHalted()
TA260 001:506.905 - 0.468ms returns FALSE
TA260 001:506.914 JLINK_HasError()
TA260 001:508.439 JLINK_IsHalted()
TA260 001:508.903 - 0.464ms returns FALSE
TA260 001:508.910 JLINK_HasError()
TA260 001:510.438 JLINK_IsHalted()
TA260 001:510.917 - 0.479ms returns FALSE
TA260 001:510.924 JLINK_HasError()
TA260 001:512.941 JLINK_IsHalted()
TA260 001:513.415 - 0.473ms returns FALSE
TA260 001:513.423 JLINK_HasError()
TA260 001:514.947 JLINK_IsHalted()
TA260 001:515.403 - 0.455ms returns FALSE
TA260 001:515.412 JLINK_HasError()
TA260 001:516.945 JLINK_IsHalted()
TA260 001:517.513 - 0.567ms returns FALSE
TA260 001:517.529 JLINK_HasError()
TA260 001:518.942 JLINK_IsHalted()
TA260 001:519.401 - 0.458ms returns FALSE
TA260 001:519.411 JLINK_HasError()
TA260 001:520.947 JLINK_IsHalted()
TA260 001:521.416 - 0.469ms returns FALSE
TA260 001:521.436 JLINK_HasError()
TA260 001:523.450 JLINK_IsHalted()
TA260 001:524.017 - 0.566ms returns FALSE
TA260 001:524.027 JLINK_HasError()
TA260 001:525.452 JLINK_IsHalted()
TA260 001:525.937 - 0.484ms returns FALSE
TA260 001:525.948 JLINK_HasError()
TA260 001:527.455 JLINK_IsHalted()
TA260 001:527.962 - 0.506ms returns FALSE
TA260 001:527.971 JLINK_HasError()
TA260 001:529.313 JLINK_IsHalted()
TA260 001:529.822 - 0.508ms returns FALSE
TA260 001:529.829 JLINK_HasError()
TA260 001:531.822 JLINK_IsHalted()
TA260 001:532.311 - 0.488ms returns FALSE
TA260 001:532.320 JLINK_HasError()
TA260 001:533.822 JLINK_IsHalted()
TA260 001:534.312 - 0.489ms returns FALSE
TA260 001:534.319 JLINK_HasError()
TA260 001:535.823 JLINK_IsHalted()
TA260 001:536.335 - 0.511ms returns FALSE
TA260 001:536.341 JLINK_HasError()
TA260 001:537.825 JLINK_IsHalted()
TA260 001:538.290 - 0.465ms returns FALSE
TA260 001:538.304 JLINK_HasError()
TA260 001:539.822 JLINK_IsHalted()
TA260 001:540.323 - 0.500ms returns FALSE
TA260 001:540.331 JLINK_HasError()
TA260 001:541.825 JLINK_IsHalted()
TA260 001:542.303 - 0.477ms returns FALSE
TA260 001:542.312 JLINK_HasError()
TA260 001:544.327 JLINK_IsHalted()
TA260 001:544.789 - 0.461ms returns FALSE
TA260 001:544.799 JLINK_HasError()
TA260 001:546.330 JLINK_IsHalted()
TA260 001:546.798 - 0.468ms returns FALSE
TA260 001:546.806 JLINK_HasError()
TA260 001:548.333 JLINK_IsHalted()
TA260 001:548.824 - 0.491ms returns FALSE
TA260 001:548.833 JLINK_HasError()
TA260 001:550.327 JLINK_IsHalted()
TA260 001:550.791 - 0.464ms returns FALSE
TA260 001:550.797 JLINK_HasError()
TA260 001:551.833 JLINK_IsHalted()
TA260 001:552.312 - 0.478ms returns FALSE
TA260 001:552.318 JLINK_HasError()
TA260 001:553.841 JLINK_IsHalted()
TA260 001:554.332 - 0.491ms returns FALSE
TA260 001:554.339 JLINK_HasError()
TA260 001:555.840 JLINK_IsHalted()
TA260 001:556.294 - 0.454ms returns FALSE
TA260 001:556.305 JLINK_HasError()
TA260 001:557.837 JLINK_IsHalted()
TA260 001:558.338 - 0.499ms returns FALSE
TA260 001:558.348 JLINK_HasError()
TA260 001:559.837 JLINK_IsHalted()
TA260 001:560.322 - 0.485ms returns FALSE
TA260 001:560.331 JLINK_HasError()
TA260 001:561.837 JLINK_IsHalted()
TA260 001:562.324 - 0.486ms returns FALSE
TA260 001:562.330 JLINK_HasError()
TA260 001:564.340 JLINK_IsHalted()
TA260 001:564.874 - 0.533ms returns FALSE
TA260 001:564.880 JLINK_HasError()
TA260 001:566.341 JLINK_IsHalted()
TA260 001:566.792 - 0.450ms returns FALSE
TA260 001:566.803 JLINK_HasError()
TA260 001:568.349 JLINK_IsHalted()
TA260 001:568.869 - 0.520ms returns FALSE
TA260 001:568.878 JLINK_HasError()
TA260 001:570.341 JLINK_IsHalted()
TA260 001:570.823 - 0.481ms returns FALSE
TA260 001:570.835 JLINK_HasError()
TA260 001:572.856 JLINK_IsHalted()
TA260 001:573.334 - 0.477ms returns FALSE
TA260 001:573.342 JLINK_HasError()
TA260 001:574.851 JLINK_IsHalted()
TA260 001:575.381 - 0.529ms returns FALSE
TA260 001:575.391 JLINK_HasError()
TA260 001:577.854 JLINK_IsHalted()
TA260 001:578.349 - 0.494ms returns FALSE
TA260 001:578.356 JLINK_HasError()
TA260 001:579.846 JLINK_IsHalted()
TA260 001:580.335 - 0.488ms returns FALSE
TA260 001:580.344 JLINK_HasError()
TA260 001:581.853 JLINK_IsHalted()
TA260 001:582.371 - 0.518ms returns FALSE
TA260 001:582.379 JLINK_HasError()
TA260 001:584.353 JLINK_IsHalted()
TA260 001:584.834 - 0.480ms returns FALSE
TA260 001:584.840 JLINK_HasError()
TA260 001:586.355 JLINK_IsHalted()
TA260 001:586.835 - 0.480ms returns FALSE
TA260 001:586.842 JLINK_HasError()
TA260 001:588.358 JLINK_IsHalted()
TA260 001:588.880 - 0.521ms returns FALSE
TA260 001:588.891 JLINK_HasError()
TA260 001:590.358 JLINK_IsHalted()
TA260 001:590.883 - 0.525ms returns FALSE
TA260 001:590.895 JLINK_HasError()
TA260 001:592.864 JLINK_IsHalted()
TA260 001:593.413 - 0.549ms returns FALSE
TA260 001:593.421 JLINK_HasError()
TA260 001:594.864 JLINK_IsHalted()
TA260 001:595.359 - 0.494ms returns FALSE
TA260 001:595.368 JLINK_HasError()
TA260 001:596.866 JLINK_IsHalted()
TA260 001:597.323 - 0.457ms returns FALSE
TA260 001:597.332 JLINK_HasError()
TA260 001:598.865 JLINK_IsHalted()
TA260 001:599.383 - 0.517ms returns FALSE
TA260 001:599.391 JLINK_HasError()
TA260 001:600.863 JLINK_IsHalted()
TA260 001:601.370 - 0.506ms returns FALSE
TA260 001:601.377 JLINK_HasError()
TA260 001:603.373 JLINK_IsHalted()
TA260 001:603.880 - 0.506ms returns FALSE
TA260 001:603.887 JLINK_HasError()
TA260 001:605.370 JLINK_IsHalted()
TA260 001:605.885 - 0.514ms returns FALSE
TA260 001:605.896 JLINK_HasError()
TA260 001:607.373 JLINK_IsHalted()
TA260 001:607.869 - 0.495ms returns FALSE
TA260 001:607.878 JLINK_HasError()
TA260 001:609.372 JLINK_IsHalted()
TA260 001:609.914 - 0.541ms returns FALSE
TA260 001:609.922 JLINK_HasError()
TA260 001:611.876 JLINK_IsHalted()
TA260 001:612.379 - 0.503ms returns FALSE
TA260 001:612.386 JLINK_HasError()
TA260 001:613.880 JLINK_IsHalted()
TA260 001:614.354 - 0.474ms returns FALSE
TA260 001:614.367 JLINK_HasError()
TA260 001:615.879 JLINK_IsHalted()
TA260 001:616.370 - 0.492ms returns FALSE
TA260 001:616.379 JLINK_HasError()
TA260 001:617.880 JLINK_IsHalted()
TA260 001:618.345 - 0.464ms returns FALSE
TA260 001:618.352 JLINK_HasError()
TA260 001:619.876 JLINK_IsHalted()
TA260 001:620.346 - 0.469ms returns FALSE
TA260 001:620.352 JLINK_HasError()
TA260 001:621.882 JLINK_IsHalted()
TA260 001:622.395 - 0.513ms returns FALSE
TA260 001:622.406 JLINK_HasError()
TA260 001:624.385 JLINK_IsHalted()
TA260 001:624.868 - 0.483ms returns FALSE
TA260 001:624.876 JLINK_HasError()
TA260 001:626.387 JLINK_IsHalted()
TA260 001:626.913 - 0.526ms returns FALSE
TA260 001:626.922 JLINK_HasError()
TA260 001:628.389 JLINK_IsHalted()
TA260 001:628.934 - 0.544ms returns FALSE
TA260 001:628.942 JLINK_HasError()
TA260 001:630.384 JLINK_IsHalted()
TA260 001:630.872 - 0.487ms returns FALSE
TA260 001:630.882 JLINK_HasError()
TA260 001:632.894 JLINK_IsHalted()
TA260 001:633.402 - 0.507ms returns FALSE
TA260 001:633.413 JLINK_HasError()
TA260 001:634.892 JLINK_IsHalted()
TA260 001:635.402 - 0.509ms returns FALSE
TA260 001:635.411 JLINK_HasError()
TA260 001:636.895 JLINK_IsHalted()
TA260 001:637.439 - 0.544ms returns FALSE
TA260 001:637.449 JLINK_HasError()
TA260 001:638.893 JLINK_IsHalted()
TA260 001:639.371 - 0.477ms returns FALSE
TA260 001:639.414 JLINK_HasError()
TA260 001:640.892 JLINK_IsHalted()
TA260 001:641.414 - 0.522ms returns FALSE
TA260 001:641.422 JLINK_HasError()
TA260 001:643.404 JLINK_IsHalted()
TA260 001:643.836 - 0.431ms returns FALSE
TA260 001:643.845 JLINK_HasError()
TA260 001:645.401 JLINK_IsHalted()
TA260 001:645.870 - 0.468ms returns FALSE
TA260 001:645.878 JLINK_HasError()
TA260 001:647.408 JLINK_IsHalted()
TA260 001:647.928 - 0.519ms returns FALSE
TA260 001:647.938 JLINK_HasError()
TA260 001:649.399 JLINK_IsHalted()
TA260 001:649.911 - 0.512ms returns FALSE
TA260 001:649.918 JLINK_HasError()
TA260 001:651.909 JLINK_IsHalted()
TA260 001:652.394 - 0.484ms returns FALSE
TA260 001:652.401 JLINK_HasError()
TA260 001:653.908 JLINK_IsHalted()
TA260 001:654.439 - 0.531ms returns FALSE
TA260 001:654.449 JLINK_HasError()
TA260 001:655.915 JLINK_IsHalted()
TA260 001:656.374 - 0.459ms returns FALSE
TA260 001:656.383 JLINK_HasError()
TA260 001:657.912 JLINK_IsHalted()
TA260 001:658.361 - 0.448ms returns FALSE
TA260 001:658.371 JLINK_HasError()
TA260 001:659.906 JLINK_IsHalted()
TA260 001:660.411 - 0.504ms returns FALSE
TA260 001:660.418 JLINK_HasError()
TA260 001:661.911 JLINK_IsHalted()
TA260 001:662.394 - 0.482ms returns FALSE
TA260 001:662.401 JLINK_HasError()
TA260 001:664.417 JLINK_IsHalted()
TA260 001:664.925 - 0.508ms returns FALSE
TA260 001:664.970 JLINK_HasError()
TA260 001:666.424 JLINK_IsHalted()
TA260 001:666.940 - 0.514ms returns FALSE
TA260 001:666.950 JLINK_HasError()
TA260 001:668.423 JLINK_IsHalted()
TA260 001:668.937 - 0.514ms returns FALSE
TA260 001:668.947 JLINK_HasError()
TA260 001:670.417 JLINK_IsHalted()
TA260 001:670.882 - 0.464ms returns FALSE
TA260 001:670.891 JLINK_HasError()
TA260 001:672.923 JLINK_IsHalted()
TA260 001:673.419 - 0.495ms returns FALSE
TA260 001:673.428 JLINK_HasError()
TA260 001:674.921 JLINK_IsHalted()
TA260 001:675.404 - 0.483ms returns FALSE
TA260 001:675.411 JLINK_HasError()
TA260 001:676.922 JLINK_IsHalted()
TA260 001:677.396 - 0.473ms returns FALSE
TA260 001:677.402 JLINK_HasError()
TA260 001:678.923 JLINK_IsHalted()
TA260 001:679.435 - 0.511ms returns FALSE
TA260 001:679.442 JLINK_HasError()
TA260 001:680.921 JLINK_IsHalted()
TA260 001:681.423 - 0.501ms returns FALSE
TA260 001:681.430 JLINK_HasError()
TA260 001:683.435 JLINK_IsHalted()
TA260 001:683.916 - 0.481ms returns FALSE
TA260 001:683.925 JLINK_HasError()
TA260 001:685.432 JLINK_IsHalted()
TA260 001:685.940 - 0.507ms returns FALSE
TA260 001:685.954 JLINK_HasError()
TA260 001:688.437 JLINK_IsHalted()
TA260 001:688.927 - 0.489ms returns FALSE
TA260 001:688.934 JLINK_HasError()
TA260 001:690.436 JLINK_IsHalted()
TA260 001:690.949 - 0.512ms returns FALSE
TA260 001:690.958 JLINK_HasError()
TA260 001:692.940 JLINK_IsHalted()
TA260 001:693.395 - 0.455ms returns FALSE
TA260 001:693.404 JLINK_HasError()
TA260 001:694.939 JLINK_IsHalted()
TA260 001:695.427 - 0.488ms returns FALSE
TA260 001:695.434 JLINK_HasError()
TA260 001:696.941 JLINK_IsHalted()
TA260 001:697.376 - 0.434ms returns FALSE
TA260 001:697.386 JLINK_HasError()
TA260 001:698.940 JLINK_IsHalted()
TA260 001:699.458 - 0.517ms returns FALSE
TA260 001:699.465 JLINK_HasError()
TA260 001:700.944 JLINK_IsHalted()
TA260 001:701.392 - 0.448ms returns FALSE
TA260 001:701.401 JLINK_HasError()
TA260 001:702.441 JLINK_IsHalted()
TA260 001:702.896 - 0.455ms returns FALSE
TA260 001:702.908 JLINK_HasError()
TA260 001:704.448 JLINK_IsHalted()
TA260 001:704.923 - 0.475ms returns FALSE
TA260 001:704.931 JLINK_HasError()
TA260 001:706.446 JLINK_IsHalted()
TA260 001:706.916 - 0.469ms returns FALSE
TA260 001:706.926 JLINK_HasError()
TA260 001:708.452 JLINK_IsHalted()
TA260 001:708.995 - 0.543ms returns FALSE
TA260 001:709.003 JLINK_HasError()
TA260 001:710.446 JLINK_IsHalted()
TA260 001:710.949 - 0.502ms returns FALSE
TA260 001:710.958 JLINK_HasError()
TA260 001:712.953 JLINK_IsHalted()
TA260 001:713.419 - 0.466ms returns FALSE
TA260 001:713.425 JLINK_HasError()
TA260 001:714.954 JLINK_IsHalted()
TA260 001:715.469 - 0.514ms returns FALSE
TA260 001:715.489 JLINK_HasError()
TA260 001:716.952 JLINK_IsHalted()
TA260 001:717.417 - 0.465ms returns FALSE
TA260 001:717.425 JLINK_HasError()
TA260 001:718.965 JLINK_IsHalted()
TA260 001:719.460 - 0.495ms returns FALSE
TA260 001:719.467 JLINK_HasError()
TA260 001:720.951 JLINK_IsHalted()
TA260 001:721.415 - 0.463ms returns FALSE
TA260 001:721.424 JLINK_HasError()
TA260 001:723.460 JLINK_IsHalted()
TA260 001:723.911 - 0.451ms returns FALSE
TA260 001:723.918 JLINK_HasError()
TA260 001:725.464 JLINK_IsHalted()
TA260 001:725.964 - 0.500ms returns FALSE
TA260 001:725.973 JLINK_HasError()
TA260 001:727.464 JLINK_IsHalted()
TA260 001:727.915 - 0.450ms returns FALSE
TA260 001:727.924 JLINK_HasError()
TA260 001:729.461 JLINK_IsHalted()
TA260 001:729.964 - 0.503ms returns FALSE
TA260 001:729.972 JLINK_HasError()
TA260 001:731.965 JLINK_IsHalted()
TA260 001:732.402 - 0.436ms returns FALSE
TA260 001:732.410 JLINK_HasError()
TA260 001:733.967 JLINK_IsHalted()
TA260 001:734.459 - 0.491ms returns FALSE
TA260 001:734.466 JLINK_HasError()
TA260 001:735.971 JLINK_IsHalted()
TA260 001:736.495 - 0.524ms returns FALSE
TA260 001:736.502 JLINK_HasError()
TA260 001:737.969 JLINK_IsHalted()
TA260 001:738.446 - 0.476ms returns FALSE
TA260 001:738.452 JLINK_HasError()
TA260 001:739.974 JLINK_IsHalted()
TA260 001:740.516 - 0.542ms returns FALSE
TA260 001:740.529 JLINK_HasError()
TA260 001:741.968 JLINK_IsHalted()
TA260 001:742.415 - 0.446ms returns FALSE
TA260 001:742.422 JLINK_HasError()
TA260 001:743.475 JLINK_IsHalted()
TA260 001:743.921 - 0.446ms returns FALSE
TA260 001:743.928 JLINK_HasError()
TA260 001:745.478 JLINK_IsHalted()
TA260 001:746.018 - 0.539ms returns FALSE
TA260 001:746.027 JLINK_HasError()
TA260 001:747.478 JLINK_IsHalted()
TA260 001:747.963 - 0.484ms returns FALSE
TA260 001:747.973 JLINK_HasError()
TA260 001:749.482 JLINK_IsHalted()
TA260 001:749.982 - 0.500ms returns FALSE
TA260 001:749.990 JLINK_HasError()
TA260 001:751.981 JLINK_IsHalted()
TA260 001:752.459 - 0.477ms returns FALSE
TA260 001:752.465 JLINK_HasError()
TA260 001:753.981 JLINK_IsHalted()
TA260 001:754.493 - 0.512ms returns FALSE
TA260 001:754.500 JLINK_HasError()
TA260 001:755.989 JLINK_IsHalted()
TA260 001:756.518 - 0.529ms returns FALSE
TA260 001:756.527 JLINK_HasError()
TA260 001:757.983 JLINK_IsHalted()
TA260 001:758.428 - 0.444ms returns FALSE
TA260 001:758.437 JLINK_HasError()
TA260 001:759.990 JLINK_IsHalted()
TA260 001:760.460 - 0.470ms returns FALSE
TA260 001:760.468 JLINK_HasError()
TA260 001:761.986 JLINK_IsHalted()
TA260 001:762.448 - 0.461ms returns FALSE
TA260 001:762.457 JLINK_HasError()
TA260 001:764.491 JLINK_IsHalted()
TA260 001:764.983 - 0.491ms returns FALSE
TA260 001:764.992 JLINK_HasError()
TA260 001:766.490 JLINK_IsHalted()
TA260 001:766.972 - 0.481ms returns FALSE
TA260 001:766.982 JLINK_HasError()
TA260 001:768.493 JLINK_IsHalted()
TA260 001:769.015 - 0.522ms returns FALSE
TA260 001:769.022 JLINK_HasError()
TA260 001:770.490 JLINK_IsHalted()
TA260 001:770.960 - 0.470ms returns FALSE
TA260 001:770.968 JLINK_HasError()
TA260 001:772.992 JLINK_IsHalted()
TA260 001:773.458 - 0.465ms returns FALSE
TA260 001:773.464 JLINK_HasError()
TA260 001:774.993 JLINK_IsHalted()
TA260 001:775.493 - 0.500ms returns FALSE
TA260 001:775.500 JLINK_HasError()
TA260 001:776.998 JLINK_IsHalted()
TA260 001:777.461 - 0.463ms returns FALSE
TA260 001:777.470 JLINK_HasError()
TA260 001:778.999 JLINK_IsHalted()
TA260 001:779.516 - 0.516ms returns FALSE
TA260 001:779.522 JLINK_HasError()
TA260 001:782.003 JLINK_IsHalted()
TA260 001:782.542 - 0.538ms returns FALSE
TA260 001:782.556 JLINK_HasError()
TA260 001:784.502 JLINK_IsHalted()
TA260 001:784.974 - 0.471ms returns FALSE
TA260 001:784.982 JLINK_HasError()
TA260 001:786.506 JLINK_IsHalted()
TA260 001:787.020 - 0.514ms returns FALSE
TA260 001:787.028 JLINK_HasError()
TA260 001:788.504 JLINK_IsHalted()
TA260 001:788.971 - 0.467ms returns FALSE
TA260 001:788.977 JLINK_HasError()
TA260 001:790.504 JLINK_IsHalted()
TA260 001:790.971 - 0.467ms returns FALSE
TA260 001:790.983 JLINK_HasError()
TA260 001:793.008 JLINK_IsHalted()
TA260 001:793.494 - 0.486ms returns FALSE
TA260 001:793.500 JLINK_HasError()
TA260 001:795.007 JLINK_IsHalted()
TA260 001:795.517 - 0.509ms returns FALSE
TA260 001:795.524 JLINK_HasError()
TA260 001:798.016 JLINK_IsHalted()
TA260 001:798.471 - 0.455ms returns FALSE
TA260 001:798.480 JLINK_HasError()
TA260 001:800.009 JLINK_IsHalted()
TA260 001:800.464 - 0.455ms returns FALSE
TA260 001:800.471 JLINK_HasError()
TA260 001:802.014 JLINK_IsHalted()
TA260 001:802.462 - 0.447ms returns FALSE
TA260 001:802.468 JLINK_HasError()
TA260 001:803.518 JLINK_IsHalted()
TA260 001:804.038 - 0.520ms returns FALSE
TA260 001:804.045 JLINK_HasError()
TA260 001:805.518 JLINK_IsHalted()
TA260 001:805.970 - 0.452ms returns FALSE
TA260 001:805.977 JLINK_HasError()
TA260 001:807.520 JLINK_IsHalted()
TA260 001:808.016 - 0.495ms returns FALSE
TA260 001:808.022 JLINK_HasError()
TA260 001:809.516 JLINK_IsHalted()
TA260 001:809.985 - 0.468ms returns FALSE
TA260 001:809.995 JLINK_HasError()
TA260 001:812.022 JLINK_IsHalted()
TA260 001:812.540 - 0.517ms returns FALSE
TA260 001:812.548 JLINK_HasError()
TA260 001:814.024 JLINK_IsHalted()
TA260 001:814.514 - 0.490ms returns FALSE
TA260 001:814.520 JLINK_HasError()
TA260 001:816.027 JLINK_IsHalted()
TA260 001:816.498 - 0.471ms returns FALSE
TA260 001:816.512 JLINK_HasError()
TA260 001:818.024 JLINK_IsHalted()
TA260 001:818.495 - 0.470ms returns FALSE
TA260 001:818.504 JLINK_HasError()
TA260 001:820.025 JLINK_IsHalted()
TA260 001:820.544 - 0.518ms returns FALSE
TA260 001:820.551 JLINK_HasError()
TA260 001:822.031 JLINK_IsHalted()
TA260 001:822.465 - 0.433ms returns FALSE
TA260 001:822.471 JLINK_HasError()
TA260 001:823.531 JLINK_IsHalted()
TA260 001:825.858   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:826.343 - 2.811ms returns TRUE
TA260 001:826.351 JLINK_ReadReg(R15 (PC))
TA260 001:826.357 - 0.006ms returns 0x20000000
TA260 001:826.362 JLINK_ClrBPEx(BPHandle = 0x00000007)
TA260 001:826.365 - 0.004ms returns 0x00
TA260 001:826.370 JLINK_ReadReg(R0)
TA260 001:826.374 - 0.003ms returns 0x00000000
TA260 001:826.970 JLINK_HasError()
TA260 001:826.985 JLINK_WriteReg(R0, 0x0800C000)
TA260 001:826.990 - 0.005ms returns 0
TA260 001:826.994 JLINK_WriteReg(R1, 0x00004000)
TA260 001:826.998 - 0.003ms returns 0
TA260 001:827.002 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:827.005 - 0.003ms returns 0
TA260 001:827.009 JLINK_WriteReg(R3, 0x00000000)
TA260 001:827.012 - 0.003ms returns 0
TA260 001:827.016 JLINK_WriteReg(R4, 0x00000000)
TA260 001:827.020 - 0.003ms returns 0
TA260 001:827.024 JLINK_WriteReg(R5, 0x00000000)
TA260 001:827.027 - 0.003ms returns 0
TA260 001:827.032 JLINK_WriteReg(R6, 0x00000000)
TA260 001:827.035 - 0.003ms returns 0
TA260 001:827.039 JLINK_WriteReg(R7, 0x00000000)
TA260 001:827.043 - 0.003ms returns 0
TA260 001:827.047 JLINK_WriteReg(R8, 0x00000000)
TA260 001:827.050 - 0.003ms returns 0
TA260 001:827.054 JLINK_WriteReg(R9, 0x20000180)
TA260 001:827.058 - 0.003ms returns 0
TA260 001:827.062 JLINK_WriteReg(R10, 0x00000000)
TA260 001:827.065 - 0.003ms returns 0
TA260 001:827.069 JLINK_WriteReg(R11, 0x00000000)
TA260 001:827.073 - 0.003ms returns 0
TA260 001:827.077 JLINK_WriteReg(R12, 0x00000000)
TA260 001:827.080 - 0.003ms returns 0
TA260 001:827.084 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:827.089 - 0.004ms returns 0
TA260 001:827.093 JLINK_WriteReg(R14, 0x20000001)
TA260 001:827.096 - 0.003ms returns 0
TA260 001:827.100 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 001:827.109 - 0.008ms returns 0
TA260 001:827.116 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:827.120 - 0.004ms returns 0
TA260 001:827.124 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:827.127 - 0.003ms returns 0
TA260 001:827.131 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:827.134 - 0.003ms returns 0
TA260 001:827.139 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:827.142 - 0.003ms returns 0
TA260 001:827.148 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:827.152 - 0.004ms returns 0x00000008
TA260 001:827.156 JLINK_Go()
TA260 001:827.167   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:829.872 - 2.715ms 
TA260 001:829.882 JLINK_IsHalted()
TA260 001:832.165   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:832.672 - 2.790ms returns TRUE
TA260 001:832.681 JLINK_ReadReg(R15 (PC))
TA260 001:832.686 - 0.004ms returns 0x20000000
TA260 001:832.691 JLINK_ClrBPEx(BPHandle = 0x00000008)
TA260 001:832.695 - 0.003ms returns 0x00
TA260 001:832.699 JLINK_ReadReg(R0)
TA260 001:832.703 - 0.003ms returns 0x00000001
TA260 001:832.708 JLINK_HasError()
TA260 001:832.713 JLINK_WriteReg(R0, 0x0800C000)
TA260 001:832.717 - 0.003ms returns 0
TA260 001:832.721 JLINK_WriteReg(R1, 0x00004000)
TA260 001:832.725 - 0.003ms returns 0
TA260 001:832.729 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:832.732 - 0.003ms returns 0
TA260 001:832.737 JLINK_WriteReg(R3, 0x00000000)
TA260 001:832.740 - 0.003ms returns 0
TA260 001:832.744 JLINK_WriteReg(R4, 0x00000000)
TA260 001:832.748 - 0.003ms returns 0
TA260 001:832.752 JLINK_WriteReg(R5, 0x00000000)
TA260 001:832.755 - 0.003ms returns 0
TA260 001:832.759 JLINK_WriteReg(R6, 0x00000000)
TA260 001:832.763 - 0.003ms returns 0
TA260 001:832.766 JLINK_WriteReg(R7, 0x00000000)
TA260 001:832.770 - 0.003ms returns 0
TA260 001:832.774 JLINK_WriteReg(R8, 0x00000000)
TA260 001:832.777 - 0.003ms returns 0
TA260 001:832.781 JLINK_WriteReg(R9, 0x20000180)
TA260 001:832.785 - 0.003ms returns 0
TA260 001:832.789 JLINK_WriteReg(R10, 0x00000000)
TA260 001:832.803 - 0.014ms returns 0
TA260 001:832.808 JLINK_WriteReg(R11, 0x00000000)
TA260 001:832.811 - 0.003ms returns 0
TA260 001:832.815 JLINK_WriteReg(R12, 0x00000000)
TA260 001:832.819 - 0.003ms returns 0
TA260 001:832.823 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:832.845 - 0.022ms returns 0
TA260 001:832.849 JLINK_WriteReg(R14, 0x20000001)
TA260 001:832.853 - 0.003ms returns 0
TA260 001:832.857 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 001:832.860 - 0.003ms returns 0
TA260 001:832.864 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:832.868 - 0.003ms returns 0
TA260 001:832.872 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:832.875 - 0.003ms returns 0
TA260 001:832.879 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:832.883 - 0.003ms returns 0
TA260 001:832.887 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:832.890 - 0.003ms returns 0
TA260 001:832.895 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:832.898 - 0.004ms returns 0x00000009
TA260 001:832.902 JLINK_Go()
TA260 001:832.910   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 001:835.651 - 2.748ms 
TA260 001:835.660 JLINK_IsHalted()
TA260 001:836.168 - 0.508ms returns FALSE
TA260 001:836.183 JLINK_HasError()
TA260 001:838.044 JLINK_IsHalted()
TA260 001:838.555 - 0.511ms returns FALSE
TA260 001:838.567 JLINK_HasError()
TA260 001:840.040 JLINK_IsHalted()
TA260 001:840.507 - 0.466ms returns FALSE
TA260 001:840.520 JLINK_HasError()
TA260 001:842.041 JLINK_IsHalted()
TA260 001:842.496 - 0.455ms returns FALSE
TA260 001:842.503 JLINK_HasError()
TA260 001:843.544 JLINK_IsHalted()
TA260 001:844.027 - 0.482ms returns FALSE
TA260 001:844.034 JLINK_HasError()
TA260 001:845.549 JLINK_IsHalted()
TA260 001:846.053 - 0.504ms returns FALSE
TA260 001:846.061 JLINK_HasError()
TA260 001:847.547 JLINK_IsHalted()
TA260 001:848.029 - 0.481ms returns FALSE
TA260 001:848.038 JLINK_HasError()
TA260 001:849.546 JLINK_IsHalted()
TA260 001:850.062 - 0.515ms returns FALSE
TA260 001:850.070 JLINK_HasError()
TA260 001:852.052 JLINK_IsHalted()
TA260 001:852.571 - 0.518ms returns FALSE
TA260 001:852.582 JLINK_HasError()
TA260 001:854.050 JLINK_IsHalted()
TA260 001:854.539 - 0.488ms returns FALSE
TA260 001:854.545 JLINK_HasError()
TA260 001:856.056 JLINK_IsHalted()
TA260 001:856.519 - 0.462ms returns FALSE
TA260 001:856.526 JLINK_HasError()
TA260 001:858.054 JLINK_IsHalted()
TA260 001:858.518 - 0.463ms returns FALSE
TA260 001:858.527 JLINK_HasError()
TA260 001:860.050 JLINK_IsHalted()
TA260 001:860.562 - 0.511ms returns FALSE
TA260 001:860.570 JLINK_HasError()
TA260 001:862.057 JLINK_IsHalted()
TA260 001:862.494 - 0.437ms returns FALSE
TA260 001:862.500 JLINK_HasError()
TA260 001:863.558 JLINK_IsHalted()
TA260 001:864.037 - 0.479ms returns FALSE
TA260 001:864.044 JLINK_HasError()
TA260 001:865.567 JLINK_IsHalted()
TA260 001:866.032 - 0.466ms returns FALSE
TA260 001:866.043 JLINK_HasError()
TA260 001:867.559 JLINK_IsHalted()
TA260 001:868.060 - 0.500ms returns FALSE
TA260 001:868.067 JLINK_HasError()
TA260 001:869.563 JLINK_IsHalted()
TA260 001:870.074 - 0.511ms returns FALSE
TA260 001:870.082 JLINK_HasError()
TA260 001:872.066 JLINK_IsHalted()
TA260 001:872.541 - 0.475ms returns FALSE
TA260 001:872.700 JLINK_HasError()
TA260 001:874.064 JLINK_IsHalted()
TA260 001:874.650 - 0.586ms returns FALSE
TA260 001:874.657 JLINK_HasError()
TA260 001:876.068 JLINK_IsHalted()
TA260 001:876.518 - 0.450ms returns FALSE
TA260 001:876.524 JLINK_HasError()
TA260 001:878.067 JLINK_IsHalted()
TA260 001:878.505 - 0.438ms returns FALSE
TA260 001:878.513 JLINK_HasError()
TA260 001:880.066 JLINK_IsHalted()
TA260 001:880.552 - 0.485ms returns FALSE
TA260 001:880.560 JLINK_HasError()
TA260 001:882.069 JLINK_IsHalted()
TA260 001:882.540 - 0.470ms returns FALSE
TA260 001:882.548 JLINK_HasError()
TA260 001:884.568 JLINK_IsHalted()
TA260 001:885.080 - 0.511ms returns FALSE
TA260 001:885.088 JLINK_HasError()
TA260 001:886.568 JLINK_IsHalted()
TA260 001:887.192 - 0.624ms returns FALSE
TA260 001:887.212 JLINK_HasError()
TA260 001:888.572 JLINK_IsHalted()
TA260 001:889.084 - 0.511ms returns FALSE
TA260 001:889.090 JLINK_HasError()
TA260 001:890.568 JLINK_IsHalted()
TA260 001:891.001 - 0.432ms returns FALSE
TA260 001:891.007 JLINK_HasError()
TA260 001:892.072 JLINK_IsHalted()
TA260 001:892.559 - 0.486ms returns FALSE
TA260 001:892.569 JLINK_HasError()
TA260 001:894.076 JLINK_IsHalted()
TA260 001:894.558 - 0.481ms returns FALSE
TA260 001:894.564 JLINK_HasError()
TA260 001:896.075 JLINK_IsHalted()
TA260 001:896.691 - 0.615ms returns FALSE
TA260 001:896.701 JLINK_HasError()
TA260 001:898.075 JLINK_IsHalted()
TA260 001:898.548 - 0.472ms returns FALSE
TA260 001:898.554 JLINK_HasError()
TA260 001:900.074 JLINK_IsHalted()
TA260 001:900.546 - 0.471ms returns FALSE
TA260 001:900.552 JLINK_HasError()
TA260 001:902.075 JLINK_IsHalted()
TA260 001:902.642 - 0.566ms returns FALSE
TA260 001:902.650 JLINK_HasError()
TA260 001:904.583 JLINK_IsHalted()
TA260 001:905.069 - 0.486ms returns FALSE
TA260 001:905.076 JLINK_HasError()
TA260 001:906.663 JLINK_IsHalted()
TA260 001:907.128 - 0.465ms returns FALSE
TA260 001:907.141 JLINK_HasError()
TA260 001:908.584 JLINK_IsHalted()
TA260 001:909.040 - 0.455ms returns FALSE
TA260 001:909.046 JLINK_HasError()
TA260 001:910.581 JLINK_IsHalted()
TA260 001:911.035 - 0.454ms returns FALSE
TA260 001:911.041 JLINK_HasError()
TA260 001:912.084 JLINK_IsHalted()
TA260 001:912.570 - 0.486ms returns FALSE
TA260 001:912.576 JLINK_HasError()
TA260 001:914.089 JLINK_IsHalted()
TA260 001:914.646 - 0.557ms returns FALSE
TA260 001:914.652 JLINK_HasError()
TA260 001:916.087 JLINK_IsHalted()
TA260 001:916.559 - 0.471ms returns FALSE
TA260 001:916.565 JLINK_HasError()
TA260 001:918.092 JLINK_IsHalted()
TA260 001:918.554 - 0.462ms returns FALSE
TA260 001:918.567 JLINK_HasError()
TA260 001:920.090 JLINK_IsHalted()
TA260 001:920.639 - 0.549ms returns FALSE
TA260 001:920.645 JLINK_HasError()
TA260 001:922.089 JLINK_IsHalted()
TA260 001:922.684 - 0.595ms returns FALSE
TA260 001:922.690 JLINK_HasError()
TA260 001:924.591 JLINK_IsHalted()
TA260 001:925.071 - 0.479ms returns FALSE
TA260 001:925.077 JLINK_HasError()
TA260 001:926.682 JLINK_IsHalted()
TA260 001:927.142 - 0.459ms returns FALSE
TA260 001:927.154 JLINK_HasError()
TA260 001:928.594 JLINK_IsHalted()
TA260 001:929.176 - 0.581ms returns FALSE
TA260 001:929.188 JLINK_HasError()
TA260 001:930.592 JLINK_IsHalted()
TA260 001:931.073 - 0.480ms returns FALSE
TA260 001:931.079 JLINK_HasError()
TA260 001:933.097 JLINK_IsHalted()
TA260 001:933.648 - 0.551ms returns FALSE
TA260 001:933.656 JLINK_HasError()
TA260 001:935.096 JLINK_IsHalted()
TA260 001:935.637 - 0.541ms returns FALSE
TA260 001:935.643 JLINK_HasError()
TA260 001:937.098 JLINK_IsHalted()
TA260 001:937.652 - 0.553ms returns FALSE
TA260 001:937.663 JLINK_HasError()
TA260 001:939.098 JLINK_IsHalted()
TA260 001:939.640 - 0.541ms returns FALSE
TA260 001:939.645 JLINK_HasError()
TA260 001:941.096 JLINK_IsHalted()
TA260 001:941.548 - 0.451ms returns FALSE
TA260 001:941.554 JLINK_HasError()
TA260 001:942.598 JLINK_IsHalted()
TA260 001:943.035 - 0.436ms returns FALSE
TA260 001:943.042 JLINK_HasError()
TA260 001:944.601 JLINK_IsHalted()
TA260 001:945.082 - 0.480ms returns FALSE
TA260 001:945.087 JLINK_HasError()
TA260 001:946.689 JLINK_IsHalted()
TA260 001:947.163 - 0.473ms returns FALSE
TA260 001:947.172 JLINK_HasError()
TA260 001:948.605 JLINK_IsHalted()
TA260 001:949.164 - 0.558ms returns FALSE
TA260 001:949.174 JLINK_HasError()
TA260 001:952.107 JLINK_IsHalted()
TA260 001:952.639 - 0.531ms returns FALSE
TA260 001:952.645 JLINK_HasError()
TA260 001:954.110 JLINK_IsHalted()
TA260 001:954.640 - 0.529ms returns FALSE
TA260 001:954.645 JLINK_HasError()
TA260 001:956.112 JLINK_IsHalted()
TA260 001:956.637 - 0.525ms returns FALSE
TA260 001:956.645 JLINK_HasError()
TA260 001:958.111 JLINK_IsHalted()
TA260 001:958.639 - 0.528ms returns FALSE
TA260 001:958.646 JLINK_HasError()
TA260 001:960.108 JLINK_IsHalted()
TA260 001:960.639 - 0.530ms returns FALSE
TA260 001:960.644 JLINK_HasError()
TA260 001:962.109 JLINK_IsHalted()
TA260 001:962.702 - 0.592ms returns FALSE
TA260 001:962.714 JLINK_HasError()
TA260 001:964.614 JLINK_IsHalted()
TA260 001:965.288 - 0.673ms returns FALSE
TA260 001:965.299 JLINK_HasError()
TA260 001:966.716 JLINK_IsHalted()
TA260 001:967.123 - 0.406ms returns FALSE
TA260 001:967.134 JLINK_HasError()
TA260 001:968.617 JLINK_IsHalted()
TA260 001:969.118 - 0.499ms returns FALSE
TA260 001:969.124 JLINK_HasError()
TA260 001:970.613 JLINK_IsHalted()
TA260 001:971.072 - 0.458ms returns FALSE
TA260 001:971.077 JLINK_HasError()
TA260 001:972.116 JLINK_IsHalted()
TA260 001:972.645 - 0.528ms returns FALSE
TA260 001:972.651 JLINK_HasError()
TA260 001:974.120 JLINK_IsHalted()
TA260 001:974.640 - 0.519ms returns FALSE
TA260 001:974.646 JLINK_HasError()
TA260 001:976.119 JLINK_IsHalted()
TA260 001:976.650 - 0.529ms returns FALSE
TA260 001:976.661 JLINK_HasError()
TA260 001:978.120 JLINK_IsHalted()
TA260 001:978.559 - 0.438ms returns FALSE
TA260 001:978.565 JLINK_HasError()
TA260 001:980.119 JLINK_IsHalted()
TA260 001:980.777 - 0.657ms returns FALSE
TA260 001:980.788 JLINK_HasError()
TA260 001:982.120 JLINK_IsHalted()
TA260 001:982.638 - 0.517ms returns FALSE
TA260 001:982.643 JLINK_HasError()
TA260 001:984.623 JLINK_IsHalted()
TA260 001:985.106 - 0.482ms returns FALSE
TA260 001:985.112 JLINK_HasError()
TA260 001:986.715 JLINK_IsHalted()
TA260 001:987.266 - 0.550ms returns FALSE
TA260 001:987.276 JLINK_HasError()
TA260 001:990.626 JLINK_IsHalted()
TA260 001:991.117 - 0.490ms returns FALSE
TA260 001:991.124 JLINK_HasError()
TA260 001:993.131 JLINK_IsHalted()
TA260 001:993.640 - 0.509ms returns FALSE
TA260 001:993.651 JLINK_HasError()
TA260 001:995.129 JLINK_IsHalted()
TA260 001:995.647 - 0.518ms returns FALSE
TA260 001:995.689 JLINK_HasError()
TA260 001:997.132 JLINK_IsHalted()
TA260 001:997.652 - 0.519ms returns FALSE
TA260 001:997.660 JLINK_HasError()
TA260 001:999.132 JLINK_IsHalted()
TA260 001:999.637 - 0.505ms returns FALSE
TA260 001:999.644 JLINK_HasError()
TA260 002:001.129 JLINK_IsHalted()
TA260 002:001.637 - 0.508ms returns FALSE
TA260 002:001.643 JLINK_HasError()
TA260 002:003.638 JLINK_IsHalted()
TA260 002:004.128 - 0.489ms returns FALSE
TA260 002:004.135 JLINK_HasError()
TA260 002:005.636 JLINK_IsHalted()
TA260 002:006.105 - 0.469ms returns FALSE
TA260 002:006.111 JLINK_HasError()
TA260 002:007.640 JLINK_IsHalted()
TA260 002:008.141 - 0.500ms returns FALSE
TA260 002:008.148 JLINK_HasError()
TA260 002:009.636 JLINK_IsHalted()
TA260 002:010.151 - 0.515ms returns FALSE
TA260 002:010.157 JLINK_HasError()
TA260 002:012.138 JLINK_IsHalted()
TA260 002:012.651 - 0.512ms returns FALSE
TA260 002:012.663 JLINK_HasError()
TA260 002:015.141 JLINK_IsHalted()
TA260 002:015.680 - 0.539ms returns FALSE
TA260 002:015.686 JLINK_HasError()
TA260 002:017.140 JLINK_IsHalted()
TA260 002:017.638 - 0.498ms returns FALSE
TA260 002:017.644 JLINK_HasError()
TA260 002:019.140 JLINK_IsHalted()
TA260 002:019.647 - 0.506ms returns FALSE
TA260 002:019.652 JLINK_HasError()
TA260 002:021.139 JLINK_IsHalted()
TA260 002:021.638 - 0.499ms returns FALSE
TA260 002:021.644 JLINK_HasError()
TA260 002:023.648 JLINK_IsHalted()
TA260 002:024.115 - 0.467ms returns FALSE
TA260 002:024.122 JLINK_HasError()
TA260 002:025.647 JLINK_IsHalted()
TA260 002:026.141 - 0.494ms returns FALSE
TA260 002:026.150 JLINK_HasError()
TA260 002:027.650 JLINK_IsHalted()
TA260 002:028.173 - 0.522ms returns FALSE
TA260 002:028.186 JLINK_HasError()
TA260 002:029.646 JLINK_IsHalted()
TA260 002:030.104 - 0.457ms returns FALSE
TA260 002:030.110 JLINK_HasError()
TA260 002:032.149 JLINK_IsHalted()
TA260 002:032.638 - 0.488ms returns FALSE
TA260 002:032.643 JLINK_HasError()
TA260 002:034.152 JLINK_IsHalted()
TA260 002:034.650 - 0.495ms returns FALSE
TA260 002:034.656 JLINK_HasError()
TA260 002:036.152 JLINK_IsHalted()
TA260 002:036.675 - 0.522ms returns FALSE
TA260 002:036.687 JLINK_HasError()
TA260 002:038.152 JLINK_IsHalted()
TA260 002:038.639 - 0.486ms returns FALSE
TA260 002:038.645 JLINK_HasError()
TA260 002:040.154 JLINK_IsHalted()
TA260 002:040.640 - 0.485ms returns FALSE
TA260 002:040.648 JLINK_HasError()
TA260 002:042.654 JLINK_IsHalted()
TA260 002:043.104 - 0.450ms returns FALSE
TA260 002:043.110 JLINK_HasError()
TA260 002:044.658 JLINK_IsHalted()
TA260 002:047.381 - 2.723ms returns FALSE
TA260 002:047.391 JLINK_HasError()
TA260 002:048.661 JLINK_IsHalted()
TA260 002:049.162 - 0.500ms returns FALSE
TA260 002:049.168 JLINK_HasError()
TA260 002:050.656 JLINK_IsHalted()
TA260 002:051.148 - 0.491ms returns FALSE
TA260 002:051.153 JLINK_HasError()
TA260 002:053.160 JLINK_IsHalted()
TA260 002:053.670 - 0.509ms returns FALSE
TA260 002:053.676 JLINK_HasError()
TA260 002:055.160 JLINK_IsHalted()
TA260 002:055.636 - 0.475ms returns FALSE
TA260 002:055.642 JLINK_HasError()
TA260 002:057.164 JLINK_IsHalted()
TA260 002:057.668 - 0.503ms returns FALSE
TA260 002:057.674 JLINK_HasError()
TA260 002:059.161 JLINK_IsHalted()
TA260 002:059.904 - 0.742ms returns FALSE
TA260 002:059.917 JLINK_HasError()
TA260 002:061.669 JLINK_IsHalted()
TA260 002:062.163 - 0.493ms returns FALSE
TA260 002:062.170 JLINK_HasError()
TA260 002:063.672 JLINK_IsHalted()
TA260 002:064.171 - 0.499ms returns FALSE
TA260 002:064.178 JLINK_HasError()
TA260 002:065.669 JLINK_IsHalted()
TA260 002:066.162 - 0.492ms returns FALSE
TA260 002:066.171 JLINK_HasError()
TA260 002:067.668 JLINK_IsHalted()
TA260 002:068.139 - 0.470ms returns FALSE
TA260 002:068.145 JLINK_HasError()
TA260 002:069.667 JLINK_IsHalted()
TA260 002:070.139 - 0.471ms returns FALSE
TA260 002:070.144 JLINK_HasError()
TA260 002:071.668 JLINK_IsHalted()
TA260 002:072.139 - 0.470ms returns FALSE
TA260 002:072.144 JLINK_HasError()
TA260 002:074.174 JLINK_IsHalted()
TA260 002:074.689 - 0.514ms returns FALSE
TA260 002:074.703 JLINK_HasError()
TA260 002:076.175 JLINK_IsHalted()
TA260 002:076.649 - 0.473ms returns FALSE
TA260 002:076.660 JLINK_HasError()
TA260 002:078.175 JLINK_IsHalted()
TA260 002:078.640 - 0.464ms returns FALSE
TA260 002:078.651 JLINK_HasError()
TA260 002:080.172 JLINK_IsHalted()
TA260 002:080.676 - 0.503ms returns FALSE
TA260 002:080.682 JLINK_HasError()
TA260 002:082.184 JLINK_IsHalted()
TA260 002:082.682 - 0.497ms returns FALSE
TA260 002:082.687 JLINK_HasError()
TA260 002:084.188 JLINK_IsHalted()
TA260 002:084.683 - 0.495ms returns FALSE
TA260 002:084.689 JLINK_HasError()
TA260 002:086.188 JLINK_IsHalted()
TA260 002:086.706 - 0.517ms returns FALSE
TA260 002:086.711 JLINK_HasError()
TA260 002:088.191 JLINK_IsHalted()
TA260 002:088.676 - 0.484ms returns FALSE
TA260 002:088.682 JLINK_HasError()
TA260 002:090.191 JLINK_IsHalted()
TA260 002:090.882 - 0.691ms returns FALSE
TA260 002:090.892 JLINK_HasError()
TA260 002:092.691 JLINK_IsHalted()
TA260 002:093.162 - 0.470ms returns FALSE
TA260 002:093.170 JLINK_HasError()
TA260 002:094.695 JLINK_IsHalted()
TA260 002:095.160 - 0.464ms returns FALSE
TA260 002:095.166 JLINK_HasError()
TA260 002:096.694 JLINK_IsHalted()
TA260 002:097.148 - 0.454ms returns FALSE
TA260 002:097.157 JLINK_HasError()
TA260 002:098.698 JLINK_IsHalted()
TA260 002:099.183 - 0.485ms returns FALSE
TA260 002:099.190 JLINK_HasError()
TA260 002:100.692 JLINK_IsHalted()
TA260 002:101.162 - 0.469ms returns FALSE
TA260 002:101.168 JLINK_HasError()
TA260 002:102.195 JLINK_IsHalted()
TA260 002:102.638 - 0.442ms returns FALSE
TA260 002:102.643 JLINK_HasError()
TA260 002:104.200 JLINK_IsHalted()
TA260 002:104.684 - 0.484ms returns FALSE
TA260 002:104.690 JLINK_HasError()
TA260 002:106.200 JLINK_IsHalted()
TA260 002:106.684 - 0.483ms returns FALSE
TA260 002:106.692 JLINK_HasError()
TA260 002:108.201 JLINK_IsHalted()
TA260 002:108.787 - 0.585ms returns FALSE
TA260 002:108.795 JLINK_HasError()
TA260 002:110.201 JLINK_IsHalted()
TA260 002:110.686 - 0.484ms returns FALSE
TA260 002:110.692 JLINK_HasError()
TA260 002:112.702 JLINK_IsHalted()
TA260 002:113.191 - 0.489ms returns FALSE
TA260 002:113.198 JLINK_HasError()
TA260 002:114.704 JLINK_IsHalted()
TA260 002:115.183 - 0.478ms returns FALSE
TA260 002:115.188 JLINK_HasError()
TA260 002:116.704 JLINK_IsHalted()
TA260 002:117.160 - 0.455ms returns FALSE
TA260 002:117.168 JLINK_HasError()
TA260 002:118.706 JLINK_IsHalted()
TA260 002:119.209 - 0.502ms returns FALSE
TA260 002:119.226 JLINK_HasError()
TA260 002:120.704 JLINK_IsHalted()
TA260 002:121.211 - 0.507ms returns FALSE
TA260 002:121.222 JLINK_HasError()
TA260 002:123.089 JLINK_IsHalted()
TA260 002:123.562 - 0.473ms returns FALSE
TA260 002:123.575 JLINK_HasError()
TA260 002:125.085 JLINK_IsHalted()
TA260 002:125.527 - 0.441ms returns FALSE
TA260 002:125.538 JLINK_HasError()
TA260 002:127.087 JLINK_IsHalted()
TA260 002:127.538 - 0.451ms returns FALSE
TA260 002:127.547 JLINK_HasError()
TA260 002:129.089 JLINK_IsHalted()
TA260 002:129.639 - 0.549ms returns FALSE
TA260 002:129.645 JLINK_HasError()
TA260 002:131.085 JLINK_IsHalted()
TA260 002:131.648 - 0.562ms returns FALSE
TA260 002:131.655 JLINK_HasError()
TA260 002:133.590 JLINK_IsHalted()
TA260 002:134.124 - 0.534ms returns FALSE
TA260 002:134.130 JLINK_HasError()
TA260 002:135.591 JLINK_IsHalted()
TA260 002:136.071 - 0.479ms returns FALSE
TA260 002:136.078 JLINK_HasError()
TA260 002:137.592 JLINK_IsHalted()
TA260 002:138.026 - 0.434ms returns FALSE
TA260 002:138.033 JLINK_HasError()
TA260 002:139.590 JLINK_IsHalted()
TA260 002:140.070 - 0.480ms returns FALSE
TA260 002:140.076 JLINK_HasError()
TA260 002:142.095 JLINK_IsHalted()
TA260 002:142.639 - 0.544ms returns FALSE
TA260 002:142.646 JLINK_HasError()
TA260 002:144.098 JLINK_IsHalted()
TA260 002:144.645 - 0.546ms returns FALSE
TA260 002:144.655 JLINK_HasError()
TA260 002:146.097 JLINK_IsHalted()
TA260 002:146.640 - 0.542ms returns FALSE
TA260 002:146.647 JLINK_HasError()
TA260 002:148.098 JLINK_IsHalted()
TA260 002:148.646 - 0.548ms returns FALSE
TA260 002:148.652 JLINK_HasError()
TA260 002:150.096 JLINK_IsHalted()
TA260 002:150.558 - 0.461ms returns FALSE
TA260 002:150.564 JLINK_HasError()
TA260 002:152.097 JLINK_IsHalted()
TA260 002:152.654 - 0.557ms returns FALSE
TA260 002:152.677 JLINK_HasError()
TA260 002:153.804 JLINK_IsHalted()
TA260 002:154.252 - 0.448ms returns FALSE
TA260 002:154.259 JLINK_HasError()
TA260 002:155.774 JLINK_IsHalted()
TA260 002:156.254 - 0.479ms returns FALSE
TA260 002:156.262 JLINK_HasError()
TA260 002:157.774 JLINK_IsHalted()
TA260 002:158.208 - 0.434ms returns FALSE
TA260 002:158.213 JLINK_HasError()
TA260 002:159.770 JLINK_IsHalted()
TA260 002:160.241 - 0.471ms returns FALSE
TA260 002:160.247 JLINK_HasError()
TA260 002:161.770 JLINK_IsHalted()
TA260 002:162.284 - 0.513ms returns FALSE
TA260 002:162.290 JLINK_HasError()
TA260 002:164.278 JLINK_IsHalted()
TA260 002:164.787 - 0.509ms returns FALSE
TA260 002:164.793 JLINK_HasError()
TA260 002:166.277 JLINK_IsHalted()
TA260 002:166.775 - 0.497ms returns FALSE
TA260 002:166.782 JLINK_HasError()
TA260 002:168.284 JLINK_IsHalted()
TA260 002:168.729 - 0.443ms returns FALSE
TA260 002:168.736 JLINK_HasError()
TA260 002:170.280 JLINK_IsHalted()
TA260 002:170.788 - 0.506ms returns FALSE
TA260 002:170.794 JLINK_HasError()
TA260 002:172.780 JLINK_IsHalted()
TA260 002:173.232 - 0.451ms returns FALSE
TA260 002:173.240 JLINK_HasError()
TA260 002:174.782 JLINK_IsHalted()
TA260 002:175.307 - 0.525ms returns FALSE
TA260 002:175.313 JLINK_HasError()
TA260 002:176.783 JLINK_IsHalted()
TA260 002:177.287 - 0.503ms returns FALSE
TA260 002:177.292 JLINK_HasError()
TA260 002:178.787 JLINK_IsHalted()
TA260 002:179.299 - 0.511ms returns FALSE
TA260 002:179.306 JLINK_HasError()
TA260 002:180.783 JLINK_IsHalted()
TA260 002:181.285 - 0.501ms returns FALSE
TA260 002:181.290 JLINK_HasError()
TA260 002:183.288 JLINK_IsHalted()
TA260 002:183.950 - 0.661ms returns FALSE
TA260 002:183.961 JLINK_HasError()
TA260 002:185.289 JLINK_IsHalted()
TA260 002:185.787 - 0.497ms returns FALSE
TA260 002:185.792 JLINK_HasError()
TA260 002:187.291 JLINK_IsHalted()
TA260 002:187.823 - 0.531ms returns FALSE
TA260 002:187.834 JLINK_HasError()
TA260 002:189.292 JLINK_IsHalted()
TA260 002:189.787 - 0.494ms returns FALSE
TA260 002:189.794 JLINK_HasError()
TA260 002:191.791 JLINK_IsHalted()
TA260 002:192.274 - 0.483ms returns FALSE
TA260 002:192.280 JLINK_HasError()
TA260 002:193.793 JLINK_IsHalted()
TA260 002:194.299 - 0.505ms returns FALSE
TA260 002:194.305 JLINK_HasError()
TA260 002:195.794 JLINK_IsHalted()
TA260 002:196.322 - 0.527ms returns FALSE
TA260 002:196.328 JLINK_HasError()
TA260 002:197.796 JLINK_IsHalted()
TA260 002:198.266 - 0.469ms returns FALSE
TA260 002:198.272 JLINK_HasError()
TA260 002:199.804 JLINK_IsHalted()
TA260 002:200.332 - 0.528ms returns FALSE
TA260 002:200.339 JLINK_HasError()
TA260 002:201.796 JLINK_IsHalted()
TA260 002:202.286 - 0.489ms returns FALSE
TA260 002:202.292 JLINK_HasError()
TA260 002:204.300 JLINK_IsHalted()
TA260 002:204.788 - 0.487ms returns FALSE
TA260 002:204.795 JLINK_HasError()
TA260 002:206.283 JLINK_IsHalted()
TA260 002:206.734 - 0.451ms returns FALSE
TA260 002:206.742 JLINK_HasError()
TA260 002:207.783 JLINK_IsHalted()
TA260 002:208.268 - 0.484ms returns FALSE
TA260 002:208.275 JLINK_HasError()
TA260 002:209.782 JLINK_IsHalted()
TA260 002:210.241 - 0.458ms returns FALSE
TA260 002:210.247 JLINK_HasError()
TA260 002:211.782 JLINK_IsHalted()
TA260 002:212.289 - 0.506ms returns FALSE
TA260 002:212.304 JLINK_HasError()
TA260 002:214.289 JLINK_IsHalted()
TA260 002:214.796 - 0.507ms returns FALSE
TA260 002:214.803 JLINK_HasError()
TA260 002:216.288 JLINK_IsHalted()
TA260 002:216.722 - 0.433ms returns FALSE
TA260 002:216.732 JLINK_HasError()
TA260 002:218.291 JLINK_IsHalted()
TA260 002:218.776 - 0.485ms returns FALSE
TA260 002:218.790 JLINK_HasError()
TA260 002:220.289 JLINK_IsHalted()
TA260 002:220.808 - 0.518ms returns FALSE
TA260 002:220.844 JLINK_HasError()
TA260 002:222.601 JLINK_IsHalted()
TA260 002:223.094 - 0.491ms returns FALSE
TA260 002:223.100 JLINK_HasError()
TA260 002:224.604 JLINK_IsHalted()
TA260 002:225.106 - 0.501ms returns FALSE
TA260 002:225.115 JLINK_HasError()
TA260 002:226.635 JLINK_IsHalted()
TA260 002:227.159 - 0.523ms returns FALSE
TA260 002:227.166 JLINK_HasError()
TA260 002:228.607 JLINK_IsHalted()
TA260 002:229.118 - 0.510ms returns FALSE
TA260 002:229.124 JLINK_HasError()
TA260 002:230.607 JLINK_IsHalted()
TA260 002:231.106 - 0.498ms returns FALSE
TA260 002:231.112 JLINK_HasError()
TA260 002:234.113 JLINK_IsHalted()
TA260 002:234.640 - 0.526ms returns FALSE
TA260 002:234.646 JLINK_HasError()
TA260 002:236.111 JLINK_IsHalted()
TA260 002:236.657 - 0.545ms returns FALSE
TA260 002:236.668 JLINK_HasError()
TA260 002:238.114 JLINK_IsHalted()
TA260 002:238.560 - 0.446ms returns FALSE
TA260 002:238.567 JLINK_HasError()
TA260 002:240.113 JLINK_IsHalted()
TA260 002:240.650 - 0.536ms returns FALSE
TA260 002:240.661 JLINK_HasError()
TA260 002:242.112 JLINK_IsHalted()
TA260 002:242.646 - 0.533ms returns FALSE
TA260 002:242.652 JLINK_HasError()
TA260 002:244.615 JLINK_IsHalted()
TA260 002:245.118 - 0.502ms returns FALSE
TA260 002:245.129 JLINK_HasError()
TA260 002:246.654 JLINK_IsHalted()
TA260 002:247.193 - 0.538ms returns FALSE
TA260 002:247.200 JLINK_HasError()
TA260 002:248.619 JLINK_IsHalted()
TA260 002:249.117 - 0.497ms returns FALSE
TA260 002:249.123 JLINK_HasError()
TA260 002:250.615 JLINK_IsHalted()
TA260 002:253.058   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:253.561 - 2.945ms returns TRUE
TA260 002:253.568 JLINK_ReadReg(R15 (PC))
TA260 002:253.573 - 0.005ms returns 0x20000000
TA260 002:253.577 JLINK_ClrBPEx(BPHandle = 0x00000009)
TA260 002:253.581 - 0.003ms returns 0x00
TA260 002:253.585 JLINK_ReadReg(R0)
TA260 002:253.589 - 0.003ms returns 0x00000000
TA260 002:253.837 JLINK_HasError()
TA260 002:253.846 JLINK_WriteReg(R0, 0x00000001)
TA260 002:253.850 - 0.004ms returns 0
TA260 002:253.854 JLINK_WriteReg(R1, 0x00004000)
TA260 002:253.858 - 0.003ms returns 0
TA260 002:253.862 JLINK_WriteReg(R2, 0x000000FF)
TA260 002:253.865 - 0.003ms returns 0
TA260 002:253.869 JLINK_WriteReg(R3, 0x00000000)
TA260 002:253.873 - 0.003ms returns 0
TA260 002:253.877 JLINK_WriteReg(R4, 0x00000000)
TA260 002:253.880 - 0.003ms returns 0
TA260 002:253.884 JLINK_WriteReg(R5, 0x00000000)
TA260 002:253.888 - 0.003ms returns 0
TA260 002:253.892 JLINK_WriteReg(R6, 0x00000000)
TA260 002:253.896 - 0.003ms returns 0
TA260 002:253.900 JLINK_WriteReg(R7, 0x00000000)
TA260 002:253.903 - 0.003ms returns 0
TA260 002:253.907 JLINK_WriteReg(R8, 0x00000000)
TA260 002:253.911 - 0.003ms returns 0
TA260 002:253.915 JLINK_WriteReg(R9, 0x20000180)
TA260 002:253.918 - 0.003ms returns 0
TA260 002:253.922 JLINK_WriteReg(R10, 0x00000000)
TA260 002:253.926 - 0.003ms returns 0
TA260 002:253.930 JLINK_WriteReg(R11, 0x00000000)
TA260 002:253.933 - 0.003ms returns 0
TA260 002:253.937 JLINK_WriteReg(R12, 0x00000000)
TA260 002:253.940 - 0.003ms returns 0
TA260 002:253.944 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:253.948 - 0.003ms returns 0
TA260 002:253.952 JLINK_WriteReg(R14, 0x20000001)
TA260 002:253.955 - 0.003ms returns 0
TA260 002:253.960 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 002:253.963 - 0.003ms returns 0
TA260 002:253.967 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:253.970 - 0.003ms returns 0
TA260 002:253.974 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:253.978 - 0.003ms returns 0
TA260 002:253.982 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:253.985 - 0.003ms returns 0
TA260 002:253.989 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:253.993 - 0.003ms returns 0
TA260 002:253.997 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:254.001 - 0.004ms returns 0x0000000A
TA260 002:254.005 JLINK_Go()
TA260 002:254.014   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:256.731 - 2.725ms 
TA260 002:256.744 JLINK_IsHalted()
TA260 002:259.076   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:259.649 - 2.905ms returns TRUE
TA260 002:259.657 JLINK_ReadReg(R15 (PC))
TA260 002:259.662 - 0.005ms returns 0x20000000
TA260 002:259.666 JLINK_ClrBPEx(BPHandle = 0x0000000A)
TA260 002:259.670 - 0.003ms returns 0x00
TA260 002:259.678 JLINK_ReadReg(R0)
TA260 002:259.683 - 0.004ms returns 0x00000000
TA260 002:314.220 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 002:314.235   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 002:314.252   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 002:316.430 - 2.209ms returns 0x184
TA260 002:316.462 JLINK_HasError()
TA260 002:316.468 JLINK_WriteReg(R0, 0x08000000)
TA260 002:316.474 - 0.005ms returns 0
TA260 002:316.478 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 002:316.481 - 0.003ms returns 0
TA260 002:316.485 JLINK_WriteReg(R2, 0x00000002)
TA260 002:316.489 - 0.003ms returns 0
TA260 002:316.493 JLINK_WriteReg(R3, 0x00000000)
TA260 002:316.496 - 0.003ms returns 0
TA260 002:316.500 JLINK_WriteReg(R4, 0x00000000)
TA260 002:316.504 - 0.003ms returns 0
TA260 002:316.508 JLINK_WriteReg(R5, 0x00000000)
TA260 002:316.511 - 0.003ms returns 0
TA260 002:316.515 JLINK_WriteReg(R6, 0x00000000)
TA260 002:316.518 - 0.003ms returns 0
TA260 002:316.522 JLINK_WriteReg(R7, 0x00000000)
TA260 002:316.526 - 0.003ms returns 0
TA260 002:316.530 JLINK_WriteReg(R8, 0x00000000)
TA260 002:316.533 - 0.003ms returns 0
TA260 002:316.538 JLINK_WriteReg(R9, 0x20000180)
TA260 002:316.541 - 0.003ms returns 0
TA260 002:316.545 JLINK_WriteReg(R10, 0x00000000)
TA260 002:316.548 - 0.003ms returns 0
TA260 002:316.722 JLINK_WriteReg(R11, 0x00000000)
TA260 002:316.727 - 0.004ms returns 0
TA260 002:316.731 JLINK_WriteReg(R12, 0x00000000)
TA260 002:316.734 - 0.003ms returns 0
TA260 002:316.738 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:316.742 - 0.004ms returns 0
TA260 002:316.747 JLINK_WriteReg(R14, 0x20000001)
TA260 002:316.750 - 0.003ms returns 0
TA260 002:316.754 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 002:316.758 - 0.003ms returns 0
TA260 002:316.762 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:316.765 - 0.003ms returns 0
TA260 002:316.769 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:316.773 - 0.003ms returns 0
TA260 002:316.777 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:316.780 - 0.003ms returns 0
TA260 002:316.784 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:316.788 - 0.003ms returns 0
TA260 002:316.793 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:316.800   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:317.524 - 0.731ms returns 0x0000000B
TA260 002:317.540 JLINK_Go()
TA260 002:317.546   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 002:318.080   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:320.878 - 3.337ms 
TA260 002:320.889 JLINK_IsHalted()
TA260 002:323.182   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:323.737 - 2.848ms returns TRUE
TA260 002:323.752 JLINK_ReadReg(R15 (PC))
TA260 002:323.757 - 0.005ms returns 0x20000000
TA260 002:323.762 JLINK_ClrBPEx(BPHandle = 0x0000000B)
TA260 002:323.766 - 0.004ms returns 0x00
TA260 002:323.771 JLINK_ReadReg(R0)
TA260 002:323.774 - 0.003ms returns 0x00000000
TA260 002:323.992 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:323.999   Data:  18 18 00 20 C1 01 00 08 D1 2A 00 08 B5 27 00 08 ...
TA260 002:324.012   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:326.645 - 2.652ms returns 0x27C
TA260 002:326.654 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:326.658   Data:  10 B5 13 46 0A 46 04 46 19 46 FF F7 F0 FF 20 46 ...
TA260 002:326.670   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:328.571 - 1.917ms returns 0x184
TA260 002:328.588 JLINK_HasError()
TA260 002:328.594 JLINK_WriteReg(R0, 0x08000000)
TA260 002:328.599 - 0.005ms returns 0
TA260 002:328.604 JLINK_WriteReg(R1, 0x00000400)
TA260 002:328.607 - 0.003ms returns 0
TA260 002:328.611 JLINK_WriteReg(R2, 0x20000184)
TA260 002:328.614 - 0.003ms returns 0
TA260 002:328.618 JLINK_WriteReg(R3, 0x00000000)
TA260 002:328.622 - 0.003ms returns 0
TA260 002:328.626 JLINK_WriteReg(R4, 0x00000000)
TA260 002:328.630 - 0.003ms returns 0
TA260 002:328.634 JLINK_WriteReg(R5, 0x00000000)
TA260 002:328.637 - 0.003ms returns 0
TA260 002:328.641 JLINK_WriteReg(R6, 0x00000000)
TA260 002:328.644 - 0.003ms returns 0
TA260 002:328.648 JLINK_WriteReg(R7, 0x00000000)
TA260 002:328.700 - 0.051ms returns 0
TA260 002:328.704 JLINK_WriteReg(R8, 0x00000000)
TA260 002:328.707 - 0.003ms returns 0
TA260 002:328.711 JLINK_WriteReg(R9, 0x20000180)
TA260 002:328.715 - 0.003ms returns 0
TA260 002:328.719 JLINK_WriteReg(R10, 0x00000000)
TA260 002:328.733 - 0.014ms returns 0
TA260 002:328.737 JLINK_WriteReg(R11, 0x00000000)
TA260 002:328.741 - 0.003ms returns 0
TA260 002:328.745 JLINK_WriteReg(R12, 0x00000000)
TA260 002:328.748 - 0.003ms returns 0
TA260 002:328.752 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:328.756 - 0.003ms returns 0
TA260 002:328.760 JLINK_WriteReg(R14, 0x20000001)
TA260 002:328.763 - 0.003ms returns 0
TA260 002:328.768 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:328.771 - 0.003ms returns 0
TA260 002:328.776 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:328.779 - 0.003ms returns 0
TA260 002:328.783 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:328.786 - 0.003ms returns 0
TA260 002:328.790 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:328.794 - 0.003ms returns 0
TA260 002:328.798 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:328.801 - 0.003ms returns 0
TA260 002:328.806 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:328.810 - 0.004ms returns 0x0000000C
TA260 002:328.814 JLINK_Go()
TA260 002:328.823   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:331.645 - 2.830ms 
TA260 002:331.657 JLINK_IsHalted()
TA260 002:332.144 - 0.487ms returns FALSE
TA260 002:332.154 JLINK_HasError()
TA260 002:335.937 JLINK_IsHalted()
TA260 002:338.282   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:338.777 - 2.839ms returns TRUE
TA260 002:338.785 JLINK_ReadReg(R15 (PC))
TA260 002:338.790 - 0.005ms returns 0x20000000
TA260 002:338.795 JLINK_ClrBPEx(BPHandle = 0x0000000C)
TA260 002:338.799 - 0.004ms returns 0x00
TA260 002:338.803 JLINK_ReadReg(R0)
TA260 002:338.807 - 0.004ms returns 0x00000000
TA260 002:340.139 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:340.152   Data:  5B D0 C3 F3 0A 54 C1 F3 0A 55 2C 44 A4 F2 F3 34 ...
TA260 002:340.166   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:342.732 - 2.593ms returns 0x27C
TA260 002:342.745 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:342.749   Data:  01 02 06 D0 0A 0D A2 F5 60 72 C1 F3 13 01 00 2A ...
TA260 002:342.759   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:344.641 - 1.895ms returns 0x184
TA260 002:344.648 JLINK_HasError()
TA260 002:344.654 JLINK_WriteReg(R0, 0x08000400)
TA260 002:344.658 - 0.004ms returns 0
TA260 002:344.662 JLINK_WriteReg(R1, 0x00000400)
TA260 002:344.666 - 0.004ms returns 0
TA260 002:344.670 JLINK_WriteReg(R2, 0x20000184)
TA260 002:344.674 - 0.003ms returns 0
TA260 002:344.678 JLINK_WriteReg(R3, 0x00000000)
TA260 002:344.681 - 0.003ms returns 0
TA260 002:344.685 JLINK_WriteReg(R4, 0x00000000)
TA260 002:344.688 - 0.003ms returns 0
TA260 002:344.692 JLINK_WriteReg(R5, 0x00000000)
TA260 002:344.696 - 0.003ms returns 0
TA260 002:344.700 JLINK_WriteReg(R6, 0x00000000)
TA260 002:344.704 - 0.003ms returns 0
TA260 002:344.708 JLINK_WriteReg(R7, 0x00000000)
TA260 002:344.711 - 0.003ms returns 0
TA260 002:344.715 JLINK_WriteReg(R8, 0x00000000)
TA260 002:344.718 - 0.003ms returns 0
TA260 002:344.722 JLINK_WriteReg(R9, 0x20000180)
TA260 002:344.726 - 0.003ms returns 0
TA260 002:344.730 JLINK_WriteReg(R10, 0x00000000)
TA260 002:344.733 - 0.003ms returns 0
TA260 002:344.737 JLINK_WriteReg(R11, 0x00000000)
TA260 002:344.740 - 0.003ms returns 0
TA260 002:344.744 JLINK_WriteReg(R12, 0x00000000)
TA260 002:344.748 - 0.003ms returns 0
TA260 002:344.752 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:344.756 - 0.003ms returns 0
TA260 002:344.760 JLINK_WriteReg(R14, 0x20000001)
TA260 002:344.763 - 0.003ms returns 0
TA260 002:344.767 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:344.771 - 0.003ms returns 0
TA260 002:344.775 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:344.778 - 0.003ms returns 0
TA260 002:344.782 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:344.786 - 0.003ms returns 0
TA260 002:344.790 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:344.796 - 0.006ms returns 0
TA260 002:344.802 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:344.806 - 0.003ms returns 0
TA260 002:344.810 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:344.814 - 0.004ms returns 0x0000000D
TA260 002:344.819 JLINK_Go()
TA260 002:344.827   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:347.534 - 2.715ms 
TA260 002:347.543 JLINK_IsHalted()
TA260 002:348.031 - 0.487ms returns FALSE
TA260 002:348.045 JLINK_HasError()
TA260 002:349.432 JLINK_IsHalted()
TA260 002:349.936 - 0.503ms returns FALSE
TA260 002:349.942 JLINK_HasError()
TA260 002:351.933 JLINK_IsHalted()
TA260 002:354.268   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:354.774 - 2.841ms returns TRUE
TA260 002:354.781 JLINK_ReadReg(R15 (PC))
TA260 002:354.785 - 0.004ms returns 0x20000000
TA260 002:354.790 JLINK_ClrBPEx(BPHandle = 0x0000000D)
TA260 002:354.794 - 0.003ms returns 0x00
TA260 002:354.798 JLINK_ReadReg(R0)
TA260 002:354.801 - 0.003ms returns 0x00000000
TA260 002:355.112 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:355.124   Data:  F0 4D 92 46 9B 46 11 B1 B1 FA 81 F2 02 E0 B0 FA ...
TA260 002:355.133   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:357.745 - 2.632ms returns 0x27C
TA260 002:357.758 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:357.762   Data:  F1 FD 20 46 4F F4 00 51 00 22 00 F0 EB FD 01 20 ...
TA260 002:357.771   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:359.644 - 1.886ms returns 0x184
TA260 002:359.658 JLINK_HasError()
TA260 002:359.664 JLINK_WriteReg(R0, 0x08000800)
TA260 002:359.669 - 0.005ms returns 0
TA260 002:359.673 JLINK_WriteReg(R1, 0x00000400)
TA260 002:359.677 - 0.003ms returns 0
TA260 002:359.681 JLINK_WriteReg(R2, 0x20000184)
TA260 002:359.684 - 0.003ms returns 0
TA260 002:359.689 JLINK_WriteReg(R3, 0x00000000)
TA260 002:359.692 - 0.003ms returns 0
TA260 002:359.696 JLINK_WriteReg(R4, 0x00000000)
TA260 002:359.700 - 0.003ms returns 0
TA260 002:359.704 JLINK_WriteReg(R5, 0x00000000)
TA260 002:359.707 - 0.003ms returns 0
TA260 002:359.711 JLINK_WriteReg(R6, 0x00000000)
TA260 002:359.714 - 0.003ms returns 0
TA260 002:359.718 JLINK_WriteReg(R7, 0x00000000)
TA260 002:359.722 - 0.003ms returns 0
TA260 002:359.726 JLINK_WriteReg(R8, 0x00000000)
TA260 002:359.729 - 0.003ms returns 0
TA260 002:359.734 JLINK_WriteReg(R9, 0x20000180)
TA260 002:359.737 - 0.003ms returns 0
TA260 002:359.741 JLINK_WriteReg(R10, 0x00000000)
TA260 002:359.744 - 0.003ms returns 0
TA260 002:359.748 JLINK_WriteReg(R11, 0x00000000)
TA260 002:359.752 - 0.003ms returns 0
TA260 002:359.756 JLINK_WriteReg(R12, 0x00000000)
TA260 002:359.759 - 0.003ms returns 0
TA260 002:359.763 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:359.767 - 0.003ms returns 0
TA260 002:359.771 JLINK_WriteReg(R14, 0x20000001)
TA260 002:359.774 - 0.003ms returns 0
TA260 002:359.778 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:359.782 - 0.003ms returns 0
TA260 002:359.786 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:359.789 - 0.003ms returns 0
TA260 002:359.793 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:359.797 - 0.003ms returns 0
TA260 002:359.801 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:359.805 - 0.003ms returns 0
TA260 002:359.809 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:359.812 - 0.003ms returns 0
TA260 002:359.816 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:359.821 - 0.004ms returns 0x0000000E
TA260 002:359.825 JLINK_Go()
TA260 002:359.832   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:362.537 - 2.711ms 
TA260 002:362.544 JLINK_IsHalted()
TA260 002:363.026 - 0.482ms returns FALSE
TA260 002:363.034 JLINK_HasError()
TA260 002:364.440 JLINK_IsHalted()
TA260 002:364.958 - 0.517ms returns FALSE
TA260 002:364.963 JLINK_HasError()
TA260 002:366.441 JLINK_IsHalted()
TA260 002:366.933 - 0.491ms returns FALSE
TA260 002:366.939 JLINK_HasError()
TA260 002:368.442 JLINK_IsHalted()
TA260 002:370.709   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:371.183 - 2.741ms returns TRUE
TA260 002:371.190 JLINK_ReadReg(R15 (PC))
TA260 002:371.197 - 0.007ms returns 0x20000000
TA260 002:371.203 JLINK_ClrBPEx(BPHandle = 0x0000000E)
TA260 002:371.207 - 0.003ms returns 0x00
TA260 002:371.211 JLINK_ReadReg(R0)
TA260 002:371.214 - 0.003ms returns 0x00000000
TA260 002:371.533 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:371.541   Data:  BD E8 F0 40 00 F0 2C BD 2D E9 F0 4F 81 B0 41 F6 ...
TA260 002:371.550   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:374.143 - 2.610ms returns 0x27C
TA260 002:374.156 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:374.160   Data:  05 F0 01 02 30 46 4F F4 80 51 00 F0 EB FB 20 46 ...
TA260 002:374.168   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:376.046 - 1.889ms returns 0x184
TA260 002:376.056 JLINK_HasError()
TA260 002:376.673 JLINK_WriteReg(R0, 0x08000C00)
TA260 002:376.685 - 0.012ms returns 0
TA260 002:376.690 JLINK_WriteReg(R1, 0x00000400)
TA260 002:376.694 - 0.004ms returns 0
TA260 002:376.699 JLINK_WriteReg(R2, 0x20000184)
TA260 002:376.703 - 0.004ms returns 0
TA260 002:376.708 JLINK_WriteReg(R3, 0x00000000)
TA260 002:376.711 - 0.003ms returns 0
TA260 002:376.715 JLINK_WriteReg(R4, 0x00000000)
TA260 002:376.718 - 0.003ms returns 0
TA260 002:376.722 JLINK_WriteReg(R5, 0x00000000)
TA260 002:376.726 - 0.003ms returns 0
TA260 002:376.730 JLINK_WriteReg(R6, 0x00000000)
TA260 002:376.733 - 0.003ms returns 0
TA260 002:376.737 JLINK_WriteReg(R7, 0x00000000)
TA260 002:376.741 - 0.003ms returns 0
TA260 002:376.745 JLINK_WriteReg(R8, 0x00000000)
TA260 002:376.748 - 0.003ms returns 0
TA260 002:376.752 JLINK_WriteReg(R9, 0x20000180)
TA260 002:376.756 - 0.003ms returns 0
TA260 002:376.760 JLINK_WriteReg(R10, 0x00000000)
TA260 002:376.763 - 0.003ms returns 0
TA260 002:376.767 JLINK_WriteReg(R11, 0x00000000)
TA260 002:376.771 - 0.003ms returns 0
TA260 002:376.774 JLINK_WriteReg(R12, 0x00000000)
TA260 002:376.778 - 0.003ms returns 0
TA260 002:376.782 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:376.786 - 0.004ms returns 0
TA260 002:376.790 JLINK_WriteReg(R14, 0x20000001)
TA260 002:376.793 - 0.003ms returns 0
TA260 002:376.798 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:376.801 - 0.003ms returns 0
TA260 002:376.805 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:376.809 - 0.003ms returns 0
TA260 002:376.813 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:376.816 - 0.003ms returns 0
TA260 002:376.820 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:376.823 - 0.003ms returns 0
TA260 002:376.827 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:376.831 - 0.003ms returns 0
TA260 002:376.836 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:376.840 - 0.004ms returns 0x0000000F
TA260 002:376.844 JLINK_Go()
TA260 002:376.852   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:379.566 - 2.722ms 
TA260 002:379.580 JLINK_IsHalted()
TA260 002:380.036 - 0.455ms returns FALSE
TA260 002:380.041 JLINK_HasError()
TA260 002:381.948 JLINK_IsHalted()
TA260 002:382.432 - 0.483ms returns FALSE
TA260 002:382.438 JLINK_HasError()
TA260 002:384.451 JLINK_IsHalted()
TA260 002:386.767   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:387.265 - 2.813ms returns TRUE
TA260 002:387.272 JLINK_ReadReg(R15 (PC))
TA260 002:387.276 - 0.004ms returns 0x20000000
TA260 002:387.281 JLINK_ClrBPEx(BPHandle = 0x0000000F)
TA260 002:387.285 - 0.003ms returns 0x00
TA260 002:387.289 JLINK_ReadReg(R0)
TA260 002:387.292 - 0.003ms returns 0x00000000
TA260 002:387.606 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:387.613   Data:  00 90 00 98 14 28 E7 DB 02 B0 BD EC 0A 8B BD E8 ...
TA260 002:387.623   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:390.232 - 2.625ms returns 0x27C
TA260 002:390.241 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:390.244   Data:  3A 43 1A 43 42 EA 09 02 B9 F1 08 0F 08 BF 42 F0 ...
TA260 002:390.253   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:392.193 - 1.952ms returns 0x184
TA260 002:392.200 JLINK_HasError()
TA260 002:392.205 JLINK_WriteReg(R0, 0x08001000)
TA260 002:392.209 - 0.004ms returns 0
TA260 002:392.214 JLINK_WriteReg(R1, 0x00000400)
TA260 002:392.220 - 0.006ms returns 0
TA260 002:392.225 JLINK_WriteReg(R2, 0x20000184)
TA260 002:392.229 - 0.003ms returns 0
TA260 002:392.233 JLINK_WriteReg(R3, 0x00000000)
TA260 002:392.236 - 0.003ms returns 0
TA260 002:392.240 JLINK_WriteReg(R4, 0x00000000)
TA260 002:392.244 - 0.003ms returns 0
TA260 002:392.248 JLINK_WriteReg(R5, 0x00000000)
TA260 002:392.251 - 0.003ms returns 0
TA260 002:392.255 JLINK_WriteReg(R6, 0x00000000)
TA260 002:392.258 - 0.003ms returns 0
TA260 002:392.262 JLINK_WriteReg(R7, 0x00000000)
TA260 002:392.266 - 0.003ms returns 0
TA260 002:392.270 JLINK_WriteReg(R8, 0x00000000)
TA260 002:392.273 - 0.003ms returns 0
TA260 002:392.277 JLINK_WriteReg(R9, 0x20000180)
TA260 002:392.281 - 0.003ms returns 0
TA260 002:392.285 JLINK_WriteReg(R10, 0x00000000)
TA260 002:392.288 - 0.003ms returns 0
TA260 002:392.292 JLINK_WriteReg(R11, 0x00000000)
TA260 002:392.295 - 0.003ms returns 0
TA260 002:392.299 JLINK_WriteReg(R12, 0x00000000)
TA260 002:392.303 - 0.003ms returns 0
TA260 002:392.307 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:392.310 - 0.003ms returns 0
TA260 002:392.314 JLINK_WriteReg(R14, 0x20000001)
TA260 002:392.318 - 0.003ms returns 0
TA260 002:392.322 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:392.325 - 0.003ms returns 0
TA260 002:392.329 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:392.333 - 0.003ms returns 0
TA260 002:392.337 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:392.340 - 0.003ms returns 0
TA260 002:392.344 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:392.348 - 0.003ms returns 0
TA260 002:392.352 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:392.355 - 0.003ms returns 0
TA260 002:392.360 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:392.364 - 0.004ms returns 0x00000010
TA260 002:392.368 JLINK_Go()
TA260 002:392.375   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:395.152 - 2.783ms 
TA260 002:395.158 JLINK_IsHalted()
TA260 002:395.638 - 0.479ms returns FALSE
TA260 002:395.643 JLINK_HasError()
TA260 002:396.961 JLINK_IsHalted()
TA260 002:397.450 - 0.488ms returns FALSE
TA260 002:397.499 JLINK_HasError()
TA260 002:398.964 JLINK_IsHalted()
TA260 002:399.411 - 0.447ms returns FALSE
TA260 002:399.417 JLINK_HasError()
TA260 002:400.961 JLINK_IsHalted()
TA260 002:403.264   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:403.776 - 2.815ms returns TRUE
TA260 002:403.787 JLINK_ReadReg(R15 (PC))
TA260 002:403.792 - 0.005ms returns 0x20000000
TA260 002:403.819 JLINK_ClrBPEx(BPHandle = 0x00000010)
TA260 002:403.824 - 0.004ms returns 0x00
TA260 002:403.828 JLINK_ReadReg(R0)
TA260 002:403.832 - 0.003ms returns 0x00000000
TA260 002:404.144 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:404.151   Data:  35 F9 94 F8 35 10 02 29 25 D1 05 46 20 68 21 6C ...
TA260 002:404.161   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:406.736 - 2.592ms returns 0x27C
TA260 002:406.748 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:406.752   Data:  40 F2 10 41 C2 F2 00 00 C2 F2 00 01 00 78 0A 68 ...
TA260 002:406.760   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:408.655 - 1.906ms returns 0x184
TA260 002:408.668 JLINK_HasError()
TA260 002:408.674 JLINK_WriteReg(R0, 0x08001400)
TA260 002:408.679 - 0.005ms returns 0
TA260 002:408.684 JLINK_WriteReg(R1, 0x00000400)
TA260 002:408.687 - 0.003ms returns 0
TA260 002:408.691 JLINK_WriteReg(R2, 0x20000184)
TA260 002:408.695 - 0.003ms returns 0
TA260 002:408.699 JLINK_WriteReg(R3, 0x00000000)
TA260 002:408.703 - 0.003ms returns 0
TA260 002:408.707 JLINK_WriteReg(R4, 0x00000000)
TA260 002:408.710 - 0.003ms returns 0
TA260 002:408.714 JLINK_WriteReg(R5, 0x00000000)
TA260 002:408.717 - 0.003ms returns 0
TA260 002:408.721 JLINK_WriteReg(R6, 0x00000000)
TA260 002:408.725 - 0.003ms returns 0
TA260 002:408.729 JLINK_WriteReg(R7, 0x00000000)
TA260 002:408.732 - 0.003ms returns 0
TA260 002:408.770 JLINK_WriteReg(R8, 0x00000000)
TA260 002:408.774 - 0.004ms returns 0
TA260 002:408.778 JLINK_WriteReg(R9, 0x20000180)
TA260 002:408.782 - 0.003ms returns 0
TA260 002:408.785 JLINK_WriteReg(R10, 0x00000000)
TA260 002:408.794 - 0.008ms returns 0
TA260 002:408.798 JLINK_WriteReg(R11, 0x00000000)
TA260 002:408.802 - 0.003ms returns 0
TA260 002:408.806 JLINK_WriteReg(R12, 0x00000000)
TA260 002:408.809 - 0.003ms returns 0
TA260 002:408.813 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:408.817 - 0.003ms returns 0
TA260 002:408.821 JLINK_WriteReg(R14, 0x20000001)
TA260 002:408.825 - 0.003ms returns 0
TA260 002:408.829 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:408.832 - 0.003ms returns 0
TA260 002:408.836 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:408.840 - 0.003ms returns 0
TA260 002:408.844 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:408.847 - 0.003ms returns 0
TA260 002:408.851 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:408.854 - 0.003ms returns 0
TA260 002:408.858 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:408.862 - 0.003ms returns 0
TA260 002:408.866 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:408.870 - 0.004ms returns 0x00000011
TA260 002:408.875 JLINK_Go()
TA260 002:408.883   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:411.647 - 2.771ms 
TA260 002:411.655 JLINK_IsHalted()
TA260 002:412.138 - 0.483ms returns FALSE
TA260 002:412.144 JLINK_HasError()
TA260 002:413.969 JLINK_IsHalted()
TA260 002:414.457 - 0.487ms returns FALSE
TA260 002:414.463 JLINK_HasError()
TA260 002:415.969 JLINK_IsHalted()
TA260 002:418.508   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:418.983 - 3.013ms returns TRUE
TA260 002:418.992 JLINK_ReadReg(R15 (PC))
TA260 002:418.997 - 0.005ms returns 0x20000000
TA260 002:419.002 JLINK_ClrBPEx(BPHandle = 0x00000011)
TA260 002:419.005 - 0.003ms returns 0x00
TA260 002:419.010 JLINK_ReadReg(R0)
TA260 002:419.014 - 0.003ms returns 0x00000000
TA260 002:419.404 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:419.413   Data:  01 68 40 26 41 F0 80 51 01 60 00 68 C4 F2 0E 26 ...
TA260 002:419.424   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:422.049 - 2.645ms returns 0x27C
TA260 002:422.056 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:422.060   Data:  00 01 00 23 FE F7 B0 FB 21 68 02 22 C1 F3 01 41 ...
TA260 002:422.067   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:423.924 - 1.867ms returns 0x184
TA260 002:423.932 JLINK_HasError()
TA260 002:423.938 JLINK_WriteReg(R0, 0x08001800)
TA260 002:423.943 - 0.004ms returns 0
TA260 002:423.948 JLINK_WriteReg(R1, 0x00000400)
TA260 002:423.951 - 0.003ms returns 0
TA260 002:423.955 JLINK_WriteReg(R2, 0x20000184)
TA260 002:423.958 - 0.003ms returns 0
TA260 002:423.962 JLINK_WriteReg(R3, 0x00000000)
TA260 002:423.966 - 0.003ms returns 0
TA260 002:423.970 JLINK_WriteReg(R4, 0x00000000)
TA260 002:423.973 - 0.003ms returns 0
TA260 002:423.977 JLINK_WriteReg(R5, 0x00000000)
TA260 002:423.981 - 0.003ms returns 0
TA260 002:423.985 JLINK_WriteReg(R6, 0x00000000)
TA260 002:423.988 - 0.003ms returns 0
TA260 002:423.992 JLINK_WriteReg(R7, 0x00000000)
TA260 002:423.995 - 0.003ms returns 0
TA260 002:424.054 JLINK_WriteReg(R8, 0x00000000)
TA260 002:424.058 - 0.004ms returns 0
TA260 002:424.062 JLINK_WriteReg(R9, 0x20000180)
TA260 002:424.066 - 0.003ms returns 0
TA260 002:424.070 JLINK_WriteReg(R10, 0x00000000)
TA260 002:424.073 - 0.003ms returns 0
TA260 002:424.077 JLINK_WriteReg(R11, 0x00000000)
TA260 002:424.080 - 0.003ms returns 0
TA260 002:424.084 JLINK_WriteReg(R12, 0x00000000)
TA260 002:424.088 - 0.003ms returns 0
TA260 002:424.092 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:424.096 - 0.003ms returns 0
TA260 002:424.100 JLINK_WriteReg(R14, 0x20000001)
TA260 002:424.103 - 0.003ms returns 0
TA260 002:424.108 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:424.112 - 0.004ms returns 0
TA260 002:424.116 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:424.119 - 0.003ms returns 0
TA260 002:424.123 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:424.127 - 0.003ms returns 0
TA260 002:424.131 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:424.134 - 0.003ms returns 0
TA260 002:424.138 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:424.142 - 0.003ms returns 0
TA260 002:424.146 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:424.157 - 0.010ms returns 0x00000012
TA260 002:424.161 JLINK_Go()
TA260 002:424.168   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:426.868 - 2.706ms 
TA260 002:426.879 JLINK_IsHalted()
TA260 002:427.378 - 0.498ms returns FALSE
TA260 002:427.385 JLINK_HasError()
TA260 002:428.479 JLINK_IsHalted()
TA260 002:428.988 - 0.508ms returns FALSE
TA260 002:429.000 JLINK_HasError()
TA260 002:430.479 JLINK_IsHalted()
TA260 002:430.957 - 0.477ms returns FALSE
TA260 002:430.963 JLINK_HasError()
TA260 002:432.884 JLINK_IsHalted()
TA260 002:435.217   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:435.686 - 2.801ms returns TRUE
TA260 002:435.692 JLINK_ReadReg(R15 (PC))
TA260 002:435.696 - 0.004ms returns 0x20000000
TA260 002:435.701 JLINK_ClrBPEx(BPHandle = 0x00000012)
TA260 002:435.705 - 0.003ms returns 0x00
TA260 002:435.709 JLINK_ReadReg(R0)
TA260 002:435.712 - 0.003ms returns 0x00000000
TA260 002:436.048 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:436.055   Data:  E1 6A 00 F0 70 60 B0 EB 01 6F 5E D1 00 20 02 B0 ...
TA260 002:436.068   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:438.735 - 2.687ms returns 0x27C
TA260 002:438.750 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:438.754   Data:  06 96 CD E9 04 66 03 96 02 96 48 D1 01 21 01 70 ...
TA260 002:438.765   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:440.648 - 1.898ms returns 0x184
TA260 002:440.657 JLINK_HasError()
TA260 002:440.662 JLINK_WriteReg(R0, 0x08001C00)
TA260 002:440.667 - 0.005ms returns 0
TA260 002:440.672 JLINK_WriteReg(R1, 0x00000400)
TA260 002:440.675 - 0.003ms returns 0
TA260 002:440.679 JLINK_WriteReg(R2, 0x20000184)
TA260 002:440.682 - 0.003ms returns 0
TA260 002:440.687 JLINK_WriteReg(R3, 0x00000000)
TA260 002:440.690 - 0.003ms returns 0
TA260 002:440.694 JLINK_WriteReg(R4, 0x00000000)
TA260 002:440.697 - 0.003ms returns 0
TA260 002:440.701 JLINK_WriteReg(R5, 0x00000000)
TA260 002:440.705 - 0.003ms returns 0
TA260 002:440.709 JLINK_WriteReg(R6, 0x00000000)
TA260 002:440.712 - 0.003ms returns 0
TA260 002:440.716 JLINK_WriteReg(R7, 0x00000000)
TA260 002:440.720 - 0.003ms returns 0
TA260 002:440.724 JLINK_WriteReg(R8, 0x00000000)
TA260 002:440.727 - 0.003ms returns 0
TA260 002:440.732 JLINK_WriteReg(R9, 0x20000180)
TA260 002:440.735 - 0.003ms returns 0
TA260 002:440.740 JLINK_WriteReg(R10, 0x00000000)
TA260 002:440.743 - 0.003ms returns 0
TA260 002:440.747 JLINK_WriteReg(R11, 0x00000000)
TA260 002:440.750 - 0.003ms returns 0
TA260 002:440.754 JLINK_WriteReg(R12, 0x00000000)
TA260 002:440.758 - 0.003ms returns 0
TA260 002:440.762 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:440.766 - 0.003ms returns 0
TA260 002:440.770 JLINK_WriteReg(R14, 0x20000001)
TA260 002:440.773 - 0.003ms returns 0
TA260 002:440.777 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:440.781 - 0.003ms returns 0
TA260 002:440.785 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:440.788 - 0.003ms returns 0
TA260 002:440.792 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:440.795 - 0.003ms returns 0
TA260 002:440.799 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:440.803 - 0.003ms returns 0
TA260 002:440.807 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:440.810 - 0.003ms returns 0
TA260 002:440.815 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:440.819 - 0.004ms returns 0x00000013
TA260 002:440.823 JLINK_Go()
TA260 002:440.831   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:443.560 - 2.736ms 
TA260 002:443.567 JLINK_IsHalted()
TA260 002:444.024 - 0.457ms returns FALSE
TA260 002:444.030 JLINK_HasError()
TA260 002:447.393 JLINK_IsHalted()
TA260 002:447.834 - 0.440ms returns FALSE
TA260 002:447.843 JLINK_HasError()
TA260 002:449.201 JLINK_IsHalted()
TA260 002:451.537   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:452.025 - 2.824ms returns TRUE
TA260 002:452.032 JLINK_ReadReg(R15 (PC))
TA260 002:452.037 - 0.004ms returns 0x20000000
TA260 002:452.041 JLINK_ClrBPEx(BPHandle = 0x00000013)
TA260 002:452.045 - 0.003ms returns 0x00
TA260 002:452.054 JLINK_ReadReg(R0)
TA260 002:452.058 - 0.005ms returns 0x00000000
TA260 002:452.380 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:452.387   Data:  C8 60 C8 68 40 F0 40 00 C8 60 02 B0 B0 BD D1 07 ...
TA260 002:452.397   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:455.015 - 2.635ms returns 0x27C
TA260 002:455.026 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:455.030   Data:  00 F0 C6 FD 20 68 01 69 21 F4 90 41 01 61 41 69 ...
TA260 002:455.039   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:456.923 - 1.896ms returns 0x184
TA260 002:456.936 JLINK_HasError()
TA260 002:456.970 JLINK_WriteReg(R0, 0x08002000)
TA260 002:456.977 - 0.006ms returns 0
TA260 002:456.982 JLINK_WriteReg(R1, 0x00000400)
TA260 002:456.986 - 0.003ms returns 0
TA260 002:456.990 JLINK_WriteReg(R2, 0x20000184)
TA260 002:456.993 - 0.003ms returns 0
TA260 002:456.997 JLINK_WriteReg(R3, 0x00000000)
TA260 002:457.001 - 0.003ms returns 0
TA260 002:457.004 JLINK_WriteReg(R4, 0x00000000)
TA260 002:457.008 - 0.003ms returns 0
TA260 002:457.012 JLINK_WriteReg(R5, 0x00000000)
TA260 002:457.015 - 0.003ms returns 0
TA260 002:457.019 JLINK_WriteReg(R6, 0x00000000)
TA260 002:457.022 - 0.003ms returns 0
TA260 002:457.026 JLINK_WriteReg(R7, 0x00000000)
TA260 002:457.030 - 0.003ms returns 0
TA260 002:457.034 JLINK_WriteReg(R8, 0x00000000)
TA260 002:457.037 - 0.003ms returns 0
TA260 002:457.041 JLINK_WriteReg(R9, 0x20000180)
TA260 002:457.045 - 0.003ms returns 0
TA260 002:457.049 JLINK_WriteReg(R10, 0x00000000)
TA260 002:457.052 - 0.003ms returns 0
TA260 002:457.056 JLINK_WriteReg(R11, 0x00000000)
TA260 002:457.060 - 0.003ms returns 0
TA260 002:457.064 JLINK_WriteReg(R12, 0x00000000)
TA260 002:457.067 - 0.003ms returns 0
TA260 002:457.071 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:457.075 - 0.004ms returns 0
TA260 002:457.079 JLINK_WriteReg(R14, 0x20000001)
TA260 002:457.082 - 0.003ms returns 0
TA260 002:457.086 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:457.090 - 0.003ms returns 0
TA260 002:457.094 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:457.097 - 0.003ms returns 0
TA260 002:457.101 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:457.105 - 0.003ms returns 0
TA260 002:457.109 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:457.112 - 0.003ms returns 0
TA260 002:457.116 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:457.119 - 0.003ms returns 0
TA260 002:457.124 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:457.129 - 0.004ms returns 0x00000014
TA260 002:457.133 JLINK_Go()
TA260 002:457.140   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:459.879 - 2.745ms 
TA260 002:459.892 JLINK_IsHalted()
TA260 002:460.389 - 0.497ms returns FALSE
TA260 002:460.396 JLINK_HasError()
TA260 002:463.713 JLINK_IsHalted()
TA260 002:464.211 - 0.497ms returns FALSE
TA260 002:464.217 JLINK_HasError()
TA260 002:465.714 JLINK_IsHalted()
TA260 002:468.057   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:468.560 - 2.845ms returns TRUE
TA260 002:468.567 JLINK_ReadReg(R15 (PC))
TA260 002:468.572 - 0.005ms returns 0x20000000
TA260 002:468.576 JLINK_ClrBPEx(BPHandle = 0x00000014)
TA260 002:468.580 - 0.003ms returns 0x00
TA260 002:468.584 JLINK_ReadReg(R0)
TA260 002:468.588 - 0.003ms returns 0x00000000
TA260 002:468.870 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:468.877   Data:  80 72 CA 60 4A 69 42 F0 01 02 4A 61 CA 68 42 F0 ...
TA260 002:468.886   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:471.470 - 2.600ms returns 0x27C
TA260 002:471.479 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:471.483   Data:  19 F1 01 05 0E D0 14 E0 00 27 A9 E7 20 20 84 F8 ...
TA260 002:471.491   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:473.276 - 1.796ms returns 0x184
TA260 002:473.285 JLINK_HasError()
TA260 002:473.292 JLINK_WriteReg(R0, 0x08002400)
TA260 002:473.297 - 0.005ms returns 0
TA260 002:473.303 JLINK_WriteReg(R1, 0x00000400)
TA260 002:473.307 - 0.004ms returns 0
TA260 002:473.312 JLINK_WriteReg(R2, 0x20000184)
TA260 002:473.317 - 0.004ms returns 0
TA260 002:473.322 JLINK_WriteReg(R3, 0x00000000)
TA260 002:473.331 - 0.009ms returns 0
TA260 002:473.343 JLINK_WriteReg(R4, 0x00000000)
TA260 002:473.347 - 0.004ms returns 0
TA260 002:473.352 JLINK_WriteReg(R5, 0x00000000)
TA260 002:473.357 - 0.004ms returns 0
TA260 002:473.362 JLINK_WriteReg(R6, 0x00000000)
TA260 002:473.366 - 0.004ms returns 0
TA260 002:473.371 JLINK_WriteReg(R7, 0x00000000)
TA260 002:473.376 - 0.004ms returns 0
TA260 002:473.381 JLINK_WriteReg(R8, 0x00000000)
TA260 002:473.385 - 0.004ms returns 0
TA260 002:473.390 JLINK_WriteReg(R9, 0x20000180)
TA260 002:473.394 - 0.004ms returns 0
TA260 002:473.399 JLINK_WriteReg(R10, 0x00000000)
TA260 002:473.404 - 0.004ms returns 0
TA260 002:473.408 JLINK_WriteReg(R11, 0x00000000)
TA260 002:473.413 - 0.004ms returns 0
TA260 002:473.418 JLINK_WriteReg(R12, 0x00000000)
TA260 002:473.422 - 0.004ms returns 0
TA260 002:473.428 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:473.432 - 0.004ms returns 0
TA260 002:473.437 JLINK_WriteReg(R14, 0x20000001)
TA260 002:473.442 - 0.004ms returns 0
TA260 002:473.447 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:473.451 - 0.004ms returns 0
TA260 002:473.457 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:473.460 - 0.003ms returns 0
TA260 002:473.464 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:473.468 - 0.003ms returns 0
TA260 002:473.472 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:473.475 - 0.003ms returns 0
TA260 002:473.479 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:473.483 - 0.003ms returns 0
TA260 002:473.488 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:473.492 - 0.004ms returns 0x00000015
TA260 002:473.496 JLINK_Go()
TA260 002:473.503   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:476.204 - 2.708ms 
TA260 002:476.210 JLINK_IsHalted()
TA260 002:476.687 - 0.476ms returns FALSE
TA260 002:476.700 JLINK_HasError()
TA260 002:478.228 JLINK_IsHalted()
TA260 002:478.826 - 0.598ms returns FALSE
TA260 002:478.837 JLINK_HasError()
TA260 002:480.219 JLINK_IsHalted()
TA260 002:482.658   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:483.118 - 2.899ms returns TRUE
TA260 002:483.130 JLINK_ReadReg(R15 (PC))
TA260 002:483.136 - 0.005ms returns 0x20000000
TA260 002:483.165 JLINK_ClrBPEx(BPHandle = 0x00000015)
TA260 002:483.170 - 0.004ms returns 0x00
TA260 002:483.175 JLINK_ReadReg(R0)
TA260 002:483.179 - 0.003ms returns 0x00000000
TA260 002:483.490 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:483.497   Data:  69 46 10 22 FE F7 82 FD 00 28 18 BF FE F7 E8 FC ...
TA260 002:483.506   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:486.093 - 2.603ms returns 0x27C
TA260 002:486.100 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:486.104   Data:  C3 61 FF F7 E5 FB 00 28 08 BF 80 BD BD E8 80 40 ...
TA260 002:486.111   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:488.009 - 1.908ms returns 0x184
TA260 002:488.023 JLINK_HasError()
TA260 002:488.028 JLINK_WriteReg(R0, 0x08002800)
TA260 002:488.033 - 0.005ms returns 0
TA260 002:488.038 JLINK_WriteReg(R1, 0x00000400)
TA260 002:488.041 - 0.003ms returns 0
TA260 002:488.045 JLINK_WriteReg(R2, 0x20000184)
TA260 002:488.049 - 0.003ms returns 0
TA260 002:488.053 JLINK_WriteReg(R3, 0x00000000)
TA260 002:488.056 - 0.003ms returns 0
TA260 002:488.060 JLINK_WriteReg(R4, 0x00000000)
TA260 002:488.063 - 0.003ms returns 0
TA260 002:488.068 JLINK_WriteReg(R5, 0x00000000)
TA260 002:488.071 - 0.003ms returns 0
TA260 002:488.075 JLINK_WriteReg(R6, 0x00000000)
TA260 002:488.078 - 0.003ms returns 0
TA260 002:488.082 JLINK_WriteReg(R7, 0x00000000)
TA260 002:488.086 - 0.003ms returns 0
TA260 002:488.090 JLINK_WriteReg(R8, 0x00000000)
TA260 002:488.093 - 0.003ms returns 0
TA260 002:488.097 JLINK_WriteReg(R9, 0x20000180)
TA260 002:488.100 - 0.003ms returns 0
TA260 002:488.104 JLINK_WriteReg(R10, 0x00000000)
TA260 002:488.108 - 0.003ms returns 0
TA260 002:488.112 JLINK_WriteReg(R11, 0x00000000)
TA260 002:488.115 - 0.003ms returns 0
TA260 002:488.119 JLINK_WriteReg(R12, 0x00000000)
TA260 002:488.122 - 0.003ms returns 0
TA260 002:488.126 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:488.133 - 0.006ms returns 0
TA260 002:488.138 JLINK_WriteReg(R14, 0x20000001)
TA260 002:488.142 - 0.003ms returns 0
TA260 002:488.146 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:488.149 - 0.003ms returns 0
TA260 002:488.153 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:488.166 - 0.013ms returns 0
TA260 002:488.170 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:488.174 - 0.003ms returns 0
TA260 002:488.178 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:488.181 - 0.003ms returns 0
TA260 002:488.185 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:488.188 - 0.003ms returns 0
TA260 002:488.193 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:488.197 - 0.004ms returns 0x00000016
TA260 002:488.201 JLINK_Go()
TA260 002:488.209   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:490.981 - 2.779ms 
TA260 002:490.990 JLINK_IsHalted()
TA260 002:491.468 - 0.477ms returns FALSE
TA260 002:491.473 JLINK_HasError()
TA260 002:492.728 JLINK_IsHalted()
TA260 002:493.219 - 0.491ms returns FALSE
TA260 002:493.226 JLINK_HasError()
TA260 002:494.731 JLINK_IsHalted()
TA260 002:495.231 - 0.499ms returns FALSE
TA260 002:495.237 JLINK_HasError()
TA260 002:499.732 JLINK_IsHalted()
TA260 002:502.050   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:502.547 - 2.814ms returns TRUE
TA260 002:502.554 JLINK_ReadReg(R15 (PC))
TA260 002:502.559 - 0.004ms returns 0x20000000
TA260 002:502.563 JLINK_ClrBPEx(BPHandle = 0x00000016)
TA260 002:502.567 - 0.003ms returns 0x00
TA260 002:502.571 JLINK_ReadReg(R0)
TA260 002:502.575 - 0.003ms returns 0x00000000
TA260 002:502.858 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:502.865   Data:  81 06 04 5D 45 5D F6 68 45 EA 04 24 34 80 02 24 ...
TA260 002:502.875   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:505.457 - 2.598ms returns 0x27C
TA260 002:505.464 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:505.467   Data:  B3 F9 48 F2 1F 51 C5 F2 EB 11 A0 FB 01 23 5A 09 ...
TA260 002:505.484   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:507.336 - 1.872ms returns 0x184
TA260 002:507.348 JLINK_HasError()
TA260 002:507.353 JLINK_WriteReg(R0, 0x08002C00)
TA260 002:507.358 - 0.005ms returns 0
TA260 002:507.362 JLINK_WriteReg(R1, 0x00000400)
TA260 002:507.366 - 0.003ms returns 0
TA260 002:507.370 JLINK_WriteReg(R2, 0x20000184)
TA260 002:507.374 - 0.004ms returns 0
TA260 002:507.378 JLINK_WriteReg(R3, 0x00000000)
TA260 002:507.381 - 0.003ms returns 0
TA260 002:507.386 JLINK_WriteReg(R4, 0x00000000)
TA260 002:507.389 - 0.003ms returns 0
TA260 002:507.393 JLINK_WriteReg(R5, 0x00000000)
TA260 002:507.396 - 0.003ms returns 0
TA260 002:507.400 JLINK_WriteReg(R6, 0x00000000)
TA260 002:507.404 - 0.003ms returns 0
TA260 002:507.408 JLINK_WriteReg(R7, 0x00000000)
TA260 002:507.411 - 0.003ms returns 0
TA260 002:507.415 JLINK_WriteReg(R8, 0x00000000)
TA260 002:507.419 - 0.003ms returns 0
TA260 002:507.423 JLINK_WriteReg(R9, 0x20000180)
TA260 002:507.426 - 0.003ms returns 0
TA260 002:507.430 JLINK_WriteReg(R10, 0x00000000)
TA260 002:507.433 - 0.003ms returns 0
TA260 002:507.437 JLINK_WriteReg(R11, 0x00000000)
TA260 002:507.441 - 0.003ms returns 0
TA260 002:507.445 JLINK_WriteReg(R12, 0x00000000)
TA260 002:507.448 - 0.003ms returns 0
TA260 002:507.452 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:507.456 - 0.003ms returns 0
TA260 002:507.460 JLINK_WriteReg(R14, 0x20000001)
TA260 002:507.463 - 0.003ms returns 0
TA260 002:507.468 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:507.471 - 0.003ms returns 0
TA260 002:507.475 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:507.478 - 0.003ms returns 0
TA260 002:507.482 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:507.486 - 0.003ms returns 0
TA260 002:507.490 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:507.493 - 0.003ms returns 0
TA260 002:507.497 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:507.500 - 0.003ms returns 0
TA260 002:507.505 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:507.509 - 0.004ms returns 0x00000017
TA260 002:507.513 JLINK_Go()
TA260 002:507.521   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:511.161 - 3.647ms 
TA260 002:511.180 JLINK_IsHalted()
TA260 002:511.670 - 0.490ms returns FALSE
TA260 002:511.678 JLINK_HasError()
TA260 002:513.677 JLINK_IsHalted()
TA260 002:514.189 - 0.511ms returns FALSE
TA260 002:514.195 JLINK_HasError()
TA260 002:515.679 JLINK_IsHalted()
TA260 002:517.993   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:518.466 - 2.786ms returns TRUE
TA260 002:518.473 JLINK_ReadReg(R15 (PC))
TA260 002:518.478 - 0.005ms returns 0x20000000
TA260 002:518.482 JLINK_ClrBPEx(BPHandle = 0x00000017)
TA260 002:518.486 - 0.003ms returns 0x00
TA260 002:518.490 JLINK_ReadReg(R0)
TA260 002:518.494 - 0.003ms returns 0x00000000
TA260 002:518.814 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:518.822   Data:  41 0A C0 F6 00 01 01 EE 10 0A 01 EB 80 01 B8 EE ...
TA260 002:518.831   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:521.471 - 2.657ms returns 0x27C
TA260 002:521.483 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:521.487   Data:  04 2B 50 F8 04 2B 41 F8 04 2B 50 F8 04 2B 41 F8 ...
TA260 002:521.496   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:523.369 - 1.885ms returns 0x184
TA260 002:523.377 JLINK_HasError()
TA260 002:523.382 JLINK_WriteReg(R0, 0x08003000)
TA260 002:523.387 - 0.004ms returns 0
TA260 002:523.391 JLINK_WriteReg(R1, 0x00000400)
TA260 002:523.395 - 0.003ms returns 0
TA260 002:523.399 JLINK_WriteReg(R2, 0x20000184)
TA260 002:523.402 - 0.003ms returns 0
TA260 002:523.407 JLINK_WriteReg(R3, 0x00000000)
TA260 002:523.410 - 0.003ms returns 0
TA260 002:523.414 JLINK_WriteReg(R4, 0x00000000)
TA260 002:523.418 - 0.003ms returns 0
TA260 002:523.422 JLINK_WriteReg(R5, 0x00000000)
TA260 002:523.426 - 0.003ms returns 0
TA260 002:523.430 JLINK_WriteReg(R6, 0x00000000)
TA260 002:523.433 - 0.003ms returns 0
TA260 002:523.437 JLINK_WriteReg(R7, 0x00000000)
TA260 002:523.440 - 0.003ms returns 0
TA260 002:523.444 JLINK_WriteReg(R8, 0x00000000)
TA260 002:523.448 - 0.003ms returns 0
TA260 002:523.452 JLINK_WriteReg(R9, 0x20000180)
TA260 002:523.455 - 0.003ms returns 0
TA260 002:523.459 JLINK_WriteReg(R10, 0x00000000)
TA260 002:523.462 - 0.003ms returns 0
TA260 002:523.466 JLINK_WriteReg(R11, 0x00000000)
TA260 002:523.470 - 0.003ms returns 0
TA260 002:523.474 JLINK_WriteReg(R12, 0x00000000)
TA260 002:523.477 - 0.003ms returns 0
TA260 002:523.481 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:523.485 - 0.003ms returns 0
TA260 002:523.489 JLINK_WriteReg(R14, 0x20000001)
TA260 002:523.493 - 0.003ms returns 0
TA260 002:523.497 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:523.500 - 0.003ms returns 0
TA260 002:523.504 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:523.508 - 0.003ms returns 0
TA260 002:523.512 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:523.515 - 0.003ms returns 0
TA260 002:523.519 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:523.523 - 0.003ms returns 0
TA260 002:523.527 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:523.530 - 0.003ms returns 0
TA260 002:523.535 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:523.539 - 0.004ms returns 0x00000018
TA260 002:523.543 JLINK_Go()
TA260 002:523.550   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:526.482 - 2.937ms 
TA260 002:526.501 JLINK_IsHalted()
TA260 002:528.744 - 2.241ms returns FALSE
TA260 002:528.764 JLINK_HasError()
TA260 002:531.676 JLINK_IsHalted()
TA260 002:534.008   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:534.496 - 2.820ms returns TRUE
TA260 002:534.504 JLINK_ReadReg(R15 (PC))
TA260 002:534.509 - 0.005ms returns 0x20000000
TA260 002:534.514 JLINK_ClrBPEx(BPHandle = 0x00000018)
TA260 002:534.518 - 0.003ms returns 0x00
TA260 002:534.522 JLINK_ReadReg(R0)
TA260 002:534.526 - 0.004ms returns 0x00000000
TA260 002:534.876 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:534.884   Data:  04 2B 50 F8 04 2B 41 F8 04 2B 0F CB FF F7 74 F9 ...
TA260 002:534.896   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:537.519 - 2.643ms returns 0x27C
TA260 002:537.537 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:537.543   Data:  01 1A 80 EE 01 0A 9F ED 5A 1A 20 EE 01 0A 10 EE ...
TA260 002:537.554   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:539.565 - 2.027ms returns 0x184
TA260 002:539.584 JLINK_HasError()
TA260 002:539.624 JLINK_WriteReg(R0, 0x08003400)
TA260 002:539.630 - 0.006ms returns 0
TA260 002:539.635 JLINK_WriteReg(R1, 0x00000400)
TA260 002:539.638 - 0.003ms returns 0
TA260 002:539.643 JLINK_WriteReg(R2, 0x20000184)
TA260 002:539.646 - 0.003ms returns 0
TA260 002:539.650 JLINK_WriteReg(R3, 0x00000000)
TA260 002:539.654 - 0.003ms returns 0
TA260 002:539.658 JLINK_WriteReg(R4, 0x00000000)
TA260 002:539.662 - 0.003ms returns 0
TA260 002:539.666 JLINK_WriteReg(R5, 0x00000000)
TA260 002:539.669 - 0.003ms returns 0
TA260 002:539.673 JLINK_WriteReg(R6, 0x00000000)
TA260 002:539.677 - 0.003ms returns 0
TA260 002:539.681 JLINK_WriteReg(R7, 0x00000000)
TA260 002:539.685 - 0.003ms returns 0
TA260 002:539.690 JLINK_WriteReg(R8, 0x00000000)
TA260 002:539.693 - 0.003ms returns 0
TA260 002:539.698 JLINK_WriteReg(R9, 0x20000180)
TA260 002:539.702 - 0.003ms returns 0
TA260 002:539.706 JLINK_WriteReg(R10, 0x00000000)
TA260 002:539.710 - 0.004ms returns 0
TA260 002:539.714 JLINK_WriteReg(R11, 0x00000000)
TA260 002:539.718 - 0.003ms returns 0
TA260 002:539.722 JLINK_WriteReg(R12, 0x00000000)
TA260 002:539.725 - 0.003ms returns 0
TA260 002:539.729 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:539.733 - 0.003ms returns 0
TA260 002:539.737 JLINK_WriteReg(R14, 0x20000001)
TA260 002:539.740 - 0.003ms returns 0
TA260 002:539.745 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:539.748 - 0.003ms returns 0
TA260 002:539.752 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:539.756 - 0.003ms returns 0
TA260 002:539.760 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:539.764 - 0.003ms returns 0
TA260 002:539.768 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:539.771 - 0.003ms returns 0
TA260 002:539.775 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:539.779 - 0.003ms returns 0
TA260 002:539.783 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:539.788 - 0.004ms returns 0x00000019
TA260 002:539.792 JLINK_Go()
TA260 002:539.801   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:542.649 - 2.856ms 
TA260 002:542.664 JLINK_IsHalted()
TA260 002:543.139 - 0.474ms returns FALSE
TA260 002:543.146 JLINK_HasError()
TA260 002:544.188 JLINK_IsHalted()
TA260 002:544.671 - 0.483ms returns FALSE
TA260 002:544.678 JLINK_HasError()
TA260 002:546.185 JLINK_IsHalted()
TA260 002:546.691 - 0.505ms returns FALSE
TA260 002:546.704 JLINK_HasError()
TA260 002:548.185 JLINK_IsHalted()
TA260 002:550.535   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:551.014 - 2.829ms returns TRUE
TA260 002:551.020 JLINK_ReadReg(R15 (PC))
TA260 002:551.025 - 0.004ms returns 0x20000000
TA260 002:551.030 JLINK_ClrBPEx(BPHandle = 0x00000019)
TA260 002:551.034 - 0.003ms returns 0x00
TA260 002:551.038 JLINK_ReadReg(R0)
TA260 002:551.042 - 0.003ms returns 0x00000000
TA260 002:551.397 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:551.404   Data:  00 60 40 21 FD F7 26 FF B0 FA 80 F0 45 09 20 46 ...
TA260 002:551.413   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:554.015 - 2.617ms returns 0x27C
TA260 002:554.022 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:554.026   Data:  30 46 01 F0 4B F8 00 2C 02 D0 00 9A 00 21 11 70 ...
TA260 002:554.033   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:555.875 - 1.852ms returns 0x184
TA260 002:555.888 JLINK_HasError()
TA260 002:555.893 JLINK_WriteReg(R0, 0x08003800)
TA260 002:555.898 - 0.005ms returns 0
TA260 002:555.903 JLINK_WriteReg(R1, 0x00000400)
TA260 002:555.906 - 0.003ms returns 0
TA260 002:555.910 JLINK_WriteReg(R2, 0x20000184)
TA260 002:555.914 - 0.003ms returns 0
TA260 002:555.918 JLINK_WriteReg(R3, 0x00000000)
TA260 002:555.921 - 0.003ms returns 0
TA260 002:555.925 JLINK_WriteReg(R4, 0x00000000)
TA260 002:555.929 - 0.003ms returns 0
TA260 002:555.933 JLINK_WriteReg(R5, 0x00000000)
TA260 002:555.937 - 0.003ms returns 0
TA260 002:555.941 JLINK_WriteReg(R6, 0x00000000)
TA260 002:555.947 - 0.006ms returns 0
TA260 002:555.952 JLINK_WriteReg(R7, 0x00000000)
TA260 002:555.955 - 0.003ms returns 0
TA260 002:555.959 JLINK_WriteReg(R8, 0x00000000)
TA260 002:555.966 - 0.006ms returns 0
TA260 002:555.970 JLINK_WriteReg(R9, 0x20000180)
TA260 002:555.974 - 0.003ms returns 0
TA260 002:555.978 JLINK_WriteReg(R10, 0x00000000)
TA260 002:555.982 - 0.003ms returns 0
TA260 002:555.986 JLINK_WriteReg(R11, 0x00000000)
TA260 002:555.989 - 0.003ms returns 0
TA260 002:555.993 JLINK_WriteReg(R12, 0x00000000)
TA260 002:555.996 - 0.003ms returns 0
TA260 002:556.000 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:556.004 - 0.003ms returns 0
TA260 002:556.008 JLINK_WriteReg(R14, 0x20000001)
TA260 002:556.011 - 0.003ms returns 0
TA260 002:556.016 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:556.019 - 0.003ms returns 0
TA260 002:556.023 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:556.026 - 0.003ms returns 0
TA260 002:556.030 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:556.034 - 0.003ms returns 0
TA260 002:556.038 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:556.041 - 0.003ms returns 0
TA260 002:556.045 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:556.049 - 0.003ms returns 0
TA260 002:556.053 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:556.057 - 0.004ms returns 0x0000001A
TA260 002:556.062 JLINK_Go()
TA260 002:556.069   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:558.946 - 2.883ms 
TA260 002:558.962 JLINK_IsHalted()
TA260 002:559.435 - 0.473ms returns FALSE
TA260 002:559.440 JLINK_HasError()
TA260 002:561.692 JLINK_IsHalted()
TA260 002:562.210 - 0.518ms returns FALSE
TA260 002:562.224 JLINK_HasError()
TA260 002:564.196 JLINK_IsHalted()
TA260 002:566.493   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:567.036 - 2.840ms returns TRUE
TA260 002:567.049 JLINK_ReadReg(R15 (PC))
TA260 002:567.055 - 0.006ms returns 0x20000000
TA260 002:567.089 JLINK_ClrBPEx(BPHandle = 0x0000001A)
TA260 002:567.096 - 0.006ms returns 0x00
TA260 002:567.101 JLINK_ReadReg(R0)
TA260 002:567.106 - 0.005ms returns 0x00000000
TA260 002:567.418 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:567.425   Data:  14 B0 BD EC 0E 8B 04 B0 BD E8 F0 8D 18 E0 9F ED ...
TA260 002:567.435   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:569.971 - 2.552ms returns 0x27C
TA260 002:569.983 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:569.987   Data:  00 F0 64 FD 10 EE 90 1A 18 EE 90 0A 8D ED 02 0B ...
TA260 002:569.996   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:571.873 - 1.889ms returns 0x184
TA260 002:571.885 JLINK_HasError()
TA260 002:571.890 JLINK_WriteReg(R0, 0x08003C00)
TA260 002:571.895 - 0.005ms returns 0
TA260 002:571.899 JLINK_WriteReg(R1, 0x00000400)
TA260 002:571.903 - 0.003ms returns 0
TA260 002:571.907 JLINK_WriteReg(R2, 0x20000184)
TA260 002:571.910 - 0.003ms returns 0
TA260 002:571.914 JLINK_WriteReg(R3, 0x00000000)
TA260 002:571.918 - 0.003ms returns 0
TA260 002:571.922 JLINK_WriteReg(R4, 0x00000000)
TA260 002:571.925 - 0.003ms returns 0
TA260 002:571.929 JLINK_WriteReg(R5, 0x00000000)
TA260 002:571.932 - 0.003ms returns 0
TA260 002:571.937 JLINK_WriteReg(R6, 0x00000000)
TA260 002:571.941 - 0.004ms returns 0
TA260 002:571.944 JLINK_WriteReg(R7, 0x00000000)
TA260 002:571.948 - 0.003ms returns 0
TA260 002:571.952 JLINK_WriteReg(R8, 0x00000000)
TA260 002:571.955 - 0.003ms returns 0
TA260 002:571.959 JLINK_WriteReg(R9, 0x20000180)
TA260 002:571.963 - 0.003ms returns 0
TA260 002:571.967 JLINK_WriteReg(R10, 0x00000000)
TA260 002:571.970 - 0.003ms returns 0
TA260 002:571.974 JLINK_WriteReg(R11, 0x00000000)
TA260 002:571.978 - 0.003ms returns 0
TA260 002:571.982 JLINK_WriteReg(R12, 0x00000000)
TA260 002:571.985 - 0.003ms returns 0
TA260 002:571.989 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:571.993 - 0.003ms returns 0
TA260 002:571.997 JLINK_WriteReg(R14, 0x20000001)
TA260 002:572.000 - 0.003ms returns 0
TA260 002:572.004 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:572.008 - 0.003ms returns 0
TA260 002:572.012 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:572.020 - 0.008ms returns 0
TA260 002:572.024 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:572.027 - 0.003ms returns 0
TA260 002:572.031 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:572.035 - 0.003ms returns 0
TA260 002:572.039 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:572.042 - 0.003ms returns 0
TA260 002:572.047 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:572.051 - 0.004ms returns 0x0000001B
TA260 002:572.055 JLINK_Go()
TA260 002:572.063   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:574.878 - 2.822ms 
TA260 002:574.890 JLINK_IsHalted()
TA260 002:575.389 - 0.498ms returns FALSE
TA260 002:575.395 JLINK_HasError()
TA260 002:577.703 JLINK_IsHalted()
TA260 002:578.184 - 0.480ms returns FALSE
TA260 002:578.191 JLINK_HasError()
TA260 002:579.701 JLINK_IsHalted()
TA260 002:582.002   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:582.481 - 2.779ms returns TRUE
TA260 002:582.487 JLINK_ReadReg(R15 (PC))
TA260 002:582.492 - 0.004ms returns 0x20000000
TA260 002:582.496 JLINK_ClrBPEx(BPHandle = 0x0000001B)
TA260 002:582.500 - 0.003ms returns 0x00
TA260 002:582.508 JLINK_ReadReg(R0)
TA260 002:582.512 - 0.003ms returns 0x00000000
TA260 002:582.815 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:582.836   Data:  A9 B9 00 20 B6 F5 80 1F 0E DA 9F ED E5 0B 53 EC ...
TA260 002:582.847   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:585.423 - 2.607ms returns 0x27C
TA260 002:585.429 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:585.432   Data:  9F ED 52 0B 51 EC 10 0B FC F7 AA F8 41 EC 1B 0B ...
TA260 002:585.439   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:587.324 - 1.894ms returns 0x184
TA260 002:587.335 JLINK_HasError()
TA260 002:587.340 JLINK_WriteReg(R0, 0x08004000)
TA260 002:587.345 - 0.004ms returns 0
TA260 002:587.349 JLINK_WriteReg(R1, 0x00000400)
TA260 002:587.352 - 0.003ms returns 0
TA260 002:587.356 JLINK_WriteReg(R2, 0x20000184)
TA260 002:587.360 - 0.003ms returns 0
TA260 002:587.364 JLINK_WriteReg(R3, 0x00000000)
TA260 002:587.368 - 0.003ms returns 0
TA260 002:587.372 JLINK_WriteReg(R4, 0x00000000)
TA260 002:587.375 - 0.003ms returns 0
TA260 002:587.379 JLINK_WriteReg(R5, 0x00000000)
TA260 002:587.382 - 0.003ms returns 0
TA260 002:587.386 JLINK_WriteReg(R6, 0x00000000)
TA260 002:587.390 - 0.003ms returns 0
TA260 002:587.394 JLINK_WriteReg(R7, 0x00000000)
TA260 002:587.397 - 0.003ms returns 0
TA260 002:587.402 JLINK_WriteReg(R8, 0x00000000)
TA260 002:587.405 - 0.003ms returns 0
TA260 002:587.409 JLINK_WriteReg(R9, 0x20000180)
TA260 002:587.412 - 0.003ms returns 0
TA260 002:587.416 JLINK_WriteReg(R10, 0x00000000)
TA260 002:587.420 - 0.003ms returns 0
TA260 002:587.424 JLINK_WriteReg(R11, 0x00000000)
TA260 002:587.427 - 0.003ms returns 0
TA260 002:587.431 JLINK_WriteReg(R12, 0x00000000)
TA260 002:587.434 - 0.003ms returns 0
TA260 002:587.438 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:587.442 - 0.003ms returns 0
TA260 002:587.446 JLINK_WriteReg(R14, 0x20000001)
TA260 002:587.449 - 0.003ms returns 0
TA260 002:587.453 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:587.457 - 0.003ms returns 0
TA260 002:587.461 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:587.464 - 0.003ms returns 0
TA260 002:587.468 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:587.472 - 0.003ms returns 0
TA260 002:587.476 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:587.479 - 0.003ms returns 0
TA260 002:587.483 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:587.486 - 0.003ms returns 0
TA260 002:587.491 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:587.495 - 0.004ms returns 0x0000001C
TA260 002:587.499 JLINK_Go()
TA260 002:587.507   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:590.398 - 2.898ms 
TA260 002:590.413 JLINK_IsHalted()
TA260 002:590.911 - 0.497ms returns FALSE
TA260 002:590.916 JLINK_HasError()
TA260 002:593.214 JLINK_IsHalted()
TA260 002:593.716 - 0.502ms returns FALSE
TA260 002:593.723 JLINK_HasError()
TA260 002:595.212 JLINK_IsHalted()
TA260 002:597.585   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:598.075 - 2.862ms returns TRUE
TA260 002:598.088 JLINK_ReadReg(R15 (PC))
TA260 002:598.093 - 0.005ms returns 0x20000000
TA260 002:598.097 JLINK_ClrBPEx(BPHandle = 0x0000001C)
TA260 002:598.101 - 0.004ms returns 0x00
TA260 002:598.106 JLINK_ReadReg(R0)
TA260 002:598.109 - 0.003ms returns 0x00000000
TA260 002:598.408 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:598.415   Data:  41 EC 19 0B 53 EC 10 2B 9D ED 00 0B 51 EC 10 0B ...
TA260 002:598.425   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:601.015 - 2.607ms returns 0x27C
TA260 002:601.022 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:601.026   Data:  A9 FE 53 EC 1A 2B FB F7 A8 FE CD E9 02 01 01 EB ...
TA260 002:601.039   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:602.915 - 1.892ms returns 0x184
TA260 002:602.924 JLINK_HasError()
TA260 002:602.930 JLINK_WriteReg(R0, 0x08004400)
TA260 002:602.935 - 0.005ms returns 0
TA260 002:602.941 JLINK_WriteReg(R1, 0x00000400)
TA260 002:602.945 - 0.004ms returns 0
TA260 002:602.950 JLINK_WriteReg(R2, 0x20000184)
TA260 002:602.954 - 0.004ms returns 0
TA260 002:602.959 JLINK_WriteReg(R3, 0x00000000)
TA260 002:602.964 - 0.004ms returns 0
TA260 002:602.969 JLINK_WriteReg(R4, 0x00000000)
TA260 002:602.973 - 0.004ms returns 0
TA260 002:602.978 JLINK_WriteReg(R5, 0x00000000)
TA260 002:602.982 - 0.004ms returns 0
TA260 002:602.987 JLINK_WriteReg(R6, 0x00000000)
TA260 002:602.992 - 0.004ms returns 0
TA260 002:602.997 JLINK_WriteReg(R7, 0x00000000)
TA260 002:603.001 - 0.004ms returns 0
TA260 002:603.006 JLINK_WriteReg(R8, 0x00000000)
TA260 002:603.010 - 0.004ms returns 0
TA260 002:603.015 JLINK_WriteReg(R9, 0x20000180)
TA260 002:603.020 - 0.004ms returns 0
TA260 002:603.025 JLINK_WriteReg(R10, 0x00000000)
TA260 002:603.030 - 0.004ms returns 0
TA260 002:603.035 JLINK_WriteReg(R11, 0x00000000)
TA260 002:603.039 - 0.004ms returns 0
TA260 002:603.044 JLINK_WriteReg(R12, 0x00000000)
TA260 002:603.048 - 0.004ms returns 0
TA260 002:603.053 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:603.058 - 0.005ms returns 0
TA260 002:603.063 JLINK_WriteReg(R14, 0x20000001)
TA260 002:603.068 - 0.004ms returns 0
TA260 002:603.073 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:603.077 - 0.004ms returns 0
TA260 002:603.082 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:603.086 - 0.004ms returns 0
TA260 002:603.092 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:603.096 - 0.004ms returns 0
TA260 002:603.101 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:603.105 - 0.004ms returns 0
TA260 002:603.110 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:603.114 - 0.004ms returns 0
TA260 002:603.120 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:603.125 - 0.005ms returns 0x0000001D
TA260 002:603.129 JLINK_Go()
TA260 002:603.137   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:605.917 - 2.787ms 
TA260 002:605.925 JLINK_IsHalted()
TA260 002:606.437 - 0.511ms returns FALSE
TA260 002:606.450 JLINK_HasError()
TA260 002:607.719 JLINK_IsHalted()
TA260 002:608.208 - 0.488ms returns FALSE
TA260 002:608.217 JLINK_HasError()
TA260 002:609.721 JLINK_IsHalted()
TA260 002:610.183 - 0.461ms returns FALSE
TA260 002:610.190 JLINK_HasError()
TA260 002:611.721 JLINK_IsHalted()
TA260 002:614.002   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:614.502 - 2.781ms returns TRUE
TA260 002:614.508 JLINK_ReadReg(R15 (PC))
TA260 002:614.513 - 0.004ms returns 0x20000000
TA260 002:614.518 JLINK_ClrBPEx(BPHandle = 0x0000001D)
TA260 002:614.521 - 0.003ms returns 0x00
TA260 002:614.526 JLINK_ReadReg(R0)
TA260 002:614.529 - 0.003ms returns 0x00000000
TA260 002:614.819 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:614.826   Data:  53 EC 11 2B FB F7 43 FD 41 EC 10 0B 35 F0 06 00 ...
TA260 002:614.835   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:617.438 - 2.619ms returns 0x27C
TA260 002:617.451 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:617.455   Data:  00 22 25 4B 20 46 31 46 FB F7 03 FC FB F7 1E FF ...
TA260 002:617.465   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:619.346 - 1.895ms returns 0x184
TA260 002:619.368 JLINK_HasError()
TA260 002:619.373 JLINK_WriteReg(R0, 0x08004800)
TA260 002:619.379 - 0.005ms returns 0
TA260 002:619.446 JLINK_WriteReg(R1, 0x00000400)
TA260 002:619.452 - 0.005ms returns 0
TA260 002:619.456 JLINK_WriteReg(R2, 0x20000184)
TA260 002:619.460 - 0.003ms returns 0
TA260 002:619.464 JLINK_WriteReg(R3, 0x00000000)
TA260 002:619.468 - 0.003ms returns 0
TA260 002:619.472 JLINK_WriteReg(R4, 0x00000000)
TA260 002:619.475 - 0.003ms returns 0
TA260 002:619.479 JLINK_WriteReg(R5, 0x00000000)
TA260 002:619.483 - 0.003ms returns 0
TA260 002:619.487 JLINK_WriteReg(R6, 0x00000000)
TA260 002:619.490 - 0.003ms returns 0
TA260 002:619.494 JLINK_WriteReg(R7, 0x00000000)
TA260 002:619.498 - 0.003ms returns 0
TA260 002:619.506 JLINK_WriteReg(R8, 0x00000000)
TA260 002:619.509 - 0.003ms returns 0
TA260 002:619.514 JLINK_WriteReg(R9, 0x20000180)
TA260 002:619.517 - 0.003ms returns 0
TA260 002:619.521 JLINK_WriteReg(R10, 0x00000000)
TA260 002:619.524 - 0.003ms returns 0
TA260 002:619.529 JLINK_WriteReg(R11, 0x00000000)
TA260 002:619.532 - 0.003ms returns 0
TA260 002:619.536 JLINK_WriteReg(R12, 0x00000000)
TA260 002:619.539 - 0.003ms returns 0
TA260 002:619.543 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:619.547 - 0.003ms returns 0
TA260 002:619.551 JLINK_WriteReg(R14, 0x20000001)
TA260 002:619.555 - 0.003ms returns 0
TA260 002:619.559 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:619.562 - 0.003ms returns 0
TA260 002:619.566 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:619.570 - 0.003ms returns 0
TA260 002:619.574 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:619.577 - 0.003ms returns 0
TA260 002:619.581 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:619.585 - 0.003ms returns 0
TA260 002:619.589 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:619.592 - 0.003ms returns 0
TA260 002:619.597 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:619.602 - 0.005ms returns 0x0000001E
TA260 002:619.606 JLINK_Go()
TA260 002:619.615   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:622.308 - 2.701ms 
TA260 002:622.316 JLINK_IsHalted()
TA260 002:622.829 - 0.513ms returns FALSE
TA260 002:622.836 JLINK_HasError()
TA260 002:624.729 JLINK_IsHalted()
TA260 002:625.210 - 0.480ms returns FALSE
TA260 002:625.216 JLINK_HasError()
TA260 002:626.730 JLINK_IsHalted()
TA260 002:629.075   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:629.649 - 2.919ms returns TRUE
TA260 002:629.657 JLINK_ReadReg(R15 (PC))
TA260 002:629.662 - 0.004ms returns 0x20000000
TA260 002:629.666 JLINK_ClrBPEx(BPHandle = 0x0000001E)
TA260 002:629.670 - 0.003ms returns 0x00
TA260 002:629.674 JLINK_ReadReg(R0)
TA260 002:629.678 - 0.003ms returns 0x00000000
TA260 002:630.065 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:630.072   Data:  0B D0 13 DC 58 28 77 D0 09 DC 00 28 75 D0 45 28 ...
TA260 002:630.081   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:632.745 - 2.680ms returns 0x27C
TA260 002:632.752 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:632.755   Data:  60 07 02 D5 24 F4 80 34 00 E0 01 27 57 45 02 DD ...
TA260 002:632.762   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:634.647 - 1.895ms returns 0x184
TA260 002:634.653 JLINK_HasError()
TA260 002:634.658 JLINK_WriteReg(R0, 0x08004C00)
TA260 002:634.663 - 0.004ms returns 0
TA260 002:634.667 JLINK_WriteReg(R1, 0x00000400)
TA260 002:634.671 - 0.003ms returns 0
TA260 002:634.675 JLINK_WriteReg(R2, 0x20000184)
TA260 002:634.679 - 0.004ms returns 0
TA260 002:634.683 JLINK_WriteReg(R3, 0x00000000)
TA260 002:634.686 - 0.003ms returns 0
TA260 002:634.690 JLINK_WriteReg(R4, 0x00000000)
TA260 002:634.694 - 0.003ms returns 0
TA260 002:634.698 JLINK_WriteReg(R5, 0x00000000)
TA260 002:634.701 - 0.003ms returns 0
TA260 002:634.705 JLINK_WriteReg(R6, 0x00000000)
TA260 002:634.709 - 0.003ms returns 0
TA260 002:634.713 JLINK_WriteReg(R7, 0x00000000)
TA260 002:634.716 - 0.003ms returns 0
TA260 002:634.720 JLINK_WriteReg(R8, 0x00000000)
TA260 002:634.723 - 0.003ms returns 0
TA260 002:634.728 JLINK_WriteReg(R9, 0x20000180)
TA260 002:634.734 - 0.006ms returns 0
TA260 002:634.739 JLINK_WriteReg(R10, 0x00000000)
TA260 002:634.743 - 0.003ms returns 0
TA260 002:634.747 JLINK_WriteReg(R11, 0x00000000)
TA260 002:634.750 - 0.003ms returns 0
TA260 002:634.754 JLINK_WriteReg(R12, 0x00000000)
TA260 002:634.758 - 0.003ms returns 0
TA260 002:634.762 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:634.766 - 0.003ms returns 0
TA260 002:634.770 JLINK_WriteReg(R14, 0x20000001)
TA260 002:634.773 - 0.003ms returns 0
TA260 002:634.777 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:634.781 - 0.003ms returns 0
TA260 002:634.785 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:634.788 - 0.003ms returns 0
TA260 002:634.792 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:634.795 - 0.003ms returns 0
TA260 002:634.799 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:634.803 - 0.003ms returns 0
TA260 002:634.807 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:634.810 - 0.003ms returns 0
TA260 002:634.815 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:634.819 - 0.004ms returns 0x0000001F
TA260 002:634.823 JLINK_Go()
TA260 002:634.830   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:637.578 - 2.755ms 
TA260 002:637.592 JLINK_IsHalted()
TA260 002:638.080 - 0.488ms returns FALSE
TA260 002:638.086 JLINK_HasError()
TA260 002:639.238 JLINK_IsHalted()
TA260 002:639.787 - 0.548ms returns FALSE
TA260 002:639.794 JLINK_HasError()
TA260 002:641.738 JLINK_IsHalted()
TA260 002:644.136   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:644.646 - 2.908ms returns TRUE
TA260 002:644.653 JLINK_ReadReg(R15 (PC))
TA260 002:644.657 - 0.004ms returns 0x20000000
TA260 002:644.662 JLINK_ClrBPEx(BPHandle = 0x0000001F)
TA260 002:644.666 - 0.003ms returns 0x00
TA260 002:644.670 JLINK_ReadReg(R0)
TA260 002:644.674 - 0.003ms returns 0x00000000
TA260 002:645.003 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:645.010   Data:  DD E9 0F 02 03 92 0E 9B 11 99 00 22 DD F8 0C A0 ...
TA260 002:645.020   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:647.652 - 2.649ms returns 0x27C
TA260 002:647.661 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:647.665   Data:  41 EC 19 0B FB F7 6A FB 41 EC 18 0B 18 EE 10 0A ...
TA260 002:647.674   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:649.550 - 1.889ms returns 0x184
TA260 002:649.558 JLINK_HasError()
TA260 002:649.563 JLINK_WriteReg(R0, 0x08005000)
TA260 002:649.568 - 0.004ms returns 0
TA260 002:649.572 JLINK_WriteReg(R1, 0x00000400)
TA260 002:649.575 - 0.003ms returns 0
TA260 002:649.579 JLINK_WriteReg(R2, 0x20000184)
TA260 002:649.583 - 0.003ms returns 0
TA260 002:649.587 JLINK_WriteReg(R3, 0x00000000)
TA260 002:649.590 - 0.003ms returns 0
TA260 002:649.594 JLINK_WriteReg(R4, 0x00000000)
TA260 002:649.598 - 0.003ms returns 0
TA260 002:649.602 JLINK_WriteReg(R5, 0x00000000)
TA260 002:649.605 - 0.003ms returns 0
TA260 002:649.609 JLINK_WriteReg(R6, 0x00000000)
TA260 002:649.612 - 0.003ms returns 0
TA260 002:649.616 JLINK_WriteReg(R7, 0x00000000)
TA260 002:649.620 - 0.003ms returns 0
TA260 002:649.624 JLINK_WriteReg(R8, 0x00000000)
TA260 002:649.627 - 0.003ms returns 0
TA260 002:649.631 JLINK_WriteReg(R9, 0x20000180)
TA260 002:649.634 - 0.003ms returns 0
TA260 002:649.638 JLINK_WriteReg(R10, 0x00000000)
TA260 002:649.642 - 0.003ms returns 0
TA260 002:649.646 JLINK_WriteReg(R11, 0x00000000)
TA260 002:649.649 - 0.003ms returns 0
TA260 002:649.653 JLINK_WriteReg(R12, 0x00000000)
TA260 002:649.657 - 0.003ms returns 0
TA260 002:649.661 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:649.664 - 0.003ms returns 0
TA260 002:649.668 JLINK_WriteReg(R14, 0x20000001)
TA260 002:649.672 - 0.003ms returns 0
TA260 002:649.676 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:649.679 - 0.003ms returns 0
TA260 002:649.683 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:649.687 - 0.003ms returns 0
TA260 002:649.691 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:649.694 - 0.003ms returns 0
TA260 002:649.698 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:649.702 - 0.003ms returns 0
TA260 002:649.706 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:649.750 - 0.044ms returns 0
TA260 002:649.755 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:649.759 - 0.004ms returns 0x00000020
TA260 002:649.763 JLINK_Go()
TA260 002:649.771   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:652.579 - 2.807ms 
TA260 002:652.608 JLINK_IsHalted()
TA260 002:653.108 - 0.506ms returns FALSE
TA260 002:653.125 JLINK_HasError()
TA260 002:654.248 JLINK_IsHalted()
TA260 002:654.731 - 0.482ms returns FALSE
TA260 002:654.736 JLINK_HasError()
TA260 002:656.251 JLINK_IsHalted()
TA260 002:656.776 - 0.524ms returns FALSE
TA260 002:656.786 JLINK_HasError()
TA260 002:658.251 JLINK_IsHalted()
TA260 002:660.490   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:660.971 - 2.719ms returns TRUE
TA260 002:660.977 JLINK_ReadReg(R15 (PC))
TA260 002:660.982 - 0.004ms returns 0x20000000
TA260 002:660.986 JLINK_ClrBPEx(BPHandle = 0x00000020)
TA260 002:660.990 - 0.003ms returns 0x00
TA260 002:660.995 JLINK_ReadReg(R0)
TA260 002:660.999 - 0.003ms returns 0x00000000
TA260 002:661.352 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:661.359   Data:  A0 01 A0 05 A0 03 A0 07 60 00 60 04 60 02 60 06 ...
TA260 002:661.368   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:663.958 - 2.606ms returns 0x27C
TA260 002:663.966 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:663.970   Data:  B4 02 B4 06 B4 01 B4 05 B4 03 B4 07 74 00 74 04 ...
TA260 002:663.978   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:665.880 - 1.913ms returns 0x184
TA260 002:665.896 JLINK_HasError()
TA260 002:665.907 JLINK_WriteReg(R0, 0x08005400)
TA260 002:665.913 - 0.005ms returns 0
TA260 002:665.917 JLINK_WriteReg(R1, 0x00000400)
TA260 002:665.920 - 0.003ms returns 0
TA260 002:665.924 JLINK_WriteReg(R2, 0x20000184)
TA260 002:665.928 - 0.003ms returns 0
TA260 002:665.932 JLINK_WriteReg(R3, 0x00000000)
TA260 002:665.935 - 0.003ms returns 0
TA260 002:665.939 JLINK_WriteReg(R4, 0x00000000)
TA260 002:665.942 - 0.003ms returns 0
TA260 002:665.947 JLINK_WriteReg(R5, 0x00000000)
TA260 002:665.950 - 0.003ms returns 0
TA260 002:665.955 JLINK_WriteReg(R6, 0x00000000)
TA260 002:665.959 - 0.003ms returns 0
TA260 002:665.963 JLINK_WriteReg(R7, 0x00000000)
TA260 002:665.966 - 0.003ms returns 0
TA260 002:665.971 JLINK_WriteReg(R8, 0x00000000)
TA260 002:665.975 - 0.004ms returns 0
TA260 002:665.980 JLINK_WriteReg(R9, 0x20000180)
TA260 002:665.983 - 0.003ms returns 0
TA260 002:665.987 JLINK_WriteReg(R10, 0x00000000)
TA260 002:665.990 - 0.003ms returns 0
TA260 002:665.994 JLINK_WriteReg(R11, 0x00000000)
TA260 002:665.998 - 0.003ms returns 0
TA260 002:666.002 JLINK_WriteReg(R12, 0x00000000)
TA260 002:666.005 - 0.003ms returns 0
TA260 002:666.009 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:666.013 - 0.003ms returns 0
TA260 002:666.017 JLINK_WriteReg(R14, 0x20000001)
TA260 002:666.020 - 0.003ms returns 0
TA260 002:666.024 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:666.028 - 0.003ms returns 0
TA260 002:666.032 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:666.035 - 0.003ms returns 0
TA260 002:666.039 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:666.042 - 0.003ms returns 0
TA260 002:666.047 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:666.050 - 0.003ms returns 0
TA260 002:666.054 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:666.058 - 0.003ms returns 0
TA260 002:666.062 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:666.066 - 0.004ms returns 0x00000021
TA260 002:666.070 JLINK_Go()
TA260 002:666.078   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:668.882 - 2.810ms 
TA260 002:668.896 JLINK_IsHalted()
TA260 002:669.343 - 0.447ms returns FALSE
TA260 002:669.350 JLINK_HasError()
TA260 002:671.758 JLINK_IsHalted()
TA260 002:672.197 - 0.439ms returns FALSE
TA260 002:672.203 JLINK_HasError()
TA260 002:673.260 JLINK_IsHalted()
TA260 002:675.648   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:676.149 - 2.888ms returns TRUE
TA260 002:676.155 JLINK_ReadReg(R15 (PC))
TA260 002:676.160 - 0.004ms returns 0x20000000
TA260 002:676.164 JLINK_ClrBPEx(BPHandle = 0x00000021)
TA260 002:676.171 - 0.006ms returns 0x00
TA260 002:676.178 JLINK_ReadReg(R0)
TA260 002:676.182 - 0.004ms returns 0x00000000
TA260 002:676.504 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:676.511   Data:  A2 01 A2 05 A2 03 A2 07 62 00 62 04 62 02 62 06 ...
TA260 002:676.521   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:679.145 - 2.640ms returns 0x27C
TA260 002:679.160 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:679.164   Data:  B6 02 B6 06 B6 01 B6 05 B6 03 B6 07 76 00 76 04 ...
TA260 002:679.174   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:681.002 - 1.842ms returns 0x184
TA260 002:681.010 JLINK_HasError()
TA260 002:681.016 JLINK_WriteReg(R0, 0x08005800)
TA260 002:681.020 - 0.004ms returns 0
TA260 002:681.024 JLINK_WriteReg(R1, 0x00000400)
TA260 002:681.028 - 0.003ms returns 0
TA260 002:681.032 JLINK_WriteReg(R2, 0x20000184)
TA260 002:681.035 - 0.003ms returns 0
TA260 002:681.039 JLINK_WriteReg(R3, 0x00000000)
TA260 002:681.043 - 0.003ms returns 0
TA260 002:681.047 JLINK_WriteReg(R4, 0x00000000)
TA260 002:681.050 - 0.003ms returns 0
TA260 002:681.054 JLINK_WriteReg(R5, 0x00000000)
TA260 002:681.057 - 0.003ms returns 0
TA260 002:681.061 JLINK_WriteReg(R6, 0x00000000)
TA260 002:681.065 - 0.003ms returns 0
TA260 002:681.069 JLINK_WriteReg(R7, 0x00000000)
TA260 002:681.072 - 0.003ms returns 0
TA260 002:681.076 JLINK_WriteReg(R8, 0x00000000)
TA260 002:681.080 - 0.003ms returns 0
TA260 002:681.084 JLINK_WriteReg(R9, 0x20000180)
TA260 002:681.088 - 0.003ms returns 0
TA260 002:681.092 JLINK_WriteReg(R10, 0x00000000)
TA260 002:681.095 - 0.003ms returns 0
TA260 002:681.099 JLINK_WriteReg(R11, 0x00000000)
TA260 002:681.102 - 0.003ms returns 0
TA260 002:681.106 JLINK_WriteReg(R12, 0x00000000)
TA260 002:681.110 - 0.003ms returns 0
TA260 002:681.114 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:681.118 - 0.004ms returns 0
TA260 002:681.122 JLINK_WriteReg(R14, 0x20000001)
TA260 002:681.125 - 0.003ms returns 0
TA260 002:681.129 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:681.132 - 0.003ms returns 0
TA260 002:681.136 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:681.140 - 0.003ms returns 0
TA260 002:681.144 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:681.147 - 0.003ms returns 0
TA260 002:681.151 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:681.155 - 0.003ms returns 0
TA260 002:681.159 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:681.162 - 0.003ms returns 0
TA260 002:681.167 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:681.171 - 0.004ms returns 0x00000022
TA260 002:681.175 JLINK_Go()
TA260 002:681.183   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:683.912 - 2.736ms 
TA260 002:683.920 JLINK_IsHalted()
TA260 002:684.389 - 0.468ms returns FALSE
TA260 002:684.395 JLINK_HasError()
TA260 002:686.267 JLINK_IsHalted()
TA260 002:686.792 - 0.524ms returns FALSE
TA260 002:686.805 JLINK_HasError()
TA260 002:688.269 JLINK_IsHalted()
TA260 002:690.583   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:691.071 - 2.801ms returns TRUE
TA260 002:691.077 JLINK_ReadReg(R15 (PC))
TA260 002:691.082 - 0.004ms returns 0x20000000
TA260 002:691.086 JLINK_ClrBPEx(BPHandle = 0x00000022)
TA260 002:691.090 - 0.003ms returns 0x00
TA260 002:691.094 JLINK_ReadReg(R0)
TA260 002:691.098 - 0.003ms returns 0x00000000
TA260 002:691.398 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:691.405   Data:  C0 7D 82 3E 93 8E 88 3E 22 9A 8E 3E 32 A0 94 3E ...
TA260 002:691.414   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:694.016 - 2.617ms returns 0x27C
TA260 002:694.024 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:694.028   Data:  02 9F 4D 3F F8 BB 4B 3F 12 D1 49 3F 65 DE 47 3F ...
TA260 002:694.036   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:695.867 - 1.843ms returns 0x184
TA260 002:695.878 JLINK_HasError()
TA260 002:695.909 JLINK_WriteReg(R0, 0x08005C00)
TA260 002:695.914 - 0.005ms returns 0
TA260 002:695.919 JLINK_WriteReg(R1, 0x00000400)
TA260 002:695.922 - 0.003ms returns 0
TA260 002:695.926 JLINK_WriteReg(R2, 0x20000184)
TA260 002:695.932 - 0.006ms returns 0
TA260 002:695.938 JLINK_WriteReg(R3, 0x00000000)
TA260 002:695.941 - 0.003ms returns 0
TA260 002:695.945 JLINK_WriteReg(R4, 0x00000000)
TA260 002:695.948 - 0.003ms returns 0
TA260 002:695.953 JLINK_WriteReg(R5, 0x00000000)
TA260 002:695.956 - 0.003ms returns 0
TA260 002:695.960 JLINK_WriteReg(R6, 0x00000000)
TA260 002:695.963 - 0.003ms returns 0
TA260 002:695.967 JLINK_WriteReg(R7, 0x00000000)
TA260 002:695.971 - 0.003ms returns 0
TA260 002:695.975 JLINK_WriteReg(R8, 0x00000000)
TA260 002:695.978 - 0.003ms returns 0
TA260 002:695.982 JLINK_WriteReg(R9, 0x20000180)
TA260 002:695.986 - 0.003ms returns 0
TA260 002:695.990 JLINK_WriteReg(R10, 0x00000000)
TA260 002:695.993 - 0.003ms returns 0
TA260 002:695.997 JLINK_WriteReg(R11, 0x00000000)
TA260 002:696.001 - 0.003ms returns 0
TA260 002:696.005 JLINK_WriteReg(R12, 0x00000000)
TA260 002:696.008 - 0.003ms returns 0
TA260 002:696.012 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:696.016 - 0.003ms returns 0
TA260 002:696.020 JLINK_WriteReg(R14, 0x20000001)
TA260 002:696.023 - 0.003ms returns 0
TA260 002:696.027 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:696.031 - 0.003ms returns 0
TA260 002:696.035 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:696.038 - 0.003ms returns 0
TA260 002:696.042 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:696.046 - 0.003ms returns 0
TA260 002:696.050 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:696.053 - 0.003ms returns 0
TA260 002:696.057 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:696.060 - 0.003ms returns 0
TA260 002:696.065 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:696.069 - 0.004ms returns 0x00000023
TA260 002:696.073 JLINK_Go()
TA260 002:696.080   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:698.915 - 2.841ms 
TA260 002:698.928 JLINK_IsHalted()
TA260 002:699.435 - 0.506ms returns FALSE
TA260 002:699.443 JLINK_HasError()
TA260 002:700.776 JLINK_IsHalted()
TA260 002:701.231 - 0.455ms returns FALSE
TA260 002:701.237 JLINK_HasError()
TA260 002:702.274 JLINK_IsHalted()
TA260 002:702.788 - 0.513ms returns FALSE
TA260 002:702.793 JLINK_HasError()
TA260 002:704.278 JLINK_IsHalted()
TA260 002:706.667   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:707.133 - 2.855ms returns TRUE
TA260 002:707.146 JLINK_ReadReg(R15 (PC))
TA260 002:707.152 - 0.005ms returns 0x20000000
TA260 002:707.156 JLINK_ClrBPEx(BPHandle = 0x00000023)
TA260 002:707.160 - 0.003ms returns 0x00
TA260 002:707.165 JLINK_ReadReg(R0)
TA260 002:707.168 - 0.003ms returns 0x00000000
TA260 002:707.480 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:707.487   Data:  C0 7D 82 BE 93 8E 88 BE 22 9A 8E BE 32 A0 94 BE ...
TA260 002:707.497   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:710.107 - 2.627ms returns 0x27C
TA260 002:710.117 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:710.120   Data:  02 9F 4D BF F8 BB 4B BF 12 D1 49 BF 65 DE 47 BF ...
TA260 002:710.129   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:712.013 - 1.896ms returns 0x184
TA260 002:712.020 JLINK_HasError()
TA260 002:712.024 JLINK_WriteReg(R0, 0x08006000)
TA260 002:712.028 - 0.004ms returns 0
TA260 002:712.033 JLINK_WriteReg(R1, 0x00000400)
TA260 002:712.036 - 0.003ms returns 0
TA260 002:712.040 JLINK_WriteReg(R2, 0x20000184)
TA260 002:712.043 - 0.003ms returns 0
TA260 002:712.047 JLINK_WriteReg(R3, 0x00000000)
TA260 002:712.051 - 0.003ms returns 0
TA260 002:712.055 JLINK_WriteReg(R4, 0x00000000)
TA260 002:712.058 - 0.003ms returns 0
TA260 002:712.062 JLINK_WriteReg(R5, 0x00000000)
TA260 002:712.066 - 0.003ms returns 0
TA260 002:712.070 JLINK_WriteReg(R6, 0x00000000)
TA260 002:712.073 - 0.003ms returns 0
TA260 002:712.077 JLINK_WriteReg(R7, 0x00000000)
TA260 002:712.080 - 0.003ms returns 0
TA260 002:712.084 JLINK_WriteReg(R8, 0x00000000)
TA260 002:712.088 - 0.003ms returns 0
TA260 002:712.092 JLINK_WriteReg(R9, 0x20000180)
TA260 002:712.095 - 0.003ms returns 0
TA260 002:712.099 JLINK_WriteReg(R10, 0x00000000)
TA260 002:712.102 - 0.003ms returns 0
TA260 002:712.107 JLINK_WriteReg(R11, 0x00000000)
TA260 002:712.115 - 0.007ms returns 0
TA260 002:712.119 JLINK_WriteReg(R12, 0x00000000)
TA260 002:712.122 - 0.003ms returns 0
TA260 002:712.126 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:712.130 - 0.003ms returns 0
TA260 002:712.134 JLINK_WriteReg(R14, 0x20000001)
TA260 002:712.137 - 0.003ms returns 0
TA260 002:712.141 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:712.144 - 0.003ms returns 0
TA260 002:712.149 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:712.152 - 0.003ms returns 0
TA260 002:712.156 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:712.159 - 0.003ms returns 0
TA260 002:712.163 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:712.167 - 0.003ms returns 0
TA260 002:712.171 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:712.174 - 0.003ms returns 0
TA260 002:712.178 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:712.182 - 0.004ms returns 0x00000024
TA260 002:712.187 JLINK_Go()
TA260 002:712.194   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:714.969 - 2.782ms 
TA260 002:714.979 JLINK_IsHalted()
TA260 002:715.479 - 0.499ms returns FALSE
TA260 002:715.485 JLINK_HasError()
TA260 002:716.786 JLINK_IsHalted()
TA260 002:717.299 - 0.512ms returns FALSE
TA260 002:717.311 JLINK_HasError()
TA260 002:718.787 JLINK_IsHalted()
TA260 002:719.309 - 0.521ms returns FALSE
TA260 002:719.316 JLINK_HasError()
TA260 002:720.786 JLINK_IsHalted()
TA260 002:723.161   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:723.685 - 2.899ms returns TRUE
TA260 002:723.732 JLINK_ReadReg(R15 (PC))
TA260 002:723.738 - 0.005ms returns 0x20000000
TA260 002:723.742 JLINK_ClrBPEx(BPHandle = 0x00000024)
TA260 002:723.746 - 0.003ms returns 0x00
TA260 002:723.750 JLINK_ReadReg(R0)
TA260 002:723.754 - 0.003ms returns 0x00000000
TA260 002:724.089 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:724.096   Data:  49 00 4D 4F 44 45 5F 46 52 45 51 55 45 4E 43 59 ...
TA260 002:724.106   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:726.731 - 2.641ms returns 0x27C
TA260 002:726.743 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:726.747   Data:  37 7D DB 3D B1 7B 7E 3F C4 9C DE 3D B0 70 7E 3F ...
TA260 002:726.756   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:728.653 - 1.909ms returns 0x184
TA260 002:728.664 JLINK_HasError()
TA260 002:728.669 JLINK_WriteReg(R0, 0x08006400)
TA260 002:728.674 - 0.004ms returns 0
TA260 002:728.678 JLINK_WriteReg(R1, 0x00000400)
TA260 002:728.681 - 0.003ms returns 0
TA260 002:728.686 JLINK_WriteReg(R2, 0x20000184)
TA260 002:728.689 - 0.003ms returns 0
TA260 002:728.693 JLINK_WriteReg(R3, 0x00000000)
TA260 002:728.697 - 0.003ms returns 0
TA260 002:728.701 JLINK_WriteReg(R4, 0x00000000)
TA260 002:728.704 - 0.003ms returns 0
TA260 002:728.708 JLINK_WriteReg(R5, 0x00000000)
TA260 002:728.712 - 0.003ms returns 0
TA260 002:728.716 JLINK_WriteReg(R6, 0x00000000)
TA260 002:728.719 - 0.003ms returns 0
TA260 002:728.723 JLINK_WriteReg(R7, 0x00000000)
TA260 002:728.726 - 0.003ms returns 0
TA260 002:728.730 JLINK_WriteReg(R8, 0x00000000)
TA260 002:728.734 - 0.003ms returns 0
TA260 002:728.738 JLINK_WriteReg(R9, 0x20000180)
TA260 002:728.742 - 0.003ms returns 0
TA260 002:728.746 JLINK_WriteReg(R10, 0x00000000)
TA260 002:728.749 - 0.003ms returns 0
TA260 002:728.753 JLINK_WriteReg(R11, 0x00000000)
TA260 002:728.756 - 0.003ms returns 0
TA260 002:728.760 JLINK_WriteReg(R12, 0x00000000)
TA260 002:728.764 - 0.003ms returns 0
TA260 002:728.768 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:728.771 - 0.003ms returns 0
TA260 002:728.775 JLINK_WriteReg(R14, 0x20000001)
TA260 002:728.779 - 0.003ms returns 0
TA260 002:728.783 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:728.786 - 0.003ms returns 0
TA260 002:728.790 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:728.794 - 0.003ms returns 0
TA260 002:728.798 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:728.801 - 0.003ms returns 0
TA260 002:728.805 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:728.808 - 0.003ms returns 0
TA260 002:728.813 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:728.816 - 0.003ms returns 0
TA260 002:728.826 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:728.830 - 0.004ms returns 0x00000025
TA260 002:728.835 JLINK_Go()
TA260 002:728.842   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:731.684 - 2.849ms 
TA260 002:731.697 JLINK_IsHalted()
TA260 002:732.148 - 0.451ms returns FALSE
TA260 002:732.154 JLINK_HasError()
TA260 002:733.794 JLINK_IsHalted()
TA260 002:734.296 - 0.502ms returns FALSE
TA260 002:734.302 JLINK_HasError()
TA260 002:735.795 JLINK_IsHalted()
TA260 002:738.144   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:738.672 - 2.876ms returns TRUE
TA260 002:738.679 JLINK_ReadReg(R15 (PC))
TA260 002:738.684 - 0.005ms returns 0x20000000
TA260 002:738.688 JLINK_ClrBPEx(BPHandle = 0x00000025)
TA260 002:738.692 - 0.003ms returns 0x00
TA260 002:738.696 JLINK_ReadReg(R0)
TA260 002:738.700 - 0.003ms returns 0x00000000
TA260 002:739.018 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:739.026   Data:  20 BF 7B 3F 6C E3 39 3E CD AC 7B 3F CF 6E 3B 3E ...
TA260 002:739.035   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:741.650 - 2.631ms returns 0x27C
TA260 002:741.660 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:741.664   Data:  FE 20 99 3E 27 2A 74 3F CE E0 99 3E DD 0B 74 3F ...
TA260 002:741.673   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:743.547 - 1.887ms returns 0x184
TA260 002:743.554 JLINK_HasError()
TA260 002:743.559 JLINK_WriteReg(R0, 0x08006800)
TA260 002:743.563 - 0.004ms returns 0
TA260 002:743.568 JLINK_WriteReg(R1, 0x00000400)
TA260 002:743.571 - 0.003ms returns 0
TA260 002:743.575 JLINK_WriteReg(R2, 0x20000184)
TA260 002:743.578 - 0.003ms returns 0
TA260 002:743.583 JLINK_WriteReg(R3, 0x00000000)
TA260 002:743.586 - 0.003ms returns 0
TA260 002:743.590 JLINK_WriteReg(R4, 0x00000000)
TA260 002:743.594 - 0.003ms returns 0
TA260 002:743.598 JLINK_WriteReg(R5, 0x00000000)
TA260 002:743.601 - 0.003ms returns 0
TA260 002:743.605 JLINK_WriteReg(R6, 0x00000000)
TA260 002:743.608 - 0.003ms returns 0
TA260 002:743.612 JLINK_WriteReg(R7, 0x00000000)
TA260 002:743.616 - 0.003ms returns 0
TA260 002:743.620 JLINK_WriteReg(R8, 0x00000000)
TA260 002:743.623 - 0.003ms returns 0
TA260 002:743.627 JLINK_WriteReg(R9, 0x20000180)
TA260 002:743.630 - 0.003ms returns 0
TA260 002:743.634 JLINK_WriteReg(R10, 0x00000000)
TA260 002:743.638 - 0.003ms returns 0
TA260 002:743.642 JLINK_WriteReg(R11, 0x00000000)
TA260 002:743.645 - 0.003ms returns 0
TA260 002:743.649 JLINK_WriteReg(R12, 0x00000000)
TA260 002:743.653 - 0.003ms returns 0
TA260 002:743.657 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:743.660 - 0.003ms returns 0
TA260 002:743.664 JLINK_WriteReg(R14, 0x20000001)
TA260 002:743.668 - 0.003ms returns 0
TA260 002:743.672 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:743.675 - 0.003ms returns 0
TA260 002:743.679 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:743.683 - 0.003ms returns 0
TA260 002:743.687 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:743.690 - 0.003ms returns 0
TA260 002:743.694 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:743.698 - 0.003ms returns 0
TA260 002:743.702 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:743.705 - 0.003ms returns 0
TA260 002:743.710 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:743.713 - 0.004ms returns 0x00000026
TA260 002:743.717 JLINK_Go()
TA260 002:743.725   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:746.493 - 2.775ms 
TA260 002:746.500 JLINK_IsHalted()
TA260 002:747.018 - 0.517ms returns FALSE
TA260 002:747.026 JLINK_HasError()
TA260 002:748.301 JLINK_IsHalted()
TA260 002:748.787 - 0.486ms returns FALSE
TA260 002:748.797 JLINK_HasError()
TA260 002:750.300 JLINK_IsHalted()
TA260 002:750.774 - 0.474ms returns FALSE
TA260 002:750.780 JLINK_HasError()
TA260 002:752.802 JLINK_IsHalted()
TA260 002:755.158   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:755.637 - 2.835ms returns TRUE
TA260 002:755.643 JLINK_ReadReg(R15 (PC))
TA260 002:755.648 - 0.004ms returns 0x20000000
TA260 002:755.652 JLINK_ClrBPEx(BPHandle = 0x00000026)
TA260 002:755.656 - 0.003ms returns 0x00
TA260 002:755.665 JLINK_ReadReg(R0)
TA260 002:755.669 - 0.003ms returns 0x00000000
TA260 002:755.977 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:755.984   Data:  D5 D7 6D 3F 8B 62 BD 3E 93 B2 6D 3F 4A 1D BE 3E ...
TA260 002:755.993   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:758.558 - 2.580ms returns 0x27C
TA260 002:758.573 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:758.577   Data:  35 80 F5 3E 89 76 60 3F 93 30 F6 3E 21 46 60 3F ...
TA260 002:758.587   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:760.459 - 1.885ms returns 0x184
TA260 002:760.468 JLINK_HasError()
TA260 002:760.473 JLINK_WriteReg(R0, 0x08006C00)
TA260 002:760.478 - 0.004ms returns 0
TA260 002:760.482 JLINK_WriteReg(R1, 0x00000400)
TA260 002:760.485 - 0.003ms returns 0
TA260 002:760.489 JLINK_WriteReg(R2, 0x20000184)
TA260 002:760.492 - 0.003ms returns 0
TA260 002:760.496 JLINK_WriteReg(R3, 0x00000000)
TA260 002:760.500 - 0.003ms returns 0
TA260 002:760.504 JLINK_WriteReg(R4, 0x00000000)
TA260 002:760.508 - 0.003ms returns 0
TA260 002:760.512 JLINK_WriteReg(R5, 0x00000000)
TA260 002:760.515 - 0.003ms returns 0
TA260 002:760.519 JLINK_WriteReg(R6, 0x00000000)
TA260 002:760.522 - 0.003ms returns 0
TA260 002:760.526 JLINK_WriteReg(R7, 0x00000000)
TA260 002:760.530 - 0.003ms returns 0
TA260 002:760.540 JLINK_WriteReg(R8, 0x00000000)
TA260 002:760.544 - 0.003ms returns 0
TA260 002:760.548 JLINK_WriteReg(R9, 0x20000180)
TA260 002:760.551 - 0.003ms returns 0
TA260 002:760.555 JLINK_WriteReg(R10, 0x00000000)
TA260 002:760.559 - 0.003ms returns 0
TA260 002:760.563 JLINK_WriteReg(R11, 0x00000000)
TA260 002:760.566 - 0.003ms returns 0
TA260 002:760.570 JLINK_WriteReg(R12, 0x00000000)
TA260 002:760.574 - 0.003ms returns 0
TA260 002:760.578 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:760.581 - 0.003ms returns 0
TA260 002:760.585 JLINK_WriteReg(R14, 0x20000001)
TA260 002:760.589 - 0.003ms returns 0
TA260 002:760.593 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:760.596 - 0.003ms returns 0
TA260 002:760.600 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:760.604 - 0.003ms returns 0
TA260 002:760.608 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:760.611 - 0.003ms returns 0
TA260 002:760.615 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:760.618 - 0.003ms returns 0
TA260 002:760.622 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:760.626 - 0.003ms returns 0
TA260 002:760.630 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:760.634 - 0.004ms returns 0x00000027
TA260 002:760.638 JLINK_Go()
TA260 002:760.646   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:763.481 - 2.842ms 
TA260 002:763.492 JLINK_IsHalted()
TA260 002:763.993 - 0.500ms returns FALSE
TA260 002:763.999 JLINK_HasError()
TA260 002:765.311 JLINK_IsHalted()
TA260 002:765.822 - 0.510ms returns FALSE
TA260 002:765.827 JLINK_HasError()
TA260 002:767.312 JLINK_IsHalted()
TA260 002:767.789 - 0.476ms returns FALSE
TA260 002:767.802 JLINK_HasError()
TA260 002:769.311 JLINK_IsHalted()
TA260 002:771.645   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:772.160 - 2.848ms returns TRUE
TA260 002:772.165 JLINK_ReadReg(R15 (PC))
TA260 002:772.170 - 0.004ms returns 0x20000000
TA260 002:772.174 JLINK_ClrBPEx(BPHandle = 0x00000027)
TA260 002:772.178 - 0.003ms returns 0x00
TA260 002:772.182 JLINK_ReadReg(R0)
TA260 002:772.186 - 0.003ms returns 0x00000000
TA260 002:772.526 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:772.533   Data:  A7 CC 56 3F 1C 46 0B 3F E5 95 56 3F 6B 9A 0B 3F ...
TA260 002:772.542   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:775.142 - 2.616ms returns 0x27C
TA260 002:775.157 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:775.161   Data:  1A 38 24 3F AB 22 44 3F 2C 85 24 3F 00 E2 43 3F ...
TA260 002:775.170   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:777.019 - 1.862ms returns 0x184
TA260 002:777.035 JLINK_HasError()
TA260 002:777.040 JLINK_WriteReg(R0, 0x08007000)
TA260 002:777.045 - 0.004ms returns 0
TA260 002:777.049 JLINK_WriteReg(R1, 0x00000400)
TA260 002:777.056 - 0.006ms returns 0
TA260 002:777.061 JLINK_WriteReg(R2, 0x20000184)
TA260 002:777.065 - 0.004ms returns 0
TA260 002:777.069 JLINK_WriteReg(R3, 0x00000000)
TA260 002:777.073 - 0.003ms returns 0
TA260 002:777.077 JLINK_WriteReg(R4, 0x00000000)
TA260 002:777.080 - 0.003ms returns 0
TA260 002:777.084 JLINK_WriteReg(R5, 0x00000000)
TA260 002:777.088 - 0.003ms returns 0
TA260 002:777.141 JLINK_WriteReg(R6, 0x00000000)
TA260 002:777.146 - 0.005ms returns 0
TA260 002:777.151 JLINK_WriteReg(R7, 0x00000000)
TA260 002:777.155 - 0.003ms returns 0
TA260 002:777.159 JLINK_WriteReg(R8, 0x00000000)
TA260 002:777.162 - 0.003ms returns 0
TA260 002:777.166 JLINK_WriteReg(R9, 0x20000180)
TA260 002:777.170 - 0.003ms returns 0
TA260 002:777.174 JLINK_WriteReg(R10, 0x00000000)
TA260 002:777.178 - 0.003ms returns 0
TA260 002:777.182 JLINK_WriteReg(R11, 0x00000000)
TA260 002:777.185 - 0.003ms returns 0
TA260 002:777.189 JLINK_WriteReg(R12, 0x00000000)
TA260 002:777.192 - 0.003ms returns 0
TA260 002:777.197 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:777.200 - 0.003ms returns 0
TA260 002:777.204 JLINK_WriteReg(R14, 0x20000001)
TA260 002:777.208 - 0.003ms returns 0
TA260 002:777.212 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:777.215 - 0.003ms returns 0
TA260 002:777.220 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:777.223 - 0.003ms returns 0
TA260 002:777.227 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:777.230 - 0.003ms returns 0
TA260 002:777.234 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:777.238 - 0.003ms returns 0
TA260 002:777.242 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:777.245 - 0.003ms returns 0
TA260 002:777.250 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:777.254 - 0.004ms returns 0x00000028
TA260 002:777.258 JLINK_Go()
TA260 002:777.266   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:780.016 - 2.757ms 
TA260 002:780.033 JLINK_IsHalted()
TA260 002:780.494 - 0.460ms returns FALSE
TA260 002:780.507 JLINK_HasError()
TA260 002:782.827 JLINK_IsHalted()
TA260 002:783.275 - 0.447ms returns FALSE
TA260 002:783.287 JLINK_HasError()
TA260 002:784.823 JLINK_IsHalted()
TA260 002:787.160   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:787.690 - 2.867ms returns TRUE
TA260 002:787.704 JLINK_ReadReg(R15 (PC))
TA260 002:787.710 - 0.006ms returns 0x20000000
TA260 002:787.714 JLINK_ClrBPEx(BPHandle = 0x00000028)
TA260 002:787.718 - 0.003ms returns 0x00
TA260 002:787.723 JLINK_ReadReg(R0)
TA260 002:787.726 - 0.003ms returns 0x00000000
TA260 002:788.071 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:788.078   Data:  4A 80 37 3F C7 80 32 3F 23 3A 37 3F C9 C8 32 3F ...
TA260 002:788.088   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:790.644 - 2.573ms returns 0x27C
TA260 002:790.654 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:790.658   Data:  85 60 47 3F 3B 45 20 3F 84 9F 47 3F CB F6 1F 3F ...
TA260 002:790.666   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:792.549 - 1.895ms returns 0x184
TA260 002:792.558 JLINK_HasError()
TA260 002:792.564 JLINK_WriteReg(R0, 0x08007400)
TA260 002:792.569 - 0.004ms returns 0
TA260 002:792.573 JLINK_WriteReg(R1, 0x00000400)
TA260 002:792.577 - 0.003ms returns 0
TA260 002:792.581 JLINK_WriteReg(R2, 0x20000184)
TA260 002:792.584 - 0.003ms returns 0
TA260 002:792.588 JLINK_WriteReg(R3, 0x00000000)
TA260 002:792.592 - 0.003ms returns 0
TA260 002:792.596 JLINK_WriteReg(R4, 0x00000000)
TA260 002:792.599 - 0.003ms returns 0
TA260 002:792.603 JLINK_WriteReg(R5, 0x00000000)
TA260 002:792.606 - 0.003ms returns 0
TA260 002:792.610 JLINK_WriteReg(R6, 0x00000000)
TA260 002:792.614 - 0.003ms returns 0
TA260 002:792.618 JLINK_WriteReg(R7, 0x00000000)
TA260 002:792.621 - 0.003ms returns 0
TA260 002:792.625 JLINK_WriteReg(R8, 0x00000000)
TA260 002:792.629 - 0.003ms returns 0
TA260 002:792.633 JLINK_WriteReg(R9, 0x20000180)
TA260 002:792.636 - 0.003ms returns 0
TA260 002:792.640 JLINK_WriteReg(R10, 0x00000000)
TA260 002:792.643 - 0.003ms returns 0
TA260 002:792.653 JLINK_WriteReg(R11, 0x00000000)
TA260 002:792.656 - 0.003ms returns 0
TA260 002:792.665 JLINK_WriteReg(R12, 0x00000000)
TA260 002:792.668 - 0.003ms returns 0
TA260 002:792.672 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:792.676 - 0.003ms returns 0
TA260 002:792.680 JLINK_WriteReg(R14, 0x20000001)
TA260 002:792.684 - 0.003ms returns 0
TA260 002:792.688 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:792.691 - 0.003ms returns 0
TA260 002:792.695 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:792.699 - 0.003ms returns 0
TA260 002:792.703 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:792.706 - 0.003ms returns 0
TA260 002:792.710 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:792.714 - 0.003ms returns 0
TA260 002:792.724 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:792.728 - 0.003ms returns 0
TA260 002:792.732 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:792.736 - 0.004ms returns 0x00000029
TA260 002:792.740 JLINK_Go()
TA260 002:792.748   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:795.502 - 2.760ms 
TA260 002:795.512 JLINK_IsHalted()
TA260 002:796.017 - 0.505ms returns FALSE
TA260 002:796.024 JLINK_HasError()
TA260 002:797.332 JLINK_IsHalted()
TA260 002:797.814 - 0.481ms returns FALSE
TA260 002:797.827 JLINK_HasError()
TA260 002:799.331 JLINK_IsHalted()
TA260 002:799.789 - 0.458ms returns FALSE
TA260 002:799.796 JLINK_HasError()
TA260 002:801.834 JLINK_IsHalted()
TA260 002:804.137   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:804.647 - 2.813ms returns TRUE
TA260 002:804.657 JLINK_ReadReg(R15 (PC))
TA260 002:804.662 - 0.004ms returns 0x20000000
TA260 002:804.693 JLINK_ClrBPEx(BPHandle = 0x00000029)
TA260 002:804.698 - 0.004ms returns 0x00
TA260 002:804.702 JLINK_ReadReg(R0)
TA260 002:804.706 - 0.004ms returns 0x00000000
TA260 002:805.015 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:805.022   Data:  A7 26 11 3F 59 DF 52 3F CD D3 10 3F 49 18 53 3F ...
TA260 002:805.031   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:807.644 - 2.629ms returns 0x27C
TA260 002:807.698 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:807.702   Data:  7B DF 62 3F 21 7E EC 3E FC 0D 63 3F BB CB EB 3E ...
TA260 002:807.711   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:809.646 - 1.948ms returns 0x184
TA260 002:809.660 JLINK_HasError()
TA260 002:809.665 JLINK_WriteReg(R0, 0x08007800)
TA260 002:809.670 - 0.005ms returns 0
TA260 002:809.674 JLINK_WriteReg(R1, 0x00000400)
TA260 002:809.678 - 0.003ms returns 0
TA260 002:809.682 JLINK_WriteReg(R2, 0x20000184)
TA260 002:809.686 - 0.003ms returns 0
TA260 002:809.690 JLINK_WriteReg(R3, 0x00000000)
TA260 002:809.693 - 0.003ms returns 0
TA260 002:809.697 JLINK_WriteReg(R4, 0x00000000)
TA260 002:809.700 - 0.003ms returns 0
TA260 002:809.704 JLINK_WriteReg(R5, 0x00000000)
TA260 002:809.708 - 0.003ms returns 0
TA260 002:809.712 JLINK_WriteReg(R6, 0x00000000)
TA260 002:809.715 - 0.003ms returns 0
TA260 002:809.719 JLINK_WriteReg(R7, 0x00000000)
TA260 002:809.722 - 0.003ms returns 0
TA260 002:809.726 JLINK_WriteReg(R8, 0x00000000)
TA260 002:809.730 - 0.003ms returns 0
TA260 002:809.734 JLINK_WriteReg(R9, 0x20000180)
TA260 002:809.737 - 0.003ms returns 0
TA260 002:809.741 JLINK_WriteReg(R10, 0x00000000)
TA260 002:809.744 - 0.003ms returns 0
TA260 002:809.749 JLINK_WriteReg(R11, 0x00000000)
TA260 002:809.752 - 0.003ms returns 0
TA260 002:809.756 JLINK_WriteReg(R12, 0x00000000)
TA260 002:809.760 - 0.003ms returns 0
TA260 002:809.764 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:809.768 - 0.003ms returns 0
TA260 002:809.772 JLINK_WriteReg(R14, 0x20000001)
TA260 002:809.775 - 0.003ms returns 0
TA260 002:809.779 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:809.782 - 0.003ms returns 0
TA260 002:809.786 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:809.790 - 0.003ms returns 0
TA260 002:809.794 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:809.797 - 0.003ms returns 0
TA260 002:809.801 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:809.804 - 0.003ms returns 0
TA260 002:809.809 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:809.812 - 0.003ms returns 0
TA260 002:809.816 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:809.826 - 0.009ms returns 0x0000002A
TA260 002:809.830 JLINK_Go()
TA260 002:809.838   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:812.559 - 2.729ms 
TA260 002:812.566 JLINK_IsHalted()
TA260 002:813.061 - 0.494ms returns FALSE
TA260 002:813.069 JLINK_HasError()
TA260 002:814.151 JLINK_IsHalted()
TA260 002:814.683 - 0.531ms returns FALSE
TA260 002:814.688 JLINK_HasError()
TA260 002:816.150 JLINK_IsHalted()
TA260 002:816.643 - 0.491ms returns FALSE
TA260 002:816.656 JLINK_HasError()
TA260 002:818.152 JLINK_IsHalted()
TA260 002:820.454   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:820.919 - 2.766ms returns TRUE
TA260 002:820.926 JLINK_ReadReg(R15 (PC))
TA260 002:820.930 - 0.004ms returns 0x20000000
TA260 002:820.934 JLINK_ClrBPEx(BPHandle = 0x0000002A)
TA260 002:820.938 - 0.003ms returns 0x00
TA260 002:820.943 JLINK_ReadReg(R0)
TA260 002:820.946 - 0.003ms returns 0x00000000
TA260 002:821.287 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:821.294   Data:  10 72 CA 3E 5E 23 6B 3F 53 B9 C9 3E 0C 4B 6B 3F ...
TA260 002:821.303   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:823.968 - 2.680ms returns 0x27C
TA260 002:823.980 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:823.984   Data:  7B A6 75 3F 32 5B 8F 3E B3 C2 75 3F 22 9A 8E 3E ...
TA260 002:823.992   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:825.868 - 1.888ms returns 0x184
TA260 002:825.876 JLINK_HasError()
TA260 002:825.882 JLINK_WriteReg(R0, 0x08007C00)
TA260 002:825.886 - 0.004ms returns 0
TA260 002:825.890 JLINK_WriteReg(R1, 0x00000400)
TA260 002:825.894 - 0.003ms returns 0
TA260 002:825.898 JLINK_WriteReg(R2, 0x20000184)
TA260 002:825.902 - 0.003ms returns 0
TA260 002:825.906 JLINK_WriteReg(R3, 0x00000000)
TA260 002:825.909 - 0.003ms returns 0
TA260 002:825.913 JLINK_WriteReg(R4, 0x00000000)
TA260 002:825.917 - 0.003ms returns 0
TA260 002:825.921 JLINK_WriteReg(R5, 0x00000000)
TA260 002:825.924 - 0.003ms returns 0
TA260 002:825.928 JLINK_WriteReg(R6, 0x00000000)
TA260 002:825.931 - 0.003ms returns 0
TA260 002:825.935 JLINK_WriteReg(R7, 0x00000000)
TA260 002:825.939 - 0.003ms returns 0
TA260 002:825.943 JLINK_WriteReg(R8, 0x00000000)
TA260 002:825.946 - 0.003ms returns 0
TA260 002:825.950 JLINK_WriteReg(R9, 0x20000180)
TA260 002:825.953 - 0.003ms returns 0
TA260 002:825.958 JLINK_WriteReg(R10, 0x00000000)
TA260 002:825.961 - 0.003ms returns 0
TA260 002:825.965 JLINK_WriteReg(R11, 0x00000000)
TA260 002:825.968 - 0.003ms returns 0
TA260 002:825.972 JLINK_WriteReg(R12, 0x00000000)
TA260 002:825.977 - 0.004ms returns 0
TA260 002:825.982 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:825.986 - 0.003ms returns 0
TA260 002:825.990 JLINK_WriteReg(R14, 0x20000001)
TA260 002:825.993 - 0.003ms returns 0
TA260 002:825.997 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:826.001 - 0.003ms returns 0
TA260 002:826.005 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:826.008 - 0.003ms returns 0
TA260 002:826.012 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:826.016 - 0.003ms returns 0
TA260 002:826.020 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:826.023 - 0.003ms returns 0
TA260 002:826.027 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:826.030 - 0.003ms returns 0
TA260 002:826.035 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:826.039 - 0.004ms returns 0x0000002B
TA260 002:826.043 JLINK_Go()
TA260 002:826.050   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:828.768 - 2.724ms 
TA260 002:828.781 JLINK_IsHalted()
TA260 002:829.285 - 0.503ms returns FALSE
TA260 002:829.291 JLINK_HasError()
TA260 002:830.676 JLINK_IsHalted()
TA260 002:831.197 - 0.520ms returns FALSE
TA260 002:831.202 JLINK_HasError()
TA260 002:833.160 JLINK_IsHalted()
TA260 002:835.490   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:835.958 - 2.798ms returns TRUE
TA260 002:835.964 JLINK_ReadReg(R15 (PC))
TA260 002:835.968 - 0.004ms returns 0x20000000
TA260 002:835.973 JLINK_ClrBPEx(BPHandle = 0x0000002B)
TA260 002:835.977 - 0.003ms returns 0x00
TA260 002:835.981 JLINK_ReadReg(R0)
TA260 002:835.988 - 0.007ms returns 0x00000000
TA260 002:836.313 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:836.320   Data:  58 9E 55 3E 1C 5E 7A 3F 01 15 54 3E 02 73 7A 3F ...
TA260 002:836.329   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:838.887 - 2.574ms returns 0x27C
TA260 002:838.900 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:838.904   Data:  C9 FC 7E 3F BB D7 B2 3D A4 05 7F 3F 80 B6 AF 3D ...
TA260 002:838.913   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:841.019 - 2.118ms returns 0x184
TA260 002:841.032 JLINK_HasError()
TA260 002:841.037 JLINK_WriteReg(R0, 0x08008000)
TA260 002:841.042 - 0.004ms returns 0
TA260 002:841.046 JLINK_WriteReg(R1, 0x00000400)
TA260 002:841.050 - 0.003ms returns 0
TA260 002:841.054 JLINK_WriteReg(R2, 0x20000184)
TA260 002:841.058 - 0.003ms returns 0
TA260 002:841.062 JLINK_WriteReg(R3, 0x00000000)
TA260 002:841.065 - 0.003ms returns 0
TA260 002:841.069 JLINK_WriteReg(R4, 0x00000000)
TA260 002:841.073 - 0.003ms returns 0
TA260 002:841.077 JLINK_WriteReg(R5, 0x00000000)
TA260 002:841.080 - 0.003ms returns 0
TA260 002:841.084 JLINK_WriteReg(R6, 0x00000000)
TA260 002:841.087 - 0.003ms returns 0
TA260 002:841.091 JLINK_WriteReg(R7, 0x00000000)
TA260 002:841.095 - 0.003ms returns 0
TA260 002:841.099 JLINK_WriteReg(R8, 0x00000000)
TA260 002:841.102 - 0.003ms returns 0
TA260 002:841.106 JLINK_WriteReg(R9, 0x20000180)
TA260 002:841.109 - 0.003ms returns 0
TA260 002:841.113 JLINK_WriteReg(R10, 0x00000000)
TA260 002:841.117 - 0.003ms returns 0
TA260 002:841.121 JLINK_WriteReg(R11, 0x00000000)
TA260 002:841.124 - 0.003ms returns 0
TA260 002:841.128 JLINK_WriteReg(R12, 0x00000000)
TA260 002:841.132 - 0.003ms returns 0
TA260 002:841.136 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:841.140 - 0.003ms returns 0
TA260 002:841.144 JLINK_WriteReg(R14, 0x20000001)
TA260 002:841.147 - 0.003ms returns 0
TA260 002:841.152 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:841.155 - 0.003ms returns 0
TA260 002:841.159 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:841.162 - 0.003ms returns 0
TA260 002:841.166 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:841.170 - 0.003ms returns 0
TA260 002:841.174 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:841.177 - 0.003ms returns 0
TA260 002:841.181 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:841.184 - 0.003ms returns 0
TA260 002:841.189 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:841.193 - 0.004ms returns 0x0000002C
TA260 002:841.197 JLINK_Go()
TA260 002:841.205   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:843.911 - 2.713ms 
TA260 002:843.919 JLINK_IsHalted()
TA260 002:844.399 - 0.479ms returns FALSE
TA260 002:844.404 JLINK_HasError()
TA260 002:845.668 JLINK_IsHalted()
TA260 002:846.140 - 0.471ms returns FALSE
TA260 002:846.145 JLINK_HasError()
TA260 002:847.672 JLINK_IsHalted()
TA260 002:848.092 - 0.419ms returns FALSE
TA260 002:848.104 JLINK_HasError()
TA260 002:849.667 JLINK_IsHalted()
TA260 002:852.038   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:852.494 - 2.826ms returns TRUE
TA260 002:852.499 JLINK_ReadReg(R15 (PC))
TA260 002:852.504 - 0.004ms returns 0x20000000
TA260 002:852.508 JLINK_ClrBPEx(BPHandle = 0x0000002C)
TA260 002:852.512 - 0.003ms returns 0x00
TA260 002:852.516 JLINK_ReadReg(R0)
TA260 002:852.519 - 0.003ms returns 0x00000000
TA260 002:852.867 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:852.874   Data:  00 30 62 3C C1 F9 7F 3F 90 0E 49 3C 11 FB 7F 3F ...
TA260 002:852.883   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:855.505 - 2.638ms returns 0x27C
TA260 002:855.518 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:855.522   Data:  8B 86 7E 3F C4 9C DE BD B1 7B 7E 3F 2E BC E1 BD ...
TA260 002:855.531   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:857.426 - 1.907ms returns 0x184
TA260 002:857.442 JLINK_HasError()
TA260 002:857.478 JLINK_WriteReg(R0, 0x08008400)
TA260 002:857.485 - 0.006ms returns 0
TA260 002:857.490 JLINK_WriteReg(R1, 0x00000400)
TA260 002:857.493 - 0.003ms returns 0
TA260 002:857.497 JLINK_WriteReg(R2, 0x20000184)
TA260 002:857.506 - 0.008ms returns 0
TA260 002:857.510 JLINK_WriteReg(R3, 0x00000000)
TA260 002:857.513 - 0.003ms returns 0
TA260 002:857.517 JLINK_WriteReg(R4, 0x00000000)
TA260 002:857.520 - 0.003ms returns 0
TA260 002:857.524 JLINK_WriteReg(R5, 0x00000000)
TA260 002:857.528 - 0.003ms returns 0
TA260 002:857.532 JLINK_WriteReg(R6, 0x00000000)
TA260 002:857.535 - 0.003ms returns 0
TA260 002:857.539 JLINK_WriteReg(R7, 0x00000000)
TA260 002:857.542 - 0.003ms returns 0
TA260 002:857.546 JLINK_WriteReg(R8, 0x00000000)
TA260 002:857.550 - 0.003ms returns 0
TA260 002:857.554 JLINK_WriteReg(R9, 0x20000180)
TA260 002:857.557 - 0.003ms returns 0
TA260 002:857.561 JLINK_WriteReg(R10, 0x00000000)
TA260 002:857.564 - 0.003ms returns 0
TA260 002:857.569 JLINK_WriteReg(R11, 0x00000000)
TA260 002:857.572 - 0.003ms returns 0
TA260 002:857.576 JLINK_WriteReg(R12, 0x00000000)
TA260 002:857.579 - 0.003ms returns 0
TA260 002:857.584 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:857.587 - 0.004ms returns 0
TA260 002:857.591 JLINK_WriteReg(R14, 0x20000001)
TA260 002:857.595 - 0.003ms returns 0
TA260 002:857.599 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:857.602 - 0.003ms returns 0
TA260 002:857.606 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:857.610 - 0.003ms returns 0
TA260 002:857.614 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:857.617 - 0.003ms returns 0
TA260 002:857.621 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:857.624 - 0.003ms returns 0
TA260 002:857.628 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:857.632 - 0.003ms returns 0
TA260 002:857.636 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:857.641 - 0.004ms returns 0x0000002D
TA260 002:857.645 JLINK_Go()
TA260 002:857.653   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:860.454 - 2.808ms 
TA260 002:860.467 JLINK_IsHalted()
TA260 002:860.958 - 0.490ms returns FALSE
TA260 002:860.964 JLINK_HasError()
TA260 002:862.675 JLINK_IsHalted()
TA260 002:863.170 - 0.495ms returns FALSE
TA260 002:863.177 JLINK_HasError()
TA260 002:864.680 JLINK_IsHalted()
TA260 002:866.992   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:867.519 - 2.838ms returns TRUE
TA260 002:867.527 JLINK_ReadReg(R15 (PC))
TA260 002:867.531 - 0.004ms returns 0x20000000
TA260 002:867.536 JLINK_ClrBPEx(BPHandle = 0x0000002D)
TA260 002:867.540 - 0.004ms returns 0x00
TA260 002:867.544 JLINK_ReadReg(R0)
TA260 002:867.548 - 0.003ms returns 0x00000000
TA260 002:867.864 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:867.871   Data:  6C E3 39 BE 20 BF 7B 3F CF 6E 3B BE CD AC 7B 3F ...
TA260 002:867.880   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:870.427 - 2.563ms returns 0x27C
TA260 002:870.440 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:870.443   Data:  4B 48 74 3F CE E0 99 BE 27 2A 74 3F 86 A0 9A BE ...
TA260 002:870.452   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:872.330 - 1.890ms returns 0x184
TA260 002:872.336 JLINK_HasError()
TA260 002:872.341 JLINK_WriteReg(R0, 0x08008800)
TA260 002:872.346 - 0.004ms returns 0
TA260 002:872.350 JLINK_WriteReg(R1, 0x00000400)
TA260 002:872.353 - 0.003ms returns 0
TA260 002:872.357 JLINK_WriteReg(R2, 0x20000184)
TA260 002:872.361 - 0.003ms returns 0
TA260 002:872.365 JLINK_WriteReg(R3, 0x00000000)
TA260 002:872.368 - 0.003ms returns 0
TA260 002:872.372 JLINK_WriteReg(R4, 0x00000000)
TA260 002:872.376 - 0.003ms returns 0
TA260 002:872.380 JLINK_WriteReg(R5, 0x00000000)
TA260 002:872.383 - 0.003ms returns 0
TA260 002:872.387 JLINK_WriteReg(R6, 0x00000000)
TA260 002:872.390 - 0.003ms returns 0
TA260 002:872.394 JLINK_WriteReg(R7, 0x00000000)
TA260 002:872.398 - 0.003ms returns 0
TA260 002:872.402 JLINK_WriteReg(R8, 0x00000000)
TA260 002:872.405 - 0.003ms returns 0
TA260 002:872.409 JLINK_WriteReg(R9, 0x20000180)
TA260 002:872.413 - 0.003ms returns 0
TA260 002:872.417 JLINK_WriteReg(R10, 0x00000000)
TA260 002:872.420 - 0.003ms returns 0
TA260 002:872.424 JLINK_WriteReg(R11, 0x00000000)
TA260 002:872.428 - 0.003ms returns 0
TA260 002:872.432 JLINK_WriteReg(R12, 0x00000000)
TA260 002:872.439 - 0.007ms returns 0
TA260 002:872.443 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:872.447 - 0.004ms returns 0
TA260 002:872.452 JLINK_WriteReg(R14, 0x20000001)
TA260 002:872.455 - 0.003ms returns 0
TA260 002:872.459 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:872.462 - 0.003ms returns 0
TA260 002:872.466 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:872.470 - 0.003ms returns 0
TA260 002:872.474 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:872.477 - 0.003ms returns 0
TA260 002:872.481 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:872.485 - 0.003ms returns 0
TA260 002:872.489 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:872.492 - 0.003ms returns 0
TA260 002:872.496 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:872.500 - 0.003ms returns 0x0000002E
TA260 002:872.504 JLINK_Go()
TA260 002:872.511   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:875.242 - 2.737ms 
TA260 002:875.252 JLINK_IsHalted()
TA260 002:875.786 - 0.534ms returns FALSE
TA260 002:875.792 JLINK_HasError()
TA260 002:877.194 JLINK_IsHalted()
TA260 002:877.810 - 0.615ms returns FALSE
TA260 002:877.820 JLINK_HasError()
TA260 002:879.185 JLINK_IsHalted()
TA260 002:881.490   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:882.015 - 2.829ms returns TRUE
TA260 002:882.022 JLINK_ReadReg(R15 (PC))
TA260 002:882.026 - 0.004ms returns 0x20000000
TA260 002:882.030 JLINK_ClrBPEx(BPHandle = 0x0000002E)
TA260 002:882.034 - 0.003ms returns 0x00
TA260 002:882.038 JLINK_ReadReg(R0)
TA260 002:882.042 - 0.003ms returns 0x00000000
TA260 002:882.356 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:882.363   Data:  8B 62 BD BE D5 D7 6D 3F 4A 1D BE BE 93 B2 6D 3F ...
TA260 002:882.372   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:884.959 - 2.602ms returns 0x27C
TA260 002:884.973 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:884.977   Data:  CF A6 60 3F 93 30 F6 BE 89 76 60 3F CB E0 F6 BE ...
TA260 002:884.985   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:886.882 - 1.908ms returns 0x184
TA260 002:886.904 JLINK_HasError()
TA260 002:886.944 JLINK_WriteReg(R0, 0x08008C00)
TA260 002:886.952 - 0.008ms returns 0
TA260 002:886.957 JLINK_WriteReg(R1, 0x00000400)
TA260 002:886.961 - 0.003ms returns 0
TA260 002:886.965 JLINK_WriteReg(R2, 0x20000184)
TA260 002:886.969 - 0.003ms returns 0
TA260 002:886.973 JLINK_WriteReg(R3, 0x00000000)
TA260 002:886.976 - 0.003ms returns 0
TA260 002:886.980 JLINK_WriteReg(R4, 0x00000000)
TA260 002:886.984 - 0.003ms returns 0
TA260 002:886.988 JLINK_WriteReg(R5, 0x00000000)
TA260 002:886.991 - 0.003ms returns 0
TA260 002:886.995 JLINK_WriteReg(R6, 0x00000000)
TA260 002:886.999 - 0.003ms returns 0
TA260 002:887.002 JLINK_WriteReg(R7, 0x00000000)
TA260 002:887.006 - 0.003ms returns 0
TA260 002:887.010 JLINK_WriteReg(R8, 0x00000000)
TA260 002:887.014 - 0.003ms returns 0
TA260 002:887.018 JLINK_WriteReg(R9, 0x20000180)
TA260 002:887.022 - 0.003ms returns 0
TA260 002:887.026 JLINK_WriteReg(R10, 0x00000000)
TA260 002:887.029 - 0.003ms returns 0
TA260 002:887.033 JLINK_WriteReg(R11, 0x00000000)
TA260 002:887.036 - 0.003ms returns 0
TA260 002:887.040 JLINK_WriteReg(R12, 0x00000000)
TA260 002:887.044 - 0.004ms returns 0
TA260 002:887.049 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:887.052 - 0.004ms returns 0
TA260 002:887.056 JLINK_WriteReg(R14, 0x20000001)
TA260 002:887.060 - 0.003ms returns 0
TA260 002:887.064 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:887.068 - 0.003ms returns 0
TA260 002:887.072 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:887.076 - 0.003ms returns 0
TA260 002:887.080 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:887.083 - 0.003ms returns 0
TA260 002:887.087 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:887.090 - 0.003ms returns 0
TA260 002:887.095 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:887.098 - 0.003ms returns 0
TA260 002:887.103 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:887.107 - 0.004ms returns 0x0000002F
TA260 002:887.111 JLINK_Go()
TA260 002:887.121   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:889.882 - 2.770ms 
TA260 002:889.899 JLINK_IsHalted()
TA260 002:890.345 - 0.445ms returns FALSE
TA260 002:890.352 JLINK_HasError()
TA260 002:892.711 JLINK_IsHalted()
TA260 002:893.218 - 0.507ms returns FALSE
TA260 002:893.227 JLINK_HasError()
TA260 002:894.702 JLINK_IsHalted()
TA260 002:897.004   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:897.503 - 2.800ms returns TRUE
TA260 002:897.511 JLINK_ReadReg(R15 (PC))
TA260 002:897.517 - 0.006ms returns 0x20000000
TA260 002:897.522 JLINK_ClrBPEx(BPHandle = 0x0000002F)
TA260 002:897.526 - 0.004ms returns 0x00
TA260 002:897.531 JLINK_ReadReg(R0)
TA260 002:897.534 - 0.003ms returns 0x00000000
TA260 002:897.915 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:897.928   Data:  1C 46 0B BF A7 CC 56 3F 6B 9A 0B BF E5 95 56 3F ...
TA260 002:897.939   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:900.651 - 2.735ms returns 0x27C
TA260 002:900.665 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:900.669   Data:  37 63 44 3F 2C 85 24 BF AB 22 44 3F 25 D2 24 BF ...
TA260 002:900.679   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:902.648 - 1.982ms returns 0x184
TA260 002:902.660 JLINK_HasError()
TA260 002:902.666 JLINK_WriteReg(R0, 0x08009000)
TA260 002:902.672 - 0.006ms returns 0
TA260 002:902.676 JLINK_WriteReg(R1, 0x00000400)
TA260 002:902.680 - 0.003ms returns 0
TA260 002:902.684 JLINK_WriteReg(R2, 0x20000184)
TA260 002:902.688 - 0.004ms returns 0
TA260 002:902.692 JLINK_WriteReg(R3, 0x00000000)
TA260 002:902.696 - 0.003ms returns 0
TA260 002:902.700 JLINK_WriteReg(R4, 0x00000000)
TA260 002:902.703 - 0.003ms returns 0
TA260 002:902.707 JLINK_WriteReg(R5, 0x00000000)
TA260 002:902.710 - 0.003ms returns 0
TA260 002:902.714 JLINK_WriteReg(R6, 0x00000000)
TA260 002:902.718 - 0.003ms returns 0
TA260 002:902.722 JLINK_WriteReg(R7, 0x00000000)
TA260 002:902.725 - 0.003ms returns 0
TA260 002:902.729 JLINK_WriteReg(R8, 0x00000000)
TA260 002:902.733 - 0.003ms returns 0
TA260 002:902.737 JLINK_WriteReg(R9, 0x20000180)
TA260 002:902.740 - 0.003ms returns 0
TA260 002:902.744 JLINK_WriteReg(R10, 0x00000000)
TA260 002:902.748 - 0.003ms returns 0
TA260 002:902.752 JLINK_WriteReg(R11, 0x00000000)
TA260 002:902.755 - 0.003ms returns 0
TA260 002:902.759 JLINK_WriteReg(R12, 0x00000000)
TA260 002:902.762 - 0.003ms returns 0
TA260 002:902.766 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:902.770 - 0.004ms returns 0
TA260 002:902.774 JLINK_WriteReg(R14, 0x20000001)
TA260 002:902.778 - 0.003ms returns 0
TA260 002:902.782 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:902.786 - 0.003ms returns 0
TA260 002:902.790 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:902.794 - 0.003ms returns 0
TA260 002:902.798 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:902.801 - 0.003ms returns 0
TA260 002:902.805 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:902.808 - 0.003ms returns 0
TA260 002:902.828 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:902.831 - 0.003ms returns 0
TA260 002:902.836 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:902.841 - 0.005ms returns 0x00000030
TA260 002:902.845 JLINK_Go()
TA260 002:902.855   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:905.651 - 2.806ms 
TA260 002:905.663 JLINK_IsHalted()
TA260 002:906.160 - 0.497ms returns FALSE
TA260 002:906.167 JLINK_HasError()
TA260 002:907.209 JLINK_IsHalted()
TA260 002:907.686 - 0.477ms returns FALSE
TA260 002:907.694 JLINK_HasError()
TA260 002:909.215 JLINK_IsHalted()
TA260 002:909.690 - 0.474ms returns FALSE
TA260 002:909.698 JLINK_HasError()
TA260 002:911.713 JLINK_IsHalted()
TA260 002:914.114   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:914.569 - 2.856ms returns TRUE
TA260 002:914.584 JLINK_ReadReg(R15 (PC))
TA260 002:914.590 - 0.005ms returns 0x20000000
TA260 002:914.629 JLINK_ClrBPEx(BPHandle = 0x00000030)
TA260 002:914.634 - 0.005ms returns 0x00
TA260 002:914.639 JLINK_ReadReg(R0)
TA260 002:914.642 - 0.003ms returns 0x00000000
TA260 002:915.156 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:915.169   Data:  C7 80 32 BF 4A 80 37 3F C9 C8 32 BF 23 3A 37 3F ...
TA260 002:915.244   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:917.935 - 2.778ms returns 0x27C
TA260 002:917.958 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:917.962   Data:  93 93 20 3F 84 9F 47 BF 3B 45 20 3F 65 DE 47 BF ...
TA260 002:917.975   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:919.880 - 1.921ms returns 0x184
TA260 002:919.899 JLINK_HasError()
TA260 002:919.904 JLINK_WriteReg(R0, 0x08009400)
TA260 002:919.911 - 0.006ms returns 0
TA260 002:919.916 JLINK_WriteReg(R1, 0x00000400)
TA260 002:919.919 - 0.003ms returns 0
TA260 002:919.924 JLINK_WriteReg(R2, 0x20000184)
TA260 002:919.927 - 0.003ms returns 0
TA260 002:919.931 JLINK_WriteReg(R3, 0x00000000)
TA260 002:919.935 - 0.003ms returns 0
TA260 002:919.939 JLINK_WriteReg(R4, 0x00000000)
TA260 002:919.942 - 0.003ms returns 0
TA260 002:919.946 JLINK_WriteReg(R5, 0x00000000)
TA260 002:919.950 - 0.003ms returns 0
TA260 002:919.954 JLINK_WriteReg(R6, 0x00000000)
TA260 002:919.957 - 0.003ms returns 0
TA260 002:919.961 JLINK_WriteReg(R7, 0x00000000)
TA260 002:919.964 - 0.003ms returns 0
TA260 002:919.968 JLINK_WriteReg(R8, 0x00000000)
TA260 002:919.972 - 0.003ms returns 0
TA260 002:919.976 JLINK_WriteReg(R9, 0x20000180)
TA260 002:919.979 - 0.003ms returns 0
TA260 002:919.983 JLINK_WriteReg(R10, 0x00000000)
TA260 002:919.987 - 0.003ms returns 0
TA260 002:919.991 JLINK_WriteReg(R11, 0x00000000)
TA260 002:919.994 - 0.003ms returns 0
TA260 002:919.998 JLINK_WriteReg(R12, 0x00000000)
TA260 002:920.002 - 0.003ms returns 0
TA260 002:920.006 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:920.010 - 0.004ms returns 0
TA260 002:920.014 JLINK_WriteReg(R14, 0x20000001)
TA260 002:920.017 - 0.003ms returns 0
TA260 002:920.021 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:920.025 - 0.003ms returns 0
TA260 002:920.029 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:920.032 - 0.003ms returns 0
TA260 002:920.036 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:920.040 - 0.003ms returns 0
TA260 002:920.044 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:920.047 - 0.003ms returns 0
TA260 002:920.051 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:920.054 - 0.003ms returns 0
TA260 002:920.060 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:920.064 - 0.005ms returns 0x00000031
TA260 002:920.068 JLINK_Go()
TA260 002:920.077   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:922.878 - 2.809ms 
TA260 002:922.895 JLINK_IsHalted()
TA260 002:923.396 - 0.501ms returns FALSE
TA260 002:923.409 JLINK_HasError()
TA260 002:925.227 JLINK_IsHalted()
TA260 002:925.792 - 0.564ms returns FALSE
TA260 002:925.802 JLINK_HasError()
TA260 002:927.228 JLINK_IsHalted()
TA260 002:929.680   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:930.178 - 2.949ms returns TRUE
TA260 002:930.187 JLINK_ReadReg(R15 (PC))
TA260 002:930.193 - 0.005ms returns 0x20000000
TA260 002:930.197 JLINK_ClrBPEx(BPHandle = 0x00000031)
TA260 002:930.201 - 0.003ms returns 0x00
TA260 002:930.206 JLINK_ReadReg(R0)
TA260 002:930.209 - 0.003ms returns 0x00000000
TA260 002:930.626 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:930.636   Data:  59 DF 52 BF A7 26 11 3F 49 18 53 BF CD D3 10 3F ...
TA260 002:930.647   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:933.256 - 2.630ms returns 0x27C
TA260 002:933.272 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:933.276   Data:  63 30 ED 3E FC 0D 63 BF 21 7E EC 3E 5A 3C 63 BF ...
TA260 002:933.287   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:935.205 - 1.932ms returns 0x184
TA260 002:935.217 JLINK_HasError()
TA260 002:935.223 JLINK_WriteReg(R0, 0x08009800)
TA260 002:935.228 - 0.005ms returns 0
TA260 002:935.232 JLINK_WriteReg(R1, 0x00000400)
TA260 002:935.236 - 0.003ms returns 0
TA260 002:935.241 JLINK_WriteReg(R2, 0x20000184)
TA260 002:935.244 - 0.003ms returns 0
TA260 002:935.248 JLINK_WriteReg(R3, 0x00000000)
TA260 002:935.252 - 0.003ms returns 0
TA260 002:935.257 JLINK_WriteReg(R4, 0x00000000)
TA260 002:935.261 - 0.003ms returns 0
TA260 002:935.265 JLINK_WriteReg(R5, 0x00000000)
TA260 002:935.277 - 0.012ms returns 0
TA260 002:935.282 JLINK_WriteReg(R6, 0x00000000)
TA260 002:935.285 - 0.003ms returns 0
TA260 002:935.289 JLINK_WriteReg(R7, 0x00000000)
TA260 002:935.292 - 0.003ms returns 0
TA260 002:935.297 JLINK_WriteReg(R8, 0x00000000)
TA260 002:935.300 - 0.003ms returns 0
TA260 002:935.305 JLINK_WriteReg(R9, 0x20000180)
TA260 002:935.308 - 0.003ms returns 0
TA260 002:935.313 JLINK_WriteReg(R10, 0x00000000)
TA260 002:935.316 - 0.003ms returns 0
TA260 002:935.320 JLINK_WriteReg(R11, 0x00000000)
TA260 002:935.324 - 0.003ms returns 0
TA260 002:935.328 JLINK_WriteReg(R12, 0x00000000)
TA260 002:935.331 - 0.003ms returns 0
TA260 002:935.335 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:935.339 - 0.004ms returns 0
TA260 002:935.343 JLINK_WriteReg(R14, 0x20000001)
TA260 002:935.347 - 0.003ms returns 0
TA260 002:935.351 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:935.355 - 0.003ms returns 0
TA260 002:935.370 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:935.375 - 0.016ms returns 0
TA260 002:935.379 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:935.382 - 0.003ms returns 0
TA260 002:935.386 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:935.390 - 0.003ms returns 0
TA260 002:935.394 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:935.397 - 0.003ms returns 0
TA260 002:935.402 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:935.407 - 0.004ms returns 0x00000032
TA260 002:935.411 JLINK_Go()
TA260 002:935.419   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:938.107 - 2.696ms 
TA260 002:938.120 JLINK_IsHalted()
TA260 002:938.643 - 0.523ms returns FALSE
TA260 002:938.651 JLINK_HasError()
TA260 002:939.734 JLINK_IsHalted()
TA260 002:940.236 - 0.501ms returns FALSE
TA260 002:940.244 JLINK_HasError()
TA260 002:941.733 JLINK_IsHalted()
TA260 002:942.244 - 0.510ms returns FALSE
TA260 002:942.253 JLINK_HasError()
TA260 002:944.238 JLINK_IsHalted()
TA260 002:946.654   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:947.166 - 2.927ms returns TRUE
TA260 002:947.177 JLINK_ReadReg(R15 (PC))
TA260 002:947.182 - 0.005ms returns 0x20000000
TA260 002:947.186 JLINK_ClrBPEx(BPHandle = 0x00000032)
TA260 002:947.190 - 0.003ms returns 0x00
TA260 002:947.195 JLINK_ReadReg(R0)
TA260 002:947.198 - 0.003ms returns 0x00000000
TA260 002:947.774 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:947.788   Data:  5E 23 6B BF 10 72 CA 3E 0C 4B 6B BF 53 B9 C9 3E ...
TA260 002:947.800   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:950.394 - 2.620ms returns 0x27C
TA260 002:950.412 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:950.415   Data:  2C 1C 90 3E B3 C2 75 BF 32 5B 8F 3E C6 DE 75 BF ...
TA260 002:950.427   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:952.246 - 1.833ms returns 0x184
TA260 002:952.254 JLINK_HasError()
TA260 002:952.259 JLINK_WriteReg(R0, 0x08009C00)
TA260 002:952.264 - 0.005ms returns 0
TA260 002:952.268 JLINK_WriteReg(R1, 0x00000400)
TA260 002:952.272 - 0.003ms returns 0
TA260 002:952.277 JLINK_WriteReg(R2, 0x20000184)
TA260 002:952.280 - 0.003ms returns 0
TA260 002:952.284 JLINK_WriteReg(R3, 0x00000000)
TA260 002:952.288 - 0.003ms returns 0
TA260 002:952.292 JLINK_WriteReg(R4, 0x00000000)
TA260 002:952.295 - 0.003ms returns 0
TA260 002:952.299 JLINK_WriteReg(R5, 0x00000000)
TA260 002:952.303 - 0.003ms returns 0
TA260 002:952.307 JLINK_WriteReg(R6, 0x00000000)
TA260 002:952.310 - 0.003ms returns 0
TA260 002:952.314 JLINK_WriteReg(R7, 0x00000000)
TA260 002:952.317 - 0.003ms returns 0
TA260 002:952.321 JLINK_WriteReg(R8, 0x00000000)
TA260 002:952.325 - 0.003ms returns 0
TA260 002:952.329 JLINK_WriteReg(R9, 0x20000180)
TA260 002:952.332 - 0.003ms returns 0
TA260 002:952.336 JLINK_WriteReg(R10, 0x00000000)
TA260 002:952.339 - 0.003ms returns 0
TA260 002:952.343 JLINK_WriteReg(R11, 0x00000000)
TA260 002:952.347 - 0.003ms returns 0
TA260 002:952.351 JLINK_WriteReg(R12, 0x00000000)
TA260 002:952.354 - 0.003ms returns 0
TA260 002:952.358 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:952.362 - 0.004ms returns 0
TA260 002:952.370 JLINK_WriteReg(R14, 0x20000001)
TA260 002:952.376 - 0.005ms returns 0
TA260 002:952.380 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:952.383 - 0.003ms returns 0
TA260 002:952.387 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:952.391 - 0.003ms returns 0
TA260 002:952.395 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:952.398 - 0.003ms returns 0
TA260 002:952.402 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:952.406 - 0.003ms returns 0
TA260 002:952.410 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:952.413 - 0.003ms returns 0
TA260 002:952.418 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:952.422 - 0.004ms returns 0x00000033
TA260 002:952.426 JLINK_Go()
TA260 002:952.434   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:955.222 - 2.795ms 
TA260 002:955.232 JLINK_IsHalted()
TA260 002:955.778 - 0.545ms returns FALSE
TA260 002:955.785 JLINK_HasError()
TA260 002:957.743 JLINK_IsHalted()
TA260 002:958.235 - 0.492ms returns FALSE
TA260 002:958.243 JLINK_HasError()
TA260 002:959.743 JLINK_IsHalted()
TA260 002:962.132   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:962.646 - 2.902ms returns TRUE
TA260 002:962.653 JLINK_ReadReg(R15 (PC))
TA260 002:962.658 - 0.004ms returns 0x20000000
TA260 002:962.663 JLINK_ClrBPEx(BPHandle = 0x00000033)
TA260 002:962.667 - 0.004ms returns 0x00
TA260 002:962.671 JLINK_ReadReg(R0)
TA260 002:962.675 - 0.003ms returns 0x00000000
TA260 002:963.155 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:963.164   Data:  1C 5E 7A BF 58 9E 55 3E 02 73 7A BF 01 15 54 3E ...
TA260 002:963.177   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:965.743 - 2.587ms returns 0x27C
TA260 002:965.765 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:965.770   Data:  DA F8 B5 3D A4 05 7F BF BB D7 B2 3D 58 0E 7F BF ...
TA260 002:965.782   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:967.648 - 1.882ms returns 0x184
TA260 002:967.702 JLINK_HasError()
TA260 002:967.709 JLINK_WriteReg(R0, 0x0800A000)
TA260 002:967.716 - 0.006ms returns 0
TA260 002:967.720 JLINK_WriteReg(R1, 0x00000400)
TA260 002:967.724 - 0.003ms returns 0
TA260 002:967.728 JLINK_WriteReg(R2, 0x20000184)
TA260 002:967.732 - 0.003ms returns 0
TA260 002:967.736 JLINK_WriteReg(R3, 0x00000000)
TA260 002:967.740 - 0.004ms returns 0
TA260 002:967.744 JLINK_WriteReg(R4, 0x00000000)
TA260 002:967.747 - 0.003ms returns 0
TA260 002:967.751 JLINK_WriteReg(R5, 0x00000000)
TA260 002:967.754 - 0.003ms returns 0
TA260 002:967.758 JLINK_WriteReg(R6, 0x00000000)
TA260 002:967.762 - 0.003ms returns 0
TA260 002:967.766 JLINK_WriteReg(R7, 0x00000000)
TA260 002:967.769 - 0.003ms returns 0
TA260 002:967.774 JLINK_WriteReg(R8, 0x00000000)
TA260 002:967.777 - 0.003ms returns 0
TA260 002:967.781 JLINK_WriteReg(R9, 0x20000180)
TA260 002:967.784 - 0.003ms returns 0
TA260 002:967.788 JLINK_WriteReg(R10, 0x00000000)
TA260 002:967.792 - 0.003ms returns 0
TA260 002:967.796 JLINK_WriteReg(R11, 0x00000000)
TA260 002:967.799 - 0.003ms returns 0
TA260 002:967.803 JLINK_WriteReg(R12, 0x00000000)
TA260 002:967.807 - 0.003ms returns 0
TA260 002:967.811 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:967.815 - 0.004ms returns 0
TA260 002:967.819 JLINK_WriteReg(R14, 0x20000001)
TA260 002:967.822 - 0.003ms returns 0
TA260 002:967.826 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:967.830 - 0.003ms returns 0
TA260 002:967.834 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:967.838 - 0.003ms returns 0
TA260 002:967.842 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:967.845 - 0.003ms returns 0
TA260 002:967.850 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:967.853 - 0.003ms returns 0
TA260 002:967.857 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:967.860 - 0.003ms returns 0
TA260 002:967.865 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:967.870 - 0.005ms returns 0x00000034
TA260 002:967.874 JLINK_Go()
TA260 002:967.883   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:970.649 - 2.774ms 
TA260 002:970.668 JLINK_IsHalted()
TA260 002:971.163 - 0.495ms returns FALSE
TA260 002:971.171 JLINK_HasError()
TA260 002:972.752 JLINK_IsHalted()
TA260 002:973.196 - 0.443ms returns FALSE
TA260 002:973.203 JLINK_HasError()
TA260 002:974.757 JLINK_IsHalted()
TA260 002:977.049   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:977.526 - 2.769ms returns TRUE
TA260 002:977.533 JLINK_ReadReg(R15 (PC))
TA260 002:977.538 - 0.005ms returns 0x20000000
TA260 002:977.554 JLINK_ClrBPEx(BPHandle = 0x00000034)
TA260 002:977.558 - 0.004ms returns 0x00
TA260 002:977.562 JLINK_ReadReg(R0)
TA260 002:977.566 - 0.003ms returns 0x00000000
TA260 002:977.948 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:977.956   Data:  C1 F9 7F BF 00 30 62 3C 11 FB 7F BF 90 0E 49 3C ...
TA260 002:977.968   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:980.656 - 2.708ms returns 0x27C
TA260 002:980.671 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:980.675   Data:  37 7D DB BD B1 7B 7E BF C4 9C DE BD B0 70 7E BF ...
TA260 002:980.688   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:982.653 - 1.981ms returns 0x184
TA260 002:982.663 JLINK_HasError()
TA260 002:982.668 JLINK_WriteReg(R0, 0x0800A400)
TA260 002:982.674 - 0.005ms returns 0
TA260 002:982.678 JLINK_WriteReg(R1, 0x00000400)
TA260 002:982.681 - 0.003ms returns 0
TA260 002:982.686 JLINK_WriteReg(R2, 0x20000184)
TA260 002:982.689 - 0.003ms returns 0
TA260 002:982.694 JLINK_WriteReg(R3, 0x00000000)
TA260 002:982.697 - 0.003ms returns 0
TA260 002:982.705 JLINK_WriteReg(R4, 0x00000000)
TA260 002:982.710 - 0.004ms returns 0
TA260 002:982.715 JLINK_WriteReg(R5, 0x00000000)
TA260 002:982.718 - 0.003ms returns 0
TA260 002:982.722 JLINK_WriteReg(R6, 0x00000000)
TA260 002:982.726 - 0.003ms returns 0
TA260 002:982.730 JLINK_WriteReg(R7, 0x00000000)
TA260 002:982.733 - 0.003ms returns 0
TA260 002:982.738 JLINK_WriteReg(R8, 0x00000000)
TA260 002:982.742 - 0.004ms returns 0
TA260 002:982.746 JLINK_WriteReg(R9, 0x20000180)
TA260 002:982.750 - 0.003ms returns 0
TA260 002:982.754 JLINK_WriteReg(R10, 0x00000000)
TA260 002:982.758 - 0.003ms returns 0
TA260 002:982.762 JLINK_WriteReg(R11, 0x00000000)
TA260 002:982.765 - 0.003ms returns 0
TA260 002:982.769 JLINK_WriteReg(R12, 0x00000000)
TA260 002:982.773 - 0.003ms returns 0
TA260 002:982.777 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:982.781 - 0.004ms returns 0
TA260 002:982.786 JLINK_WriteReg(R14, 0x20000001)
TA260 002:982.789 - 0.003ms returns 0
TA260 002:982.793 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:982.797 - 0.003ms returns 0
TA260 002:982.802 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:982.805 - 0.003ms returns 0
TA260 002:982.809 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:982.826 - 0.017ms returns 0
TA260 002:982.831 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:982.835 - 0.003ms returns 0
TA260 002:982.839 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:982.843 - 0.003ms returns 0
TA260 002:982.847 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:982.852 - 0.004ms returns 0x00000035
TA260 002:982.856 JLINK_Go()
TA260 002:982.866   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 002:985.642 - 2.786ms 
TA260 002:985.653 JLINK_IsHalted()
TA260 002:986.108 - 0.455ms returns FALSE
TA260 002:986.115 JLINK_HasError()
TA260 002:987.767 JLINK_IsHalted()
TA260 002:988.230 - 0.463ms returns FALSE
TA260 002:988.236 JLINK_HasError()
TA260 002:989.768 JLINK_IsHalted()
TA260 002:992.047   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:992.526 - 2.758ms returns TRUE
TA260 002:992.549 JLINK_ReadReg(R15 (PC))
TA260 002:992.555 - 0.005ms returns 0x20000000
TA260 002:992.589 JLINK_ClrBPEx(BPHandle = 0x00000035)
TA260 002:992.594 - 0.005ms returns 0x00
TA260 002:992.599 JLINK_ReadReg(R0)
TA260 002:992.603 - 0.004ms returns 0x00000000
TA260 002:993.021 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:993.030   Data:  20 BF 7B BF 6C E3 39 BE CD AC 7B BF CF 6E 3B BE ...
TA260 002:993.041   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:995.656 - 2.634ms returns 0x27C
TA260 002:995.673 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:995.677   Data:  FE 20 99 BE 27 2A 74 BF CE E0 99 BE DD 0B 74 BF ...
TA260 002:995.738   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:997.654 - 1.980ms returns 0x184
TA260 002:997.668 JLINK_HasError()
TA260 002:997.675 JLINK_WriteReg(R0, 0x0800A800)
TA260 002:997.683 - 0.007ms returns 0
TA260 002:997.687 JLINK_WriteReg(R1, 0x00000400)
TA260 002:997.691 - 0.003ms returns 0
TA260 002:997.695 JLINK_WriteReg(R2, 0x20000184)
TA260 002:997.698 - 0.003ms returns 0
TA260 002:997.703 JLINK_WriteReg(R3, 0x00000000)
TA260 002:997.706 - 0.003ms returns 0
TA260 002:997.710 JLINK_WriteReg(R4, 0x00000000)
TA260 002:997.714 - 0.003ms returns 0
TA260 002:997.718 JLINK_WriteReg(R5, 0x00000000)
TA260 002:997.722 - 0.003ms returns 0
TA260 002:997.726 JLINK_WriteReg(R6, 0x00000000)
TA260 002:997.730 - 0.003ms returns 0
TA260 002:997.734 JLINK_WriteReg(R7, 0x00000000)
TA260 002:997.738 - 0.003ms returns 0
TA260 002:997.742 JLINK_WriteReg(R8, 0x00000000)
TA260 002:997.745 - 0.003ms returns 0
TA260 002:997.750 JLINK_WriteReg(R9, 0x20000180)
TA260 002:997.753 - 0.003ms returns 0
TA260 002:997.757 JLINK_WriteReg(R10, 0x00000000)
TA260 002:997.760 - 0.003ms returns 0
TA260 002:997.765 JLINK_WriteReg(R11, 0x00000000)
TA260 002:997.768 - 0.003ms returns 0
TA260 002:997.772 JLINK_WriteReg(R12, 0x00000000)
TA260 002:997.776 - 0.003ms returns 0
TA260 002:997.780 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:997.784 - 0.004ms returns 0
TA260 002:997.788 JLINK_WriteReg(R14, 0x20000001)
TA260 002:997.792 - 0.003ms returns 0
TA260 002:997.796 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:997.800 - 0.004ms returns 0
TA260 002:997.804 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:997.808 - 0.003ms returns 0
TA260 002:997.812 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:997.816 - 0.003ms returns 0
TA260 002:997.820 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:997.823 - 0.003ms returns 0
TA260 002:997.827 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:997.830 - 0.003ms returns 0
TA260 002:997.836 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:997.840 - 0.004ms returns 0x00000036
TA260 002:997.844 JLINK_Go()
TA260 002:997.854   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:000.651 - 2.806ms 
TA260 003:000.667 JLINK_IsHalted()
TA260 003:001.186 - 0.518ms returns FALSE
TA260 003:001.192 JLINK_HasError()
TA260 003:005.793 JLINK_IsHalted()
TA260 003:008.192   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:008.740 - 2.946ms returns TRUE
TA260 003:008.763 JLINK_ReadReg(R15 (PC))
TA260 003:008.769 - 0.006ms returns 0x20000000
TA260 003:008.774 JLINK_ClrBPEx(BPHandle = 0x00000036)
TA260 003:008.778 - 0.004ms returns 0x00
TA260 003:008.782 JLINK_ReadReg(R0)
TA260 003:008.787 - 0.004ms returns 0x00000000
TA260 003:009.200 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:009.209   Data:  D5 D7 6D BF 8B 62 BD BE 93 B2 6D BF 4A 1D BE BE ...
TA260 003:009.221   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:011.843 - 2.641ms returns 0x27C
TA260 003:011.862 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:011.866   Data:  35 80 F5 BE 89 76 60 BF 93 30 F6 BE 21 46 60 BF ...
TA260 003:011.879   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:013.740 - 1.877ms returns 0x184
TA260 003:013.751 JLINK_HasError()
TA260 003:013.757 JLINK_WriteReg(R0, 0x0800AC00)
TA260 003:013.763 - 0.005ms returns 0
TA260 003:013.767 JLINK_WriteReg(R1, 0x00000400)
TA260 003:013.770 - 0.003ms returns 0
TA260 003:013.775 JLINK_WriteReg(R2, 0x20000184)
TA260 003:013.778 - 0.003ms returns 0
TA260 003:013.782 JLINK_WriteReg(R3, 0x00000000)
TA260 003:013.786 - 0.003ms returns 0
TA260 003:013.790 JLINK_WriteReg(R4, 0x00000000)
TA260 003:013.793 - 0.003ms returns 0
TA260 003:013.797 JLINK_WriteReg(R5, 0x00000000)
TA260 003:013.800 - 0.003ms returns 0
TA260 003:013.804 JLINK_WriteReg(R6, 0x00000000)
TA260 003:013.808 - 0.003ms returns 0
TA260 003:013.812 JLINK_WriteReg(R7, 0x00000000)
TA260 003:013.815 - 0.003ms returns 0
TA260 003:013.820 JLINK_WriteReg(R8, 0x00000000)
TA260 003:013.823 - 0.003ms returns 0
TA260 003:013.827 JLINK_WriteReg(R9, 0x20000180)
TA260 003:013.835 - 0.007ms returns 0
TA260 003:013.840 JLINK_WriteReg(R10, 0x00000000)
TA260 003:013.843 - 0.003ms returns 0
TA260 003:013.913 JLINK_WriteReg(R11, 0x00000000)
TA260 003:013.917 - 0.003ms returns 0
TA260 003:013.921 JLINK_WriteReg(R12, 0x00000000)
TA260 003:013.925 - 0.003ms returns 0
TA260 003:013.929 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:013.933 - 0.004ms returns 0
TA260 003:013.937 JLINK_WriteReg(R14, 0x20000001)
TA260 003:013.941 - 0.003ms returns 0
TA260 003:013.945 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:013.948 - 0.003ms returns 0
TA260 003:013.953 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:013.956 - 0.003ms returns 0
TA260 003:013.960 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:013.964 - 0.003ms returns 0
TA260 003:013.968 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:013.971 - 0.003ms returns 0
TA260 003:013.975 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:013.978 - 0.003ms returns 0
TA260 003:013.983 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:013.988 - 0.004ms returns 0x00000037
TA260 003:013.992 JLINK_Go()
TA260 003:014.000   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:016.731 - 2.738ms 
TA260 003:016.745 JLINK_IsHalted()
TA260 003:017.248 - 0.502ms returns FALSE
TA260 003:017.258 JLINK_HasError()
TA260 003:019.302 JLINK_IsHalted()
TA260 003:019.793 - 0.491ms returns FALSE
TA260 003:019.803 JLINK_HasError()
TA260 003:021.802 JLINK_IsHalted()
TA260 003:024.162   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:024.644 - 2.841ms returns TRUE
TA260 003:024.654 JLINK_ReadReg(R15 (PC))
TA260 003:024.660 - 0.005ms returns 0x20000000
TA260 003:024.665 JLINK_ClrBPEx(BPHandle = 0x00000037)
TA260 003:024.669 - 0.003ms returns 0x00
TA260 003:024.673 JLINK_ReadReg(R0)
TA260 003:024.677 - 0.003ms returns 0x00000000
TA260 003:025.220 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:025.234   Data:  A7 CC 56 BF 1C 46 0B BF E5 95 56 BF 6B 9A 0B BF ...
TA260 003:025.247   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:027.931 - 2.710ms returns 0x27C
TA260 003:027.950 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:027.955   Data:  1A 38 24 BF AB 22 44 BF 2C 85 24 BF 00 E2 43 BF ...
TA260 003:027.968   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:029.794 - 1.843ms returns 0x184
TA260 003:029.809 JLINK_HasError()
TA260 003:029.814 JLINK_WriteReg(R0, 0x0800B000)
TA260 003:029.820 - 0.005ms returns 0
TA260 003:029.824 JLINK_WriteReg(R1, 0x00000400)
TA260 003:029.828 - 0.003ms returns 0
TA260 003:029.832 JLINK_WriteReg(R2, 0x20000184)
TA260 003:029.836 - 0.003ms returns 0
TA260 003:029.840 JLINK_WriteReg(R3, 0x00000000)
TA260 003:029.843 - 0.003ms returns 0
TA260 003:029.847 JLINK_WriteReg(R4, 0x00000000)
TA260 003:029.851 - 0.003ms returns 0
TA260 003:029.855 JLINK_WriteReg(R5, 0x00000000)
TA260 003:029.858 - 0.003ms returns 0
TA260 003:029.862 JLINK_WriteReg(R6, 0x00000000)
TA260 003:029.866 - 0.003ms returns 0
TA260 003:029.870 JLINK_WriteReg(R7, 0x00000000)
TA260 003:029.873 - 0.003ms returns 0
TA260 003:029.877 JLINK_WriteReg(R8, 0x00000000)
TA260 003:029.881 - 0.003ms returns 0
TA260 003:029.886 JLINK_WriteReg(R9, 0x20000180)
TA260 003:029.891 - 0.005ms returns 0
TA260 003:029.895 JLINK_WriteReg(R10, 0x00000000)
TA260 003:029.898 - 0.003ms returns 0
TA260 003:029.902 JLINK_WriteReg(R11, 0x00000000)
TA260 003:029.906 - 0.003ms returns 0
TA260 003:029.910 JLINK_WriteReg(R12, 0x00000000)
TA260 003:029.913 - 0.003ms returns 0
TA260 003:029.917 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:029.921 - 0.004ms returns 0
TA260 003:029.926 JLINK_WriteReg(R14, 0x20000001)
TA260 003:029.929 - 0.003ms returns 0
TA260 003:029.933 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:029.936 - 0.003ms returns 0
TA260 003:029.941 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:029.944 - 0.003ms returns 0
TA260 003:029.948 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:029.952 - 0.003ms returns 0
TA260 003:029.956 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:029.959 - 0.003ms returns 0
TA260 003:029.963 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:029.971 - 0.007ms returns 0
TA260 003:029.976 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:029.984 - 0.008ms returns 0x00000038
TA260 003:029.988 JLINK_Go()
TA260 003:029.997   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:032.731 - 2.742ms 
TA260 003:032.740 JLINK_IsHalted()
TA260 003:033.229 - 0.489ms returns FALSE
TA260 003:033.238 JLINK_HasError()
TA260 003:034.306 JLINK_IsHalted()
TA260 003:034.778 - 0.472ms returns FALSE
TA260 003:034.786 JLINK_HasError()
TA260 003:036.304 JLINK_IsHalted()
TA260 003:036.775 - 0.470ms returns FALSE
TA260 003:036.782 JLINK_HasError()
TA260 003:038.308 JLINK_IsHalted()
TA260 003:040.671   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:041.174 - 2.866ms returns TRUE
TA260 003:041.182 JLINK_ReadReg(R15 (PC))
TA260 003:041.188 - 0.006ms returns 0x20000000
TA260 003:041.193 JLINK_ClrBPEx(BPHandle = 0x00000038)
TA260 003:041.197 - 0.003ms returns 0x00
TA260 003:041.202 JLINK_ReadReg(R0)
TA260 003:041.205 - 0.003ms returns 0x00000000
TA260 003:041.568 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:041.576   Data:  4A 80 37 BF C7 80 32 BF 23 3A 37 BF C9 C8 32 BF ...
TA260 003:041.588   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:044.210 - 2.641ms returns 0x27C
TA260 003:044.229 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:044.233   Data:  85 60 47 BF 3B 45 20 BF 84 9F 47 BF CB F6 1F BF ...
TA260 003:044.243   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:046.108 - 1.878ms returns 0x184
TA260 003:046.119 JLINK_HasError()
TA260 003:046.730 JLINK_WriteReg(R0, 0x0800B400)
TA260 003:046.737 - 0.007ms returns 0
TA260 003:046.742 JLINK_WriteReg(R1, 0x00000400)
TA260 003:046.745 - 0.003ms returns 0
TA260 003:046.749 JLINK_WriteReg(R2, 0x20000184)
TA260 003:046.753 - 0.003ms returns 0
TA260 003:046.757 JLINK_WriteReg(R3, 0x00000000)
TA260 003:046.761 - 0.003ms returns 0
TA260 003:046.765 JLINK_WriteReg(R4, 0x00000000)
TA260 003:046.768 - 0.003ms returns 0
TA260 003:046.773 JLINK_WriteReg(R5, 0x00000000)
TA260 003:046.777 - 0.003ms returns 0
TA260 003:046.781 JLINK_WriteReg(R6, 0x00000000)
TA260 003:046.784 - 0.003ms returns 0
TA260 003:046.800 JLINK_WriteReg(R7, 0x00000000)
TA260 003:046.804 - 0.015ms returns 0
TA260 003:046.809 JLINK_WriteReg(R8, 0x00000000)
TA260 003:046.812 - 0.003ms returns 0
TA260 003:046.816 JLINK_WriteReg(R9, 0x20000180)
TA260 003:046.820 - 0.003ms returns 0
TA260 003:046.824 JLINK_WriteReg(R10, 0x00000000)
TA260 003:046.827 - 0.003ms returns 0
TA260 003:046.831 JLINK_WriteReg(R11, 0x00000000)
TA260 003:046.835 - 0.003ms returns 0
TA260 003:046.839 JLINK_WriteReg(R12, 0x00000000)
TA260 003:046.842 - 0.003ms returns 0
TA260 003:046.846 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:046.850 - 0.004ms returns 0
TA260 003:046.855 JLINK_WriteReg(R14, 0x20000001)
TA260 003:046.858 - 0.003ms returns 0
TA260 003:046.863 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:046.866 - 0.004ms returns 0
TA260 003:046.870 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:046.874 - 0.003ms returns 0
TA260 003:046.878 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:046.882 - 0.003ms returns 0
TA260 003:046.886 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:046.890 - 0.004ms returns 0
TA260 003:046.894 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:046.897 - 0.003ms returns 0
TA260 003:046.902 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:046.907 - 0.005ms returns 0x00000039
TA260 003:046.911 JLINK_Go()
TA260 003:046.920   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:049.577 - 2.665ms 
TA260 003:049.596 JLINK_IsHalted()
TA260 003:050.092 - 0.496ms returns FALSE
TA260 003:050.099 JLINK_HasError()
TA260 003:051.813 JLINK_IsHalted()
TA260 003:052.277 - 0.464ms returns FALSE
TA260 003:052.284 JLINK_HasError()
TA260 003:053.321 JLINK_IsHalted()
TA260 003:053.811 - 0.489ms returns FALSE
TA260 003:053.818 JLINK_HasError()
TA260 003:055.318 JLINK_IsHalted()
TA260 003:057.782   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:058.303 - 2.984ms returns TRUE
TA260 003:058.312 JLINK_ReadReg(R15 (PC))
TA260 003:058.323 - 0.011ms returns 0x20000000
TA260 003:058.328 JLINK_ClrBPEx(BPHandle = 0x00000039)
TA260 003:058.332 - 0.004ms returns 0x00
TA260 003:058.337 JLINK_ReadReg(R0)
TA260 003:058.340 - 0.003ms returns 0x00000000
TA260 003:058.732 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:058.741   Data:  A7 26 11 BF 59 DF 52 BF CD D3 10 BF 49 18 53 BF ...
TA260 003:058.752   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:061.287 - 2.554ms returns 0x27C
TA260 003:061.303 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:061.307   Data:  7B DF 62 BF 21 7E EC BE FC 0D 63 BF BB CB EB BE ...
TA260 003:061.315   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:063.155 - 1.851ms returns 0x184
TA260 003:063.168 JLINK_HasError()
TA260 003:063.175 JLINK_WriteReg(R0, 0x0800B800)
TA260 003:063.182 - 0.007ms returns 0
TA260 003:063.187 JLINK_WriteReg(R1, 0x00000400)
TA260 003:063.192 - 0.004ms returns 0
TA260 003:063.197 JLINK_WriteReg(R2, 0x20000184)
TA260 003:063.201 - 0.004ms returns 0
TA260 003:063.206 JLINK_WriteReg(R3, 0x00000000)
TA260 003:063.211 - 0.004ms returns 0
TA260 003:063.216 JLINK_WriteReg(R4, 0x00000000)
TA260 003:063.221 - 0.005ms returns 0
TA260 003:063.226 JLINK_WriteReg(R5, 0x00000000)
TA260 003:063.230 - 0.004ms returns 0
TA260 003:063.235 JLINK_WriteReg(R6, 0x00000000)
TA260 003:063.239 - 0.004ms returns 0
TA260 003:063.244 JLINK_WriteReg(R7, 0x00000000)
TA260 003:063.249 - 0.004ms returns 0
TA260 003:063.254 JLINK_WriteReg(R8, 0x00000000)
TA260 003:063.258 - 0.004ms returns 0
TA260 003:063.263 JLINK_WriteReg(R9, 0x20000180)
TA260 003:063.268 - 0.004ms returns 0
TA260 003:063.273 JLINK_WriteReg(R10, 0x00000000)
TA260 003:063.277 - 0.004ms returns 0
TA260 003:063.282 JLINK_WriteReg(R11, 0x00000000)
TA260 003:063.286 - 0.004ms returns 0
TA260 003:063.292 JLINK_WriteReg(R12, 0x00000000)
TA260 003:063.296 - 0.004ms returns 0
TA260 003:063.301 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:063.306 - 0.005ms returns 0
TA260 003:063.311 JLINK_WriteReg(R14, 0x20000001)
TA260 003:063.315 - 0.004ms returns 0
TA260 003:063.321 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:063.325 - 0.004ms returns 0
TA260 003:063.330 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:063.335 - 0.005ms returns 0
TA260 003:063.339 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:063.343 - 0.003ms returns 0
TA260 003:063.347 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:063.351 - 0.003ms returns 0
TA260 003:063.355 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:063.358 - 0.003ms returns 0
TA260 003:063.363 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:063.367 - 0.004ms returns 0x0000003A
TA260 003:063.372 JLINK_Go()
TA260 003:063.380   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:066.129 - 2.756ms 
TA260 003:066.141 JLINK_IsHalted()
TA260 003:066.663 - 0.521ms returns FALSE
TA260 003:066.683 JLINK_HasError()
TA260 003:067.826 JLINK_IsHalted()
TA260 003:068.314 - 0.487ms returns FALSE
TA260 003:068.323 JLINK_HasError()
TA260 003:069.830 JLINK_IsHalted()
TA260 003:070.289 - 0.458ms returns FALSE
TA260 003:070.297 JLINK_HasError()
TA260 003:071.826 JLINK_IsHalted()
TA260 003:074.154   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:074.721 - 2.894ms returns TRUE
TA260 003:074.730 JLINK_ReadReg(R15 (PC))
TA260 003:074.735 - 0.006ms returns 0x20000000
TA260 003:074.741 JLINK_ClrBPEx(BPHandle = 0x0000003A)
TA260 003:074.744 - 0.004ms returns 0x00
TA260 003:074.749 JLINK_ReadReg(R0)
TA260 003:074.753 - 0.003ms returns 0x00000000
TA260 003:075.114 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:075.123   Data:  10 72 CA BE 5E 23 6B BF 53 B9 C9 BE 0C 4B 6B BF ...
TA260 003:075.134   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:077.768 - 2.654ms returns 0x27C
TA260 003:077.787 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:077.791   Data:  7B A6 75 BF 32 5B 8F BE B3 C2 75 BF 22 9A 8E BE ...
TA260 003:077.804   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:079.709 - 1.921ms returns 0x184
TA260 003:079.722 JLINK_HasError()
TA260 003:079.733 JLINK_WriteReg(R0, 0x0800BC00)
TA260 003:079.739 - 0.006ms returns 0
TA260 003:079.743 JLINK_WriteReg(R1, 0x00000400)
TA260 003:079.746 - 0.003ms returns 0
TA260 003:079.750 JLINK_WriteReg(R2, 0x20000184)
TA260 003:079.754 - 0.003ms returns 0
TA260 003:079.758 JLINK_WriteReg(R3, 0x00000000)
TA260 003:079.761 - 0.003ms returns 0
TA260 003:079.766 JLINK_WriteReg(R4, 0x00000000)
TA260 003:079.770 - 0.004ms returns 0
TA260 003:079.774 JLINK_WriteReg(R5, 0x00000000)
TA260 003:079.778 - 0.003ms returns 0
TA260 003:079.782 JLINK_WriteReg(R6, 0x00000000)
TA260 003:079.785 - 0.003ms returns 0
TA260 003:079.789 JLINK_WriteReg(R7, 0x00000000)
TA260 003:079.793 - 0.004ms returns 0
TA260 003:079.797 JLINK_WriteReg(R8, 0x00000000)
TA260 003:079.800 - 0.003ms returns 0
TA260 003:079.805 JLINK_WriteReg(R9, 0x20000180)
TA260 003:079.808 - 0.003ms returns 0
TA260 003:079.812 JLINK_WriteReg(R10, 0x00000000)
TA260 003:079.816 - 0.003ms returns 0
TA260 003:079.820 JLINK_WriteReg(R11, 0x00000000)
TA260 003:079.823 - 0.003ms returns 0
TA260 003:079.827 JLINK_WriteReg(R12, 0x00000000)
TA260 003:079.830 - 0.003ms returns 0
TA260 003:079.835 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:079.839 - 0.004ms returns 0
TA260 003:079.843 JLINK_WriteReg(R14, 0x20000001)
TA260 003:079.846 - 0.003ms returns 0
TA260 003:079.851 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:079.854 - 0.003ms returns 0
TA260 003:079.858 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:079.862 - 0.003ms returns 0
TA260 003:079.866 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:079.869 - 0.003ms returns 0
TA260 003:079.873 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:079.877 - 0.003ms returns 0
TA260 003:079.881 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:079.885 - 0.003ms returns 0
TA260 003:079.890 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:079.894 - 0.005ms returns 0x0000003B
TA260 003:079.899 JLINK_Go()
TA260 003:079.908   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:082.649 - 2.750ms 
TA260 003:082.661 JLINK_IsHalted()
TA260 003:083.140 - 0.479ms returns FALSE
TA260 003:083.149 JLINK_HasError()
TA260 003:084.341 JLINK_IsHalted()
TA260 003:084.793 - 0.451ms returns FALSE
TA260 003:084.799 JLINK_HasError()
TA260 003:086.341 JLINK_IsHalted()
TA260 003:086.824 - 0.482ms returns FALSE
TA260 003:086.833 JLINK_HasError()
TA260 003:088.343 JLINK_IsHalted()
TA260 003:090.675   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:091.142 - 2.798ms returns TRUE
TA260 003:091.149 JLINK_ReadReg(R15 (PC))
TA260 003:091.154 - 0.005ms returns 0x20000000
TA260 003:091.159 JLINK_ClrBPEx(BPHandle = 0x0000003B)
TA260 003:091.163 - 0.004ms returns 0x00
TA260 003:091.168 JLINK_ReadReg(R0)
TA260 003:091.180 - 0.012ms returns 0x00000000
TA260 003:091.554 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:091.562   Data:  58 9E 55 BE 1C 5E 7A BF 01 15 54 BE 02 73 7A BF ...
TA260 003:091.573   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:094.205 - 2.650ms returns 0x27C
TA260 003:094.216 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:094.220   Data:  C9 FC 7E BF BB D7 B2 BD A4 05 7F BF 80 B6 AF BD ...
TA260 003:094.230   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:096.203 - 1.986ms returns 0x184
TA260 003:096.256 JLINK_HasError()
TA260 003:096.264 JLINK_WriteReg(R0, 0x0800C000)
TA260 003:096.270 - 0.005ms returns 0
TA260 003:096.274 JLINK_WriteReg(R1, 0x00000400)
TA260 003:096.277 - 0.003ms returns 0
TA260 003:096.281 JLINK_WriteReg(R2, 0x20000184)
TA260 003:096.285 - 0.003ms returns 0
TA260 003:096.289 JLINK_WriteReg(R3, 0x00000000)
TA260 003:096.292 - 0.003ms returns 0
TA260 003:096.296 JLINK_WriteReg(R4, 0x00000000)
TA260 003:096.300 - 0.003ms returns 0
TA260 003:096.304 JLINK_WriteReg(R5, 0x00000000)
TA260 003:096.307 - 0.003ms returns 0
TA260 003:096.311 JLINK_WriteReg(R6, 0x00000000)
TA260 003:096.315 - 0.003ms returns 0
TA260 003:096.319 JLINK_WriteReg(R7, 0x00000000)
TA260 003:096.322 - 0.003ms returns 0
TA260 003:096.326 JLINK_WriteReg(R8, 0x00000000)
TA260 003:096.330 - 0.003ms returns 0
TA260 003:096.336 JLINK_WriteReg(R9, 0x20000180)
TA260 003:096.340 - 0.003ms returns 0
TA260 003:096.344 JLINK_WriteReg(R10, 0x00000000)
TA260 003:096.347 - 0.003ms returns 0
TA260 003:096.351 JLINK_WriteReg(R11, 0x00000000)
TA260 003:096.355 - 0.003ms returns 0
TA260 003:096.359 JLINK_WriteReg(R12, 0x00000000)
TA260 003:096.362 - 0.003ms returns 0
TA260 003:096.366 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:096.370 - 0.004ms returns 0
TA260 003:096.374 JLINK_WriteReg(R14, 0x20000001)
TA260 003:096.378 - 0.003ms returns 0
TA260 003:096.382 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:096.385 - 0.003ms returns 0
TA260 003:096.389 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:096.393 - 0.003ms returns 0
TA260 003:096.397 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:096.400 - 0.003ms returns 0
TA260 003:096.404 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:096.408 - 0.003ms returns 0
TA260 003:096.412 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:096.415 - 0.003ms returns 0
TA260 003:096.420 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:096.425 - 0.005ms returns 0x0000003C
TA260 003:096.429 JLINK_Go()
TA260 003:096.437   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:099.230 - 2.800ms 
TA260 003:099.249 JLINK_IsHalted()
TA260 003:099.825 - 0.576ms returns FALSE
TA260 003:099.833 JLINK_HasError()
TA260 003:101.853 JLINK_IsHalted()
TA260 003:102.413 - 0.559ms returns FALSE
TA260 003:102.421 JLINK_HasError()
TA260 003:104.358 JLINK_IsHalted()
TA260 003:106.720   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:107.258 - 2.900ms returns TRUE
TA260 003:107.268 JLINK_ReadReg(R15 (PC))
TA260 003:107.274 - 0.006ms returns 0x20000000
TA260 003:107.279 JLINK_ClrBPEx(BPHandle = 0x0000003C)
TA260 003:107.283 - 0.003ms returns 0x00
TA260 003:107.288 JLINK_ReadReg(R0)
TA260 003:107.291 - 0.003ms returns 0x00000000
TA260 003:107.715 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:107.726   Data:  00 30 62 BC C1 F9 7F BF 90 0E 49 BC 11 FB 7F BF ...
TA260 003:107.738   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:110.331 - 2.616ms returns 0x27C
TA260 003:110.342 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:110.346   Data:  8B 86 7E BF C4 9C DE 3D B1 7B 7E BF 2E BC E1 3D ...
TA260 003:110.355   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:112.246 - 1.903ms returns 0x184
TA260 003:112.254 JLINK_HasError()
TA260 003:112.259 JLINK_WriteReg(R0, 0x0800C400)
TA260 003:112.264 - 0.005ms returns 0
TA260 003:112.270 JLINK_WriteReg(R1, 0x00000400)
TA260 003:112.273 - 0.003ms returns 0
TA260 003:112.278 JLINK_WriteReg(R2, 0x20000184)
TA260 003:112.281 - 0.003ms returns 0
TA260 003:112.285 JLINK_WriteReg(R3, 0x00000000)
TA260 003:112.289 - 0.003ms returns 0
TA260 003:112.293 JLINK_WriteReg(R4, 0x00000000)
TA260 003:112.296 - 0.003ms returns 0
TA260 003:112.300 JLINK_WriteReg(R5, 0x00000000)
TA260 003:112.303 - 0.003ms returns 0
TA260 003:112.308 JLINK_WriteReg(R6, 0x00000000)
TA260 003:112.311 - 0.003ms returns 0
TA260 003:112.315 JLINK_WriteReg(R7, 0x00000000)
TA260 003:112.319 - 0.003ms returns 0
TA260 003:112.323 JLINK_WriteReg(R8, 0x00000000)
TA260 003:112.331 - 0.008ms returns 0
TA260 003:112.336 JLINK_WriteReg(R9, 0x20000180)
TA260 003:112.340 - 0.004ms returns 0
TA260 003:112.345 JLINK_WriteReg(R10, 0x00000000)
TA260 003:112.348 - 0.003ms returns 0
TA260 003:112.352 JLINK_WriteReg(R11, 0x00000000)
TA260 003:112.355 - 0.003ms returns 0
TA260 003:112.360 JLINK_WriteReg(R12, 0x00000000)
TA260 003:112.364 - 0.004ms returns 0
TA260 003:112.368 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:112.372 - 0.004ms returns 0
TA260 003:112.376 JLINK_WriteReg(R14, 0x20000001)
TA260 003:112.380 - 0.003ms returns 0
TA260 003:112.384 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:112.387 - 0.003ms returns 0
TA260 003:112.391 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:112.395 - 0.003ms returns 0
TA260 003:112.399 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:112.402 - 0.003ms returns 0
TA260 003:112.406 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:112.413 - 0.007ms returns 0
TA260 003:112.418 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:112.421 - 0.003ms returns 0
TA260 003:112.426 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:112.430 - 0.005ms returns 0x0000003D
TA260 003:112.434 JLINK_Go()
TA260 003:112.443   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:115.254 - 2.819ms 
TA260 003:115.268 JLINK_IsHalted()
TA260 003:115.789 - 0.521ms returns FALSE
TA260 003:115.797 JLINK_HasError()
TA260 003:116.864 JLINK_IsHalted()
TA260 003:117.425 - 0.560ms returns FALSE
TA260 003:117.437 JLINK_HasError()
TA260 003:118.513 JLINK_IsHalted()
TA260 003:119.028 - 0.515ms returns FALSE
TA260 003:119.038 JLINK_HasError()
TA260 003:120.517 JLINK_IsHalted()
TA260 003:122.826   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:123.312 - 2.794ms returns TRUE
TA260 003:123.327 JLINK_ReadReg(R15 (PC))
TA260 003:123.332 - 0.005ms returns 0x20000000
TA260 003:123.368 JLINK_ClrBPEx(BPHandle = 0x0000003D)
TA260 003:123.374 - 0.006ms returns 0x00
TA260 003:123.379 JLINK_ReadReg(R0)
TA260 003:123.382 - 0.003ms returns 0x00000000
TA260 003:123.758 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:123.768   Data:  6C E3 39 3E 20 BF 7B BF CF 6E 3B 3E CD AC 7B BF ...
TA260 003:123.779   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:126.382 - 2.623ms returns 0x27C
TA260 003:126.393 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:126.397   Data:  4B 48 74 BF CE E0 99 3E 27 2A 74 BF 86 A0 9A 3E ...
TA260 003:126.406   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:128.256 - 1.863ms returns 0x184
TA260 003:128.267 JLINK_HasError()
TA260 003:128.272 JLINK_WriteReg(R0, 0x0800C800)
TA260 003:128.278 - 0.005ms returns 0
TA260 003:128.282 JLINK_WriteReg(R1, 0x00000400)
TA260 003:128.286 - 0.003ms returns 0
TA260 003:128.290 JLINK_WriteReg(R2, 0x20000184)
TA260 003:128.294 - 0.003ms returns 0
TA260 003:128.298 JLINK_WriteReg(R3, 0x00000000)
TA260 003:128.301 - 0.003ms returns 0
TA260 003:128.305 JLINK_WriteReg(R4, 0x00000000)
TA260 003:128.309 - 0.003ms returns 0
TA260 003:128.313 JLINK_WriteReg(R5, 0x00000000)
TA260 003:128.316 - 0.003ms returns 0
TA260 003:128.320 JLINK_WriteReg(R6, 0x00000000)
TA260 003:128.324 - 0.003ms returns 0
TA260 003:128.328 JLINK_WriteReg(R7, 0x00000000)
TA260 003:128.332 - 0.003ms returns 0
TA260 003:128.336 JLINK_WriteReg(R8, 0x00000000)
TA260 003:128.340 - 0.003ms returns 0
TA260 003:128.344 JLINK_WriteReg(R9, 0x20000180)
TA260 003:128.347 - 0.003ms returns 0
TA260 003:128.351 JLINK_WriteReg(R10, 0x00000000)
TA260 003:128.354 - 0.003ms returns 0
TA260 003:128.358 JLINK_WriteReg(R11, 0x00000000)
TA260 003:128.362 - 0.003ms returns 0
TA260 003:128.366 JLINK_WriteReg(R12, 0x00000000)
TA260 003:128.369 - 0.003ms returns 0
TA260 003:128.373 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:128.377 - 0.004ms returns 0
TA260 003:128.382 JLINK_WriteReg(R14, 0x20000001)
TA260 003:128.385 - 0.003ms returns 0
TA260 003:128.390 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:128.433 - 0.043ms returns 0
TA260 003:128.438 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:128.441 - 0.003ms returns 0
TA260 003:128.445 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:128.449 - 0.003ms returns 0
TA260 003:128.453 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:128.456 - 0.003ms returns 0
TA260 003:128.460 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:128.464 - 0.003ms returns 0
TA260 003:128.469 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:128.474 - 0.005ms returns 0x0000003E
TA260 003:128.486 JLINK_Go()
TA260 003:128.495   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:131.254 - 2.767ms 
TA260 003:131.262 JLINK_IsHalted()
TA260 003:131.788 - 0.525ms returns FALSE
TA260 003:131.794 JLINK_HasError()
TA260 003:133.526 JLINK_IsHalted()
TA260 003:134.026 - 0.500ms returns FALSE
TA260 003:134.035 JLINK_HasError()
TA260 003:135.531 JLINK_IsHalted()
TA260 003:137.813   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:138.302 - 2.771ms returns TRUE
TA260 003:138.313 JLINK_ReadReg(R15 (PC))
TA260 003:138.319 - 0.006ms returns 0x20000000
TA260 003:138.328 JLINK_ClrBPEx(BPHandle = 0x0000003E)
TA260 003:138.332 - 0.003ms returns 0x00
TA260 003:138.336 JLINK_ReadReg(R0)
TA260 003:138.340 - 0.003ms returns 0x00000000
TA260 003:138.766 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:138.777   Data:  8B 62 BD 3E D5 D7 6D BF 4A 1D BE 3E 93 B2 6D BF ...
TA260 003:138.800   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:141.472 - 2.705ms returns 0x27C
TA260 003:141.486 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:141.489   Data:  CF A6 60 BF 93 30 F6 3E 89 76 60 BF CB E0 F6 3E ...
TA260 003:141.498   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:143.384 - 1.897ms returns 0x184
TA260 003:143.393 JLINK_HasError()
TA260 003:143.399 JLINK_WriteReg(R0, 0x0800CC00)
TA260 003:143.404 - 0.005ms returns 0
TA260 003:143.408 JLINK_WriteReg(R1, 0x00000400)
TA260 003:143.412 - 0.003ms returns 0
TA260 003:143.416 JLINK_WriteReg(R2, 0x20000184)
TA260 003:143.419 - 0.003ms returns 0
TA260 003:143.423 JLINK_WriteReg(R3, 0x00000000)
TA260 003:143.427 - 0.003ms returns 0
TA260 003:143.431 JLINK_WriteReg(R4, 0x00000000)
TA260 003:143.434 - 0.003ms returns 0
TA260 003:143.438 JLINK_WriteReg(R5, 0x00000000)
TA260 003:143.442 - 0.003ms returns 0
TA260 003:143.446 JLINK_WriteReg(R6, 0x00000000)
TA260 003:143.449 - 0.003ms returns 0
TA260 003:143.453 JLINK_WriteReg(R7, 0x00000000)
TA260 003:143.457 - 0.003ms returns 0
TA260 003:143.461 JLINK_WriteReg(R8, 0x00000000)
TA260 003:143.464 - 0.003ms returns 0
TA260 003:143.469 JLINK_WriteReg(R9, 0x20000180)
TA260 003:143.472 - 0.003ms returns 0
TA260 003:143.476 JLINK_WriteReg(R10, 0x00000000)
TA260 003:143.480 - 0.003ms returns 0
TA260 003:143.484 JLINK_WriteReg(R11, 0x00000000)
TA260 003:143.487 - 0.003ms returns 0
TA260 003:143.491 JLINK_WriteReg(R12, 0x00000000)
TA260 003:143.495 - 0.003ms returns 0
TA260 003:143.499 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:143.503 - 0.003ms returns 0
TA260 003:143.507 JLINK_WriteReg(R14, 0x20000001)
TA260 003:143.510 - 0.003ms returns 0
TA260 003:143.514 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:143.517 - 0.003ms returns 0
TA260 003:143.522 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:143.525 - 0.003ms returns 0
TA260 003:143.529 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:143.532 - 0.003ms returns 0
TA260 003:143.536 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:143.540 - 0.003ms returns 0
TA260 003:143.544 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:143.547 - 0.003ms returns 0
TA260 003:143.552 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:143.556 - 0.004ms returns 0x0000003F
TA260 003:143.560 JLINK_Go()
TA260 003:143.570   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:146.310 - 2.749ms 
TA260 003:146.317 JLINK_IsHalted()
TA260 003:146.790 - 0.472ms returns FALSE
TA260 003:146.797 JLINK_HasError()
TA260 003:148.035 JLINK_IsHalted()
TA260 003:148.588 - 0.552ms returns FALSE
TA260 003:148.603 JLINK_HasError()
TA260 003:150.039 JLINK_IsHalted()
TA260 003:150.550 - 0.510ms returns FALSE
TA260 003:150.562 JLINK_HasError()
TA260 003:152.036 JLINK_IsHalted()
TA260 003:154.418   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:154.873 - 2.836ms returns TRUE
TA260 003:154.884 JLINK_ReadReg(R15 (PC))
TA260 003:154.889 - 0.005ms returns 0x20000000
TA260 003:154.894 JLINK_ClrBPEx(BPHandle = 0x0000003F)
TA260 003:154.898 - 0.003ms returns 0x00
TA260 003:154.902 JLINK_ReadReg(R0)
TA260 003:154.906 - 0.003ms returns 0x00000000
TA260 003:155.304 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:155.317   Data:  1C 46 0B 3F A7 CC 56 BF 6B 9A 0B 3F E5 95 56 BF ...
TA260 003:155.329   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:157.950 - 2.644ms returns 0x27C
TA260 003:157.967 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:157.971   Data:  37 63 44 BF 2C 85 24 3F AB 22 44 BF 25 D2 24 3F ...
TA260 003:157.984   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:159.876 - 1.908ms returns 0x184
TA260 003:159.893 JLINK_HasError()
TA260 003:159.899 JLINK_WriteReg(R0, 0x0800D000)
TA260 003:159.922 - 0.011ms returns 0
TA260 003:159.927 JLINK_WriteReg(R1, 0x00000400)
TA260 003:159.931 - 0.003ms returns 0
TA260 003:159.935 JLINK_WriteReg(R2, 0x20000184)
TA260 003:159.939 - 0.003ms returns 0
TA260 003:159.943 JLINK_WriteReg(R3, 0x00000000)
TA260 003:159.946 - 0.003ms returns 0
TA260 003:159.950 JLINK_WriteReg(R4, 0x00000000)
TA260 003:159.954 - 0.003ms returns 0
TA260 003:159.958 JLINK_WriteReg(R5, 0x00000000)
TA260 003:159.961 - 0.003ms returns 0
TA260 003:159.966 JLINK_WriteReg(R6, 0x00000000)
TA260 003:159.969 - 0.003ms returns 0
TA260 003:159.973 JLINK_WriteReg(R7, 0x00000000)
TA260 003:159.977 - 0.003ms returns 0
TA260 003:159.981 JLINK_WriteReg(R8, 0x00000000)
TA260 003:159.985 - 0.003ms returns 0
TA260 003:159.989 JLINK_WriteReg(R9, 0x20000180)
TA260 003:159.992 - 0.003ms returns 0
TA260 003:159.996 JLINK_WriteReg(R10, 0x00000000)
TA260 003:160.000 - 0.003ms returns 0
TA260 003:160.004 JLINK_WriteReg(R11, 0x00000000)
TA260 003:160.007 - 0.003ms returns 0
TA260 003:160.012 JLINK_WriteReg(R12, 0x00000000)
TA260 003:160.015 - 0.003ms returns 0
TA260 003:160.020 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:160.024 - 0.004ms returns 0
TA260 003:160.028 JLINK_WriteReg(R14, 0x20000001)
TA260 003:160.032 - 0.003ms returns 0
TA260 003:160.036 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:160.039 - 0.003ms returns 0
TA260 003:160.043 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:160.047 - 0.003ms returns 0
TA260 003:160.051 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:160.054 - 0.003ms returns 0
TA260 003:160.165 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:160.169 - 0.003ms returns 0
TA260 003:160.173 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:160.176 - 0.003ms returns 0
TA260 003:160.182 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:160.187 - 0.005ms returns 0x00000040
TA260 003:160.191 JLINK_Go()
TA260 003:160.201   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:162.915 - 2.723ms 
TA260 003:162.930 JLINK_IsHalted()
TA260 003:163.424 - 0.494ms returns FALSE
TA260 003:163.431 JLINK_HasError()
TA260 003:165.049 JLINK_IsHalted()
TA260 003:165.540 - 0.490ms returns FALSE
TA260 003:165.551 JLINK_HasError()
TA260 003:167.048 JLINK_IsHalted()
TA260 003:169.419   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:169.960 - 2.911ms returns TRUE
TA260 003:169.968 JLINK_ReadReg(R15 (PC))
TA260 003:169.974 - 0.006ms returns 0x20000000
TA260 003:169.979 JLINK_ClrBPEx(BPHandle = 0x00000040)
TA260 003:169.983 - 0.003ms returns 0x00
TA260 003:169.987 JLINK_ReadReg(R0)
TA260 003:169.991 - 0.003ms returns 0x00000000
TA260 003:170.424 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:170.432   Data:  C7 80 32 3F 4A 80 37 BF C9 C8 32 3F 23 3A 37 BF ...
TA260 003:170.443   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:173.030 - 2.606ms returns 0x27C
TA260 003:173.051 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:173.056   Data:  93 93 20 BF 84 9F 47 3F 3B 45 20 BF 65 DE 47 3F ...
TA260 003:173.069   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:174.923 - 1.871ms returns 0x184
TA260 003:174.942 JLINK_HasError()
TA260 003:174.983 JLINK_WriteReg(R0, 0x0800D400)
TA260 003:174.990 - 0.007ms returns 0
TA260 003:174.995 JLINK_WriteReg(R1, 0x00000400)
TA260 003:174.999 - 0.003ms returns 0
TA260 003:175.003 JLINK_WriteReg(R2, 0x20000184)
TA260 003:175.007 - 0.004ms returns 0
TA260 003:175.011 JLINK_WriteReg(R3, 0x00000000)
TA260 003:175.014 - 0.003ms returns 0
TA260 003:175.019 JLINK_WriteReg(R4, 0x00000000)
TA260 003:175.022 - 0.003ms returns 0
TA260 003:175.026 JLINK_WriteReg(R5, 0x00000000)
TA260 003:175.030 - 0.003ms returns 0
TA260 003:175.034 JLINK_WriteReg(R6, 0x00000000)
TA260 003:175.037 - 0.003ms returns 0
TA260 003:175.041 JLINK_WriteReg(R7, 0x00000000)
TA260 003:175.044 - 0.003ms returns 0
TA260 003:175.048 JLINK_WriteReg(R8, 0x00000000)
TA260 003:175.062 - 0.013ms returns 0
TA260 003:175.067 JLINK_WriteReg(R9, 0x20000180)
TA260 003:175.071 - 0.003ms returns 0
TA260 003:175.075 JLINK_WriteReg(R10, 0x00000000)
TA260 003:175.083 - 0.008ms returns 0
TA260 003:175.087 JLINK_WriteReg(R11, 0x00000000)
TA260 003:175.091 - 0.003ms returns 0
TA260 003:175.095 JLINK_WriteReg(R12, 0x00000000)
TA260 003:175.098 - 0.003ms returns 0
TA260 003:175.102 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:175.106 - 0.004ms returns 0
TA260 003:175.110 JLINK_WriteReg(R14, 0x20000001)
TA260 003:175.114 - 0.003ms returns 0
TA260 003:175.118 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:175.121 - 0.003ms returns 0
TA260 003:175.126 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:175.129 - 0.003ms returns 0
TA260 003:175.133 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:175.136 - 0.003ms returns 0
TA260 003:175.141 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:175.144 - 0.003ms returns 0
TA260 003:175.148 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:175.152 - 0.003ms returns 0
TA260 003:175.157 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:175.162 - 0.004ms returns 0x00000041
TA260 003:175.167 JLINK_Go()
TA260 003:175.176   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:177.930 - 2.762ms 
TA260 003:177.950 JLINK_IsHalted()
TA260 003:178.439 - 0.488ms returns FALSE
TA260 003:178.449 JLINK_HasError()
TA260 003:179.557 JLINK_IsHalted()
TA260 003:180.076 - 0.518ms returns FALSE
TA260 003:180.083 JLINK_HasError()
TA260 003:182.060 JLINK_IsHalted()
TA260 003:184.417   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:184.940 - 2.880ms returns TRUE
TA260 003:184.950 JLINK_ReadReg(R15 (PC))
TA260 003:184.955 - 0.005ms returns 0x20000000
TA260 003:184.960 JLINK_ClrBPEx(BPHandle = 0x00000041)
TA260 003:184.964 - 0.003ms returns 0x00
TA260 003:184.968 JLINK_ReadReg(R0)
TA260 003:184.972 - 0.004ms returns 0x00000000
TA260 003:185.359 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:185.370   Data:  59 DF 52 3F A7 26 11 BF 49 18 53 3F CD D3 10 BF ...
TA260 003:185.381   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:187.970 - 2.611ms returns 0x27C
TA260 003:187.986 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:187.990   Data:  63 30 ED BE FC 0D 63 3F 21 7E EC BE 5A 3C 63 3F ...
TA260 003:188.001   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:189.876 - 1.890ms returns 0x184
TA260 003:189.886 JLINK_HasError()
TA260 003:189.893 JLINK_WriteReg(R0, 0x0800D800)
TA260 003:189.898 - 0.005ms returns 0
TA260 003:189.902 JLINK_WriteReg(R1, 0x00000400)
TA260 003:189.906 - 0.003ms returns 0
TA260 003:189.910 JLINK_WriteReg(R2, 0x20000184)
TA260 003:189.913 - 0.003ms returns 0
TA260 003:189.917 JLINK_WriteReg(R3, 0x00000000)
TA260 003:189.921 - 0.003ms returns 0
TA260 003:189.925 JLINK_WriteReg(R4, 0x00000000)
TA260 003:189.928 - 0.003ms returns 0
TA260 003:189.932 JLINK_WriteReg(R5, 0x00000000)
TA260 003:189.936 - 0.003ms returns 0
TA260 003:189.940 JLINK_WriteReg(R6, 0x00000000)
TA260 003:189.943 - 0.003ms returns 0
TA260 003:189.947 JLINK_WriteReg(R7, 0x00000000)
TA260 003:189.950 - 0.003ms returns 0
TA260 003:189.954 JLINK_WriteReg(R8, 0x00000000)
TA260 003:189.958 - 0.003ms returns 0
TA260 003:189.962 JLINK_WriteReg(R9, 0x20000180)
TA260 003:189.965 - 0.003ms returns 0
TA260 003:189.969 JLINK_WriteReg(R10, 0x00000000)
TA260 003:189.973 - 0.003ms returns 0
TA260 003:189.977 JLINK_WriteReg(R11, 0x00000000)
TA260 003:189.981 - 0.003ms returns 0
TA260 003:189.985 JLINK_WriteReg(R12, 0x00000000)
TA260 003:189.988 - 0.003ms returns 0
TA260 003:189.993 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:189.996 - 0.004ms returns 0
TA260 003:190.000 JLINK_WriteReg(R14, 0x20000001)
TA260 003:190.004 - 0.003ms returns 0
TA260 003:190.008 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:190.012 - 0.003ms returns 0
TA260 003:190.016 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:190.019 - 0.003ms returns 0
TA260 003:190.023 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:190.026 - 0.003ms returns 0
TA260 003:190.030 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:190.034 - 0.003ms returns 0
TA260 003:190.038 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:190.041 - 0.003ms returns 0
TA260 003:190.046 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:190.057 - 0.010ms returns 0x00000042
TA260 003:190.061 JLINK_Go()
TA260 003:190.070   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:192.790 - 2.728ms 
TA260 003:192.800 JLINK_IsHalted()
TA260 003:193.276 - 0.476ms returns FALSE
TA260 003:193.282 JLINK_HasError()
TA260 003:195.072 JLINK_IsHalted()
TA260 003:195.500 - 0.427ms returns FALSE
TA260 003:195.507 JLINK_HasError()
TA260 003:197.068 JLINK_IsHalted()
TA260 003:199.423   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:199.917 - 2.848ms returns TRUE
TA260 003:199.925 JLINK_ReadReg(R15 (PC))
TA260 003:199.930 - 0.005ms returns 0x20000000
TA260 003:199.935 JLINK_ClrBPEx(BPHandle = 0x00000042)
TA260 003:199.939 - 0.003ms returns 0x00
TA260 003:199.943 JLINK_ReadReg(R0)
TA260 003:199.947 - 0.003ms returns 0x00000000
TA260 003:200.353 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:200.363   Data:  5E 23 6B 3F 10 72 CA BE 0C 4B 6B 3F 53 B9 C9 BE ...
TA260 003:200.373   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:202.966 - 2.612ms returns 0x27C
TA260 003:202.977 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:202.981   Data:  2C 1C 90 BE B3 C2 75 3F 32 5B 8F BE C6 DE 75 3F ...
TA260 003:202.990   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:204.878 - 1.901ms returns 0x184
TA260 003:204.890 JLINK_HasError()
TA260 003:204.896 JLINK_WriteReg(R0, 0x0800DC00)
TA260 003:204.901 - 0.005ms returns 0
TA260 003:204.906 JLINK_WriteReg(R1, 0x00000400)
TA260 003:204.909 - 0.003ms returns 0
TA260 003:204.913 JLINK_WriteReg(R2, 0x20000184)
TA260 003:204.917 - 0.003ms returns 0
TA260 003:204.921 JLINK_WriteReg(R3, 0x00000000)
TA260 003:204.924 - 0.003ms returns 0
TA260 003:204.928 JLINK_WriteReg(R4, 0x00000000)
TA260 003:204.932 - 0.003ms returns 0
TA260 003:204.936 JLINK_WriteReg(R5, 0x00000000)
TA260 003:204.939 - 0.003ms returns 0
TA260 003:204.943 JLINK_WriteReg(R6, 0x00000000)
TA260 003:204.947 - 0.003ms returns 0
TA260 003:204.951 JLINK_WriteReg(R7, 0x00000000)
TA260 003:204.954 - 0.003ms returns 0
TA260 003:204.958 JLINK_WriteReg(R8, 0x00000000)
TA260 003:204.962 - 0.003ms returns 0
TA260 003:204.966 JLINK_WriteReg(R9, 0x20000180)
TA260 003:204.969 - 0.003ms returns 0
TA260 003:204.973 JLINK_WriteReg(R10, 0x00000000)
TA260 003:204.976 - 0.003ms returns 0
TA260 003:204.980 JLINK_WriteReg(R11, 0x00000000)
TA260 003:204.984 - 0.003ms returns 0
TA260 003:204.988 JLINK_WriteReg(R12, 0x00000000)
TA260 003:204.992 - 0.003ms returns 0
TA260 003:204.996 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:205.000 - 0.004ms returns 0
TA260 003:205.004 JLINK_WriteReg(R14, 0x20000001)
TA260 003:205.007 - 0.003ms returns 0
TA260 003:205.012 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:205.015 - 0.003ms returns 0
TA260 003:205.019 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:205.023 - 0.003ms returns 0
TA260 003:205.028 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:205.032 - 0.004ms returns 0
TA260 003:205.036 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:205.039 - 0.003ms returns 0
TA260 003:205.043 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:205.047 - 0.003ms returns 0
TA260 003:205.051 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:205.056 - 0.004ms returns 0x00000043
TA260 003:205.060 JLINK_Go()
TA260 003:205.068   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:207.733 - 2.673ms 
TA260 003:207.752 JLINK_IsHalted()
TA260 003:208.198 - 0.446ms returns FALSE
TA260 003:208.205 JLINK_HasError()
TA260 003:209.576 JLINK_IsHalted()
TA260 003:210.039 - 0.462ms returns FALSE
TA260 003:210.048 JLINK_HasError()
TA260 003:212.083 JLINK_IsHalted()
TA260 003:214.508   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:215.030 - 2.946ms returns TRUE
TA260 003:215.038 JLINK_ReadReg(R15 (PC))
TA260 003:215.045 - 0.006ms returns 0x20000000
TA260 003:215.049 JLINK_ClrBPEx(BPHandle = 0x00000043)
TA260 003:215.053 - 0.004ms returns 0x00
TA260 003:215.058 JLINK_ReadReg(R0)
TA260 003:215.061 - 0.003ms returns 0x00000000
TA260 003:215.446 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:215.459   Data:  1C 5E 7A 3F 58 9E 55 BE 02 73 7A 3F 01 15 54 BE ...
TA260 003:215.471   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:218.082 - 2.635ms returns 0x27C
TA260 003:218.099 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:218.103   Data:  DA F8 B5 BD A4 05 7F 3F BB D7 B2 BD 58 0E 7F 3F ...
TA260 003:218.114   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:220.024 - 1.925ms returns 0x184
TA260 003:220.035 JLINK_HasError()
TA260 003:220.040 JLINK_WriteReg(R0, 0x0800E000)
TA260 003:220.046 - 0.006ms returns 0
TA260 003:220.051 JLINK_WriteReg(R1, 0x00000400)
TA260 003:220.054 - 0.003ms returns 0
TA260 003:220.058 JLINK_WriteReg(R2, 0x20000184)
TA260 003:220.062 - 0.003ms returns 0
TA260 003:220.066 JLINK_WriteReg(R3, 0x00000000)
TA260 003:220.070 - 0.003ms returns 0
TA260 003:220.074 JLINK_WriteReg(R4, 0x00000000)
TA260 003:220.077 - 0.003ms returns 0
TA260 003:220.081 JLINK_WriteReg(R5, 0x00000000)
TA260 003:220.085 - 0.003ms returns 0
TA260 003:220.089 JLINK_WriteReg(R6, 0x00000000)
TA260 003:220.092 - 0.003ms returns 0
TA260 003:220.096 JLINK_WriteReg(R7, 0x00000000)
TA260 003:220.100 - 0.003ms returns 0
TA260 003:220.104 JLINK_WriteReg(R8, 0x00000000)
TA260 003:220.107 - 0.003ms returns 0
TA260 003:220.111 JLINK_WriteReg(R9, 0x20000180)
TA260 003:220.115 - 0.003ms returns 0
TA260 003:220.119 JLINK_WriteReg(R10, 0x00000000)
TA260 003:220.122 - 0.003ms returns 0
TA260 003:220.126 JLINK_WriteReg(R11, 0x00000000)
TA260 003:220.130 - 0.003ms returns 0
TA260 003:220.134 JLINK_WriteReg(R12, 0x00000000)
TA260 003:220.137 - 0.003ms returns 0
TA260 003:220.142 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:220.146 - 0.004ms returns 0
TA260 003:220.150 JLINK_WriteReg(R14, 0x20000001)
TA260 003:220.153 - 0.003ms returns 0
TA260 003:220.157 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:220.160 - 0.003ms returns 0
TA260 003:220.165 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:220.168 - 0.003ms returns 0
TA260 003:220.172 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:220.175 - 0.003ms returns 0
TA260 003:220.180 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:220.183 - 0.003ms returns 0
TA260 003:220.187 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:220.190 - 0.003ms returns 0
TA260 003:220.195 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:220.199 - 0.004ms returns 0x00000044
TA260 003:220.203 JLINK_Go()
TA260 003:220.211   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:222.961 - 2.757ms 
TA260 003:222.970 JLINK_IsHalted()
TA260 003:223.467 - 0.497ms returns FALSE
TA260 003:223.473 JLINK_HasError()
TA260 003:225.596 JLINK_IsHalted()
TA260 003:226.092 - 0.494ms returns FALSE
TA260 003:226.104 JLINK_HasError()
TA260 003:227.589 JLINK_IsHalted()
TA260 003:229.985   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:230.431 - 2.840ms returns TRUE
TA260 003:230.439 JLINK_ReadReg(R15 (PC))
TA260 003:230.444 - 0.005ms returns 0x20000000
TA260 003:230.449 JLINK_ClrBPEx(BPHandle = 0x00000044)
TA260 003:230.453 - 0.003ms returns 0x00
TA260 003:230.457 JLINK_ReadReg(R0)
TA260 003:230.461 - 0.003ms returns 0x00000000
TA260 003:230.931 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:230.942   Data:  C1 F9 7F 3F 00 30 62 BC 11 FB 7F 3F 90 0E 49 BC ...
TA260 003:230.953   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:233.511 - 2.579ms returns 0x27C
TA260 003:233.527 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:233.530   Data:  FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF ...
TA260 003:233.540   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:235.432 - 1.905ms returns 0x184
TA260 003:235.442 JLINK_HasError()
TA260 003:235.447 JLINK_WriteReg(R0, 0x0800E400)
TA260 003:235.453 - 0.005ms returns 0
TA260 003:235.457 JLINK_WriteReg(R1, 0x00000118)
TA260 003:235.460 - 0.003ms returns 0
TA260 003:235.464 JLINK_WriteReg(R2, 0x20000184)
TA260 003:235.468 - 0.003ms returns 0
TA260 003:235.472 JLINK_WriteReg(R3, 0x00000000)
TA260 003:235.475 - 0.003ms returns 0
TA260 003:235.479 JLINK_WriteReg(R4, 0x00000000)
TA260 003:235.483 - 0.003ms returns 0
TA260 003:235.491 JLINK_WriteReg(R5, 0x00000000)
TA260 003:235.495 - 0.003ms returns 0
TA260 003:235.499 JLINK_WriteReg(R6, 0x00000000)
TA260 003:235.502 - 0.003ms returns 0
TA260 003:235.507 JLINK_WriteReg(R7, 0x00000000)
TA260 003:235.510 - 0.003ms returns 0
TA260 003:235.515 JLINK_WriteReg(R8, 0x00000000)
TA260 003:235.518 - 0.003ms returns 0
TA260 003:235.522 JLINK_WriteReg(R9, 0x20000180)
TA260 003:235.525 - 0.003ms returns 0
TA260 003:235.529 JLINK_WriteReg(R10, 0x00000000)
TA260 003:235.533 - 0.003ms returns 0
TA260 003:235.537 JLINK_WriteReg(R11, 0x00000000)
TA260 003:235.540 - 0.003ms returns 0
TA260 003:235.545 JLINK_WriteReg(R12, 0x00000000)
TA260 003:235.548 - 0.003ms returns 0
TA260 003:235.552 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:235.556 - 0.004ms returns 0
TA260 003:235.560 JLINK_WriteReg(R14, 0x20000001)
TA260 003:235.564 - 0.003ms returns 0
TA260 003:235.568 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:235.572 - 0.003ms returns 0
TA260 003:235.576 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:235.579 - 0.003ms returns 0
TA260 003:235.583 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:235.587 - 0.003ms returns 0
TA260 003:235.591 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:235.594 - 0.003ms returns 0
TA260 003:235.598 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:235.602 - 0.003ms returns 0
TA260 003:235.606 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:235.610 - 0.004ms returns 0x00000045
TA260 003:235.614 JLINK_Go()
TA260 003:235.623   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:238.362 - 2.746ms 
TA260 003:238.373 JLINK_IsHalted()
TA260 003:238.870 - 0.497ms returns FALSE
TA260 003:238.878 JLINK_HasError()
TA260 003:240.101 JLINK_IsHalted()
TA260 003:242.461   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:242.964 - 2.862ms returns TRUE
TA260 003:242.974 JLINK_ReadReg(R15 (PC))
TA260 003:242.980 - 0.005ms returns 0x20000000
TA260 003:242.984 JLINK_ClrBPEx(BPHandle = 0x00000045)
TA260 003:242.988 - 0.003ms returns 0x00
TA260 003:242.993 JLINK_ReadReg(R0)
TA260 003:242.996 - 0.003ms returns 0x00000000
TA260 003:243.002 JLINK_HasError()
TA260 003:243.006 JLINK_WriteReg(R0, 0x00000002)
TA260 003:243.010 - 0.004ms returns 0
TA260 003:243.014 JLINK_WriteReg(R1, 0x00000118)
TA260 003:243.018 - 0.003ms returns 0
TA260 003:243.022 JLINK_WriteReg(R2, 0x20000184)
TA260 003:243.025 - 0.003ms returns 0
TA260 003:243.030 JLINK_WriteReg(R3, 0x00000000)
TA260 003:243.033 - 0.003ms returns 0
TA260 003:243.037 JLINK_WriteReg(R4, 0x00000000)
TA260 003:243.040 - 0.003ms returns 0
TA260 003:243.044 JLINK_WriteReg(R5, 0x00000000)
TA260 003:243.048 - 0.003ms returns 0
TA260 003:243.052 JLINK_WriteReg(R6, 0x00000000)
TA260 003:243.055 - 0.003ms returns 0
TA260 003:243.059 JLINK_WriteReg(R7, 0x00000000)
TA260 003:243.063 - 0.003ms returns 0
TA260 003:243.067 JLINK_WriteReg(R8, 0x00000000)
TA260 003:243.070 - 0.003ms returns 0
TA260 003:243.074 JLINK_WriteReg(R9, 0x20000180)
TA260 003:243.078 - 0.003ms returns 0
TA260 003:243.082 JLINK_WriteReg(R10, 0x00000000)
TA260 003:243.086 - 0.004ms returns 0
TA260 003:243.091 JLINK_WriteReg(R11, 0x00000000)
TA260 003:243.094 - 0.003ms returns 0
TA260 003:243.099 JLINK_WriteReg(R12, 0x00000000)
TA260 003:243.102 - 0.003ms returns 0
TA260 003:243.106 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:243.110 - 0.003ms returns 0
TA260 003:243.114 JLINK_WriteReg(R14, 0x20000001)
TA260 003:243.117 - 0.003ms returns 0
TA260 003:243.121 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 003:243.125 - 0.003ms returns 0
TA260 003:243.129 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:243.132 - 0.003ms returns 0
TA260 003:243.136 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:243.139 - 0.003ms returns 0
TA260 003:243.143 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:243.147 - 0.003ms returns 0
TA260 003:243.151 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:243.154 - 0.003ms returns 0
TA260 003:243.159 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:243.163 - 0.004ms returns 0x00000046
TA260 003:243.167 JLINK_Go()
TA260 003:243.179   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:245.920 - 2.752ms 
TA260 003:245.941 JLINK_IsHalted()
TA260 003:248.239   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:248.748 - 2.806ms returns TRUE
TA260 003:248.755 JLINK_ReadReg(R15 (PC))
TA260 003:248.760 - 0.005ms returns 0x20000000
TA260 003:248.764 JLINK_ClrBPEx(BPHandle = 0x00000046)
TA260 003:248.768 - 0.003ms returns 0x00
TA260 003:248.773 JLINK_ReadReg(R0)
TA260 003:248.776 - 0.003ms returns 0x00000000
TA260 003:304.561 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 003:304.577   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 003:304.596   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 003:306.605 - 2.043ms returns 0x184
TA260 003:306.647 JLINK_HasError()
TA260 003:306.655 JLINK_WriteReg(R0, 0x08000000)
TA260 003:306.661 - 0.006ms returns 0
TA260 003:306.666 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 003:306.669 - 0.003ms returns 0
TA260 003:306.674 JLINK_WriteReg(R2, 0x00000003)
TA260 003:306.677 - 0.003ms returns 0
TA260 003:306.681 JLINK_WriteReg(R3, 0x00000000)
TA260 003:306.684 - 0.003ms returns 0
TA260 003:306.689 JLINK_WriteReg(R4, 0x00000000)
TA260 003:306.692 - 0.003ms returns 0
TA260 003:306.696 JLINK_WriteReg(R5, 0x00000000)
TA260 003:306.700 - 0.003ms returns 0
TA260 003:306.704 JLINK_WriteReg(R6, 0x00000000)
TA260 003:306.707 - 0.003ms returns 0
TA260 003:306.712 JLINK_WriteReg(R7, 0x00000000)
TA260 003:306.715 - 0.003ms returns 0
TA260 003:306.719 JLINK_WriteReg(R8, 0x00000000)
TA260 003:306.725 - 0.006ms returns 0
TA260 003:306.729 JLINK_WriteReg(R9, 0x20000180)
TA260 003:306.733 - 0.003ms returns 0
TA260 003:306.737 JLINK_WriteReg(R10, 0x00000000)
TA260 003:306.740 - 0.003ms returns 0
TA260 003:306.744 JLINK_WriteReg(R11, 0x00000000)
TA260 003:306.748 - 0.003ms returns 0
TA260 003:306.752 JLINK_WriteReg(R12, 0x00000000)
TA260 003:306.756 - 0.003ms returns 0
TA260 003:306.760 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:306.764 - 0.004ms returns 0
TA260 003:306.769 JLINK_WriteReg(R14, 0x20000001)
TA260 003:306.772 - 0.003ms returns 0
TA260 003:306.776 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 003:306.780 - 0.003ms returns 0
TA260 003:306.784 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:306.788 - 0.003ms returns 0
TA260 003:306.792 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:306.795 - 0.003ms returns 0
TA260 003:306.800 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:306.803 - 0.003ms returns 0
TA260 003:306.807 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:306.811 - 0.003ms returns 0
TA260 003:306.816 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:306.825   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:307.344 - 0.527ms returns 0x00000047
TA260 003:307.361 JLINK_Go()
TA260 003:307.368   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 003:307.874   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:310.645 - 3.283ms 
TA260 003:310.660 JLINK_IsHalted()
TA260 003:313.015   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:313.485 - 2.824ms returns TRUE
TA260 003:313.494 JLINK_ReadReg(R15 (PC))
TA260 003:313.501 - 0.006ms returns 0x20000000
TA260 003:313.505 JLINK_ClrBPEx(BPHandle = 0x00000047)
TA260 003:313.509 - 0.003ms returns 0x00
TA260 003:313.513 JLINK_ReadReg(R0)
TA260 003:313.517 - 0.003ms returns 0x00000000
TA260 003:313.521 JLINK_HasError()
TA260 003:313.526 JLINK_WriteReg(R0, 0xFFFFFFFF)
TA260 003:313.530 - 0.004ms returns 0
TA260 003:313.534 JLINK_WriteReg(R1, 0x08000000)
TA260 003:313.538 - 0.003ms returns 0
TA260 003:313.542 JLINK_WriteReg(R2, 0x0000E518)
TA260 003:313.546 - 0.003ms returns 0
TA260 003:313.550 JLINK_WriteReg(R3, 0x04C11DB7)
TA260 003:313.553 - 0.003ms returns 0
TA260 003:313.557 JLINK_WriteReg(R4, 0x00000000)
TA260 003:313.560 - 0.003ms returns 0
TA260 003:313.564 JLINK_WriteReg(R5, 0x00000000)
TA260 003:313.568 - 0.003ms returns 0
TA260 003:313.572 JLINK_WriteReg(R6, 0x00000000)
TA260 003:313.575 - 0.003ms returns 0
TA260 003:313.579 JLINK_WriteReg(R7, 0x00000000)
TA260 003:313.582 - 0.003ms returns 0
TA260 003:313.590 JLINK_WriteReg(R8, 0x00000000)
TA260 003:313.594 - 0.004ms returns 0
TA260 003:313.598 JLINK_WriteReg(R9, 0x20000180)
TA260 003:313.602 - 0.003ms returns 0
TA260 003:313.606 JLINK_WriteReg(R10, 0x00000000)
TA260 003:313.609 - 0.003ms returns 0
TA260 003:313.613 JLINK_WriteReg(R11, 0x00000000)
TA260 003:313.616 - 0.003ms returns 0
TA260 003:313.620 JLINK_WriteReg(R12, 0x00000000)
TA260 003:313.624 - 0.003ms returns 0
TA260 003:313.628 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:313.632 - 0.003ms returns 0
TA260 003:313.636 JLINK_WriteReg(R14, 0x20000001)
TA260 003:313.639 - 0.003ms returns 0
TA260 003:313.643 JLINK_WriteReg(R15 (PC), 0x20000002)
TA260 003:313.647 - 0.003ms returns 0
TA260 003:313.651 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:313.654 - 0.003ms returns 0
TA260 003:313.658 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:313.662 - 0.003ms returns 0
TA260 003:313.666 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:313.669 - 0.003ms returns 0
TA260 003:313.673 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:313.677 - 0.003ms returns 0
TA260 003:313.681 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:313.685 - 0.004ms returns 0x00000048
TA260 003:313.689 JLINK_Go()
TA260 003:313.696   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:316.404 - 2.714ms 
TA260 003:316.420 JLINK_IsHalted()
TA260 003:316.913 - 0.493ms returns FALSE
TA260 003:316.922 JLINK_HasError()
TA260 003:323.434 JLINK_IsHalted()
TA260 003:323.919 - 0.484ms returns FALSE
TA260 003:323.936 JLINK_HasError()
TA260 003:325.442 JLINK_IsHalted()
TA260 003:325.896 - 0.453ms returns FALSE
TA260 003:325.906 JLINK_HasError()
TA260 003:327.432 JLINK_IsHalted()
TA260 003:327.927 - 0.494ms returns FALSE
TA260 003:327.944 JLINK_HasError()
TA260 003:329.427 JLINK_IsHalted()
TA260 003:329.936 - 0.508ms returns FALSE
TA260 003:329.944 JLINK_HasError()
TA260 003:331.937 JLINK_IsHalted()
TA260 003:332.404 - 0.467ms returns FALSE
TA260 003:332.412 JLINK_HasError()
TA260 003:333.939 JLINK_IsHalted()
TA260 003:334.436 - 0.496ms returns FALSE
TA260 003:334.444 JLINK_HasError()
TA260 003:335.944 JLINK_IsHalted()
TA260 003:336.452 - 0.507ms returns FALSE
TA260 003:336.460 JLINK_HasError()
TA260 003:337.940 JLINK_IsHalted()
TA260 003:338.438 - 0.498ms returns FALSE
TA260 003:338.449 JLINK_HasError()
TA260 003:339.939 JLINK_IsHalted()
TA260 003:340.436 - 0.497ms returns FALSE
TA260 003:340.445 JLINK_HasError()
TA260 003:341.940 JLINK_IsHalted()
TA260 003:342.440 - 0.500ms returns FALSE
TA260 003:342.451 JLINK_HasError()
TA260 003:344.444 JLINK_IsHalted()
TA260 003:344.915 - 0.470ms returns FALSE
TA260 003:344.922 JLINK_HasError()
TA260 003:346.449 JLINK_IsHalted()
TA260 003:346.961 - 0.512ms returns FALSE
TA260 003:346.970 JLINK_HasError()
TA260 003:348.445 JLINK_IsHalted()
TA260 003:348.925 - 0.479ms returns FALSE
TA260 003:348.937 JLINK_HasError()
TA260 003:350.449 JLINK_IsHalted()
TA260 003:350.936 - 0.487ms returns FALSE
TA260 003:350.944 JLINK_HasError()
TA260 003:352.953 JLINK_IsHalted()
TA260 003:353.425 - 0.472ms returns FALSE
TA260 003:353.436 JLINK_HasError()
TA260 003:354.950 JLINK_IsHalted()
TA260 003:355.462 - 0.511ms returns FALSE
TA260 003:355.471 JLINK_HasError()
TA260 003:356.961 JLINK_IsHalted()
TA260 003:357.462 - 0.501ms returns FALSE
TA260 003:357.472 JLINK_HasError()
TA260 003:358.962 JLINK_IsHalted()
TA260 003:359.484 - 0.522ms returns FALSE
TA260 003:359.493 JLINK_HasError()
TA260 003:360.956 JLINK_IsHalted()
TA260 003:361.450 - 0.494ms returns FALSE
TA260 003:361.457 JLINK_HasError()
TA260 003:363.461 JLINK_IsHalted()
TA260 003:363.970 - 0.508ms returns FALSE
TA260 003:363.977 JLINK_HasError()
TA260 003:365.464 JLINK_IsHalted()
TA260 003:365.966 - 0.501ms returns FALSE
TA260 003:365.974 JLINK_HasError()
TA260 003:367.467 JLINK_IsHalted()
TA260 003:367.964 - 0.496ms returns FALSE
TA260 003:367.972 JLINK_HasError()
TA260 003:369.464 JLINK_IsHalted()
TA260 003:369.915 - 0.450ms returns FALSE
TA260 003:369.926 JLINK_HasError()
TA260 003:371.977 JLINK_IsHalted()
TA260 003:372.468 - 0.491ms returns FALSE
TA260 003:372.476 JLINK_HasError()
TA260 003:373.976 JLINK_IsHalted()
TA260 003:374.475 - 0.497ms returns FALSE
TA260 003:374.486 JLINK_HasError()
TA260 003:375.977 JLINK_IsHalted()
TA260 003:376.465 - 0.488ms returns FALSE
TA260 003:376.471 JLINK_HasError()
TA260 003:378.013 JLINK_IsHalted()
TA260 003:378.516 - 0.502ms returns FALSE
TA260 003:378.535 JLINK_HasError()
TA260 003:379.978 JLINK_IsHalted()
TA260 003:380.512 - 0.534ms returns FALSE
TA260 003:380.522 JLINK_HasError()
TA260 003:381.973 JLINK_IsHalted()
TA260 003:382.486 - 0.513ms returns FALSE
TA260 003:382.495 JLINK_HasError()
TA260 003:383.984 JLINK_IsHalted()
TA260 003:384.462 - 0.477ms returns FALSE
TA260 003:384.469 JLINK_HasError()
TA260 003:385.984 JLINK_IsHalted()
TA260 003:386.446 - 0.461ms returns FALSE
TA260 003:386.452 JLINK_HasError()
TA260 003:387.982 JLINK_IsHalted()
TA260 003:388.448 - 0.466ms returns FALSE
TA260 003:388.455 JLINK_HasError()
TA260 003:389.982 JLINK_IsHalted()
TA260 003:390.452 - 0.470ms returns FALSE
TA260 003:390.458 JLINK_HasError()
TA260 003:391.988 JLINK_IsHalted()
TA260 003:392.464 - 0.476ms returns FALSE
TA260 003:392.472 JLINK_HasError()
TA260 003:394.495 JLINK_IsHalted()
TA260 003:394.974 - 0.478ms returns FALSE
TA260 003:394.981 JLINK_HasError()
TA260 003:396.489 JLINK_IsHalted()
TA260 003:396.963 - 0.473ms returns FALSE
TA260 003:396.974 JLINK_HasError()
TA260 003:398.492 JLINK_IsHalted()
TA260 003:399.019 - 0.525ms returns FALSE
TA260 003:399.029 JLINK_HasError()
TA260 003:400.493 JLINK_IsHalted()
TA260 003:400.938 - 0.445ms returns FALSE
TA260 003:400.948 JLINK_HasError()
TA260 003:401.996 JLINK_IsHalted()
TA260 003:402.487 - 0.489ms returns FALSE
TA260 003:402.493 JLINK_HasError()
TA260 003:404.005 JLINK_IsHalted()
TA260 003:404.482 - 0.477ms returns FALSE
TA260 003:404.490 JLINK_HasError()
TA260 003:406.003 JLINK_IsHalted()
TA260 003:406.667 - 0.663ms returns FALSE
TA260 003:406.674 JLINK_HasError()
TA260 003:408.002 JLINK_IsHalted()
TA260 003:408.498 - 0.495ms returns FALSE
TA260 003:408.505 JLINK_HasError()
TA260 003:410.000 JLINK_IsHalted()
TA260 003:410.458 - 0.457ms returns FALSE
TA260 003:410.465 JLINK_HasError()
TA260 003:411.999 JLINK_IsHalted()
TA260 003:412.465 - 0.465ms returns FALSE
TA260 003:412.472 JLINK_HasError()
TA260 003:413.505 JLINK_IsHalted()
TA260 003:414.017 - 0.511ms returns FALSE
TA260 003:414.026 JLINK_HasError()
TA260 003:415.514 JLINK_IsHalted()
TA260 003:416.019 - 0.505ms returns FALSE
TA260 003:416.027 JLINK_HasError()
TA260 003:417.509 JLINK_IsHalted()
TA260 003:418.019 - 0.509ms returns FALSE
TA260 003:418.030 JLINK_HasError()
TA260 003:419.517 JLINK_IsHalted()
TA260 003:420.029 - 0.512ms returns FALSE
TA260 003:420.038 JLINK_HasError()
TA260 003:422.015 JLINK_IsHalted()
TA260 003:422.539 - 0.523ms returns FALSE
TA260 003:422.547 JLINK_HasError()
TA260 003:424.026 JLINK_IsHalted()
TA260 003:424.522 - 0.495ms returns FALSE
TA260 003:424.530 JLINK_HasError()
TA260 003:426.027 JLINK_IsHalted()
TA260 003:426.484 - 0.456ms returns FALSE
TA260 003:426.490 JLINK_HasError()
TA260 003:428.026 JLINK_IsHalted()
TA260 003:428.623 - 0.596ms returns FALSE
TA260 003:429.256 JLINK_HasError()
TA260 003:432.027 JLINK_IsHalted()
TA260 003:432.536 - 0.508ms returns FALSE
TA260 003:432.549 JLINK_HasError()
TA260 003:434.528 JLINK_IsHalted()
TA260 003:435.016 - 0.488ms returns FALSE
TA260 003:435.024 JLINK_HasError()
TA260 003:436.694 JLINK_IsHalted()
TA260 003:437.210 - 0.516ms returns FALSE
TA260 003:437.220 JLINK_HasError()
TA260 003:438.524 JLINK_IsHalted()
TA260 003:439.039 - 0.514ms returns FALSE
TA260 003:439.048 JLINK_HasError()
TA260 003:440.532 JLINK_IsHalted()
TA260 003:441.028 - 0.496ms returns FALSE
TA260 003:441.035 JLINK_HasError()
TA260 003:443.032 JLINK_IsHalted()
TA260 003:443.542 - 0.510ms returns FALSE
TA260 003:443.553 JLINK_HasError()
TA260 003:445.032 JLINK_IsHalted()
TA260 003:445.541 - 0.508ms returns FALSE
TA260 003:445.548 JLINK_HasError()
TA260 003:447.034 JLINK_IsHalted()
TA260 003:447.551 - 0.516ms returns FALSE
TA260 003:447.558 JLINK_HasError()
TA260 003:449.033 JLINK_IsHalted()
TA260 003:449.542 - 0.508ms returns FALSE
TA260 003:449.551 JLINK_HasError()
TA260 003:451.044 JLINK_IsHalted()
TA260 003:451.562 - 0.518ms returns FALSE
TA260 003:451.571 JLINK_HasError()
TA260 003:453.542 JLINK_IsHalted()
TA260 003:454.031 - 0.488ms returns FALSE
TA260 003:454.040 JLINK_HasError()
TA260 003:455.541 JLINK_IsHalted()
TA260 003:455.982 - 0.441ms returns FALSE
TA260 003:455.989 JLINK_HasError()
TA260 003:457.542 JLINK_IsHalted()
TA260 003:458.024 - 0.481ms returns FALSE
TA260 003:458.034 JLINK_HasError()
TA260 003:459.540 JLINK_IsHalted()
TA260 003:460.030 - 0.489ms returns FALSE
TA260 003:460.036 JLINK_HasError()
TA260 003:462.052 JLINK_IsHalted()
TA260 003:462.483 - 0.431ms returns FALSE
TA260 003:462.496 JLINK_HasError()
TA260 003:464.053 JLINK_IsHalted()
TA260 003:464.508 - 0.453ms returns FALSE
TA260 003:464.519 JLINK_HasError()
TA260 003:466.055 JLINK_IsHalted()
TA260 003:466.662 - 0.607ms returns FALSE
TA260 003:466.670 JLINK_HasError()
TA260 003:468.048 JLINK_IsHalted()
TA260 003:468.519 - 0.470ms returns FALSE
TA260 003:468.527 JLINK_HasError()
TA260 003:470.052 JLINK_IsHalted()
TA260 003:470.519 - 0.467ms returns FALSE
TA260 003:470.525 JLINK_HasError()
TA260 003:472.061 JLINK_IsHalted()
TA260 003:472.549 - 0.487ms returns FALSE
TA260 003:472.556 JLINK_HasError()
TA260 003:474.557 JLINK_IsHalted()
TA260 003:475.032 - 0.475ms returns FALSE
TA260 003:475.043 JLINK_HasError()
TA260 003:476.591 JLINK_IsHalted()
TA260 003:477.052 - 0.460ms returns FALSE
TA260 003:477.064 JLINK_HasError()
TA260 003:478.556 JLINK_IsHalted()
TA260 003:479.052 - 0.495ms returns FALSE
TA260 003:479.062 JLINK_HasError()
TA260 003:480.559 JLINK_IsHalted()
TA260 003:481.087 - 0.527ms returns FALSE
TA260 003:481.094 JLINK_HasError()
TA260 003:483.060 JLINK_IsHalted()
TA260 003:483.531 - 0.471ms returns FALSE
TA260 003:483.540 JLINK_HasError()
TA260 003:485.575 JLINK_IsHalted()
TA260 003:486.062 - 0.487ms returns FALSE
TA260 003:486.072 JLINK_HasError()
TA260 003:487.564 JLINK_IsHalted()
TA260 003:488.038 - 0.473ms returns FALSE
TA260 003:488.046 JLINK_HasError()
TA260 003:489.567 JLINK_IsHalted()
TA260 003:490.029 - 0.461ms returns FALSE
TA260 003:490.038 JLINK_HasError()
TA260 003:492.070 JLINK_IsHalted()
TA260 003:492.548 - 0.477ms returns FALSE
TA260 003:492.564 JLINK_HasError()
TA260 003:496.715 JLINK_IsHalted()
TA260 003:497.237 - 0.521ms returns FALSE
TA260 003:497.244 JLINK_HasError()
TA260 003:498.746 JLINK_IsHalted()
TA260 003:499.257 - 0.511ms returns FALSE
TA260 003:499.267 JLINK_HasError()
TA260 003:500.726 JLINK_IsHalted()
TA260 003:501.195 - 0.468ms returns FALSE
TA260 003:501.204 JLINK_HasError()
TA260 003:503.233 JLINK_IsHalted()
TA260 003:503.716 - 0.482ms returns FALSE
TA260 003:503.723 JLINK_HasError()
TA260 003:505.232 JLINK_IsHalted()
TA260 003:505.726 - 0.493ms returns FALSE
TA260 003:505.739 JLINK_HasError()
TA260 003:507.235 JLINK_IsHalted()
TA260 003:507.743 - 0.507ms returns FALSE
TA260 003:507.754 JLINK_HasError()
TA260 003:509.231 JLINK_IsHalted()
TA260 003:509.792 - 0.561ms returns FALSE
TA260 003:509.799 JLINK_HasError()
TA260 003:511.738 JLINK_IsHalted()
TA260 003:512.164 - 0.425ms returns FALSE
TA260 003:512.171 JLINK_HasError()
TA260 003:513.740 JLINK_IsHalted()
TA260 003:514.235 - 0.495ms returns FALSE
TA260 003:514.241 JLINK_HasError()
TA260 003:515.746 JLINK_IsHalted()
TA260 003:516.245 - 0.498ms returns FALSE
TA260 003:516.253 JLINK_HasError()
TA260 003:517.744 JLINK_IsHalted()
TA260 003:518.163 - 0.419ms returns FALSE
TA260 003:518.172 JLINK_HasError()
TA260 003:519.740 JLINK_IsHalted()
TA260 003:520.245 - 0.505ms returns FALSE
TA260 003:520.251 JLINK_HasError()
TA260 003:521.740 JLINK_IsHalted()
TA260 003:522.241 - 0.500ms returns FALSE
TA260 003:522.247 JLINK_HasError()
TA260 003:523.782 JLINK_IsHalted()
TA260 003:524.276 - 0.494ms returns FALSE
TA260 003:524.285 JLINK_HasError()
TA260 003:525.329 JLINK_IsHalted()
TA260 003:525.823 - 0.493ms returns FALSE
TA260 003:525.829 JLINK_HasError()
TA260 003:526.883 JLINK_IsHalted()
TA260 003:527.380 - 0.496ms returns FALSE
TA260 003:527.387 JLINK_HasError()
TA260 003:528.436 JLINK_IsHalted()
TA260 003:528.936 - 0.500ms returns FALSE
TA260 003:528.943 JLINK_HasError()
TA260 003:529.990 JLINK_IsHalted()
TA260 003:530.497 - 0.506ms returns FALSE
TA260 003:530.503 JLINK_HasError()
TA260 003:531.542 JLINK_IsHalted()
TA260 003:532.050 - 0.508ms returns FALSE
TA260 003:532.057 JLINK_HasError()
TA260 003:533.630 JLINK_IsHalted()
TA260 003:534.112 - 0.482ms returns FALSE
TA260 003:534.121 JLINK_HasError()
TA260 003:535.634 JLINK_IsHalted()
TA260 003:536.140 - 0.505ms returns FALSE
TA260 003:536.147 JLINK_HasError()
TA260 003:537.630 JLINK_IsHalted()
TA260 003:538.091 - 0.460ms returns FALSE
TA260 003:538.105 JLINK_HasError()
TA260 003:540.638 JLINK_IsHalted()
TA260 003:541.136 - 0.490ms returns FALSE
TA260 003:541.143 JLINK_HasError()
TA260 003:543.137 JLINK_IsHalted()
TA260 003:543.648 - 0.510ms returns FALSE
TA260 003:543.654 JLINK_HasError()
TA260 003:545.138 JLINK_IsHalted()
TA260 003:545.639 - 0.500ms returns FALSE
TA260 003:545.645 JLINK_HasError()
TA260 003:547.138 JLINK_IsHalted()
TA260 003:547.640 - 0.502ms returns FALSE
TA260 003:547.646 JLINK_HasError()
TA260 003:549.142 JLINK_IsHalted()
TA260 003:549.651 - 0.508ms returns FALSE
TA260 003:549.657 JLINK_HasError()
TA260 003:551.646 JLINK_IsHalted()
TA260 003:552.092 - 0.446ms returns FALSE
TA260 003:552.103 JLINK_HasError()
TA260 003:553.652 JLINK_IsHalted()
TA260 003:554.150 - 0.497ms returns FALSE
TA260 003:554.156 JLINK_HasError()
TA260 003:555.651 JLINK_IsHalted()
TA260 003:556.131 - 0.479ms returns FALSE
TA260 003:556.142 JLINK_HasError()
TA260 003:557.651 JLINK_IsHalted()
TA260 003:558.138 - 0.487ms returns FALSE
TA260 003:558.144 JLINK_HasError()
TA260 003:559.652 JLINK_IsHalted()
TA260 003:560.142 - 0.490ms returns FALSE
TA260 003:560.149 JLINK_HasError()
TA260 003:561.652 JLINK_IsHalted()
TA260 003:562.125 - 0.473ms returns FALSE
TA260 003:562.131 JLINK_HasError()
TA260 003:564.163 JLINK_IsHalted()
TA260 003:564.685 - 0.522ms returns FALSE
TA260 003:564.692 JLINK_HasError()
TA260 003:566.157 JLINK_IsHalted()
TA260 003:566.654 - 0.496ms returns FALSE
TA260 003:566.660 JLINK_HasError()
TA260 003:568.160 JLINK_IsHalted()
TA260 003:568.642 - 0.481ms returns FALSE
TA260 003:568.654 JLINK_HasError()
TA260 003:570.160 JLINK_IsHalted()
TA260 003:570.674 - 0.514ms returns FALSE
TA260 003:570.681 JLINK_HasError()
TA260 003:572.663 JLINK_IsHalted()
TA260 003:573.095 - 0.431ms returns FALSE
TA260 003:573.112 JLINK_HasError()
TA260 003:574.667 JLINK_IsHalted()
TA260 003:575.161 - 0.494ms returns FALSE
TA260 003:575.168 JLINK_HasError()
TA260 003:576.666 JLINK_IsHalted()
TA260 003:577.142 - 0.476ms returns FALSE
TA260 003:577.149 JLINK_HasError()
TA260 003:578.669 JLINK_IsHalted()
TA260 003:579.149 - 0.479ms returns FALSE
TA260 003:579.155 JLINK_HasError()
TA260 003:580.668 JLINK_IsHalted()
TA260 003:581.185 - 0.516ms returns FALSE
TA260 003:581.195 JLINK_HasError()
TA260 003:583.176 JLINK_IsHalted()
TA260 003:583.648 - 0.471ms returns FALSE
TA260 003:583.656 JLINK_HasError()
TA260 003:585.180 JLINK_IsHalted()
TA260 003:585.687 - 0.507ms returns FALSE
TA260 003:585.693 JLINK_HasError()
TA260 003:587.178 JLINK_IsHalted()
TA260 003:587.686 - 0.508ms returns FALSE
TA260 003:587.693 JLINK_HasError()
TA260 003:589.179 JLINK_IsHalted()
TA260 003:589.648 - 0.468ms returns FALSE
TA260 003:589.655 JLINK_HasError()
TA260 003:591.683 JLINK_IsHalted()
TA260 003:592.153 - 0.470ms returns FALSE
TA260 003:592.168 JLINK_HasError()
TA260 003:593.684 JLINK_IsHalted()
TA260 003:594.181 - 0.496ms returns FALSE
TA260 003:594.188 JLINK_HasError()
TA260 003:595.689 JLINK_IsHalted()
TA260 003:596.188 - 0.498ms returns FALSE
TA260 003:596.199 JLINK_HasError()
TA260 003:597.686 JLINK_IsHalted()
TA260 003:598.107 - 0.421ms returns FALSE
TA260 003:598.118 JLINK_HasError()
TA260 003:599.686 JLINK_IsHalted()
TA260 003:600.213 - 0.526ms returns FALSE
TA260 003:600.224 JLINK_HasError()
TA260 003:601.688 JLINK_IsHalted()
TA260 003:602.185 - 0.497ms returns FALSE
TA260 003:602.191 JLINK_HasError()
TA260 003:604.197 JLINK_IsHalted()
TA260 003:604.685 - 0.487ms returns FALSE
TA260 003:604.694 JLINK_HasError()
TA260 003:606.245 JLINK_IsHalted()
TA260 003:606.880 - 0.634ms returns FALSE
TA260 003:606.888 JLINK_HasError()
TA260 003:608.197 JLINK_IsHalted()
TA260 003:608.686 - 0.489ms returns FALSE
TA260 003:608.696 JLINK_HasError()
TA260 003:610.194 JLINK_IsHalted()
TA260 003:610.684 - 0.490ms returns FALSE
TA260 003:610.690 JLINK_HasError()
TA260 003:612.700 JLINK_IsHalted()
TA260 003:613.165 - 0.465ms returns FALSE
TA260 003:613.172 JLINK_HasError()
TA260 003:614.704 JLINK_IsHalted()
TA260 003:615.208 - 0.503ms returns FALSE
TA260 003:615.214 JLINK_HasError()
TA260 003:616.708 JLINK_IsHalted()
TA260 003:617.165 - 0.456ms returns FALSE
TA260 003:617.173 JLINK_HasError()
TA260 003:618.704 JLINK_IsHalted()
TA260 003:619.187 - 0.483ms returns FALSE
TA260 003:619.195 JLINK_HasError()
TA260 003:620.701 JLINK_IsHalted()
TA260 003:621.192 - 0.491ms returns FALSE
TA260 003:621.199 JLINK_HasError()
TA260 003:623.209 JLINK_IsHalted()
TA260 003:623.674 - 0.464ms returns FALSE
TA260 003:623.681 JLINK_HasError()
TA260 003:625.211 JLINK_IsHalted()
TA260 003:625.714 - 0.502ms returns FALSE
TA260 003:625.721 JLINK_HasError()
TA260 003:627.219 JLINK_IsHalted()
TA260 003:627.716 - 0.497ms returns FALSE
TA260 003:627.727 JLINK_HasError()
TA260 003:629.212 JLINK_IsHalted()
TA260 003:629.720 - 0.507ms returns FALSE
TA260 003:629.727 JLINK_HasError()
TA260 003:631.716 JLINK_IsHalted()
TA260 003:632.208 - 0.492ms returns FALSE
TA260 003:632.220 JLINK_HasError()
TA260 003:633.718 JLINK_IsHalted()
TA260 003:634.210 - 0.491ms returns FALSE
TA260 003:634.216 JLINK_HasError()
TA260 003:635.720 JLINK_IsHalted()
TA260 003:636.198 - 0.477ms returns FALSE
TA260 003:636.206 JLINK_HasError()
TA260 003:637.724 JLINK_IsHalted()
TA260 003:640.147   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:640.639 - 2.915ms returns TRUE
TA260 003:640.647 JLINK_ReadReg(R15 (PC))
TA260 003:640.652 - 0.005ms returns 0x20000000
TA260 003:640.656 JLINK_ClrBPEx(BPHandle = 0x00000048)
TA260 003:640.660 - 0.003ms returns 0x00
TA260 003:640.665 JLINK_ReadReg(R0)
TA260 003:640.668 - 0.003ms returns 0x1969CF0F
TA260 003:642.471 JLINK_HasError()
TA260 003:642.487 JLINK_WriteReg(R0, 0x00000003)
TA260 003:642.493 - 0.005ms returns 0
TA260 003:642.497 JLINK_WriteReg(R1, 0x08000000)
TA260 003:642.500 - 0.003ms returns 0
TA260 003:642.505 JLINK_WriteReg(R2, 0x0000E518)
TA260 003:642.509 - 0.003ms returns 0
TA260 003:642.514 JLINK_WriteReg(R3, 0x04C11DB7)
TA260 003:642.517 - 0.003ms returns 0
TA260 003:642.521 JLINK_WriteReg(R4, 0x00000000)
TA260 003:642.524 - 0.003ms returns 0
TA260 003:642.528 JLINK_WriteReg(R5, 0x00000000)
TA260 003:642.532 - 0.003ms returns 0
TA260 003:642.536 JLINK_WriteReg(R6, 0x00000000)
TA260 003:642.539 - 0.003ms returns 0
TA260 003:642.543 JLINK_WriteReg(R7, 0x00000000)
TA260 003:642.546 - 0.003ms returns 0
TA260 003:642.550 JLINK_WriteReg(R8, 0x00000000)
TA260 003:642.554 - 0.003ms returns 0
TA260 003:642.558 JLINK_WriteReg(R9, 0x20000180)
TA260 003:642.562 - 0.003ms returns 0
TA260 003:642.565 JLINK_WriteReg(R10, 0x00000000)
TA260 003:642.569 - 0.003ms returns 0
TA260 003:642.573 JLINK_WriteReg(R11, 0x00000000)
TA260 003:642.576 - 0.003ms returns 0
TA260 003:642.580 JLINK_WriteReg(R12, 0x00000000)
TA260 003:642.584 - 0.003ms returns 0
TA260 003:642.588 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:642.592 - 0.003ms returns 0
TA260 003:642.596 JLINK_WriteReg(R14, 0x20000001)
TA260 003:642.599 - 0.003ms returns 0
TA260 003:642.603 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 003:642.607 - 0.003ms returns 0
TA260 003:642.611 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:642.618 - 0.006ms returns 0
TA260 003:642.623 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:642.626 - 0.003ms returns 0
TA260 003:642.630 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:642.634 - 0.003ms returns 0
TA260 003:642.638 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:642.642 - 0.004ms returns 0
TA260 003:642.647 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:642.652 - 0.005ms returns 0x00000049
TA260 003:642.656 JLINK_Go()
TA260 003:642.665   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:645.401 - 2.744ms 
TA260 003:645.413 JLINK_IsHalted()
TA260 003:647.728   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:648.246 - 2.833ms returns TRUE
TA260 003:648.255 JLINK_ReadReg(R15 (PC))
TA260 003:648.260 - 0.005ms returns 0x20000000
TA260 003:648.264 JLINK_ClrBPEx(BPHandle = 0x00000049)
TA260 003:648.269 - 0.004ms returns 0x00
TA260 003:648.273 JLINK_ReadReg(R0)
TA260 003:648.277 - 0.003ms returns 0x00000000
TA260 003:701.232 JLINK_WriteMemEx(0x20000000, 0x00000002 Bytes, Flags = 0x02000000)
TA260 003:701.252   Data:  FE E7
TA260 003:701.269   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 003:701.793 - 0.561ms returns 0x2
TA260 003:701.802 JLINK_HasError()
TA260 003:711.814 JLINK_Close()
TA260 003:713.900   OnDisconnectTarget() start
TA260 003:713.918    J-Link Script File: Executing OnDisconnectTarget()
TA260 003:713.939   CPU_WriteMem(4 bytes @ 0xE0042004)
TA260 003:714.440   CPU_WriteMem(4 bytes @ 0xE0042008)
TA260 003:716.731   OnDisconnectTarget() end - Took 994us
TA260 003:716.746   CPU_ReadMem(4 bytes @ 0xE0001000)
TA260 003:733.854 - 22.039ms
TA260 003:733.870   
TA260 003:733.874   Closed
