TA260 000:003.945   SEGGER J-Link V8.16 Log File
TA260 000:004.284   DLL Compiled: Feb 26 2025 12:07:26
TA260 000:004.288   Logging started @ 2025-08-02 08:15
TA260 000:004.292   Process: G:\keil\keil arm\UV4\UV4.exe
TA260 000:004.302 - 4.295ms 
TA260 000:004.311 JLINK_SetWarnOutHandler(...)
TA260 000:004.314 - 0.005ms 
TA260 000:004.322 JLINK_OpenEx(...)
TA260 000:008.376   Firmware: J-Link V9 compiled May  7 2021 16:26:12
TA260 000:009.769   Firmware: J-Link V9 compiled May  7 2021 16:26:12
TA260 000:009.894   Decompressing FW timestamp took 92 us
TA260 000:017.471   Hardware: V9.60
TA260 000:017.488   S/N: 69655018
TA260 000:017.493   OEM: SEGGER
TA260 000:017.498   Feature(s): RDI, GD<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>B<PERSON>, JFlash
TA260 000:018.816   Bootloader: (FW returned invalid version)
TA260 000:020.332   TELNET listener socket opened on port 19021
TA260 000:020.395   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
TA260 000:020.512   WEBSRV Webserver running on local port 19080
TA260 000:020.583   Looking for J-Link GUI Server exe at: G:\keil\keil arm\ARM\Segger\JLinkGUIServer.exe
TA260 000:020.650   Looking for J-Link GUI Server exe at: C:\Program Files (x86)\SEGGER\JLink_V630d\\JLinkGUIServer.exe
TA260 000:322.972   Failed to connect to J-Link GUI Server.
TA260 000:322.998 - 318.669ms returns "O.K."
TA260 000:323.023 JLINK_GetEmuCaps()
TA260 000:323.031 - 0.005ms returns 0xB9FF7BBF
TA260 000:323.038 JLINK_TIF_GetAvailable(...)
TA260 000:323.483 - 0.445ms 
TA260 000:323.503 JLINK_SetErrorOutHandler(...)
TA260 000:323.508 - 0.004ms 
TA260 000:323.530 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\2025ele_ori\zuolan_stm32\MDK-ARM\JLinkSettings.ini"", ...). 
TA260 000:333.950 - 10.421ms returns 0x00
TA260 000:335.899 JLINK_ExecCommand("Device = STM32F429IGTx", ...). 
TA260 000:336.988   Flash bank @ 0x90000000: SFL: Parsing sectorization info from ELF file
TA260 000:337.000     FlashDevice.SectorInfo[0]: .SectorSize = 0x00010000, .SectorStartAddr = 0x00000000
TA260 000:341.805   Device "STM32F429IG" selected.
TA260 000:342.027 - 6.113ms returns 0x00
TA260 000:342.038 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
TA260 000:342.051   ERROR: Unknown command
TA260 000:342.056 - 0.013ms returns 0x01
TA260 000:342.061 JLINK_GetHardwareVersion()
TA260 000:342.065 - 0.003ms returns 96000
TA260 000:342.070 JLINK_GetDLLVersion()
TA260 000:342.073 - 0.003ms returns 81600
TA260 000:342.077 JLINK_GetOEMString(...)
TA260 000:342.082 JLINK_GetFirmwareString(...)
TA260 000:342.086 - 0.003ms 
TA260 000:346.786 JLINK_GetDLLVersion()
TA260 000:346.803 - 0.016ms returns 81600
TA260 000:346.808 JLINK_GetCompileDateTime()
TA260 000:346.812 - 0.003ms 
TA260 000:348.109 JLINK_GetFirmwareString(...)
TA260 000:348.120 - 0.010ms 
TA260 000:349.431 JLINK_GetHardwareVersion()
TA260 000:349.442 - 0.010ms returns 96000
TA260 000:350.717 JLINK_GetSN()
TA260 000:350.728 - 0.010ms returns 69655018
TA260 000:351.999 JLINK_GetOEMString(...)
TA260 000:354.946 JLINK_TIF_Select(JLINKARM_TIF_SWD)
TA260 000:356.440 - 1.496ms returns 0x00
TA260 000:356.449 JLINK_HasError()
TA260 000:356.460 JLINK_SetSpeed(5000)
TA260 000:356.792 - 0.334ms 
TA260 000:356.799 JLINK_GetId()
TA260 000:358.735   InitTarget() start
TA260 000:358.759    J-Link Script File: Executing InitTarget()
TA260 000:360.630   SWD selected. Executing JTAG -> SWD switching sequence.
TA260 000:365.204   DAP initialized successfully.
TA260 000:377.646   InitTarget() end - Took 17.2ms
TA260 000:380.222   Found SW-DP with ID 0x2BA01477
TA260 000:385.482   DPIDR: 0x2BA01477
TA260 000:387.152   CoreSight SoC-400 or earlier
TA260 000:388.515   Scanning AP map to find all available APs
TA260 000:390.878   AP[1]: Stopped AP scan as end of AP map has been reached
TA260 000:392.246   AP[0]: AHB-AP (IDR: 0x24770011, ADDR: 0x00000000)
TA260 000:393.889   Iterating through AP map to find AHB-AP to use
TA260 000:396.760   AP[0]: Core found
TA260 000:398.142   AP[0]: AHB-AP ROM base: 0xE00FF000
TA260 000:400.200   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
TA260 000:402.005   Found Cortex-M4 r0p1, Little endian.
TA260 000:402.767   -- Max. mem block: 0x00010C40
TA260 000:403.584   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:404.065   CPU_ReadMem(4 bytes @ 0x********)
TA260 000:406.073   FPUnit: 6 code (BP) slots and 2 literal slots
TA260 000:406.098   CPU_ReadMem(4 bytes @ 0xE000EDFC)
TA260 000:406.589   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:407.096   CPU_ReadMem(4 bytes @ 0x********)
TA260 000:407.554   CPU_WriteMem(4 bytes @ 0x********)
TA260 000:408.050   CPU_ReadMem(4 bytes @ 0xE000ED88)
TA260 000:408.552   CPU_WriteMem(4 bytes @ 0xE000ED88)
TA260 000:409.040   CPU_ReadMem(4 bytes @ 0xE000ED88)
TA260 000:409.501   CPU_WriteMem(4 bytes @ 0xE000ED88)
TA260 000:411.439   CoreSight components:
TA260 000:412.782   ROMTbl[0] @ E00FF000
TA260 000:412.796   CPU_ReadMem(64 bytes @ 0xE00FF000)
TA260 000:413.582   CPU_ReadMem(32 bytes @ 0xE000EFE0)
TA260 000:415.738   [0][0]: E000E000 CID B105E00D PID 000BB00C SCS-M7
TA260 000:415.757   CPU_ReadMem(32 bytes @ 0xE0001FE0)
TA260 000:418.074   [0][1]: ******** CID B105E00D PID 003BB002 DWT
TA260 000:418.091   CPU_ReadMem(32 bytes @ 0xE0002FE0)
TA260 000:420.078   [0][2]: ******** CID B105E00D PID 002BB003 FPB
TA260 000:420.093   CPU_ReadMem(32 bytes @ 0xE0000FE0)
TA260 000:422.082   [0][3]: ******** CID B105E00D PID 003BB001 ITM
TA260 000:422.096   CPU_ReadMem(32 bytes @ 0xE0040FE0)
TA260 000:424.594   [0][4]: ******** CID B105900D PID 000BB9A1 TPIU
TA260 000:424.614   CPU_ReadMem(32 bytes @ 0xE0041FE0)
TA260 000:426.765   [0][5]: ******** CID B105900D PID 000BB925 ETM
TA260 000:427.282 - 70.482ms returns 0x2BA01477
TA260 000:427.327 JLINK_GetDLLVersion()
TA260 000:427.332 - 0.004ms returns 81600
TA260 000:427.340 JLINK_CORE_GetFound()
TA260 000:427.344 - 0.004ms returns 0xE0000FF
TA260 000:427.349 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
TA260 000:427.354   Value=0xE00FF000
TA260 000:427.359 - 0.010ms returns 0
TA260 000:428.787 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
TA260 000:428.797   Value=0xE00FF000
TA260 000:428.803 - 0.015ms returns 0
TA260 000:428.807 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
TA260 000:428.811   Value=0x********
TA260 000:428.815 - 0.008ms returns 0
TA260 000:428.820 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
TA260 000:428.841   CPU_ReadMem(32 bytes @ 0xE0041FD0)
TA260 000:429.504   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
TA260 000:429.511 - 0.690ms returns 32 (0x20)
TA260 000:429.516 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
TA260 000:429.519   Value=0x00000000
TA260 000:429.524 - 0.008ms returns 0
TA260 000:429.529 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
TA260 000:429.532   Value=0x********
TA260 000:429.537 - 0.008ms returns 0
TA260 000:429.541 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
TA260 000:429.544   Value=0x********
TA260 000:429.549 - 0.008ms returns 0
TA260 000:429.553 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
TA260 000:429.556   Value=0x********
TA260 000:429.561 - 0.008ms returns 0
TA260 000:429.566 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
TA260 000:429.569   Value=0x********
TA260 000:429.574 - 0.008ms returns 0
TA260 000:429.578 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
TA260 000:429.581   Value=0xE000E000
TA260 000:429.586 - 0.008ms returns 0
TA260 000:429.590 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
TA260 000:429.593   Value=0xE000EDF0
TA260 000:429.598 - 0.008ms returns 0
TA260 000:429.602 JLINK_GetDebugInfo(0x01 = Unknown)
TA260 000:429.606   Value=0x00000001
TA260 000:429.610 - 0.008ms returns 0
TA260 000:429.615 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
TA260 000:429.620   CPU_ReadMem(4 bytes @ 0xE000ED00)
TA260 000:430.097   Data:  41 C2 0F 41
TA260 000:430.103   Debug reg: CPUID
TA260 000:430.107 - 0.492ms returns 1 (0x1)
TA260 000:430.113 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
TA260 000:430.119   Value=0x00000000
TA260 000:430.124 - 0.011ms returns 0
TA260 000:430.129 JLINK_HasError()
TA260 000:430.134 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
TA260 000:430.137 - 0.003ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
TA260 000:430.141 JLINK_Reset()
TA260 000:430.147   JLINK_GetResetTypeDesc
TA260 000:430.150   - 0.003ms 
TA260 000:431.525   Reset type: NORMAL (https://wiki.segger.com/J-Link_Reset_Strategies)
TA260 000:431.545   CPU is running
TA260 000:431.551   CPU_WriteMem(4 bytes @ 0xE000EDF0)
TA260 000:432.051   CPU is running
TA260 000:432.057   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:434.261   Reset: Halt core after reset via DEMCR.VC_CORERESET.
TA260 000:436.657   Reset: Reset device via AIRCR.SYSRESETREQ.
TA260 000:436.677   CPU is running
TA260 000:436.686   CPU_WriteMem(4 bytes @ 0xE000ED0C)
TA260 000:491.456   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:491.938   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:494.797   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:501.329   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:504.173   CPU_WriteMem(4 bytes @ 0x********)
TA260 000:504.724   CPU_ReadMem(4 bytes @ 0xE000EDFC)
TA260 000:505.277   CPU_ReadMem(4 bytes @ 0x********)
TA260 000:505.772 - 75.630ms 
TA260 000:505.813 JLINK_Halt()
TA260 000:505.817 - 0.004ms returns 0x00
TA260 000:505.823 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
TA260 000:505.831   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TA260 000:506.312   Data:  03 00 03 00
TA260 000:506.318   Debug reg: DHCSR
TA260 000:506.324 - 0.501ms returns 1 (0x1)
TA260 000:506.329 JLINK_WriteU32(0xE000EDF0, 0xA05F0003)
TA260 000:506.332   Debug reg: DHCSR
TA260 000:506.549   CPU_WriteMem(4 bytes @ 0xE000EDF0)
TA260 000:507.066 - 0.736ms returns 0 (0x00000000)
TA260 000:507.072 JLINK_WriteU32(0xE000EDFC, 0x01000000)
TA260 000:507.076   Debug reg: DEMCR
TA260 000:507.083   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TA260 000:507.585 - 0.512ms returns 0 (0x00000000)
TA260 000:514.652 JLINK_GetHWStatus(...)
TA260 000:515.047 - 0.393ms returns 0
TA260 000:518.969 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
TA260 000:518.982 - 0.012ms returns 0x06
TA260 000:518.987 JLINK_GetNumBPUnits(Type = 0xF0)
TA260 000:518.990 - 0.003ms returns 0x2000
TA260 000:518.995 JLINK_GetNumWPUnits()
TA260 000:518.998 - 0.003ms returns 4
TA260 000:522.765 JLINK_GetSpeed()
TA260 000:522.776 - 0.011ms returns 4000
TA260 000:526.147 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
TA260 000:526.183   CPU_ReadMem(4 bytes @ 0xE000E004)
TA260 000:526.618   Data:  02 00 00 00
TA260 000:526.636 - 0.489ms returns 1 (0x1)
TA260 000:526.643 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
TA260 000:526.651   CPU_ReadMem(4 bytes @ 0xE000E004)
TA260 000:527.182   Data:  02 00 00 00
TA260 000:527.194 - 0.551ms returns 1 (0x1)
TA260 000:527.201 JLINK_WriteMemEx(0x********, 0x0000001C Bytes, Flags = 0x02000004)
TA260 000:527.205   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
TA260 000:527.216   CPU_WriteMem(28 bytes @ 0x********)
TA260 000:527.798 - 0.597ms returns 0x1C
TA260 000:527.815 JLINK_Halt()
TA260 000:527.819 - 0.003ms returns 0x00
TA260 000:527.823 JLINK_IsHalted()
TA260 000:527.827 - 0.004ms returns TRUE
TA260 000:530.501 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 000:530.513   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 000:530.784   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 000:532.649 - 2.147ms returns 0x184
TA260 000:532.708 JLINK_HasError()
TA260 000:532.714 JLINK_WriteReg(R0, 0x08000000)
TA260 000:532.720 - 0.005ms returns 0
TA260 000:532.724 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 000:532.728 - 0.003ms returns 0
TA260 000:532.732 JLINK_WriteReg(R2, 0x00000001)
TA260 000:532.735 - 0.003ms returns 0
TA260 000:532.740 JLINK_WriteReg(R3, 0x00000000)
TA260 000:532.744 - 0.004ms returns 0
TA260 000:532.749 JLINK_WriteReg(R4, 0x00000000)
TA260 000:532.752 - 0.003ms returns 0
TA260 000:532.757 JLINK_WriteReg(R5, 0x00000000)
TA260 000:532.764 - 0.007ms returns 0
TA260 000:532.770 JLINK_WriteReg(R6, 0x00000000)
TA260 000:532.774 - 0.003ms returns 0
TA260 000:532.778 JLINK_WriteReg(R7, 0x00000000)
TA260 000:532.781 - 0.003ms returns 0
TA260 000:532.798 JLINK_WriteReg(R8, 0x00000000)
TA260 000:532.801 - 0.016ms returns 0
TA260 000:532.806 JLINK_WriteReg(R9, 0x20000180)
TA260 000:532.809 - 0.003ms returns 0
TA260 000:532.813 JLINK_WriteReg(R10, 0x00000000)
TA260 000:532.816 - 0.003ms returns 0
TA260 000:532.821 JLINK_WriteReg(R11, 0x00000000)
TA260 000:532.824 - 0.003ms returns 0
TA260 000:532.828 JLINK_WriteReg(R12, 0x00000000)
TA260 000:532.831 - 0.003ms returns 0
TA260 000:532.835 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:532.840 - 0.004ms returns 0
TA260 000:532.844 JLINK_WriteReg(R14, 0x20000001)
TA260 000:532.847 - 0.003ms returns 0
TA260 000:532.855 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 000:532.859 - 0.007ms returns 0
TA260 000:532.863 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:532.866 - 0.003ms returns 0
TA260 000:532.870 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:532.874 - 0.003ms returns 0
TA260 000:532.878 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:532.882 - 0.003ms returns 0
TA260 000:532.886 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:532.889 - 0.003ms returns 0
TA260 000:532.894 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:532.902   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:533.535 - 0.641ms returns 0x00000001
TA260 000:533.549 JLINK_Go()
TA260 000:533.555   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 000:534.087   CPU_ReadMem(4 bytes @ 0x********)
TA260 000:534.708   CPU_WriteMem(4 bytes @ 0xE0002008)
TA260 000:534.723   CPU_WriteMem(4 bytes @ 0xE000200C)
TA260 000:534.729   CPU_WriteMem(4 bytes @ 0xE0002010)
TA260 000:534.734   CPU_WriteMem(4 bytes @ 0xE0002014)
TA260 000:534.739   CPU_WriteMem(4 bytes @ 0xE0002018)
TA260 000:534.749   CPU_WriteMem(4 bytes @ 0xE000201C)
TA260 000:535.946   CPU_WriteMem(4 bytes @ 0xE0001004)
TA260 000:540.512   Memory map 'after startup completion point' is active
TA260 000:540.537 - 6.988ms 
TA260 000:540.546 JLINK_IsHalted()
TA260 000:542.953   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:543.474 - 2.927ms returns TRUE
TA260 000:543.495 JLINK_ReadReg(R15 (PC))
TA260 000:543.503 - 0.008ms returns 0x20000000
TA260 000:543.508 JLINK_ClrBPEx(BPHandle = 0x00000001)
TA260 000:543.512 - 0.004ms returns 0x00
TA260 000:543.517 JLINK_ReadReg(R0)
TA260 000:543.520 - 0.003ms returns 0x00000000
TA260 000:544.026 JLINK_HasError()
TA260 000:544.042 JLINK_WriteReg(R0, 0x08000000)
TA260 000:544.048 - 0.006ms returns 0
TA260 000:544.053 JLINK_WriteReg(R1, 0x00004000)
TA260 000:544.057 - 0.003ms returns 0
TA260 000:544.061 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:544.064 - 0.003ms returns 0
TA260 000:544.068 JLINK_WriteReg(R3, 0x00000000)
TA260 000:544.072 - 0.003ms returns 0
TA260 000:544.076 JLINK_WriteReg(R4, 0x00000000)
TA260 000:544.079 - 0.003ms returns 0
TA260 000:544.083 JLINK_WriteReg(R5, 0x00000000)
TA260 000:544.087 - 0.003ms returns 0
TA260 000:544.091 JLINK_WriteReg(R6, 0x00000000)
TA260 000:544.112 - 0.020ms returns 0
TA260 000:544.116 JLINK_WriteReg(R7, 0x00000000)
TA260 000:544.121 - 0.004ms returns 0
TA260 000:544.126 JLINK_WriteReg(R8, 0x00000000)
TA260 000:544.130 - 0.003ms returns 0
TA260 000:544.134 JLINK_WriteReg(R9, 0x20000180)
TA260 000:544.137 - 0.003ms returns 0
TA260 000:544.141 JLINK_WriteReg(R10, 0x00000000)
TA260 000:544.145 - 0.003ms returns 0
TA260 000:544.149 JLINK_WriteReg(R11, 0x00000000)
TA260 000:544.152 - 0.003ms returns 0
TA260 000:544.156 JLINK_WriteReg(R12, 0x00000000)
TA260 000:544.160 - 0.003ms returns 0
TA260 000:544.164 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:544.168 - 0.004ms returns 0
TA260 000:544.172 JLINK_WriteReg(R14, 0x20000001)
TA260 000:544.175 - 0.003ms returns 0
TA260 000:544.179 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 000:544.183 - 0.003ms returns 0
TA260 000:544.187 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:544.191 - 0.003ms returns 0
TA260 000:544.247 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:544.256 - 0.009ms returns 0
TA260 000:544.261 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:544.265 - 0.004ms returns 0
TA260 000:544.270 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:544.273 - 0.003ms returns 0
TA260 000:544.278 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:544.282 - 0.004ms returns 0x00000002
TA260 000:544.287 JLINK_Go()
TA260 000:544.299   CPU_ReadMem(4 bytes @ 0x********)
TA260 000:547.015 - 2.727ms 
TA260 000:547.028 JLINK_IsHalted()
TA260 000:549.416   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:549.892 - 2.863ms returns TRUE
TA260 000:549.909 JLINK_ReadReg(R15 (PC))
TA260 000:549.916 - 0.007ms returns 0x20000000
TA260 000:549.920 JLINK_ClrBPEx(BPHandle = 0x00000002)
TA260 000:549.925 - 0.004ms returns 0x00
TA260 000:549.930 JLINK_ReadReg(R0)
TA260 000:549.933 - 0.003ms returns 0x00000001
TA260 000:549.938 JLINK_HasError()
TA260 000:549.943 JLINK_WriteReg(R0, 0x08000000)
TA260 000:549.947 - 0.003ms returns 0
TA260 000:549.952 JLINK_WriteReg(R1, 0x00004000)
TA260 000:549.955 - 0.003ms returns 0
TA260 000:549.959 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:549.963 - 0.003ms returns 0
TA260 000:549.967 JLINK_WriteReg(R3, 0x00000000)
TA260 000:549.970 - 0.003ms returns 0
TA260 000:549.975 JLINK_WriteReg(R4, 0x00000000)
TA260 000:549.978 - 0.003ms returns 0
TA260 000:549.982 JLINK_WriteReg(R5, 0x00000000)
TA260 000:549.986 - 0.003ms returns 0
TA260 000:549.990 JLINK_WriteReg(R6, 0x00000000)
TA260 000:549.993 - 0.003ms returns 0
TA260 000:550.001 JLINK_WriteReg(R7, 0x00000000)
TA260 000:550.004 - 0.003ms returns 0
TA260 000:550.009 JLINK_WriteReg(R8, 0x00000000)
TA260 000:550.012 - 0.003ms returns 0
TA260 000:550.016 JLINK_WriteReg(R9, 0x20000180)
TA260 000:550.019 - 0.003ms returns 0
TA260 000:550.024 JLINK_WriteReg(R10, 0x00000000)
TA260 000:550.027 - 0.003ms returns 0
TA260 000:550.031 JLINK_WriteReg(R11, 0x00000000)
TA260 000:550.034 - 0.003ms returns 0
TA260 000:550.038 JLINK_WriteReg(R12, 0x00000000)
TA260 000:550.042 - 0.003ms returns 0
TA260 000:550.046 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:550.050 - 0.003ms returns 0
TA260 000:550.054 JLINK_WriteReg(R14, 0x20000001)
TA260 000:550.057 - 0.003ms returns 0
TA260 000:550.061 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 000:550.065 - 0.003ms returns 0
TA260 000:550.069 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:550.072 - 0.003ms returns 0
TA260 000:550.076 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:550.080 - 0.003ms returns 0
TA260 000:550.084 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:550.087 - 0.003ms returns 0
TA260 000:550.092 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:550.095 - 0.003ms returns 0
TA260 000:550.100 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:550.104 - 0.004ms returns 0x00000003
TA260 000:550.108 JLINK_Go()
TA260 000:550.116   CPU_ReadMem(4 bytes @ 0x********)
TA260 000:552.863 - 2.754ms 
TA260 000:552.878 JLINK_IsHalted()
TA260 000:553.528 - 0.648ms returns FALSE
TA260 000:553.550 JLINK_HasError()
TA260 000:564.344 JLINK_IsHalted()
TA260 000:564.853 - 0.508ms returns FALSE
TA260 000:564.865 JLINK_HasError()
TA260 000:566.337 JLINK_IsHalted()
TA260 000:566.893 - 0.555ms returns FALSE
TA260 000:566.900 JLINK_HasError()
TA260 000:568.331 JLINK_IsHalted()
TA260 000:568.816 - 0.484ms returns FALSE
TA260 000:568.824 JLINK_HasError()
TA260 000:570.335 JLINK_IsHalted()
TA260 000:570.852 - 0.517ms returns FALSE
TA260 000:570.860 JLINK_HasError()
TA260 000:572.335 JLINK_IsHalted()
TA260 000:572.819 - 0.483ms returns FALSE
TA260 000:572.828 JLINK_HasError()
TA260 000:574.848 JLINK_IsHalted()
TA260 000:575.430 - 0.582ms returns FALSE
TA260 000:575.450 JLINK_HasError()
TA260 000:576.845 JLINK_IsHalted()
TA260 000:577.410 - 0.565ms returns FALSE
TA260 000:577.421 JLINK_HasError()
TA260 000:578.848 JLINK_IsHalted()
TA260 000:579.317 - 0.468ms returns FALSE
TA260 000:579.324 JLINK_HasError()
TA260 000:580.848 JLINK_IsHalted()
TA260 000:581.347 - 0.499ms returns FALSE
TA260 000:581.368 JLINK_HasError()
TA260 000:583.350 JLINK_IsHalted()
TA260 000:583.883 - 0.532ms returns FALSE
TA260 000:583.896 JLINK_HasError()
TA260 000:585.355 JLINK_IsHalted()
TA260 000:585.885 - 0.530ms returns FALSE
TA260 000:585.893 JLINK_HasError()
TA260 000:587.352 JLINK_IsHalted()
TA260 000:587.837 - 0.484ms returns FALSE
TA260 000:587.842 JLINK_HasError()
TA260 000:589.349 JLINK_IsHalted()
TA260 000:589.836 - 0.486ms returns FALSE
TA260 000:589.841 JLINK_HasError()
TA260 000:591.345 JLINK_IsHalted()
TA260 000:591.823 - 0.477ms returns FALSE
TA260 000:591.828 JLINK_HasError()
TA260 000:593.854 JLINK_IsHalted()
TA260 000:594.364 - 0.509ms returns FALSE
TA260 000:594.377 JLINK_HasError()
TA260 000:595.859 JLINK_IsHalted()
TA260 000:596.315 - 0.455ms returns FALSE
TA260 000:596.321 JLINK_HasError()
TA260 000:597.855 JLINK_IsHalted()
TA260 000:598.344 - 0.488ms returns FALSE
TA260 000:598.349 JLINK_HasError()
TA260 000:599.853 JLINK_IsHalted()
TA260 000:600.298 - 0.445ms returns FALSE
TA260 000:600.305 JLINK_HasError()
TA260 000:602.355 JLINK_IsHalted()
TA260 000:602.871 - 0.515ms returns FALSE
TA260 000:602.876 JLINK_HasError()
TA260 000:604.363 JLINK_IsHalted()
TA260 000:604.833 - 0.470ms returns FALSE
TA260 000:604.845 JLINK_HasError()
TA260 000:606.387 JLINK_IsHalted()
TA260 000:606.885 - 0.497ms returns FALSE
TA260 000:606.891 JLINK_HasError()
TA260 000:608.359 JLINK_IsHalted()
TA260 000:608.835 - 0.475ms returns FALSE
TA260 000:608.841 JLINK_HasError()
TA260 000:610.361 JLINK_IsHalted()
TA260 000:610.859 - 0.498ms returns FALSE
TA260 000:610.866 JLINK_HasError()
TA260 000:612.360 JLINK_IsHalted()
TA260 000:612.844 - 0.483ms returns FALSE
TA260 000:612.855 JLINK_HasError()
TA260 000:614.868 JLINK_IsHalted()
TA260 000:615.418 - 0.549ms returns FALSE
TA260 000:615.425 JLINK_HasError()
TA260 000:616.865 JLINK_IsHalted()
TA260 000:617.404 - 0.538ms returns FALSE
TA260 000:617.410 JLINK_HasError()
TA260 000:618.867 JLINK_IsHalted()
TA260 000:619.417 - 0.550ms returns FALSE
TA260 000:619.424 JLINK_HasError()
TA260 000:620.867 JLINK_IsHalted()
TA260 000:621.372 - 0.504ms returns FALSE
TA260 000:621.378 JLINK_HasError()
TA260 000:623.373 JLINK_IsHalted()
TA260 000:623.805 - 0.431ms returns FALSE
TA260 000:623.812 JLINK_HasError()
TA260 000:625.198 JLINK_IsHalted()
TA260 000:625.699 - 0.501ms returns FALSE
TA260 000:625.706 JLINK_HasError()
TA260 000:627.195 JLINK_IsHalted()
TA260 000:627.691 - 0.495ms returns FALSE
TA260 000:627.697 JLINK_HasError()
TA260 000:629.195 JLINK_IsHalted()
TA260 000:629.697 - 0.501ms returns FALSE
TA260 000:629.703 JLINK_HasError()
TA260 000:631.197 JLINK_IsHalted()
TA260 000:631.690 - 0.493ms returns FALSE
TA260 000:631.696 JLINK_HasError()
TA260 000:633.701 JLINK_IsHalted()
TA260 000:634.191 - 0.490ms returns FALSE
TA260 000:634.198 JLINK_HasError()
TA260 000:635.703 JLINK_IsHalted()
TA260 000:636.283 - 0.579ms returns FALSE
TA260 000:636.297 JLINK_HasError()
TA260 000:638.705 JLINK_IsHalted()
TA260 000:639.279 - 0.574ms returns FALSE
TA260 000:639.286 JLINK_HasError()
TA260 000:640.705 JLINK_IsHalted()
TA260 000:641.198 - 0.493ms returns FALSE
TA260 000:641.205 JLINK_HasError()
TA260 000:643.205 JLINK_IsHalted()
TA260 000:643.649 - 0.443ms returns FALSE
TA260 000:643.663 JLINK_HasError()
TA260 000:645.209 JLINK_IsHalted()
TA260 000:645.691 - 0.482ms returns FALSE
TA260 000:645.697 JLINK_HasError()
TA260 000:647.208 JLINK_IsHalted()
TA260 000:647.711 - 0.503ms returns FALSE
TA260 000:647.718 JLINK_HasError()
TA260 000:649.210 JLINK_IsHalted()
TA260 000:649.688 - 0.477ms returns FALSE
TA260 000:649.694 JLINK_HasError()
TA260 000:651.206 JLINK_IsHalted()
TA260 000:651.627 - 0.420ms returns FALSE
TA260 000:651.635 JLINK_HasError()
TA260 000:652.710 JLINK_IsHalted()
TA260 000:653.191 - 0.480ms returns FALSE
TA260 000:653.197 JLINK_HasError()
TA260 000:654.719 JLINK_IsHalted()
TA260 000:655.271 - 0.551ms returns FALSE
TA260 000:655.277 JLINK_HasError()
TA260 000:656.715 JLINK_IsHalted()
TA260 000:657.276 - 0.561ms returns FALSE
TA260 000:657.282 JLINK_HasError()
TA260 000:658.715 JLINK_IsHalted()
TA260 000:659.203 - 0.487ms returns FALSE
TA260 000:659.208 JLINK_HasError()
TA260 000:660.720 JLINK_IsHalted()
TA260 000:661.201 - 0.480ms returns FALSE
TA260 000:661.207 JLINK_HasError()
TA260 000:663.220 JLINK_IsHalted()
TA260 000:663.797 - 0.576ms returns FALSE
TA260 000:663.807 JLINK_HasError()
TA260 000:665.223 JLINK_IsHalted()
TA260 000:665.668 - 0.444ms returns FALSE
TA260 000:665.673 JLINK_HasError()
TA260 000:667.223 JLINK_IsHalted()
TA260 000:667.734 - 0.510ms returns FALSE
TA260 000:667.740 JLINK_HasError()
TA260 000:669.823 JLINK_IsHalted()
TA260 000:670.270 - 0.446ms returns FALSE
TA260 000:670.276 JLINK_HasError()
TA260 000:672.325 JLINK_IsHalted()
TA260 000:672.790 - 0.465ms returns FALSE
TA260 000:672.796 JLINK_HasError()
TA260 000:674.333 JLINK_IsHalted()
TA260 000:674.875 - 0.541ms returns FALSE
TA260 000:674.888 JLINK_HasError()
TA260 000:676.335 JLINK_IsHalted()
TA260 000:676.753 - 0.418ms returns FALSE
TA260 000:676.760 JLINK_HasError()
TA260 000:678.330 JLINK_IsHalted()
TA260 000:678.802 - 0.471ms returns FALSE
TA260 000:678.807 JLINK_HasError()
TA260 000:680.333 JLINK_IsHalted()
TA260 000:680.799 - 0.466ms returns FALSE
TA260 000:680.807 JLINK_HasError()
TA260 000:682.331 JLINK_IsHalted()
TA260 000:682.850 - 0.518ms returns FALSE
TA260 000:682.863 JLINK_HasError()
TA260 000:686.840 JLINK_IsHalted()
TA260 000:687.346 - 0.505ms returns FALSE
TA260 000:687.353 JLINK_HasError()
TA260 000:688.838 JLINK_IsHalted()
TA260 000:689.350 - 0.511ms returns FALSE
TA260 000:689.356 JLINK_HasError()
TA260 000:690.842 JLINK_IsHalted()
TA260 000:691.298 - 0.456ms returns FALSE
TA260 000:691.304 JLINK_HasError()
TA260 000:692.340 JLINK_IsHalted()
TA260 000:692.820 - 0.480ms returns FALSE
TA260 000:692.826 JLINK_HasError()
TA260 000:694.350 JLINK_IsHalted()
TA260 000:694.864 - 0.513ms returns FALSE
TA260 000:694.870 JLINK_HasError()
TA260 000:696.348 JLINK_IsHalted()
TA260 000:696.835 - 0.487ms returns FALSE
TA260 000:696.842 JLINK_HasError()
TA260 000:698.346 JLINK_IsHalted()
TA260 000:698.876 - 0.529ms returns FALSE
TA260 000:698.885 JLINK_HasError()
TA260 000:700.347 JLINK_IsHalted()
TA260 000:700.837 - 0.490ms returns FALSE
TA260 000:700.845 JLINK_HasError()
TA260 000:702.345 JLINK_IsHalted()
TA260 000:702.823 - 0.477ms returns FALSE
TA260 000:702.828 JLINK_HasError()
TA260 000:704.823 JLINK_IsHalted()
TA260 000:705.316 - 0.492ms returns FALSE
TA260 000:705.322 JLINK_HasError()
TA260 000:706.719 JLINK_IsHalted()
TA260 000:707.278 - 0.558ms returns FALSE
TA260 000:707.286 JLINK_HasError()
TA260 000:709.220 JLINK_IsHalted()
TA260 000:709.693 - 0.472ms returns FALSE
TA260 000:709.703 JLINK_HasError()
TA260 000:711.221 JLINK_IsHalted()
TA260 000:711.690 - 0.468ms returns FALSE
TA260 000:711.695 JLINK_HasError()
TA260 000:713.732 JLINK_IsHalted()
TA260 000:714.347 - 0.614ms returns FALSE
TA260 000:714.356 JLINK_HasError()
TA260 000:715.729 JLINK_IsHalted()
TA260 000:716.190 - 0.461ms returns FALSE
TA260 000:716.196 JLINK_HasError()
TA260 000:717.727 JLINK_IsHalted()
TA260 000:718.188 - 0.460ms returns FALSE
TA260 000:718.193 JLINK_HasError()
TA260 000:719.727 JLINK_IsHalted()
TA260 000:720.268 - 0.540ms returns FALSE
TA260 000:720.274 JLINK_HasError()
TA260 000:722.230 JLINK_IsHalted()
TA260 000:722.710 - 0.479ms returns FALSE
TA260 000:722.716 JLINK_HasError()
TA260 000:724.740 JLINK_IsHalted()
TA260 000:725.191 - 0.450ms returns FALSE
TA260 000:725.197 JLINK_HasError()
TA260 000:726.737 JLINK_IsHalted()
TA260 000:727.277 - 0.539ms returns FALSE
TA260 000:727.283 JLINK_HasError()
TA260 000:728.740 JLINK_IsHalted()
TA260 000:729.267 - 0.527ms returns FALSE
TA260 000:729.274 JLINK_HasError()
TA260 000:730.452 JLINK_IsHalted()
TA260 000:730.917 - 0.464ms returns FALSE
TA260 000:730.926 JLINK_HasError()
TA260 000:731.959 JLINK_IsHalted()
TA260 000:732.450 - 0.491ms returns FALSE
TA260 000:732.456 JLINK_HasError()
TA260 000:733.993 JLINK_IsHalted()
TA260 000:734.501 - 0.507ms returns FALSE
TA260 000:734.508 JLINK_HasError()
TA260 000:735.964 JLINK_IsHalted()
TA260 000:736.417 - 0.452ms returns FALSE
TA260 000:736.422 JLINK_HasError()
TA260 000:737.962 JLINK_IsHalted()
TA260 000:738.414 - 0.451ms returns FALSE
TA260 000:738.419 JLINK_HasError()
TA260 000:739.964 JLINK_IsHalted()
TA260 000:740.440 - 0.475ms returns FALSE
TA260 000:740.446 JLINK_HasError()
TA260 000:742.375 JLINK_IsHalted()
TA260 000:742.871 - 0.495ms returns FALSE
TA260 000:742.878 JLINK_HasError()
TA260 000:744.888 JLINK_IsHalted()
TA260 000:745.490 - 0.601ms returns FALSE
TA260 000:745.498 JLINK_HasError()
TA260 000:747.937 JLINK_IsHalted()
TA260 000:748.407 - 0.469ms returns FALSE
TA260 000:748.419 JLINK_HasError()
TA260 000:749.934 JLINK_IsHalted()
TA260 000:750.418 - 0.483ms returns FALSE
TA260 000:750.424 JLINK_HasError()
TA260 000:752.437 JLINK_IsHalted()
TA260 000:752.938 - 0.500ms returns FALSE
TA260 000:752.944 JLINK_HasError()
TA260 000:754.450 JLINK_IsHalted()
TA260 000:754.938 - 0.487ms returns FALSE
TA260 000:754.945 JLINK_HasError()
TA260 000:756.445 JLINK_IsHalted()
TA260 000:756.963 - 0.517ms returns FALSE
TA260 000:756.969 JLINK_HasError()
TA260 000:758.443 JLINK_IsHalted()
TA260 000:758.937 - 0.493ms returns FALSE
TA260 000:758.943 JLINK_HasError()
TA260 000:760.446 JLINK_IsHalted()
TA260 000:760.969 - 0.522ms returns FALSE
TA260 000:760.978 JLINK_HasError()
TA260 000:762.945 JLINK_IsHalted()
TA260 000:763.455 - 0.509ms returns FALSE
TA260 000:763.465 JLINK_HasError()
TA260 000:764.955 JLINK_IsHalted()
TA260 000:765.419 - 0.464ms returns FALSE
TA260 000:765.431 JLINK_HasError()
TA260 000:766.949 JLINK_IsHalted()
TA260 000:767.451 - 0.502ms returns FALSE
TA260 000:767.457 JLINK_HasError()
TA260 000:768.951 JLINK_IsHalted()
TA260 000:769.451 - 0.499ms returns FALSE
TA260 000:769.457 JLINK_HasError()
TA260 000:770.948 JLINK_IsHalted()
TA260 000:771.415 - 0.466ms returns FALSE
TA260 000:771.420 JLINK_HasError()
TA260 000:772.451 JLINK_IsHalted()
TA260 000:772.938 - 0.487ms returns FALSE
TA260 000:772.944 JLINK_HasError()
TA260 000:774.458 JLINK_IsHalted()
TA260 000:774.963 - 0.504ms returns FALSE
TA260 000:774.969 JLINK_HasError()
TA260 000:776.455 JLINK_IsHalted()
TA260 000:777.000 - 0.544ms returns FALSE
TA260 000:777.009 JLINK_HasError()
TA260 000:778.455 JLINK_IsHalted()
TA260 000:778.939 - 0.483ms returns FALSE
TA260 000:778.945 JLINK_HasError()
TA260 000:780.458 JLINK_IsHalted()
TA260 000:780.940 - 0.481ms returns FALSE
TA260 000:780.949 JLINK_HasError()
TA260 000:782.958 JLINK_IsHalted()
TA260 000:783.453 - 0.494ms returns FALSE
TA260 000:783.466 JLINK_HasError()
TA260 000:784.972 JLINK_IsHalted()
TA260 000:785.511 - 0.538ms returns FALSE
TA260 000:785.521 JLINK_HasError()
TA260 000:786.963 JLINK_IsHalted()
TA260 000:787.417 - 0.454ms returns FALSE
TA260 000:787.423 JLINK_HasError()
TA260 000:788.965 JLINK_IsHalted()
TA260 000:789.462 - 0.497ms returns FALSE
TA260 000:789.470 JLINK_HasError()
TA260 000:790.963 JLINK_IsHalted()
TA260 000:791.451 - 0.488ms returns FALSE
TA260 000:791.457 JLINK_HasError()
TA260 000:793.473 JLINK_IsHalted()
TA260 000:794.037 - 0.563ms returns FALSE
TA260 000:794.051 JLINK_HasError()
TA260 000:795.474 JLINK_IsHalted()
TA260 000:795.953 - 0.478ms returns FALSE
TA260 000:795.966 JLINK_HasError()
TA260 000:797.472 JLINK_IsHalted()
TA260 000:797.997 - 0.524ms returns FALSE
TA260 000:798.002 JLINK_HasError()
TA260 000:799.473 JLINK_IsHalted()
TA260 000:799.961 - 0.488ms returns FALSE
TA260 000:799.967 JLINK_HasError()
TA260 000:801.974 JLINK_IsHalted()
TA260 000:802.461 - 0.486ms returns FALSE
TA260 000:802.467 JLINK_HasError()
TA260 000:803.982 JLINK_IsHalted()
TA260 000:804.464 - 0.482ms returns FALSE
TA260 000:804.471 JLINK_HasError()
TA260 000:805.981 JLINK_IsHalted()
TA260 000:806.495 - 0.513ms returns FALSE
TA260 000:806.501 JLINK_HasError()
TA260 000:807.978 JLINK_IsHalted()
TA260 000:808.422 - 0.442ms returns FALSE
TA260 000:808.483 JLINK_HasError()
TA260 000:809.984 JLINK_IsHalted()
TA260 000:810.407 - 0.422ms returns FALSE
TA260 000:810.417 JLINK_HasError()
TA260 000:811.980 JLINK_IsHalted()
TA260 000:812.449 - 0.469ms returns FALSE
TA260 000:812.455 JLINK_HasError()
TA260 000:813.486 JLINK_IsHalted()
TA260 000:814.146 - 0.659ms returns FALSE
TA260 000:814.157 JLINK_HasError()
TA260 000:815.489 JLINK_IsHalted()
TA260 000:815.983 - 0.493ms returns FALSE
TA260 000:815.989 JLINK_HasError()
TA260 000:817.489 JLINK_IsHalted()
TA260 000:817.984 - 0.495ms returns FALSE
TA260 000:817.991 JLINK_HasError()
TA260 000:819.487 JLINK_IsHalted()
TA260 000:819.962 - 0.474ms returns FALSE
TA260 000:819.967 JLINK_HasError()
TA260 000:821.991 JLINK_IsHalted()
TA260 000:822.506 - 0.514ms returns FALSE
TA260 000:822.513 JLINK_HasError()
TA260 000:824.508 JLINK_IsHalted()
TA260 000:824.952 - 0.443ms returns FALSE
TA260 000:824.964 JLINK_HasError()
TA260 000:826.507 JLINK_IsHalted()
TA260 000:826.959 - 0.452ms returns FALSE
TA260 000:826.969 JLINK_HasError()
TA260 000:828.503 JLINK_IsHalted()
TA260 000:828.998 - 0.494ms returns FALSE
TA260 000:829.004 JLINK_HasError()
TA260 000:830.506 JLINK_IsHalted()
TA260 000:830.984 - 0.478ms returns FALSE
TA260 000:830.990 JLINK_HasError()
TA260 000:833.007 JLINK_IsHalted()
TA260 000:833.483 - 0.475ms returns FALSE
TA260 000:833.489 JLINK_HasError()
TA260 000:835.043 JLINK_IsHalted()
TA260 000:835.575 - 0.531ms returns FALSE
TA260 000:835.581 JLINK_HasError()
TA260 000:837.010 JLINK_IsHalted()
TA260 000:837.506 - 0.495ms returns FALSE
TA260 000:837.512 JLINK_HasError()
TA260 000:839.053 JLINK_IsHalted()
TA260 000:839.594 - 0.540ms returns FALSE
TA260 000:839.606 JLINK_HasError()
TA260 000:841.012 JLINK_IsHalted()
TA260 000:841.498 - 0.485ms returns FALSE
TA260 000:841.508 JLINK_HasError()
TA260 000:843.316 JLINK_IsHalted()
TA260 000:844.034 - 0.718ms returns FALSE
TA260 000:844.047 JLINK_HasError()
TA260 000:845.823 JLINK_IsHalted()
TA260 000:846.457 - 0.634ms returns FALSE
TA260 000:846.465 JLINK_HasError()
TA260 000:847.841 JLINK_IsHalted()
TA260 000:848.299 - 0.457ms returns FALSE
TA260 000:848.306 JLINK_HasError()
TA260 000:849.819 JLINK_IsHalted()
TA260 000:850.303 - 0.483ms returns FALSE
TA260 000:850.309 JLINK_HasError()
TA260 000:852.322 JLINK_IsHalted()
TA260 000:852.802 - 0.479ms returns FALSE
TA260 000:852.811 JLINK_HasError()
TA260 000:854.329 JLINK_IsHalted()
TA260 000:854.866 - 0.537ms returns FALSE
TA260 000:854.879 JLINK_HasError()
TA260 000:856.329 JLINK_IsHalted()
TA260 000:856.815 - 0.485ms returns FALSE
TA260 000:856.822 JLINK_HasError()
TA260 000:858.325 JLINK_IsHalted()
TA260 000:858.780 - 0.454ms returns FALSE
TA260 000:858.787 JLINK_HasError()
TA260 000:860.328 JLINK_IsHalted()
TA260 000:860.884 - 0.555ms returns FALSE
TA260 000:860.897 JLINK_HasError()
TA260 000:862.344 JLINK_IsHalted()
TA260 000:862.875 - 0.531ms returns FALSE
TA260 000:862.885 JLINK_HasError()
TA260 000:864.839 JLINK_IsHalted()
TA260 000:865.408 - 0.568ms returns FALSE
TA260 000:865.415 JLINK_HasError()
TA260 000:866.830 JLINK_IsHalted()
TA260 000:867.300 - 0.469ms returns FALSE
TA260 000:867.306 JLINK_HasError()
TA260 000:868.834 JLINK_IsHalted()
TA260 000:869.350 - 0.516ms returns FALSE
TA260 000:869.360 JLINK_HasError()
TA260 000:870.836 JLINK_IsHalted()
TA260 000:871.306 - 0.469ms returns FALSE
TA260 000:871.317 JLINK_HasError()
TA260 000:873.340 JLINK_IsHalted()
TA260 000:873.788 - 0.447ms returns FALSE
TA260 000:873.803 JLINK_HasError()
TA260 000:875.346 JLINK_IsHalted()
TA260 000:875.798 - 0.451ms returns FALSE
TA260 000:875.809 JLINK_HasError()
TA260 000:877.341 JLINK_IsHalted()
TA260 000:877.850 - 0.509ms returns FALSE
TA260 000:877.858 JLINK_HasError()
TA260 000:879.344 JLINK_IsHalted()
TA260 000:879.825 - 0.481ms returns FALSE
TA260 000:879.832 JLINK_HasError()
TA260 000:881.342 JLINK_IsHalted()
TA260 000:881.837 - 0.495ms returns FALSE
TA260 000:881.843 JLINK_HasError()
TA260 000:883.853 JLINK_IsHalted()
TA260 000:884.308 - 0.454ms returns FALSE
TA260 000:884.322 JLINK_HasError()
TA260 000:885.854 JLINK_IsHalted()
TA260 000:886.308 - 0.453ms returns FALSE
TA260 000:886.322 JLINK_HasError()
TA260 000:887.702 JLINK_IsHalted()
TA260 000:888.157 - 0.454ms returns FALSE
TA260 000:888.164 JLINK_HasError()
TA260 000:889.203 JLINK_IsHalted()
TA260 000:889.719 - 0.515ms returns FALSE
TA260 000:889.725 JLINK_HasError()
TA260 000:891.217 JLINK_IsHalted()
TA260 000:891.694 - 0.476ms returns FALSE
TA260 000:891.702 JLINK_HasError()
TA260 000:893.713 JLINK_IsHalted()
TA260 000:894.184 - 0.471ms returns FALSE
TA260 000:894.197 JLINK_HasError()
TA260 000:895.717 JLINK_IsHalted()
TA260 000:896.277 - 0.559ms returns FALSE
TA260 000:896.283 JLINK_HasError()
TA260 000:897.709 JLINK_IsHalted()
TA260 000:898.268 - 0.558ms returns FALSE
TA260 000:898.274 JLINK_HasError()
TA260 000:899.711 JLINK_IsHalted()
TA260 000:900.196 - 0.485ms returns FALSE
TA260 000:900.202 JLINK_HasError()
TA260 000:902.218 JLINK_IsHalted()
TA260 000:902.699 - 0.481ms returns FALSE
TA260 000:902.705 JLINK_HasError()
TA260 000:904.221 JLINK_IsHalted()
TA260 000:904.718 - 0.497ms returns FALSE
TA260 000:904.732 JLINK_HasError()
TA260 000:906.220 JLINK_IsHalted()
TA260 000:906.699 - 0.479ms returns FALSE
TA260 000:906.705 JLINK_HasError()
TA260 000:908.218 JLINK_IsHalted()
TA260 000:908.664 - 0.445ms returns FALSE
TA260 000:908.670 JLINK_HasError()
TA260 000:910.218 JLINK_IsHalted()
TA260 000:910.708 - 0.489ms returns FALSE
TA260 000:910.714 JLINK_HasError()
TA260 000:912.219 JLINK_IsHalted()
TA260 000:912.691 - 0.472ms returns FALSE
TA260 000:912.697 JLINK_HasError()
TA260 000:914.733 JLINK_IsHalted()
TA260 000:915.195 - 0.461ms returns FALSE
TA260 000:915.209 JLINK_HasError()
TA260 000:916.728 JLINK_IsHalted()
TA260 000:917.272 - 0.542ms returns FALSE
TA260 000:917.286 JLINK_HasError()
TA260 000:918.731 JLINK_IsHalted()
TA260 000:919.273 - 0.542ms returns FALSE
TA260 000:919.281 JLINK_HasError()
TA260 000:920.728 JLINK_IsHalted()
TA260 000:921.268 - 0.539ms returns FALSE
TA260 000:921.275 JLINK_HasError()
TA260 000:923.744 JLINK_IsHalted()
TA260 000:924.271 - 0.525ms returns FALSE
TA260 000:924.279 JLINK_HasError()
TA260 000:925.748 JLINK_IsHalted()
TA260 000:926.281 - 0.531ms returns FALSE
TA260 000:926.289 JLINK_HasError()
TA260 000:927.746 JLINK_IsHalted()
TA260 000:928.268 - 0.522ms returns FALSE
TA260 000:928.274 JLINK_HasError()
TA260 000:929.744 JLINK_IsHalted()
TA260 000:930.277 - 0.532ms returns FALSE
TA260 000:930.290 JLINK_HasError()
TA260 000:932.250 JLINK_IsHalted()
TA260 000:932.735 - 0.484ms returns FALSE
TA260 000:932.741 JLINK_HasError()
TA260 000:934.253 JLINK_IsHalted()
TA260 000:934.712 - 0.458ms returns FALSE
TA260 000:934.721 JLINK_HasError()
TA260 000:936.254 JLINK_IsHalted()
TA260 000:936.745 - 0.490ms returns FALSE
TA260 000:936.755 JLINK_HasError()
TA260 000:938.250 JLINK_IsHalted()
TA260 000:938.711 - 0.461ms returns FALSE
TA260 000:938.716 JLINK_HasError()
TA260 000:940.250 JLINK_IsHalted()
TA260 000:940.747 - 0.496ms returns FALSE
TA260 000:940.753 JLINK_HasError()
TA260 000:942.251 JLINK_IsHalted()
TA260 000:942.745 - 0.493ms returns FALSE
TA260 000:942.751 JLINK_HasError()
TA260 000:944.763 JLINK_IsHalted()
TA260 000:945.271 - 0.507ms returns FALSE
TA260 000:945.278 JLINK_HasError()
TA260 000:946.761 JLINK_IsHalted()
TA260 000:947.279 - 0.517ms returns FALSE
TA260 000:947.285 JLINK_HasError()
TA260 000:949.759 JLINK_IsHalted()
TA260 000:950.278 - 0.518ms returns FALSE
TA260 000:950.284 JLINK_HasError()
TA260 000:952.260 JLINK_IsHalted()
TA260 000:952.747 - 0.487ms returns FALSE
TA260 000:952.753 JLINK_HasError()
TA260 000:954.267 JLINK_IsHalted()
TA260 000:954.696 - 0.428ms returns FALSE
TA260 000:954.703 JLINK_HasError()
TA260 000:956.270 JLINK_IsHalted()
TA260 000:956.781 - 0.510ms returns FALSE
TA260 000:956.787 JLINK_HasError()
TA260 000:958.265 JLINK_IsHalted()
TA260 000:958.735 - 0.470ms returns FALSE
TA260 000:958.743 JLINK_HasError()
TA260 000:960.264 JLINK_IsHalted()
TA260 000:960.746 - 0.482ms returns FALSE
TA260 000:960.752 JLINK_HasError()
TA260 000:962.267 JLINK_IsHalted()
TA260 000:962.748 - 0.480ms returns FALSE
TA260 000:962.754 JLINK_HasError()
TA260 000:964.781 JLINK_IsHalted()
TA260 000:967.130   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:967.630 - 2.848ms returns TRUE
TA260 000:967.637 JLINK_ReadReg(R15 (PC))
TA260 000:967.643 - 0.005ms returns 0x20000000
TA260 000:967.647 JLINK_ClrBPEx(BPHandle = 0x00000003)
TA260 000:967.651 - 0.003ms returns 0x00
TA260 000:967.656 JLINK_ReadReg(R0)
TA260 000:967.659 - 0.003ms returns 0x00000000
TA260 000:968.064 JLINK_HasError()
TA260 000:968.075 JLINK_WriteReg(R0, 0x08004000)
TA260 000:968.079 - 0.004ms returns 0
TA260 000:968.084 JLINK_WriteReg(R1, 0x00004000)
TA260 000:968.087 - 0.003ms returns 0
TA260 000:968.091 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:968.094 - 0.003ms returns 0
TA260 000:968.098 JLINK_WriteReg(R3, 0x00000000)
TA260 000:968.102 - 0.003ms returns 0
TA260 000:968.106 JLINK_WriteReg(R4, 0x00000000)
TA260 000:968.109 - 0.003ms returns 0
TA260 000:968.113 JLINK_WriteReg(R5, 0x00000000)
TA260 000:968.117 - 0.003ms returns 0
TA260 000:968.121 JLINK_WriteReg(R6, 0x00000000)
TA260 000:968.124 - 0.003ms returns 0
TA260 000:968.128 JLINK_WriteReg(R7, 0x00000000)
TA260 000:968.132 - 0.003ms returns 0
TA260 000:968.136 JLINK_WriteReg(R8, 0x00000000)
TA260 000:968.139 - 0.003ms returns 0
TA260 000:968.143 JLINK_WriteReg(R9, 0x20000180)
TA260 000:968.146 - 0.003ms returns 0
TA260 000:968.151 JLINK_WriteReg(R10, 0x00000000)
TA260 000:968.154 - 0.003ms returns 0
TA260 000:968.158 JLINK_WriteReg(R11, 0x00000000)
TA260 000:968.161 - 0.003ms returns 0
TA260 000:968.165 JLINK_WriteReg(R12, 0x00000000)
TA260 000:968.169 - 0.003ms returns 0
TA260 000:968.173 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:968.177 - 0.004ms returns 0
TA260 000:968.181 JLINK_WriteReg(R14, 0x20000001)
TA260 000:968.185 - 0.003ms returns 0
TA260 000:968.189 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 000:968.192 - 0.003ms returns 0
TA260 000:968.196 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:968.200 - 0.003ms returns 0
TA260 000:968.204 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:968.207 - 0.003ms returns 0
TA260 000:968.211 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:968.215 - 0.003ms returns 0
TA260 000:968.219 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:968.222 - 0.003ms returns 0
TA260 000:968.226 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:968.231 - 0.004ms returns 0x00000004
TA260 000:968.235 JLINK_Go()
TA260 000:968.243   CPU_ReadMem(4 bytes @ 0x********)
TA260 000:971.029 - 2.794ms 
TA260 000:971.042 JLINK_IsHalted()
TA260 000:973.504   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 000:974.013 - 2.970ms returns TRUE
TA260 000:974.024 JLINK_ReadReg(R15 (PC))
TA260 000:974.030 - 0.005ms returns 0x20000000
TA260 000:974.034 JLINK_ClrBPEx(BPHandle = 0x00000004)
TA260 000:974.038 - 0.003ms returns 0x00
TA260 000:974.042 JLINK_ReadReg(R0)
TA260 000:974.046 - 0.003ms returns 0x00000001
TA260 000:974.050 JLINK_HasError()
TA260 000:974.055 JLINK_WriteReg(R0, 0x08004000)
TA260 000:974.059 - 0.003ms returns 0
TA260 000:974.063 JLINK_WriteReg(R1, 0x00004000)
TA260 000:974.067 - 0.003ms returns 0
TA260 000:974.071 JLINK_WriteReg(R2, 0x000000FF)
TA260 000:974.074 - 0.003ms returns 0
TA260 000:974.078 JLINK_WriteReg(R3, 0x00000000)
TA260 000:974.082 - 0.003ms returns 0
TA260 000:974.086 JLINK_WriteReg(R4, 0x00000000)
TA260 000:974.089 - 0.003ms returns 0
TA260 000:974.093 JLINK_WriteReg(R5, 0x00000000)
TA260 000:974.096 - 0.003ms returns 0
TA260 000:974.100 JLINK_WriteReg(R6, 0x00000000)
TA260 000:974.104 - 0.003ms returns 0
TA260 000:974.108 JLINK_WriteReg(R7, 0x00000000)
TA260 000:974.111 - 0.003ms returns 0
TA260 000:974.115 JLINK_WriteReg(R8, 0x00000000)
TA260 000:974.118 - 0.003ms returns 0
TA260 000:974.122 JLINK_WriteReg(R9, 0x20000180)
TA260 000:974.126 - 0.003ms returns 0
TA260 000:974.130 JLINK_WriteReg(R10, 0x00000000)
TA260 000:974.139 - 0.008ms returns 0
TA260 000:974.143 JLINK_WriteReg(R11, 0x00000000)
TA260 000:974.146 - 0.003ms returns 0
TA260 000:974.150 JLINK_WriteReg(R12, 0x00000000)
TA260 000:974.153 - 0.003ms returns 0
TA260 000:974.158 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 000:974.161 - 0.003ms returns 0
TA260 000:974.165 JLINK_WriteReg(R14, 0x20000001)
TA260 000:974.169 - 0.003ms returns 0
TA260 000:974.173 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 000:974.176 - 0.003ms returns 0
TA260 000:974.181 JLINK_WriteReg(XPSR, 0x01000000)
TA260 000:974.184 - 0.003ms returns 0
TA260 000:974.188 JLINK_WriteReg(MSP, 0x20001000)
TA260 000:974.192 - 0.003ms returns 0
TA260 000:974.196 JLINK_WriteReg(PSP, 0x20001000)
TA260 000:974.199 - 0.003ms returns 0
TA260 000:974.203 JLINK_WriteReg(CFBP, 0x00000000)
TA260 000:974.207 - 0.003ms returns 0
TA260 000:974.211 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 000:974.215 - 0.003ms returns 0x00000005
TA260 000:974.219 JLINK_Go()
TA260 000:974.226   CPU_ReadMem(4 bytes @ 0x********)
TA260 000:977.030 - 2.811ms 
TA260 000:977.041 JLINK_IsHalted()
TA260 000:977.543 - 0.502ms returns FALSE
TA260 000:977.553 JLINK_HasError()
TA260 000:979.281 JLINK_IsHalted()
TA260 000:979.746 - 0.465ms returns FALSE
TA260 000:979.754 JLINK_HasError()
TA260 000:981.279 JLINK_IsHalted()
TA260 000:981.770 - 0.491ms returns FALSE
TA260 000:981.777 JLINK_HasError()
TA260 000:983.797 JLINK_IsHalted()
TA260 000:984.280 - 0.483ms returns FALSE
TA260 000:984.329 JLINK_HasError()
TA260 000:985.790 JLINK_IsHalted()
TA260 000:986.315 - 0.524ms returns FALSE
TA260 000:986.321 JLINK_HasError()
TA260 000:987.787 JLINK_IsHalted()
TA260 000:988.276 - 0.488ms returns FALSE
TA260 000:988.281 JLINK_HasError()
TA260 000:989.787 JLINK_IsHalted()
TA260 000:990.266 - 0.478ms returns FALSE
TA260 000:990.272 JLINK_HasError()
TA260 000:992.289 JLINK_IsHalted()
TA260 000:992.814 - 0.524ms returns FALSE
TA260 000:992.820 JLINK_HasError()
TA260 000:994.297 JLINK_IsHalted()
TA260 000:994.782 - 0.485ms returns FALSE
TA260 000:994.795 JLINK_HasError()
TA260 000:996.297 JLINK_IsHalted()
TA260 000:996.815 - 0.517ms returns FALSE
TA260 000:996.821 JLINK_HasError()
TA260 000:998.293 JLINK_IsHalted()
TA260 000:998.789 - 0.495ms returns FALSE
TA260 000:998.794 JLINK_HasError()
TA260 001:000.293 JLINK_IsHalted()
TA260 001:000.779 - 0.486ms returns FALSE
TA260 001:000.786 JLINK_HasError()
TA260 001:002.294 JLINK_IsHalted()
TA260 001:002.814 - 0.520ms returns FALSE
TA260 001:002.820 JLINK_HasError()
TA260 001:004.805 JLINK_IsHalted()
TA260 001:005.278 - 0.472ms returns FALSE
TA260 001:005.285 JLINK_HasError()
TA260 001:006.802 JLINK_IsHalted()
TA260 001:007.313 - 0.511ms returns FALSE
TA260 001:007.320 JLINK_HasError()
TA260 001:008.799 JLINK_IsHalted()
TA260 001:009.285 - 0.485ms returns FALSE
TA260 001:009.299 JLINK_HasError()
TA260 001:010.808 JLINK_IsHalted()
TA260 001:011.282 - 0.474ms returns FALSE
TA260 001:011.291 JLINK_HasError()
TA260 001:013.309 JLINK_IsHalted()
TA260 001:013.782 - 0.472ms returns FALSE
TA260 001:013.791 JLINK_HasError()
TA260 001:015.315 JLINK_IsHalted()
TA260 001:015.797 - 0.482ms returns FALSE
TA260 001:015.806 JLINK_HasError()
TA260 001:017.311 JLINK_IsHalted()
TA260 001:017.798 - 0.487ms returns FALSE
TA260 001:017.807 JLINK_HasError()
TA260 001:019.310 JLINK_IsHalted()
TA260 001:019.746 - 0.436ms returns FALSE
TA260 001:019.752 JLINK_HasError()
TA260 001:021.308 JLINK_IsHalted()
TA260 001:021.816 - 0.507ms returns FALSE
TA260 001:021.822 JLINK_HasError()
TA260 001:023.822 JLINK_IsHalted()
TA260 001:024.323 - 0.500ms returns FALSE
TA260 001:024.339 JLINK_HasError()
TA260 001:025.826 JLINK_IsHalted()
TA260 001:026.308 - 0.482ms returns FALSE
TA260 001:026.316 JLINK_HasError()
TA260 001:027.821 JLINK_IsHalted()
TA260 001:028.320 - 0.498ms returns FALSE
TA260 001:028.329 JLINK_HasError()
TA260 001:029.821 JLINK_IsHalted()
TA260 001:030.318 - 0.497ms returns FALSE
TA260 001:030.326 JLINK_HasError()
TA260 001:032.323 JLINK_IsHalted()
TA260 001:032.785 - 0.461ms returns FALSE
TA260 001:032.792 JLINK_HasError()
TA260 001:034.329 JLINK_IsHalted()
TA260 001:034.799 - 0.470ms returns FALSE
TA260 001:034.819 JLINK_HasError()
TA260 001:036.330 JLINK_IsHalted()
TA260 001:036.844 - 0.513ms returns FALSE
TA260 001:036.850 JLINK_HasError()
TA260 001:038.326 JLINK_IsHalted()
TA260 001:038.830 - 0.503ms returns FALSE
TA260 001:038.839 JLINK_HasError()
TA260 001:040.331 JLINK_IsHalted()
TA260 001:040.798 - 0.466ms returns FALSE
TA260 001:040.811 JLINK_HasError()
TA260 001:042.326 JLINK_IsHalted()
TA260 001:042.838 - 0.512ms returns FALSE
TA260 001:042.847 JLINK_HasError()
TA260 001:044.843 JLINK_IsHalted()
TA260 001:045.424 - 0.580ms returns FALSE
TA260 001:045.432 JLINK_HasError()
TA260 001:046.835 JLINK_IsHalted()
TA260 001:047.283 - 0.447ms returns FALSE
TA260 001:047.292 JLINK_HasError()
TA260 001:048.835 JLINK_IsHalted()
TA260 001:049.374 - 0.539ms returns FALSE
TA260 001:049.384 JLINK_HasError()
TA260 001:050.835 JLINK_IsHalted()
TA260 001:051.363 - 0.527ms returns FALSE
TA260 001:051.372 JLINK_HasError()
TA260 001:053.341 JLINK_IsHalted()
TA260 001:053.818 - 0.477ms returns FALSE
TA260 001:053.826 JLINK_HasError()
TA260 001:055.341 JLINK_IsHalted()
TA260 001:055.806 - 0.464ms returns FALSE
TA260 001:055.816 JLINK_HasError()
TA260 001:057.342 JLINK_IsHalted()
TA260 001:057.864 - 0.522ms returns FALSE
TA260 001:057.876 JLINK_HasError()
TA260 001:059.346 JLINK_IsHalted()
TA260 001:059.799 - 0.452ms returns FALSE
TA260 001:059.809 JLINK_HasError()
TA260 001:061.342 JLINK_IsHalted()
TA260 001:061.796 - 0.454ms returns FALSE
TA260 001:061.803 JLINK_HasError()
TA260 001:062.846 JLINK_IsHalted()
TA260 001:063.354 - 0.507ms returns FALSE
TA260 001:063.360 JLINK_HasError()
TA260 001:064.856 JLINK_IsHalted()
TA260 001:065.421 - 0.565ms returns FALSE
TA260 001:065.432 JLINK_HasError()
TA260 001:066.848 JLINK_IsHalted()
TA260 001:067.322 - 0.472ms returns FALSE
TA260 001:067.330 JLINK_HasError()
TA260 001:068.857 JLINK_IsHalted()
TA260 001:069.308 - 0.451ms returns FALSE
TA260 001:069.319 JLINK_HasError()
TA260 001:070.854 JLINK_IsHalted()
TA260 001:071.351 - 0.496ms returns FALSE
TA260 001:071.358 JLINK_HasError()
TA260 001:073.361 JLINK_IsHalted()
TA260 001:073.976 - 0.614ms returns FALSE
TA260 001:073.985 JLINK_HasError()
TA260 001:076.368 JLINK_IsHalted()
TA260 001:076.845 - 0.476ms returns FALSE
TA260 001:076.895 JLINK_HasError()
TA260 001:078.363 JLINK_IsHalted()
TA260 001:078.878 - 0.514ms returns FALSE
TA260 001:078.885 JLINK_HasError()
TA260 001:080.364 JLINK_IsHalted()
TA260 001:080.875 - 0.510ms returns FALSE
TA260 001:080.886 JLINK_HasError()
TA260 001:082.362 JLINK_IsHalted()
TA260 001:082.851 - 0.488ms returns FALSE
TA260 001:082.860 JLINK_HasError()
TA260 001:084.872 JLINK_IsHalted()
TA260 001:085.370 - 0.497ms returns FALSE
TA260 001:085.388 JLINK_HasError()
TA260 001:086.868 JLINK_IsHalted()
TA260 001:087.407 - 0.539ms returns FALSE
TA260 001:087.417 JLINK_HasError()
TA260 001:088.871 JLINK_IsHalted()
TA260 001:089.422 - 0.550ms returns FALSE
TA260 001:089.431 JLINK_HasError()
TA260 001:090.876 JLINK_IsHalted()
TA260 001:091.328 - 0.452ms returns FALSE
TA260 001:091.339 JLINK_HasError()
TA260 001:092.384 JLINK_IsHalted()
TA260 001:092.862 - 0.477ms returns FALSE
TA260 001:092.869 JLINK_HasError()
TA260 001:094.386 JLINK_IsHalted()
TA260 001:094.879 - 0.493ms returns FALSE
TA260 001:094.889 JLINK_HasError()
TA260 001:096.408 JLINK_IsHalted()
TA260 001:096.901 - 0.493ms returns FALSE
TA260 001:096.908 JLINK_HasError()
TA260 001:098.381 JLINK_IsHalted()
TA260 001:098.908 - 0.527ms returns FALSE
TA260 001:098.918 JLINK_HasError()
TA260 001:100.383 JLINK_IsHalted()
TA260 001:100.899 - 0.516ms returns FALSE
TA260 001:100.908 JLINK_HasError()
TA260 001:102.383 JLINK_IsHalted()
TA260 001:102.935 - 0.551ms returns FALSE
TA260 001:102.950 JLINK_HasError()
TA260 001:104.792 JLINK_IsHalted()
TA260 001:105.305 - 0.512ms returns FALSE
TA260 001:105.319 JLINK_HasError()
TA260 001:106.791 JLINK_IsHalted()
TA260 001:107.273 - 0.481ms returns FALSE
TA260 001:107.280 JLINK_HasError()
TA260 001:108.790 JLINK_IsHalted()
TA260 001:109.364 - 0.573ms returns FALSE
TA260 001:109.374 JLINK_HasError()
TA260 001:110.795 JLINK_IsHalted()
TA260 001:111.271 - 0.476ms returns FALSE
TA260 001:111.280 JLINK_HasError()
TA260 001:113.297 JLINK_IsHalted()
TA260 001:113.819 - 0.522ms returns FALSE
TA260 001:113.833 JLINK_HasError()
TA260 001:115.299 JLINK_IsHalted()
TA260 001:115.792 - 0.493ms returns FALSE
TA260 001:115.798 JLINK_HasError()
TA260 001:117.299 JLINK_IsHalted()
TA260 001:117.816 - 0.516ms returns FALSE
TA260 001:117.823 JLINK_HasError()
TA260 001:119.303 JLINK_IsHalted()
TA260 001:119.760 - 0.456ms returns FALSE
TA260 001:119.767 JLINK_HasError()
TA260 001:122.153 JLINK_IsHalted()
TA260 001:122.661 - 0.508ms returns FALSE
TA260 001:122.668 JLINK_HasError()
TA260 001:124.665 JLINK_IsHalted()
TA260 001:125.141 - 0.475ms returns FALSE
TA260 001:125.155 JLINK_HasError()
TA260 001:126.667 JLINK_IsHalted()
TA260 001:127.193 - 0.526ms returns FALSE
TA260 001:127.205 JLINK_HasError()
TA260 001:128.662 JLINK_IsHalted()
TA260 001:129.113 - 0.450ms returns FALSE
TA260 001:129.120 JLINK_HasError()
TA260 001:130.673 JLINK_IsHalted()
TA260 001:131.180 - 0.506ms returns FALSE
TA260 001:131.189 JLINK_HasError()
TA260 001:133.167 JLINK_IsHalted()
TA260 001:133.658 - 0.490ms returns FALSE
TA260 001:133.665 JLINK_HasError()
TA260 001:135.173 JLINK_IsHalted()
TA260 001:135.649 - 0.475ms returns FALSE
TA260 001:135.656 JLINK_HasError()
TA260 001:136.823 JLINK_IsHalted()
TA260 001:137.279 - 0.456ms returns FALSE
TA260 001:137.289 JLINK_HasError()
TA260 001:138.399 JLINK_IsHalted()
TA260 001:138.839 - 0.440ms returns FALSE
TA260 001:138.851 JLINK_HasError()
TA260 001:139.945 JLINK_IsHalted()
TA260 001:140.405 - 0.460ms returns FALSE
TA260 001:140.413 JLINK_HasError()
TA260 001:141.492 JLINK_IsHalted()
TA260 001:141.908 - 0.415ms returns FALSE
TA260 001:141.915 JLINK_HasError()
TA260 001:143.033 JLINK_IsHalted()
TA260 001:143.541 - 0.507ms returns FALSE
TA260 001:143.549 JLINK_HasError()
TA260 001:144.978 JLINK_IsHalted()
TA260 001:145.508 - 0.529ms returns FALSE
TA260 001:145.524 JLINK_HasError()
TA260 001:147.089 JLINK_IsHalted()
TA260 001:147.568 - 0.478ms returns FALSE
TA260 001:147.577 JLINK_HasError()
TA260 001:149.086 JLINK_IsHalted()
TA260 001:149.590 - 0.503ms returns FALSE
TA260 001:149.598 JLINK_HasError()
TA260 001:151.091 JLINK_IsHalted()
TA260 001:151.551 - 0.459ms returns FALSE
TA260 001:151.561 JLINK_HasError()
TA260 001:153.594 JLINK_IsHalted()
TA260 001:154.126 - 0.531ms returns FALSE
TA260 001:154.133 JLINK_HasError()
TA260 001:155.593 JLINK_IsHalted()
TA260 001:156.007 - 0.414ms returns FALSE
TA260 001:156.014 JLINK_HasError()
TA260 001:157.594 JLINK_IsHalted()
TA260 001:158.079 - 0.484ms returns FALSE
TA260 001:158.086 JLINK_HasError()
TA260 001:159.597 JLINK_IsHalted()
TA260 001:160.110 - 0.513ms returns FALSE
TA260 001:160.118 JLINK_HasError()
TA260 001:162.099 JLINK_IsHalted()
TA260 001:162.590 - 0.491ms returns FALSE
TA260 001:162.597 JLINK_HasError()
TA260 001:164.106 JLINK_IsHalted()
TA260 001:164.603 - 0.496ms returns FALSE
TA260 001:164.616 JLINK_HasError()
TA260 001:166.106 JLINK_IsHalted()
TA260 001:166.577 - 0.470ms returns FALSE
TA260 001:166.585 JLINK_HasError()
TA260 001:169.110 JLINK_IsHalted()
TA260 001:169.653 - 0.542ms returns FALSE
TA260 001:169.668 JLINK_HasError()
TA260 001:171.102 JLINK_IsHalted()
TA260 001:171.594 - 0.491ms returns FALSE
TA260 001:171.605 JLINK_HasError()
TA260 001:173.617 JLINK_IsHalted()
TA260 001:174.105 - 0.488ms returns FALSE
TA260 001:174.117 JLINK_HasError()
TA260 001:175.616 JLINK_IsHalted()
TA260 001:176.126 - 0.509ms returns FALSE
TA260 001:176.143 JLINK_HasError()
TA260 001:177.612 JLINK_IsHalted()
TA260 001:178.092 - 0.479ms returns FALSE
TA260 001:178.100 JLINK_HasError()
TA260 001:179.614 JLINK_IsHalted()
TA260 001:180.047 - 0.432ms returns FALSE
TA260 001:180.055 JLINK_HasError()
TA260 001:182.119 JLINK_IsHalted()
TA260 001:182.613 - 0.493ms returns FALSE
TA260 001:182.624 JLINK_HasError()
TA260 001:187.592 JLINK_IsHalted()
TA260 001:188.073 - 0.480ms returns FALSE
TA260 001:188.080 JLINK_HasError()
TA260 001:189.592 JLINK_IsHalted()
TA260 001:190.077 - 0.485ms returns FALSE
TA260 001:190.089 JLINK_HasError()
TA260 001:192.093 JLINK_IsHalted()
TA260 001:192.589 - 0.495ms returns FALSE
TA260 001:192.598 JLINK_HasError()
TA260 001:194.101 JLINK_IsHalted()
TA260 001:194.594 - 0.493ms returns FALSE
TA260 001:194.602 JLINK_HasError()
TA260 001:196.102 JLINK_IsHalted()
TA260 001:196.612 - 0.510ms returns FALSE
TA260 001:196.620 JLINK_HasError()
TA260 001:198.096 JLINK_IsHalted()
TA260 001:198.591 - 0.494ms returns FALSE
TA260 001:198.599 JLINK_HasError()
TA260 001:200.099 JLINK_IsHalted()
TA260 001:200.595 - 0.495ms returns FALSE
TA260 001:200.606 JLINK_HasError()
TA260 001:202.099 JLINK_IsHalted()
TA260 001:202.589 - 0.490ms returns FALSE
TA260 001:202.596 JLINK_HasError()
TA260 001:204.607 JLINK_IsHalted()
TA260 001:205.053 - 0.446ms returns FALSE
TA260 001:205.070 JLINK_HasError()
TA260 001:206.600 JLINK_IsHalted()
TA260 001:207.103 - 0.502ms returns FALSE
TA260 001:207.113 JLINK_HasError()
TA260 001:208.603 JLINK_IsHalted()
TA260 001:209.044 - 0.440ms returns FALSE
TA260 001:209.052 JLINK_HasError()
TA260 001:210.608 JLINK_IsHalted()
TA260 001:211.098 - 0.489ms returns FALSE
TA260 001:211.105 JLINK_HasError()
TA260 001:213.107 JLINK_IsHalted()
TA260 001:213.627 - 0.519ms returns FALSE
TA260 001:213.641 JLINK_HasError()
TA260 001:215.114 JLINK_IsHalted()
TA260 001:215.601 - 0.486ms returns FALSE
TA260 001:215.612 JLINK_HasError()
TA260 001:217.110 JLINK_IsHalted()
TA260 001:217.556 - 0.446ms returns FALSE
TA260 001:217.565 JLINK_HasError()
TA260 001:219.116 JLINK_IsHalted()
TA260 001:219.612 - 0.495ms returns FALSE
TA260 001:219.622 JLINK_HasError()
TA260 001:221.111 JLINK_IsHalted()
TA260 001:221.634 - 0.523ms returns FALSE
TA260 001:221.644 JLINK_HasError()
TA260 001:223.621 JLINK_IsHalted()
TA260 001:224.091 - 0.470ms returns FALSE
TA260 001:224.102 JLINK_HasError()
TA260 001:225.624 JLINK_IsHalted()
TA260 001:226.077 - 0.453ms returns FALSE
TA260 001:226.087 JLINK_HasError()
TA260 001:227.625 JLINK_IsHalted()
TA260 001:228.129 - 0.504ms returns FALSE
TA260 001:228.140 JLINK_HasError()
TA260 001:229.620 JLINK_IsHalted()
TA260 001:230.160 - 0.539ms returns FALSE
TA260 001:230.168 JLINK_HasError()
TA260 001:232.125 JLINK_IsHalted()
TA260 001:232.602 - 0.476ms returns FALSE
TA260 001:232.609 JLINK_HasError()
TA260 001:234.126 JLINK_IsHalted()
TA260 001:234.614 - 0.487ms returns FALSE
TA260 001:234.621 JLINK_HasError()
TA260 001:236.130 JLINK_IsHalted()
TA260 001:236.646 - 0.515ms returns FALSE
TA260 001:236.652 JLINK_HasError()
TA260 001:238.127 JLINK_IsHalted()
TA260 001:238.591 - 0.463ms returns FALSE
TA260 001:238.600 JLINK_HasError()
TA260 001:240.129 JLINK_IsHalted()
TA260 001:240.613 - 0.484ms returns FALSE
TA260 001:240.626 JLINK_HasError()
TA260 001:242.128 JLINK_IsHalted()
TA260 001:242.613 - 0.485ms returns FALSE
TA260 001:242.621 JLINK_HasError()
TA260 001:244.640 JLINK_IsHalted()
TA260 001:245.139 - 0.499ms returns FALSE
TA260 001:245.154 JLINK_HasError()
TA260 001:246.630 JLINK_IsHalted()
TA260 001:247.115 - 0.484ms returns FALSE
TA260 001:247.123 JLINK_HasError()
TA260 001:248.638 JLINK_IsHalted()
TA260 001:249.119 - 0.480ms returns FALSE
TA260 001:249.130 JLINK_HasError()
TA260 001:250.632 JLINK_IsHalted()
TA260 001:251.171 - 0.538ms returns FALSE
TA260 001:251.187 JLINK_HasError()
TA260 001:253.137 JLINK_IsHalted()
TA260 001:253.656 - 0.518ms returns FALSE
TA260 001:253.662 JLINK_HasError()
TA260 001:255.145 JLINK_IsHalted()
TA260 001:255.658 - 0.512ms returns FALSE
TA260 001:255.665 JLINK_HasError()
TA260 001:257.138 JLINK_IsHalted()
TA260 001:257.598 - 0.460ms returns FALSE
TA260 001:257.610 JLINK_HasError()
TA260 001:259.145 JLINK_IsHalted()
TA260 001:259.595 - 0.450ms returns FALSE
TA260 001:259.607 JLINK_HasError()
TA260 001:261.146 JLINK_IsHalted()
TA260 001:261.593 - 0.446ms returns FALSE
TA260 001:261.602 JLINK_HasError()
TA260 001:262.642 JLINK_IsHalted()
TA260 001:263.132 - 0.489ms returns FALSE
TA260 001:263.148 JLINK_HasError()
TA260 001:264.658 JLINK_IsHalted()
TA260 001:265.128 - 0.470ms returns FALSE
TA260 001:265.145 JLINK_HasError()
TA260 001:266.649 JLINK_IsHalted()
TA260 001:267.169 - 0.519ms returns FALSE
TA260 001:267.179 JLINK_HasError()
TA260 001:268.650 JLINK_IsHalted()
TA260 001:269.147 - 0.497ms returns FALSE
TA260 001:269.155 JLINK_HasError()
TA260 001:270.658 JLINK_IsHalted()
TA260 001:271.124 - 0.466ms returns FALSE
TA260 001:271.132 JLINK_HasError()
TA260 001:273.153 JLINK_IsHalted()
TA260 001:273.658 - 0.504ms returns FALSE
TA260 001:273.664 JLINK_HasError()
TA260 001:275.163 JLINK_IsHalted()
TA260 001:275.663 - 0.500ms returns FALSE
TA260 001:275.670 JLINK_HasError()
TA260 001:277.158 JLINK_IsHalted()
TA260 001:277.647 - 0.489ms returns FALSE
TA260 001:277.655 JLINK_HasError()
TA260 001:279.164 JLINK_IsHalted()
TA260 001:279.646 - 0.482ms returns FALSE
TA260 001:279.655 JLINK_HasError()
TA260 001:281.166 JLINK_IsHalted()
TA260 001:281.614 - 0.448ms returns FALSE
TA260 001:281.623 JLINK_HasError()
TA260 001:282.661 JLINK_IsHalted()
TA260 001:283.131 - 0.469ms returns FALSE
TA260 001:283.141 JLINK_HasError()
TA260 001:284.668 JLINK_IsHalted()
TA260 001:285.286 - 0.617ms returns FALSE
TA260 001:285.306 JLINK_HasError()
TA260 001:286.667 JLINK_IsHalted()
TA260 001:287.126 - 0.458ms returns FALSE
TA260 001:287.134 JLINK_HasError()
TA260 001:288.669 JLINK_IsHalted()
TA260 001:289.130 - 0.461ms returns FALSE
TA260 001:289.139 JLINK_HasError()
TA260 001:290.671 JLINK_IsHalted()
TA260 001:291.148 - 0.477ms returns FALSE
TA260 001:291.161 JLINK_HasError()
TA260 001:293.176 JLINK_IsHalted()
TA260 001:293.661 - 0.484ms returns FALSE
TA260 001:293.674 JLINK_HasError()
TA260 001:295.177 JLINK_IsHalted()
TA260 001:295.660 - 0.481ms returns FALSE
TA260 001:295.667 JLINK_HasError()
TA260 001:297.177 JLINK_IsHalted()
TA260 001:297.734 - 0.556ms returns FALSE
TA260 001:297.741 JLINK_HasError()
TA260 001:299.174 JLINK_IsHalted()
TA260 001:299.655 - 0.480ms returns FALSE
TA260 001:299.662 JLINK_HasError()
TA260 001:301.179 JLINK_IsHalted()
TA260 001:301.613 - 0.433ms returns FALSE
TA260 001:301.621 JLINK_HasError()
TA260 001:302.677 JLINK_IsHalted()
TA260 001:303.135 - 0.457ms returns FALSE
TA260 001:303.159 JLINK_HasError()
TA260 001:304.687 JLINK_IsHalted()
TA260 001:305.212 - 0.524ms returns FALSE
TA260 001:305.232 JLINK_HasError()
TA260 001:306.685 JLINK_IsHalted()
TA260 001:307.204 - 0.519ms returns FALSE
TA260 001:307.217 JLINK_HasError()
TA260 001:308.685 JLINK_IsHalted()
TA260 001:309.216 - 0.530ms returns FALSE
TA260 001:309.228 JLINK_HasError()
TA260 001:310.690 JLINK_IsHalted()
TA260 001:311.208 - 0.517ms returns FALSE
TA260 001:311.217 JLINK_HasError()
TA260 001:313.191 JLINK_IsHalted()
TA260 001:313.708 - 0.516ms returns FALSE
TA260 001:313.717 JLINK_HasError()
TA260 001:315.195 JLINK_IsHalted()
TA260 001:315.690 - 0.494ms returns FALSE
TA260 001:315.697 JLINK_HasError()
TA260 001:317.193 JLINK_IsHalted()
TA260 001:317.663 - 0.469ms returns FALSE
TA260 001:317.671 JLINK_HasError()
TA260 001:319.196 JLINK_IsHalted()
TA260 001:319.708 - 0.512ms returns FALSE
TA260 001:319.718 JLINK_HasError()
TA260 001:321.194 JLINK_IsHalted()
TA260 001:321.721 - 0.526ms returns FALSE
TA260 001:321.736 JLINK_HasError()
TA260 001:323.704 JLINK_IsHalted()
TA260 001:324.275 - 0.570ms returns FALSE
TA260 001:324.287 JLINK_HasError()
TA260 001:325.703 JLINK_IsHalted()
TA260 001:326.154 - 0.450ms returns FALSE
TA260 001:326.208 JLINK_HasError()
TA260 001:327.705 JLINK_IsHalted()
TA260 001:328.283 - 0.578ms returns FALSE
TA260 001:328.292 JLINK_HasError()
TA260 001:329.711 JLINK_IsHalted()
TA260 001:330.271 - 0.559ms returns FALSE
TA260 001:330.281 JLINK_HasError()
TA260 001:332.208 JLINK_IsHalted()
TA260 001:332.634 - 0.425ms returns FALSE
TA260 001:332.643 JLINK_HasError()
TA260 001:334.210 JLINK_IsHalted()
TA260 001:334.709 - 0.498ms returns FALSE
TA260 001:334.725 JLINK_HasError()
TA260 001:336.206 JLINK_IsHalted()
TA260 001:336.679 - 0.472ms returns FALSE
TA260 001:336.687 JLINK_HasError()
TA260 001:338.210 JLINK_IsHalted()
TA260 001:338.706 - 0.496ms returns FALSE
TA260 001:338.715 JLINK_HasError()
TA260 001:340.210 JLINK_IsHalted()
TA260 001:340.710 - 0.500ms returns FALSE
TA260 001:340.717 JLINK_HasError()
TA260 001:342.208 JLINK_IsHalted()
TA260 001:342.724 - 0.505ms returns FALSE
TA260 001:342.734 JLINK_HasError()
TA260 001:344.728 JLINK_IsHalted()
TA260 001:345.283 - 0.555ms returns FALSE
TA260 001:345.291 JLINK_HasError()
TA260 001:346.717 JLINK_IsHalted()
TA260 001:347.192 - 0.475ms returns FALSE
TA260 001:347.199 JLINK_HasError()
TA260 001:348.726 JLINK_IsHalted()
TA260 001:349.190 - 0.464ms returns FALSE
TA260 001:349.197 JLINK_HasError()
TA260 001:350.720 JLINK_IsHalted()
TA260 001:351.179 - 0.459ms returns FALSE
TA260 001:351.189 JLINK_HasError()
TA260 001:352.232 JLINK_IsHalted()
TA260 001:352.772 - 0.538ms returns FALSE
TA260 001:352.781 JLINK_HasError()
TA260 001:354.236 JLINK_IsHalted()
TA260 001:354.759 - 0.521ms returns FALSE
TA260 001:354.775 JLINK_HasError()
TA260 001:356.430 JLINK_IsHalted()
TA260 001:356.939 - 0.508ms returns FALSE
TA260 001:356.945 JLINK_HasError()
TA260 001:358.435 JLINK_IsHalted()
TA260 001:358.943 - 0.509ms returns FALSE
TA260 001:358.953 JLINK_HasError()
TA260 001:360.440 JLINK_IsHalted()
TA260 001:360.926 - 0.486ms returns FALSE
TA260 001:360.937 JLINK_HasError()
TA260 001:362.939 JLINK_IsHalted()
TA260 001:363.466 - 0.526ms returns FALSE
TA260 001:363.481 JLINK_HasError()
TA260 001:364.949 JLINK_IsHalted()
TA260 001:365.407 - 0.458ms returns FALSE
TA260 001:365.416 JLINK_HasError()
TA260 001:366.945 JLINK_IsHalted()
TA260 001:367.408 - 0.462ms returns FALSE
TA260 001:367.421 JLINK_HasError()
TA260 001:368.996 JLINK_IsHalted()
TA260 001:369.544 - 0.548ms returns FALSE
TA260 001:369.555 JLINK_HasError()
TA260 001:370.948 JLINK_IsHalted()
TA260 001:371.444 - 0.495ms returns FALSE
TA260 001:371.454 JLINK_HasError()
TA260 001:373.468 JLINK_IsHalted()
TA260 001:373.992 - 0.524ms returns FALSE
TA260 001:374.004 JLINK_HasError()
TA260 001:375.455 JLINK_IsHalted()
TA260 001:376.001 - 0.545ms returns FALSE
TA260 001:376.014 JLINK_HasError()
TA260 001:377.450 JLINK_IsHalted()
TA260 001:377.931 - 0.480ms returns FALSE
TA260 001:377.940 JLINK_HasError()
TA260 001:379.457 JLINK_IsHalted()
TA260 001:379.922 - 0.465ms returns FALSE
TA260 001:379.930 JLINK_HasError()
TA260 001:381.960 JLINK_IsHalted()
TA260 001:382.453 - 0.492ms returns FALSE
TA260 001:382.460 JLINK_HasError()
TA260 001:383.967 JLINK_IsHalted()
TA260 001:384.447 - 0.480ms returns FALSE
TA260 001:384.463 JLINK_HasError()
TA260 001:385.965 JLINK_IsHalted()
TA260 001:386.423 - 0.457ms returns FALSE
TA260 001:386.431 JLINK_HasError()
TA260 001:387.960 JLINK_IsHalted()
TA260 001:390.306   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:390.797 - 2.836ms returns TRUE
TA260 001:390.805 JLINK_ReadReg(R15 (PC))
TA260 001:390.811 - 0.005ms returns 0x20000000
TA260 001:390.815 JLINK_ClrBPEx(BPHandle = 0x00000005)
TA260 001:390.819 - 0.003ms returns 0x00
TA260 001:390.824 JLINK_ReadReg(R0)
TA260 001:390.828 - 0.004ms returns 0x00000000
TA260 001:391.218 JLINK_HasError()
TA260 001:391.231 JLINK_WriteReg(R0, 0x08008000)
TA260 001:391.235 - 0.005ms returns 0
TA260 001:391.240 JLINK_WriteReg(R1, 0x00004000)
TA260 001:391.243 - 0.003ms returns 0
TA260 001:391.248 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:391.251 - 0.003ms returns 0
TA260 001:391.255 JLINK_WriteReg(R3, 0x00000000)
TA260 001:391.258 - 0.003ms returns 0
TA260 001:391.262 JLINK_WriteReg(R4, 0x00000000)
TA260 001:391.266 - 0.003ms returns 0
TA260 001:391.270 JLINK_WriteReg(R5, 0x00000000)
TA260 001:391.281 - 0.010ms returns 0
TA260 001:391.285 JLINK_WriteReg(R6, 0x00000000)
TA260 001:391.288 - 0.003ms returns 0
TA260 001:391.292 JLINK_WriteReg(R7, 0x00000000)
TA260 001:391.296 - 0.003ms returns 0
TA260 001:391.300 JLINK_WriteReg(R8, 0x00000000)
TA260 001:391.303 - 0.003ms returns 0
TA260 001:391.308 JLINK_WriteReg(R9, 0x20000180)
TA260 001:391.311 - 0.003ms returns 0
TA260 001:391.315 JLINK_WriteReg(R10, 0x00000000)
TA260 001:391.318 - 0.003ms returns 0
TA260 001:391.322 JLINK_WriteReg(R11, 0x00000000)
TA260 001:391.326 - 0.003ms returns 0
TA260 001:391.330 JLINK_WriteReg(R12, 0x00000000)
TA260 001:391.334 - 0.003ms returns 0
TA260 001:391.338 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:391.342 - 0.003ms returns 0
TA260 001:391.346 JLINK_WriteReg(R14, 0x20000001)
TA260 001:391.349 - 0.003ms returns 0
TA260 001:391.353 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 001:391.357 - 0.003ms returns 0
TA260 001:391.361 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:391.364 - 0.003ms returns 0
TA260 001:391.368 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:391.372 - 0.003ms returns 0
TA260 001:391.376 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:391.379 - 0.003ms returns 0
TA260 001:391.383 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:391.387 - 0.003ms returns 0
TA260 001:391.391 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:391.395 - 0.004ms returns 0x00000006
TA260 001:391.400 JLINK_Go()
TA260 001:391.409   CPU_ReadMem(4 bytes @ 0x********)
TA260 001:394.284 - 2.883ms 
TA260 001:394.306 JLINK_IsHalted()
TA260 001:396.683   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:397.157 - 2.850ms returns TRUE
TA260 001:397.169 JLINK_ReadReg(R15 (PC))
TA260 001:397.174 - 0.005ms returns 0x20000000
TA260 001:397.223 JLINK_ClrBPEx(BPHandle = 0x00000006)
TA260 001:397.228 - 0.005ms returns 0x00
TA260 001:397.233 JLINK_ReadReg(R0)
TA260 001:397.236 - 0.003ms returns 0x00000001
TA260 001:397.241 JLINK_HasError()
TA260 001:397.246 JLINK_WriteReg(R0, 0x08008000)
TA260 001:397.250 - 0.004ms returns 0
TA260 001:397.254 JLINK_WriteReg(R1, 0x00004000)
TA260 001:397.258 - 0.003ms returns 0
TA260 001:397.262 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:397.265 - 0.003ms returns 0
TA260 001:397.269 JLINK_WriteReg(R3, 0x00000000)
TA260 001:397.273 - 0.003ms returns 0
TA260 001:397.277 JLINK_WriteReg(R4, 0x00000000)
TA260 001:397.280 - 0.003ms returns 0
TA260 001:397.284 JLINK_WriteReg(R5, 0x00000000)
TA260 001:397.288 - 0.003ms returns 0
TA260 001:397.292 JLINK_WriteReg(R6, 0x00000000)
TA260 001:397.295 - 0.003ms returns 0
TA260 001:397.299 JLINK_WriteReg(R7, 0x00000000)
TA260 001:397.302 - 0.003ms returns 0
TA260 001:397.307 JLINK_WriteReg(R8, 0x00000000)
TA260 001:397.310 - 0.003ms returns 0
TA260 001:397.314 JLINK_WriteReg(R9, 0x20000180)
TA260 001:397.317 - 0.003ms returns 0
TA260 001:397.322 JLINK_WriteReg(R10, 0x00000000)
TA260 001:397.325 - 0.003ms returns 0
TA260 001:397.329 JLINK_WriteReg(R11, 0x00000000)
TA260 001:397.332 - 0.003ms returns 0
TA260 001:397.337 JLINK_WriteReg(R12, 0x00000000)
TA260 001:397.340 - 0.003ms returns 0
TA260 001:397.382 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:397.386 - 0.042ms returns 0
TA260 001:397.390 JLINK_WriteReg(R14, 0x20000001)
TA260 001:397.394 - 0.003ms returns 0
TA260 001:397.398 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 001:397.401 - 0.003ms returns 0
TA260 001:397.405 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:397.409 - 0.003ms returns 0
TA260 001:397.414 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:397.417 - 0.003ms returns 0
TA260 001:397.421 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:397.425 - 0.003ms returns 0
TA260 001:397.429 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:397.432 - 0.003ms returns 0
TA260 001:397.437 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:397.441 - 0.004ms returns 0x00000007
TA260 001:397.445 JLINK_Go()
TA260 001:397.453   CPU_ReadMem(4 bytes @ 0x********)
TA260 001:400.273 - 2.827ms 
TA260 001:400.289 JLINK_IsHalted()
TA260 001:400.838 - 0.548ms returns FALSE
TA260 001:400.863 JLINK_HasError()
TA260 001:402.978 JLINK_IsHalted()
TA260 001:403.461 - 0.483ms returns FALSE
TA260 001:403.477 JLINK_HasError()
TA260 001:404.981 JLINK_IsHalted()
TA260 001:405.459 - 0.477ms returns FALSE
TA260 001:405.478 JLINK_HasError()
TA260 001:406.976 JLINK_IsHalted()
TA260 001:407.511 - 0.534ms returns FALSE
TA260 001:407.520 JLINK_HasError()
TA260 001:408.977 JLINK_IsHalted()
TA260 001:409.514 - 0.536ms returns FALSE
TA260 001:409.547 JLINK_HasError()
TA260 001:410.980 JLINK_IsHalted()
TA260 001:411.512 - 0.531ms returns FALSE
TA260 001:411.519 JLINK_HasError()
TA260 001:413.482 JLINK_IsHalted()
TA260 001:413.921 - 0.439ms returns FALSE
TA260 001:413.929 JLINK_HasError()
TA260 001:415.481 JLINK_IsHalted()
TA260 001:416.003 - 0.520ms returns FALSE
TA260 001:416.016 JLINK_HasError()
TA260 001:417.487 JLINK_IsHalted()
TA260 001:417.967 - 0.479ms returns FALSE
TA260 001:417.976 JLINK_HasError()
TA260 001:419.487 JLINK_IsHalted()
TA260 001:419.986 - 0.499ms returns FALSE
TA260 001:419.992 JLINK_HasError()
TA260 001:421.989 JLINK_IsHalted()
TA260 001:422.408 - 0.419ms returns FALSE
TA260 001:422.424 JLINK_HasError()
TA260 001:423.992 JLINK_IsHalted()
TA260 001:424.466 - 0.473ms returns FALSE
TA260 001:424.473 JLINK_HasError()
TA260 001:426.491 JLINK_IsHalted()
TA260 001:426.953 - 0.461ms returns FALSE
TA260 001:426.960 JLINK_HasError()
TA260 001:428.495 JLINK_IsHalted()
TA260 001:428.964 - 0.469ms returns FALSE
TA260 001:428.970 JLINK_HasError()
TA260 001:430.493 JLINK_IsHalted()
TA260 001:430.998 - 0.504ms returns FALSE
TA260 001:431.008 JLINK_HasError()
TA260 001:433.000 JLINK_IsHalted()
TA260 001:433.492 - 0.492ms returns FALSE
TA260 001:433.503 JLINK_HasError()
TA260 001:435.001 JLINK_IsHalted()
TA260 001:435.440 - 0.439ms returns FALSE
TA260 001:435.448 JLINK_HasError()
TA260 001:437.001 JLINK_IsHalted()
TA260 001:437.451 - 0.449ms returns FALSE
TA260 001:437.458 JLINK_HasError()
TA260 001:439.008 JLINK_IsHalted()
TA260 001:439.505 - 0.496ms returns FALSE
TA260 001:439.512 JLINK_HasError()
TA260 001:441.010 JLINK_IsHalted()
TA260 001:441.511 - 0.501ms returns FALSE
TA260 001:441.519 JLINK_HasError()
TA260 001:443.513 JLINK_IsHalted()
TA260 001:444.029 - 0.515ms returns FALSE
TA260 001:444.061 JLINK_HasError()
TA260 001:445.514 JLINK_IsHalted()
TA260 001:445.989 - 0.474ms returns FALSE
TA260 001:446.001 JLINK_HasError()
TA260 001:447.753 JLINK_IsHalted()
TA260 001:448.269 - 0.516ms returns FALSE
TA260 001:448.281 JLINK_HasError()
TA260 001:449.753 JLINK_IsHalted()
TA260 001:450.282 - 0.527ms returns FALSE
TA260 001:450.310 JLINK_HasError()
TA260 001:452.254 JLINK_IsHalted()
TA260 001:452.698 - 0.443ms returns FALSE
TA260 001:452.706 JLINK_HasError()
TA260 001:454.265 JLINK_IsHalted()
TA260 001:454.724 - 0.458ms returns FALSE
TA260 001:454.730 JLINK_HasError()
TA260 001:456.258 JLINK_IsHalted()
TA260 001:456.758 - 0.499ms returns FALSE
TA260 001:456.763 JLINK_HasError()
TA260 001:458.258 JLINK_IsHalted()
TA260 001:458.747 - 0.489ms returns FALSE
TA260 001:458.753 JLINK_HasError()
TA260 001:460.265 JLINK_IsHalted()
TA260 001:460.727 - 0.461ms returns FALSE
TA260 001:460.733 JLINK_HasError()
TA260 001:462.258 JLINK_IsHalted()
TA260 001:462.718 - 0.459ms returns FALSE
TA260 001:462.724 JLINK_HasError()
TA260 001:463.767 JLINK_IsHalted()
TA260 001:464.271 - 0.503ms returns FALSE
TA260 001:464.279 JLINK_HasError()
TA260 001:465.771 JLINK_IsHalted()
TA260 001:466.279 - 0.508ms returns FALSE
TA260 001:466.285 JLINK_HasError()
TA260 001:467.765 JLINK_IsHalted()
TA260 001:468.271 - 0.505ms returns FALSE
TA260 001:468.277 JLINK_HasError()
TA260 001:469.768 JLINK_IsHalted()
TA260 001:470.293 - 0.525ms returns FALSE
TA260 001:470.302 JLINK_HasError()
TA260 001:472.271 JLINK_IsHalted()
TA260 001:472.759 - 0.488ms returns FALSE
TA260 001:472.766 JLINK_HasError()
TA260 001:474.275 JLINK_IsHalted()
TA260 001:474.749 - 0.473ms returns FALSE
TA260 001:474.756 JLINK_HasError()
TA260 001:476.281 JLINK_IsHalted()
TA260 001:476.786 - 0.504ms returns FALSE
TA260 001:476.792 JLINK_HasError()
TA260 001:477.992 JLINK_IsHalted()
TA260 001:478.514 - 0.521ms returns FALSE
TA260 001:478.525 JLINK_HasError()
TA260 001:479.990 JLINK_IsHalted()
TA260 001:480.446 - 0.455ms returns FALSE
TA260 001:480.456 JLINK_HasError()
TA260 001:481.991 JLINK_IsHalted()
TA260 001:482.407 - 0.415ms returns FALSE
TA260 001:482.415 JLINK_HasError()
TA260 001:483.490 JLINK_IsHalted()
TA260 001:483.964 - 0.473ms returns FALSE
TA260 001:483.982 JLINK_HasError()
TA260 001:485.498 JLINK_IsHalted()
TA260 001:486.000 - 0.500ms returns FALSE
TA260 001:486.010 JLINK_HasError()
TA260 001:487.491 JLINK_IsHalted()
TA260 001:487.999 - 0.507ms returns FALSE
TA260 001:488.006 JLINK_HasError()
TA260 001:489.493 JLINK_IsHalted()
TA260 001:489.977 - 0.483ms returns FALSE
TA260 001:489.983 JLINK_HasError()
TA260 001:491.998 JLINK_IsHalted()
TA260 001:492.532 - 0.533ms returns FALSE
TA260 001:492.539 JLINK_HasError()
TA260 001:494.000 JLINK_IsHalted()
TA260 001:494.511 - 0.511ms returns FALSE
TA260 001:494.518 JLINK_HasError()
TA260 001:496.008 JLINK_IsHalted()
TA260 001:496.499 - 0.490ms returns FALSE
TA260 001:496.506 JLINK_HasError()
TA260 001:498.011 JLINK_IsHalted()
TA260 001:498.498 - 0.486ms returns FALSE
TA260 001:498.504 JLINK_HasError()
TA260 001:500.004 JLINK_IsHalted()
TA260 001:500.512 - 0.507ms returns FALSE
TA260 001:500.521 JLINK_HasError()
TA260 001:502.007 JLINK_IsHalted()
TA260 001:502.499 - 0.491ms returns FALSE
TA260 001:502.508 JLINK_HasError()
TA260 001:504.513 JLINK_IsHalted()
TA260 001:504.999 - 0.486ms returns FALSE
TA260 001:505.006 JLINK_HasError()
TA260 001:506.510 JLINK_IsHalted()
TA260 001:506.954 - 0.443ms returns FALSE
TA260 001:506.961 JLINK_HasError()
TA260 001:508.509 JLINK_IsHalted()
TA260 001:508.956 - 0.446ms returns FALSE
TA260 001:508.969 JLINK_HasError()
TA260 001:512.022 JLINK_IsHalted()
TA260 001:512.490 - 0.467ms returns FALSE
TA260 001:512.496 JLINK_HasError()
TA260 001:514.018 JLINK_IsHalted()
TA260 001:514.542 - 0.524ms returns FALSE
TA260 001:514.549 JLINK_HasError()
TA260 001:516.021 JLINK_IsHalted()
TA260 001:516.533 - 0.511ms returns FALSE
TA260 001:516.542 JLINK_HasError()
TA260 001:518.020 JLINK_IsHalted()
TA260 001:518.465 - 0.444ms returns FALSE
TA260 001:518.472 JLINK_HasError()
TA260 001:520.018 JLINK_IsHalted()
TA260 001:520.499 - 0.481ms returns FALSE
TA260 001:520.505 JLINK_HasError()
TA260 001:522.023 JLINK_IsHalted()
TA260 001:522.524 - 0.501ms returns FALSE
TA260 001:522.531 JLINK_HasError()
TA260 001:524.032 JLINK_IsHalted()
TA260 001:524.513 - 0.480ms returns FALSE
TA260 001:525.819 JLINK_HasError()
TA260 001:527.030 JLINK_IsHalted()
TA260 001:527.513 - 0.482ms returns FALSE
TA260 001:527.521 JLINK_HasError()
TA260 001:529.036 JLINK_IsHalted()
TA260 001:529.548 - 0.511ms returns FALSE
TA260 001:529.557 JLINK_HasError()
TA260 001:531.031 JLINK_IsHalted()
TA260 001:531.546 - 0.514ms returns FALSE
TA260 001:531.556 JLINK_HasError()
TA260 001:533.540 JLINK_IsHalted()
TA260 001:534.038 - 0.498ms returns FALSE
TA260 001:534.051 JLINK_HasError()
TA260 001:535.535 JLINK_IsHalted()
TA260 001:536.074 - 0.538ms returns FALSE
TA260 001:536.081 JLINK_HasError()
TA260 001:537.535 JLINK_IsHalted()
TA260 001:538.035 - 0.499ms returns FALSE
TA260 001:538.044 JLINK_HasError()
TA260 001:539.552 JLINK_IsHalted()
TA260 001:540.151 - 0.598ms returns FALSE
TA260 001:540.163 JLINK_HasError()
TA260 001:542.049 JLINK_IsHalted()
TA260 001:542.511 - 0.461ms returns FALSE
TA260 001:542.517 JLINK_HasError()
TA260 001:544.047 JLINK_IsHalted()
TA260 001:544.501 - 0.453ms returns FALSE
TA260 001:544.508 JLINK_HasError()
TA260 001:546.048 JLINK_IsHalted()
TA260 001:546.547 - 0.498ms returns FALSE
TA260 001:546.554 JLINK_HasError()
TA260 001:548.048 JLINK_IsHalted()
TA260 001:548.546 - 0.497ms returns FALSE
TA260 001:548.555 JLINK_HasError()
TA260 001:550.056 JLINK_IsHalted()
TA260 001:550.563 - 0.507ms returns FALSE
TA260 001:550.582 JLINK_HasError()
TA260 001:552.049 JLINK_IsHalted()
TA260 001:552.544 - 0.494ms returns FALSE
TA260 001:552.554 JLINK_HasError()
TA260 001:554.558 JLINK_IsHalted()
TA260 001:555.092 - 0.533ms returns FALSE
TA260 001:555.098 JLINK_HasError()
TA260 001:556.551 JLINK_IsHalted()
TA260 001:557.034 - 0.482ms returns FALSE
TA260 001:557.040 JLINK_HasError()
TA260 001:558.555 JLINK_IsHalted()
TA260 001:559.044 - 0.489ms returns FALSE
TA260 001:559.052 JLINK_HasError()
TA260 001:560.565 JLINK_IsHalted()
TA260 001:560.983 - 0.417ms returns FALSE
TA260 001:560.989 JLINK_HasError()
TA260 001:562.056 JLINK_IsHalted()
TA260 001:562.544 - 0.488ms returns FALSE
TA260 001:562.551 JLINK_HasError()
TA260 001:564.064 JLINK_IsHalted()
TA260 001:564.583 - 0.519ms returns FALSE
TA260 001:564.595 JLINK_HasError()
TA260 001:566.064 JLINK_IsHalted()
TA260 001:566.587 - 0.523ms returns FALSE
TA260 001:566.595 JLINK_HasError()
TA260 001:568.064 JLINK_IsHalted()
TA260 001:568.568 - 0.504ms returns FALSE
TA260 001:568.577 JLINK_HasError()
TA260 001:570.064 JLINK_IsHalted()
TA260 001:570.512 - 0.447ms returns FALSE
TA260 001:570.519 JLINK_HasError()
TA260 001:572.064 JLINK_IsHalted()
TA260 001:572.510 - 0.446ms returns FALSE
TA260 001:572.517 JLINK_HasError()
TA260 001:573.569 JLINK_IsHalted()
TA260 001:574.048 - 0.479ms returns FALSE
TA260 001:574.061 JLINK_HasError()
TA260 001:575.574 JLINK_IsHalted()
TA260 001:576.043 - 0.468ms returns FALSE
TA260 001:576.059 JLINK_HasError()
TA260 001:577.570 JLINK_IsHalted()
TA260 001:578.042 - 0.472ms returns FALSE
TA260 001:578.050 JLINK_HasError()
TA260 001:579.575 JLINK_IsHalted()
TA260 001:580.024 - 0.448ms returns FALSE
TA260 001:580.032 JLINK_HasError()
TA260 001:582.086 JLINK_IsHalted()
TA260 001:582.544 - 0.457ms returns FALSE
TA260 001:582.551 JLINK_HasError()
TA260 001:584.081 JLINK_IsHalted()
TA260 001:584.505 - 0.424ms returns FALSE
TA260 001:584.512 JLINK_HasError()
TA260 001:586.084 JLINK_IsHalted()
TA260 001:586.566 - 0.482ms returns FALSE
TA260 001:586.575 JLINK_HasError()
TA260 001:588.084 JLINK_IsHalted()
TA260 001:588.647 - 0.562ms returns FALSE
TA260 001:588.655 JLINK_HasError()
TA260 001:590.082 JLINK_IsHalted()
TA260 001:590.647 - 0.565ms returns FALSE
TA260 001:590.656 JLINK_HasError()
TA260 001:591.797 JLINK_IsHalted()
TA260 001:592.322 - 0.524ms returns FALSE
TA260 001:592.338 JLINK_HasError()
TA260 001:594.314 JLINK_IsHalted()
TA260 001:594.756 - 0.442ms returns FALSE
TA260 001:594.762 JLINK_HasError()
TA260 001:596.310 JLINK_IsHalted()
TA260 001:596.771 - 0.461ms returns FALSE
TA260 001:596.781 JLINK_HasError()
TA260 001:598.314 JLINK_IsHalted()
TA260 001:598.794 - 0.479ms returns FALSE
TA260 001:598.802 JLINK_HasError()
TA260 001:600.307 JLINK_IsHalted()
TA260 001:600.816 - 0.509ms returns FALSE
TA260 001:600.826 JLINK_HasError()
TA260 001:602.312 JLINK_IsHalted()
TA260 001:602.786 - 0.473ms returns FALSE
TA260 001:602.799 JLINK_HasError()
TA260 001:604.831 JLINK_IsHalted()
TA260 001:605.448 - 0.617ms returns FALSE
TA260 001:605.456 JLINK_HasError()
TA260 001:606.823 JLINK_IsHalted()
TA260 001:607.272 - 0.449ms returns FALSE
TA260 001:607.280 JLINK_HasError()
TA260 001:608.826 JLINK_IsHalted()
TA260 001:609.276 - 0.450ms returns FALSE
TA260 001:609.291 JLINK_HasError()
TA260 001:610.823 JLINK_IsHalted()
TA260 001:611.316 - 0.493ms returns FALSE
TA260 001:611.324 JLINK_HasError()
TA260 001:613.325 JLINK_IsHalted()
TA260 001:613.850 - 0.524ms returns FALSE
TA260 001:613.866 JLINK_HasError()
TA260 001:615.327 JLINK_IsHalted()
TA260 001:615.815 - 0.487ms returns FALSE
TA260 001:615.823 JLINK_HasError()
TA260 001:617.324 JLINK_IsHalted()
TA260 001:617.798 - 0.472ms returns FALSE
TA260 001:617.813 JLINK_HasError()
TA260 001:620.330 JLINK_IsHalted()
TA260 001:620.897 - 0.566ms returns FALSE
TA260 001:620.905 JLINK_HasError()
TA260 001:622.326 JLINK_IsHalted()
TA260 001:622.829 - 0.503ms returns FALSE
TA260 001:622.836 JLINK_HasError()
TA260 001:624.332 JLINK_IsHalted()
TA260 001:624.840 - 0.508ms returns FALSE
TA260 001:624.849 JLINK_HasError()
TA260 001:626.339 JLINK_IsHalted()
TA260 001:626.846 - 0.507ms returns FALSE
TA260 001:626.878 JLINK_HasError()
TA260 001:628.333 JLINK_IsHalted()
TA260 001:628.815 - 0.482ms returns FALSE
TA260 001:628.824 JLINK_HasError()
TA260 001:630.335 JLINK_IsHalted()
TA260 001:630.807 - 0.472ms returns FALSE
TA260 001:630.815 JLINK_HasError()
TA260 001:632.337 JLINK_IsHalted()
TA260 001:632.805 - 0.467ms returns FALSE
TA260 001:632.821 JLINK_HasError()
TA260 001:634.843 JLINK_IsHalted()
TA260 001:635.376 - 0.532ms returns FALSE
TA260 001:635.385 JLINK_HasError()
TA260 001:636.841 JLINK_IsHalted()
TA260 001:637.318 - 0.476ms returns FALSE
TA260 001:637.327 JLINK_HasError()
TA260 001:638.844 JLINK_IsHalted()
TA260 001:639.433 - 0.588ms returns FALSE
TA260 001:639.442 JLINK_HasError()
TA260 001:640.840 JLINK_IsHalted()
TA260 001:641.316 - 0.475ms returns FALSE
TA260 001:641.324 JLINK_HasError()
TA260 001:643.348 JLINK_IsHalted()
TA260 001:644.027 - 0.679ms returns FALSE
TA260 001:644.039 JLINK_HasError()
TA260 001:645.347 JLINK_IsHalted()
TA260 001:645.838 - 0.491ms returns FALSE
TA260 001:645.845 JLINK_HasError()
TA260 001:647.348 JLINK_IsHalted()
TA260 001:647.865 - 0.517ms returns FALSE
TA260 001:647.873 JLINK_HasError()
TA260 001:649.351 JLINK_IsHalted()
TA260 001:649.862 - 0.510ms returns FALSE
TA260 001:649.870 JLINK_HasError()
TA260 001:651.349 JLINK_IsHalted()
TA260 001:651.792 - 0.443ms returns FALSE
TA260 001:651.806 JLINK_HasError()
TA260 001:652.849 JLINK_IsHalted()
TA260 001:653.273 - 0.424ms returns FALSE
TA260 001:653.282 JLINK_HasError()
TA260 001:654.678 JLINK_IsHalted()
TA260 001:655.095 - 0.417ms returns FALSE
TA260 001:655.101 JLINK_HasError()
TA260 001:656.676 JLINK_IsHalted()
TA260 001:657.155 - 0.478ms returns FALSE
TA260 001:657.161 JLINK_HasError()
TA260 001:658.679 JLINK_IsHalted()
TA260 001:659.192 - 0.513ms returns FALSE
TA260 001:659.221 JLINK_HasError()
TA260 001:660.680 JLINK_IsHalted()
TA260 001:661.179 - 0.498ms returns FALSE
TA260 001:661.187 JLINK_HasError()
TA260 001:663.179 JLINK_IsHalted()
TA260 001:663.659 - 0.479ms returns FALSE
TA260 001:663.666 JLINK_HasError()
TA260 001:665.184 JLINK_IsHalted()
TA260 001:665.645 - 0.461ms returns FALSE
TA260 001:665.652 JLINK_HasError()
TA260 001:667.180 JLINK_IsHalted()
TA260 001:667.689 - 0.509ms returns FALSE
TA260 001:667.695 JLINK_HasError()
TA260 001:669.180 JLINK_IsHalted()
TA260 001:669.664 - 0.484ms returns FALSE
TA260 001:669.670 JLINK_HasError()
TA260 001:671.180 JLINK_IsHalted()
TA260 001:671.666 - 0.485ms returns FALSE
TA260 001:671.671 JLINK_HasError()
TA260 001:673.691 JLINK_IsHalted()
TA260 001:674.193 - 0.501ms returns FALSE
TA260 001:674.206 JLINK_HasError()
TA260 001:675.690 JLINK_IsHalted()
TA260 001:676.150 - 0.458ms returns FALSE
TA260 001:676.162 JLINK_HasError()
TA260 001:677.689 JLINK_IsHalted()
TA260 001:678.189 - 0.500ms returns FALSE
TA260 001:678.195 JLINK_HasError()
TA260 001:679.689 JLINK_IsHalted()
TA260 001:680.282 - 0.592ms returns FALSE
TA260 001:680.291 JLINK_HasError()
TA260 001:683.195 JLINK_IsHalted()
TA260 001:683.714 - 0.519ms returns FALSE
TA260 001:683.727 JLINK_HasError()
TA260 001:685.196 JLINK_IsHalted()
TA260 001:685.712 - 0.516ms returns FALSE
TA260 001:685.718 JLINK_HasError()
TA260 001:687.194 JLINK_IsHalted()
TA260 001:687.665 - 0.470ms returns FALSE
TA260 001:687.671 JLINK_HasError()
TA260 001:689.196 JLINK_IsHalted()
TA260 001:689.698 - 0.501ms returns FALSE
TA260 001:689.704 JLINK_HasError()
TA260 001:691.194 JLINK_IsHalted()
TA260 001:691.690 - 0.495ms returns FALSE
TA260 001:691.695 JLINK_HasError()
TA260 001:693.704 JLINK_IsHalted()
TA260 001:694.160 - 0.455ms returns FALSE
TA260 001:694.172 JLINK_HasError()
TA260 001:695.704 JLINK_IsHalted()
TA260 001:696.183 - 0.478ms returns FALSE
TA260 001:696.193 JLINK_HasError()
TA260 001:697.706 JLINK_IsHalted()
TA260 001:698.277 - 0.571ms returns FALSE
TA260 001:698.283 JLINK_HasError()
TA260 001:699.703 JLINK_IsHalted()
TA260 001:700.190 - 0.486ms returns FALSE
TA260 001:700.196 JLINK_HasError()
TA260 001:702.206 JLINK_IsHalted()
TA260 001:702.666 - 0.459ms returns FALSE
TA260 001:702.676 JLINK_HasError()
TA260 001:704.210 JLINK_IsHalted()
TA260 001:704.714 - 0.503ms returns FALSE
TA260 001:704.721 JLINK_HasError()
TA260 001:706.209 JLINK_IsHalted()
TA260 001:706.699 - 0.489ms returns FALSE
TA260 001:706.705 JLINK_HasError()
TA260 001:708.207 JLINK_IsHalted()
TA260 001:708.688 - 0.480ms returns FALSE
TA260 001:708.694 JLINK_HasError()
TA260 001:710.209 JLINK_IsHalted()
TA260 001:710.666 - 0.457ms returns FALSE
TA260 001:710.672 JLINK_HasError()
TA260 001:712.214 JLINK_IsHalted()
TA260 001:712.717 - 0.503ms returns FALSE
TA260 001:712.723 JLINK_HasError()
TA260 001:714.720 JLINK_IsHalted()
TA260 001:715.195 - 0.474ms returns FALSE
TA260 001:715.203 JLINK_HasError()
TA260 001:716.721 JLINK_IsHalted()
TA260 001:717.284 - 0.563ms returns FALSE
TA260 001:717.295 JLINK_HasError()
TA260 001:718.726 JLINK_IsHalted()
TA260 001:719.270 - 0.543ms returns FALSE
TA260 001:719.280 JLINK_HasError()
TA260 001:720.719 JLINK_IsHalted()
TA260 001:721.271 - 0.551ms returns FALSE
TA260 001:721.279 JLINK_HasError()
TA260 001:723.219 JLINK_IsHalted()
TA260 001:723.691 - 0.471ms returns FALSE
TA260 001:723.698 JLINK_HasError()
TA260 001:725.740 JLINK_IsHalted()
TA260 001:726.184 - 0.443ms returns FALSE
TA260 001:726.230 JLINK_HasError()
TA260 001:727.477 JLINK_IsHalted()
TA260 001:728.001 - 0.523ms returns FALSE
TA260 001:728.012 JLINK_HasError()
TA260 001:730.481 JLINK_IsHalted()
TA260 001:731.022 - 0.541ms returns FALSE
TA260 001:731.035 JLINK_HasError()
TA260 001:732.982 JLINK_IsHalted()
TA260 001:733.468 - 0.485ms returns FALSE
TA260 001:733.481 JLINK_HasError()
TA260 001:734.985 JLINK_IsHalted()
TA260 001:735.498 - 0.512ms returns FALSE
TA260 001:735.504 JLINK_HasError()
TA260 001:736.983 JLINK_IsHalted()
TA260 001:737.462 - 0.479ms returns FALSE
TA260 001:737.469 JLINK_HasError()
TA260 001:738.985 JLINK_IsHalted()
TA260 001:739.461 - 0.475ms returns FALSE
TA260 001:739.469 JLINK_HasError()
TA260 001:740.982 JLINK_IsHalted()
TA260 001:741.494 - 0.512ms returns FALSE
TA260 001:741.500 JLINK_HasError()
TA260 001:743.491 JLINK_IsHalted()
TA260 001:743.952 - 0.460ms returns FALSE
TA260 001:743.962 JLINK_HasError()
TA260 001:745.496 JLINK_IsHalted()
TA260 001:746.016 - 0.520ms returns FALSE
TA260 001:746.024 JLINK_HasError()
TA260 001:747.497 JLINK_IsHalted()
TA260 001:747.972 - 0.474ms returns FALSE
TA260 001:747.978 JLINK_HasError()
TA260 001:749.492 JLINK_IsHalted()
TA260 001:749.972 - 0.479ms returns FALSE
TA260 001:749.978 JLINK_HasError()
TA260 001:751.992 JLINK_IsHalted()
TA260 001:752.506 - 0.513ms returns FALSE
TA260 001:752.511 JLINK_HasError()
TA260 001:753.997 JLINK_IsHalted()
TA260 001:754.464 - 0.466ms returns FALSE
TA260 001:754.471 JLINK_HasError()
TA260 001:756.001 JLINK_IsHalted()
TA260 001:756.464 - 0.462ms returns FALSE
TA260 001:756.470 JLINK_HasError()
TA260 001:758.002 JLINK_IsHalted()
TA260 001:758.420 - 0.418ms returns FALSE
TA260 001:758.426 JLINK_HasError()
TA260 001:759.998 JLINK_IsHalted()
TA260 001:760.497 - 0.498ms returns FALSE
TA260 001:760.504 JLINK_HasError()
TA260 001:761.997 JLINK_IsHalted()
TA260 001:762.541 - 0.543ms returns FALSE
TA260 001:762.548 JLINK_HasError()
TA260 001:764.509 JLINK_IsHalted()
TA260 001:765.001 - 0.493ms returns FALSE
TA260 001:765.014 JLINK_HasError()
TA260 001:766.502 JLINK_IsHalted()
TA260 001:766.971 - 0.468ms returns FALSE
TA260 001:766.977 JLINK_HasError()
TA260 001:768.502 JLINK_IsHalted()
TA260 001:769.019 - 0.517ms returns FALSE
TA260 001:769.025 JLINK_HasError()
TA260 001:770.504 JLINK_IsHalted()
TA260 001:771.008 - 0.503ms returns FALSE
TA260 001:771.016 JLINK_HasError()
TA260 001:773.009 JLINK_IsHalted()
TA260 001:773.557 - 0.547ms returns FALSE
TA260 001:773.565 JLINK_HasError()
TA260 001:775.014 JLINK_IsHalted()
TA260 001:775.510 - 0.495ms returns FALSE
TA260 001:775.522 JLINK_HasError()
TA260 001:777.011 JLINK_IsHalted()
TA260 001:777.498 - 0.486ms returns FALSE
TA260 001:777.506 JLINK_HasError()
TA260 001:779.013 JLINK_IsHalted()
TA260 001:779.498 - 0.484ms returns FALSE
TA260 001:779.506 JLINK_HasError()
TA260 001:781.011 JLINK_IsHalted()
TA260 001:781.463 - 0.451ms returns FALSE
TA260 001:781.469 JLINK_HasError()
TA260 001:782.512 JLINK_IsHalted()
TA260 001:782.984 - 0.472ms returns FALSE
TA260 001:782.990 JLINK_HasError()
TA260 001:784.517 JLINK_IsHalted()
TA260 001:785.020 - 0.503ms returns FALSE
TA260 001:785.026 JLINK_HasError()
TA260 001:786.516 JLINK_IsHalted()
TA260 001:786.996 - 0.480ms returns FALSE
TA260 001:787.002 JLINK_HasError()
TA260 001:788.523 JLINK_IsHalted()
TA260 001:788.998 - 0.475ms returns FALSE
TA260 001:789.006 JLINK_HasError()
TA260 001:790.518 JLINK_IsHalted()
TA260 001:791.022 - 0.502ms returns FALSE
TA260 001:791.031 JLINK_HasError()
TA260 001:793.020 JLINK_IsHalted()
TA260 001:793.500 - 0.480ms returns FALSE
TA260 001:793.508 JLINK_HasError()
TA260 001:795.027 JLINK_IsHalted()
TA260 001:795.498 - 0.470ms returns FALSE
TA260 001:795.505 JLINK_HasError()
TA260 001:797.022 JLINK_IsHalted()
TA260 001:797.496 - 0.472ms returns FALSE
TA260 001:797.501 JLINK_HasError()
TA260 001:799.028 JLINK_IsHalted()
TA260 001:799.565 - 0.537ms returns FALSE
TA260 001:799.572 JLINK_HasError()
TA260 001:801.023 JLINK_IsHalted()
TA260 001:801.541 - 0.517ms returns FALSE
TA260 001:801.546 JLINK_HasError()
TA260 001:803.534 JLINK_IsHalted()
TA260 001:803.977 - 0.442ms returns FALSE
TA260 001:803.992 JLINK_HasError()
TA260 001:805.537 JLINK_IsHalted()
TA260 001:806.070 - 0.532ms returns FALSE
TA260 001:806.083 JLINK_HasError()
TA260 001:807.533 JLINK_IsHalted()
TA260 001:808.030 - 0.496ms returns FALSE
TA260 001:808.036 JLINK_HasError()
TA260 001:809.537 JLINK_IsHalted()
TA260 001:810.058 - 0.520ms returns FALSE
TA260 001:810.066 JLINK_HasError()
TA260 001:812.035 JLINK_IsHalted()
TA260 001:814.347   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:814.846 - 2.810ms returns TRUE
TA260 001:814.860 JLINK_ReadReg(R15 (PC))
TA260 001:814.866 - 0.006ms returns 0x20000000
TA260 001:814.871 JLINK_ClrBPEx(BPHandle = 0x00000007)
TA260 001:814.875 - 0.004ms returns 0x00
TA260 001:814.879 JLINK_ReadReg(R0)
TA260 001:814.883 - 0.003ms returns 0x00000000
TA260 001:815.265 JLINK_HasError()
TA260 001:815.275 JLINK_WriteReg(R0, 0x0800C000)
TA260 001:815.280 - 0.004ms returns 0
TA260 001:815.284 JLINK_WriteReg(R1, 0x00004000)
TA260 001:815.288 - 0.003ms returns 0
TA260 001:815.292 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:815.295 - 0.003ms returns 0
TA260 001:815.299 JLINK_WriteReg(R3, 0x00000000)
TA260 001:815.302 - 0.003ms returns 0
TA260 001:815.306 JLINK_WriteReg(R4, 0x00000000)
TA260 001:815.310 - 0.003ms returns 0
TA260 001:815.314 JLINK_WriteReg(R5, 0x00000000)
TA260 001:815.317 - 0.003ms returns 0
TA260 001:815.321 JLINK_WriteReg(R6, 0x00000000)
TA260 001:815.325 - 0.003ms returns 0
TA260 001:815.329 JLINK_WriteReg(R7, 0x00000000)
TA260 001:815.332 - 0.003ms returns 0
TA260 001:815.336 JLINK_WriteReg(R8, 0x00000000)
TA260 001:815.340 - 0.003ms returns 0
TA260 001:815.344 JLINK_WriteReg(R9, 0x20000180)
TA260 001:815.348 - 0.003ms returns 0
TA260 001:815.352 JLINK_WriteReg(R10, 0x00000000)
TA260 001:815.355 - 0.003ms returns 0
TA260 001:815.359 JLINK_WriteReg(R11, 0x00000000)
TA260 001:815.363 - 0.003ms returns 0
TA260 001:815.367 JLINK_WriteReg(R12, 0x00000000)
TA260 001:815.370 - 0.003ms returns 0
TA260 001:815.374 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:815.378 - 0.003ms returns 0
TA260 001:815.382 JLINK_WriteReg(R14, 0x20000001)
TA260 001:815.385 - 0.003ms returns 0
TA260 001:815.389 JLINK_WriteReg(R15 (PC), 0x20000020)
TA260 001:815.393 - 0.003ms returns 0
TA260 001:815.397 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:815.400 - 0.003ms returns 0
TA260 001:815.404 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:815.408 - 0.003ms returns 0
TA260 001:815.412 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:815.419 - 0.007ms returns 0
TA260 001:815.424 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:815.427 - 0.003ms returns 0
TA260 001:815.431 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:815.436 - 0.004ms returns 0x00000008
TA260 001:815.440 JLINK_Go()
TA260 001:815.449   CPU_ReadMem(4 bytes @ 0x********)
TA260 001:818.284 - 2.844ms 
TA260 001:818.295 JLINK_IsHalted()
TA260 001:820.678   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 001:821.167 - 2.871ms returns TRUE
TA260 001:821.174 JLINK_ReadReg(R15 (PC))
TA260 001:821.178 - 0.004ms returns 0x20000000
TA260 001:821.183 JLINK_ClrBPEx(BPHandle = 0x00000008)
TA260 001:821.187 - 0.003ms returns 0x00
TA260 001:821.191 JLINK_ReadReg(R0)
TA260 001:821.194 - 0.003ms returns 0x00000001
TA260 001:821.199 JLINK_HasError()
TA260 001:821.203 JLINK_WriteReg(R0, 0x0800C000)
TA260 001:821.207 - 0.003ms returns 0
TA260 001:821.211 JLINK_WriteReg(R1, 0x00004000)
TA260 001:821.215 - 0.003ms returns 0
TA260 001:821.219 JLINK_WriteReg(R2, 0x000000FF)
TA260 001:821.222 - 0.003ms returns 0
TA260 001:821.226 JLINK_WriteReg(R3, 0x00000000)
TA260 001:821.230 - 0.003ms returns 0
TA260 001:821.234 JLINK_WriteReg(R4, 0x00000000)
TA260 001:821.237 - 0.003ms returns 0
TA260 001:821.241 JLINK_WriteReg(R5, 0x00000000)
TA260 001:821.244 - 0.003ms returns 0
TA260 001:821.249 JLINK_WriteReg(R6, 0x00000000)
TA260 001:821.252 - 0.003ms returns 0
TA260 001:821.256 JLINK_WriteReg(R7, 0x00000000)
TA260 001:821.260 - 0.003ms returns 0
TA260 001:821.264 JLINK_WriteReg(R8, 0x00000000)
TA260 001:821.267 - 0.003ms returns 0
TA260 001:821.271 JLINK_WriteReg(R9, 0x20000180)
TA260 001:821.274 - 0.003ms returns 0
TA260 001:821.278 JLINK_WriteReg(R10, 0x00000000)
TA260 001:821.282 - 0.003ms returns 0
TA260 001:821.286 JLINK_WriteReg(R11, 0x00000000)
TA260 001:821.289 - 0.003ms returns 0
TA260 001:821.321 JLINK_WriteReg(R12, 0x00000000)
TA260 001:821.325 - 0.003ms returns 0
TA260 001:821.329 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 001:821.333 - 0.003ms returns 0
TA260 001:821.337 JLINK_WriteReg(R14, 0x20000001)
TA260 001:821.340 - 0.003ms returns 0
TA260 001:821.345 JLINK_WriteReg(R15 (PC), 0x200000C0)
TA260 001:821.348 - 0.003ms returns 0
TA260 001:821.352 JLINK_WriteReg(XPSR, 0x01000000)
TA260 001:821.355 - 0.003ms returns 0
TA260 001:821.360 JLINK_WriteReg(MSP, 0x20001000)
TA260 001:821.363 - 0.003ms returns 0
TA260 001:821.367 JLINK_WriteReg(PSP, 0x20001000)
TA260 001:821.370 - 0.003ms returns 0
TA260 001:821.374 JLINK_WriteReg(CFBP, 0x00000000)
TA260 001:821.378 - 0.003ms returns 0
TA260 001:821.382 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 001:821.386 - 0.004ms returns 0x00000009
TA260 001:821.390 JLINK_Go()
TA260 001:821.396   CPU_ReadMem(4 bytes @ 0x********)
TA260 001:824.185 - 2.794ms 
TA260 001:824.199 JLINK_IsHalted()
TA260 001:824.712 - 0.512ms returns FALSE
TA260 001:824.725 JLINK_HasError()
TA260 001:826.590 JLINK_IsHalted()
TA260 001:827.050 - 0.460ms returns FALSE
TA260 001:827.056 JLINK_HasError()
TA260 001:828.588 JLINK_IsHalted()
TA260 001:829.042 - 0.454ms returns FALSE
TA260 001:829.052 JLINK_HasError()
TA260 001:830.596 JLINK_IsHalted()
TA260 001:831.075 - 0.478ms returns FALSE
TA260 001:831.080 JLINK_HasError()
TA260 001:833.092 JLINK_IsHalted()
TA260 001:833.591 - 0.499ms returns FALSE
TA260 001:833.602 JLINK_HasError()
TA260 001:835.105 JLINK_IsHalted()
TA260 001:835.594 - 0.488ms returns FALSE
TA260 001:835.607 JLINK_HasError()
TA260 001:838.099 JLINK_IsHalted()
TA260 001:838.577 - 0.477ms returns FALSE
TA260 001:838.583 JLINK_HasError()
TA260 001:840.096 JLINK_IsHalted()
TA260 001:840.587 - 0.490ms returns FALSE
TA260 001:840.593 JLINK_HasError()
TA260 001:842.098 JLINK_IsHalted()
TA260 001:842.586 - 0.487ms returns FALSE
TA260 001:842.591 JLINK_HasError()
TA260 001:844.609 JLINK_IsHalted()
TA260 001:845.123 - 0.514ms returns FALSE
TA260 001:845.129 JLINK_HasError()
TA260 001:846.603 JLINK_IsHalted()
TA260 001:847.097 - 0.493ms returns FALSE
TA260 001:847.112 JLINK_HasError()
TA260 001:848.604 JLINK_IsHalted()
TA260 001:849.098 - 0.494ms returns FALSE
TA260 001:849.104 JLINK_HasError()
TA260 001:850.607 JLINK_IsHalted()
TA260 001:851.088 - 0.480ms returns FALSE
TA260 001:851.094 JLINK_HasError()
TA260 001:853.109 JLINK_IsHalted()
TA260 001:853.599 - 0.489ms returns FALSE
TA260 001:853.606 JLINK_HasError()
TA260 001:855.119 JLINK_IsHalted()
TA260 001:855.607 - 0.487ms returns FALSE
TA260 001:855.613 JLINK_HasError()
TA260 001:857.113 JLINK_IsHalted()
TA260 001:857.588 - 0.474ms returns FALSE
TA260 001:857.594 JLINK_HasError()
TA260 001:859.113 JLINK_IsHalted()
TA260 001:859.637 - 0.522ms returns FALSE
TA260 001:859.652 JLINK_HasError()
TA260 001:861.116 JLINK_IsHalted()
TA260 001:861.592 - 0.475ms returns FALSE
TA260 001:861.598 JLINK_HasError()
TA260 001:863.621 JLINK_IsHalted()
TA260 001:864.116 - 0.495ms returns FALSE
TA260 001:864.123 JLINK_HasError()
TA260 001:865.622 JLINK_IsHalted()
TA260 001:866.194 - 0.571ms returns FALSE
TA260 001:866.207 JLINK_HasError()
TA260 001:867.624 JLINK_IsHalted()
TA260 001:868.077 - 0.452ms returns FALSE
TA260 001:868.083 JLINK_HasError()
TA260 001:869.622 JLINK_IsHalted()
TA260 001:870.108 - 0.485ms returns FALSE
TA260 001:870.114 JLINK_HasError()
TA260 001:872.123 JLINK_IsHalted()
TA260 001:872.562 - 0.439ms returns FALSE
TA260 001:872.568 JLINK_HasError()
TA260 001:874.127 JLINK_IsHalted()
TA260 001:874.635 - 0.507ms returns FALSE
TA260 001:874.646 JLINK_HasError()
TA260 001:876.130 JLINK_IsHalted()
TA260 001:876.649 - 0.518ms returns FALSE
TA260 001:876.658 JLINK_HasError()
TA260 001:878.127 JLINK_IsHalted()
TA260 001:878.599 - 0.472ms returns FALSE
TA260 001:878.605 JLINK_HasError()
TA260 001:880.127 JLINK_IsHalted()
TA260 001:880.586 - 0.459ms returns FALSE
TA260 001:880.593 JLINK_HasError()
TA260 001:882.134 JLINK_IsHalted()
TA260 001:882.601 - 0.467ms returns FALSE
TA260 001:882.608 JLINK_HasError()
TA260 001:883.639 JLINK_IsHalted()
TA260 001:884.170 - 0.529ms returns FALSE
TA260 001:884.182 JLINK_HasError()
TA260 001:885.646 JLINK_IsHalted()
TA260 001:886.085 - 0.439ms returns FALSE
TA260 001:886.097 JLINK_HasError()
TA260 001:887.639 JLINK_IsHalted()
TA260 001:888.095 - 0.456ms returns FALSE
TA260 001:888.101 JLINK_HasError()
TA260 001:889.641 JLINK_IsHalted()
TA260 001:890.090 - 0.449ms returns FALSE
TA260 001:890.096 JLINK_HasError()
TA260 001:892.142 JLINK_IsHalted()
TA260 001:892.643 - 0.501ms returns FALSE
TA260 001:892.649 JLINK_HasError()
TA260 001:894.154 JLINK_IsHalted()
TA260 001:894.613 - 0.458ms returns FALSE
TA260 001:894.620 JLINK_HasError()
TA260 001:896.147 JLINK_IsHalted()
TA260 001:896.608 - 0.461ms returns FALSE
TA260 001:896.614 JLINK_HasError()
TA260 001:898.151 JLINK_IsHalted()
TA260 001:898.594 - 0.442ms returns FALSE
TA260 001:898.607 JLINK_HasError()
TA260 001:900.147 JLINK_IsHalted()
TA260 001:900.634 - 0.486ms returns FALSE
TA260 001:900.640 JLINK_HasError()
TA260 001:902.147 JLINK_IsHalted()
TA260 001:902.648 - 0.501ms returns FALSE
TA260 001:902.654 JLINK_HasError()
TA260 001:904.655 JLINK_IsHalted()
TA260 001:905.168 - 0.513ms returns FALSE
TA260 001:905.182 JLINK_HasError()
TA260 001:906.652 JLINK_IsHalted()
TA260 001:907.145 - 0.492ms returns FALSE
TA260 001:907.152 JLINK_HasError()
TA260 001:908.652 JLINK_IsHalted()
TA260 001:909.132 - 0.480ms returns FALSE
TA260 001:909.138 JLINK_HasError()
TA260 001:910.654 JLINK_IsHalted()
TA260 001:911.089 - 0.434ms returns FALSE
TA260 001:911.099 JLINK_HasError()
TA260 001:912.156 JLINK_IsHalted()
TA260 001:912.695 - 0.538ms returns FALSE
TA260 001:912.700 JLINK_HasError()
TA260 001:914.166 JLINK_IsHalted()
TA260 001:914.635 - 0.469ms returns FALSE
TA260 001:914.643 JLINK_HasError()
TA260 001:916.162 JLINK_IsHalted()
TA260 001:916.611 - 0.448ms returns FALSE
TA260 001:916.618 JLINK_HasError()
TA260 001:918.160 JLINK_IsHalted()
TA260 001:918.644 - 0.484ms returns FALSE
TA260 001:918.650 JLINK_HasError()
TA260 001:920.159 JLINK_IsHalted()
TA260 001:920.653 - 0.493ms returns FALSE
TA260 001:920.665 JLINK_HasError()
TA260 001:922.160 JLINK_IsHalted()
TA260 001:922.632 - 0.471ms returns FALSE
TA260 001:922.638 JLINK_HasError()
TA260 001:924.172 JLINK_IsHalted()
TA260 001:924.654 - 0.482ms returns FALSE
TA260 001:924.660 JLINK_HasError()
TA260 001:926.171 JLINK_IsHalted()
TA260 001:926.657 - 0.485ms returns FALSE
TA260 001:926.663 JLINK_HasError()
TA260 001:928.170 JLINK_IsHalted()
TA260 001:928.699 - 0.529ms returns FALSE
TA260 001:928.710 JLINK_HasError()
TA260 001:930.173 JLINK_IsHalted()
TA260 001:930.701 - 0.528ms returns FALSE
TA260 001:930.711 JLINK_HasError()
TA260 001:932.170 JLINK_IsHalted()
TA260 001:932.666 - 0.496ms returns FALSE
TA260 001:932.672 JLINK_HasError()
TA260 001:934.675 JLINK_IsHalted()
TA260 001:935.155 - 0.480ms returns FALSE
TA260 001:935.161 JLINK_HasError()
TA260 001:936.673 JLINK_IsHalted()
TA260 001:937.165 - 0.491ms returns FALSE
TA260 001:937.170 JLINK_HasError()
TA260 001:938.673 JLINK_IsHalted()
TA260 001:939.144 - 0.471ms returns FALSE
TA260 001:939.150 JLINK_HasError()
TA260 001:940.675 JLINK_IsHalted()
TA260 001:941.176 - 0.500ms returns FALSE
TA260 001:941.182 JLINK_HasError()
TA260 001:943.179 JLINK_IsHalted()
TA260 001:943.957 - 0.777ms returns FALSE
TA260 001:943.971 JLINK_HasError()
TA260 001:947.183 JLINK_IsHalted()
TA260 001:947.657 - 0.473ms returns FALSE
TA260 001:947.664 JLINK_HasError()
TA260 001:949.183 JLINK_IsHalted()
TA260 001:949.698 - 0.514ms returns FALSE
TA260 001:949.704 JLINK_HasError()
TA260 001:951.182 JLINK_IsHalted()
TA260 001:951.678 - 0.496ms returns FALSE
TA260 001:951.684 JLINK_HasError()
TA260 001:953.686 JLINK_IsHalted()
TA260 001:954.123 - 0.437ms returns FALSE
TA260 001:954.132 JLINK_HasError()
TA260 001:955.689 JLINK_IsHalted()
TA260 001:956.166 - 0.476ms returns FALSE
TA260 001:956.175 JLINK_HasError()
TA260 001:957.686 JLINK_IsHalted()
TA260 001:958.188 - 0.501ms returns FALSE
TA260 001:958.194 JLINK_HasError()
TA260 001:959.690 JLINK_IsHalted()
TA260 001:960.134 - 0.443ms returns FALSE
TA260 001:960.148 JLINK_HasError()
TA260 001:962.190 JLINK_IsHalted()
TA260 001:962.657 - 0.466ms returns FALSE
TA260 001:962.664 JLINK_HasError()
TA260 001:964.206 JLINK_IsHalted()
TA260 001:964.648 - 0.441ms returns FALSE
TA260 001:964.659 JLINK_HasError()
TA260 001:966.196 JLINK_IsHalted()
TA260 001:966.690 - 0.494ms returns FALSE
TA260 001:966.696 JLINK_HasError()
TA260 001:968.193 JLINK_IsHalted()
TA260 001:968.690 - 0.497ms returns FALSE
TA260 001:968.696 JLINK_HasError()
TA260 001:970.193 JLINK_IsHalted()
TA260 001:970.654 - 0.461ms returns FALSE
TA260 001:970.660 JLINK_HasError()
TA260 001:972.194 JLINK_IsHalted()
TA260 001:972.702 - 0.507ms returns FALSE
TA260 001:972.707 JLINK_HasError()
TA260 001:974.705 JLINK_IsHalted()
TA260 001:975.132 - 0.427ms returns FALSE
TA260 001:975.138 JLINK_HasError()
TA260 001:976.701 JLINK_IsHalted()
TA260 001:977.173 - 0.471ms returns FALSE
TA260 001:977.179 JLINK_HasError()
TA260 001:978.699 JLINK_IsHalted()
TA260 001:979.169 - 0.469ms returns FALSE
TA260 001:979.176 JLINK_HasError()
TA260 001:980.706 JLINK_IsHalted()
TA260 001:981.201 - 0.495ms returns FALSE
TA260 001:981.215 JLINK_HasError()
TA260 001:983.206 JLINK_IsHalted()
TA260 001:983.738 - 0.532ms returns FALSE
TA260 001:983.749 JLINK_HasError()
TA260 001:985.208 JLINK_IsHalted()
TA260 001:985.714 - 0.505ms returns FALSE
TA260 001:985.721 JLINK_HasError()
TA260 001:987.206 JLINK_IsHalted()
TA260 001:987.712 - 0.505ms returns FALSE
TA260 001:987.718 JLINK_HasError()
TA260 001:989.207 JLINK_IsHalted()
TA260 001:989.748 - 0.540ms returns FALSE
TA260 001:989.758 JLINK_HasError()
TA260 001:991.209 JLINK_IsHalted()
TA260 001:991.690 - 0.481ms returns FALSE
TA260 001:991.696 JLINK_HasError()
TA260 001:993.714 JLINK_IsHalted()
TA260 001:994.183 - 0.468ms returns FALSE
TA260 001:994.195 JLINK_HasError()
TA260 001:995.717 JLINK_IsHalted()
TA260 001:996.277 - 0.559ms returns FALSE
TA260 001:996.284 JLINK_HasError()
TA260 001:997.715 JLINK_IsHalted()
TA260 001:998.198 - 0.482ms returns FALSE
TA260 001:998.204 JLINK_HasError()
TA260 001:999.713 JLINK_IsHalted()
TA260 002:000.189 - 0.475ms returns FALSE
TA260 002:000.194 JLINK_HasError()
TA260 002:002.216 JLINK_IsHalted()
TA260 002:002.733 - 0.516ms returns FALSE
TA260 002:002.738 JLINK_HasError()
TA260 002:004.226 JLINK_IsHalted()
TA260 002:004.692 - 0.465ms returns FALSE
TA260 002:004.702 JLINK_HasError()
TA260 002:006.226 JLINK_IsHalted()
TA260 002:006.644 - 0.418ms returns FALSE
TA260 002:006.654 JLINK_HasError()
TA260 002:008.219 JLINK_IsHalted()
TA260 002:008.700 - 0.480ms returns FALSE
TA260 002:008.706 JLINK_HasError()
TA260 002:010.221 JLINK_IsHalted()
TA260 002:010.699 - 0.477ms returns FALSE
TA260 002:010.706 JLINK_HasError()
TA260 002:012.224 JLINK_IsHalted()
TA260 002:012.709 - 0.484ms returns FALSE
TA260 002:012.719 JLINK_HasError()
TA260 002:014.728 JLINK_IsHalted()
TA260 002:015.204 - 0.476ms returns FALSE
TA260 002:015.216 JLINK_HasError()
TA260 002:016.727 JLINK_IsHalted()
TA260 002:017.268 - 0.541ms returns FALSE
TA260 002:017.274 JLINK_HasError()
TA260 002:018.727 JLINK_IsHalted()
TA260 002:019.278 - 0.551ms returns FALSE
TA260 002:019.287 JLINK_HasError()
TA260 002:020.728 JLINK_IsHalted()
TA260 002:021.272 - 0.543ms returns FALSE
TA260 002:021.281 JLINK_HasError()
TA260 002:023.231 JLINK_IsHalted()
TA260 002:023.787 - 0.555ms returns FALSE
TA260 002:023.800 JLINK_HasError()
TA260 002:025.742 JLINK_IsHalted()
TA260 002:026.274 - 0.531ms returns FALSE
TA260 002:026.286 JLINK_HasError()
TA260 002:027.738 JLINK_IsHalted()
TA260 002:028.200 - 0.462ms returns FALSE
TA260 002:028.206 JLINK_HasError()
TA260 002:029.738 JLINK_IsHalted()
TA260 002:030.198 - 0.460ms returns FALSE
TA260 002:030.204 JLINK_HasError()
TA260 002:032.245 JLINK_IsHalted()
TA260 002:032.791 - 0.546ms returns FALSE
TA260 002:032.801 JLINK_HasError()
TA260 002:034.247 JLINK_IsHalted()
TA260 002:034.746 - 0.498ms returns FALSE
TA260 002:034.751 JLINK_HasError()
TA260 002:036.246 JLINK_IsHalted()
TA260 002:036.717 - 0.470ms returns FALSE
TA260 002:036.730 JLINK_HasError()
TA260 002:038.247 JLINK_IsHalted()
TA260 002:038.757 - 0.509ms returns FALSE
TA260 002:038.763 JLINK_HasError()
TA260 002:040.246 JLINK_IsHalted()
TA260 002:040.747 - 0.501ms returns FALSE
TA260 002:040.753 JLINK_HasError()
TA260 002:042.246 JLINK_IsHalted()
TA260 002:042.734 - 0.487ms returns FALSE
TA260 002:042.740 JLINK_HasError()
TA260 002:044.754 JLINK_IsHalted()
TA260 002:045.276 - 0.522ms returns FALSE
TA260 002:045.282 JLINK_HasError()
TA260 002:046.751 JLINK_IsHalted()
TA260 002:047.269 - 0.517ms returns FALSE
TA260 002:047.275 JLINK_HasError()
TA260 002:048.752 JLINK_IsHalted()
TA260 002:049.278 - 0.525ms returns FALSE
TA260 002:049.284 JLINK_HasError()
TA260 002:050.752 JLINK_IsHalted()
TA260 002:051.268 - 0.516ms returns FALSE
TA260 002:051.274 JLINK_HasError()
TA260 002:053.258 JLINK_IsHalted()
TA260 002:053.749 - 0.490ms returns FALSE
TA260 002:053.760 JLINK_HasError()
TA260 002:056.264 JLINK_IsHalted()
TA260 002:056.749 - 0.484ms returns FALSE
TA260 002:056.763 JLINK_HasError()
TA260 002:058.258 JLINK_IsHalted()
TA260 002:058.735 - 0.476ms returns FALSE
TA260 002:058.740 JLINK_HasError()
TA260 002:060.260 JLINK_IsHalted()
TA260 002:060.746 - 0.486ms returns FALSE
TA260 002:060.752 JLINK_HasError()
TA260 002:062.259 JLINK_IsHalted()
TA260 002:062.793 - 0.533ms returns FALSE
TA260 002:062.798 JLINK_HasError()
TA260 002:064.768 JLINK_IsHalted()
TA260 002:065.314 - 0.546ms returns FALSE
TA260 002:065.320 JLINK_HasError()
TA260 002:066.764 JLINK_IsHalted()
TA260 002:067.268 - 0.503ms returns FALSE
TA260 002:067.274 JLINK_HasError()
TA260 002:068.766 JLINK_IsHalted()
TA260 002:069.271 - 0.504ms returns FALSE
TA260 002:069.279 JLINK_HasError()
TA260 002:070.766 JLINK_IsHalted()
TA260 002:071.276 - 0.510ms returns FALSE
TA260 002:071.282 JLINK_HasError()
TA260 002:073.271 JLINK_IsHalted()
TA260 002:073.820 - 0.548ms returns FALSE
TA260 002:073.834 JLINK_HasError()
TA260 002:075.273 JLINK_IsHalted()
TA260 002:075.767 - 0.493ms returns FALSE
TA260 002:075.773 JLINK_HasError()
TA260 002:077.272 JLINK_IsHalted()
TA260 002:077.769 - 0.497ms returns FALSE
TA260 002:077.776 JLINK_HasError()
TA260 002:079.270 JLINK_IsHalted()
TA260 002:079.747 - 0.476ms returns FALSE
TA260 002:079.753 JLINK_HasError()
TA260 002:081.273 JLINK_IsHalted()
TA260 002:081.746 - 0.472ms returns FALSE
TA260 002:081.752 JLINK_HasError()
TA260 002:083.787 JLINK_IsHalted()
TA260 002:084.282 - 0.494ms returns FALSE
TA260 002:084.293 JLINK_HasError()
TA260 002:085.784 JLINK_IsHalted()
TA260 002:086.201 - 0.416ms returns FALSE
TA260 002:086.215 JLINK_HasError()
TA260 002:087.782 JLINK_IsHalted()
TA260 002:088.313 - 0.531ms returns FALSE
TA260 002:088.319 JLINK_HasError()
TA260 002:089.781 JLINK_IsHalted()
TA260 002:090.299 - 0.517ms returns FALSE
TA260 002:090.305 JLINK_HasError()
TA260 002:092.283 JLINK_IsHalted()
TA260 002:092.792 - 0.508ms returns FALSE
TA260 002:092.797 JLINK_HasError()
TA260 002:094.291 JLINK_IsHalted()
TA260 002:094.782 - 0.491ms returns FALSE
TA260 002:094.789 JLINK_HasError()
TA260 002:096.288 JLINK_IsHalted()
TA260 002:096.812 - 0.524ms returns FALSE
TA260 002:096.818 JLINK_HasError()
TA260 002:098.288 JLINK_IsHalted()
TA260 002:098.820 - 0.531ms returns FALSE
TA260 002:098.827 JLINK_HasError()
TA260 002:100.287 JLINK_IsHalted()
TA260 002:100.790 - 0.502ms returns FALSE
TA260 002:100.796 JLINK_HasError()
TA260 002:102.288 JLINK_IsHalted()
TA260 002:102.830 - 0.541ms returns FALSE
TA260 002:102.843 JLINK_HasError()
TA260 002:104.794 JLINK_IsHalted()
TA260 002:105.315 - 0.520ms returns FALSE
TA260 002:105.322 JLINK_HasError()
TA260 002:106.795 JLINK_IsHalted()
TA260 002:107.277 - 0.482ms returns FALSE
TA260 002:107.285 JLINK_HasError()
TA260 002:108.794 JLINK_IsHalted()
TA260 002:109.275 - 0.481ms returns FALSE
TA260 002:109.281 JLINK_HasError()
TA260 002:110.796 JLINK_IsHalted()
TA260 002:111.277 - 0.481ms returns FALSE
TA260 002:111.283 JLINK_HasError()
TA260 002:113.300 JLINK_IsHalted()
TA260 002:113.850 - 0.549ms returns FALSE
TA260 002:113.863 JLINK_HasError()
TA260 002:116.306 JLINK_IsHalted()
TA260 002:116.769 - 0.462ms returns FALSE
TA260 002:116.775 JLINK_HasError()
TA260 002:118.300 JLINK_IsHalted()
TA260 002:118.778 - 0.478ms returns FALSE
TA260 002:118.784 JLINK_HasError()
TA260 002:120.300 JLINK_IsHalted()
TA260 002:120.791 - 0.491ms returns FALSE
TA260 002:120.797 JLINK_HasError()
TA260 002:122.302 JLINK_IsHalted()
TA260 002:122.837 - 0.534ms returns FALSE
TA260 002:122.842 JLINK_HasError()
TA260 002:124.312 JLINK_IsHalted()
TA260 002:124.758 - 0.445ms returns FALSE
TA260 002:124.764 JLINK_HasError()
TA260 002:126.310 JLINK_IsHalted()
TA260 002:126.758 - 0.447ms returns FALSE
TA260 002:126.763 JLINK_HasError()
TA260 002:128.307 JLINK_IsHalted()
TA260 002:128.810 - 0.502ms returns FALSE
TA260 002:128.820 JLINK_HasError()
TA260 002:130.643 JLINK_IsHalted()
TA260 002:131.144 - 0.500ms returns FALSE
TA260 002:131.150 JLINK_HasError()
TA260 002:132.240 JLINK_IsHalted()
TA260 002:132.744 - 0.503ms returns FALSE
TA260 002:132.755 JLINK_HasError()
TA260 002:134.750 JLINK_IsHalted()
TA260 002:135.278 - 0.528ms returns FALSE
TA260 002:135.289 JLINK_HasError()
TA260 002:136.752 JLINK_IsHalted()
TA260 002:137.281 - 0.529ms returns FALSE
TA260 002:137.288 JLINK_HasError()
TA260 002:138.755 JLINK_IsHalted()
TA260 002:139.279 - 0.524ms returns FALSE
TA260 002:139.286 JLINK_HasError()
TA260 002:140.750 JLINK_IsHalted()
TA260 002:141.276 - 0.526ms returns FALSE
TA260 002:141.284 JLINK_HasError()
TA260 002:142.755 JLINK_IsHalted()
TA260 002:143.191 - 0.436ms returns FALSE
TA260 002:143.199 JLINK_HasError()
TA260 002:144.765 JLINK_IsHalted()
TA260 002:145.270 - 0.504ms returns FALSE
TA260 002:145.276 JLINK_HasError()
TA260 002:146.760 JLINK_IsHalted()
TA260 002:147.278 - 0.518ms returns FALSE
TA260 002:147.284 JLINK_HasError()
TA260 002:148.762 JLINK_IsHalted()
TA260 002:149.278 - 0.516ms returns FALSE
TA260 002:149.286 JLINK_HasError()
TA260 002:150.761 JLINK_IsHalted()
TA260 002:151.278 - 0.517ms returns FALSE
TA260 002:151.285 JLINK_HasError()
TA260 002:153.265 JLINK_IsHalted()
TA260 002:153.771 - 0.505ms returns FALSE
TA260 002:153.778 JLINK_HasError()
TA260 002:155.264 JLINK_IsHalted()
TA260 002:155.746 - 0.481ms returns FALSE
TA260 002:155.751 JLINK_HasError()
TA260 002:157.265 JLINK_IsHalted()
TA260 002:157.736 - 0.471ms returns FALSE
TA260 002:157.742 JLINK_HasError()
TA260 002:159.269 JLINK_IsHalted()
TA260 002:159.752 - 0.482ms returns FALSE
TA260 002:159.769 JLINK_HasError()
TA260 002:162.267 JLINK_IsHalted()
TA260 002:162.700 - 0.432ms returns FALSE
TA260 002:162.706 JLINK_HasError()
TA260 002:163.778 JLINK_IsHalted()
TA260 002:164.318 - 0.539ms returns FALSE
TA260 002:164.329 JLINK_HasError()
TA260 002:165.780 JLINK_IsHalted()
TA260 002:166.314 - 0.534ms returns FALSE
TA260 002:166.321 JLINK_HasError()
TA260 002:167.772 JLINK_IsHalted()
TA260 002:168.269 - 0.496ms returns FALSE
TA260 002:168.274 JLINK_HasError()
TA260 002:169.772 JLINK_IsHalted()
TA260 002:170.268 - 0.495ms returns FALSE
TA260 002:170.274 JLINK_HasError()
TA260 002:172.274 JLINK_IsHalted()
TA260 002:172.746 - 0.471ms returns FALSE
TA260 002:172.752 JLINK_HasError()
TA260 002:174.284 JLINK_IsHalted()
TA260 002:174.816 - 0.532ms returns FALSE
TA260 002:174.827 JLINK_HasError()
TA260 002:176.282 JLINK_IsHalted()
TA260 002:176.748 - 0.465ms returns FALSE
TA260 002:176.759 JLINK_HasError()
TA260 002:180.279 JLINK_IsHalted()
TA260 002:180.771 - 0.491ms returns FALSE
TA260 002:180.777 JLINK_HasError()
TA260 002:182.284 JLINK_IsHalted()
TA260 002:182.790 - 0.505ms returns FALSE
TA260 002:182.796 JLINK_HasError()
TA260 002:184.579 JLINK_IsHalted()
TA260 002:185.089 - 0.509ms returns FALSE
TA260 002:185.099 JLINK_HasError()
TA260 002:186.574 JLINK_IsHalted()
TA260 002:187.051 - 0.477ms returns FALSE
TA260 002:187.057 JLINK_HasError()
TA260 002:188.574 JLINK_IsHalted()
TA260 002:189.050 - 0.476ms returns FALSE
TA260 002:189.055 JLINK_HasError()
TA260 002:190.581 JLINK_IsHalted()
TA260 002:191.080 - 0.498ms returns FALSE
TA260 002:191.089 JLINK_HasError()
TA260 002:193.091 JLINK_IsHalted()
TA260 002:193.591 - 0.499ms returns FALSE
TA260 002:193.604 JLINK_HasError()
TA260 002:195.084 JLINK_IsHalted()
TA260 002:195.592 - 0.507ms returns FALSE
TA260 002:195.605 JLINK_HasError()
TA260 002:197.082 JLINK_IsHalted()
TA260 002:197.544 - 0.461ms returns FALSE
TA260 002:197.550 JLINK_HasError()
TA260 002:199.081 JLINK_IsHalted()
TA260 002:199.578 - 0.496ms returns FALSE
TA260 002:199.584 JLINK_HasError()
TA260 002:201.084 JLINK_IsHalted()
TA260 002:201.551 - 0.466ms returns FALSE
TA260 002:201.557 JLINK_HasError()
TA260 002:203.590 JLINK_IsHalted()
TA260 002:204.047 - 0.456ms returns FALSE
TA260 002:204.057 JLINK_HasError()
TA260 002:205.599 JLINK_IsHalted()
TA260 002:206.090 - 0.490ms returns FALSE
TA260 002:206.096 JLINK_HasError()
TA260 002:207.595 JLINK_IsHalted()
TA260 002:208.090 - 0.494ms returns FALSE
TA260 002:208.102 JLINK_HasError()
TA260 002:209.594 JLINK_IsHalted()
TA260 002:210.089 - 0.494ms returns FALSE
TA260 002:210.101 JLINK_HasError()
TA260 002:212.104 JLINK_IsHalted()
TA260 002:212.644 - 0.539ms returns FALSE
TA260 002:212.651 JLINK_HasError()
TA260 002:214.101 JLINK_IsHalted()
TA260 002:214.510 - 0.408ms returns FALSE
TA260 002:214.523 JLINK_HasError()
TA260 002:216.100 JLINK_IsHalted()
TA260 002:216.588 - 0.487ms returns FALSE
TA260 002:216.594 JLINK_HasError()
TA260 002:218.096 JLINK_IsHalted()
TA260 002:218.596 - 0.499ms returns FALSE
TA260 002:218.602 JLINK_HasError()
TA260 002:220.096 JLINK_IsHalted()
TA260 002:220.586 - 0.490ms returns FALSE
TA260 002:220.592 JLINK_HasError()
TA260 002:222.097 JLINK_IsHalted()
TA260 002:222.592 - 0.494ms returns FALSE
TA260 002:222.602 JLINK_HasError()
TA260 002:224.606 JLINK_IsHalted()
TA260 002:225.041 - 0.434ms returns FALSE
TA260 002:225.050 JLINK_HasError()
TA260 002:226.114 JLINK_IsHalted()
TA260 002:226.612 - 0.497ms returns FALSE
TA260 002:226.621 JLINK_HasError()
TA260 002:228.106 JLINK_IsHalted()
TA260 002:228.591 - 0.484ms returns FALSE
TA260 002:228.600 JLINK_HasError()
TA260 002:230.106 JLINK_IsHalted()
TA260 002:230.595 - 0.489ms returns FALSE
TA260 002:230.601 JLINK_HasError()
TA260 002:232.107 JLINK_IsHalted()
TA260 002:232.586 - 0.479ms returns FALSE
TA260 002:232.592 JLINK_HasError()
TA260 002:234.644 JLINK_IsHalted()
TA260 002:235.192 - 0.548ms returns FALSE
TA260 002:235.198 JLINK_HasError()
TA260 002:236.622 JLINK_IsHalted()
TA260 002:238.945   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:239.462 - 2.840ms returns TRUE
TA260 002:239.469 JLINK_ReadReg(R15 (PC))
TA260 002:239.475 - 0.005ms returns 0x20000000
TA260 002:239.479 JLINK_ClrBPEx(BPHandle = 0x00000009)
TA260 002:239.483 - 0.004ms returns 0x00
TA260 002:239.488 JLINK_ReadReg(R0)
TA260 002:239.491 - 0.003ms returns 0x00000000
TA260 002:239.757 JLINK_HasError()
TA260 002:239.764 JLINK_WriteReg(R0, 0x00000001)
TA260 002:239.769 - 0.004ms returns 0
TA260 002:239.774 JLINK_WriteReg(R1, 0x00004000)
TA260 002:239.778 - 0.003ms returns 0
TA260 002:239.782 JLINK_WriteReg(R2, 0x000000FF)
TA260 002:239.785 - 0.003ms returns 0
TA260 002:239.789 JLINK_WriteReg(R3, 0x00000000)
TA260 002:239.792 - 0.003ms returns 0
TA260 002:239.796 JLINK_WriteReg(R4, 0x00000000)
TA260 002:239.800 - 0.003ms returns 0
TA260 002:239.804 JLINK_WriteReg(R5, 0x00000000)
TA260 002:239.807 - 0.003ms returns 0
TA260 002:239.811 JLINK_WriteReg(R6, 0x00000000)
TA260 002:239.815 - 0.003ms returns 0
TA260 002:239.819 JLINK_WriteReg(R7, 0x00000000)
TA260 002:239.822 - 0.003ms returns 0
TA260 002:239.826 JLINK_WriteReg(R8, 0x00000000)
TA260 002:239.830 - 0.003ms returns 0
TA260 002:239.834 JLINK_WriteReg(R9, 0x20000180)
TA260 002:239.837 - 0.003ms returns 0
TA260 002:239.841 JLINK_WriteReg(R10, 0x00000000)
TA260 002:239.845 - 0.003ms returns 0
TA260 002:239.849 JLINK_WriteReg(R11, 0x00000000)
TA260 002:239.852 - 0.003ms returns 0
TA260 002:239.856 JLINK_WriteReg(R12, 0x00000000)
TA260 002:239.859 - 0.003ms returns 0
TA260 002:239.864 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:239.867 - 0.003ms returns 0
TA260 002:239.871 JLINK_WriteReg(R14, 0x20000001)
TA260 002:239.875 - 0.003ms returns 0
TA260 002:239.879 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 002:239.882 - 0.003ms returns 0
TA260 002:239.886 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:239.890 - 0.003ms returns 0
TA260 002:239.894 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:239.897 - 0.003ms returns 0
TA260 002:239.901 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:239.904 - 0.003ms returns 0
TA260 002:239.909 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:239.912 - 0.003ms returns 0
TA260 002:239.916 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:239.922 - 0.005ms returns 0x0000000A
TA260 002:239.926 JLINK_Go()
TA260 002:239.934   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:242.689 - 2.763ms 
TA260 002:242.695 JLINK_IsHalted()
TA260 002:245.175   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:245.692 - 2.996ms returns TRUE
TA260 002:245.699 JLINK_ReadReg(R15 (PC))
TA260 002:245.704 - 0.005ms returns 0x20000000
TA260 002:245.708 JLINK_ClrBPEx(BPHandle = 0x0000000A)
TA260 002:245.712 - 0.004ms returns 0x00
TA260 002:245.717 JLINK_ReadReg(R0)
TA260 002:245.720 - 0.003ms returns 0x00000000
TA260 002:301.093 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 002:301.105   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 002:301.122   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 002:303.010 - 1.918ms returns 0x184
TA260 002:303.032 JLINK_HasError()
TA260 002:303.038 JLINK_WriteReg(R0, 0x08000000)
TA260 002:303.044 - 0.005ms returns 0
TA260 002:303.048 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 002:303.052 - 0.003ms returns 0
TA260 002:303.056 JLINK_WriteReg(R2, 0x00000002)
TA260 002:303.059 - 0.003ms returns 0
TA260 002:303.063 JLINK_WriteReg(R3, 0x00000000)
TA260 002:303.082 - 0.019ms returns 0
TA260 002:303.087 JLINK_WriteReg(R4, 0x00000000)
TA260 002:303.090 - 0.003ms returns 0
TA260 002:303.095 JLINK_WriteReg(R5, 0x00000000)
TA260 002:303.098 - 0.003ms returns 0
TA260 002:303.102 JLINK_WriteReg(R6, 0x00000000)
TA260 002:303.106 - 0.003ms returns 0
TA260 002:303.110 JLINK_WriteReg(R7, 0x00000000)
TA260 002:303.113 - 0.003ms returns 0
TA260 002:303.117 JLINK_WriteReg(R8, 0x00000000)
TA260 002:303.120 - 0.003ms returns 0
TA260 002:303.124 JLINK_WriteReg(R9, 0x20000180)
TA260 002:303.128 - 0.003ms returns 0
TA260 002:303.132 JLINK_WriteReg(R10, 0x00000000)
TA260 002:303.136 - 0.003ms returns 0
TA260 002:303.140 JLINK_WriteReg(R11, 0x00000000)
TA260 002:303.143 - 0.003ms returns 0
TA260 002:303.147 JLINK_WriteReg(R12, 0x00000000)
TA260 002:303.150 - 0.003ms returns 0
TA260 002:303.154 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:303.158 - 0.004ms returns 0
TA260 002:303.163 JLINK_WriteReg(R14, 0x20000001)
TA260 002:303.166 - 0.003ms returns 0
TA260 002:303.170 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 002:303.174 - 0.003ms returns 0
TA260 002:303.178 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:303.181 - 0.003ms returns 0
TA260 002:303.185 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:303.189 - 0.003ms returns 0
TA260 002:303.193 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:303.196 - 0.003ms returns 0
TA260 002:303.200 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:303.203 - 0.003ms returns 0
TA260 002:303.208 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:303.215   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:303.790 - 0.581ms returns 0x0000000B
TA260 002:303.804 JLINK_Go()
TA260 002:303.811   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 002:304.288   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:307.020 - 3.215ms 
TA260 002:307.028 JLINK_IsHalted()
TA260 002:309.343   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:309.803 - 2.774ms returns TRUE
TA260 002:309.814 JLINK_ReadReg(R15 (PC))
TA260 002:309.819 - 0.004ms returns 0x20000000
TA260 002:309.823 JLINK_ClrBPEx(BPHandle = 0x0000000B)
TA260 002:309.828 - 0.004ms returns 0x00
TA260 002:309.832 JLINK_ReadReg(R0)
TA260 002:309.836 - 0.003ms returns 0x00000000
TA260 002:310.040 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:310.046   Data:  18 18 00 20 C1 01 00 08 E1 2A 00 08 C5 27 00 08 ...
TA260 002:310.060   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:312.652 - 2.611ms returns 0x27C
TA260 002:312.664 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:312.668   Data:  10 B5 13 46 0A 46 04 46 19 46 FF F7 F0 FF 20 46 ...
TA260 002:312.679   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:314.553 - 1.887ms returns 0x184
TA260 002:314.572 JLINK_HasError()
TA260 002:314.605 JLINK_WriteReg(R0, 0x08000000)
TA260 002:314.612 - 0.006ms returns 0
TA260 002:314.616 JLINK_WriteReg(R1, 0x00000400)
TA260 002:314.620 - 0.003ms returns 0
TA260 002:314.624 JLINK_WriteReg(R2, 0x20000184)
TA260 002:314.627 - 0.003ms returns 0
TA260 002:314.631 JLINK_WriteReg(R3, 0x00000000)
TA260 002:314.634 - 0.003ms returns 0
TA260 002:314.639 JLINK_WriteReg(R4, 0x00000000)
TA260 002:314.642 - 0.003ms returns 0
TA260 002:314.646 JLINK_WriteReg(R5, 0x00000000)
TA260 002:314.650 - 0.003ms returns 0
TA260 002:314.654 JLINK_WriteReg(R6, 0x00000000)
TA260 002:314.657 - 0.003ms returns 0
TA260 002:314.661 JLINK_WriteReg(R7, 0x00000000)
TA260 002:314.664 - 0.003ms returns 0
TA260 002:314.672 JLINK_WriteReg(R8, 0x00000000)
TA260 002:314.676 - 0.003ms returns 0
TA260 002:314.680 JLINK_WriteReg(R9, 0x20000180)
TA260 002:314.683 - 0.003ms returns 0
TA260 002:314.687 JLINK_WriteReg(R10, 0x00000000)
TA260 002:314.691 - 0.003ms returns 0
TA260 002:314.695 JLINK_WriteReg(R11, 0x00000000)
TA260 002:314.698 - 0.003ms returns 0
TA260 002:314.702 JLINK_WriteReg(R12, 0x00000000)
TA260 002:314.706 - 0.003ms returns 0
TA260 002:314.710 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:314.714 - 0.003ms returns 0
TA260 002:314.718 JLINK_WriteReg(R14, 0x20000001)
TA260 002:314.721 - 0.003ms returns 0
TA260 002:314.766 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:314.769 - 0.003ms returns 0
TA260 002:314.773 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:314.777 - 0.003ms returns 0
TA260 002:314.781 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:314.784 - 0.003ms returns 0
TA260 002:314.799 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:314.802 - 0.003ms returns 0
TA260 002:314.806 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:314.810 - 0.003ms returns 0
TA260 002:314.814 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:314.819 - 0.004ms returns 0x0000000C
TA260 002:314.823 JLINK_Go()
TA260 002:314.831   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:317.546 - 2.722ms 
TA260 002:317.558 JLINK_IsHalted()
TA260 002:318.065 - 0.506ms returns FALSE
TA260 002:318.071 JLINK_HasError()
TA260 002:320.677 JLINK_IsHalted()
TA260 002:321.146 - 0.468ms returns FALSE
TA260 002:321.159 JLINK_HasError()
TA260 002:323.177 JLINK_IsHalted()
TA260 002:325.594   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:326.079 - 2.901ms returns TRUE
TA260 002:326.087 JLINK_ReadReg(R15 (PC))
TA260 002:326.093 - 0.005ms returns 0x20000000
TA260 002:326.097 JLINK_ClrBPEx(BPHandle = 0x0000000C)
TA260 002:326.101 - 0.004ms returns 0x00
TA260 002:326.106 JLINK_ReadReg(R0)
TA260 002:326.110 - 0.003ms returns 0x00000000
TA260 002:326.430 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:326.437   Data:  5B D0 C3 F3 0A 54 C1 F3 0A 55 2C 44 A4 F2 F3 34 ...
TA260 002:326.447   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:329.043 - 2.613ms returns 0x27C
TA260 002:329.050 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:329.054   Data:  01 02 06 D0 0A 0D A2 F5 60 72 C1 F3 13 01 00 2A ...
TA260 002:329.062   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:330.949 - 1.899ms returns 0x184
TA260 002:330.956 JLINK_HasError()
TA260 002:330.961 JLINK_WriteReg(R0, 0x08000400)
TA260 002:330.966 - 0.004ms returns 0
TA260 002:330.970 JLINK_WriteReg(R1, 0x00000400)
TA260 002:330.973 - 0.003ms returns 0
TA260 002:330.978 JLINK_WriteReg(R2, 0x20000184)
TA260 002:330.981 - 0.003ms returns 0
TA260 002:330.985 JLINK_WriteReg(R3, 0x00000000)
TA260 002:330.988 - 0.003ms returns 0
TA260 002:330.992 JLINK_WriteReg(R4, 0x00000000)
TA260 002:330.996 - 0.003ms returns 0
TA260 002:331.000 JLINK_WriteReg(R5, 0x00000000)
TA260 002:331.003 - 0.003ms returns 0
TA260 002:331.007 JLINK_WriteReg(R6, 0x00000000)
TA260 002:331.011 - 0.003ms returns 0
TA260 002:331.015 JLINK_WriteReg(R7, 0x00000000)
TA260 002:331.018 - 0.003ms returns 0
TA260 002:331.023 JLINK_WriteReg(R8, 0x00000000)
TA260 002:331.026 - 0.003ms returns 0
TA260 002:331.030 JLINK_WriteReg(R9, 0x20000180)
TA260 002:331.033 - 0.003ms returns 0
TA260 002:331.038 JLINK_WriteReg(R10, 0x00000000)
TA260 002:331.041 - 0.003ms returns 0
TA260 002:331.045 JLINK_WriteReg(R11, 0x00000000)
TA260 002:331.048 - 0.003ms returns 0
TA260 002:331.052 JLINK_WriteReg(R12, 0x00000000)
TA260 002:331.056 - 0.003ms returns 0
TA260 002:331.060 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:331.063 - 0.003ms returns 0
TA260 002:331.067 JLINK_WriteReg(R14, 0x20000001)
TA260 002:331.071 - 0.003ms returns 0
TA260 002:331.075 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:331.078 - 0.003ms returns 0
TA260 002:331.082 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:331.086 - 0.003ms returns 0
TA260 002:331.090 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:331.093 - 0.003ms returns 0
TA260 002:331.097 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:331.101 - 0.003ms returns 0
TA260 002:331.105 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:331.108 - 0.003ms returns 0
TA260 002:331.113 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:331.117 - 0.004ms returns 0x0000000D
TA260 002:331.121 JLINK_Go()
TA260 002:331.128   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:334.082 - 2.960ms 
TA260 002:334.096 JLINK_IsHalted()
TA260 002:334.522 - 0.425ms returns FALSE
TA260 002:334.536 JLINK_HasError()
TA260 002:336.193 JLINK_IsHalted()
TA260 002:336.666 - 0.473ms returns FALSE
TA260 002:336.708 JLINK_HasError()
TA260 002:338.187 JLINK_IsHalted()
TA260 002:340.485   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:340.975 - 2.787ms returns TRUE
TA260 002:340.986 JLINK_ReadReg(R15 (PC))
TA260 002:340.990 - 0.004ms returns 0x20000000
TA260 002:341.021 JLINK_ClrBPEx(BPHandle = 0x0000000D)
TA260 002:341.026 - 0.005ms returns 0x00
TA260 002:341.031 JLINK_ReadReg(R0)
TA260 002:341.035 - 0.004ms returns 0x00000000
TA260 002:341.347 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:341.355   Data:  F0 4D 92 46 9B 46 11 B1 B1 FA 81 F2 02 E0 B0 FA ...
TA260 002:341.364   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:343.959 - 2.612ms returns 0x27C
TA260 002:343.971 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:343.975   Data:  F9 FD 20 46 4F F4 00 51 00 22 00 F0 F3 FD 01 20 ...
TA260 002:343.984   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:345.826 - 1.855ms returns 0x184
TA260 002:345.835 JLINK_HasError()
TA260 002:345.840 JLINK_WriteReg(R0, 0x08000800)
TA260 002:345.846 - 0.005ms returns 0
TA260 002:345.850 JLINK_WriteReg(R1, 0x00000400)
TA260 002:345.853 - 0.003ms returns 0
TA260 002:345.857 JLINK_WriteReg(R2, 0x20000184)
TA260 002:345.861 - 0.003ms returns 0
TA260 002:345.865 JLINK_WriteReg(R3, 0x00000000)
TA260 002:345.868 - 0.003ms returns 0
TA260 002:345.872 JLINK_WriteReg(R4, 0x00000000)
TA260 002:345.875 - 0.003ms returns 0
TA260 002:345.879 JLINK_WriteReg(R5, 0x00000000)
TA260 002:345.883 - 0.003ms returns 0
TA260 002:345.887 JLINK_WriteReg(R6, 0x00000000)
TA260 002:345.890 - 0.003ms returns 0
TA260 002:345.894 JLINK_WriteReg(R7, 0x00000000)
TA260 002:345.898 - 0.003ms returns 0
TA260 002:345.902 JLINK_WriteReg(R8, 0x00000000)
TA260 002:345.905 - 0.003ms returns 0
TA260 002:345.910 JLINK_WriteReg(R9, 0x20000180)
TA260 002:345.913 - 0.003ms returns 0
TA260 002:345.917 JLINK_WriteReg(R10, 0x00000000)
TA260 002:345.920 - 0.003ms returns 0
TA260 002:345.924 JLINK_WriteReg(R11, 0x00000000)
TA260 002:345.928 - 0.003ms returns 0
TA260 002:345.932 JLINK_WriteReg(R12, 0x00000000)
TA260 002:345.935 - 0.003ms returns 0
TA260 002:345.939 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:345.943 - 0.003ms returns 0
TA260 002:345.947 JLINK_WriteReg(R14, 0x20000001)
TA260 002:345.950 - 0.003ms returns 0
TA260 002:345.954 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:345.958 - 0.003ms returns 0
TA260 002:345.962 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:345.966 - 0.003ms returns 0
TA260 002:345.970 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:345.973 - 0.003ms returns 0
TA260 002:345.977 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:345.980 - 0.003ms returns 0
TA260 002:345.984 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:345.988 - 0.003ms returns 0
TA260 002:345.993 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:345.997 - 0.004ms returns 0x0000000E
TA260 002:346.001 JLINK_Go()
TA260 002:346.008   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:348.772 - 2.770ms 
TA260 002:348.785 JLINK_IsHalted()
TA260 002:349.270 - 0.485ms returns FALSE
TA260 002:349.276 JLINK_HasError()
TA260 002:350.699 JLINK_IsHalted()
TA260 002:351.189 - 0.489ms returns FALSE
TA260 002:351.197 JLINK_HasError()
TA260 002:353.201 JLINK_IsHalted()
TA260 002:355.773   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:356.280 - 3.078ms returns TRUE
TA260 002:356.287 JLINK_ReadReg(R15 (PC))
TA260 002:356.292 - 0.004ms returns 0x20000000
TA260 002:356.296 JLINK_ClrBPEx(BPHandle = 0x0000000E)
TA260 002:356.300 - 0.003ms returns 0x00
TA260 002:356.304 JLINK_ReadReg(R0)
TA260 002:356.308 - 0.003ms returns 0x00000000
TA260 002:356.653 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:356.660   Data:  BD E8 F0 40 00 F0 34 BD 2D E9 F0 4F 81 B0 41 F6 ...
TA260 002:356.671   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:359.285 - 2.632ms returns 0x27C
TA260 002:359.301 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:359.306   Data:  05 F0 01 02 30 46 4F F4 80 51 00 F0 F3 FB 20 46 ...
TA260 002:359.318   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:361.376 - 2.074ms returns 0x184
TA260 002:361.389 JLINK_HasError()
TA260 002:361.395 JLINK_WriteReg(R0, 0x08000C00)
TA260 002:361.400 - 0.005ms returns 0
TA260 002:361.405 JLINK_WriteReg(R1, 0x00000400)
TA260 002:361.408 - 0.003ms returns 0
TA260 002:361.412 JLINK_WriteReg(R2, 0x20000184)
TA260 002:361.416 - 0.003ms returns 0
TA260 002:361.420 JLINK_WriteReg(R3, 0x00000000)
TA260 002:361.423 - 0.003ms returns 0
TA260 002:361.427 JLINK_WriteReg(R4, 0x00000000)
TA260 002:361.430 - 0.003ms returns 0
TA260 002:361.434 JLINK_WriteReg(R5, 0x00000000)
TA260 002:361.438 - 0.003ms returns 0
TA260 002:361.442 JLINK_WriteReg(R6, 0x00000000)
TA260 002:361.445 - 0.003ms returns 0
TA260 002:361.449 JLINK_WriteReg(R7, 0x00000000)
TA260 002:361.453 - 0.003ms returns 0
TA260 002:361.457 JLINK_WriteReg(R8, 0x00000000)
TA260 002:361.460 - 0.003ms returns 0
TA260 002:361.464 JLINK_WriteReg(R9, 0x20000180)
TA260 002:361.468 - 0.003ms returns 0
TA260 002:361.472 JLINK_WriteReg(R10, 0x00000000)
TA260 002:361.475 - 0.003ms returns 0
TA260 002:361.479 JLINK_WriteReg(R11, 0x00000000)
TA260 002:361.483 - 0.003ms returns 0
TA260 002:361.487 JLINK_WriteReg(R12, 0x00000000)
TA260 002:361.490 - 0.003ms returns 0
TA260 002:361.494 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:361.498 - 0.004ms returns 0
TA260 002:361.502 JLINK_WriteReg(R14, 0x20000001)
TA260 002:361.506 - 0.003ms returns 0
TA260 002:361.510 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:361.513 - 0.003ms returns 0
TA260 002:361.517 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:361.521 - 0.003ms returns 0
TA260 002:361.525 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:361.528 - 0.003ms returns 0
TA260 002:361.532 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:361.536 - 0.003ms returns 0
TA260 002:361.540 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:361.543 - 0.003ms returns 0
TA260 002:361.548 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:361.552 - 0.004ms returns 0x0000000F
TA260 002:361.556 JLINK_Go()
TA260 002:361.564   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:364.501 - 2.945ms 
TA260 002:364.515 JLINK_IsHalted()
TA260 002:365.009 - 0.493ms returns FALSE
TA260 002:365.018 JLINK_HasError()
TA260 002:366.712 JLINK_IsHalted()
TA260 002:367.270 - 0.557ms returns FALSE
TA260 002:367.276 JLINK_HasError()
TA260 002:368.708 JLINK_IsHalted()
TA260 002:371.032   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:371.508 - 2.799ms returns TRUE
TA260 002:371.514 JLINK_ReadReg(R15 (PC))
TA260 002:371.518 - 0.004ms returns 0x20000000
TA260 002:371.523 JLINK_ClrBPEx(BPHandle = 0x0000000F)
TA260 002:371.527 - 0.003ms returns 0x00
TA260 002:371.531 JLINK_ReadReg(R0)
TA260 002:371.535 - 0.003ms returns 0x00000000
TA260 002:371.888 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:371.895   Data:  00 90 00 98 14 28 E7 DB 02 B0 BD EC 0A 8B BD E8 ...
TA260 002:371.904   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:374.517 - 2.629ms returns 0x27C
TA260 002:374.534 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:374.538   Data:  3A 43 CF 6A 1A 43 CB 6B 3A 43 0F 6B 1A 43 4B 6B ...
TA260 002:374.548   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:376.407 - 1.873ms returns 0x184
TA260 002:376.422 JLINK_HasError()
TA260 002:376.427 JLINK_WriteReg(R0, 0x08001000)
TA260 002:376.433 - 0.005ms returns 0
TA260 002:376.437 JLINK_WriteReg(R1, 0x00000400)
TA260 002:376.440 - 0.003ms returns 0
TA260 002:376.444 JLINK_WriteReg(R2, 0x20000184)
TA260 002:376.448 - 0.003ms returns 0
TA260 002:376.452 JLINK_WriteReg(R3, 0x00000000)
TA260 002:376.455 - 0.003ms returns 0
TA260 002:376.459 JLINK_WriteReg(R4, 0x00000000)
TA260 002:376.463 - 0.003ms returns 0
TA260 002:376.467 JLINK_WriteReg(R5, 0x00000000)
TA260 002:376.470 - 0.003ms returns 0
TA260 002:376.474 JLINK_WriteReg(R6, 0x00000000)
TA260 002:376.477 - 0.003ms returns 0
TA260 002:376.481 JLINK_WriteReg(R7, 0x00000000)
TA260 002:376.485 - 0.003ms returns 0
TA260 002:376.489 JLINK_WriteReg(R8, 0x00000000)
TA260 002:376.492 - 0.003ms returns 0
TA260 002:376.496 JLINK_WriteReg(R9, 0x20000180)
TA260 002:376.546 - 0.049ms returns 0
TA260 002:376.550 JLINK_WriteReg(R10, 0x00000000)
TA260 002:376.554 - 0.003ms returns 0
TA260 002:376.558 JLINK_WriteReg(R11, 0x00000000)
TA260 002:376.561 - 0.003ms returns 0
TA260 002:376.565 JLINK_WriteReg(R12, 0x00000000)
TA260 002:376.569 - 0.003ms returns 0
TA260 002:376.573 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:376.577 - 0.003ms returns 0
TA260 002:376.581 JLINK_WriteReg(R14, 0x20000001)
TA260 002:376.584 - 0.003ms returns 0
TA260 002:376.588 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:376.592 - 0.003ms returns 0
TA260 002:376.596 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:376.599 - 0.003ms returns 0
TA260 002:376.603 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:376.606 - 0.003ms returns 0
TA260 002:376.610 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:376.614 - 0.003ms returns 0
TA260 002:376.618 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:376.621 - 0.003ms returns 0
TA260 002:376.626 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:376.630 - 0.004ms returns 0x00000010
TA260 002:376.634 JLINK_Go()
TA260 002:376.643   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:379.400 - 2.765ms 
TA260 002:379.414 JLINK_IsHalted()
TA260 002:379.920 - 0.505ms returns FALSE
TA260 002:379.932 JLINK_HasError()
TA260 002:382.222 JLINK_IsHalted()
TA260 002:382.759 - 0.536ms returns FALSE
TA260 002:382.771 JLINK_HasError()
TA260 002:384.728 JLINK_IsHalted()
TA260 002:386.986   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:387.464 - 2.735ms returns TRUE
TA260 002:387.471 JLINK_ReadReg(R15 (PC))
TA260 002:387.476 - 0.004ms returns 0x20000000
TA260 002:387.480 JLINK_ClrBPEx(BPHandle = 0x00000010)
TA260 002:387.484 - 0.003ms returns 0x00
TA260 002:387.488 JLINK_ReadReg(R0)
TA260 002:387.492 - 0.003ms returns 0x00000000
TA260 002:387.812 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:387.819   Data:  00 F0 68 F8 06 B0 80 BD 70 B5 86 6D 04 46 00 F0 ...
TA260 002:387.829   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:390.360 - 2.547ms returns 0x27C
TA260 002:390.367 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:390.371   Data:  40 F2 10 40 C2 F2 00 00 00 68 70 47 40 F2 A4 00 ...
TA260 002:390.378   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:392.277 - 1.910ms returns 0x184
TA260 002:392.287 JLINK_HasError()
TA260 002:392.880 JLINK_WriteReg(R0, 0x08001400)
TA260 002:392.886 - 0.005ms returns 0
TA260 002:392.890 JLINK_WriteReg(R1, 0x00000400)
TA260 002:392.893 - 0.003ms returns 0
TA260 002:392.897 JLINK_WriteReg(R2, 0x20000184)
TA260 002:392.901 - 0.003ms returns 0
TA260 002:392.905 JLINK_WriteReg(R3, 0x00000000)
TA260 002:392.908 - 0.003ms returns 0
TA260 002:392.912 JLINK_WriteReg(R4, 0x00000000)
TA260 002:392.915 - 0.003ms returns 0
TA260 002:392.919 JLINK_WriteReg(R5, 0x00000000)
TA260 002:392.923 - 0.003ms returns 0
TA260 002:392.927 JLINK_WriteReg(R6, 0x00000000)
TA260 002:392.930 - 0.003ms returns 0
TA260 002:392.934 JLINK_WriteReg(R7, 0x00000000)
TA260 002:392.938 - 0.003ms returns 0
TA260 002:392.942 JLINK_WriteReg(R8, 0x00000000)
TA260 002:392.945 - 0.003ms returns 0
TA260 002:392.949 JLINK_WriteReg(R9, 0x20000180)
TA260 002:392.952 - 0.003ms returns 0
TA260 002:392.956 JLINK_WriteReg(R10, 0x00000000)
TA260 002:392.960 - 0.003ms returns 0
TA260 002:392.964 JLINK_WriteReg(R11, 0x00000000)
TA260 002:392.968 - 0.003ms returns 0
TA260 002:392.972 JLINK_WriteReg(R12, 0x00000000)
TA260 002:392.975 - 0.003ms returns 0
TA260 002:392.979 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:392.983 - 0.003ms returns 0
TA260 002:392.987 JLINK_WriteReg(R14, 0x20000001)
TA260 002:392.990 - 0.003ms returns 0
TA260 002:392.994 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:392.998 - 0.003ms returns 0
TA260 002:393.002 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:393.005 - 0.003ms returns 0
TA260 002:393.009 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:393.012 - 0.003ms returns 0
TA260 002:393.016 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:393.020 - 0.003ms returns 0
TA260 002:393.024 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:393.032 - 0.007ms returns 0
TA260 002:393.036 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:393.040 - 0.004ms returns 0x00000011
TA260 002:393.044 JLINK_Go()
TA260 002:393.052   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:395.854 - 2.809ms 
TA260 002:395.869 JLINK_IsHalted()
TA260 002:396.404 - 0.535ms returns FALSE
TA260 002:396.411 JLINK_HasError()
TA260 002:398.230 JLINK_IsHalted()
TA260 002:398.719 - 0.488ms returns FALSE
TA260 002:398.725 JLINK_HasError()
TA260 002:400.232 JLINK_IsHalted()
TA260 002:402.538   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:403.040 - 2.808ms returns TRUE
TA260 002:403.047 JLINK_ReadReg(R15 (PC))
TA260 002:403.051 - 0.004ms returns 0x20000000
TA260 002:403.056 JLINK_ClrBPEx(BPHandle = 0x00000011)
TA260 002:403.060 - 0.003ms returns 0x00
TA260 002:403.064 JLINK_ReadReg(R0)
TA260 002:403.084 - 0.020ms returns 0x00000000
TA260 002:403.582 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:403.590   Data:  70 B5 82 B0 00 20 01 90 43 F6 40 00 C4 F2 02 00 ...
TA260 002:403.600   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:406.180 - 2.598ms returns 0x27C
TA260 002:406.192 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:406.195   Data:  C3 F3 88 10 5C BF 42 F2 00 41 C0 F2 F4 01 A1 FB ...
TA260 002:406.204   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:408.052 - 1.860ms returns 0x184
TA260 002:408.058 JLINK_HasError()
TA260 002:408.064 JLINK_WriteReg(R0, 0x08001800)
TA260 002:408.068 - 0.005ms returns 0
TA260 002:408.073 JLINK_WriteReg(R1, 0x00000400)
TA260 002:408.076 - 0.003ms returns 0
TA260 002:408.080 JLINK_WriteReg(R2, 0x20000184)
TA260 002:408.084 - 0.003ms returns 0
TA260 002:408.088 JLINK_WriteReg(R3, 0x00000000)
TA260 002:408.091 - 0.003ms returns 0
TA260 002:408.095 JLINK_WriteReg(R4, 0x00000000)
TA260 002:408.098 - 0.003ms returns 0
TA260 002:408.102 JLINK_WriteReg(R5, 0x00000000)
TA260 002:408.106 - 0.003ms returns 0
TA260 002:408.110 JLINK_WriteReg(R6, 0x00000000)
TA260 002:408.113 - 0.003ms returns 0
TA260 002:408.117 JLINK_WriteReg(R7, 0x00000000)
TA260 002:408.121 - 0.003ms returns 0
TA260 002:408.125 JLINK_WriteReg(R8, 0x00000000)
TA260 002:408.128 - 0.003ms returns 0
TA260 002:408.132 JLINK_WriteReg(R9, 0x20000180)
TA260 002:408.136 - 0.003ms returns 0
TA260 002:408.140 JLINK_WriteReg(R10, 0x00000000)
TA260 002:408.143 - 0.003ms returns 0
TA260 002:408.147 JLINK_WriteReg(R11, 0x00000000)
TA260 002:408.150 - 0.003ms returns 0
TA260 002:408.154 JLINK_WriteReg(R12, 0x00000000)
TA260 002:408.158 - 0.003ms returns 0
TA260 002:408.162 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:408.166 - 0.003ms returns 0
TA260 002:408.170 JLINK_WriteReg(R14, 0x20000001)
TA260 002:408.173 - 0.003ms returns 0
TA260 002:408.177 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:408.181 - 0.003ms returns 0
TA260 002:408.185 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:408.188 - 0.003ms returns 0
TA260 002:408.192 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:408.195 - 0.003ms returns 0
TA260 002:408.199 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:408.203 - 0.003ms returns 0
TA260 002:408.207 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:408.210 - 0.003ms returns 0
TA260 002:408.215 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:408.219 - 0.004ms returns 0x00000012
TA260 002:408.223 JLINK_Go()
TA260 002:408.230   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:411.029 - 2.806ms 
TA260 002:411.041 JLINK_IsHalted()
TA260 002:411.541 - 0.499ms returns FALSE
TA260 002:411.547 JLINK_HasError()
TA260 002:413.242 JLINK_IsHalted()
TA260 002:413.719 - 0.476ms returns FALSE
TA260 002:413.732 JLINK_HasError()
TA260 002:415.252 JLINK_IsHalted()
TA260 002:417.532   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:418.042 - 2.789ms returns TRUE
TA260 002:418.052 JLINK_ReadReg(R15 (PC))
TA260 002:418.057 - 0.004ms returns 0x20000000
TA260 002:418.088 JLINK_ClrBPEx(BPHandle = 0x00000012)
TA260 002:418.093 - 0.005ms returns 0x00
TA260 002:418.097 JLINK_ReadReg(R0)
TA260 002:418.104 - 0.007ms returns 0x00000000
TA260 002:418.425 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:418.432   Data:  C9 03 99 43 00 F4 40 32 A1 F5 80 31 8A 42 64 D1 ...
TA260 002:418.442   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:421.053 - 2.627ms returns 0x27C
TA260 002:421.060 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:421.064   Data:  87 B0 40 F2 44 10 C2 F2 00 00 01 78 00 26 00 29 ...
TA260 002:421.071   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:422.904 - 1.844ms returns 0x184
TA260 002:422.910 JLINK_HasError()
TA260 002:422.916 JLINK_WriteReg(R0, 0x08001C00)
TA260 002:422.920 - 0.005ms returns 0
TA260 002:422.924 JLINK_WriteReg(R1, 0x00000400)
TA260 002:422.929 - 0.004ms returns 0
TA260 002:422.933 JLINK_WriteReg(R2, 0x20000184)
TA260 002:422.936 - 0.003ms returns 0
TA260 002:422.940 JLINK_WriteReg(R3, 0x00000000)
TA260 002:422.943 - 0.003ms returns 0
TA260 002:422.947 JLINK_WriteReg(R4, 0x00000000)
TA260 002:422.951 - 0.003ms returns 0
TA260 002:422.955 JLINK_WriteReg(R5, 0x00000000)
TA260 002:422.958 - 0.003ms returns 0
TA260 002:422.962 JLINK_WriteReg(R6, 0x00000000)
TA260 002:422.966 - 0.003ms returns 0
TA260 002:422.970 JLINK_WriteReg(R7, 0x00000000)
TA260 002:422.973 - 0.003ms returns 0
TA260 002:422.977 JLINK_WriteReg(R8, 0x00000000)
TA260 002:422.980 - 0.003ms returns 0
TA260 002:422.984 JLINK_WriteReg(R9, 0x20000180)
TA260 002:422.988 - 0.003ms returns 0
TA260 002:422.992 JLINK_WriteReg(R10, 0x00000000)
TA260 002:422.995 - 0.003ms returns 0
TA260 002:422.999 JLINK_WriteReg(R11, 0x00000000)
TA260 002:423.002 - 0.003ms returns 0
TA260 002:423.006 JLINK_WriteReg(R12, 0x00000000)
TA260 002:423.010 - 0.003ms returns 0
TA260 002:423.014 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:423.018 - 0.003ms returns 0
TA260 002:423.022 JLINK_WriteReg(R14, 0x20000001)
TA260 002:423.025 - 0.003ms returns 0
TA260 002:423.029 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:423.033 - 0.003ms returns 0
TA260 002:423.037 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:423.040 - 0.003ms returns 0
TA260 002:423.045 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:423.048 - 0.003ms returns 0
TA260 002:423.052 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:423.055 - 0.003ms returns 0
TA260 002:423.059 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:423.063 - 0.003ms returns 0
TA260 002:423.086 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:423.090 - 0.004ms returns 0x00000013
TA260 002:423.094 JLINK_Go()
TA260 002:423.101   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:426.058 - 2.964ms 
TA260 002:426.077 JLINK_IsHalted()
TA260 002:426.558 - 0.480ms returns FALSE
TA260 002:426.570 JLINK_HasError()
TA260 002:429.257 JLINK_IsHalted()
TA260 002:429.750 - 0.492ms returns FALSE
TA260 002:429.763 JLINK_HasError()
TA260 002:431.256 JLINK_IsHalted()
TA260 002:433.547   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:434.422 - 3.165ms returns TRUE
TA260 002:434.435 JLINK_ReadReg(R15 (PC))
TA260 002:434.440 - 0.005ms returns 0x20000000
TA260 002:434.445 JLINK_ClrBPEx(BPHandle = 0x00000013)
TA260 002:434.449 - 0.004ms returns 0x00
TA260 002:434.453 JLINK_ReadReg(R0)
TA260 002:434.457 - 0.003ms returns 0x00000000
TA260 002:434.777 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:434.784   Data:  C2 8C 01 3A 13 04 C2 84 E2 D1 C8 68 20 F0 80 00 ...
TA260 002:434.794   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:437.362 - 2.585ms returns 0x27C
TA260 002:437.373 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:437.377   Data:  84 F8 3D 00 20 68 C1 68 21 F4 00 51 C1 60 20 46 ...
TA260 002:437.385   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:439.269 - 1.896ms returns 0x184
TA260 002:439.276 JLINK_HasError()
TA260 002:439.281 JLINK_WriteReg(R0, 0x08002000)
TA260 002:439.286 - 0.004ms returns 0
TA260 002:439.290 JLINK_WriteReg(R1, 0x00000400)
TA260 002:439.293 - 0.003ms returns 0
TA260 002:439.297 JLINK_WriteReg(R2, 0x20000184)
TA260 002:439.301 - 0.003ms returns 0
TA260 002:439.305 JLINK_WriteReg(R3, 0x00000000)
TA260 002:439.312 - 0.006ms returns 0
TA260 002:439.317 JLINK_WriteReg(R4, 0x00000000)
TA260 002:439.321 - 0.003ms returns 0
TA260 002:439.325 JLINK_WriteReg(R5, 0x00000000)
TA260 002:439.328 - 0.003ms returns 0
TA260 002:439.332 JLINK_WriteReg(R6, 0x00000000)
TA260 002:439.335 - 0.003ms returns 0
TA260 002:439.340 JLINK_WriteReg(R7, 0x00000000)
TA260 002:439.343 - 0.003ms returns 0
TA260 002:439.347 JLINK_WriteReg(R8, 0x00000000)
TA260 002:439.350 - 0.003ms returns 0
TA260 002:439.354 JLINK_WriteReg(R9, 0x20000180)
TA260 002:439.358 - 0.003ms returns 0
TA260 002:439.362 JLINK_WriteReg(R10, 0x00000000)
TA260 002:439.365 - 0.003ms returns 0
TA260 002:439.369 JLINK_WriteReg(R11, 0x00000000)
TA260 002:439.372 - 0.003ms returns 0
TA260 002:439.376 JLINK_WriteReg(R12, 0x00000000)
TA260 002:439.380 - 0.003ms returns 0
TA260 002:439.384 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:439.387 - 0.003ms returns 0
TA260 002:439.391 JLINK_WriteReg(R14, 0x20000001)
TA260 002:439.395 - 0.003ms returns 0
TA260 002:439.399 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:439.402 - 0.003ms returns 0
TA260 002:439.407 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:439.410 - 0.003ms returns 0
TA260 002:439.414 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:439.418 - 0.003ms returns 0
TA260 002:439.422 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:439.425 - 0.003ms returns 0
TA260 002:439.429 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:439.432 - 0.003ms returns 0
TA260 002:439.437 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:439.441 - 0.004ms returns 0x00000014
TA260 002:439.445 JLINK_Go()
TA260 002:439.452   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:442.202 - 2.756ms 
TA260 002:442.211 JLINK_IsHalted()
TA260 002:442.688 - 0.477ms returns FALSE
TA260 002:442.694 JLINK_HasError()
TA260 002:444.189 JLINK_IsHalted()
TA260 002:444.633 - 0.443ms returns FALSE
TA260 002:444.640 JLINK_HasError()
TA260 002:446.191 JLINK_IsHalted()
TA260 002:448.493   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:448.974 - 2.782ms returns TRUE
TA260 002:448.980 JLINK_ReadReg(R15 (PC))
TA260 002:448.985 - 0.004ms returns 0x20000000
TA260 002:448.992 JLINK_ClrBPEx(BPHandle = 0x00000014)
TA260 002:448.996 - 0.004ms returns 0x00
TA260 002:449.000 JLINK_ReadReg(R0)
TA260 002:449.004 - 0.003ms returns 0x00000000
TA260 002:449.400 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:449.409   Data:  18 64 83 F8 3E 10 19 68 83 F8 3C 00 CA 68 42 F4 ...
TA260 002:449.418   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:452.010 - 2.609ms returns 0x27C
TA260 002:452.017 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:452.021   Data:  0C E0 20 68 00 68 40 06 37 D4 B9 F1 00 0F 1F D0 ...
TA260 002:452.029   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:453.908 - 1.890ms returns 0x184
TA260 002:453.920 JLINK_HasError()
TA260 002:453.925 JLINK_WriteReg(R0, 0x08002400)
TA260 002:453.931 - 0.005ms returns 0
TA260 002:453.935 JLINK_WriteReg(R1, 0x00000400)
TA260 002:453.938 - 0.003ms returns 0
TA260 002:453.942 JLINK_WriteReg(R2, 0x20000184)
TA260 002:453.946 - 0.003ms returns 0
TA260 002:453.950 JLINK_WriteReg(R3, 0x00000000)
TA260 002:453.954 - 0.003ms returns 0
TA260 002:453.958 JLINK_WriteReg(R4, 0x00000000)
TA260 002:453.961 - 0.003ms returns 0
TA260 002:453.965 JLINK_WriteReg(R5, 0x00000000)
TA260 002:453.969 - 0.003ms returns 0
TA260 002:453.973 JLINK_WriteReg(R6, 0x00000000)
TA260 002:453.976 - 0.003ms returns 0
TA260 002:453.980 JLINK_WriteReg(R7, 0x00000000)
TA260 002:453.984 - 0.003ms returns 0
TA260 002:453.988 JLINK_WriteReg(R8, 0x00000000)
TA260 002:453.992 - 0.003ms returns 0
TA260 002:453.996 JLINK_WriteReg(R9, 0x20000180)
TA260 002:453.999 - 0.003ms returns 0
TA260 002:454.003 JLINK_WriteReg(R10, 0x00000000)
TA260 002:454.007 - 0.003ms returns 0
TA260 002:454.011 JLINK_WriteReg(R11, 0x00000000)
TA260 002:454.014 - 0.003ms returns 0
TA260 002:454.018 JLINK_WriteReg(R12, 0x00000000)
TA260 002:454.021 - 0.003ms returns 0
TA260 002:454.026 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:454.032 - 0.006ms returns 0
TA260 002:454.037 JLINK_WriteReg(R14, 0x20000001)
TA260 002:454.041 - 0.003ms returns 0
TA260 002:454.045 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:454.048 - 0.003ms returns 0
TA260 002:454.052 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:454.056 - 0.003ms returns 0
TA260 002:454.060 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:454.063 - 0.003ms returns 0
TA260 002:454.068 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:454.071 - 0.003ms returns 0
TA260 002:454.075 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:454.078 - 0.003ms returns 0
TA260 002:454.083 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:454.087 - 0.004ms returns 0x00000015
TA260 002:454.091 JLINK_Go()
TA260 002:454.100   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:456.862 - 2.769ms 
TA260 002:456.871 JLINK_IsHalted()
TA260 002:457.467 - 0.595ms returns FALSE
TA260 002:457.480 JLINK_HasError()
TA260 002:458.698 JLINK_IsHalted()
TA260 002:459.190 - 0.492ms returns FALSE
TA260 002:459.198 JLINK_HasError()
TA260 002:460.696 JLINK_IsHalted()
TA260 002:461.190 - 0.494ms returns FALSE
TA260 002:461.198 JLINK_HasError()
TA260 002:463.200 JLINK_IsHalted()
TA260 002:465.685   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:466.191 - 2.990ms returns TRUE
TA260 002:466.200 JLINK_ReadReg(R15 (PC))
TA260 002:466.206 - 0.006ms returns 0x20000000
TA260 002:466.210 JLINK_ClrBPEx(BPHandle = 0x00000015)
TA260 002:466.214 - 0.003ms returns 0x00
TA260 002:466.218 JLINK_ReadReg(R0)
TA260 002:466.222 - 0.003ms returns 0x00000000
TA260 002:466.563 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:466.570   Data:  00 28 18 BF FE F7 F4 FC 40 F2 5C 10 C2 F2 00 00 ...
TA260 002:466.580   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:469.179 - 2.615ms returns 0x27C
TA260 002:469.189 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:469.193   Data:  4F F4 E1 32 80 E8 0E 00 C0 E9 03 33 C0 E9 05 C3 ...
TA260 002:469.200   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:471.088 - 1.898ms returns 0x184
TA260 002:471.097 JLINK_HasError()
TA260 002:471.129 JLINK_WriteReg(R0, 0x08002800)
TA260 002:471.134 - 0.005ms returns 0
TA260 002:471.139 JLINK_WriteReg(R1, 0x00000400)
TA260 002:471.142 - 0.003ms returns 0
TA260 002:471.146 JLINK_WriteReg(R2, 0x20000184)
TA260 002:471.150 - 0.003ms returns 0
TA260 002:471.154 JLINK_WriteReg(R3, 0x00000000)
TA260 002:471.158 - 0.003ms returns 0
TA260 002:471.162 JLINK_WriteReg(R4, 0x00000000)
TA260 002:471.165 - 0.003ms returns 0
TA260 002:471.169 JLINK_WriteReg(R5, 0x00000000)
TA260 002:471.173 - 0.003ms returns 0
TA260 002:471.177 JLINK_WriteReg(R6, 0x00000000)
TA260 002:471.180 - 0.003ms returns 0
TA260 002:471.184 JLINK_WriteReg(R7, 0x00000000)
TA260 002:471.188 - 0.003ms returns 0
TA260 002:471.192 JLINK_WriteReg(R8, 0x00000000)
TA260 002:471.195 - 0.003ms returns 0
TA260 002:471.199 JLINK_WriteReg(R9, 0x20000180)
TA260 002:471.202 - 0.003ms returns 0
TA260 002:471.206 JLINK_WriteReg(R10, 0x00000000)
TA260 002:471.210 - 0.003ms returns 0
TA260 002:471.214 JLINK_WriteReg(R11, 0x00000000)
TA260 002:471.217 - 0.003ms returns 0
TA260 002:471.221 JLINK_WriteReg(R12, 0x00000000)
TA260 002:471.224 - 0.003ms returns 0
TA260 002:471.228 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:471.232 - 0.003ms returns 0
TA260 002:471.236 JLINK_WriteReg(R14, 0x20000001)
TA260 002:471.240 - 0.003ms returns 0
TA260 002:471.244 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:471.247 - 0.003ms returns 0
TA260 002:471.251 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:471.255 - 0.004ms returns 0
TA260 002:471.259 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:471.263 - 0.003ms returns 0
TA260 002:471.267 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:471.270 - 0.003ms returns 0
TA260 002:471.274 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:471.277 - 0.003ms returns 0
TA260 002:471.282 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:471.286 - 0.004ms returns 0x00000016
TA260 002:471.290 JLINK_Go()
TA260 002:471.298   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:474.364 - 3.073ms 
TA260 002:474.381 JLINK_IsHalted()
TA260 002:474.840 - 0.458ms returns FALSE
TA260 002:474.854 JLINK_HasError()
TA260 002:476.713 JLINK_IsHalted()
TA260 002:477.281 - 0.567ms returns FALSE
TA260 002:477.287 JLINK_HasError()
TA260 002:478.449 JLINK_IsHalted()
TA260 002:480.792   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:481.315 - 2.865ms returns TRUE
TA260 002:481.321 JLINK_ReadReg(R15 (PC))
TA260 002:481.327 - 0.005ms returns 0x20000000
TA260 002:481.331 JLINK_ClrBPEx(BPHandle = 0x00000016)
TA260 002:481.335 - 0.003ms returns 0x00
TA260 002:481.339 JLINK_ReadReg(R0)
TA260 002:481.343 - 0.003ms returns 0x00000000
TA260 002:481.788 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:481.797   Data:  2C 70 01 24 E8 E7 00 BF 65 1C 9D 42 2A D2 0C EB ...
TA260 002:481.808   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:484.366 - 2.577ms returns 0x27C
TA260 002:484.404 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:484.408   Data:  B1 F5 00 4F A0 FB 02 01 1C D1 5A 00 DB 0F FD F7 ...
TA260 002:484.418   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:486.301 - 1.897ms returns 0x184
TA260 002:486.310 JLINK_HasError()
TA260 002:486.315 JLINK_WriteReg(R0, 0x08002C00)
TA260 002:486.320 - 0.004ms returns 0
TA260 002:486.324 JLINK_WriteReg(R1, 0x00000400)
TA260 002:486.328 - 0.004ms returns 0
TA260 002:486.332 JLINK_WriteReg(R2, 0x20000184)
TA260 002:486.336 - 0.003ms returns 0
TA260 002:486.340 JLINK_WriteReg(R3, 0x00000000)
TA260 002:486.343 - 0.003ms returns 0
TA260 002:486.347 JLINK_WriteReg(R4, 0x00000000)
TA260 002:486.350 - 0.003ms returns 0
TA260 002:486.354 JLINK_WriteReg(R5, 0x00000000)
TA260 002:486.358 - 0.003ms returns 0
TA260 002:486.362 JLINK_WriteReg(R6, 0x00000000)
TA260 002:486.365 - 0.003ms returns 0
TA260 002:486.369 JLINK_WriteReg(R7, 0x00000000)
TA260 002:486.373 - 0.003ms returns 0
TA260 002:486.377 JLINK_WriteReg(R8, 0x00000000)
TA260 002:486.380 - 0.003ms returns 0
TA260 002:486.384 JLINK_WriteReg(R9, 0x20000180)
TA260 002:486.387 - 0.003ms returns 0
TA260 002:486.391 JLINK_WriteReg(R10, 0x00000000)
TA260 002:486.395 - 0.003ms returns 0
TA260 002:486.399 JLINK_WriteReg(R11, 0x00000000)
TA260 002:486.402 - 0.003ms returns 0
TA260 002:486.406 JLINK_WriteReg(R12, 0x00000000)
TA260 002:486.410 - 0.003ms returns 0
TA260 002:486.414 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:486.418 - 0.004ms returns 0
TA260 002:486.422 JLINK_WriteReg(R14, 0x20000001)
TA260 002:486.425 - 0.003ms returns 0
TA260 002:486.429 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:486.433 - 0.003ms returns 0
TA260 002:486.437 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:486.440 - 0.003ms returns 0
TA260 002:486.444 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:486.448 - 0.003ms returns 0
TA260 002:486.452 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:486.455 - 0.003ms returns 0
TA260 002:486.459 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:486.463 - 0.003ms returns 0
TA260 002:486.467 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:486.471 - 0.004ms returns 0x00000017
TA260 002:486.475 JLINK_Go()
TA260 002:486.483   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:489.276 - 2.800ms 
TA260 002:489.285 JLINK_IsHalted()
TA260 002:489.762 - 0.477ms returns FALSE
TA260 002:489.776 JLINK_HasError()
TA260 002:492.179 JLINK_IsHalted()
TA260 002:492.655 - 0.476ms returns FALSE
TA260 002:492.661 JLINK_HasError()
TA260 002:494.196 JLINK_IsHalted()
TA260 002:496.490   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:496.998 - 2.802ms returns TRUE
TA260 002:497.038 JLINK_ReadReg(R15 (PC))
TA260 002:497.045 - 0.006ms returns 0x20000000
TA260 002:497.050 JLINK_ClrBPEx(BPHandle = 0x00000017)
TA260 002:497.053 - 0.004ms returns 0x00
TA260 002:497.058 JLINK_ReadReg(R0)
TA260 002:497.062 - 0.003ms returns 0x00000000
TA260 002:497.446 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:497.454   Data:  02 1A B1 EB 50 2F 45 F6 BC 31 1C BF 00 20 B0 EE ...
TA260 002:497.465   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:500.042 - 2.596ms returns 0x27C
TA260 002:500.054 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:500.058   Data:  04 2B 50 F8 04 2B 41 F8 04 2B 50 F8 04 2B 41 F8 ...
TA260 002:500.066   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:501.963 - 1.908ms returns 0x184
TA260 002:501.970 JLINK_HasError()
TA260 002:501.975 JLINK_WriteReg(R0, 0x08003000)
TA260 002:501.979 - 0.004ms returns 0
TA260 002:501.983 JLINK_WriteReg(R1, 0x00000400)
TA260 002:501.987 - 0.003ms returns 0
TA260 002:501.991 JLINK_WriteReg(R2, 0x20000184)
TA260 002:501.994 - 0.003ms returns 0
TA260 002:501.999 JLINK_WriteReg(R3, 0x00000000)
TA260 002:502.002 - 0.003ms returns 0
TA260 002:502.006 JLINK_WriteReg(R4, 0x00000000)
TA260 002:502.010 - 0.003ms returns 0
TA260 002:502.014 JLINK_WriteReg(R5, 0x00000000)
TA260 002:502.017 - 0.003ms returns 0
TA260 002:502.021 JLINK_WriteReg(R6, 0x00000000)
TA260 002:502.025 - 0.003ms returns 0
TA260 002:502.029 JLINK_WriteReg(R7, 0x00000000)
TA260 002:502.032 - 0.003ms returns 0
TA260 002:502.036 JLINK_WriteReg(R8, 0x00000000)
TA260 002:502.040 - 0.003ms returns 0
TA260 002:502.044 JLINK_WriteReg(R9, 0x20000180)
TA260 002:502.047 - 0.003ms returns 0
TA260 002:502.051 JLINK_WriteReg(R10, 0x00000000)
TA260 002:502.054 - 0.003ms returns 0
TA260 002:502.058 JLINK_WriteReg(R11, 0x00000000)
TA260 002:502.062 - 0.003ms returns 0
TA260 002:502.066 JLINK_WriteReg(R12, 0x00000000)
TA260 002:502.069 - 0.003ms returns 0
TA260 002:502.073 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:502.077 - 0.004ms returns 0
TA260 002:502.081 JLINK_WriteReg(R14, 0x20000001)
TA260 002:502.084 - 0.003ms returns 0
TA260 002:502.088 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:502.092 - 0.003ms returns 0
TA260 002:502.096 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:502.100 - 0.003ms returns 0
TA260 002:502.104 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:502.107 - 0.003ms returns 0
TA260 002:502.111 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:502.114 - 0.003ms returns 0
TA260 002:502.119 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:502.122 - 0.003ms returns 0
TA260 002:502.127 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:502.131 - 0.004ms returns 0x00000018
TA260 002:502.135 JLINK_Go()
TA260 002:502.142   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:504.862 - 2.726ms 
TA260 002:504.875 JLINK_IsHalted()
TA260 002:505.415 - 0.539ms returns FALSE
TA260 002:505.421 JLINK_HasError()
TA260 002:506.694 JLINK_IsHalted()
TA260 002:507.190 - 0.495ms returns FALSE
TA260 002:507.197 JLINK_HasError()
TA260 002:508.687 JLINK_IsHalted()
TA260 002:509.189 - 0.501ms returns FALSE
TA260 002:509.196 JLINK_HasError()
TA260 002:510.403 JLINK_IsHalted()
TA260 002:512.766   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:513.270 - 2.866ms returns TRUE
TA260 002:513.278 JLINK_ReadReg(R15 (PC))
TA260 002:513.282 - 0.004ms returns 0x20000000
TA260 002:513.287 JLINK_ClrBPEx(BPHandle = 0x00000018)
TA260 002:513.290 - 0.003ms returns 0x00
TA260 002:513.295 JLINK_ReadReg(R0)
TA260 002:513.298 - 0.003ms returns 0x00000000
TA260 002:513.728 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:513.740   Data:  04 2B 50 F8 04 2B 41 F8 04 2B 50 F8 04 2B 41 F8 ...
TA260 002:513.751   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:516.366 - 2.637ms returns 0x27C
TA260 002:516.380 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:516.384   Data:  18 01 C2 F2 00 01 01 EB 80 00 94 ED 01 0A 10 ED ...
TA260 002:516.393   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:518.282 - 1.902ms returns 0x184
TA260 002:518.294 JLINK_HasError()
TA260 002:518.307 JLINK_WriteReg(R0, 0x08003400)
TA260 002:518.314 - 0.013ms returns 0
TA260 002:518.320 JLINK_WriteReg(R1, 0x00000400)
TA260 002:518.325 - 0.004ms returns 0
TA260 002:518.330 JLINK_WriteReg(R2, 0x20000184)
TA260 002:518.334 - 0.004ms returns 0
TA260 002:518.339 JLINK_WriteReg(R3, 0x00000000)
TA260 002:518.344 - 0.005ms returns 0
TA260 002:518.348 JLINK_WriteReg(R4, 0x00000000)
TA260 002:518.352 - 0.003ms returns 0
TA260 002:518.356 JLINK_WriteReg(R5, 0x00000000)
TA260 002:518.363 - 0.007ms returns 0
TA260 002:518.369 JLINK_WriteReg(R6, 0x00000000)
TA260 002:518.372 - 0.003ms returns 0
TA260 002:518.376 JLINK_WriteReg(R7, 0x00000000)
TA260 002:518.380 - 0.003ms returns 0
TA260 002:518.384 JLINK_WriteReg(R8, 0x00000000)
TA260 002:518.387 - 0.003ms returns 0
TA260 002:518.391 JLINK_WriteReg(R9, 0x20000180)
TA260 002:518.394 - 0.003ms returns 0
TA260 002:518.398 JLINK_WriteReg(R10, 0x00000000)
TA260 002:518.402 - 0.003ms returns 0
TA260 002:518.406 JLINK_WriteReg(R11, 0x00000000)
TA260 002:518.409 - 0.003ms returns 0
TA260 002:518.413 JLINK_WriteReg(R12, 0x00000000)
TA260 002:518.417 - 0.003ms returns 0
TA260 002:518.421 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:518.425 - 0.003ms returns 0
TA260 002:518.429 JLINK_WriteReg(R14, 0x20000001)
TA260 002:518.432 - 0.003ms returns 0
TA260 002:518.437 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:518.441 - 0.004ms returns 0
TA260 002:518.445 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:518.448 - 0.003ms returns 0
TA260 002:518.452 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:518.456 - 0.003ms returns 0
TA260 002:518.460 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:518.463 - 0.003ms returns 0
TA260 002:518.467 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:518.470 - 0.003ms returns 0
TA260 002:518.475 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:518.479 - 0.004ms returns 0x00000019
TA260 002:518.483 JLINK_Go()
TA260 002:518.491   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:521.275 - 2.791ms 
TA260 002:521.282 JLINK_IsHalted()
TA260 002:521.770 - 0.487ms returns FALSE
TA260 002:521.775 JLINK_HasError()
TA260 002:523.463 JLINK_IsHalted()
TA260 002:523.955 - 0.491ms returns FALSE
TA260 002:523.964 JLINK_HasError()
TA260 002:525.926 JLINK_IsHalted()
TA260 002:528.276   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:528.747 - 2.821ms returns TRUE
TA260 002:528.754 JLINK_ReadReg(R15 (PC))
TA260 002:528.758 - 0.004ms returns 0x20000000
TA260 002:528.762 JLINK_ClrBPEx(BPHandle = 0x00000019)
TA260 002:528.766 - 0.003ms returns 0x00
TA260 002:528.770 JLINK_ReadReg(R0)
TA260 002:528.774 - 0.003ms returns 0x00000000
TA260 002:529.144 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:529.152   Data:  00 00 7A 44 B0 B5 40 F2 00 44 C4 F2 02 04 04 F5 ...
TA260 002:529.161   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:531.781 - 2.636ms returns 0x27C
TA260 002:531.787 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:531.791   Data:  60 1E 00 E0 00 20 CD E9 00 10 06 4B 6A 46 29 46 ...
TA260 002:531.797   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:533.636 - 1.848ms returns 0x184
TA260 002:533.648 JLINK_HasError()
TA260 002:533.654 JLINK_WriteReg(R0, 0x08003800)
TA260 002:533.659 - 0.005ms returns 0
TA260 002:533.663 JLINK_WriteReg(R1, 0x00000400)
TA260 002:533.666 - 0.003ms returns 0
TA260 002:533.670 JLINK_WriteReg(R2, 0x20000184)
TA260 002:533.674 - 0.003ms returns 0
TA260 002:533.678 JLINK_WriteReg(R3, 0x00000000)
TA260 002:533.682 - 0.003ms returns 0
TA260 002:533.686 JLINK_WriteReg(R4, 0x00000000)
TA260 002:533.689 - 0.003ms returns 0
TA260 002:533.693 JLINK_WriteReg(R5, 0x00000000)
TA260 002:533.697 - 0.003ms returns 0
TA260 002:533.701 JLINK_WriteReg(R6, 0x00000000)
TA260 002:533.704 - 0.003ms returns 0
TA260 002:533.709 JLINK_WriteReg(R7, 0x00000000)
TA260 002:533.712 - 0.003ms returns 0
TA260 002:533.716 JLINK_WriteReg(R8, 0x00000000)
TA260 002:533.720 - 0.003ms returns 0
TA260 002:533.724 JLINK_WriteReg(R9, 0x20000180)
TA260 002:533.727 - 0.003ms returns 0
TA260 002:533.731 JLINK_WriteReg(R10, 0x00000000)
TA260 002:533.735 - 0.003ms returns 0
TA260 002:533.739 JLINK_WriteReg(R11, 0x00000000)
TA260 002:533.742 - 0.003ms returns 0
TA260 002:533.746 JLINK_WriteReg(R12, 0x00000000)
TA260 002:533.750 - 0.003ms returns 0
TA260 002:533.754 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:533.757 - 0.003ms returns 0
TA260 002:533.761 JLINK_WriteReg(R14, 0x20000001)
TA260 002:533.764 - 0.003ms returns 0
TA260 002:533.769 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:533.776 - 0.007ms returns 0
TA260 002:533.780 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:533.784 - 0.003ms returns 0
TA260 002:533.788 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:533.792 - 0.003ms returns 0
TA260 002:533.796 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:533.799 - 0.003ms returns 0
TA260 002:533.803 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:533.806 - 0.003ms returns 0
TA260 002:533.811 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:533.815 - 0.004ms returns 0x0000001A
TA260 002:533.819 JLINK_Go()
TA260 002:533.828   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:536.609 - 2.789ms 
TA260 002:536.623 JLINK_IsHalted()
TA260 002:537.077 - 0.453ms returns FALSE
TA260 002:537.083 JLINK_HasError()
TA260 002:538.433 JLINK_IsHalted()
TA260 002:538.926 - 0.493ms returns FALSE
TA260 002:538.932 JLINK_HasError()
TA260 002:540.436 JLINK_IsHalted()
TA260 002:540.939 - 0.502ms returns FALSE
TA260 002:540.945 JLINK_HasError()
TA260 002:542.937 JLINK_IsHalted()
TA260 002:545.354   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:545.931 - 2.993ms returns TRUE
TA260 002:545.938 JLINK_ReadReg(R15 (PC))
TA260 002:545.943 - 0.004ms returns 0x20000000
TA260 002:545.948 JLINK_ClrBPEx(BPHandle = 0x0000001A)
TA260 002:545.952 - 0.003ms returns 0x00
TA260 002:545.956 JLINK_ReadReg(R0)
TA260 002:545.960 - 0.003ms returns 0x00000000
TA260 002:546.288 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:546.296   Data:  00 F0 66 FE 51 EC 10 0B 81 F0 00 41 41 EC 10 0B ...
TA260 002:546.305   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:548.904 - 2.616ms returns 0x27C
TA260 002:548.915 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:548.918   Data:  1E 00 03 90 9D ED 02 0B C4 E6 02 20 00 F0 86 FD ...
TA260 002:548.925   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:550.830 - 1.915ms returns 0x184
TA260 002:550.845 JLINK_HasError()
TA260 002:550.879 JLINK_WriteReg(R0, 0x08003C00)
TA260 002:550.885 - 0.006ms returns 0
TA260 002:550.889 JLINK_WriteReg(R1, 0x00000400)
TA260 002:550.893 - 0.003ms returns 0
TA260 002:550.897 JLINK_WriteReg(R2, 0x20000184)
TA260 002:550.901 - 0.003ms returns 0
TA260 002:550.905 JLINK_WriteReg(R3, 0x00000000)
TA260 002:550.908 - 0.003ms returns 0
TA260 002:550.912 JLINK_WriteReg(R4, 0x00000000)
TA260 002:550.916 - 0.003ms returns 0
TA260 002:550.920 JLINK_WriteReg(R5, 0x00000000)
TA260 002:550.923 - 0.003ms returns 0
TA260 002:550.927 JLINK_WriteReg(R6, 0x00000000)
TA260 002:550.930 - 0.003ms returns 0
TA260 002:550.935 JLINK_WriteReg(R7, 0x00000000)
TA260 002:550.938 - 0.003ms returns 0
TA260 002:550.943 JLINK_WriteReg(R8, 0x00000000)
TA260 002:550.946 - 0.003ms returns 0
TA260 002:550.950 JLINK_WriteReg(R9, 0x20000180)
TA260 002:550.953 - 0.003ms returns 0
TA260 002:550.957 JLINK_WriteReg(R10, 0x00000000)
TA260 002:550.961 - 0.003ms returns 0
TA260 002:550.965 JLINK_WriteReg(R11, 0x00000000)
TA260 002:550.968 - 0.003ms returns 0
TA260 002:550.972 JLINK_WriteReg(R12, 0x00000000)
TA260 002:550.976 - 0.003ms returns 0
TA260 002:550.980 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:550.983 - 0.003ms returns 0
TA260 002:550.987 JLINK_WriteReg(R14, 0x20000001)
TA260 002:550.991 - 0.003ms returns 0
TA260 002:550.995 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:550.998 - 0.003ms returns 0
TA260 002:551.003 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:551.007 - 0.004ms returns 0
TA260 002:551.011 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:551.014 - 0.003ms returns 0
TA260 002:551.018 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:551.021 - 0.003ms returns 0
TA260 002:551.025 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:551.029 - 0.003ms returns 0
TA260 002:551.034 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:551.038 - 0.004ms returns 0x0000001B
TA260 002:551.042 JLINK_Go()
TA260 002:551.051   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:553.781 - 2.737ms 
TA260 002:553.797 JLINK_IsHalted()
TA260 002:554.303 - 0.505ms returns FALSE
TA260 002:554.309 JLINK_HasError()
TA260 002:555.459 JLINK_IsHalted()
TA260 002:555.922 - 0.462ms returns FALSE
TA260 002:555.933 JLINK_HasError()
TA260 002:557.448 JLINK_IsHalted()
TA260 002:557.939 - 0.490ms returns FALSE
TA260 002:557.944 JLINK_HasError()
TA260 002:559.450 JLINK_IsHalted()
TA260 002:561.790   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:562.268 - 2.818ms returns TRUE
TA260 002:562.275 JLINK_ReadReg(R15 (PC))
TA260 002:562.280 - 0.005ms returns 0x20000000
TA260 002:562.285 JLINK_ClrBPEx(BPHandle = 0x0000001B)
TA260 002:562.288 - 0.004ms returns 0x00
TA260 002:562.293 JLINK_ReadReg(R0)
TA260 002:562.296 - 0.003ms returns 0x00000000
TA260 002:562.637 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:562.644   Data:  E7 F9 53 EC 1B 2B FC F7 E6 F9 41 EC 10 0B 00 F0 ...
TA260 002:562.654   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:565.285 - 2.647ms returns 0x27C
TA260 002:565.300 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:565.304   Data:  FC F7 AB F8 9D ED 04 0B 41 EC 19 0B 53 EC 10 2B ...
TA260 002:565.315   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:567.181 - 1.880ms returns 0x184
TA260 002:567.192 JLINK_HasError()
TA260 002:567.197 JLINK_WriteReg(R0, 0x08004000)
TA260 002:567.203 - 0.006ms returns 0
TA260 002:567.208 JLINK_WriteReg(R1, 0x00000400)
TA260 002:567.211 - 0.003ms returns 0
TA260 002:567.215 JLINK_WriteReg(R2, 0x20000184)
TA260 002:567.219 - 0.003ms returns 0
TA260 002:567.223 JLINK_WriteReg(R3, 0x00000000)
TA260 002:567.226 - 0.003ms returns 0
TA260 002:567.230 JLINK_WriteReg(R4, 0x00000000)
TA260 002:567.234 - 0.003ms returns 0
TA260 002:567.237 JLINK_WriteReg(R5, 0x00000000)
TA260 002:567.241 - 0.003ms returns 0
TA260 002:567.245 JLINK_WriteReg(R6, 0x00000000)
TA260 002:567.248 - 0.003ms returns 0
TA260 002:567.252 JLINK_WriteReg(R7, 0x00000000)
TA260 002:567.256 - 0.003ms returns 0
TA260 002:567.260 JLINK_WriteReg(R8, 0x00000000)
TA260 002:567.263 - 0.003ms returns 0
TA260 002:567.267 JLINK_WriteReg(R9, 0x20000180)
TA260 002:567.271 - 0.003ms returns 0
TA260 002:567.275 JLINK_WriteReg(R10, 0x00000000)
TA260 002:567.278 - 0.003ms returns 0
TA260 002:567.282 JLINK_WriteReg(R11, 0x00000000)
TA260 002:567.286 - 0.003ms returns 0
TA260 002:567.290 JLINK_WriteReg(R12, 0x00000000)
TA260 002:567.293 - 0.003ms returns 0
TA260 002:567.297 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:567.301 - 0.003ms returns 0
TA260 002:567.305 JLINK_WriteReg(R14, 0x20000001)
TA260 002:567.308 - 0.003ms returns 0
TA260 002:567.312 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:567.316 - 0.003ms returns 0
TA260 002:567.320 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:567.323 - 0.003ms returns 0
TA260 002:567.327 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:567.331 - 0.003ms returns 0
TA260 002:567.335 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:567.338 - 0.003ms returns 0
TA260 002:567.342 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:567.346 - 0.004ms returns 0
TA260 002:567.350 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:567.355 - 0.004ms returns 0x0000001C
TA260 002:567.359 JLINK_Go()
TA260 002:567.367   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:570.109 - 2.750ms 
TA260 002:570.117 JLINK_IsHalted()
TA260 002:570.587 - 0.471ms returns FALSE
TA260 002:570.593 JLINK_HasError()
TA260 002:571.955 JLINK_IsHalted()
TA260 002:572.417 - 0.462ms returns FALSE
TA260 002:572.423 JLINK_HasError()
TA260 002:573.464 JLINK_IsHalted()
TA260 002:574.189 - 0.724ms returns FALSE
TA260 002:574.203 JLINK_HasError()
TA260 002:575.490 JLINK_IsHalted()
TA260 002:577.854   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:578.406 - 2.915ms returns TRUE
TA260 002:578.413 JLINK_ReadReg(R15 (PC))
TA260 002:578.418 - 0.005ms returns 0x20000000
TA260 002:578.422 JLINK_ClrBPEx(BPHandle = 0x0000001C)
TA260 002:578.426 - 0.004ms returns 0x00
TA260 002:578.431 JLINK_ReadReg(R0)
TA260 002:578.434 - 0.003ms returns 0x00000000
TA260 002:578.766 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:578.773   Data:  FB F7 EC FF 53 EC 19 2B FB F7 41 FF 9D ED 04 0B ...
TA260 002:578.788   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:581.362 - 2.595ms returns 0x27C
TA260 002:581.371 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:581.375   Data:  19 2B FB F7 A7 FE 9D ED 02 1B 53 EC 11 2B FB F7 ...
TA260 002:581.384   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:583.270 - 1.898ms returns 0x184
TA260 002:583.278 JLINK_HasError()
TA260 002:583.283 JLINK_WriteReg(R0, 0x08004400)
TA260 002:583.288 - 0.004ms returns 0
TA260 002:583.292 JLINK_WriteReg(R1, 0x00000400)
TA260 002:583.295 - 0.003ms returns 0
TA260 002:583.300 JLINK_WriteReg(R2, 0x20000184)
TA260 002:583.303 - 0.003ms returns 0
TA260 002:583.307 JLINK_WriteReg(R3, 0x00000000)
TA260 002:583.310 - 0.003ms returns 0
TA260 002:583.314 JLINK_WriteReg(R4, 0x00000000)
TA260 002:583.318 - 0.003ms returns 0
TA260 002:583.470 JLINK_WriteReg(R5, 0x00000000)
TA260 002:583.474 - 0.003ms returns 0
TA260 002:583.478 JLINK_WriteReg(R6, 0x00000000)
TA260 002:583.481 - 0.003ms returns 0
TA260 002:583.485 JLINK_WriteReg(R7, 0x00000000)
TA260 002:583.489 - 0.003ms returns 0
TA260 002:583.493 JLINK_WriteReg(R8, 0x00000000)
TA260 002:583.497 - 0.004ms returns 0
TA260 002:583.501 JLINK_WriteReg(R9, 0x20000180)
TA260 002:583.505 - 0.003ms returns 0
TA260 002:583.509 JLINK_WriteReg(R10, 0x00000000)
TA260 002:583.512 - 0.003ms returns 0
TA260 002:583.516 JLINK_WriteReg(R11, 0x00000000)
TA260 002:583.520 - 0.003ms returns 0
TA260 002:583.524 JLINK_WriteReg(R12, 0x00000000)
TA260 002:583.527 - 0.003ms returns 0
TA260 002:583.531 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:583.535 - 0.003ms returns 0
TA260 002:583.539 JLINK_WriteReg(R14, 0x20000001)
TA260 002:583.542 - 0.003ms returns 0
TA260 002:583.546 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:583.550 - 0.003ms returns 0
TA260 002:583.555 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:583.558 - 0.003ms returns 0
TA260 002:583.562 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:583.566 - 0.003ms returns 0
TA260 002:583.570 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:583.573 - 0.003ms returns 0
TA260 002:583.577 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:583.580 - 0.003ms returns 0
TA260 002:583.585 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:583.589 - 0.004ms returns 0x0000001D
TA260 002:583.594 JLINK_Go()
TA260 002:583.602   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:586.301 - 2.707ms 
TA260 002:586.310 JLINK_IsHalted()
TA260 002:586.790 - 0.480ms returns FALSE
TA260 002:586.796 JLINK_HasError()
TA260 002:587.970 JLINK_IsHalted()
TA260 002:588.415 - 0.445ms returns FALSE
TA260 002:588.421 JLINK_HasError()
TA260 002:589.976 JLINK_IsHalted()
TA260 002:590.463 - 0.492ms returns FALSE
TA260 002:590.469 JLINK_HasError()
TA260 002:591.971 JLINK_IsHalted()
TA260 002:594.312   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:594.844 - 2.872ms returns TRUE
TA260 002:594.857 JLINK_ReadReg(R15 (PC))
TA260 002:594.862 - 0.005ms returns 0x20000000
TA260 002:594.866 JLINK_ClrBPEx(BPHandle = 0x0000001D)
TA260 002:594.870 - 0.004ms returns 0x00
TA260 002:594.875 JLINK_ReadReg(R0)
TA260 002:594.878 - 0.003ms returns 0x00000000
TA260 002:595.461 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:595.473   Data:  10 0B 6D 1E 04 EB C5 00 90 ED 00 1B 51 EC 10 0B ...
TA260 002:595.486   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:598.100 - 2.638ms returns 0x27C
TA260 002:598.107 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:598.111   Data:  28 4B FB F7 3B FF 03 D8 4F F0 FF 30 01 46 07 E0 ...
TA260 002:598.119   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:599.998 - 1.891ms returns 0x184
TA260 002:600.006 JLINK_HasError()
TA260 002:600.011 JLINK_WriteReg(R0, 0x08004800)
TA260 002:600.016 - 0.004ms returns 0
TA260 002:600.020 JLINK_WriteReg(R1, 0x00000400)
TA260 002:600.024 - 0.003ms returns 0
TA260 002:600.028 JLINK_WriteReg(R2, 0x20000184)
TA260 002:600.031 - 0.003ms returns 0
TA260 002:600.035 JLINK_WriteReg(R3, 0x00000000)
TA260 002:600.038 - 0.003ms returns 0
TA260 002:600.043 JLINK_WriteReg(R4, 0x00000000)
TA260 002:600.050 - 0.007ms returns 0
TA260 002:600.055 JLINK_WriteReg(R5, 0x00000000)
TA260 002:600.058 - 0.003ms returns 0
TA260 002:600.062 JLINK_WriteReg(R6, 0x00000000)
TA260 002:600.066 - 0.003ms returns 0
TA260 002:600.070 JLINK_WriteReg(R7, 0x00000000)
TA260 002:600.073 - 0.003ms returns 0
TA260 002:600.077 JLINK_WriteReg(R8, 0x00000000)
TA260 002:600.087 - 0.003ms returns 0
TA260 002:600.091 JLINK_WriteReg(R9, 0x20000180)
TA260 002:600.094 - 0.003ms returns 0
TA260 002:600.098 JLINK_WriteReg(R10, 0x00000000)
TA260 002:600.102 - 0.003ms returns 0
TA260 002:600.106 JLINK_WriteReg(R11, 0x00000000)
TA260 002:600.109 - 0.003ms returns 0
TA260 002:600.115 JLINK_WriteReg(R12, 0x00000000)
TA260 002:600.119 - 0.004ms returns 0
TA260 002:600.123 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:600.127 - 0.003ms returns 0
TA260 002:600.131 JLINK_WriteReg(R14, 0x20000001)
TA260 002:600.134 - 0.003ms returns 0
TA260 002:600.138 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:600.142 - 0.003ms returns 0
TA260 002:600.146 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:600.149 - 0.003ms returns 0
TA260 002:600.153 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:600.197 - 0.043ms returns 0
TA260 002:600.201 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:600.205 - 0.003ms returns 0
TA260 002:600.209 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:600.212 - 0.003ms returns 0
TA260 002:600.217 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:600.221 - 0.004ms returns 0x0000001E
TA260 002:600.226 JLINK_Go()
TA260 002:600.233   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:602.895 - 2.669ms 
TA260 002:602.911 JLINK_IsHalted()
TA260 002:603.470 - 0.559ms returns FALSE
TA260 002:603.480 JLINK_HasError()
TA260 002:605.988 JLINK_IsHalted()
TA260 002:606.510 - 0.521ms returns FALSE
TA260 002:606.516 JLINK_HasError()
TA260 002:607.983 JLINK_IsHalted()
TA260 002:610.302   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:610.821 - 2.837ms returns TRUE
TA260 002:610.835 JLINK_ReadReg(R15 (PC))
TA260 002:610.840 - 0.005ms returns 0x20000000
TA260 002:610.844 JLINK_ClrBPEx(BPHandle = 0x0000001E)
TA260 002:610.849 - 0.004ms returns 0x00
TA260 002:610.854 JLINK_ReadReg(R0)
TA260 002:610.858 - 0.004ms returns 0x00000000
TA260 002:611.882 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:611.894   Data:  82 42 02 D1 04 F5 80 14 76 1C 76 1C 30 78 66 28 ...
TA260 002:611.905   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:614.541 - 2.659ms returns 0x27C
TA260 002:614.558 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:614.562   Data:  5C EA 01 00 F0 D1 02 98 06 A9 08 1A 00 F1 20 0A ...
TA260 002:614.574   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:616.434 - 1.875ms returns 0x184
TA260 002:616.448 JLINK_HasError()
TA260 002:616.454 JLINK_WriteReg(R0, 0x08004C00)
TA260 002:616.460 - 0.006ms returns 0
TA260 002:616.464 JLINK_WriteReg(R1, 0x00000400)
TA260 002:616.468 - 0.003ms returns 0
TA260 002:616.472 JLINK_WriteReg(R2, 0x20000184)
TA260 002:616.475 - 0.003ms returns 0
TA260 002:616.479 JLINK_WriteReg(R3, 0x00000000)
TA260 002:616.483 - 0.003ms returns 0
TA260 002:616.487 JLINK_WriteReg(R4, 0x00000000)
TA260 002:616.490 - 0.003ms returns 0
TA260 002:616.494 JLINK_WriteReg(R5, 0x00000000)
TA260 002:616.498 - 0.003ms returns 0
TA260 002:616.502 JLINK_WriteReg(R6, 0x00000000)
TA260 002:616.505 - 0.003ms returns 0
TA260 002:616.509 JLINK_WriteReg(R7, 0x00000000)
TA260 002:616.513 - 0.003ms returns 0
TA260 002:616.517 JLINK_WriteReg(R8, 0x00000000)
TA260 002:616.521 - 0.003ms returns 0
TA260 002:616.525 JLINK_WriteReg(R9, 0x20000180)
TA260 002:616.528 - 0.003ms returns 0
TA260 002:616.532 JLINK_WriteReg(R10, 0x00000000)
TA260 002:616.536 - 0.003ms returns 0
TA260 002:616.540 JLINK_WriteReg(R11, 0x00000000)
TA260 002:616.543 - 0.003ms returns 0
TA260 002:616.547 JLINK_WriteReg(R12, 0x00000000)
TA260 002:616.550 - 0.003ms returns 0
TA260 002:616.554 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:616.558 - 0.003ms returns 0
TA260 002:616.562 JLINK_WriteReg(R14, 0x20000001)
TA260 002:616.569 - 0.006ms returns 0
TA260 002:616.575 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:616.578 - 0.003ms returns 0
TA260 002:616.582 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:616.586 - 0.003ms returns 0
TA260 002:616.590 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:616.594 - 0.003ms returns 0
TA260 002:616.598 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:616.601 - 0.003ms returns 0
TA260 002:616.605 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:616.608 - 0.003ms returns 0
TA260 002:616.613 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:616.617 - 0.004ms returns 0x0000001F
TA260 002:616.621 JLINK_Go()
TA260 002:616.631   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:619.559 - 2.936ms 
TA260 002:619.575 JLINK_IsHalted()
TA260 002:620.078 - 0.503ms returns FALSE
TA260 002:620.093 JLINK_HasError()
TA260 002:623.005 JLINK_IsHalted()
TA260 002:623.556 - 0.550ms returns FALSE
TA260 002:623.568 JLINK_HasError()
TA260 002:625.006 JLINK_IsHalted()
TA260 002:627.406   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:627.946 - 2.940ms returns TRUE
TA260 002:627.955 JLINK_ReadReg(R15 (PC))
TA260 002:627.961 - 0.006ms returns 0x20000000
TA260 002:627.965 JLINK_ClrBPEx(BPHandle = 0x0000001F)
TA260 002:627.969 - 0.003ms returns 0x00
TA260 002:627.974 JLINK_ReadReg(R0)
TA260 002:627.977 - 0.003ms returns 0x00000000
TA260 002:628.307 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:628.315   Data:  00 40 00 97 CD E9 01 10 06 A9 0E A8 FF F7 CA FC ...
TA260 002:628.325   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:630.949 - 2.642ms returns 0x27C
TA260 002:630.961 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:630.965   Data:  02 B0 51 EC 10 0B 70 47 00 B5 2D ED 04 8B 83 B0 ...
TA260 002:630.972   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:632.808 - 1.845ms returns 0x184
TA260 002:632.824 JLINK_HasError()
TA260 002:632.857 JLINK_WriteReg(R0, 0x08005000)
TA260 002:632.864 - 0.007ms returns 0
TA260 002:632.868 JLINK_WriteReg(R1, 0x00000400)
TA260 002:632.872 - 0.003ms returns 0
TA260 002:632.876 JLINK_WriteReg(R2, 0x20000184)
TA260 002:632.880 - 0.003ms returns 0
TA260 002:632.884 JLINK_WriteReg(R3, 0x00000000)
TA260 002:632.887 - 0.003ms returns 0
TA260 002:632.891 JLINK_WriteReg(R4, 0x00000000)
TA260 002:632.895 - 0.003ms returns 0
TA260 002:632.899 JLINK_WriteReg(R5, 0x00000000)
TA260 002:632.902 - 0.003ms returns 0
TA260 002:632.906 JLINK_WriteReg(R6, 0x00000000)
TA260 002:632.909 - 0.003ms returns 0
TA260 002:632.913 JLINK_WriteReg(R7, 0x00000000)
TA260 002:632.917 - 0.003ms returns 0
TA260 002:632.921 JLINK_WriteReg(R8, 0x00000000)
TA260 002:632.924 - 0.003ms returns 0
TA260 002:632.928 JLINK_WriteReg(R9, 0x20000180)
TA260 002:632.932 - 0.003ms returns 0
TA260 002:632.936 JLINK_WriteReg(R10, 0x00000000)
TA260 002:632.939 - 0.003ms returns 0
TA260 002:632.943 JLINK_WriteReg(R11, 0x00000000)
TA260 002:632.946 - 0.003ms returns 0
TA260 002:632.951 JLINK_WriteReg(R12, 0x00000000)
TA260 002:632.954 - 0.003ms returns 0
TA260 002:632.958 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:632.962 - 0.003ms returns 0
TA260 002:632.966 JLINK_WriteReg(R14, 0x20000001)
TA260 002:632.969 - 0.003ms returns 0
TA260 002:632.974 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:632.977 - 0.003ms returns 0
TA260 002:632.981 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:632.984 - 0.003ms returns 0
TA260 002:632.988 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:632.992 - 0.003ms returns 0
TA260 002:632.996 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:632.999 - 0.003ms returns 0
TA260 002:633.003 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:633.006 - 0.003ms returns 0
TA260 002:633.011 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:633.015 - 0.004ms returns 0x00000020
TA260 002:633.019 JLINK_Go()
TA260 002:633.028   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:635.882 - 2.862ms 
TA260 002:635.898 JLINK_IsHalted()
TA260 002:636.418 - 0.520ms returns FALSE
TA260 002:636.427 JLINK_HasError()
TA260 002:638.015 JLINK_IsHalted()
TA260 002:638.510 - 0.494ms returns FALSE
TA260 002:638.520 JLINK_HasError()
TA260 002:640.014 JLINK_IsHalted()
TA260 002:642.339   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:642.838 - 2.823ms returns TRUE
TA260 002:642.845 JLINK_ReadReg(R15 (PC))
TA260 002:642.850 - 0.005ms returns 0x20000000
TA260 002:642.864 JLINK_ClrBPEx(BPHandle = 0x00000020)
TA260 002:642.868 - 0.003ms returns 0x00
TA260 002:642.872 JLINK_ReadReg(R0)
TA260 002:642.876 - 0.003ms returns 0x00000000
TA260 002:643.231 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:643.239   Data:  20 01 20 05 20 03 20 07 A0 00 A0 04 A0 02 A0 06 ...
TA260 002:643.249   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:645.957 - 2.725ms returns 0x27C
TA260 002:645.974 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:645.978   Data:  34 02 34 06 34 01 34 05 34 03 34 07 B4 00 B4 04 ...
TA260 002:645.989   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:647.860 - 1.885ms returns 0x184
TA260 002:647.868 JLINK_HasError()
TA260 002:647.873 JLINK_WriteReg(R0, 0x08005400)
TA260 002:647.878 - 0.004ms returns 0
TA260 002:647.882 JLINK_WriteReg(R1, 0x00000400)
TA260 002:647.886 - 0.003ms returns 0
TA260 002:647.890 JLINK_WriteReg(R2, 0x20000184)
TA260 002:647.893 - 0.003ms returns 0
TA260 002:647.897 JLINK_WriteReg(R3, 0x00000000)
TA260 002:647.901 - 0.003ms returns 0
TA260 002:647.905 JLINK_WriteReg(R4, 0x00000000)
TA260 002:647.908 - 0.003ms returns 0
TA260 002:647.913 JLINK_WriteReg(R5, 0x00000000)
TA260 002:647.919 - 0.006ms returns 0
TA260 002:647.924 JLINK_WriteReg(R6, 0x00000000)
TA260 002:647.927 - 0.003ms returns 0
TA260 002:647.931 JLINK_WriteReg(R7, 0x00000000)
TA260 002:647.934 - 0.003ms returns 0
TA260 002:647.938 JLINK_WriteReg(R8, 0x00000000)
TA260 002:647.942 - 0.003ms returns 0
TA260 002:647.946 JLINK_WriteReg(R9, 0x20000180)
TA260 002:647.949 - 0.003ms returns 0
TA260 002:647.953 JLINK_WriteReg(R10, 0x00000000)
TA260 002:647.957 - 0.003ms returns 0
TA260 002:647.961 JLINK_WriteReg(R11, 0x00000000)
TA260 002:647.964 - 0.003ms returns 0
TA260 002:647.968 JLINK_WriteReg(R12, 0x00000000)
TA260 002:647.972 - 0.003ms returns 0
TA260 002:647.976 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:647.980 - 0.004ms returns 0
TA260 002:647.984 JLINK_WriteReg(R14, 0x20000001)
TA260 002:647.987 - 0.003ms returns 0
TA260 002:647.991 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:647.994 - 0.003ms returns 0
TA260 002:647.998 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:648.002 - 0.003ms returns 0
TA260 002:648.006 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:648.010 - 0.003ms returns 0
TA260 002:648.014 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:648.017 - 0.003ms returns 0
TA260 002:648.021 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:648.024 - 0.003ms returns 0
TA260 002:648.029 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:648.033 - 0.004ms returns 0x00000021
TA260 002:648.037 JLINK_Go()
TA260 002:648.044   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:650.835 - 2.798ms 
TA260 002:650.843 JLINK_IsHalted()
TA260 002:651.345 - 0.501ms returns FALSE
TA260 002:651.351 JLINK_HasError()
TA260 002:652.844 JLINK_IsHalted()
TA260 002:653.314 - 0.470ms returns FALSE
TA260 002:653.462 JLINK_HasError()
TA260 002:654.853 JLINK_IsHalted()
TA260 002:657.141   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:657.614 - 2.759ms returns TRUE
TA260 002:657.627 JLINK_ReadReg(R15 (PC))
TA260 002:657.633 - 0.006ms returns 0x20000000
TA260 002:657.638 JLINK_ClrBPEx(BPHandle = 0x00000021)
TA260 002:657.642 - 0.004ms returns 0x00
TA260 002:657.647 JLINK_ReadReg(R0)
TA260 002:657.650 - 0.004ms returns 0x00000000
TA260 002:657.991 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:657.998   Data:  22 01 22 05 22 03 22 07 A2 00 A2 04 A2 02 A2 06 ...
TA260 002:658.008   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:660.598 - 2.607ms returns 0x27C
TA260 002:660.610 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:660.614   Data:  36 02 36 06 36 01 36 05 36 03 36 07 B6 00 B6 04 ...
TA260 002:660.623   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:662.558 - 1.947ms returns 0x184
TA260 002:662.571 JLINK_HasError()
TA260 002:662.576 JLINK_WriteReg(R0, 0x08005800)
TA260 002:662.581 - 0.005ms returns 0
TA260 002:662.586 JLINK_WriteReg(R1, 0x00000400)
TA260 002:662.589 - 0.003ms returns 0
TA260 002:662.593 JLINK_WriteReg(R2, 0x20000184)
TA260 002:662.597 - 0.004ms returns 0
TA260 002:662.601 JLINK_WriteReg(R3, 0x00000000)
TA260 002:662.604 - 0.003ms returns 0
TA260 002:662.609 JLINK_WriteReg(R4, 0x00000000)
TA260 002:662.612 - 0.003ms returns 0
TA260 002:662.616 JLINK_WriteReg(R5, 0x00000000)
TA260 002:662.619 - 0.003ms returns 0
TA260 002:662.623 JLINK_WriteReg(R6, 0x00000000)
TA260 002:662.627 - 0.003ms returns 0
TA260 002:662.631 JLINK_WriteReg(R7, 0x00000000)
TA260 002:662.634 - 0.003ms returns 0
TA260 002:662.638 JLINK_WriteReg(R8, 0x00000000)
TA260 002:662.641 - 0.003ms returns 0
TA260 002:662.645 JLINK_WriteReg(R9, 0x20000180)
TA260 002:662.649 - 0.003ms returns 0
TA260 002:662.653 JLINK_WriteReg(R10, 0x00000000)
TA260 002:662.656 - 0.003ms returns 0
TA260 002:662.660 JLINK_WriteReg(R11, 0x00000000)
TA260 002:662.663 - 0.003ms returns 0
TA260 002:662.667 JLINK_WriteReg(R12, 0x00000000)
TA260 002:662.671 - 0.003ms returns 0
TA260 002:662.675 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:662.678 - 0.003ms returns 0
TA260 002:662.682 JLINK_WriteReg(R14, 0x20000001)
TA260 002:662.686 - 0.003ms returns 0
TA260 002:662.690 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:662.694 - 0.003ms returns 0
TA260 002:662.698 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:662.701 - 0.003ms returns 0
TA260 002:662.705 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:662.708 - 0.003ms returns 0
TA260 002:662.713 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:662.716 - 0.003ms returns 0
TA260 002:662.720 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:662.724 - 0.003ms returns 0
TA260 002:662.728 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:662.732 - 0.004ms returns 0x00000022
TA260 002:662.736 JLINK_Go()
TA260 002:662.745   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:665.600 - 2.863ms 
TA260 002:665.613 JLINK_IsHalted()
TA260 002:666.111 - 0.497ms returns FALSE
TA260 002:666.118 JLINK_HasError()
TA260 002:667.716 JLINK_IsHalted()
TA260 002:668.177 - 0.461ms returns FALSE
TA260 002:668.183 JLINK_HasError()
TA260 002:669.716 JLINK_IsHalted()
TA260 002:672.084   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:672.586 - 2.869ms returns TRUE
TA260 002:672.596 JLINK_ReadReg(R15 (PC))
TA260 002:672.601 - 0.005ms returns 0x20000000
TA260 002:672.606 JLINK_ClrBPEx(BPHandle = 0x00000022)
TA260 002:672.610 - 0.003ms returns 0x00
TA260 002:672.614 JLINK_ReadReg(R0)
TA260 002:672.618 - 0.003ms returns 0x00000000
TA260 002:673.200 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:673.209   Data:  01 15 54 3E 13 5C 60 3E 7F 9A 6C 3E CC CF 78 3E ...
TA260 002:673.221   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:675.845 - 2.645ms returns 0x27C
TA260 002:675.874 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:675.878   Data:  31 DB 54 3F 49 18 53 3F 3D 4D 51 3F 20 7A 4F 3F ...
TA260 002:675.892   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:677.837 - 1.963ms returns 0x184
TA260 002:677.854 JLINK_HasError()
TA260 002:677.860 JLINK_WriteReg(R0, 0x08005C00)
TA260 002:677.867 - 0.006ms returns 0
TA260 002:677.871 JLINK_WriteReg(R1, 0x00000400)
TA260 002:677.875 - 0.003ms returns 0
TA260 002:677.879 JLINK_WriteReg(R2, 0x20000184)
TA260 002:677.882 - 0.003ms returns 0
TA260 002:677.886 JLINK_WriteReg(R3, 0x00000000)
TA260 002:677.890 - 0.003ms returns 0
TA260 002:677.894 JLINK_WriteReg(R4, 0x00000000)
TA260 002:677.897 - 0.003ms returns 0
TA260 002:677.902 JLINK_WriteReg(R5, 0x00000000)
TA260 002:677.905 - 0.003ms returns 0
TA260 002:677.909 JLINK_WriteReg(R6, 0x00000000)
TA260 002:677.912 - 0.003ms returns 0
TA260 002:677.916 JLINK_WriteReg(R7, 0x00000000)
TA260 002:677.920 - 0.003ms returns 0
TA260 002:677.924 JLINK_WriteReg(R8, 0x00000000)
TA260 002:677.927 - 0.003ms returns 0
TA260 002:677.937 JLINK_WriteReg(R9, 0x20000180)
TA260 002:677.943 - 0.006ms returns 0
TA260 002:677.947 JLINK_WriteReg(R10, 0x00000000)
TA260 002:677.951 - 0.003ms returns 0
TA260 002:677.955 JLINK_WriteReg(R11, 0x00000000)
TA260 002:677.958 - 0.003ms returns 0
TA260 002:677.962 JLINK_WriteReg(R12, 0x00000000)
TA260 002:677.966 - 0.003ms returns 0
TA260 002:677.970 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:677.974 - 0.004ms returns 0
TA260 002:677.978 JLINK_WriteReg(R14, 0x20000001)
TA260 002:677.982 - 0.003ms returns 0
TA260 002:677.986 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:677.989 - 0.003ms returns 0
TA260 002:677.993 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:677.997 - 0.003ms returns 0
TA260 002:678.001 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:678.004 - 0.003ms returns 0
TA260 002:678.008 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:678.012 - 0.003ms returns 0
TA260 002:678.016 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:678.019 - 0.003ms returns 0
TA260 002:678.024 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:678.029 - 0.005ms returns 0x00000023
TA260 002:678.033 JLINK_Go()
TA260 002:678.042   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:680.864 - 2.830ms 
TA260 002:680.878 JLINK_IsHalted()
TA260 002:681.419 - 0.540ms returns FALSE
TA260 002:681.428 JLINK_HasError()
TA260 002:685.748 JLINK_IsHalted()
TA260 002:688.178   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:688.659 - 2.910ms returns TRUE
TA260 002:688.667 JLINK_ReadReg(R15 (PC))
TA260 002:688.672 - 0.005ms returns 0x20000000
TA260 002:688.677 JLINK_ClrBPEx(BPHandle = 0x00000023)
TA260 002:688.681 - 0.004ms returns 0x00
TA260 002:688.686 JLINK_ReadReg(R0)
TA260 002:688.689 - 0.003ms returns 0x00000000
TA260 002:689.051 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:689.059   Data:  01 15 54 BE 13 5C 60 BE 7F 9A 6C BE CC CF 78 BE ...
TA260 002:689.069   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:691.693 - 2.641ms returns 0x27C
TA260 002:691.708 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:691.712   Data:  31 DB 54 BF 49 18 53 BF 3D 4D 51 BF 20 7A 4F BF ...
TA260 002:691.721   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:693.660 - 1.951ms returns 0x184
TA260 002:693.680 JLINK_HasError()
TA260 002:693.685 JLINK_WriteReg(R0, 0x08006000)
TA260 002:693.692 - 0.007ms returns 0
TA260 002:693.696 JLINK_WriteReg(R1, 0x00000400)
TA260 002:693.700 - 0.004ms returns 0
TA260 002:693.705 JLINK_WriteReg(R2, 0x20000184)
TA260 002:693.709 - 0.004ms returns 0
TA260 002:693.713 JLINK_WriteReg(R3, 0x00000000)
TA260 002:693.716 - 0.003ms returns 0
TA260 002:693.720 JLINK_WriteReg(R4, 0x00000000)
TA260 002:693.724 - 0.003ms returns 0
TA260 002:693.728 JLINK_WriteReg(R5, 0x00000000)
TA260 002:693.731 - 0.003ms returns 0
TA260 002:693.737 JLINK_WriteReg(R6, 0x00000000)
TA260 002:693.742 - 0.005ms returns 0
TA260 002:693.796 JLINK_WriteReg(R7, 0x00000000)
TA260 002:693.800 - 0.003ms returns 0
TA260 002:693.805 JLINK_WriteReg(R8, 0x00000000)
TA260 002:693.808 - 0.003ms returns 0
TA260 002:693.812 JLINK_WriteReg(R9, 0x20000180)
TA260 002:693.816 - 0.003ms returns 0
TA260 002:693.820 JLINK_WriteReg(R10, 0x00000000)
TA260 002:693.823 - 0.003ms returns 0
TA260 002:693.828 JLINK_WriteReg(R11, 0x00000000)
TA260 002:693.831 - 0.003ms returns 0
TA260 002:693.835 JLINK_WriteReg(R12, 0x00000000)
TA260 002:693.838 - 0.003ms returns 0
TA260 002:693.842 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:693.846 - 0.003ms returns 0
TA260 002:693.850 JLINK_WriteReg(R14, 0x20000001)
TA260 002:693.854 - 0.003ms returns 0
TA260 002:693.858 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:693.862 - 0.003ms returns 0
TA260 002:693.866 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:693.869 - 0.003ms returns 0
TA260 002:693.874 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:693.877 - 0.003ms returns 0
TA260 002:693.881 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:693.884 - 0.003ms returns 0
TA260 002:693.888 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:693.892 - 0.003ms returns 0
TA260 002:693.901 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:693.908 - 0.007ms returns 0x00000024
TA260 002:693.913 JLINK_Go()
TA260 002:693.923   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:696.648 - 2.735ms 
TA260 002:696.662 JLINK_IsHalted()
TA260 002:697.169 - 0.507ms returns FALSE
TA260 002:697.176 JLINK_HasError()
TA260 002:698.243 JLINK_IsHalted()
TA260 002:698.747 - 0.504ms returns FALSE
TA260 002:698.754 JLINK_HasError()
TA260 002:700.256 JLINK_IsHalted()
TA260 002:700.772 - 0.517ms returns FALSE
TA260 002:700.780 JLINK_HasError()
TA260 002:702.249 JLINK_IsHalted()
TA260 002:704.616   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:705.178 - 2.928ms returns TRUE
TA260 002:705.194 JLINK_ReadReg(R15 (PC))
TA260 002:705.200 - 0.007ms returns 0x20000000
TA260 002:705.205 JLINK_ClrBPEx(BPHandle = 0x00000024)
TA260 002:705.209 - 0.004ms returns 0x00
TA260 002:705.214 JLINK_ReadReg(R0)
TA260 002:705.218 - 0.004ms returns 0x00000000
TA260 002:705.622 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:705.629   Data:  54 55 44 45 00 4D 4F 44 45 5F 44 49 53 41 4E 54 ...
TA260 002:705.640   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:708.290 - 2.668ms returns 0x27C
TA260 002:708.311 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:708.315   Data:  B9 3D D5 3D 3D 91 7E 3F 89 5D D8 3D 8B 86 7E 3F ...
TA260 002:708.326   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:710.273 - 1.962ms returns 0x184
TA260 002:710.320 JLINK_HasError()
TA260 002:710.327 JLINK_WriteReg(R0, 0x08006400)
TA260 002:710.332 - 0.005ms returns 0
TA260 002:710.336 JLINK_WriteReg(R1, 0x00000400)
TA260 002:710.339 - 0.003ms returns 0
TA260 002:710.343 JLINK_WriteReg(R2, 0x20000184)
TA260 002:710.347 - 0.003ms returns 0
TA260 002:710.351 JLINK_WriteReg(R3, 0x00000000)
TA260 002:710.354 - 0.003ms returns 0
TA260 002:710.358 JLINK_WriteReg(R4, 0x00000000)
TA260 002:710.362 - 0.003ms returns 0
TA260 002:710.366 JLINK_WriteReg(R5, 0x00000000)
TA260 002:710.369 - 0.003ms returns 0
TA260 002:710.373 JLINK_WriteReg(R6, 0x00000000)
TA260 002:710.376 - 0.003ms returns 0
TA260 002:710.381 JLINK_WriteReg(R7, 0x00000000)
TA260 002:710.384 - 0.003ms returns 0
TA260 002:710.388 JLINK_WriteReg(R8, 0x00000000)
TA260 002:710.391 - 0.003ms returns 0
TA260 002:710.395 JLINK_WriteReg(R9, 0x20000180)
TA260 002:710.399 - 0.003ms returns 0
TA260 002:710.403 JLINK_WriteReg(R10, 0x00000000)
TA260 002:710.406 - 0.003ms returns 0
TA260 002:710.410 JLINK_WriteReg(R11, 0x00000000)
TA260 002:710.414 - 0.003ms returns 0
TA260 002:710.418 JLINK_WriteReg(R12, 0x00000000)
TA260 002:710.421 - 0.003ms returns 0
TA260 002:710.425 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:710.429 - 0.003ms returns 0
TA260 002:710.433 JLINK_WriteReg(R14, 0x20000001)
TA260 002:710.436 - 0.003ms returns 0
TA260 002:710.440 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:710.444 - 0.003ms returns 0
TA260 002:710.448 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:710.452 - 0.003ms returns 0
TA260 002:710.456 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:710.459 - 0.003ms returns 0
TA260 002:710.463 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:710.466 - 0.003ms returns 0
TA260 002:710.470 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:710.474 - 0.003ms returns 0
TA260 002:710.479 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:710.483 - 0.004ms returns 0x00000025
TA260 002:710.487 JLINK_Go()
TA260 002:710.496   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:713.193 - 2.705ms 
TA260 002:713.204 JLINK_IsHalted()
TA260 002:713.658 - 0.454ms returns FALSE
TA260 002:713.666 JLINK_HasError()
TA260 002:716.264 JLINK_IsHalted()
TA260 002:716.722 - 0.458ms returns FALSE
TA260 002:716.729 JLINK_HasError()
TA260 002:718.260 JLINK_IsHalted()
TA260 002:720.561   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:721.054 - 2.794ms returns TRUE
TA260 002:721.063 JLINK_ReadReg(R15 (PC))
TA260 002:721.069 - 0.005ms returns 0x20000000
TA260 002:721.073 JLINK_ClrBPEx(BPHandle = 0x00000025)
TA260 002:721.077 - 0.003ms returns 0x00
TA260 002:721.086 JLINK_ReadReg(R0)
TA260 002:721.092 - 0.005ms returns 0x00000000
TA260 002:721.538 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:721.553   Data:  53 E3 7B 3F 50 CC 36 3E 4D D1 7B 3F EC 57 38 3E ...
TA260 002:721.566   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:724.167 - 2.628ms returns 0x27C
TA260 002:724.191 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:724.196   Data:  17 A1 97 3E 49 66 74 3F 16 61 98 3E 4B 48 74 3F ...
TA260 002:724.210   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:726.048 - 1.857ms returns 0x184
TA260 002:726.062 JLINK_HasError()
TA260 002:726.069 JLINK_WriteReg(R0, 0x08006800)
TA260 002:726.074 - 0.005ms returns 0
TA260 002:726.079 JLINK_WriteReg(R1, 0x00000400)
TA260 002:726.082 - 0.003ms returns 0
TA260 002:726.086 JLINK_WriteReg(R2, 0x20000184)
TA260 002:726.090 - 0.003ms returns 0
TA260 002:726.094 JLINK_WriteReg(R3, 0x00000000)
TA260 002:726.097 - 0.003ms returns 0
TA260 002:726.101 JLINK_WriteReg(R4, 0x00000000)
TA260 002:726.105 - 0.003ms returns 0
TA260 002:726.109 JLINK_WriteReg(R5, 0x00000000)
TA260 002:726.112 - 0.003ms returns 0
TA260 002:726.116 JLINK_WriteReg(R6, 0x00000000)
TA260 002:726.120 - 0.003ms returns 0
TA260 002:726.124 JLINK_WriteReg(R7, 0x00000000)
TA260 002:726.127 - 0.003ms returns 0
TA260 002:726.131 JLINK_WriteReg(R8, 0x00000000)
TA260 002:726.134 - 0.003ms returns 0
TA260 002:726.138 JLINK_WriteReg(R9, 0x20000180)
TA260 002:726.142 - 0.003ms returns 0
TA260 002:726.146 JLINK_WriteReg(R10, 0x00000000)
TA260 002:726.149 - 0.003ms returns 0
TA260 002:726.153 JLINK_WriteReg(R11, 0x00000000)
TA260 002:726.157 - 0.003ms returns 0
TA260 002:726.161 JLINK_WriteReg(R12, 0x00000000)
TA260 002:726.165 - 0.003ms returns 0
TA260 002:726.169 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:726.173 - 0.004ms returns 0
TA260 002:726.177 JLINK_WriteReg(R14, 0x20000001)
TA260 002:726.180 - 0.003ms returns 0
TA260 002:726.185 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:726.188 - 0.003ms returns 0
TA260 002:726.192 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:726.196 - 0.003ms returns 0
TA260 002:726.200 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:726.203 - 0.003ms returns 0
TA260 002:726.207 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:726.210 - 0.003ms returns 0
TA260 002:726.214 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:726.218 - 0.003ms returns 0
TA260 002:726.222 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:726.227 - 0.004ms returns 0x00000026
TA260 002:726.231 JLINK_Go()
TA260 002:726.240   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:729.022 - 2.790ms 
TA260 002:729.032 JLINK_IsHalted()
TA260 002:729.557 - 0.524ms returns FALSE
TA260 002:729.566 JLINK_HasError()
TA260 002:731.271 JLINK_IsHalted()
TA260 002:731.794 - 0.523ms returns FALSE
TA260 002:731.801 JLINK_HasError()
TA260 002:733.780 JLINK_IsHalted()
TA260 002:736.093   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:736.645 - 2.864ms returns TRUE
TA260 002:736.658 JLINK_ReadReg(R15 (PC))
TA260 002:736.665 - 0.006ms returns 0x20000000
TA260 002:736.673 JLINK_ClrBPEx(BPHandle = 0x00000026)
TA260 002:736.678 - 0.005ms returns 0x00
TA260 002:736.707 JLINK_ReadReg(R0)
TA260 002:736.711 - 0.004ms returns 0x00000000
TA260 002:737.078 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:737.086   Data:  EB 21 6E 3F B6 EC BB 3E F2 FC 6D 3F AF A7 BC 3E ...
TA260 002:737.098   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:739.748 - 2.669ms returns 0x27C
TA260 002:739.759 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:739.763   Data:  07 1F F4 3E F2 D6 60 3F B1 CF F4 3E CF A6 60 3F ...
TA260 002:739.772   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:741.653 - 1.893ms returns 0x184
TA260 002:741.663 JLINK_HasError()
TA260 002:741.668 JLINK_WriteReg(R0, 0x08006C00)
TA260 002:741.673 - 0.005ms returns 0
TA260 002:741.678 JLINK_WriteReg(R1, 0x00000400)
TA260 002:741.682 - 0.004ms returns 0
TA260 002:741.688 JLINK_WriteReg(R2, 0x20000184)
TA260 002:741.692 - 0.003ms returns 0
TA260 002:741.696 JLINK_WriteReg(R3, 0x00000000)
TA260 002:741.707 - 0.011ms returns 0
TA260 002:741.711 JLINK_WriteReg(R4, 0x00000000)
TA260 002:741.714 - 0.003ms returns 0
TA260 002:741.718 JLINK_WriteReg(R5, 0x00000000)
TA260 002:741.722 - 0.003ms returns 0
TA260 002:741.726 JLINK_WriteReg(R6, 0x00000000)
TA260 002:741.730 - 0.003ms returns 0
TA260 002:741.734 JLINK_WriteReg(R7, 0x00000000)
TA260 002:741.737 - 0.003ms returns 0
TA260 002:741.741 JLINK_WriteReg(R8, 0x00000000)
TA260 002:741.744 - 0.003ms returns 0
TA260 002:741.748 JLINK_WriteReg(R9, 0x20000180)
TA260 002:741.752 - 0.003ms returns 0
TA260 002:741.756 JLINK_WriteReg(R10, 0x00000000)
TA260 002:741.760 - 0.003ms returns 0
TA260 002:741.764 JLINK_WriteReg(R11, 0x00000000)
TA260 002:741.767 - 0.003ms returns 0
TA260 002:741.771 JLINK_WriteReg(R12, 0x00000000)
TA260 002:741.774 - 0.003ms returns 0
TA260 002:741.779 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:741.783 - 0.004ms returns 0
TA260 002:741.787 JLINK_WriteReg(R14, 0x20000001)
TA260 002:741.790 - 0.003ms returns 0
TA260 002:741.794 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:741.798 - 0.003ms returns 0
TA260 002:741.802 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:741.805 - 0.003ms returns 0
TA260 002:741.810 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:741.813 - 0.003ms returns 0
TA260 002:741.817 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:741.820 - 0.003ms returns 0
TA260 002:741.824 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:741.828 - 0.003ms returns 0
TA260 002:741.832 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:741.837 - 0.004ms returns 0x00000027
TA260 002:741.841 JLINK_Go()
TA260 002:741.850   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:744.605 - 2.762ms 
TA260 002:744.631 JLINK_IsHalted()
TA260 002:745.112 - 0.481ms returns FALSE
TA260 002:745.119 JLINK_HasError()
TA260 002:747.019 JLINK_IsHalted()
TA260 002:747.503 - 0.484ms returns FALSE
TA260 002:747.511 JLINK_HasError()
TA260 002:749.024 JLINK_IsHalted()
TA260 002:751.410   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:751.948 - 2.923ms returns TRUE
TA260 002:751.956 JLINK_ReadReg(R15 (PC))
TA260 002:751.962 - 0.006ms returns 0x20000000
TA260 002:751.966 JLINK_ClrBPEx(BPHandle = 0x00000027)
TA260 002:751.970 - 0.003ms returns 0x00
TA260 002:751.975 JLINK_ReadReg(R0)
TA260 002:751.979 - 0.003ms returns 0x00000000
TA260 002:752.352 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:752.360   Data:  C7 39 57 3F 3D 9D 0A 3F 48 03 57 3F B7 F1 0A 3F ...
TA260 002:752.371   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:754.989 - 2.636ms returns 0x27C
TA260 002:755.013 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:755.017   Data:  A9 9D 23 3F A5 A3 44 3F EE EA 23 3F 37 63 44 3F ...
TA260 002:755.032   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:756.956 - 1.942ms returns 0x184
TA260 002:756.964 JLINK_HasError()
TA260 002:756.970 JLINK_WriteReg(R0, 0x08007000)
TA260 002:756.975 - 0.005ms returns 0
TA260 002:756.980 JLINK_WriteReg(R1, 0x00000400)
TA260 002:756.984 - 0.003ms returns 0
TA260 002:756.988 JLINK_WriteReg(R2, 0x20000184)
TA260 002:756.991 - 0.003ms returns 0
TA260 002:756.995 JLINK_WriteReg(R3, 0x00000000)
TA260 002:756.999 - 0.003ms returns 0
TA260 002:757.003 JLINK_WriteReg(R4, 0x00000000)
TA260 002:757.006 - 0.003ms returns 0
TA260 002:757.010 JLINK_WriteReg(R5, 0x00000000)
TA260 002:757.013 - 0.003ms returns 0
TA260 002:757.017 JLINK_WriteReg(R6, 0x00000000)
TA260 002:757.021 - 0.003ms returns 0
TA260 002:757.025 JLINK_WriteReg(R7, 0x00000000)
TA260 002:757.028 - 0.003ms returns 0
TA260 002:757.032 JLINK_WriteReg(R8, 0x00000000)
TA260 002:757.036 - 0.003ms returns 0
TA260 002:757.040 JLINK_WriteReg(R9, 0x20000180)
TA260 002:757.043 - 0.003ms returns 0
TA260 002:757.047 JLINK_WriteReg(R10, 0x00000000)
TA260 002:757.050 - 0.003ms returns 0
TA260 002:757.055 JLINK_WriteReg(R11, 0x00000000)
TA260 002:757.058 - 0.003ms returns 0
TA260 002:757.062 JLINK_WriteReg(R12, 0x00000000)
TA260 002:757.066 - 0.003ms returns 0
TA260 002:757.070 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:757.081 - 0.011ms returns 0
TA260 002:757.086 JLINK_WriteReg(R14, 0x20000001)
TA260 002:757.090 - 0.004ms returns 0
TA260 002:757.094 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:757.097 - 0.003ms returns 0
TA260 002:757.101 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:757.105 - 0.003ms returns 0
TA260 002:757.109 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:757.112 - 0.003ms returns 0
TA260 002:757.116 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:757.119 - 0.003ms returns 0
TA260 002:757.124 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:757.127 - 0.003ms returns 0
TA260 002:757.131 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:757.136 - 0.004ms returns 0x00000028
TA260 002:757.140 JLINK_Go()
TA260 002:757.147   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:759.933 - 2.792ms 
TA260 002:759.949 JLINK_IsHalted()
TA260 002:760.462 - 0.513ms returns FALSE
TA260 002:760.469 JLINK_HasError()
TA260 002:762.254 JLINK_IsHalted()
TA260 002:762.698 - 0.443ms returns FALSE
TA260 002:762.709 JLINK_HasError()
TA260 002:763.756 JLINK_IsHalted()
TA260 002:764.281 - 0.525ms returns FALSE
TA260 002:764.289 JLINK_HasError()
TA260 002:765.763 JLINK_IsHalted()
TA260 002:768.130   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:768.614 - 2.850ms returns TRUE
TA260 002:768.622 JLINK_ReadReg(R15 (PC))
TA260 002:768.627 - 0.005ms returns 0x20000000
TA260 002:768.632 JLINK_ClrBPEx(BPHandle = 0x00000028)
TA260 002:768.635 - 0.003ms returns 0x00
TA260 002:768.640 JLINK_ReadReg(R0)
TA260 002:768.644 - 0.003ms returns 0x00000000
TA260 002:769.078 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:769.088   Data:  43 0C 38 3F 71 F0 31 3F 55 C6 37 3F AA 38 32 3F ...
TA260 002:769.099   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:771.751 - 2.672ms returns 0x27C
TA260 002:771.773 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:771.777   Data:  2A E2 46 3F D2 E1 20 3F 67 21 47 3F 93 93 20 3F ...
TA260 002:771.789   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:773.652 - 1.878ms returns 0x184
TA260 002:773.663 JLINK_HasError()
TA260 002:773.668 JLINK_WriteReg(R0, 0x08007400)
TA260 002:773.674 - 0.005ms returns 0
TA260 002:773.678 JLINK_WriteReg(R1, 0x00000400)
TA260 002:773.682 - 0.003ms returns 0
TA260 002:773.686 JLINK_WriteReg(R2, 0x20000184)
TA260 002:773.689 - 0.003ms returns 0
TA260 002:773.694 JLINK_WriteReg(R3, 0x00000000)
TA260 002:773.697 - 0.003ms returns 0
TA260 002:773.701 JLINK_WriteReg(R4, 0x00000000)
TA260 002:773.704 - 0.003ms returns 0
TA260 002:773.708 JLINK_WriteReg(R5, 0x00000000)
TA260 002:773.712 - 0.003ms returns 0
TA260 002:773.716 JLINK_WriteReg(R6, 0x00000000)
TA260 002:773.719 - 0.003ms returns 0
TA260 002:773.723 JLINK_WriteReg(R7, 0x00000000)
TA260 002:773.726 - 0.003ms returns 0
TA260 002:773.731 JLINK_WriteReg(R8, 0x00000000)
TA260 002:773.734 - 0.003ms returns 0
TA260 002:773.738 JLINK_WriteReg(R9, 0x20000180)
TA260 002:773.742 - 0.003ms returns 0
TA260 002:773.746 JLINK_WriteReg(R10, 0x00000000)
TA260 002:773.749 - 0.003ms returns 0
TA260 002:773.754 JLINK_WriteReg(R11, 0x00000000)
TA260 002:773.757 - 0.003ms returns 0
TA260 002:773.762 JLINK_WriteReg(R12, 0x00000000)
TA260 002:773.765 - 0.003ms returns 0
TA260 002:773.769 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:773.773 - 0.004ms returns 0
TA260 002:773.777 JLINK_WriteReg(R14, 0x20000001)
TA260 002:773.780 - 0.003ms returns 0
TA260 002:773.784 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:773.788 - 0.003ms returns 0
TA260 002:773.792 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:773.796 - 0.003ms returns 0
TA260 002:773.800 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:773.803 - 0.003ms returns 0
TA260 002:773.807 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:773.811 - 0.003ms returns 0
TA260 002:773.815 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:773.818 - 0.003ms returns 0
TA260 002:773.823 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:773.827 - 0.004ms returns 0x00000029
TA260 002:773.831 JLINK_Go()
TA260 002:773.840   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:776.592 - 2.759ms 
TA260 002:776.610 JLINK_IsHalted()
TA260 002:777.091 - 0.480ms returns FALSE
TA260 002:777.098 JLINK_HasError()
TA260 002:778.266 JLINK_IsHalted()
TA260 002:778.746 - 0.480ms returns FALSE
TA260 002:778.753 JLINK_HasError()
TA260 002:780.279 JLINK_IsHalted()
TA260 002:780.787 - 0.508ms returns FALSE
TA260 002:780.797 JLINK_HasError()
TA260 002:782.002 JLINK_IsHalted()
TA260 002:784.414   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:784.928 - 2.926ms returns TRUE
TA260 002:784.939 JLINK_ReadReg(R15 (PC))
TA260 002:784.945 - 0.006ms returns 0x20000000
TA260 002:784.950 JLINK_ClrBPEx(BPHandle = 0x00000029)
TA260 002:784.954 - 0.003ms returns 0x00
TA260 002:784.958 JLINK_ReadReg(R0)
TA260 002:784.962 - 0.004ms returns 0x00000000
TA260 002:785.357 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:785.367   Data:  19 CC 11 3F 18 6D 52 3F 6B 79 11 3F 49 A6 52 3F ...
TA260 002:785.379   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:788.020 - 2.663ms returns 0x27C
TA260 002:788.034 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:788.039   Data:  10 82 62 3F 80 E2 ED 3E D7 B0 62 3F 63 30 ED 3E ...
TA260 002:788.049   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:789.970 - 1.935ms returns 0x184
TA260 002:789.988 JLINK_HasError()
TA260 002:790.029 JLINK_WriteReg(R0, 0x08007800)
TA260 002:790.036 - 0.006ms returns 0
TA260 002:790.041 JLINK_WriteReg(R1, 0x00000400)
TA260 002:790.045 - 0.003ms returns 0
TA260 002:790.049 JLINK_WriteReg(R2, 0x20000184)
TA260 002:790.052 - 0.003ms returns 0
TA260 002:790.056 JLINK_WriteReg(R3, 0x00000000)
TA260 002:790.060 - 0.003ms returns 0
TA260 002:790.063 JLINK_WriteReg(R4, 0x00000000)
TA260 002:790.067 - 0.003ms returns 0
TA260 002:790.071 JLINK_WriteReg(R5, 0x00000000)
TA260 002:790.074 - 0.003ms returns 0
TA260 002:790.078 JLINK_WriteReg(R6, 0x00000000)
TA260 002:790.082 - 0.003ms returns 0
TA260 002:790.086 JLINK_WriteReg(R7, 0x00000000)
TA260 002:790.089 - 0.003ms returns 0
TA260 002:790.093 JLINK_WriteReg(R8, 0x00000000)
TA260 002:790.096 - 0.003ms returns 0
TA260 002:790.100 JLINK_WriteReg(R9, 0x20000180)
TA260 002:790.104 - 0.003ms returns 0
TA260 002:790.108 JLINK_WriteReg(R10, 0x00000000)
TA260 002:790.111 - 0.003ms returns 0
TA260 002:790.115 JLINK_WriteReg(R11, 0x00000000)
TA260 002:790.119 - 0.003ms returns 0
TA260 002:790.123 JLINK_WriteReg(R12, 0x00000000)
TA260 002:790.127 - 0.003ms returns 0
TA260 002:790.131 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:790.136 - 0.005ms returns 0
TA260 002:790.140 JLINK_WriteReg(R14, 0x20000001)
TA260 002:790.143 - 0.003ms returns 0
TA260 002:790.147 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:790.150 - 0.003ms returns 0
TA260 002:790.155 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:790.158 - 0.003ms returns 0
TA260 002:790.162 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:790.166 - 0.003ms returns 0
TA260 002:790.169 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:790.173 - 0.003ms returns 0
TA260 002:790.177 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:790.180 - 0.003ms returns 0
TA260 002:790.185 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:790.190 - 0.005ms returns 0x0000002A
TA260 002:790.194 JLINK_Go()
TA260 002:790.202   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:793.071 - 2.876ms 
TA260 002:793.085 JLINK_IsHalted()
TA260 002:793.579 - 0.476ms returns FALSE
TA260 002:793.592 JLINK_HasError()
TA260 002:795.007 JLINK_IsHalted()
TA260 002:795.544 - 0.537ms returns FALSE
TA260 002:795.551 JLINK_HasError()
TA260 002:797.006 JLINK_IsHalted()
TA260 002:799.344   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:799.817 - 2.810ms returns TRUE
TA260 002:799.826 JLINK_ReadReg(R15 (PC))
TA260 002:799.831 - 0.005ms returns 0x20000000
TA260 002:799.835 JLINK_ClrBPEx(BPHandle = 0x0000002A)
TA260 002:799.840 - 0.004ms returns 0x00
TA260 002:799.844 JLINK_ReadReg(R0)
TA260 002:799.848 - 0.003ms returns 0x00000000
TA260 002:800.249 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:800.264   Data:  2C E3 CB 3E 95 D3 6A 3F AE 2A CB 3E 8C FB 6A 3F ...
TA260 002:800.280   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:802.916 - 2.666ms returns 0x27C
TA260 002:802.928 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:802.932   Data:  97 6D 75 3F 10 DD 90 3E 1C 8A 75 3F 2C 1C 90 3E ...
TA260 002:802.952   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:804.825 - 1.896ms returns 0x184
TA260 002:804.840 JLINK_HasError()
TA260 002:804.846 JLINK_WriteReg(R0, 0x08007C00)
TA260 002:804.851 - 0.006ms returns 0
TA260 002:804.856 JLINK_WriteReg(R1, 0x00000400)
TA260 002:804.859 - 0.003ms returns 0
TA260 002:804.863 JLINK_WriteReg(R2, 0x20000184)
TA260 002:804.867 - 0.004ms returns 0
TA260 002:804.871 JLINK_WriteReg(R3, 0x00000000)
TA260 002:804.875 - 0.003ms returns 0
TA260 002:804.879 JLINK_WriteReg(R4, 0x00000000)
TA260 002:804.882 - 0.003ms returns 0
TA260 002:804.886 JLINK_WriteReg(R5, 0x00000000)
TA260 002:804.890 - 0.003ms returns 0
TA260 002:804.894 JLINK_WriteReg(R6, 0x00000000)
TA260 002:804.897 - 0.003ms returns 0
TA260 002:804.901 JLINK_WriteReg(R7, 0x00000000)
TA260 002:804.904 - 0.003ms returns 0
TA260 002:804.908 JLINK_WriteReg(R8, 0x00000000)
TA260 002:804.912 - 0.003ms returns 0
TA260 002:804.916 JLINK_WriteReg(R9, 0x20000180)
TA260 002:804.919 - 0.003ms returns 0
TA260 002:804.923 JLINK_WriteReg(R10, 0x00000000)
TA260 002:804.926 - 0.003ms returns 0
TA260 002:804.930 JLINK_WriteReg(R11, 0x00000000)
TA260 002:804.934 - 0.003ms returns 0
TA260 002:804.938 JLINK_WriteReg(R12, 0x00000000)
TA260 002:804.942 - 0.003ms returns 0
TA260 002:804.946 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:804.950 - 0.004ms returns 0
TA260 002:804.954 JLINK_WriteReg(R14, 0x20000001)
TA260 002:804.957 - 0.003ms returns 0
TA260 002:804.962 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:804.965 - 0.004ms returns 0
TA260 002:804.969 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:804.973 - 0.003ms returns 0
TA260 002:804.977 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:804.980 - 0.003ms returns 0
TA260 002:804.984 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:804.988 - 0.003ms returns 0
TA260 002:804.992 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:804.995 - 0.003ms returns 0
TA260 002:805.000 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:805.004 - 0.004ms returns 0x0000002B
TA260 002:805.008 JLINK_Go()
TA260 002:805.018   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:807.754 - 2.745ms 
TA260 002:807.764 JLINK_IsHalted()
TA260 002:808.269 - 0.504ms returns FALSE
TA260 002:808.275 JLINK_HasError()
TA260 002:809.518 JLINK_IsHalted()
TA260 002:810.024 - 0.505ms returns FALSE
TA260 002:810.031 JLINK_HasError()
TA260 002:812.015 JLINK_IsHalted()
TA260 002:814.360   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:814.879 - 2.863ms returns TRUE
TA260 002:814.891 JLINK_ReadReg(R15 (PC))
TA260 002:814.897 - 0.006ms returns 0x20000000
TA260 002:814.902 JLINK_ClrBPEx(BPHandle = 0x0000002B)
TA260 002:814.906 - 0.004ms returns 0x00
TA260 002:814.910 JLINK_ReadReg(R0)
TA260 002:814.914 - 0.004ms returns 0x00000000
TA260 002:815.305 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:815.315   Data:  A4 B0 58 3E DD 33 7A 3F 8F 27 57 3E 10 49 7A 3F ...
TA260 002:815.326   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:817.975 - 2.669ms returns 0x27C
TA260 002:817.989 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:817.992   Data:  9D EA 7E 3F DD 19 B9 3D C7 F3 7E 3F DA F8 B5 3D ...
TA260 002:818.002   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:819.914 - 1.924ms returns 0x184
TA260 002:819.923 JLINK_HasError()
TA260 002:819.929 JLINK_WriteReg(R0, 0x08008000)
TA260 002:819.934 - 0.005ms returns 0
TA260 002:819.939 JLINK_WriteReg(R1, 0x00000400)
TA260 002:819.942 - 0.003ms returns 0
TA260 002:819.946 JLINK_WriteReg(R2, 0x20000184)
TA260 002:819.950 - 0.003ms returns 0
TA260 002:819.954 JLINK_WriteReg(R3, 0x00000000)
TA260 002:819.958 - 0.003ms returns 0
TA260 002:819.962 JLINK_WriteReg(R4, 0x00000000)
TA260 002:819.965 - 0.003ms returns 0
TA260 002:819.975 JLINK_WriteReg(R5, 0x00000000)
TA260 002:819.980 - 0.005ms returns 0
TA260 002:819.984 JLINK_WriteReg(R6, 0x00000000)
TA260 002:819.988 - 0.003ms returns 0
TA260 002:819.992 JLINK_WriteReg(R7, 0x00000000)
TA260 002:819.995 - 0.003ms returns 0
TA260 002:819.999 JLINK_WriteReg(R8, 0x00000000)
TA260 002:820.002 - 0.003ms returns 0
TA260 002:820.006 JLINK_WriteReg(R9, 0x20000180)
TA260 002:820.010 - 0.003ms returns 0
TA260 002:820.014 JLINK_WriteReg(R10, 0x00000000)
TA260 002:820.018 - 0.003ms returns 0
TA260 002:820.022 JLINK_WriteReg(R11, 0x00000000)
TA260 002:820.025 - 0.003ms returns 0
TA260 002:820.029 JLINK_WriteReg(R12, 0x00000000)
TA260 002:820.032 - 0.003ms returns 0
TA260 002:820.036 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:820.041 - 0.004ms returns 0
TA260 002:820.045 JLINK_WriteReg(R14, 0x20000001)
TA260 002:820.048 - 0.003ms returns 0
TA260 002:820.053 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:820.056 - 0.003ms returns 0
TA260 002:820.061 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:820.064 - 0.003ms returns 0
TA260 002:820.068 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:820.072 - 0.003ms returns 0
TA260 002:820.076 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:820.080 - 0.004ms returns 0
TA260 002:820.084 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:820.088 - 0.003ms returns 0
TA260 002:820.092 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:820.097 - 0.004ms returns 0x0000002C
TA260 002:820.101 JLINK_Go()
TA260 002:820.109   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:822.772 - 2.671ms 
TA260 002:822.781 JLINK_IsHalted()
TA260 002:823.294 - 0.512ms returns FALSE
TA260 002:823.308 JLINK_HasError()
TA260 002:825.538 JLINK_IsHalted()
TA260 002:826.003 - 0.465ms returns FALSE
TA260 002:826.019 JLINK_HasError()
TA260 002:828.030 JLINK_IsHalted()
TA260 002:830.358   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:830.814 - 2.782ms returns TRUE
TA260 002:830.827 JLINK_ReadReg(R15 (PC))
TA260 002:830.833 - 0.006ms returns 0x20000000
TA260 002:830.838 JLINK_ClrBPEx(BPHandle = 0x0000002C)
TA260 002:830.842 - 0.003ms returns 0x00
TA260 002:830.847 JLINK_ReadReg(R0)
TA260 002:830.850 - 0.003ms returns 0x00000000
TA260 002:831.234 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:831.242   Data:  38 39 8A 3C AC F6 7F 3F 4B 51 7B 3C 4A F8 7F 3F ...
TA260 002:831.252   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:833.877 - 2.642ms returns 0x27C
TA260 002:833.897 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:833.902   Data:  C9 9B 7E 3F 89 5D D8 BD 3D 91 7E 3F 37 7D DB BD ...
TA260 002:833.915   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:835.790 - 1.892ms returns 0x184
TA260 002:835.805 JLINK_HasError()
TA260 002:835.812 JLINK_WriteReg(R0, 0x08008400)
TA260 002:835.817 - 0.006ms returns 0
TA260 002:835.822 JLINK_WriteReg(R1, 0x00000400)
TA260 002:835.825 - 0.003ms returns 0
TA260 002:835.829 JLINK_WriteReg(R2, 0x20000184)
TA260 002:835.833 - 0.004ms returns 0
TA260 002:835.837 JLINK_WriteReg(R3, 0x00000000)
TA260 002:835.841 - 0.003ms returns 0
TA260 002:835.845 JLINK_WriteReg(R4, 0x00000000)
TA260 002:835.849 - 0.003ms returns 0
TA260 002:835.852 JLINK_WriteReg(R5, 0x00000000)
TA260 002:835.856 - 0.003ms returns 0
TA260 002:835.860 JLINK_WriteReg(R6, 0x00000000)
TA260 002:835.863 - 0.003ms returns 0
TA260 002:835.868 JLINK_WriteReg(R7, 0x00000000)
TA260 002:835.872 - 0.003ms returns 0
TA260 002:835.876 JLINK_WriteReg(R8, 0x00000000)
TA260 002:835.879 - 0.003ms returns 0
TA260 002:835.883 JLINK_WriteReg(R9, 0x20000180)
TA260 002:835.886 - 0.003ms returns 0
TA260 002:835.890 JLINK_WriteReg(R10, 0x00000000)
TA260 002:835.894 - 0.003ms returns 0
TA260 002:835.898 JLINK_WriteReg(R11, 0x00000000)
TA260 002:835.902 - 0.003ms returns 0
TA260 002:835.906 JLINK_WriteReg(R12, 0x00000000)
TA260 002:835.909 - 0.003ms returns 0
TA260 002:835.913 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:835.918 - 0.004ms returns 0
TA260 002:835.922 JLINK_WriteReg(R14, 0x20000001)
TA260 002:835.925 - 0.003ms returns 0
TA260 002:835.935 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:835.942 - 0.007ms returns 0
TA260 002:835.946 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:835.950 - 0.003ms returns 0
TA260 002:835.964 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:835.968 - 0.004ms returns 0
TA260 002:835.972 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:835.976 - 0.003ms returns 0
TA260 002:835.980 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:835.983 - 0.003ms returns 0
TA260 002:835.988 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:835.992 - 0.004ms returns 0x0000002D
TA260 002:835.996 JLINK_Go()
TA260 002:836.006   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:838.817 - 2.820ms 
TA260 002:838.826 JLINK_IsHalted()
TA260 002:839.302 - 0.475ms returns FALSE
TA260 002:839.308 JLINK_HasError()
TA260 002:841.343 JLINK_IsHalted()
TA260 002:841.782 - 0.439ms returns FALSE
TA260 002:841.789 JLINK_HasError()
TA260 002:842.847 JLINK_IsHalted()
TA260 002:845.056   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:845.630 - 2.782ms returns TRUE
TA260 002:845.639 JLINK_ReadReg(R15 (PC))
TA260 002:845.645 - 0.006ms returns 0x20000000
TA260 002:845.650 JLINK_ClrBPEx(BPHandle = 0x0000002D)
TA260 002:845.654 - 0.004ms returns 0x00
TA260 002:845.658 JLINK_ReadReg(R0)
TA260 002:845.663 - 0.004ms returns 0x00000000
TA260 002:846.088 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:846.096   Data:  50 CC 36 BE 53 E3 7B 3F EC 57 38 BE 4D D1 7B 3F ...
TA260 002:846.108   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:848.748 - 2.660ms returns 0x27C
TA260 002:848.760 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:848.764   Data:  22 84 74 3F 16 61 98 BE 49 66 74 3F FE 20 99 BE ...
TA260 002:848.772   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:850.656 - 1.895ms returns 0x184
TA260 002:850.672 JLINK_HasError()
TA260 002:850.678 JLINK_WriteReg(R0, 0x08008800)
TA260 002:850.684 - 0.006ms returns 0
TA260 002:850.689 JLINK_WriteReg(R1, 0x00000400)
TA260 002:850.692 - 0.003ms returns 0
TA260 002:850.696 JLINK_WriteReg(R2, 0x20000184)
TA260 002:850.700 - 0.003ms returns 0
TA260 002:850.704 JLINK_WriteReg(R3, 0x00000000)
TA260 002:850.708 - 0.003ms returns 0
TA260 002:850.712 JLINK_WriteReg(R4, 0x00000000)
TA260 002:850.715 - 0.003ms returns 0
TA260 002:850.720 JLINK_WriteReg(R5, 0x00000000)
TA260 002:850.723 - 0.003ms returns 0
TA260 002:850.727 JLINK_WriteReg(R6, 0x00000000)
TA260 002:850.730 - 0.003ms returns 0
TA260 002:850.735 JLINK_WriteReg(R7, 0x00000000)
TA260 002:850.738 - 0.003ms returns 0
TA260 002:850.742 JLINK_WriteReg(R8, 0x00000000)
TA260 002:850.746 - 0.003ms returns 0
TA260 002:850.750 JLINK_WriteReg(R9, 0x20000180)
TA260 002:850.753 - 0.003ms returns 0
TA260 002:850.757 JLINK_WriteReg(R10, 0x00000000)
TA260 002:850.761 - 0.003ms returns 0
TA260 002:850.765 JLINK_WriteReg(R11, 0x00000000)
TA260 002:850.768 - 0.003ms returns 0
TA260 002:850.772 JLINK_WriteReg(R12, 0x00000000)
TA260 002:850.776 - 0.003ms returns 0
TA260 002:850.780 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:850.784 - 0.004ms returns 0
TA260 002:850.788 JLINK_WriteReg(R14, 0x20000001)
TA260 002:850.791 - 0.003ms returns 0
TA260 002:850.796 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:850.799 - 0.003ms returns 0
TA260 002:850.803 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:850.807 - 0.003ms returns 0
TA260 002:850.811 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:850.814 - 0.003ms returns 0
TA260 002:850.819 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:850.822 - 0.003ms returns 0
TA260 002:850.826 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:850.830 - 0.003ms returns 0
TA260 002:850.835 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:850.839 - 0.004ms returns 0x0000002E
TA260 002:850.844 JLINK_Go()
TA260 002:850.853   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:853.574 - 2.729ms 
TA260 002:853.590 JLINK_IsHalted()
TA260 002:854.091 - 0.500ms returns FALSE
TA260 002:854.107 JLINK_HasError()
TA260 002:855.364 JLINK_IsHalted()
TA260 002:855.842 - 0.478ms returns FALSE
TA260 002:855.857 JLINK_HasError()
TA260 002:857.361 JLINK_IsHalted()
TA260 002:857.877 - 0.516ms returns FALSE
TA260 002:857.887 JLINK_HasError()
TA260 002:859.364 JLINK_IsHalted()
TA260 002:861.686   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:862.158 - 2.794ms returns TRUE
TA260 002:862.166 JLINK_ReadReg(R15 (PC))
TA260 002:862.173 - 0.006ms returns 0x20000000
TA260 002:862.178 JLINK_ClrBPEx(BPHandle = 0x0000002E)
TA260 002:862.182 - 0.004ms returns 0x00
TA260 002:862.186 JLINK_ReadReg(R0)
TA260 002:862.190 - 0.003ms returns 0x00000000
TA260 002:862.590 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:862.600   Data:  B6 EC BB BE EB 21 6E 3F AF A7 BC BE F2 FC 6D 3F ...
TA260 002:862.612   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:865.200 - 2.609ms returns 0x27C
TA260 002:865.218 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:865.222   Data:  F2 06 61 3F B1 CF F4 BE F2 D6 60 3F 35 80 F5 BE ...
TA260 002:865.235   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:867.140 - 1.921ms returns 0x184
TA260 002:867.158 JLINK_HasError()
TA260 002:867.199 JLINK_WriteReg(R0, 0x08008C00)
TA260 002:867.206 - 0.007ms returns 0
TA260 002:867.210 JLINK_WriteReg(R1, 0x00000400)
TA260 002:867.214 - 0.003ms returns 0
TA260 002:867.218 JLINK_WriteReg(R2, 0x20000184)
TA260 002:867.222 - 0.004ms returns 0
TA260 002:867.226 JLINK_WriteReg(R3, 0x00000000)
TA260 002:867.230 - 0.004ms returns 0
TA260 002:867.234 JLINK_WriteReg(R4, 0x00000000)
TA260 002:867.237 - 0.003ms returns 0
TA260 002:867.242 JLINK_WriteReg(R5, 0x00000000)
TA260 002:867.246 - 0.003ms returns 0
TA260 002:867.250 JLINK_WriteReg(R6, 0x00000000)
TA260 002:867.253 - 0.003ms returns 0
TA260 002:867.257 JLINK_WriteReg(R7, 0x00000000)
TA260 002:867.260 - 0.003ms returns 0
TA260 002:867.264 JLINK_WriteReg(R8, 0x00000000)
TA260 002:867.268 - 0.003ms returns 0
TA260 002:867.272 JLINK_WriteReg(R9, 0x20000180)
TA260 002:867.275 - 0.003ms returns 0
TA260 002:867.279 JLINK_WriteReg(R10, 0x00000000)
TA260 002:867.283 - 0.003ms returns 0
TA260 002:867.287 JLINK_WriteReg(R11, 0x00000000)
TA260 002:867.290 - 0.003ms returns 0
TA260 002:867.294 JLINK_WriteReg(R12, 0x00000000)
TA260 002:867.298 - 0.003ms returns 0
TA260 002:867.302 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:867.306 - 0.004ms returns 0
TA260 002:867.310 JLINK_WriteReg(R14, 0x20000001)
TA260 002:867.313 - 0.003ms returns 0
TA260 002:867.317 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:867.321 - 0.003ms returns 0
TA260 002:867.325 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:867.329 - 0.003ms returns 0
TA260 002:867.333 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:867.337 - 0.004ms returns 0
TA260 002:867.341 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:867.345 - 0.003ms returns 0
TA260 002:867.349 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:867.352 - 0.003ms returns 0
TA260 002:867.357 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:867.361 - 0.004ms returns 0x0000002F
TA260 002:867.366 JLINK_Go()
TA260 002:867.375   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:870.160 - 2.794ms 
TA260 002:870.170 JLINK_IsHalted()
TA260 002:870.658 - 0.487ms returns FALSE
TA260 002:870.666 JLINK_HasError()
TA260 002:872.322 JLINK_IsHalted()
TA260 002:872.816 - 0.494ms returns FALSE
TA260 002:872.825 JLINK_HasError()
TA260 002:874.362 JLINK_IsHalted()
TA260 002:876.828   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:877.458 - 3.095ms returns TRUE
TA260 002:877.470 JLINK_ReadReg(R15 (PC))
TA260 002:877.476 - 0.005ms returns 0x20000000
TA260 002:877.481 JLINK_ClrBPEx(BPHandle = 0x0000002F)
TA260 002:877.485 - 0.004ms returns 0x00
TA260 002:877.489 JLINK_ReadReg(R0)
TA260 002:877.493 - 0.003ms returns 0x00000000
TA260 002:877.866 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:877.873   Data:  3D 9D 0A BF C7 39 57 3F B7 F1 0A BF 48 03 57 3F ...
TA260 002:877.884   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:880.556 - 2.689ms returns 0x27C
TA260 002:880.568 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:880.572   Data:  F5 E3 44 3F EE EA 23 BF A5 A3 44 3F 1A 38 24 BF ...
TA260 002:880.587   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:882.503 - 1.934ms returns 0x184
TA260 002:882.516 JLINK_HasError()
TA260 002:882.522 JLINK_WriteReg(R0, 0x08009000)
TA260 002:882.528 - 0.005ms returns 0
TA260 002:882.532 JLINK_WriteReg(R1, 0x00000400)
TA260 002:882.538 - 0.005ms returns 0
TA260 002:882.542 JLINK_WriteReg(R2, 0x20000184)
TA260 002:882.546 - 0.003ms returns 0
TA260 002:882.550 JLINK_WriteReg(R3, 0x00000000)
TA260 002:882.553 - 0.003ms returns 0
TA260 002:882.557 JLINK_WriteReg(R4, 0x00000000)
TA260 002:882.560 - 0.003ms returns 0
TA260 002:882.565 JLINK_WriteReg(R5, 0x00000000)
TA260 002:882.568 - 0.003ms returns 0
TA260 002:882.572 JLINK_WriteReg(R6, 0x00000000)
TA260 002:882.576 - 0.003ms returns 0
TA260 002:882.580 JLINK_WriteReg(R7, 0x00000000)
TA260 002:882.583 - 0.003ms returns 0
TA260 002:882.587 JLINK_WriteReg(R8, 0x00000000)
TA260 002:882.591 - 0.003ms returns 0
TA260 002:882.595 JLINK_WriteReg(R9, 0x20000180)
TA260 002:882.598 - 0.003ms returns 0
TA260 002:882.602 JLINK_WriteReg(R10, 0x00000000)
TA260 002:882.606 - 0.003ms returns 0
TA260 002:882.610 JLINK_WriteReg(R11, 0x00000000)
TA260 002:882.613 - 0.003ms returns 0
TA260 002:882.617 JLINK_WriteReg(R12, 0x00000000)
TA260 002:882.621 - 0.003ms returns 0
TA260 002:882.625 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:882.629 - 0.004ms returns 0
TA260 002:882.634 JLINK_WriteReg(R14, 0x20000001)
TA260 002:882.638 - 0.004ms returns 0
TA260 002:882.642 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:882.646 - 0.003ms returns 0
TA260 002:882.650 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:882.654 - 0.004ms returns 0
TA260 002:882.658 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:882.662 - 0.003ms returns 0
TA260 002:882.666 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:882.669 - 0.003ms returns 0
TA260 002:882.673 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:882.677 - 0.003ms returns 0
TA260 002:882.682 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:882.686 - 0.004ms returns 0x00000030
TA260 002:882.690 JLINK_Go()
TA260 002:882.699   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:885.414 - 2.722ms 
TA260 002:885.439 JLINK_IsHalted()
TA260 002:885.918 - 0.478ms returns FALSE
TA260 002:885.924 JLINK_HasError()
TA260 002:887.835 JLINK_IsHalted()
TA260 002:888.317 - 0.482ms returns FALSE
TA260 002:888.324 JLINK_HasError()
TA260 002:889.833 JLINK_IsHalted()
TA260 002:892.195   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:892.691 - 2.857ms returns TRUE
TA260 002:892.704 JLINK_ReadReg(R15 (PC))
TA260 002:892.710 - 0.005ms returns 0x20000000
TA260 002:892.745 JLINK_ClrBPEx(BPHandle = 0x00000030)
TA260 002:892.751 - 0.006ms returns 0x00
TA260 002:892.755 JLINK_ReadReg(R0)
TA260 002:892.759 - 0.003ms returns 0x00000000
TA260 002:893.152 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:893.162   Data:  71 F0 31 BF 43 0C 38 3F AA 38 32 BF 55 C6 37 3F ...
TA260 002:893.174   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:895.846 - 2.693ms returns 0x27C
TA260 002:895.871 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:895.876   Data:  F9 2F 21 3F 67 21 47 BF D2 E1 20 3F 85 60 47 BF ...
TA260 002:895.889   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:897.833 - 1.961ms returns 0x184
TA260 002:897.844 JLINK_HasError()
TA260 002:897.850 JLINK_WriteReg(R0, 0x08009400)
TA260 002:897.855 - 0.005ms returns 0
TA260 002:897.859 JLINK_WriteReg(R1, 0x00000400)
TA260 002:897.863 - 0.004ms returns 0
TA260 002:897.868 JLINK_WriteReg(R2, 0x20000184)
TA260 002:897.872 - 0.003ms returns 0
TA260 002:897.876 JLINK_WriteReg(R3, 0x00000000)
TA260 002:897.879 - 0.003ms returns 0
TA260 002:897.883 JLINK_WriteReg(R4, 0x00000000)
TA260 002:897.887 - 0.003ms returns 0
TA260 002:897.891 JLINK_WriteReg(R5, 0x00000000)
TA260 002:897.894 - 0.003ms returns 0
TA260 002:897.898 JLINK_WriteReg(R6, 0x00000000)
TA260 002:897.902 - 0.003ms returns 0
TA260 002:897.906 JLINK_WriteReg(R7, 0x00000000)
TA260 002:897.909 - 0.003ms returns 0
TA260 002:897.918 JLINK_WriteReg(R8, 0x00000000)
TA260 002:897.923 - 0.005ms returns 0
TA260 002:897.927 JLINK_WriteReg(R9, 0x20000180)
TA260 002:897.931 - 0.003ms returns 0
TA260 002:897.935 JLINK_WriteReg(R10, 0x00000000)
TA260 002:897.939 - 0.003ms returns 0
TA260 002:897.943 JLINK_WriteReg(R11, 0x00000000)
TA260 002:897.946 - 0.003ms returns 0
TA260 002:897.950 JLINK_WriteReg(R12, 0x00000000)
TA260 002:897.954 - 0.003ms returns 0
TA260 002:897.958 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:897.962 - 0.004ms returns 0
TA260 002:897.966 JLINK_WriteReg(R14, 0x20000001)
TA260 002:897.970 - 0.003ms returns 0
TA260 002:897.975 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:897.978 - 0.004ms returns 0
TA260 002:897.983 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:897.987 - 0.003ms returns 0
TA260 002:897.991 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:897.995 - 0.003ms returns 0
TA260 002:897.999 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:898.002 - 0.003ms returns 0
TA260 002:898.006 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:898.010 - 0.003ms returns 0
TA260 002:898.014 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:898.019 - 0.004ms returns 0x00000031
TA260 002:898.023 JLINK_Go()
TA260 002:898.032   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:900.896 - 2.871ms 
TA260 002:900.913 JLINK_IsHalted()
TA260 002:901.421 - 0.507ms returns FALSE
TA260 002:901.428 JLINK_HasError()
TA260 002:902.843 JLINK_IsHalted()
TA260 002:903.428 - 0.583ms returns FALSE
TA260 002:903.450 JLINK_HasError()
TA260 002:904.855 JLINK_IsHalted()
TA260 002:907.313   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:907.845 - 2.989ms returns TRUE
TA260 002:907.856 JLINK_ReadReg(R15 (PC))
TA260 002:907.861 - 0.005ms returns 0x20000000
TA260 002:907.866 JLINK_ClrBPEx(BPHandle = 0x00000031)
TA260 002:907.870 - 0.004ms returns 0x00
TA260 002:907.874 JLINK_ReadReg(R0)
TA260 002:907.878 - 0.003ms returns 0x00000000
TA260 002:908.252 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:908.261   Data:  18 6D 52 BF 19 CC 11 3F 49 A6 52 BF 6B 79 11 3F ...
TA260 002:908.272   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:910.833 - 2.581ms returns 0x27C
TA260 002:910.847 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:910.851   Data:  79 94 EE 3E D7 B0 62 BF 80 E2 ED 3E 7B DF 62 BF ...
TA260 002:910.862   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:912.826 - 1.978ms returns 0x184
TA260 002:912.842 JLINK_HasError()
TA260 002:912.848 JLINK_WriteReg(R0, 0x08009800)
TA260 002:912.853 - 0.005ms returns 0
TA260 002:912.857 JLINK_WriteReg(R1, 0x00000400)
TA260 002:912.861 - 0.003ms returns 0
TA260 002:912.865 JLINK_WriteReg(R2, 0x20000184)
TA260 002:912.868 - 0.003ms returns 0
TA260 002:912.873 JLINK_WriteReg(R3, 0x00000000)
TA260 002:912.876 - 0.003ms returns 0
TA260 002:912.880 JLINK_WriteReg(R4, 0x00000000)
TA260 002:912.884 - 0.003ms returns 0
TA260 002:912.888 JLINK_WriteReg(R5, 0x00000000)
TA260 002:912.891 - 0.003ms returns 0
TA260 002:912.895 JLINK_WriteReg(R6, 0x00000000)
TA260 002:912.899 - 0.003ms returns 0
TA260 002:912.903 JLINK_WriteReg(R7, 0x00000000)
TA260 002:912.906 - 0.003ms returns 0
TA260 002:912.910 JLINK_WriteReg(R8, 0x00000000)
TA260 002:912.914 - 0.003ms returns 0
TA260 002:912.918 JLINK_WriteReg(R9, 0x20000180)
TA260 002:912.921 - 0.003ms returns 0
TA260 002:912.925 JLINK_WriteReg(R10, 0x00000000)
TA260 002:912.928 - 0.003ms returns 0
TA260 002:912.932 JLINK_WriteReg(R11, 0x00000000)
TA260 002:912.936 - 0.003ms returns 0
TA260 002:912.940 JLINK_WriteReg(R12, 0x00000000)
TA260 002:912.943 - 0.003ms returns 0
TA260 002:912.948 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:912.952 - 0.004ms returns 0
TA260 002:912.956 JLINK_WriteReg(R14, 0x20000001)
TA260 002:912.959 - 0.003ms returns 0
TA260 002:912.963 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:912.967 - 0.003ms returns 0
TA260 002:912.971 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:912.974 - 0.003ms returns 0
TA260 002:912.978 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:912.982 - 0.003ms returns 0
TA260 002:912.990 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:912.995 - 0.005ms returns 0
TA260 002:912.999 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:913.003 - 0.003ms returns 0
TA260 002:913.008 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:913.012 - 0.004ms returns 0x00000032
TA260 002:913.016 JLINK_Go()
TA260 002:913.024   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:915.904 - 2.887ms 
TA260 002:915.928 JLINK_IsHalted()
TA260 002:916.420 - 0.491ms returns FALSE
TA260 002:916.426 JLINK_HasError()
TA260 002:918.356 JLINK_IsHalted()
TA260 002:918.782 - 0.426ms returns FALSE
TA260 002:918.789 JLINK_HasError()
TA260 002:920.363 JLINK_IsHalted()
TA260 002:922.694   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:923.162 - 2.799ms returns TRUE
TA260 002:923.181 JLINK_ReadReg(R15 (PC))
TA260 002:923.188 - 0.007ms returns 0x20000000
TA260 002:923.193 JLINK_ClrBPEx(BPHandle = 0x00000032)
TA260 002:923.197 - 0.004ms returns 0x00
TA260 002:923.202 JLINK_ReadReg(R0)
TA260 002:923.221 - 0.018ms returns 0x00000000
TA260 002:923.794 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:923.806   Data:  95 D3 6A BF 2C E3 CB 3E 8C FB 6A BF AE 2A CB 3E ...
TA260 002:923.832   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:926.381 - 2.586ms returns 0x27C
TA260 002:926.402 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:926.406   Data:  DD 9D 91 3E 1C 8A 75 BF 10 DD 90 3E 7B A6 75 BF ...
TA260 002:926.420   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:928.284 - 1.881ms returns 0x184
TA260 002:928.296 JLINK_HasError()
TA260 002:928.302 JLINK_WriteReg(R0, 0x08009C00)
TA260 002:928.308 - 0.005ms returns 0
TA260 002:928.312 JLINK_WriteReg(R1, 0x00000400)
TA260 002:928.316 - 0.003ms returns 0
TA260 002:928.320 JLINK_WriteReg(R2, 0x20000184)
TA260 002:928.323 - 0.003ms returns 0
TA260 002:928.328 JLINK_WriteReg(R3, 0x00000000)
TA260 002:928.331 - 0.003ms returns 0
TA260 002:928.335 JLINK_WriteReg(R4, 0x00000000)
TA260 002:928.339 - 0.004ms returns 0
TA260 002:928.343 JLINK_WriteReg(R5, 0x00000000)
TA260 002:928.347 - 0.003ms returns 0
TA260 002:928.351 JLINK_WriteReg(R6, 0x00000000)
TA260 002:928.354 - 0.003ms returns 0
TA260 002:928.358 JLINK_WriteReg(R7, 0x00000000)
TA260 002:928.362 - 0.003ms returns 0
TA260 002:928.366 JLINK_WriteReg(R8, 0x00000000)
TA260 002:928.370 - 0.004ms returns 0
TA260 002:928.374 JLINK_WriteReg(R9, 0x20000180)
TA260 002:928.378 - 0.003ms returns 0
TA260 002:928.382 JLINK_WriteReg(R10, 0x00000000)
TA260 002:928.385 - 0.003ms returns 0
TA260 002:928.389 JLINK_WriteReg(R11, 0x00000000)
TA260 002:928.392 - 0.003ms returns 0
TA260 002:928.396 JLINK_WriteReg(R12, 0x00000000)
TA260 002:928.400 - 0.003ms returns 0
TA260 002:928.404 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:928.409 - 0.004ms returns 0
TA260 002:928.413 JLINK_WriteReg(R14, 0x20000001)
TA260 002:928.416 - 0.003ms returns 0
TA260 002:928.420 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:928.424 - 0.003ms returns 0
TA260 002:928.428 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:928.431 - 0.003ms returns 0
TA260 002:928.435 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:928.439 - 0.003ms returns 0
TA260 002:928.444 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:928.447 - 0.003ms returns 0
TA260 002:928.451 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:928.454 - 0.003ms returns 0
TA260 002:928.459 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:928.464 - 0.004ms returns 0x00000033
TA260 002:928.468 JLINK_Go()
TA260 002:928.478   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:931.203 - 2.734ms 
TA260 002:931.211 JLINK_IsHalted()
TA260 002:931.710 - 0.499ms returns FALSE
TA260 002:931.716 JLINK_HasError()
TA260 002:933.877 JLINK_IsHalted()
TA260 002:934.411 - 0.532ms returns FALSE
TA260 002:934.424 JLINK_HasError()
TA260 002:935.882 JLINK_IsHalted()
TA260 002:938.305   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:938.817 - 2.935ms returns TRUE
TA260 002:938.826 JLINK_ReadReg(R15 (PC))
TA260 002:938.832 - 0.005ms returns 0x20000000
TA260 002:938.836 JLINK_ClrBPEx(BPHandle = 0x00000033)
TA260 002:938.846 - 0.009ms returns 0x00
TA260 002:938.850 JLINK_ReadReg(R0)
TA260 002:938.854 - 0.003ms returns 0x00000000
TA260 002:939.226 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:939.234   Data:  DD 33 7A BF A4 B0 58 3E 10 49 7A BF 8F 27 57 3E ...
TA260 002:939.245   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:941.843 - 2.616ms returns 0x27C
TA260 002:941.856 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:941.860   Data:  C3 3A BC 3D C7 F3 7E BF DD 19 B9 3D C9 FC 7E BF ...
TA260 002:941.870   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:943.851 - 1.987ms returns 0x184
TA260 002:943.876 JLINK_HasError()
TA260 002:943.921 JLINK_WriteReg(R0, 0x0800A000)
TA260 002:943.938 - 0.017ms returns 0
TA260 002:943.943 JLINK_WriteReg(R1, 0x00000400)
TA260 002:943.946 - 0.003ms returns 0
TA260 002:943.951 JLINK_WriteReg(R2, 0x20000184)
TA260 002:943.954 - 0.003ms returns 0
TA260 002:943.958 JLINK_WriteReg(R3, 0x00000000)
TA260 002:943.962 - 0.003ms returns 0
TA260 002:943.966 JLINK_WriteReg(R4, 0x00000000)
TA260 002:943.970 - 0.004ms returns 0
TA260 002:943.974 JLINK_WriteReg(R5, 0x00000000)
TA260 002:943.977 - 0.003ms returns 0
TA260 002:943.981 JLINK_WriteReg(R6, 0x00000000)
TA260 002:943.984 - 0.003ms returns 0
TA260 002:943.988 JLINK_WriteReg(R7, 0x00000000)
TA260 002:943.992 - 0.003ms returns 0
TA260 002:943.996 JLINK_WriteReg(R8, 0x00000000)
TA260 002:943.999 - 0.003ms returns 0
TA260 002:944.003 JLINK_WriteReg(R9, 0x20000180)
TA260 002:944.007 - 0.003ms returns 0
TA260 002:944.011 JLINK_WriteReg(R10, 0x00000000)
TA260 002:944.014 - 0.003ms returns 0
TA260 002:944.018 JLINK_WriteReg(R11, 0x00000000)
TA260 002:944.021 - 0.003ms returns 0
TA260 002:944.025 JLINK_WriteReg(R12, 0x00000000)
TA260 002:944.029 - 0.003ms returns 0
TA260 002:944.033 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:944.038 - 0.004ms returns 0
TA260 002:944.042 JLINK_WriteReg(R14, 0x20000001)
TA260 002:944.045 - 0.003ms returns 0
TA260 002:944.049 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:944.052 - 0.003ms returns 0
TA260 002:944.057 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:944.060 - 0.003ms returns 0
TA260 002:944.064 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:944.068 - 0.003ms returns 0
TA260 002:944.072 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:944.075 - 0.003ms returns 0
TA260 002:944.079 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:944.083 - 0.003ms returns 0
TA260 002:944.087 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:944.092 - 0.004ms returns 0x00000034
TA260 002:944.096 JLINK_Go()
TA260 002:944.106   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:946.968 - 2.869ms 
TA260 002:946.983 JLINK_IsHalted()
TA260 002:947.505 - 0.522ms returns FALSE
TA260 002:947.512 JLINK_HasError()
TA260 002:949.389 JLINK_IsHalted()
TA260 002:949.885 - 0.496ms returns FALSE
TA260 002:949.892 JLINK_HasError()
TA260 002:951.387 JLINK_IsHalted()
TA260 002:953.697   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:954.218 - 2.830ms returns TRUE
TA260 002:954.232 JLINK_ReadReg(R15 (PC))
TA260 002:954.238 - 0.005ms returns 0x20000000
TA260 002:954.242 JLINK_ClrBPEx(BPHandle = 0x00000034)
TA260 002:954.246 - 0.004ms returns 0x00
TA260 002:954.252 JLINK_ReadReg(R0)
TA260 002:954.255 - 0.003ms returns 0x00000000
TA260 002:954.822 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:954.836   Data:  AC F6 7F BF 38 39 8A 3C 4A F8 7F BF 4B 51 7B 3C ...
TA260 002:954.849   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:957.372 - 2.549ms returns 0x27C
TA260 002:957.386 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:957.389   Data:  B9 3D D5 BD 3D 91 7E BF 89 5D D8 BD 8B 86 7E BF ...
TA260 002:957.398   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:959.284 - 1.898ms returns 0x184
TA260 002:959.295 JLINK_HasError()
TA260 002:959.302 JLINK_WriteReg(R0, 0x0800A400)
TA260 002:959.307 - 0.005ms returns 0
TA260 002:959.312 JLINK_WriteReg(R1, 0x00000400)
TA260 002:959.315 - 0.003ms returns 0
TA260 002:959.319 JLINK_WriteReg(R2, 0x20000184)
TA260 002:959.481 - 0.161ms returns 0
TA260 002:959.486 JLINK_WriteReg(R3, 0x00000000)
TA260 002:959.490 - 0.003ms returns 0
TA260 002:959.494 JLINK_WriteReg(R4, 0x00000000)
TA260 002:959.498 - 0.003ms returns 0
TA260 002:959.502 JLINK_WriteReg(R5, 0x00000000)
TA260 002:959.505 - 0.003ms returns 0
TA260 002:959.520 JLINK_WriteReg(R6, 0x00000000)
TA260 002:959.524 - 0.003ms returns 0
TA260 002:959.528 JLINK_WriteReg(R7, 0x00000000)
TA260 002:959.531 - 0.003ms returns 0
TA260 002:959.536 JLINK_WriteReg(R8, 0x00000000)
TA260 002:959.539 - 0.004ms returns 0
TA260 002:959.543 JLINK_WriteReg(R9, 0x20000180)
TA260 002:959.547 - 0.003ms returns 0
TA260 002:959.551 JLINK_WriteReg(R10, 0x00000000)
TA260 002:959.554 - 0.003ms returns 0
TA260 002:959.558 JLINK_WriteReg(R11, 0x00000000)
TA260 002:959.562 - 0.003ms returns 0
TA260 002:959.566 JLINK_WriteReg(R12, 0x00000000)
TA260 002:959.569 - 0.003ms returns 0
TA260 002:959.574 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:959.577 - 0.004ms returns 0
TA260 002:959.581 JLINK_WriteReg(R14, 0x20000001)
TA260 002:959.585 - 0.003ms returns 0
TA260 002:959.589 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:959.593 - 0.004ms returns 0
TA260 002:959.597 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:959.600 - 0.003ms returns 0
TA260 002:959.604 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:959.608 - 0.003ms returns 0
TA260 002:959.612 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:959.616 - 0.003ms returns 0
TA260 002:959.620 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:959.623 - 0.003ms returns 0
TA260 002:959.628 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:959.633 - 0.005ms returns 0x00000035
TA260 002:959.637 JLINK_Go()
TA260 002:959.646   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:962.437 - 2.800ms 
TA260 002:962.451 JLINK_IsHalted()
TA260 002:962.946 - 0.494ms returns FALSE
TA260 002:962.955 JLINK_HasError()
TA260 002:964.407 JLINK_IsHalted()
TA260 002:964.943 - 0.536ms returns FALSE
TA260 002:964.958 JLINK_HasError()
TA260 002:966.404 JLINK_IsHalted()
TA260 002:968.694   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:969.199 - 2.794ms returns TRUE
TA260 002:969.217 JLINK_ReadReg(R15 (PC))
TA260 002:969.223 - 0.006ms returns 0x20000000
TA260 002:969.261 JLINK_ClrBPEx(BPHandle = 0x00000035)
TA260 002:969.266 - 0.005ms returns 0x00
TA260 002:969.271 JLINK_ReadReg(R0)
TA260 002:969.275 - 0.003ms returns 0x00000000
TA260 002:970.009 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:970.026   Data:  53 E3 7B BF 50 CC 36 BE 4D D1 7B BF EC 57 38 BE ...
TA260 002:970.038   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:972.609 - 2.599ms returns 0x27C
TA260 002:972.620 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:972.624   Data:  17 A1 97 BE 49 66 74 BF 16 61 98 BE 4B 48 74 BF ...
TA260 002:972.632   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:974.511 - 1.890ms returns 0x184
TA260 002:974.530 JLINK_HasError()
TA260 002:974.536 JLINK_WriteReg(R0, 0x0800A800)
TA260 002:974.543 - 0.007ms returns 0
TA260 002:974.548 JLINK_WriteReg(R1, 0x00000400)
TA260 002:974.551 - 0.003ms returns 0
TA260 002:974.555 JLINK_WriteReg(R2, 0x20000184)
TA260 002:974.558 - 0.003ms returns 0
TA260 002:974.562 JLINK_WriteReg(R3, 0x00000000)
TA260 002:974.566 - 0.003ms returns 0
TA260 002:974.570 JLINK_WriteReg(R4, 0x00000000)
TA260 002:974.573 - 0.003ms returns 0
TA260 002:974.577 JLINK_WriteReg(R5, 0x00000000)
TA260 002:974.581 - 0.004ms returns 0
TA260 002:974.586 JLINK_WriteReg(R6, 0x00000000)
TA260 002:974.590 - 0.003ms returns 0
TA260 002:974.594 JLINK_WriteReg(R7, 0x00000000)
TA260 002:974.597 - 0.003ms returns 0
TA260 002:974.602 JLINK_WriteReg(R8, 0x00000000)
TA260 002:974.605 - 0.003ms returns 0
TA260 002:974.609 JLINK_WriteReg(R9, 0x20000180)
TA260 002:974.612 - 0.003ms returns 0
TA260 002:974.616 JLINK_WriteReg(R10, 0x00000000)
TA260 002:974.620 - 0.003ms returns 0
TA260 002:974.624 JLINK_WriteReg(R11, 0x00000000)
TA260 002:974.627 - 0.003ms returns 0
TA260 002:974.631 JLINK_WriteReg(R12, 0x00000000)
TA260 002:974.673 - 0.042ms returns 0
TA260 002:974.678 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:974.682 - 0.004ms returns 0
TA260 002:974.686 JLINK_WriteReg(R14, 0x20000001)
TA260 002:974.690 - 0.003ms returns 0
TA260 002:974.694 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:974.697 - 0.003ms returns 0
TA260 002:974.702 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:974.705 - 0.003ms returns 0
TA260 002:974.709 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:974.712 - 0.003ms returns 0
TA260 002:974.716 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:974.720 - 0.003ms returns 0
TA260 002:974.724 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:974.728 - 0.003ms returns 0
TA260 002:974.732 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:974.736 - 0.004ms returns 0x00000036
TA260 002:974.741 JLINK_Go()
TA260 002:974.750   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:977.514 - 2.772ms 
TA260 002:977.536 JLINK_IsHalted()
TA260 002:978.047 - 0.510ms returns FALSE
TA260 002:978.053 JLINK_HasError()
TA260 002:979.909 JLINK_IsHalted()
TA260 002:980.347 - 0.437ms returns FALSE
TA260 002:980.355 JLINK_HasError()
TA260 002:982.419 JLINK_IsHalted()
TA260 002:984.683   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 002:985.196 - 2.777ms returns TRUE
TA260 002:985.205 JLINK_ReadReg(R15 (PC))
TA260 002:985.211 - 0.006ms returns 0x20000000
TA260 002:985.215 JLINK_ClrBPEx(BPHandle = 0x00000036)
TA260 002:985.219 - 0.003ms returns 0x00
TA260 002:985.223 JLINK_ReadReg(R0)
TA260 002:985.227 - 0.003ms returns 0x00000000
TA260 002:985.845 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 002:985.858   Data:  EB 21 6E BF B6 EC BB BE F2 FC 6D BF AF A7 BC BE ...
TA260 002:985.871   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 002:988.566 - 2.721ms returns 0x27C
TA260 002:988.576 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 002:988.579   Data:  07 1F F4 BE F2 D6 60 BF B1 CF F4 BE CF A6 60 BF ...
TA260 002:988.587   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 002:990.466 - 1.889ms returns 0x184
TA260 002:990.476 JLINK_HasError()
TA260 002:990.482 JLINK_WriteReg(R0, 0x0800AC00)
TA260 002:990.488 - 0.006ms returns 0
TA260 002:990.492 JLINK_WriteReg(R1, 0x00000400)
TA260 002:990.495 - 0.003ms returns 0
TA260 002:990.500 JLINK_WriteReg(R2, 0x20000184)
TA260 002:990.503 - 0.003ms returns 0
TA260 002:990.507 JLINK_WriteReg(R3, 0x00000000)
TA260 002:990.510 - 0.003ms returns 0
TA260 002:990.514 JLINK_WriteReg(R4, 0x00000000)
TA260 002:990.518 - 0.003ms returns 0
TA260 002:990.522 JLINK_WriteReg(R5, 0x00000000)
TA260 002:990.525 - 0.003ms returns 0
TA260 002:990.529 JLINK_WriteReg(R6, 0x00000000)
TA260 002:990.533 - 0.003ms returns 0
TA260 002:990.536 JLINK_WriteReg(R7, 0x00000000)
TA260 002:990.540 - 0.003ms returns 0
TA260 002:990.544 JLINK_WriteReg(R8, 0x00000000)
TA260 002:990.548 - 0.003ms returns 0
TA260 002:990.552 JLINK_WriteReg(R9, 0x20000180)
TA260 002:990.555 - 0.003ms returns 0
TA260 002:990.559 JLINK_WriteReg(R10, 0x00000000)
TA260 002:990.563 - 0.003ms returns 0
TA260 002:990.567 JLINK_WriteReg(R11, 0x00000000)
TA260 002:990.571 - 0.003ms returns 0
TA260 002:990.584 JLINK_WriteReg(R12, 0x00000000)
TA260 002:990.588 - 0.003ms returns 0
TA260 002:990.592 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 002:990.596 - 0.004ms returns 0
TA260 002:990.600 JLINK_WriteReg(R14, 0x20000001)
TA260 002:990.603 - 0.003ms returns 0
TA260 002:990.608 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 002:990.611 - 0.003ms returns 0
TA260 002:990.615 JLINK_WriteReg(XPSR, 0x01000000)
TA260 002:990.618 - 0.003ms returns 0
TA260 002:990.622 JLINK_WriteReg(MSP, 0x20001000)
TA260 002:990.626 - 0.003ms returns 0
TA260 002:990.630 JLINK_WriteReg(PSP, 0x20001000)
TA260 002:990.633 - 0.003ms returns 0
TA260 002:990.637 JLINK_WriteReg(CFBP, 0x00000000)
TA260 002:990.641 - 0.003ms returns 0
TA260 002:990.645 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 002:990.650 - 0.005ms returns 0x00000037
TA260 002:990.654 JLINK_Go()
TA260 002:990.662   CPU_ReadMem(4 bytes @ 0x********)
TA260 002:993.533 - 2.878ms 
TA260 002:993.564 JLINK_IsHalted()
TA260 002:993.987 - 0.422ms returns FALSE
TA260 002:993.999 JLINK_HasError()
TA260 002:995.932 JLINK_IsHalted()
TA260 002:996.421 - 0.488ms returns FALSE
TA260 002:996.430 JLINK_HasError()
TA260 002:997.927 JLINK_IsHalted()
TA260 003:000.295   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:000.810 - 2.882ms returns TRUE
TA260 003:000.818 JLINK_ReadReg(R15 (PC))
TA260 003:000.823 - 0.005ms returns 0x20000000
TA260 003:000.828 JLINK_ClrBPEx(BPHandle = 0x00000037)
TA260 003:000.832 - 0.003ms returns 0x00
TA260 003:000.836 JLINK_ReadReg(R0)
TA260 003:000.840 - 0.004ms returns 0x00000000
TA260 003:001.441 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:001.455   Data:  C7 39 57 BF 3D 9D 0A BF 48 03 57 BF B7 F1 0A BF ...
TA260 003:001.469   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:004.118 - 2.677ms returns 0x27C
TA260 003:004.142 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:004.146   Data:  A9 9D 23 BF A5 A3 44 BF EE EA 23 BF 37 63 44 BF ...
TA260 003:004.159   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:006.047 - 1.905ms returns 0x184
TA260 003:006.059 JLINK_HasError()
TA260 003:006.065 JLINK_WriteReg(R0, 0x0800B000)
TA260 003:006.071 - 0.006ms returns 0
TA260 003:006.075 JLINK_WriteReg(R1, 0x00000400)
TA260 003:006.079 - 0.003ms returns 0
TA260 003:006.083 JLINK_WriteReg(R2, 0x20000184)
TA260 003:006.086 - 0.003ms returns 0
TA260 003:006.090 JLINK_WriteReg(R3, 0x00000000)
TA260 003:006.094 - 0.003ms returns 0
TA260 003:006.098 JLINK_WriteReg(R4, 0x00000000)
TA260 003:006.101 - 0.003ms returns 0
TA260 003:006.105 JLINK_WriteReg(R5, 0x00000000)
TA260 003:006.109 - 0.003ms returns 0
TA260 003:006.113 JLINK_WriteReg(R6, 0x00000000)
TA260 003:006.116 - 0.003ms returns 0
TA260 003:006.120 JLINK_WriteReg(R7, 0x00000000)
TA260 003:006.124 - 0.003ms returns 0
TA260 003:006.128 JLINK_WriteReg(R8, 0x00000000)
TA260 003:006.132 - 0.003ms returns 0
TA260 003:006.136 JLINK_WriteReg(R9, 0x20000180)
TA260 003:006.139 - 0.003ms returns 0
TA260 003:006.143 JLINK_WriteReg(R10, 0x00000000)
TA260 003:006.146 - 0.003ms returns 0
TA260 003:006.151 JLINK_WriteReg(R11, 0x00000000)
TA260 003:006.154 - 0.003ms returns 0
TA260 003:006.158 JLINK_WriteReg(R12, 0x00000000)
TA260 003:006.162 - 0.003ms returns 0
TA260 003:006.166 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:006.170 - 0.004ms returns 0
TA260 003:006.174 JLINK_WriteReg(R14, 0x20000001)
TA260 003:006.177 - 0.003ms returns 0
TA260 003:006.182 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:006.185 - 0.003ms returns 0
TA260 003:006.189 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:006.193 - 0.003ms returns 0
TA260 003:006.197 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:006.200 - 0.003ms returns 0
TA260 003:006.204 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:006.207 - 0.003ms returns 0
TA260 003:006.212 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:006.215 - 0.003ms returns 0
TA260 003:006.220 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:006.225 - 0.005ms returns 0x00000038
TA260 003:006.229 JLINK_Go()
TA260 003:006.238   CPU_ReadMem(4 bytes @ 0x********)
TA260 003:009.044 - 2.814ms 
TA260 003:009.056 JLINK_IsHalted()
TA260 003:009.556 - 0.500ms returns FALSE
TA260 003:009.563 JLINK_HasError()
TA260 003:011.432 JLINK_IsHalted()
TA260 003:011.963 - 0.530ms returns FALSE
TA260 003:011.968 JLINK_HasError()
TA260 003:013.940 JLINK_IsHalted()
TA260 003:016.312   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:016.824 - 2.884ms returns TRUE
TA260 003:016.833 JLINK_ReadReg(R15 (PC))
TA260 003:016.839 - 0.005ms returns 0x20000000
TA260 003:016.844 JLINK_ClrBPEx(BPHandle = 0x00000038)
TA260 003:016.847 - 0.004ms returns 0x00
TA260 003:016.852 JLINK_ReadReg(R0)
TA260 003:016.856 - 0.003ms returns 0x00000000
TA260 003:017.411 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:017.426   Data:  43 0C 38 BF 71 F0 31 BF 55 C6 37 BF AA 38 32 BF ...
TA260 003:017.439   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:020.024 - 2.613ms returns 0x27C
TA260 003:020.084 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:020.088   Data:  2A E2 46 BF D2 E1 20 BF 67 21 47 BF 93 93 20 BF ...
TA260 003:020.102   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:022.004 - 1.919ms returns 0x184
TA260 003:022.020 JLINK_HasError()
TA260 003:022.561 JLINK_WriteReg(R0, 0x0800B400)
TA260 003:022.570 - 0.009ms returns 0
TA260 003:022.576 JLINK_WriteReg(R1, 0x00000400)
TA260 003:022.579 - 0.003ms returns 0
TA260 003:022.583 JLINK_WriteReg(R2, 0x20000184)
TA260 003:022.587 - 0.003ms returns 0
TA260 003:022.591 JLINK_WriteReg(R3, 0x00000000)
TA260 003:022.595 - 0.003ms returns 0
TA260 003:022.599 JLINK_WriteReg(R4, 0x00000000)
TA260 003:022.602 - 0.003ms returns 0
TA260 003:022.606 JLINK_WriteReg(R5, 0x00000000)
TA260 003:022.610 - 0.003ms returns 0
TA260 003:022.614 JLINK_WriteReg(R6, 0x00000000)
TA260 003:022.618 - 0.003ms returns 0
TA260 003:022.622 JLINK_WriteReg(R7, 0x00000000)
TA260 003:022.626 - 0.003ms returns 0
TA260 003:022.630 JLINK_WriteReg(R8, 0x00000000)
TA260 003:022.633 - 0.003ms returns 0
TA260 003:022.638 JLINK_WriteReg(R9, 0x20000180)
TA260 003:022.641 - 0.003ms returns 0
TA260 003:022.645 JLINK_WriteReg(R10, 0x00000000)
TA260 003:022.649 - 0.003ms returns 0
TA260 003:022.653 JLINK_WriteReg(R11, 0x00000000)
TA260 003:022.656 - 0.003ms returns 0
TA260 003:022.660 JLINK_WriteReg(R12, 0x00000000)
TA260 003:022.664 - 0.003ms returns 0
TA260 003:022.668 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:022.672 - 0.004ms returns 0
TA260 003:022.677 JLINK_WriteReg(R14, 0x20000001)
TA260 003:022.680 - 0.003ms returns 0
TA260 003:022.685 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:022.688 - 0.004ms returns 0
TA260 003:022.693 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:022.696 - 0.003ms returns 0
TA260 003:022.700 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:022.704 - 0.003ms returns 0
TA260 003:022.708 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:022.711 - 0.003ms returns 0
TA260 003:022.715 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:022.719 - 0.004ms returns 0
TA260 003:022.724 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:022.729 - 0.005ms returns 0x00000039
TA260 003:022.733 JLINK_Go()
TA260 003:022.742   CPU_ReadMem(4 bytes @ 0x********)
TA260 003:025.502 - 2.768ms 
TA260 003:025.522 JLINK_IsHalted()
TA260 003:025.987 - 0.465ms returns FALSE
TA260 003:025.994 JLINK_HasError()
TA260 003:027.957 JLINK_IsHalted()
TA260 003:028.404 - 0.447ms returns FALSE
TA260 003:028.412 JLINK_HasError()
TA260 003:029.953 JLINK_IsHalted()
TA260 003:032.303   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:032.778 - 2.824ms returns TRUE
TA260 003:032.796 JLINK_ReadReg(R15 (PC))
TA260 003:032.802 - 0.006ms returns 0x20000000
TA260 003:032.807 JLINK_ClrBPEx(BPHandle = 0x00000039)
TA260 003:032.811 - 0.004ms returns 0x00
TA260 003:032.816 JLINK_ReadReg(R0)
TA260 003:032.819 - 0.003ms returns 0x00000000
TA260 003:033.896 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:033.912   Data:  19 CC 11 BF 18 6D 52 BF 6B 79 11 BF 49 A6 52 BF ...
TA260 003:033.925   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:036.561 - 2.663ms returns 0x27C
TA260 003:036.581 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:036.589   Data:  10 82 62 BF 80 E2 ED BE D7 B0 62 BF 63 30 ED BE ...
TA260 003:036.603   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:038.502 - 1.920ms returns 0x184
TA260 003:038.511 JLINK_HasError()
TA260 003:038.516 JLINK_WriteReg(R0, 0x0800B800)
TA260 003:038.522 - 0.005ms returns 0
TA260 003:038.526 JLINK_WriteReg(R1, 0x00000400)
TA260 003:038.529 - 0.003ms returns 0
TA260 003:038.534 JLINK_WriteReg(R2, 0x20000184)
TA260 003:038.537 - 0.003ms returns 0
TA260 003:038.541 JLINK_WriteReg(R3, 0x00000000)
TA260 003:038.545 - 0.003ms returns 0
TA260 003:038.549 JLINK_WriteReg(R4, 0x00000000)
TA260 003:038.552 - 0.003ms returns 0
TA260 003:038.556 JLINK_WriteReg(R5, 0x00000000)
TA260 003:038.560 - 0.003ms returns 0
TA260 003:038.564 JLINK_WriteReg(R6, 0x00000000)
TA260 003:038.567 - 0.003ms returns 0
TA260 003:038.617 JLINK_WriteReg(R7, 0x00000000)
TA260 003:038.621 - 0.003ms returns 0
TA260 003:038.625 JLINK_WriteReg(R8, 0x00000000)
TA260 003:038.628 - 0.003ms returns 0
TA260 003:038.632 JLINK_WriteReg(R9, 0x20000180)
TA260 003:038.636 - 0.003ms returns 0
TA260 003:038.640 JLINK_WriteReg(R10, 0x00000000)
TA260 003:038.643 - 0.003ms returns 0
TA260 003:038.647 JLINK_WriteReg(R11, 0x00000000)
TA260 003:038.651 - 0.003ms returns 0
TA260 003:038.655 JLINK_WriteReg(R12, 0x00000000)
TA260 003:038.658 - 0.003ms returns 0
TA260 003:038.662 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:038.678 - 0.004ms returns 0
TA260 003:038.683 JLINK_WriteReg(R14, 0x20000001)
TA260 003:038.686 - 0.003ms returns 0
TA260 003:038.690 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:038.694 - 0.003ms returns 0
TA260 003:038.698 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:038.702 - 0.003ms returns 0
TA260 003:038.706 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:038.709 - 0.003ms returns 0
TA260 003:038.713 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:038.717 - 0.003ms returns 0
TA260 003:038.721 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:038.724 - 0.003ms returns 0
TA260 003:038.729 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:038.733 - 0.004ms returns 0x0000003A
TA260 003:038.737 JLINK_Go()
TA260 003:038.746   CPU_ReadMem(4 bytes @ 0x********)
TA260 003:041.513 - 2.775ms 
TA260 003:041.522 JLINK_IsHalted()
TA260 003:042.021 - 0.498ms returns FALSE
TA260 003:042.028 JLINK_HasError()
TA260 003:043.975 JLINK_IsHalted()
TA260 003:044.515 - 0.540ms returns FALSE
TA260 003:044.523 JLINK_HasError()
TA260 003:045.974 JLINK_IsHalted()
TA260 003:048.347   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:048.845 - 2.870ms returns TRUE
TA260 003:048.859 JLINK_ReadReg(R15 (PC))
TA260 003:048.864 - 0.005ms returns 0x20000000
TA260 003:048.911 JLINK_ClrBPEx(BPHandle = 0x0000003A)
TA260 003:048.917 - 0.006ms returns 0x00
TA260 003:048.921 JLINK_ReadReg(R0)
TA260 003:048.925 - 0.003ms returns 0x00000000
TA260 003:049.330 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:049.338   Data:  2C E3 CB BE 95 D3 6A BF AE 2A CB BE 8C FB 6A BF ...
TA260 003:049.349   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:051.962 - 2.632ms returns 0x27C
TA260 003:051.978 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:051.982   Data:  97 6D 75 BF 10 DD 90 BE 1C 8A 75 BF 2C 1C 90 BE ...
TA260 003:051.992   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:053.886 - 1.907ms returns 0x184
TA260 003:053.906 JLINK_HasError()
TA260 003:053.912 JLINK_WriteReg(R0, 0x0800BC00)
TA260 003:053.919 - 0.007ms returns 0
TA260 003:053.923 JLINK_WriteReg(R1, 0x00000400)
TA260 003:053.926 - 0.003ms returns 0
TA260 003:053.931 JLINK_WriteReg(R2, 0x20000184)
TA260 003:053.934 - 0.003ms returns 0
TA260 003:053.938 JLINK_WriteReg(R3, 0x00000000)
TA260 003:053.942 - 0.003ms returns 0
TA260 003:053.946 JLINK_WriteReg(R4, 0x00000000)
TA260 003:053.949 - 0.003ms returns 0
TA260 003:053.953 JLINK_WriteReg(R5, 0x00000000)
TA260 003:053.957 - 0.003ms returns 0
TA260 003:053.961 JLINK_WriteReg(R6, 0x00000000)
TA260 003:053.964 - 0.003ms returns 0
TA260 003:053.968 JLINK_WriteReg(R7, 0x00000000)
TA260 003:053.972 - 0.003ms returns 0
TA260 003:053.976 JLINK_WriteReg(R8, 0x00000000)
TA260 003:053.979 - 0.003ms returns 0
TA260 003:053.983 JLINK_WriteReg(R9, 0x20000180)
TA260 003:053.987 - 0.003ms returns 0
TA260 003:053.991 JLINK_WriteReg(R10, 0x00000000)
TA260 003:053.994 - 0.003ms returns 0
TA260 003:053.998 JLINK_WriteReg(R11, 0x00000000)
TA260 003:054.002 - 0.003ms returns 0
TA260 003:054.006 JLINK_WriteReg(R12, 0x00000000)
TA260 003:054.009 - 0.003ms returns 0
TA260 003:054.014 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:054.018 - 0.004ms returns 0
TA260 003:054.022 JLINK_WriteReg(R14, 0x20000001)
TA260 003:054.025 - 0.003ms returns 0
TA260 003:054.029 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:054.033 - 0.003ms returns 0
TA260 003:054.037 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:054.098 - 0.061ms returns 0
TA260 003:054.102 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:054.106 - 0.003ms returns 0
TA260 003:054.110 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:054.113 - 0.003ms returns 0
TA260 003:054.118 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:054.121 - 0.003ms returns 0
TA260 003:054.126 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:054.131 - 0.005ms returns 0x0000003B
TA260 003:054.135 JLINK_Go()
TA260 003:054.144   CPU_ReadMem(4 bytes @ 0x********)
TA260 003:056.825 - 2.689ms 
TA260 003:056.843 JLINK_IsHalted()
TA260 003:057.317 - 0.473ms returns FALSE
TA260 003:057.322 JLINK_HasError()
TA260 003:058.584 JLINK_IsHalted()
TA260 003:059.044 - 0.459ms returns FALSE
TA260 003:059.055 JLINK_HasError()
TA260 003:060.583 JLINK_IsHalted()
TA260 003:061.068 - 0.484ms returns FALSE
TA260 003:061.075 JLINK_HasError()
TA260 003:063.084 JLINK_IsHalted()
TA260 003:065.459   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:066.013 - 2.928ms returns TRUE
TA260 003:066.021 JLINK_ReadReg(R15 (PC))
TA260 003:066.027 - 0.005ms returns 0x20000000
TA260 003:066.032 JLINK_ClrBPEx(BPHandle = 0x0000003B)
TA260 003:066.036 - 0.004ms returns 0x00
TA260 003:066.040 JLINK_ReadReg(R0)
TA260 003:066.044 - 0.003ms returns 0x00000000
TA260 003:066.431 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:066.439   Data:  A4 B0 58 BE DD 33 7A BF 8F 27 57 BE 10 49 7A BF ...
TA260 003:066.449   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:069.069 - 2.637ms returns 0x27C
TA260 003:069.081 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:069.086   Data:  9D EA 7E BF DD 19 B9 BD C7 F3 7E BF DA F8 B5 BD ...
TA260 003:069.095   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:070.953 - 1.871ms returns 0x184
TA260 003:070.961 JLINK_HasError()
TA260 003:070.966 JLINK_WriteReg(R0, 0x0800C000)
TA260 003:070.971 - 0.004ms returns 0
TA260 003:070.975 JLINK_WriteReg(R1, 0x00000400)
TA260 003:070.979 - 0.003ms returns 0
TA260 003:070.983 JLINK_WriteReg(R2, 0x20000184)
TA260 003:070.986 - 0.003ms returns 0
TA260 003:070.990 JLINK_WriteReg(R3, 0x00000000)
TA260 003:070.994 - 0.003ms returns 0
TA260 003:070.998 JLINK_WriteReg(R4, 0x00000000)
TA260 003:071.001 - 0.003ms returns 0
TA260 003:071.005 JLINK_WriteReg(R5, 0x00000000)
TA260 003:071.009 - 0.003ms returns 0
TA260 003:071.013 JLINK_WriteReg(R6, 0x00000000)
TA260 003:071.016 - 0.003ms returns 0
TA260 003:071.020 JLINK_WriteReg(R7, 0x00000000)
TA260 003:071.024 - 0.003ms returns 0
TA260 003:071.028 JLINK_WriteReg(R8, 0x00000000)
TA260 003:071.031 - 0.003ms returns 0
TA260 003:071.035 JLINK_WriteReg(R9, 0x20000180)
TA260 003:071.039 - 0.003ms returns 0
TA260 003:071.043 JLINK_WriteReg(R10, 0x00000000)
TA260 003:071.046 - 0.003ms returns 0
TA260 003:071.050 JLINK_WriteReg(R11, 0x00000000)
TA260 003:071.054 - 0.003ms returns 0
TA260 003:071.058 JLINK_WriteReg(R12, 0x00000000)
TA260 003:071.061 - 0.003ms returns 0
TA260 003:071.065 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:071.069 - 0.003ms returns 0
TA260 003:071.073 JLINK_WriteReg(R14, 0x20000001)
TA260 003:071.077 - 0.003ms returns 0
TA260 003:071.081 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:071.084 - 0.003ms returns 0
TA260 003:071.089 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:071.092 - 0.003ms returns 0
TA260 003:071.096 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:071.100 - 0.003ms returns 0
TA260 003:071.104 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:071.107 - 0.003ms returns 0
TA260 003:071.111 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:071.114 - 0.003ms returns 0
TA260 003:071.119 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:071.123 - 0.004ms returns 0x0000003C
TA260 003:071.127 JLINK_Go()
TA260 003:071.135   CPU_ReadMem(4 bytes @ 0x********)
TA260 003:074.057 - 2.929ms 
TA260 003:074.114 JLINK_IsHalted()
TA260 003:074.588 - 0.474ms returns FALSE
TA260 003:074.595 JLINK_HasError()
TA260 003:076.592 JLINK_IsHalted()
TA260 003:077.073 - 0.480ms returns FALSE
TA260 003:077.081 JLINK_HasError()
TA260 003:078.597 JLINK_IsHalted()
TA260 003:080.900   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:081.402 - 2.805ms returns TRUE
TA260 003:081.411 JLINK_ReadReg(R15 (PC))
TA260 003:081.417 - 0.005ms returns 0x20000000
TA260 003:081.422 JLINK_ClrBPEx(BPHandle = 0x0000003C)
TA260 003:081.425 - 0.003ms returns 0x00
TA260 003:081.430 JLINK_ReadReg(R0)
TA260 003:081.434 - 0.003ms returns 0x00000000
TA260 003:081.820 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:081.828   Data:  38 39 8A BC AC F6 7F BF 4B 51 7B BC 4A F8 7F BF ...
TA260 003:081.840   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:084.469 - 2.648ms returns 0x27C
TA260 003:084.493 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:084.497   Data:  C9 9B 7E BF 89 5D D8 3D 3D 91 7E BF 37 7D DB 3D ...
TA260 003:084.509   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:086.420 - 1.926ms returns 0x184
TA260 003:086.427 JLINK_HasError()
TA260 003:086.432 JLINK_WriteReg(R0, 0x0800C400)
TA260 003:086.437 - 0.005ms returns 0
TA260 003:086.441 JLINK_WriteReg(R1, 0x00000400)
TA260 003:086.445 - 0.003ms returns 0
TA260 003:086.449 JLINK_WriteReg(R2, 0x20000184)
TA260 003:086.453 - 0.004ms returns 0
TA260 003:086.457 JLINK_WriteReg(R3, 0x00000000)
TA260 003:086.460 - 0.003ms returns 0
TA260 003:086.464 JLINK_WriteReg(R4, 0x00000000)
TA260 003:086.468 - 0.003ms returns 0
TA260 003:086.472 JLINK_WriteReg(R5, 0x00000000)
TA260 003:086.476 - 0.003ms returns 0
TA260 003:086.483 JLINK_WriteReg(R6, 0x00000000)
TA260 003:086.486 - 0.003ms returns 0
TA260 003:086.490 JLINK_WriteReg(R7, 0x00000000)
TA260 003:086.494 - 0.003ms returns 0
TA260 003:086.498 JLINK_WriteReg(R8, 0x00000000)
TA260 003:086.501 - 0.003ms returns 0
TA260 003:086.505 JLINK_WriteReg(R9, 0x20000180)
TA260 003:086.508 - 0.003ms returns 0
TA260 003:086.512 JLINK_WriteReg(R10, 0x00000000)
TA260 003:086.516 - 0.003ms returns 0
TA260 003:086.520 JLINK_WriteReg(R11, 0x00000000)
TA260 003:086.524 - 0.003ms returns 0
TA260 003:086.528 JLINK_WriteReg(R12, 0x00000000)
TA260 003:086.531 - 0.003ms returns 0
TA260 003:086.535 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:086.540 - 0.004ms returns 0
TA260 003:086.544 JLINK_WriteReg(R14, 0x20000001)
TA260 003:086.547 - 0.003ms returns 0
TA260 003:086.551 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:086.554 - 0.003ms returns 0
TA260 003:086.558 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:086.562 - 0.003ms returns 0
TA260 003:086.566 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:086.570 - 0.003ms returns 0
TA260 003:086.574 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:086.577 - 0.003ms returns 0
TA260 003:086.581 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:086.585 - 0.003ms returns 0
TA260 003:086.589 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:086.594 - 0.004ms returns 0x0000003D
TA260 003:086.598 JLINK_Go()
TA260 003:086.606   CPU_ReadMem(4 bytes @ 0x********)
TA260 003:089.318 - 2.720ms 
TA260 003:089.327 JLINK_IsHalted()
TA260 003:089.796 - 0.468ms returns FALSE
TA260 003:089.803 JLINK_HasError()
TA260 003:091.976 JLINK_IsHalted()
TA260 003:092.463 - 0.486ms returns FALSE
TA260 003:092.471 JLINK_HasError()
TA260 003:093.987 JLINK_IsHalted()
TA260 003:096.366   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:096.883 - 2.895ms returns TRUE
TA260 003:096.893 JLINK_ReadReg(R15 (PC))
TA260 003:096.900 - 0.006ms returns 0x20000000
TA260 003:096.905 JLINK_ClrBPEx(BPHandle = 0x0000003D)
TA260 003:096.909 - 0.004ms returns 0x00
TA260 003:096.914 JLINK_ReadReg(R0)
TA260 003:096.918 - 0.003ms returns 0x00000000
TA260 003:097.312 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:097.320   Data:  50 CC 36 3E 53 E3 7B BF EC 57 38 3E 4D D1 7B BF ...
TA260 003:097.332   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:099.958 - 2.645ms returns 0x27C
TA260 003:099.976 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:099.981   Data:  22 84 74 BF 16 61 98 3E 49 66 74 BF FE 20 99 3E ...
TA260 003:099.991   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:101.880 - 1.903ms returns 0x184
TA260 003:101.892 JLINK_HasError()
TA260 003:101.903 JLINK_WriteReg(R0, 0x0800C800)
TA260 003:101.908 - 0.005ms returns 0
TA260 003:101.946 JLINK_WriteReg(R1, 0x00000400)
TA260 003:101.950 - 0.005ms returns 0
TA260 003:101.955 JLINK_WriteReg(R2, 0x20000184)
TA260 003:101.958 - 0.003ms returns 0
TA260 003:101.962 JLINK_WriteReg(R3, 0x00000000)
TA260 003:101.966 - 0.003ms returns 0
TA260 003:101.970 JLINK_WriteReg(R4, 0x00000000)
TA260 003:101.973 - 0.003ms returns 0
TA260 003:101.977 JLINK_WriteReg(R5, 0x00000000)
TA260 003:101.981 - 0.003ms returns 0
TA260 003:101.985 JLINK_WriteReg(R6, 0x00000000)
TA260 003:101.990 - 0.004ms returns 0
TA260 003:101.994 JLINK_WriteReg(R7, 0x00000000)
TA260 003:101.997 - 0.003ms returns 0
TA260 003:102.002 JLINK_WriteReg(R8, 0x00000000)
TA260 003:102.005 - 0.003ms returns 0
TA260 003:102.009 JLINK_WriteReg(R9, 0x20000180)
TA260 003:102.013 - 0.003ms returns 0
TA260 003:102.017 JLINK_WriteReg(R10, 0x00000000)
TA260 003:102.020 - 0.003ms returns 0
TA260 003:102.024 JLINK_WriteReg(R11, 0x00000000)
TA260 003:102.028 - 0.003ms returns 0
TA260 003:102.032 JLINK_WriteReg(R12, 0x00000000)
TA260 003:102.035 - 0.003ms returns 0
TA260 003:102.040 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:102.043 - 0.004ms returns 0
TA260 003:102.047 JLINK_WriteReg(R14, 0x20000001)
TA260 003:102.051 - 0.003ms returns 0
TA260 003:102.059 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:102.063 - 0.004ms returns 0
TA260 003:102.067 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:102.071 - 0.003ms returns 0
TA260 003:102.075 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:102.078 - 0.003ms returns 0
TA260 003:102.082 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:102.087 - 0.004ms returns 0
TA260 003:102.092 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:102.096 - 0.003ms returns 0
TA260 003:102.100 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:102.105 - 0.004ms returns 0x0000003E
TA260 003:102.109 JLINK_Go()
TA260 003:102.117   CPU_ReadMem(4 bytes @ 0x********)
TA260 003:104.838 - 2.728ms 
TA260 003:104.857 JLINK_IsHalted()
TA260 003:105.317 - 0.459ms returns FALSE
TA260 003:105.324 JLINK_HasError()
TA260 003:106.500 JLINK_IsHalted()
TA260 003:107.002 - 0.502ms returns FALSE
TA260 003:107.010 JLINK_HasError()
TA260 003:108.489 JLINK_IsHalted()
TA260 003:109.023 - 0.533ms returns FALSE
TA260 003:109.032 JLINK_HasError()
TA260 003:110.499 JLINK_IsHalted()
TA260 003:112.813   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:113.390 - 2.890ms returns TRUE
TA260 003:113.403 JLINK_ReadReg(R15 (PC))
TA260 003:113.410 - 0.006ms returns 0x20000000
TA260 003:113.414 JLINK_ClrBPEx(BPHandle = 0x0000003E)
TA260 003:113.418 - 0.003ms returns 0x00
TA260 003:113.423 JLINK_ReadReg(R0)
TA260 003:113.427 - 0.003ms returns 0x00000000
TA260 003:113.931 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:113.942   Data:  B6 EC BB 3E EB 21 6E BF AF A7 BC 3E F2 FC 6D BF ...
TA260 003:113.954   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:116.570 - 2.638ms returns 0x27C
TA260 003:116.586 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:116.590   Data:  F2 06 61 BF B1 CF F4 3E F2 D6 60 BF 35 80 F5 3E ...
TA260 003:116.604   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:118.469 - 1.882ms returns 0x184
TA260 003:118.480 JLINK_HasError()
TA260 003:118.486 JLINK_WriteReg(R0, 0x0800CC00)
TA260 003:118.492 - 0.006ms returns 0
TA260 003:118.496 JLINK_WriteReg(R1, 0x00000400)
TA260 003:118.500 - 0.003ms returns 0
TA260 003:118.504 JLINK_WriteReg(R2, 0x20000184)
TA260 003:118.508 - 0.003ms returns 0
TA260 003:118.512 JLINK_WriteReg(R3, 0x00000000)
TA260 003:118.516 - 0.003ms returns 0
TA260 003:118.520 JLINK_WriteReg(R4, 0x00000000)
TA260 003:118.523 - 0.003ms returns 0
TA260 003:118.527 JLINK_WriteReg(R5, 0x00000000)
TA260 003:118.531 - 0.003ms returns 0
TA260 003:118.535 JLINK_WriteReg(R6, 0x00000000)
TA260 003:118.539 - 0.003ms returns 0
TA260 003:118.543 JLINK_WriteReg(R7, 0x00000000)
TA260 003:118.546 - 0.003ms returns 0
TA260 003:118.550 JLINK_WriteReg(R8, 0x00000000)
TA260 003:118.557 - 0.007ms returns 0
TA260 003:118.561 JLINK_WriteReg(R9, 0x20000180)
TA260 003:118.565 - 0.003ms returns 0
TA260 003:118.569 JLINK_WriteReg(R10, 0x00000000)
TA260 003:118.573 - 0.003ms returns 0
TA260 003:118.577 JLINK_WriteReg(R11, 0x00000000)
TA260 003:118.581 - 0.003ms returns 0
TA260 003:118.604 JLINK_WriteReg(R12, 0x00000000)
TA260 003:118.609 - 0.004ms returns 0
TA260 003:118.613 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:118.617 - 0.004ms returns 0
TA260 003:118.621 JLINK_WriteReg(R14, 0x20000001)
TA260 003:118.624 - 0.003ms returns 0
TA260 003:118.628 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:118.632 - 0.003ms returns 0
TA260 003:118.636 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:118.640 - 0.004ms returns 0
TA260 003:118.644 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:118.648 - 0.003ms returns 0
TA260 003:118.652 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:118.656 - 0.003ms returns 0
TA260 003:118.660 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:118.663 - 0.003ms returns 0
TA260 003:118.668 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:118.673 - 0.004ms returns 0x0000003F
TA260 003:118.677 JLINK_Go()
TA260 003:118.686   CPU_ReadMem(4 bytes @ 0x********)
TA260 003:121.407 - 2.729ms 
TA260 003:121.423 JLINK_IsHalted()
TA260 003:121.917 - 0.494ms returns FALSE
TA260 003:121.924 JLINK_HasError()
TA260 003:124.513 JLINK_IsHalted()
TA260 003:124.996 - 0.482ms returns FALSE
TA260 003:125.013 JLINK_HasError()
TA260 003:126.883 JLINK_IsHalted()
TA260 003:129.159   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:129.705 - 2.822ms returns TRUE
TA260 003:129.715 JLINK_ReadReg(R15 (PC))
TA260 003:129.721 - 0.005ms returns 0x20000000
TA260 003:129.726 JLINK_ClrBPEx(BPHandle = 0x0000003F)
TA260 003:129.730 - 0.004ms returns 0x00
TA260 003:129.735 JLINK_ReadReg(R0)
TA260 003:129.738 - 0.003ms returns 0x00000000
TA260 003:130.258 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:130.270   Data:  3D 9D 0A 3F C7 39 57 BF B7 F1 0A 3F 48 03 57 BF ...
TA260 003:130.283   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:132.887 - 2.628ms returns 0x27C
TA260 003:132.900 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:132.903   Data:  F5 E3 44 BF EE EA 23 3F A5 A3 44 BF 1A 38 24 3F ...
TA260 003:132.912   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:134.799 - 1.898ms returns 0x184
TA260 003:134.824 JLINK_HasError()
TA260 003:134.830 JLINK_WriteReg(R0, 0x0800D000)
TA260 003:134.837 - 0.006ms returns 0
TA260 003:134.841 JLINK_WriteReg(R1, 0x00000400)
TA260 003:134.844 - 0.003ms returns 0
TA260 003:134.848 JLINK_WriteReg(R2, 0x20000184)
TA260 003:134.852 - 0.003ms returns 0
TA260 003:134.856 JLINK_WriteReg(R3, 0x00000000)
TA260 003:134.859 - 0.003ms returns 0
TA260 003:134.863 JLINK_WriteReg(R4, 0x00000000)
TA260 003:134.867 - 0.003ms returns 0
TA260 003:134.871 JLINK_WriteReg(R5, 0x00000000)
TA260 003:134.874 - 0.003ms returns 0
TA260 003:134.878 JLINK_WriteReg(R6, 0x00000000)
TA260 003:134.882 - 0.003ms returns 0
TA260 003:134.886 JLINK_WriteReg(R7, 0x00000000)
TA260 003:134.889 - 0.003ms returns 0
TA260 003:134.893 JLINK_WriteReg(R8, 0x00000000)
TA260 003:134.896 - 0.003ms returns 0
TA260 003:134.900 JLINK_WriteReg(R9, 0x20000180)
TA260 003:134.904 - 0.003ms returns 0
TA260 003:134.908 JLINK_WriteReg(R10, 0x00000000)
TA260 003:134.912 - 0.003ms returns 0
TA260 003:134.916 JLINK_WriteReg(R11, 0x00000000)
TA260 003:134.920 - 0.004ms returns 0
TA260 003:134.924 JLINK_WriteReg(R12, 0x00000000)
TA260 003:134.928 - 0.003ms returns 0
TA260 003:134.932 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:134.936 - 0.004ms returns 0
TA260 003:134.940 JLINK_WriteReg(R14, 0x20000001)
TA260 003:134.944 - 0.003ms returns 0
TA260 003:134.948 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:134.951 - 0.003ms returns 0
TA260 003:134.955 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:134.959 - 0.003ms returns 0
TA260 003:134.963 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:134.966 - 0.003ms returns 0
TA260 003:134.970 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:134.977 - 0.007ms returns 0
TA260 003:134.981 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:134.985 - 0.003ms returns 0
TA260 003:134.989 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:134.994 - 0.004ms returns 0x00000040
TA260 003:134.998 JLINK_Go()
TA260 003:135.007   CPU_ReadMem(4 bytes @ 0x********)
TA260 003:137.699 - 2.700ms 
TA260 003:137.706 JLINK_IsHalted()
TA260 003:138.270 - 0.563ms returns FALSE
TA260 003:138.276 JLINK_HasError()
TA260 003:139.410 JLINK_IsHalted()
TA260 003:139.939 - 0.529ms returns FALSE
TA260 003:139.946 JLINK_HasError()
TA260 003:141.410 JLINK_IsHalted()
TA260 003:141.898 - 0.487ms returns FALSE
TA260 003:141.905 JLINK_HasError()
TA260 003:143.927 JLINK_IsHalted()
TA260 003:146.199   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:146.673 - 2.745ms returns TRUE
TA260 003:146.682 JLINK_ReadReg(R15 (PC))
TA260 003:146.688 - 0.006ms returns 0x20000000
TA260 003:146.692 JLINK_ClrBPEx(BPHandle = 0x00000040)
TA260 003:146.696 - 0.004ms returns 0x00
TA260 003:146.701 JLINK_ReadReg(R0)
TA260 003:146.704 - 0.003ms returns 0x00000000
TA260 003:147.129 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:147.141   Data:  71 F0 31 3F 43 0C 38 BF AA 38 32 3F 55 C6 37 BF ...
TA260 003:147.153   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:149.792 - 2.663ms returns 0x27C
TA260 003:149.802 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:149.806   Data:  F9 2F 21 BF 67 21 47 3F D2 E1 20 BF 85 60 47 3F ...
TA260 003:149.815   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:151.639 - 1.836ms returns 0x184
TA260 003:151.651 JLINK_HasError()
TA260 003:151.657 JLINK_WriteReg(R0, 0x0800D400)
TA260 003:151.662 - 0.005ms returns 0
TA260 003:151.666 JLINK_WriteReg(R1, 0x00000400)
TA260 003:151.670 - 0.003ms returns 0
TA260 003:151.674 JLINK_WriteReg(R2, 0x20000184)
TA260 003:151.677 - 0.003ms returns 0
TA260 003:151.682 JLINK_WriteReg(R3, 0x00000000)
TA260 003:151.685 - 0.003ms returns 0
TA260 003:151.689 JLINK_WriteReg(R4, 0x00000000)
TA260 003:151.693 - 0.003ms returns 0
TA260 003:151.697 JLINK_WriteReg(R5, 0x00000000)
TA260 003:151.700 - 0.003ms returns 0
TA260 003:151.704 JLINK_WriteReg(R6, 0x00000000)
TA260 003:151.708 - 0.003ms returns 0
TA260 003:151.712 JLINK_WriteReg(R7, 0x00000000)
TA260 003:151.715 - 0.003ms returns 0
TA260 003:151.719 JLINK_WriteReg(R8, 0x00000000)
TA260 003:151.723 - 0.003ms returns 0
TA260 003:151.727 JLINK_WriteReg(R9, 0x20000180)
TA260 003:151.731 - 0.003ms returns 0
TA260 003:151.735 JLINK_WriteReg(R10, 0x00000000)
TA260 003:151.738 - 0.003ms returns 0
TA260 003:151.743 JLINK_WriteReg(R11, 0x00000000)
TA260 003:151.746 - 0.004ms returns 0
TA260 003:151.751 JLINK_WriteReg(R12, 0x00000000)
TA260 003:151.754 - 0.003ms returns 0
TA260 003:151.758 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:151.763 - 0.004ms returns 0
TA260 003:151.767 JLINK_WriteReg(R14, 0x20000001)
TA260 003:151.771 - 0.003ms returns 0
TA260 003:151.775 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:151.778 - 0.003ms returns 0
TA260 003:151.783 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:151.786 - 0.003ms returns 0
TA260 003:151.790 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:151.794 - 0.003ms returns 0
TA260 003:151.798 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:151.801 - 0.003ms returns 0
TA260 003:151.805 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:151.809 - 0.003ms returns 0
TA260 003:151.814 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:151.818 - 0.005ms returns 0x00000041
TA260 003:151.823 JLINK_Go()
TA260 003:151.831   CPU_ReadMem(4 bytes @ 0x********)
TA260 003:154.550 - 2.726ms 
TA260 003:154.580 JLINK_IsHalted()
TA260 003:155.077 - 0.497ms returns FALSE
TA260 003:155.094 JLINK_HasError()
TA260 003:156.430 JLINK_IsHalted()
TA260 003:156.901 - 0.470ms returns FALSE
TA260 003:156.908 JLINK_HasError()
TA260 003:158.426 JLINK_IsHalted()
TA260 003:160.787   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:161.284 - 2.857ms returns TRUE
TA260 003:161.292 JLINK_ReadReg(R15 (PC))
TA260 003:161.301 - 0.009ms returns 0x20000000
TA260 003:161.307 JLINK_ClrBPEx(BPHandle = 0x00000041)
TA260 003:161.311 - 0.003ms returns 0x00
TA260 003:161.315 JLINK_ReadReg(R0)
TA260 003:161.319 - 0.003ms returns 0x00000000
TA260 003:161.697 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:161.706   Data:  18 6D 52 3F 19 CC 11 BF 49 A6 52 3F 6B 79 11 BF ...
TA260 003:161.718   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:164.294 - 2.596ms returns 0x27C
TA260 003:164.315 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:164.319   Data:  79 94 EE BE D7 B0 62 3F 80 E2 ED BE 7B DF 62 3F ...
TA260 003:164.341   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:166.292 - 1.977ms returns 0x184
TA260 003:166.314 JLINK_HasError()
TA260 003:166.320 JLINK_WriteReg(R0, 0x0800D800)
TA260 003:166.327 - 0.007ms returns 0
TA260 003:166.331 JLINK_WriteReg(R1, 0x00000400)
TA260 003:166.335 - 0.003ms returns 0
TA260 003:166.339 JLINK_WriteReg(R2, 0x20000184)
TA260 003:166.342 - 0.003ms returns 0
TA260 003:166.346 JLINK_WriteReg(R3, 0x00000000)
TA260 003:166.350 - 0.003ms returns 0
TA260 003:166.354 JLINK_WriteReg(R4, 0x00000000)
TA260 003:166.357 - 0.003ms returns 0
TA260 003:166.361 JLINK_WriteReg(R5, 0x00000000)
TA260 003:166.364 - 0.003ms returns 0
TA260 003:166.368 JLINK_WriteReg(R6, 0x00000000)
TA260 003:166.372 - 0.003ms returns 0
TA260 003:166.376 JLINK_WriteReg(R7, 0x00000000)
TA260 003:166.379 - 0.003ms returns 0
TA260 003:166.383 JLINK_WriteReg(R8, 0x00000000)
TA260 003:166.387 - 0.003ms returns 0
TA260 003:166.391 JLINK_WriteReg(R9, 0x20000180)
TA260 003:166.394 - 0.003ms returns 0
TA260 003:166.398 JLINK_WriteReg(R10, 0x00000000)
TA260 003:166.402 - 0.003ms returns 0
TA260 003:166.406 JLINK_WriteReg(R11, 0x00000000)
TA260 003:166.409 - 0.003ms returns 0
TA260 003:166.414 JLINK_WriteReg(R12, 0x00000000)
TA260 003:166.417 - 0.003ms returns 0
TA260 003:166.421 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:166.426 - 0.004ms returns 0
TA260 003:166.430 JLINK_WriteReg(R14, 0x20000001)
TA260 003:166.433 - 0.003ms returns 0
TA260 003:166.438 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:166.441 - 0.003ms returns 0
TA260 003:166.445 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:166.449 - 0.003ms returns 0
TA260 003:166.453 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:166.456 - 0.003ms returns 0
TA260 003:166.460 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:166.463 - 0.003ms returns 0
TA260 003:166.467 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:166.471 - 0.003ms returns 0
TA260 003:166.476 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:166.480 - 0.004ms returns 0x00000042
TA260 003:166.484 JLINK_Go()
TA260 003:166.493   CPU_ReadMem(4 bytes @ 0x********)
TA260 003:169.283 - 2.797ms 
TA260 003:169.298 JLINK_IsHalted()
TA260 003:169.792 - 0.494ms returns FALSE
TA260 003:169.799 JLINK_HasError()
TA260 003:170.938 JLINK_IsHalted()
TA260 003:171.453 - 0.514ms returns FALSE
TA260 003:171.467 JLINK_HasError()
TA260 003:175.453 JLINK_IsHalted()
TA260 003:177.788   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:178.275 - 2.821ms returns TRUE
TA260 003:178.284 JLINK_ReadReg(R15 (PC))
TA260 003:178.289 - 0.005ms returns 0x20000000
TA260 003:178.294 JLINK_ClrBPEx(BPHandle = 0x00000042)
TA260 003:178.298 - 0.003ms returns 0x00
TA260 003:178.303 JLINK_ReadReg(R0)
TA260 003:178.307 - 0.004ms returns 0x00000000
TA260 003:178.717 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:178.726   Data:  95 D3 6A 3F 2C E3 CB BE 8C FB 6A 3F AE 2A CB BE ...
TA260 003:178.736   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:181.384 - 2.666ms returns 0x27C
TA260 003:181.402 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:181.406   Data:  DD 9D 91 BE 1C 8A 75 3F 10 DD 90 BE 7B A6 75 3F ...
TA260 003:181.418   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:183.312 - 1.909ms returns 0x184
TA260 003:183.328 JLINK_HasError()
TA260 003:183.367 JLINK_WriteReg(R0, 0x0800DC00)
TA260 003:183.383 - 0.017ms returns 0
TA260 003:183.390 JLINK_WriteReg(R1, 0x00000400)
TA260 003:183.397 - 0.007ms returns 0
TA260 003:183.402 JLINK_WriteReg(R2, 0x20000184)
TA260 003:183.407 - 0.004ms returns 0
TA260 003:183.412 JLINK_WriteReg(R3, 0x00000000)
TA260 003:183.416 - 0.004ms returns 0
TA260 003:183.422 JLINK_WriteReg(R4, 0x00000000)
TA260 003:183.426 - 0.004ms returns 0
TA260 003:183.431 JLINK_WriteReg(R5, 0x00000000)
TA260 003:183.435 - 0.004ms returns 0
TA260 003:183.440 JLINK_WriteReg(R6, 0x00000000)
TA260 003:183.445 - 0.005ms returns 0
TA260 003:183.449 JLINK_WriteReg(R7, 0x00000000)
TA260 003:183.453 - 0.003ms returns 0
TA260 003:183.457 JLINK_WriteReg(R8, 0x00000000)
TA260 003:183.460 - 0.003ms returns 0
TA260 003:183.464 JLINK_WriteReg(R9, 0x20000180)
TA260 003:183.468 - 0.003ms returns 0
TA260 003:183.472 JLINK_WriteReg(R10, 0x00000000)
TA260 003:183.483 - 0.011ms returns 0
TA260 003:183.488 JLINK_WriteReg(R11, 0x00000000)
TA260 003:183.491 - 0.003ms returns 0
TA260 003:183.496 JLINK_WriteReg(R12, 0x00000000)
TA260 003:183.499 - 0.003ms returns 0
TA260 003:183.504 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:183.509 - 0.005ms returns 0
TA260 003:183.513 JLINK_WriteReg(R14, 0x20000001)
TA260 003:183.517 - 0.003ms returns 0
TA260 003:183.521 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:183.525 - 0.003ms returns 0
TA260 003:183.529 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:183.532 - 0.003ms returns 0
TA260 003:183.536 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:183.540 - 0.003ms returns 0
TA260 003:183.544 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:183.547 - 0.003ms returns 0
TA260 003:183.551 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:183.554 - 0.003ms returns 0
TA260 003:183.559 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:183.566 - 0.006ms returns 0x00000043
TA260 003:183.570 JLINK_Go()
TA260 003:183.582   CPU_ReadMem(4 bytes @ 0x********)
TA260 003:186.306 - 2.734ms 
TA260 003:186.323 JLINK_IsHalted()
TA260 003:186.786 - 0.462ms returns FALSE
TA260 003:186.799 JLINK_HasError()
TA260 003:188.382 JLINK_IsHalted()
TA260 003:188.879 - 0.496ms returns FALSE
TA260 003:188.890 JLINK_HasError()
TA260 003:190.384 JLINK_IsHalted()
TA260 003:192.779   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:193.298 - 2.913ms returns TRUE
TA260 003:193.320 JLINK_ReadReg(R15 (PC))
TA260 003:193.328 - 0.008ms returns 0x20000000
TA260 003:193.333 JLINK_ClrBPEx(BPHandle = 0x00000043)
TA260 003:193.338 - 0.004ms returns 0x00
TA260 003:193.342 JLINK_ReadReg(R0)
TA260 003:193.346 - 0.003ms returns 0x00000000
TA260 003:193.820 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:193.836   Data:  DD 33 7A 3F A4 B0 58 BE 10 49 7A 3F 8F 27 57 BE ...
TA260 003:193.853   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:196.526 - 2.706ms returns 0x27C
TA260 003:196.542 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:196.547   Data:  C3 3A BC BD C7 F3 7E 3F DD 19 B9 BD C9 FC 7E 3F ...
TA260 003:196.561   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:198.454 - 1.912ms returns 0x184
TA260 003:198.472 JLINK_HasError()
TA260 003:198.478 JLINK_WriteReg(R0, 0x0800E000)
TA260 003:198.483 - 0.005ms returns 0
TA260 003:198.488 JLINK_WriteReg(R1, 0x00000400)
TA260 003:198.492 - 0.003ms returns 0
TA260 003:198.496 JLINK_WriteReg(R2, 0x20000184)
TA260 003:198.499 - 0.003ms returns 0
TA260 003:198.503 JLINK_WriteReg(R3, 0x00000000)
TA260 003:198.507 - 0.003ms returns 0
TA260 003:198.512 JLINK_WriteReg(R4, 0x00000000)
TA260 003:198.515 - 0.003ms returns 0
TA260 003:198.520 JLINK_WriteReg(R5, 0x00000000)
TA260 003:198.524 - 0.004ms returns 0
TA260 003:198.528 JLINK_WriteReg(R6, 0x00000000)
TA260 003:198.531 - 0.003ms returns 0
TA260 003:198.535 JLINK_WriteReg(R7, 0x00000000)
TA260 003:198.539 - 0.003ms returns 0
TA260 003:198.543 JLINK_WriteReg(R8, 0x00000000)
TA260 003:198.546 - 0.003ms returns 0
TA260 003:198.551 JLINK_WriteReg(R9, 0x20000180)
TA260 003:198.554 - 0.003ms returns 0
TA260 003:198.558 JLINK_WriteReg(R10, 0x00000000)
TA260 003:198.562 - 0.003ms returns 0
TA260 003:198.566 JLINK_WriteReg(R11, 0x00000000)
TA260 003:198.573 - 0.007ms returns 0
TA260 003:198.578 JLINK_WriteReg(R12, 0x00000000)
TA260 003:198.581 - 0.003ms returns 0
TA260 003:198.585 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:198.590 - 0.004ms returns 0
TA260 003:198.594 JLINK_WriteReg(R14, 0x20000001)
TA260 003:198.597 - 0.003ms returns 0
TA260 003:198.602 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:198.605 - 0.003ms returns 0
TA260 003:198.610 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:198.613 - 0.003ms returns 0
TA260 003:198.617 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:198.621 - 0.003ms returns 0
TA260 003:198.626 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:198.629 - 0.003ms returns 0
TA260 003:198.634 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:198.638 - 0.004ms returns 0
TA260 003:198.642 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:198.647 - 0.004ms returns 0x00000044
TA260 003:198.651 JLINK_Go()
TA260 003:198.659   CPU_ReadMem(4 bytes @ 0x********)
TA260 003:201.399 - 2.747ms 
TA260 003:201.412 JLINK_IsHalted()
TA260 003:201.919 - 0.506ms returns FALSE
TA260 003:201.928 JLINK_HasError()
TA260 003:203.595 JLINK_IsHalted()
TA260 003:204.075 - 0.480ms returns FALSE
TA260 003:204.090 JLINK_HasError()
TA260 003:205.592 JLINK_IsHalted()
TA260 003:207.870   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:208.350 - 2.757ms returns TRUE
TA260 003:208.400 JLINK_ReadReg(R15 (PC))
TA260 003:208.407 - 0.006ms returns 0x20000000
TA260 003:208.412 JLINK_ClrBPEx(BPHandle = 0x00000044)
TA260 003:208.416 - 0.004ms returns 0x00
TA260 003:208.420 JLINK_ReadReg(R0)
TA260 003:208.424 - 0.003ms returns 0x00000000
TA260 003:208.888 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
TA260 003:208.897   Data:  AC F6 7F 3F 38 39 8A BC 4A F8 7F 3F 4B 51 7B BC ...
TA260 003:208.909   CPU_WriteMem(636 bytes @ 0x20000184)
TA260 003:211.470 - 2.582ms returns 0x27C
TA260 003:211.486 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
TA260 003:211.489   Data:  FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF ...
TA260 003:211.499   CPU_WriteMem(388 bytes @ 0x20000400)
TA260 003:213.419 - 1.932ms returns 0x184
TA260 003:213.438 JLINK_HasError()
TA260 003:213.447 JLINK_WriteReg(R0, 0x0800E400)
TA260 003:213.455 - 0.008ms returns 0
TA260 003:213.459 JLINK_WriteReg(R1, 0x00000128)
TA260 003:213.463 - 0.003ms returns 0
TA260 003:213.467 JLINK_WriteReg(R2, 0x20000184)
TA260 003:213.470 - 0.003ms returns 0
TA260 003:213.474 JLINK_WriteReg(R3, 0x00000000)
TA260 003:213.478 - 0.003ms returns 0
TA260 003:213.482 JLINK_WriteReg(R4, 0x00000000)
TA260 003:213.487 - 0.004ms returns 0
TA260 003:213.491 JLINK_WriteReg(R5, 0x00000000)
TA260 003:213.494 - 0.003ms returns 0
TA260 003:213.498 JLINK_WriteReg(R6, 0x00000000)
TA260 003:213.502 - 0.003ms returns 0
TA260 003:213.506 JLINK_WriteReg(R7, 0x00000000)
TA260 003:213.509 - 0.003ms returns 0
TA260 003:213.514 JLINK_WriteReg(R8, 0x00000000)
TA260 003:213.517 - 0.004ms returns 0
TA260 003:213.521 JLINK_WriteReg(R9, 0x20000180)
TA260 003:213.525 - 0.003ms returns 0
TA260 003:213.529 JLINK_WriteReg(R10, 0x00000000)
TA260 003:213.533 - 0.003ms returns 0
TA260 003:213.537 JLINK_WriteReg(R11, 0x00000000)
TA260 003:213.540 - 0.003ms returns 0
TA260 003:213.544 JLINK_WriteReg(R12, 0x00000000)
TA260 003:213.548 - 0.003ms returns 0
TA260 003:213.552 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:213.557 - 0.004ms returns 0
TA260 003:213.561 JLINK_WriteReg(R14, 0x20000001)
TA260 003:213.564 - 0.003ms returns 0
TA260 003:213.568 JLINK_WriteReg(R15 (PC), 0x2000010C)
TA260 003:213.572 - 0.003ms returns 0
TA260 003:213.576 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:213.580 - 0.003ms returns 0
TA260 003:213.584 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:213.588 - 0.003ms returns 0
TA260 003:213.592 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:213.596 - 0.003ms returns 0
TA260 003:213.600 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:213.603 - 0.003ms returns 0
TA260 003:213.608 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:213.613 - 0.004ms returns 0x00000045
TA260 003:213.621 JLINK_Go()
TA260 003:213.630   CPU_ReadMem(4 bytes @ 0x********)
TA260 003:216.319 - 2.698ms 
TA260 003:216.344 JLINK_IsHalted()
TA260 003:216.817 - 0.473ms returns FALSE
TA260 003:216.827 JLINK_HasError()
TA260 003:218.102 JLINK_IsHalted()
TA260 003:220.438   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:220.943 - 2.840ms returns TRUE
TA260 003:220.952 JLINK_ReadReg(R15 (PC))
TA260 003:220.957 - 0.005ms returns 0x20000000
TA260 003:220.962 JLINK_ClrBPEx(BPHandle = 0x00000045)
TA260 003:220.965 - 0.003ms returns 0x00
TA260 003:220.970 JLINK_ReadReg(R0)
TA260 003:220.973 - 0.003ms returns 0x00000000
TA260 003:220.978 JLINK_HasError()
TA260 003:220.982 JLINK_WriteReg(R0, 0x00000002)
TA260 003:220.986 - 0.003ms returns 0
TA260 003:220.991 JLINK_WriteReg(R1, 0x00000128)
TA260 003:220.994 - 0.003ms returns 0
TA260 003:220.998 JLINK_WriteReg(R2, 0x20000184)
TA260 003:221.002 - 0.003ms returns 0
TA260 003:221.006 JLINK_WriteReg(R3, 0x00000000)
TA260 003:221.009 - 0.003ms returns 0
TA260 003:221.013 JLINK_WriteReg(R4, 0x00000000)
TA260 003:221.016 - 0.003ms returns 0
TA260 003:221.020 JLINK_WriteReg(R5, 0x00000000)
TA260 003:221.024 - 0.003ms returns 0
TA260 003:221.028 JLINK_WriteReg(R6, 0x00000000)
TA260 003:221.031 - 0.003ms returns 0
TA260 003:221.036 JLINK_WriteReg(R7, 0x00000000)
TA260 003:221.039 - 0.003ms returns 0
TA260 003:221.043 JLINK_WriteReg(R8, 0x00000000)
TA260 003:221.046 - 0.003ms returns 0
TA260 003:221.051 JLINK_WriteReg(R9, 0x20000180)
TA260 003:221.054 - 0.003ms returns 0
TA260 003:221.058 JLINK_WriteReg(R10, 0x00000000)
TA260 003:221.062 - 0.003ms returns 0
TA260 003:221.066 JLINK_WriteReg(R11, 0x00000000)
TA260 003:221.069 - 0.003ms returns 0
TA260 003:221.073 JLINK_WriteReg(R12, 0x00000000)
TA260 003:221.076 - 0.003ms returns 0
TA260 003:221.080 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:221.084 - 0.003ms returns 0
TA260 003:221.088 JLINK_WriteReg(R14, 0x20000001)
TA260 003:221.091 - 0.003ms returns 0
TA260 003:221.096 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 003:221.099 - 0.003ms returns 0
TA260 003:221.103 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:221.107 - 0.003ms returns 0
TA260 003:221.111 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:221.114 - 0.003ms returns 0
TA260 003:221.118 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:221.121 - 0.003ms returns 0
TA260 003:221.125 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:221.129 - 0.003ms returns 0
TA260 003:221.133 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:221.137 - 0.004ms returns 0x00000046
TA260 003:221.141 JLINK_Go()
TA260 003:221.149   CPU_ReadMem(4 bytes @ 0x********)
TA260 003:224.028 - 2.886ms 
TA260 003:224.050 JLINK_IsHalted()
TA260 003:226.449   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:226.969 - 2.919ms returns TRUE
TA260 003:226.978 JLINK_ReadReg(R15 (PC))
TA260 003:226.983 - 0.005ms returns 0x20000000
TA260 003:226.988 JLINK_ClrBPEx(BPHandle = 0x00000046)
TA260 003:226.992 - 0.004ms returns 0x00
TA260 003:226.996 JLINK_ReadReg(R0)
TA260 003:227.000 - 0.003ms returns 0x00000000
TA260 003:282.743 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
TA260 003:282.755   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TA260 003:282.772   CPU_WriteMem(388 bytes @ 0x20000000)
TA260 003:284.664 - 1.920ms returns 0x184
TA260 003:284.725 JLINK_HasError()
TA260 003:284.732 JLINK_WriteReg(R0, 0x08000000)
TA260 003:284.738 - 0.006ms returns 0
TA260 003:284.743 JLINK_WriteReg(R1, 0x0ABA9500)
TA260 003:284.746 - 0.003ms returns 0
TA260 003:284.750 JLINK_WriteReg(R2, 0x00000003)
TA260 003:284.754 - 0.003ms returns 0
TA260 003:284.758 JLINK_WriteReg(R3, 0x00000000)
TA260 003:284.761 - 0.003ms returns 0
TA260 003:284.765 JLINK_WriteReg(R4, 0x00000000)
TA260 003:284.769 - 0.003ms returns 0
TA260 003:284.773 JLINK_WriteReg(R5, 0x00000000)
TA260 003:284.776 - 0.003ms returns 0
TA260 003:284.780 JLINK_WriteReg(R6, 0x00000000)
TA260 003:284.784 - 0.003ms returns 0
TA260 003:284.788 JLINK_WriteReg(R7, 0x00000000)
TA260 003:284.792 - 0.004ms returns 0
TA260 003:284.800 JLINK_WriteReg(R8, 0x00000000)
TA260 003:284.803 - 0.003ms returns 0
TA260 003:284.807 JLINK_WriteReg(R9, 0x20000180)
TA260 003:284.811 - 0.003ms returns 0
TA260 003:284.815 JLINK_WriteReg(R10, 0x00000000)
TA260 003:284.818 - 0.003ms returns 0
TA260 003:284.822 JLINK_WriteReg(R11, 0x00000000)
TA260 003:284.826 - 0.003ms returns 0
TA260 003:284.830 JLINK_WriteReg(R12, 0x00000000)
TA260 003:284.834 - 0.003ms returns 0
TA260 003:284.838 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:284.842 - 0.004ms returns 0
TA260 003:284.846 JLINK_WriteReg(R14, 0x20000001)
TA260 003:284.850 - 0.003ms returns 0
TA260 003:284.854 JLINK_WriteReg(R15 (PC), 0x20000054)
TA260 003:284.857 - 0.003ms returns 0
TA260 003:284.861 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:284.865 - 0.003ms returns 0
TA260 003:284.869 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:284.872 - 0.003ms returns 0
TA260 003:284.876 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:284.880 - 0.003ms returns 0
TA260 003:284.884 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:284.887 - 0.003ms returns 0
TA260 003:284.892 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:284.900   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:285.350 - 0.458ms returns 0x00000047
TA260 003:285.357 JLINK_Go()
TA260 003:285.362   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 003:285.846   CPU_ReadMem(4 bytes @ 0x********)
TA260 003:288.600 - 3.243ms 
TA260 003:288.612 JLINK_IsHalted()
TA260 003:290.913   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:291.408 - 2.796ms returns TRUE
TA260 003:291.418 JLINK_ReadReg(R15 (PC))
TA260 003:291.423 - 0.004ms returns 0x20000000
TA260 003:291.427 JLINK_ClrBPEx(BPHandle = 0x00000047)
TA260 003:291.431 - 0.004ms returns 0x00
TA260 003:291.436 JLINK_ReadReg(R0)
TA260 003:291.439 - 0.003ms returns 0x00000000
TA260 003:291.444 JLINK_HasError()
TA260 003:291.448 JLINK_WriteReg(R0, 0xFFFFFFFF)
TA260 003:291.453 - 0.004ms returns 0
TA260 003:291.457 JLINK_WriteReg(R1, 0x08000000)
TA260 003:291.461 - 0.003ms returns 0
TA260 003:291.465 JLINK_WriteReg(R2, 0x0000E528)
TA260 003:291.468 - 0.003ms returns 0
TA260 003:291.472 JLINK_WriteReg(R3, 0x04C11DB7)
TA260 003:291.476 - 0.003ms returns 0
TA260 003:291.480 JLINK_WriteReg(R4, 0x00000000)
TA260 003:291.483 - 0.003ms returns 0
TA260 003:291.487 JLINK_WriteReg(R5, 0x00000000)
TA260 003:291.491 - 0.003ms returns 0
TA260 003:291.495 JLINK_WriteReg(R6, 0x00000000)
TA260 003:291.498 - 0.003ms returns 0
TA260 003:291.503 JLINK_WriteReg(R7, 0x00000000)
TA260 003:291.507 - 0.003ms returns 0
TA260 003:291.511 JLINK_WriteReg(R8, 0x00000000)
TA260 003:291.514 - 0.003ms returns 0
TA260 003:291.519 JLINK_WriteReg(R9, 0x20000180)
TA260 003:291.526 - 0.006ms returns 0
TA260 003:291.530 JLINK_WriteReg(R10, 0x00000000)
TA260 003:291.534 - 0.003ms returns 0
TA260 003:291.538 JLINK_WriteReg(R11, 0x00000000)
TA260 003:291.541 - 0.003ms returns 0
TA260 003:291.545 JLINK_WriteReg(R12, 0x00000000)
TA260 003:291.549 - 0.003ms returns 0
TA260 003:291.553 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:291.556 - 0.003ms returns 0
TA260 003:291.560 JLINK_WriteReg(R14, 0x20000001)
TA260 003:291.564 - 0.003ms returns 0
TA260 003:291.568 JLINK_WriteReg(R15 (PC), 0x20000002)
TA260 003:291.572 - 0.003ms returns 0
TA260 003:291.576 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:291.579 - 0.003ms returns 0
TA260 003:291.583 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:291.587 - 0.003ms returns 0
TA260 003:291.591 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:291.595 - 0.003ms returns 0
TA260 003:291.599 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:291.603 - 0.003ms returns 0
TA260 003:291.607 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:291.611 - 0.004ms returns 0x00000048
TA260 003:291.615 JLINK_Go()
TA260 003:291.622   CPU_ReadMem(4 bytes @ 0x********)
TA260 003:294.382 - 2.766ms 
TA260 003:294.404 JLINK_IsHalted()
TA260 003:294.842 - 0.438ms returns FALSE
TA260 003:294.855 JLINK_HasError()
TA260 003:298.328 JLINK_IsHalted()
TA260 003:298.786 - 0.457ms returns FALSE
TA260 003:298.805 JLINK_HasError()
TA260 003:300.316 JLINK_IsHalted()
TA260 003:300.845 - 0.528ms returns FALSE
TA260 003:300.852 JLINK_HasError()
TA260 003:302.319 JLINK_IsHalted()
TA260 003:302.830 - 0.510ms returns FALSE
TA260 003:302.840 JLINK_HasError()
TA260 003:304.829 JLINK_IsHalted()
TA260 003:305.290 - 0.460ms returns FALSE
TA260 003:305.303 JLINK_HasError()
TA260 003:306.823 JLINK_IsHalted()
TA260 003:307.308 - 0.484ms returns FALSE
TA260 003:307.318 JLINK_HasError()
TA260 003:308.832 JLINK_IsHalted()
TA260 003:309.305 - 0.473ms returns FALSE
TA260 003:309.322 JLINK_HasError()
TA260 003:310.826 JLINK_IsHalted()
TA260 003:311.272 - 0.445ms returns FALSE
TA260 003:311.282 JLINK_HasError()
TA260 003:312.330 JLINK_IsHalted()
TA260 003:312.785 - 0.454ms returns FALSE
TA260 003:312.794 JLINK_HasError()
TA260 003:314.337 JLINK_IsHalted()
TA260 003:314.795 - 0.458ms returns FALSE
TA260 003:314.803 JLINK_HasError()
TA260 003:316.335 JLINK_IsHalted()
TA260 003:316.826 - 0.490ms returns FALSE
TA260 003:316.832 JLINK_HasError()
TA260 003:318.330 JLINK_IsHalted()
TA260 003:318.836 - 0.506ms returns FALSE
TA260 003:318.841 JLINK_HasError()
TA260 003:320.331 JLINK_IsHalted()
TA260 003:320.827 - 0.495ms returns FALSE
TA260 003:320.832 JLINK_HasError()
TA260 003:322.331 JLINK_IsHalted()
TA260 003:322.848 - 0.516ms returns FALSE
TA260 003:322.853 JLINK_HasError()
TA260 003:324.838 JLINK_IsHalted()
TA260 003:325.363 - 0.525ms returns FALSE
TA260 003:325.369 JLINK_HasError()
TA260 003:327.340 JLINK_IsHalted()
TA260 003:327.782 - 0.442ms returns FALSE
TA260 003:327.789 JLINK_HasError()
TA260 003:329.338 JLINK_IsHalted()
TA260 003:329.813 - 0.474ms returns FALSE
TA260 003:329.818 JLINK_HasError()
TA260 003:331.338 JLINK_IsHalted()
TA260 003:331.800 - 0.462ms returns FALSE
TA260 003:331.806 JLINK_HasError()
TA260 003:333.847 JLINK_IsHalted()
TA260 003:334.345 - 0.498ms returns FALSE
TA260 003:334.361 JLINK_HasError()
TA260 003:335.846 JLINK_IsHalted()
TA260 003:336.314 - 0.468ms returns FALSE
TA260 003:336.320 JLINK_HasError()
TA260 003:337.844 JLINK_IsHalted()
TA260 003:338.313 - 0.468ms returns FALSE
TA260 003:338.319 JLINK_HasError()
TA260 003:339.845 JLINK_IsHalted()
TA260 003:340.419 - 0.573ms returns FALSE
TA260 003:340.425 JLINK_HasError()
TA260 003:342.348 JLINK_IsHalted()
TA260 003:342.840 - 0.492ms returns FALSE
TA260 003:342.852 JLINK_HasError()
TA260 003:344.359 JLINK_IsHalted()
TA260 003:344.827 - 0.467ms returns FALSE
TA260 003:344.840 JLINK_HasError()
TA260 003:346.355 JLINK_IsHalted()
TA260 003:346.849 - 0.493ms returns FALSE
TA260 003:346.855 JLINK_HasError()
TA260 003:348.351 JLINK_IsHalted()
TA260 003:348.813 - 0.462ms returns FALSE
TA260 003:348.819 JLINK_HasError()
TA260 003:350.352 JLINK_IsHalted()
TA260 003:350.839 - 0.486ms returns FALSE
TA260 003:350.847 JLINK_HasError()
TA260 003:352.353 JLINK_IsHalted()
TA260 003:352.872 - 0.518ms returns FALSE
TA260 003:352.878 JLINK_HasError()
TA260 003:354.860 JLINK_IsHalted()
TA260 003:355.419 - 0.559ms returns FALSE
TA260 003:355.425 JLINK_HasError()
TA260 003:356.858 JLINK_IsHalted()
TA260 003:357.372 - 0.514ms returns FALSE
TA260 003:357.378 JLINK_HasError()
TA260 003:358.860 JLINK_IsHalted()
TA260 003:359.271 - 0.410ms returns FALSE
TA260 003:359.282 JLINK_HasError()
TA260 003:362.363 JLINK_IsHalted()
TA260 003:362.872 - 0.508ms returns FALSE
TA260 003:362.878 JLINK_HasError()
TA260 003:364.367 JLINK_IsHalted()
TA260 003:364.806 - 0.439ms returns FALSE
TA260 003:364.818 JLINK_HasError()
TA260 003:366.366 JLINK_IsHalted()
TA260 003:366.871 - 0.504ms returns FALSE
TA260 003:366.876 JLINK_HasError()
TA260 003:368.364 JLINK_IsHalted()
TA260 003:368.861 - 0.497ms returns FALSE
TA260 003:368.866 JLINK_HasError()
TA260 003:370.366 JLINK_IsHalted()
TA260 003:370.846 - 0.480ms returns FALSE
TA260 003:370.852 JLINK_HasError()
TA260 003:372.365 JLINK_IsHalted()
TA260 003:372.845 - 0.479ms returns FALSE
TA260 003:372.850 JLINK_HasError()
TA260 003:374.875 JLINK_IsHalted()
TA260 003:375.350 - 0.475ms returns FALSE
TA260 003:375.362 JLINK_HasError()
TA260 003:377.237 JLINK_IsHalted()
TA260 003:377.737 - 0.499ms returns FALSE
TA260 003:377.749 JLINK_HasError()
TA260 003:379.240 JLINK_IsHalted()
TA260 003:379.723 - 0.482ms returns FALSE
TA260 003:379.728 JLINK_HasError()
TA260 003:381.240 JLINK_IsHalted()
TA260 003:381.812 - 0.572ms returns FALSE
TA260 003:381.818 JLINK_HasError()
TA260 003:383.750 JLINK_IsHalted()
TA260 003:384.282 - 0.532ms returns FALSE
TA260 003:384.293 JLINK_HasError()
TA260 003:385.748 JLINK_IsHalted()
TA260 003:386.270 - 0.521ms returns FALSE
TA260 003:386.275 JLINK_HasError()
TA260 003:387.747 JLINK_IsHalted()
TA260 003:388.268 - 0.520ms returns FALSE
TA260 003:388.273 JLINK_HasError()
TA260 003:389.748 JLINK_IsHalted()
TA260 003:390.206 - 0.457ms returns FALSE
TA260 003:390.214 JLINK_HasError()
TA260 003:392.257 JLINK_IsHalted()
TA260 003:392.748 - 0.491ms returns FALSE
TA260 003:392.756 JLINK_HasError()
TA260 003:394.291 JLINK_IsHalted()
TA260 003:394.730 - 0.439ms returns FALSE
TA260 003:394.738 JLINK_HasError()
TA260 003:396.260 JLINK_IsHalted()
TA260 003:396.773 - 0.512ms returns FALSE
TA260 003:396.786 JLINK_HasError()
TA260 003:398.256 JLINK_IsHalted()
TA260 003:398.746 - 0.490ms returns FALSE
TA260 003:398.752 JLINK_HasError()
TA260 003:400.256 JLINK_IsHalted()
TA260 003:400.774 - 0.517ms returns FALSE
TA260 003:400.780 JLINK_HasError()
TA260 003:402.259 JLINK_IsHalted()
TA260 003:402.734 - 0.475ms returns FALSE
TA260 003:402.740 JLINK_HasError()
TA260 003:404.766 JLINK_IsHalted()
TA260 003:405.293 - 0.526ms returns FALSE
TA260 003:405.300 JLINK_HasError()
TA260 003:406.769 JLINK_IsHalted()
TA260 003:407.282 - 0.512ms returns FALSE
TA260 003:407.294 JLINK_HasError()
TA260 003:409.794 JLINK_IsHalted()
TA260 003:410.280 - 0.485ms returns FALSE
TA260 003:410.291 JLINK_HasError()
TA260 003:412.266 JLINK_IsHalted()
TA260 003:412.748 - 0.482ms returns FALSE
TA260 003:412.755 JLINK_HasError()
TA260 003:414.296 JLINK_IsHalted()
TA260 003:414.772 - 0.475ms returns FALSE
TA260 003:414.784 JLINK_HasError()
TA260 003:416.273 JLINK_IsHalted()
TA260 003:416.792 - 0.518ms returns FALSE
TA260 003:416.798 JLINK_HasError()
TA260 003:418.269 JLINK_IsHalted()
TA260 003:418.791 - 0.522ms returns FALSE
TA260 003:418.797 JLINK_HasError()
TA260 003:420.269 JLINK_IsHalted()
TA260 003:420.747 - 0.478ms returns FALSE
TA260 003:420.753 JLINK_HasError()
TA260 003:422.272 JLINK_IsHalted()
TA260 003:422.759 - 0.486ms returns FALSE
TA260 003:422.765 JLINK_HasError()
TA260 003:424.788 JLINK_IsHalted()
TA260 003:425.269 - 0.481ms returns FALSE
TA260 003:425.276 JLINK_HasError()
TA260 003:427.284 JLINK_IsHalted()
TA260 003:427.830 - 0.545ms returns FALSE
TA260 003:427.841 JLINK_HasError()
TA260 003:429.280 JLINK_IsHalted()
TA260 003:429.716 - 0.436ms returns FALSE
TA260 003:429.723 JLINK_HasError()
TA260 003:431.293 JLINK_IsHalted()
TA260 003:431.757 - 0.463ms returns FALSE
TA260 003:431.763 JLINK_HasError()
TA260 003:433.788 JLINK_IsHalted()
TA260 003:434.276 - 0.487ms returns FALSE
TA260 003:434.283 JLINK_HasError()
TA260 003:435.790 JLINK_IsHalted()
TA260 003:436.313 - 0.523ms returns FALSE
TA260 003:436.320 JLINK_HasError()
TA260 003:437.789 JLINK_IsHalted()
TA260 003:438.271 - 0.481ms returns FALSE
TA260 003:438.277 JLINK_HasError()
TA260 003:439.786 JLINK_IsHalted()
TA260 003:440.266 - 0.480ms returns FALSE
TA260 003:440.272 JLINK_HasError()
TA260 003:442.289 JLINK_IsHalted()
TA260 003:442.747 - 0.458ms returns FALSE
TA260 003:442.753 JLINK_HasError()
TA260 003:444.298 JLINK_IsHalted()
TA260 003:444.733 - 0.435ms returns FALSE
TA260 003:444.741 JLINK_HasError()
TA260 003:446.298 JLINK_IsHalted()
TA260 003:446.816 - 0.517ms returns FALSE
TA260 003:446.822 JLINK_HasError()
TA260 003:448.292 JLINK_IsHalted()
TA260 003:448.790 - 0.497ms returns FALSE
TA260 003:448.795 JLINK_HasError()
TA260 003:450.296 JLINK_IsHalted()
TA260 003:450.816 - 0.519ms returns FALSE
TA260 003:450.822 JLINK_HasError()
TA260 003:452.294 JLINK_IsHalted()
TA260 003:452.840 - 0.546ms returns FALSE
TA260 003:452.850 JLINK_HasError()
TA260 003:454.808 JLINK_IsHalted()
TA260 003:455.315 - 0.507ms returns FALSE
TA260 003:455.322 JLINK_HasError()
TA260 003:456.803 JLINK_IsHalted()
TA260 003:457.275 - 0.472ms returns FALSE
TA260 003:457.281 JLINK_HasError()
TA260 003:458.802 JLINK_IsHalted()
TA260 003:459.277 - 0.474ms returns FALSE
TA260 003:459.283 JLINK_HasError()
TA260 003:460.805 JLINK_IsHalted()
TA260 003:461.312 - 0.507ms returns FALSE
TA260 003:461.318 JLINK_HasError()
TA260 003:463.442 JLINK_IsHalted()
TA260 003:463.899 - 0.456ms returns FALSE
TA260 003:463.910 JLINK_HasError()
TA260 003:465.312 JLINK_IsHalted()
TA260 003:465.822 - 0.503ms returns FALSE
TA260 003:465.828 JLINK_HasError()
TA260 003:467.312 JLINK_IsHalted()
TA260 003:467.815 - 0.503ms returns FALSE
TA260 003:467.820 JLINK_HasError()
TA260 003:469.312 JLINK_IsHalted()
TA260 003:469.749 - 0.436ms returns FALSE
TA260 003:469.756 JLINK_HasError()
TA260 003:472.318 JLINK_IsHalted()
TA260 003:472.837 - 0.519ms returns FALSE
TA260 003:472.843 JLINK_HasError()
TA260 003:474.819 JLINK_IsHalted()
TA260 003:475.307 - 0.487ms returns FALSE
TA260 003:475.314 JLINK_HasError()
TA260 003:476.822 JLINK_IsHalted()
TA260 003:477.306 - 0.483ms returns FALSE
TA260 003:477.316 JLINK_HasError()
TA260 003:478.816 JLINK_IsHalted()
TA260 003:479.280 - 0.464ms returns FALSE
TA260 003:479.288 JLINK_HasError()
TA260 003:480.818 JLINK_IsHalted()
TA260 003:481.314 - 0.496ms returns FALSE
TA260 003:481.320 JLINK_HasError()
TA260 003:483.324 JLINK_IsHalted()
TA260 003:483.920 - 0.596ms returns FALSE
TA260 003:483.931 JLINK_HasError()
TA260 003:485.328 JLINK_IsHalted()
TA260 003:485.839 - 0.510ms returns FALSE
TA260 003:485.848 JLINK_HasError()
TA260 003:487.333 JLINK_IsHalted()
TA260 003:487.837 - 0.504ms returns FALSE
TA260 003:487.843 JLINK_HasError()
TA260 003:489.322 JLINK_IsHalted()
TA260 003:489.756 - 0.433ms returns FALSE
TA260 003:489.762 JLINK_HasError()
TA260 003:491.325 JLINK_IsHalted()
TA260 003:491.802 - 0.477ms returns FALSE
TA260 003:491.808 JLINK_HasError()
TA260 003:493.832 JLINK_IsHalted()
TA260 003:494.276 - 0.444ms returns FALSE
TA260 003:494.283 JLINK_HasError()
TA260 003:495.844 JLINK_IsHalted()
TA260 003:496.319 - 0.474ms returns FALSE
TA260 003:496.332 JLINK_HasError()
TA260 003:497.835 JLINK_IsHalted()
TA260 003:498.300 - 0.465ms returns FALSE
TA260 003:498.306 JLINK_HasError()
TA260 003:499.830 JLINK_IsHalted()
TA260 003:500.375 - 0.544ms returns FALSE
TA260 003:500.383 JLINK_HasError()
TA260 003:502.336 JLINK_IsHalted()
TA260 003:502.826 - 0.490ms returns FALSE
TA260 003:502.832 JLINK_HasError()
TA260 003:504.340 JLINK_IsHalted()
TA260 003:504.784 - 0.443ms returns FALSE
TA260 003:504.796 JLINK_HasError()
TA260 003:506.338 JLINK_IsHalted()
TA260 003:506.860 - 0.521ms returns FALSE
TA260 003:506.866 JLINK_HasError()
TA260 003:508.344 JLINK_IsHalted()
TA260 003:508.824 - 0.480ms returns FALSE
TA260 003:508.830 JLINK_HasError()
TA260 003:510.342 JLINK_IsHalted()
TA260 003:510.860 - 0.518ms returns FALSE
TA260 003:510.872 JLINK_HasError()
TA260 003:512.338 JLINK_IsHalted()
TA260 003:512.841 - 0.502ms returns FALSE
TA260 003:512.846 JLINK_HasError()
TA260 003:514.848 JLINK_IsHalted()
TA260 003:515.419 - 0.570ms returns FALSE
TA260 003:515.538 JLINK_HasError()
TA260 003:516.848 JLINK_IsHalted()
TA260 003:517.316 - 0.468ms returns FALSE
TA260 003:517.322 JLINK_HasError()
TA260 003:518.849 JLINK_IsHalted()
TA260 003:519.316 - 0.466ms returns FALSE
TA260 003:519.321 JLINK_HasError()
TA260 003:520.846 JLINK_IsHalted()
TA260 003:521.315 - 0.469ms returns FALSE
TA260 003:521.321 JLINK_HasError()
TA260 003:523.445 JLINK_IsHalted()
TA260 003:523.987 - 0.541ms returns FALSE
TA260 003:523.996 JLINK_HasError()
TA260 003:525.350 JLINK_IsHalted()
TA260 003:525.791 - 0.441ms returns FALSE
TA260 003:525.797 JLINK_HasError()
TA260 003:526.851 JLINK_IsHalted()
TA260 003:527.381 - 0.529ms returns FALSE
TA260 003:527.388 JLINK_HasError()
TA260 003:528.856 JLINK_IsHalted()
TA260 003:529.314 - 0.458ms returns FALSE
TA260 003:529.321 JLINK_HasError()
TA260 003:530.855 JLINK_IsHalted()
TA260 003:531.313 - 0.458ms returns FALSE
TA260 003:531.319 JLINK_HasError()
TA260 003:532.355 JLINK_IsHalted()
TA260 003:533.024 - 0.668ms returns FALSE
TA260 003:533.098 JLINK_HasError()
TA260 003:534.368 JLINK_IsHalted()
TA260 003:534.795 - 0.426ms returns FALSE
TA260 003:534.808 JLINK_HasError()
TA260 003:536.361 JLINK_IsHalted()
TA260 003:536.839 - 0.477ms returns FALSE
TA260 003:536.845 JLINK_HasError()
TA260 003:538.359 JLINK_IsHalted()
TA260 003:538.836 - 0.477ms returns FALSE
TA260 003:538.842 JLINK_HasError()
TA260 003:540.360 JLINK_IsHalted()
TA260 003:540.840 - 0.479ms returns FALSE
TA260 003:540.849 JLINK_HasError()
TA260 003:542.360 JLINK_IsHalted()
TA260 003:542.864 - 0.503ms returns FALSE
TA260 003:542.870 JLINK_HasError()
TA260 003:544.881 JLINK_IsHalted()
TA260 003:545.317 - 0.435ms returns FALSE
TA260 003:545.323 JLINK_HasError()
TA260 003:546.869 JLINK_IsHalted()
TA260 003:547.350 - 0.480ms returns FALSE
TA260 003:547.356 JLINK_HasError()
TA260 003:548.876 JLINK_IsHalted()
TA260 003:549.365 - 0.489ms returns FALSE
TA260 003:549.378 JLINK_HasError()
TA260 003:550.882 JLINK_IsHalted()
TA260 003:551.418 - 0.536ms returns FALSE
TA260 003:551.426 JLINK_HasError()
TA260 003:553.439 JLINK_IsHalted()
TA260 003:554.202 - 0.763ms returns FALSE
TA260 003:554.211 JLINK_HasError()
TA260 003:555.384 JLINK_IsHalted()
TA260 003:555.840 - 0.456ms returns FALSE
TA260 003:555.850 JLINK_HasError()
TA260 003:557.379 JLINK_IsHalted()
TA260 003:557.882 - 0.503ms returns FALSE
TA260 003:557.888 JLINK_HasError()
TA260 003:559.381 JLINK_IsHalted()
TA260 003:560.100 - 0.717ms returns FALSE
TA260 003:560.109 JLINK_HasError()
TA260 003:561.379 JLINK_IsHalted()
TA260 003:561.860 - 0.480ms returns FALSE
TA260 003:561.865 JLINK_HasError()
TA260 003:563.894 JLINK_IsHalted()
TA260 003:564.364 - 0.469ms returns FALSE
TA260 003:564.373 JLINK_HasError()
TA260 003:565.892 JLINK_IsHalted()
TA260 003:566.346 - 0.453ms returns FALSE
TA260 003:566.352 JLINK_HasError()
TA260 003:567.890 JLINK_IsHalted()
TA260 003:568.403 - 0.513ms returns FALSE
TA260 003:568.408 JLINK_HasError()
TA260 003:569.888 JLINK_IsHalted()
TA260 003:570.402 - 0.513ms returns FALSE
TA260 003:570.408 JLINK_HasError()
TA260 003:572.391 JLINK_IsHalted()
TA260 003:572.871 - 0.480ms returns FALSE
TA260 003:572.877 JLINK_HasError()
TA260 003:574.397 JLINK_IsHalted()
TA260 003:574.860 - 0.462ms returns FALSE
TA260 003:574.867 JLINK_HasError()
TA260 003:576.400 JLINK_IsHalted()
TA260 003:576.916 - 0.516ms returns FALSE
TA260 003:576.936 JLINK_HasError()
TA260 003:578.396 JLINK_IsHalted()
TA260 003:578.890 - 0.493ms returns FALSE
TA260 003:578.901 JLINK_HasError()
TA260 003:581.402 JLINK_IsHalted()
TA260 003:581.893 - 0.491ms returns FALSE
TA260 003:581.900 JLINK_HasError()
TA260 003:583.910 JLINK_IsHalted()
TA260 003:584.325 - 0.414ms returns FALSE
TA260 003:584.337 JLINK_HasError()
TA260 003:585.906 JLINK_IsHalted()
TA260 003:586.426 - 0.519ms returns FALSE
TA260 003:586.444 JLINK_HasError()
TA260 003:587.906 JLINK_IsHalted()
TA260 003:588.418 - 0.512ms returns FALSE
TA260 003:588.424 JLINK_HasError()
TA260 003:589.529 JLINK_IsHalted()
TA260 003:590.061 - 0.532ms returns FALSE
TA260 003:590.068 JLINK_HasError()
TA260 003:592.032 JLINK_IsHalted()
TA260 003:592.549 - 0.517ms returns FALSE
TA260 003:592.555 JLINK_HasError()
TA260 003:594.544 JLINK_IsHalted()
TA260 003:595.022 - 0.477ms returns FALSE
TA260 003:595.028 JLINK_HasError()
TA260 003:596.544 JLINK_IsHalted()
TA260 003:597.029 - 0.490ms returns FALSE
TA260 003:597.035 JLINK_HasError()
TA260 003:598.537 JLINK_IsHalted()
TA260 003:599.018 - 0.481ms returns FALSE
TA260 003:599.024 JLINK_HasError()
TA260 003:600.542 JLINK_IsHalted()
TA260 003:601.042 - 0.500ms returns FALSE
TA260 003:601.052 JLINK_HasError()
TA260 003:603.040 JLINK_IsHalted()
TA260 003:603.500 - 0.459ms returns FALSE
TA260 003:603.508 JLINK_HasError()
TA260 003:605.047 JLINK_IsHalted()
TA260 003:605.563 - 0.515ms returns FALSE
TA260 003:605.569 JLINK_HasError()
TA260 003:607.046 JLINK_IsHalted()
TA260 003:607.546 - 0.500ms returns FALSE
TA260 003:607.553 JLINK_HasError()
TA260 003:609.044 JLINK_IsHalted()
TA260 003:609.544 - 0.500ms returns FALSE
TA260 003:609.556 JLINK_HasError()
TA260 003:612.048 JLINK_IsHalted()
TA260 003:612.501 - 0.452ms returns FALSE
TA260 003:612.508 JLINK_HasError()
TA260 003:613.550 JLINK_IsHalted()
TA260 003:614.068 - 0.517ms returns FALSE
TA260 003:614.078 JLINK_HasError()
TA260 003:615.554 JLINK_IsHalted()
TA260 003:617.850   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:618.360 - 2.805ms returns TRUE
TA260 003:618.367 JLINK_ReadReg(R15 (PC))
TA260 003:618.372 - 0.005ms returns 0x20000000
TA260 003:618.377 JLINK_ClrBPEx(BPHandle = 0x00000048)
TA260 003:618.381 - 0.003ms returns 0x00
TA260 003:618.385 JLINK_ReadReg(R0)
TA260 003:618.389 - 0.003ms returns 0x1CCC4DD9
TA260 003:620.076 JLINK_HasError()
TA260 003:620.085 JLINK_WriteReg(R0, 0x00000003)
TA260 003:620.089 - 0.004ms returns 0
TA260 003:620.094 JLINK_WriteReg(R1, 0x08000000)
TA260 003:620.097 - 0.003ms returns 0
TA260 003:620.101 JLINK_WriteReg(R2, 0x0000E528)
TA260 003:620.104 - 0.003ms returns 0
TA260 003:620.108 JLINK_WriteReg(R3, 0x04C11DB7)
TA260 003:620.112 - 0.003ms returns 0
TA260 003:620.116 JLINK_WriteReg(R4, 0x00000000)
TA260 003:620.120 - 0.003ms returns 0
TA260 003:620.123 JLINK_WriteReg(R5, 0x00000000)
TA260 003:620.127 - 0.004ms returns 0
TA260 003:620.132 JLINK_WriteReg(R6, 0x00000000)
TA260 003:620.135 - 0.003ms returns 0
TA260 003:620.139 JLINK_WriteReg(R7, 0x00000000)
TA260 003:620.142 - 0.003ms returns 0
TA260 003:620.146 JLINK_WriteReg(R8, 0x00000000)
TA260 003:620.150 - 0.003ms returns 0
TA260 003:620.154 JLINK_WriteReg(R9, 0x20000180)
TA260 003:620.157 - 0.003ms returns 0
TA260 003:620.162 JLINK_WriteReg(R10, 0x00000000)
TA260 003:620.165 - 0.003ms returns 0
TA260 003:620.169 JLINK_WriteReg(R11, 0x00000000)
TA260 003:620.172 - 0.003ms returns 0
TA260 003:620.176 JLINK_WriteReg(R12, 0x00000000)
TA260 003:620.180 - 0.003ms returns 0
TA260 003:620.184 JLINK_WriteReg(R13 (SP), 0x20001000)
TA260 003:620.188 - 0.003ms returns 0
TA260 003:620.192 JLINK_WriteReg(R14, 0x20000001)
TA260 003:620.195 - 0.003ms returns 0
TA260 003:620.199 JLINK_WriteReg(R15 (PC), 0x20000086)
TA260 003:620.202 - 0.003ms returns 0
TA260 003:620.206 JLINK_WriteReg(XPSR, 0x01000000)
TA260 003:620.210 - 0.003ms returns 0
TA260 003:620.214 JLINK_WriteReg(MSP, 0x20001000)
TA260 003:620.217 - 0.003ms returns 0
TA260 003:620.222 JLINK_WriteReg(PSP, 0x20001000)
TA260 003:620.225 - 0.003ms returns 0
TA260 003:620.229 JLINK_WriteReg(CFBP, 0x00000000)
TA260 003:620.232 - 0.003ms returns 0
TA260 003:620.237 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
TA260 003:620.241 - 0.004ms returns 0x00000049
TA260 003:620.246 JLINK_Go()
TA260 003:620.254   CPU_ReadMem(4 bytes @ 0x********)
TA260 003:623.006 - 2.760ms 
TA260 003:623.012 JLINK_IsHalted()
TA260 003:625.359   CPU_ReadMem(2 bytes @ 0x20000000)
TA260 003:625.863 - 2.850ms returns TRUE
TA260 003:625.870 JLINK_ReadReg(R15 (PC))
TA260 003:625.876 - 0.005ms returns 0x20000000
TA260 003:625.880 JLINK_ClrBPEx(BPHandle = 0x00000049)
TA260 003:625.884 - 0.003ms returns 0x00
TA260 003:625.888 JLINK_ReadReg(R0)
TA260 003:625.891 - 0.003ms returns 0x00000000
TA260 003:678.698 JLINK_WriteMemEx(0x20000000, 0x00000002 Bytes, Flags = 0x02000000)
TA260 003:678.720   Data:  FE E7
TA260 003:678.738   CPU_WriteMem(2 bytes @ 0x20000000)
TA260 003:679.285 - 0.587ms returns 0x2
TA260 003:679.302 JLINK_HasError()
TA260 003:682.527 JLINK_Close()
TA260 003:684.700   OnDisconnectTarget() start
TA260 003:684.715    J-Link Script File: Executing OnDisconnectTarget()
TA260 003:684.725   CPU_WriteMem(4 bytes @ 0xE0042004)
TA260 003:685.182   CPU_WriteMem(4 bytes @ 0xE0042008)
TA260 003:687.420   OnDisconnectTarget() end - Took 961us
TA260 003:687.436   CPU_ReadMem(4 bytes @ 0x********)
TA260 003:706.860 - 24.332ms
TA260 003:706.880   
TA260 003:706.884   Closed
